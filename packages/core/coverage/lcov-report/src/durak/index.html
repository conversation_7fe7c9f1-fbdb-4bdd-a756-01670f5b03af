
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/durak</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/durak</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">62.36% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>285/457</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">53.53% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>121/226</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">66.66% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>54/81</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">62.07% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>275/443</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="bot.ts"><a href="bot.ts.html">bot.ts</a></td>
	<td data-value="63.7" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 63%"></div><div class="cover-empty" style="width: 37%"></div></div>
	</td>
	<td data-value="63.7" class="pct medium">63.7%</td>
	<td data-value="124" class="abs medium">79/124</td>
	<td data-value="55.07" class="pct medium">55.07%</td>
	<td data-value="69" class="abs medium">38/69</td>
	<td data-value="61.9" class="pct medium">61.9%</td>
	<td data-value="42" class="abs medium">26/42</td>
	<td data-value="64.1" class="pct medium">64.1%</td>
	<td data-value="117" class="abs medium">75/117</td>
	</tr>

<tr>
	<td class="file medium" data-value="index.ts"><a href="index.ts.html">index.ts</a></td>
	<td data-value="61.86" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 61%"></div><div class="cover-empty" style="width: 39%"></div></div>
	</td>
	<td data-value="61.86" class="pct medium">61.86%</td>
	<td data-value="333" class="abs medium">206/333</td>
	<td data-value="52.86" class="pct medium">52.86%</td>
	<td data-value="157" class="abs medium">83/157</td>
	<td data-value="71.79" class="pct medium">71.79%</td>
	<td data-value="39" class="abs medium">28/39</td>
	<td data-value="61.34" class="pct medium">61.34%</td>
	<td data-value="326" class="abs medium">200/326</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-10T12:35:43.385Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    