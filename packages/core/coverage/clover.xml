<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1749558943398" clover="3.2.0">
  <project timestamp="1749558943399" name="All files">
    <metrics statements="479" coveredstatements="311" conditionals="238" coveredconditionals="133" methods="87" coveredmethods="60" elements="804" coveredelements="504" complexity="0" loc="479" ncloc="479" packages="2" files="3" classes="3"/>
    <package name="src">
      <metrics statements="36" coveredstatements="36" conditionals="12" coveredconditionals="12" methods="6" coveredmethods="6"/>
      <file name="types.ts" path="/Users/<USER>/S/a/A1-K/packages/core/src/types.ts">
        <metrics statements="36" coveredstatements="36" conditionals="12" coveredconditionals="12" methods="6" coveredmethods="6"/>
        <line num="6" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="7" count="2" type="stmt"/>
        <line num="8" count="2" type="stmt"/>
        <line num="9" count="2" type="stmt"/>
        <line num="10" count="2" type="stmt"/>
        <line num="13" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="14" count="2" type="stmt"/>
        <line num="15" count="2" type="stmt"/>
        <line num="16" count="2" type="stmt"/>
        <line num="17" count="2" type="stmt"/>
        <line num="18" count="2" type="stmt"/>
        <line num="19" count="2" type="stmt"/>
        <line num="20" count="2" type="stmt"/>
        <line num="21" count="2" type="stmt"/>
        <line num="22" count="2" type="stmt"/>
        <line num="39" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="40" count="2" type="stmt"/>
        <line num="41" count="2" type="stmt"/>
        <line num="42" count="2" type="stmt"/>
        <line num="43" count="2" type="stmt"/>
        <line num="75" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="76" count="2" type="stmt"/>
        <line num="77" count="2" type="stmt"/>
        <line num="78" count="2" type="stmt"/>
        <line num="82" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="83" count="2" type="stmt"/>
        <line num="84" count="2" type="stmt"/>
        <line num="85" count="2" type="stmt"/>
        <line num="86" count="2" type="stmt"/>
        <line num="90" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="91" count="2" type="stmt"/>
        <line num="92" count="2" type="stmt"/>
        <line num="93" count="2" type="stmt"/>
        <line num="94" count="2" type="stmt"/>
        <line num="95" count="2" type="stmt"/>
        <line num="96" count="2" type="stmt"/>
      </file>
    </package>
    <package name="src.durak">
      <metrics statements="443" coveredstatements="275" conditionals="226" coveredconditionals="121" methods="81" coveredmethods="54"/>
      <file name="bot.ts" path="/Users/<USER>/S/a/A1-K/packages/core/src/durak/bot.ts">
        <metrics statements="117" coveredstatements="75" conditionals="69" coveredconditionals="38" methods="42" coveredmethods="26"/>
        <line num="5" count="1" type="stmt"/>
        <line num="17" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="41" count="22" type="stmt"/>
        <line num="42" count="22" type="stmt"/>
        <line num="43" count="22" type="stmt"/>
        <line num="50" count="9" type="stmt"/>
        <line num="62" count="12" type="stmt"/>
        <line num="63" count="8" type="cond" truecount="0" falsecount="1"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="8" type="stmt"/>
        <line num="68" count="8" type="stmt"/>
        <line num="71" count="8" type="cond" truecount="4" falsecount="0"/>
        <line num="73" count="4" type="stmt"/>
        <line num="74" count="4" type="cond" truecount="4" falsecount="0"/>
        <line num="76" count="2" type="stmt"/>
        <line num="77" count="2" type="cond" truecount="2" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="2" type="cond" truecount="1" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="86" count="2" type="stmt"/>
        <line num="96" count="4" type="stmt"/>
        <line num="98" count="4" type="cond" truecount="0" falsecount="1"/>
        <line num="99" count="0" type="stmt"/>
        <line num="108" count="4" type="cond" truecount="2" falsecount="2"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="116" count="3" type="stmt"/>
        <line num="117" count="3" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="128" count="4" type="stmt"/>
        <line num="139" count="2" type="stmt"/>
        <line num="140" count="2" type="cond" truecount="2" falsecount="1"/>
        <line num="141" count="0" type="stmt"/>
        <line num="147" count="2" type="stmt"/>
        <line num="148" count="2" type="stmt"/>
        <line num="150" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="151" count="0" type="stmt"/>
        <line num="158" count="2" type="stmt"/>
        <line num="160" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="161" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="182" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="210" count="4" type="stmt"/>
        <line num="214" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="218" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="220" count="24" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="232" count="2" type="stmt"/>
        <line num="233" count="12" type="stmt"/>
        <line num="234" count="12" type="stmt"/>
        <line num="235" count="5" type="stmt"/>
        <line num="239" count="12" type="stmt"/>
        <line num="240" count="6" type="stmt"/>
        <line num="251" count="6" type="stmt"/>
        <line num="255" count="12" type="cond" truecount="3" falsecount="0"/>
        <line num="256" count="3" type="stmt"/>
        <line num="260" count="9" type="cond" truecount="3" falsecount="0"/>
        <line num="261" count="2" type="stmt"/>
        <line num="265" count="7" type="cond" truecount="1" falsecount="3"/>
        <line num="267" count="0" type="stmt"/>
        <line num="270" count="7" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="282" count="2" type="cond" truecount="2" falsecount="2"/>
        <line num="284" count="1" type="stmt"/>
        <line num="288" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="292" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="300" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="302" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="316" count="4" type="stmt"/>
        <line num="317" count="26" type="stmt"/>
        <line num="321" count="26" type="stmt"/>
        <line num="324" count="4" type="stmt"/>
        <line num="325" count="20" type="stmt"/>
        <line num="326" count="20" type="stmt"/>
        <line num="329" count="20" type="cond" truecount="1" falsecount="2"/>
        <line num="330" count="0" type="stmt"/>
        <line num="332" count="20" type="cond" truecount="3" falsecount="0"/>
        <line num="333" count="7" type="stmt"/>
        <line num="337" count="13" type="cond" truecount="2" falsecount="0"/>
        <line num="343" count="1" type="stmt"/>
        <line num="348" count="1" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="357" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="358" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="374" count="1" type="stmt"/>
        <line num="375" count="1" type="stmt"/>
        <line num="381" count="15" type="stmt"/>
        <line num="382" count="15" type="stmt"/>
        <line num="389" count="3" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/S/a/A1-K/packages/core/src/durak/index.ts">
        <metrics statements="326" coveredstatements="200" conditionals="157" coveredconditionals="83" methods="39" coveredmethods="28"/>
        <line num="8" count="2" type="stmt"/>
        <line num="25" count="2" type="stmt"/>
        <line num="28" count="15" type="stmt"/>
        <line num="31" count="15" type="stmt"/>
        <line num="32" count="15" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="48" count="0" type="stmt"/>
        <line num="56" count="29" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="70" count="15" type="stmt"/>
        <line num="71" count="15" type="stmt"/>
        <line num="74" count="15" type="stmt"/>
        <line num="75" count="15" type="stmt"/>
        <line num="78" count="15" type="stmt"/>
        <line num="81" count="15" type="stmt"/>
        <line num="83" count="15" type="stmt"/>
        <line num="101" count="15" type="stmt"/>
        <line num="102" count="15" type="stmt"/>
        <line num="103" count="15" type="stmt"/>
        <line num="105" count="15" type="stmt"/>
        <line num="106" count="60" type="stmt"/>
        <line num="107" count="540" type="stmt"/>
        <line num="111" count="15" type="stmt"/>
        <line num="118" count="15" type="stmt"/>
        <line num="119" count="525" type="stmt"/>
        <line num="120" count="525" type="stmt"/>
        <line num="128" count="15" type="stmt"/>
        <line num="130" count="15" type="stmt"/>
        <line num="131" count="90" type="stmt"/>
        <line num="132" count="192" type="cond" truecount="1" falsecount="0"/>
        <line num="133" count="192" type="stmt"/>
        <line num="134" count="192" type="cond" truecount="1" falsecount="0"/>
        <line num="135" count="192" type="stmt"/>
        <line num="146" count="15" type="stmt"/>
        <line num="147" count="15" type="stmt"/>
        <line num="150" count="15" type="stmt"/>
        <line num="151" count="32" type="stmt"/>
        <line num="152" count="32" type="stmt"/>
        <line num="153" count="192" type="cond" truecount="1" falsecount="0"/>
        <line num="154" count="42" type="stmt"/>
        <line num="155" count="42" type="cond" truecount="1" falsecount="0"/>
        <line num="156" count="27" type="stmt"/>
        <line num="157" count="27" type="stmt"/>
        <line num="164" count="15" type="cond" truecount="1" falsecount="0"/>
        <line num="165" count="1" type="stmt"/>
        <line num="168" count="15" type="stmt"/>
        <line num="175" count="56" type="stmt"/>
        <line num="187" count="56" type="stmt"/>
        <line num="194" count="65" type="stmt"/>
        <line num="201" count="29" type="stmt"/>
        <line num="202" count="65" type="stmt"/>
        <line num="210" count="11" type="cond" truecount="1" falsecount="1"/>
        <line num="211" count="11" type="stmt"/>
        <line num="212" count="11" type="stmt"/>
        <line num="215" count="11" type="stmt"/>
        <line num="221" count="11" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="239" count="19" type="stmt"/>
        <line num="240" count="19" type="stmt"/>
        <line num="241" count="19" type="stmt"/>
        <line num="242" count="19" type="stmt"/>
        <line num="243" count="19" type="cond" truecount="2" falsecount="0"/>
        <line num="244" count="19" type="stmt"/>
        <line num="247" count="19" type="cond" truecount="2" falsecount="0"/>
        <line num="249" count="16" type="cond" truecount="2" falsecount="0"/>
        <line num="254" count="8" type="stmt"/>
        <line num="257" count="8" type="cond" truecount="2" falsecount="3"/>
        <line num="258" count="0" type="stmt"/>
        <line num="261" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="266" count="7" type="cond" truecount="2" falsecount="1"/>
        <line num="267" count="0" type="stmt"/>
        <line num="273" count="7" type="cond" truecount="0" falsecount="1"/>
        <line num="277" count="0" type="stmt"/>
        <line num="282" count="7" type="stmt"/>
        <line num="284" count="1" type="stmt"/>
        <line num="291" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="299" count="2" type="stmt"/>
        <line num="300" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="304" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="305" count="0" type="stmt"/>
        <line num="310" count="2" type="stmt"/>
        <line num="313" count="1" type="cond" truecount="2" falsecount="3"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="319" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="323" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="330" count="1" type="stmt"/>
        <line num="340" count="19" type="cond" truecount="0" falsecount="1"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="345" count="30" type="stmt"/>
        <line num="346" count="19" type="cond" truecount="0" falsecount="1"/>
        <line num="347" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="352" count="19" type="stmt"/>
        <line num="353" count="19" type="cond" truecount="1" falsecount="0"/>
        <line num="354" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="355" count="2" type="stmt"/>
        <line num="359" count="17" type="stmt"/>
        <line num="360" count="17" type="cond" truecount="3" falsecount="2"/>
        <line num="362" count="10" type="stmt"/>
        <line num="363" count="10" type="stmt"/>
        <line num="365" count="5" type="stmt"/>
        <line num="366" count="5" type="stmt"/>
        <line num="368" count="2" type="stmt"/>
        <line num="369" count="2" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="0" type="stmt"/>
        <line num="379" count="17" type="cond" truecount="1" falsecount="0"/>
        <line num="381" count="17" type="stmt"/>
        <line num="389" count="17" type="cond" truecount="1" falsecount="0"/>
        <line num="400" count="17" type="stmt"/>
        <line num="412" count="10" type="cond" truecount="1" falsecount="0"/>
        <line num="413" count="8" type="stmt"/>
        <line num="417" count="6" type="stmt"/>
        <line num="418" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="419" count="0" type="stmt"/>
        <line num="429" count="2" type="cond" truecount="3" falsecount="1"/>
        <line num="430" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="431" count="0" type="stmt"/>
        <line num="434" count="2" type="stmt"/>
        <line num="442" count="10" type="cond" truecount="2" falsecount="1"/>
        <line num="443" count="0" type="stmt"/>
        <line num="444" count="0" type="stmt"/>
        <line num="454" count="10" type="stmt"/>
        <line num="455" count="10" type="cond" truecount="0" falsecount="1"/>
        <line num="456" count="0" type="stmt"/>
        <line num="459" count="0" type="stmt"/>
        <line num="461" count="10" type="stmt"/>
        <line num="462" count="10" type="stmt"/>
        <line num="465" count="10" type="cond" truecount="0" falsecount="1"/>
        <line num="468" count="0" type="stmt"/>
        <line num="471" count="0" type="stmt"/>
        <line num="475" count="10" type="stmt"/>
        <line num="476" count="10" type="stmt"/>
        <line num="479" count="10" type="stmt"/>
        <line num="482" count="10" type="stmt"/>
        <line num="483" count="10" type="stmt"/>
        <line num="485" count="10" type="stmt"/>
        <line num="488" count="10" type="stmt"/>
        <line num="500" count="16" type="cond" truecount="1" falsecount="0"/>
        <line num="504" count="7" type="stmt"/>
        <line num="507" count="9" type="cond" truecount="1" falsecount="2"/>
        <line num="508" count="0" type="stmt"/>
        <line num="511" count="9" type="cond" truecount="0" falsecount="1"/>
        <line num="516" count="0" type="stmt"/>
        <line num="518" count="9" type="stmt"/>
        <line num="526" count="5" type="cond" truecount="2" falsecount="1"/>
        <line num="527" count="0" type="stmt"/>
        <line num="528" count="0" type="stmt"/>
        <line num="538" count="5" type="stmt"/>
        <line num="539" count="5" type="cond" truecount="0" falsecount="1"/>
        <line num="540" count="0" type="stmt"/>
        <line num="543" count="0" type="stmt"/>
        <line num="545" count="5" type="stmt"/>
        <line num="548" count="5" type="stmt"/>
        <line num="549" count="5" type="cond" truecount="2" falsecount="1"/>
        <line num="550" count="0" type="stmt"/>
        <line num="551" count="0" type="stmt"/>
        <line num="553" count="5" type="stmt"/>
        <line num="556" count="5" type="cond" truecount="0" falsecount="1"/>
        <line num="559" count="0" type="stmt"/>
        <line num="562" count="0" type="stmt"/>
        <line num="566" count="5" type="stmt"/>
        <line num="567" count="5" type="stmt"/>
        <line num="570" count="5" type="stmt"/>
        <line num="571" count="6" type="stmt"/>
        <line num="573" count="5" type="stmt"/>
        <line num="575" count="5" type="cond" truecount="3" falsecount="1"/>
        <line num="578" count="5" type="cond" truecount="4" falsecount="1"/>
        <line num="579" count="0" type="stmt"/>
        <line num="580" count="0" type="stmt"/>
        <line num="585" count="5" type="cond" truecount="1" falsecount="1"/>
        <line num="586" count="5" type="stmt"/>
        <line num="587" count="5" type="stmt"/>
        <line num="594" count="0" type="stmt"/>
        <line num="595" count="0" type="stmt"/>
        <line num="600" count="5" type="stmt"/>
        <line num="601" count="5" type="stmt"/>
        <line num="608" count="2" type="stmt"/>
        <line num="609" count="2" type="stmt"/>
        <line num="610" count="2" type="stmt"/>
        <line num="611" count="2" type="stmt"/>
        <line num="612" count="2" type="stmt"/>
        <line num="613" count="2" type="stmt"/>
        <line num="624" count="2" type="stmt"/>
        <line num="625" count="2" type="stmt"/>
        <line num="628" count="2" type="stmt"/>
        <line num="629" count="2" type="stmt"/>
        <line num="630" count="2" type="stmt"/>
        <line num="635" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="637" count="0" type="stmt"/>
        <line num="638" count="0" type="stmt"/>
        <line num="640" count="0" type="stmt"/>
        <line num="641" count="0" type="stmt"/>
        <line num="645" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="649" count="0" type="stmt"/>
        <line num="650" count="0" type="stmt"/>
        <line num="654" count="2" type="stmt"/>
        <line num="655" count="2" type="stmt"/>
        <line num="656" count="2" type="stmt"/>
        <line num="661" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="663" count="0" type="stmt"/>
        <line num="664" count="0" type="stmt"/>
        <line num="666" count="0" type="stmt"/>
        <line num="667" count="0" type="stmt"/>
        <line num="671" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="675" count="0" type="stmt"/>
        <line num="678" count="0" type="stmt"/>
        <line num="681" count="2" type="stmt"/>
        <line num="682" count="2" type="stmt"/>
        <line num="683" count="2" type="stmt"/>
        <line num="685" count="2" type="stmt"/>
        <line num="688" count="2" type="stmt"/>
        <line num="689" count="2" type="stmt"/>
        <line num="703" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="704" count="0" type="stmt"/>
        <line num="705" count="0" type="stmt"/>
        <line num="709" count="2" type="stmt"/>
        <line num="713" count="2" type="stmt"/>
        <line num="716" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="717" count="0" type="stmt"/>
        <line num="721" count="2" type="stmt"/>
        <line num="722" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="723" count="0" type="stmt"/>
        <line num="727" count="2" type="stmt"/>
        <line num="729" count="2" type="stmt"/>
        <line num="736" count="0" type="stmt"/>
        <line num="737" count="0" type="stmt"/>
        <line num="745" count="0" type="stmt"/>
        <line num="746" count="0" type="stmt"/>
        <line num="749" count="0" type="stmt"/>
        <line num="752" count="0" type="stmt"/>
        <line num="753" count="0" type="stmt"/>
        <line num="754" count="0" type="stmt"/>
        <line num="759" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="761" count="0" type="stmt"/>
        <line num="764" count="0" type="stmt"/>
        <line num="766" count="0" type="stmt"/>
        <line num="767" count="0" type="stmt"/>
        <line num="771" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="775" count="0" type="stmt"/>
        <line num="776" count="0" type="stmt"/>
        <line num="779" count="0" type="stmt"/>
        <line num="780" count="0" type="stmt"/>
        <line num="782" count="0" type="stmt"/>
        <line num="790" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="791" count="0" type="stmt"/>
        <line num="792" count="0" type="stmt"/>
        <line num="796" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="797" count="0" type="stmt"/>
        <line num="798" count="0" type="stmt"/>
        <line num="800" count="0" type="stmt"/>
        <line num="801" count="0" type="stmt"/>
        <line num="803" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="804" count="0" type="stmt"/>
        <line num="805" count="0" type="stmt"/>
        <line num="809" count="0" type="stmt"/>
        <line num="812" count="0" type="stmt"/>
        <line num="815" count="0" type="stmt"/>
        <line num="818" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="819" count="0" type="stmt"/>
        <line num="823" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="825" count="0" type="stmt"/>
        <line num="829" count="0" type="stmt"/>
        <line num="831" count="0" type="stmt"/>
        <line num="834" count="0" type="stmt"/>
        <line num="842" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="843" count="0" type="stmt"/>
        <line num="846" count="0" type="stmt"/>
        <line num="850" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="852" count="0" type="stmt"/>
        <line num="855" count="0" type="stmt"/>
        <line num="862" count="0" type="stmt"/>
        <line num="865" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="866" count="0" type="stmt"/>
        <line num="871" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="873" count="0" type="stmt"/>
        <line num="877" count="0" type="stmt"/>
        <line num="879" count="0" type="stmt"/>
        <line num="882" count="0" type="stmt"/>
        <line num="889" count="2" type="stmt"/>
        <line num="890" count="2" type="stmt"/>
        <line num="891" count="2" type="stmt"/>
        <line num="893" count="2" type="stmt"/>
        <line num="894" count="5" type="stmt"/>
        <line num="896" count="5" type="cond" truecount="2" falsecount="1"/>
        <line num="897" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="898" count="4" type="stmt"/>
        <line num="899" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="900" count="4" type="stmt"/>
        <line num="905" count="5" type="stmt"/>
        <line num="909" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="912" count="0" type="stmt"/>
        <line num="929" count="20" type="stmt"/>
        <line num="930" count="47" type="stmt"/>
        <line num="932" count="20" type="stmt"/>
        <line num="933" count="47" type="stmt"/>
        <line num="937" count="20" type="cond" truecount="3" falsecount="0"/>
        <line num="938" count="1" type="stmt"/>
        <line num="940" count="1" type="stmt"/>
        <line num="941" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="943" count="1" type="stmt"/>
        <line num="944" count="1" type="stmt"/>
        <line num="948" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="950" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="951" count="1" type="stmt"/>
        <line num="952" count="1" type="stmt"/>
        <line num="954" count="0" type="stmt"/>
        <line num="958" count="0" type="stmt"/>
        <line num="959" count="0" type="stmt"/>
        <line num="960" count="0" type="stmt"/>
        <line num="961" count="0" type="stmt"/>
        <line num="965" count="1" type="stmt"/>
        <line num="971" count="1" type="stmt"/>
        <line num="972" count="1" type="stmt"/>
        <line num="973" count="1" type="stmt"/>
        <line num="975" count="19" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
