"use strict";
/**
 * Общие типы и интерфейсы для игрового ядра "Козырь Мастер"
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameEvent = exports.PlayerAction = exports.GameStatus = exports.DurakVariant = exports.CardRank = exports.CardSuit = void 0;
// Типы карт
var CardSuit;
(function (CardSuit) {
    CardSuit["HEARTS"] = "hearts";
    CardSuit["DIAMONDS"] = "diamonds";
    CardSuit["CLUBS"] = "clubs";
    CardSuit["SPADES"] = "spades";
})(CardSuit || (exports.CardSuit = CardSuit = {}));
var CardRank;
(function (CardRank) {
    CardRank["SIX"] = "6";
    CardRank["SEVEN"] = "7";
    CardRank["EIGHT"] = "8";
    CardRank["NINE"] = "9";
    CardRank["TEN"] = "10";
    CardRank["JACK"] = "jack";
    CardRank["QUEEN"] = "queen";
    CardRank["KING"] = "king";
    CardRank["ACE"] = "ace";
})(CardRank || (exports.CardRank = CardRank = {}));
// Варианты игры Дурак
var DurakVariant;
(function (DurakVariant) {
    DurakVariant["CLASSIC"] = "classic";
    DurakVariant["THROWING"] = "throwing";
    DurakVariant["TRANSFERABLE"] = "transferable";
    DurakVariant["TEAM"] = "team";
})(DurakVariant || (exports.DurakVariant = DurakVariant = {}));
// Статус игры
var GameStatus;
(function (GameStatus) {
    GameStatus["NOT_STARTED"] = "not_started";
    GameStatus["IN_PROGRESS"] = "in_progress";
    GameStatus["FINISHED"] = "finished";
})(GameStatus || (exports.GameStatus = GameStatus = {}));
// Действия игрока
var PlayerAction;
(function (PlayerAction) {
    PlayerAction["ATTACK"] = "attack";
    PlayerAction["DEFEND"] = "defend";
    PlayerAction["TAKE"] = "take";
    PlayerAction["PASS"] = "pass";
})(PlayerAction || (exports.PlayerAction = PlayerAction = {}));
// События игры
var GameEvent;
(function (GameEvent) {
    GameEvent["GAME_STARTED"] = "game_started";
    GameEvent["GAME_ENDED"] = "game_ended";
    GameEvent["PLAYER_MOVED"] = "player_moved";
    GameEvent["TURN_CHANGED"] = "turn_changed";
    GameEvent["CARDS_DEALT"] = "cards_dealt";
    GameEvent["ROUND_ENDED"] = "round_ended";
})(GameEvent || (exports.GameEvent = GameEvent = {}));
//# sourceMappingURL=types.js.map