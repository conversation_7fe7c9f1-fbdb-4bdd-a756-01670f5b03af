"use strict";
/**
 * Система ботов для игры "Дурак"
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BotFactory = exports.DurakBot = exports.BotDifficulty = void 0;
const types_1 = require("../types");
/**
 * Уровни сложности бота
 */
var BotDifficulty;
(function (BotDifficulty) {
    BotDifficulty["EASY"] = "easy";
    BotDifficulty["MEDIUM"] = "medium";
    BotDifficulty["HARD"] = "hard";
})(BotDifficulty || (exports.BotDifficulty = BotDifficulty = {}));
/**
 * Базовый класс бота для игры "Дурак"
 */
class DurakBot {
    constructor(id, difficulty = BotDifficulty.MEDIUM) {
        this.id = id;
        this.difficulty = difficulty;
        this.name = `Bot_${difficulty}_${id}`;
    }
    /**
     * Создать игрока-бота
     */
    createPlayer() {
        return {
            id: this.id,
            name: this.name,
            hand: [],
            isActive: false,
        };
    }
    /**
     * Принять решение о следующем ходе
     */
    makeDecision(gameState, playerId) {
        const player = gameState.players.find(p => p.id === playerId);
        if (!player) {
            throw new Error(`Player ${playerId} not found`);
        }
        const isAttacker = gameState.attackerIndex === gameState.players.indexOf(player);
        const isDefender = gameState.defenderIndex === gameState.players.indexOf(player);
        // Определяем возможные действия
        if (isAttacker && gameState.tableCards.length === 0) {
            // Атакующий должен атаковать
            return this.decideAttack(gameState, player);
        }
        else if (isDefender && this.hasUndefendedCards(gameState)) {
            // Защитник должен защищаться или брать карты
            return this.decideDefendOrTake(gameState, player);
        }
        else if (isAttacker && this.allCardsDefended(gameState)) {
            // Атакующий может подкинуть карты или сказать "бито"
            return this.decideThrowOrPass(gameState, player);
        }
        else if (!isDefender && this.allCardsDefended(gameState)) {
            // Другие игроки могут подкинуть карты
            return this.decideThrow(gameState, player);
        }
        // По умолчанию - пас
        return {
            action: types_1.PlayerAction.PASS,
            reasoning: "Default pass action",
        };
    }
    /**
     * Решение об атаке
     */
    decideAttack(gameState, player) {
        const validCards = this.getValidAttackCards(gameState, player);
        if (validCards.length === 0) {
            return {
                action: types_1.PlayerAction.PASS,
                reasoning: "No valid attack cards",
            };
        }
        // Выбираем карту в зависимости от сложности
        let cardIndex;
        switch (this.difficulty) {
            case BotDifficulty.EASY:
                // Легкий бот играет случайно
                cardIndex = validCards[Math.floor(Math.random() * validCards.length)];
                break;
            case BotDifficulty.MEDIUM:
                // Средний бот предпочитает младшие карты
                cardIndex = this.selectLowestCard(player, validCards, gameState.trumpSuit);
                break;
            case BotDifficulty.HARD:
                // Сложный бот использует стратегию
                cardIndex = this.selectStrategicAttackCard(player, validCards, gameState);
                break;
            default:
                cardIndex = validCards[0];
        }
        return {
            action: types_1.PlayerAction.ATTACK,
            cardIndex,
            reasoning: `Attack with card at index ${cardIndex}`,
        };
    }
    /**
     * Решение о защите или взятии карт
     */
    decideDefendOrTake(gameState, player) {
        const lastPair = gameState.tableCards.at(-1);
        if (!lastPair || lastPair.length !== 1) {
            return {
                action: types_1.PlayerAction.TAKE,
                reasoning: "No card to defend against",
            };
        }
        const attackingCard = lastPair[0];
        const validDefenseCards = this.getValidDefenseCards(player, attackingCard, gameState.trumpSuit);
        if (validDefenseCards.length === 0) {
            return {
                action: types_1.PlayerAction.TAKE,
                reasoning: "No valid defense cards",
            };
        }
        // Решаем, защищаться или брать карты
        const shouldDefend = this.shouldDefend(gameState, player, validDefenseCards);
        if (shouldDefend) {
            const cardIndex = this.selectDefenseCard(validDefenseCards, attackingCard, gameState.trumpSuit);
            return {
                action: types_1.PlayerAction.DEFEND,
                cardIndex,
                reasoning: `Defend with card at index ${cardIndex}`,
            };
        }
        else {
            return {
                action: types_1.PlayerAction.TAKE,
                reasoning: "Decided to take cards instead of defending",
            };
        }
    }
    /**
     * Решение о подкидывании или пасе
     */
    decideThrowOrPass(gameState, player) {
        const throwCards = this.getValidThrowCards(gameState, player);
        if (throwCards.length === 0 || !this.shouldThrow(gameState, player)) {
            return {
                action: types_1.PlayerAction.PASS,
                reasoning: "No valid throw cards or decided not to throw",
            };
        }
        const cardIndex = throwCards[0]; // Простая стратегия - первая подходящая карта
        return {
            action: types_1.PlayerAction.ATTACK,
            cardIndex,
            reasoning: `Throw card at index ${cardIndex}`,
        };
    }
    /**
     * Решение о подкидывании (для не-атакующих игроков)
     */
    decideThrow(gameState, player) {
        // Пока что не-атакующие игроки не подкидывают (упрощение)
        return {
            action: types_1.PlayerAction.PASS,
            reasoning: "Non-attacker decided not to throw",
        };
    }
    // Вспомогательные методы
    hasUndefendedCards(gameState) {
        return gameState.tableCards.some(pair => pair.length === 1);
    }
    allCardsDefended(gameState) {
        return gameState.tableCards.length > 0 && gameState.tableCards.every(pair => pair.length === 2);
    }
    getValidAttackCards(gameState, player) {
        if (gameState.tableCards.length === 0) {
            // Первая атака - любая карта
            return player.hand.map((_, index) => index);
        }
        // Подкидывание - карты с рангами, уже лежащими на столе
        const ranksOnTable = new Set(gameState.tableCards.flat().map(card => card.rank));
        return player.hand
            .map((card, index) => ({ card, index }))
            .filter(({ card }) => ranksOnTable.has(card.rank))
            .map(({ index }) => index);
    }
    getValidDefenseCards(player, attackingCard, trumpSuit) {
        return player.hand
            .map((card, index) => ({ card, index }))
            .filter(({ card }) => this.canDefend(attackingCard, card, trumpSuit))
            .map(({ index }) => index);
    }
    canDefend(attackCard, defendCard, trumpSuit) {
        const getRankValue = (rank) => {
            const values = {
                [types_1.CardRank.SIX]: 6,
                [types_1.CardRank.SEVEN]: 7,
                [types_1.CardRank.EIGHT]: 8,
                [types_1.CardRank.NINE]: 9,
                [types_1.CardRank.TEN]: 10,
                [types_1.CardRank.JACK]: 11,
                [types_1.CardRank.QUEEN]: 12,
                [types_1.CardRank.KING]: 13,
                [types_1.CardRank.ACE]: 14,
            };
            return values[rank];
        };
        // Карта той же масти, но старше
        if (attackCard.suit === defendCard.suit && getRankValue(defendCard.rank) > getRankValue(attackCard.rank)) {
            return true;
        }
        // Козырь бьет не-козырь
        if (defendCard.suit === trumpSuit && attackCard.suit !== trumpSuit) {
            return true;
        }
        // Козырь бьет козырь, если старше
        if (attackCard.suit === trumpSuit && defendCard.suit === trumpSuit &&
            getRankValue(defendCard.rank) > getRankValue(attackCard.rank)) {
            return true;
        }
        return false;
    }
    getValidThrowCards(gameState, player) {
        const ranksOnTable = new Set(gameState.tableCards.flat().map(card => card.rank));
        return player.hand
            .map((card, index) => ({ card, index }))
            .filter(({ card }) => ranksOnTable.has(card.rank))
            .map(({ index }) => index);
    }
    shouldDefend(gameState, player, validDefenseCards) {
        switch (this.difficulty) {
            case BotDifficulty.EASY:
                return Math.random() > 0.3; // 70% шанс защищаться
            case BotDifficulty.MEDIUM:
                // Защищается, если у него мало карт или много карт на столе
                return player.hand.length <= 3 || gameState.tableCards.length >= 3;
            case BotDifficulty.HARD:
                // Более сложная логика
                return this.strategicDefendDecision(gameState, player, validDefenseCards);
            default:
                return true;
        }
    }
    shouldThrow(gameState, player) {
        switch (this.difficulty) {
            case BotDifficulty.EASY:
                return Math.random() > 0.7; // 30% шанс подкинуть
            case BotDifficulty.MEDIUM:
                return player.hand.length > 5; // Подкидывает, если много карт
            case BotDifficulty.HARD:
                return this.strategicThrowDecision(gameState, player);
            default:
                return false;
        }
    }
    selectLowestCard(player, validIndices, trumpSuit) {
        const getRankValue = (rank) => {
            const values = {
                [types_1.CardRank.SIX]: 6, [types_1.CardRank.SEVEN]: 7, [types_1.CardRank.EIGHT]: 8, [types_1.CardRank.NINE]: 9,
                [types_1.CardRank.TEN]: 10, [types_1.CardRank.JACK]: 11, [types_1.CardRank.QUEEN]: 12, [types_1.CardRank.KING]: 13, [types_1.CardRank.ACE]: 14,
            };
            return values[rank];
        };
        return validIndices.reduce((lowestIndex, currentIndex) => {
            const lowestCard = player.hand[lowestIndex];
            const currentCard = player.hand[currentIndex];
            // Предпочитаем не-козыри
            if (lowestCard.suit === trumpSuit && currentCard.suit !== trumpSuit) {
                return currentIndex;
            }
            if (lowestCard.suit !== trumpSuit && currentCard.suit === trumpSuit) {
                return lowestIndex;
            }
            // Если обе карты одного типа (козыри или не-козыри), выбираем младшую
            return getRankValue(currentCard.rank) < getRankValue(lowestCard.rank) ? currentIndex : lowestIndex;
        });
    }
    selectStrategicAttackCard(player, validIndices, gameState) {
        // Пока что используем простую стратегию - младшая карта
        return this.selectLowestCard(player, validIndices, gameState.trumpSuit);
    }
    selectDefenseCard(validIndices, attackingCard, trumpSuit) {
        // Выбираем первую подходящую карту (можно улучшить)
        return validIndices[0];
    }
    strategicDefendDecision(gameState, player, validDefenseCards) {
        // Упрощенная стратегическая логика
        const cardsOnTable = gameState.tableCards.flat().length;
        const playerCardCount = player.hand.length;
        // Не защищается, если на столе много карт и у игрока мало карт
        if (cardsOnTable >= 6 && playerCardCount <= 4) {
            return false;
        }
        return true;
    }
    strategicThrowDecision(gameState, player) {
        // Подкидывает, если у защитника много карт
        const defender = gameState.players[gameState.defenderIndex];
        return defender.hand.length > 6;
    }
}
exports.DurakBot = DurakBot;
/**
 * Фабрика для создания ботов
 */
class BotFactory {
    /**
     * Создать бота с указанной сложностью
     */
    static createBot(difficulty = BotDifficulty.MEDIUM) {
        const id = `bot_${++this.botCounter}`;
        return new DurakBot(id, difficulty);
    }
    /**
     * Создать несколько ботов
     */
    static createBots(count, difficulty = BotDifficulty.MEDIUM) {
        return Array.from({ length: count }, () => this.createBot(difficulty));
    }
}
exports.BotFactory = BotFactory;
BotFactory.botCounter = 0;
//# sourceMappingURL=bot.js.map