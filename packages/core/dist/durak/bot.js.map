{"version": 3, "file": "bot.js", "sourceRoot": "", "sources": ["../../src/durak/bot.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAEH,oCAOkB;AAElB;;GAEG;AACH,IAAY,aAIX;AAJD,WAAY,aAAa;IACvB,8BAAa,CAAA;IACb,kCAAiB,CAAA;IACjB,8BAAa,CAAA;AACf,CAAC,EAJW,aAAa,6BAAb,aAAa,QAIxB;AAWD;;GAEG;AACH,MAAa,QAAQ;IAKnB,YAAY,EAAU,EAAE,aAA4B,aAAa,CAAC,MAAM;QACtE,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,OAAO,UAAU,IAAI,EAAE,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,EAAE;YACR,QAAQ,EAAE,KAAK;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,SAAoB,EAAE,QAAgB;QACxD,MAAM,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;QAC9D,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,UAAU,QAAQ,YAAY,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,UAAU,GAAG,SAAS,CAAC,aAAa,KAAK,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACjF,MAAM,UAAU,GAAG,SAAS,CAAC,aAAa,KAAK,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAEjF,gCAAgC;QAChC,IAAI,UAAU,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpD,6BAA6B;YAC7B,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAC9C,CAAC;aAAM,IAAI,UAAU,IAAI,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5D,6CAA6C;YAC7C,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QACpD,CAAC;aAAM,IAAI,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC;YAC1D,qDAAqD;YACrD,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QACnD,CAAC;aAAM,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3D,sCAAsC;YACtC,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC;QAED,qBAAqB;QACrB,OAAO;YACL,MAAM,EAAE,oBAAY,CAAC,IAAI;YACzB,SAAS,EAAE,qBAAqB;SACjC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,SAAoB,EAAE,MAAc;QACvD,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAE/D,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO;gBACL,MAAM,EAAE,oBAAY,CAAC,IAAI;gBACzB,SAAS,EAAE,uBAAuB;aACnC,CAAC;QACJ,CAAC;QAED,4CAA4C;QAC5C,IAAI,SAAiB,CAAC;QAEtB,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;YACxB,KAAK,aAAa,CAAC,IAAI;gBACrB,6BAA6B;gBAC7B,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;gBACtE,MAAM;YAER,KAAK,aAAa,CAAC,MAAM;gBACvB,yCAAyC;gBACzC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;gBAC3E,MAAM;YAER,KAAK,aAAa,CAAC,IAAI;gBACrB,mCAAmC;gBACnC,SAAS,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;gBAC1E,MAAM;YAER;gBACE,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO;YACL,MAAM,EAAE,oBAAY,CAAC,MAAM;YAC3B,SAAS;YACT,SAAS,EAAE,6BAA6B,SAAS,EAAE;SACpD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,SAAoB,EAAE,MAAc;QAC7D,MAAM,QAAQ,GAAG,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvC,OAAO;gBACL,MAAM,EAAE,oBAAY,CAAC,IAAI;gBACzB,SAAS,EAAE,2BAA2B;aACvC,CAAC;QACJ,CAAC;QAED,MAAM,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,aAAa,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;QAEhG,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO;gBACL,MAAM,EAAE,oBAAY,CAAC,IAAI;gBACzB,SAAS,EAAE,wBAAwB;aACpC,CAAC;QACJ,CAAC;QAED,qCAAqC;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC;QAE7E,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,aAAa,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;YAChG,OAAO;gBACL,MAAM,EAAE,oBAAY,CAAC,MAAM;gBAC3B,SAAS;gBACT,SAAS,EAAE,6BAA6B,SAAS,EAAE;aACpD,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO;gBACL,MAAM,EAAE,oBAAY,CAAC,IAAI;gBACzB,SAAS,EAAE,4CAA4C;aACxD,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,SAAoB,EAAE,MAAc;QAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAE9D,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,CAAC;YACpE,OAAO;gBACL,MAAM,EAAE,oBAAY,CAAC,IAAI;gBACzB,SAAS,EAAE,8CAA8C;aAC1D,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,8CAA8C;QAC/E,OAAO;YACL,MAAM,EAAE,oBAAY,CAAC,MAAM;YAC3B,SAAS;YACT,SAAS,EAAE,uBAAuB,SAAS,EAAE;SAC9C,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,SAAoB,EAAE,MAAc;QACtD,0DAA0D;QAC1D,OAAO;YACL,MAAM,EAAE,oBAAY,CAAC,IAAI;YACzB,SAAS,EAAE,mCAAmC;SAC/C,CAAC;IACJ,CAAC;IAED,yBAAyB;IAEjB,kBAAkB,CAAC,SAAoB;QAC7C,OAAO,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;IAC9D,CAAC;IAEO,gBAAgB,CAAC,SAAoB;QAC3C,OAAO,SAAS,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;IAClG,CAAC;IAEO,mBAAmB,CAAC,SAAoB,EAAE,MAAc;QAC9D,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtC,6BAA6B;YAC7B,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC;QAED,wDAAwD;QACxD,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACjF,OAAO,MAAM,CAAC,IAAI;aACf,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;aACvC,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACjD,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAEO,oBAAoB,CAAC,MAAc,EAAE,aAAmB,EAAE,SAAmB;QACnF,OAAO,MAAM,CAAC,IAAI;aACf,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;aACvC,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;aACpE,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAEO,SAAS,CAAC,UAAgB,EAAE,UAAgB,EAAE,SAAmB;QACvE,MAAM,YAAY,GAAG,CAAC,IAAc,EAAU,EAAE;YAC9C,MAAM,MAAM,GAA6B;gBACvC,CAAC,gBAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACjB,CAAC,gBAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnB,CAAC,gBAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnB,CAAC,gBAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClB,CAAC,gBAAQ,CAAC,GAAG,CAAC,EAAE,EAAE;gBAClB,CAAC,gBAAQ,CAAC,IAAI,CAAC,EAAE,EAAE;gBACnB,CAAC,gBAAQ,CAAC,KAAK,CAAC,EAAE,EAAE;gBACpB,CAAC,gBAAQ,CAAC,IAAI,CAAC,EAAE,EAAE;gBACnB,CAAC,gBAAQ,CAAC,GAAG,CAAC,EAAE,EAAE;aACnB,CAAC;YACF,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC,CAAC;QAEF,gCAAgC;QAChC,IAAI,UAAU,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,IAAI,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACzG,OAAO,IAAI,CAAC;QACd,CAAC;QAED,wBAAwB;QACxB,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACnE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,kCAAkC;QAClC,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS;YAC9D,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAClE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,kBAAkB,CAAC,SAAoB,EAAE,MAAc;QAC7D,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACjF,OAAO,MAAM,CAAC,IAAI;aACf,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;aACvC,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACjD,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAEO,YAAY,CAAC,SAAoB,EAAE,MAAc,EAAE,iBAA2B;QACpF,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;YACxB,KAAK,aAAa,CAAC,IAAI;gBACrB,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,sBAAsB;YAEpD,KAAK,aAAa,CAAC,MAAM;gBACvB,4DAA4D;gBAC5D,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC;YAErE,KAAK,aAAa,CAAC,IAAI;gBACrB,uBAAuB;gBACvB,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC;YAE5E;gBACE,OAAO,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,SAAoB,EAAE,MAAc;QACtD,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;YACxB,KAAK,aAAa,CAAC,IAAI;gBACrB,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,qBAAqB;YAEnD,KAAK,aAAa,CAAC,MAAM;gBACvB,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,+BAA+B;YAEhE,KAAK,aAAa,CAAC,IAAI;gBACrB,OAAO,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAExD;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,MAAc,EAAE,YAAsB,EAAE,SAAmB;QAClF,MAAM,YAAY,GAAG,CAAC,IAAc,EAAU,EAAE;YAC9C,MAAM,MAAM,GAA6B;gBACvC,CAAC,gBAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,gBAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,gBAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,gBAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC/E,CAAC,gBAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,gBAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,gBAAQ,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,gBAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,gBAAQ,CAAC,GAAG,CAAC,EAAE,EAAE;aACvG,CAAC;YACF,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC,CAAC;QAEF,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,YAAY,EAAE,EAAE;YACvD,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5C,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE9C,yBAAyB;YACzB,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBACpE,OAAO,YAAY,CAAC;YACtB,CAAC;YACD,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBACpE,OAAO,WAAW,CAAC;YACrB,CAAC;YAED,sEAAsE;YACtE,OAAO,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC;QACrG,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,yBAAyB,CAAC,MAAc,EAAE,YAAsB,EAAE,SAAoB;QAC5F,wDAAwD;QACxD,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,YAAY,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAC1E,CAAC;IAEO,iBAAiB,CAAC,YAAsB,EAAE,aAAmB,EAAE,SAAmB;QACxF,oDAAoD;QACpD,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAEO,uBAAuB,CAAC,SAAoB,EAAE,MAAc,EAAE,iBAA2B;QAC/F,mCAAmC;QACnC,MAAM,YAAY,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC;QACxD,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;QAE3C,+DAA+D;QAC/D,IAAI,YAAY,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC,EAAE,CAAC;YAC9C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,sBAAsB,CAAC,SAAoB,EAAE,MAAc;QACjE,2CAA2C;QAC3C,MAAM,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAC5D,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAClC,CAAC;CACF;AA9UD,4BA8UC;AAED;;GAEG;AACH,MAAa,UAAU;IAGrB;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,aAA4B,aAAa,CAAC,MAAM;QAC/D,MAAM,EAAE,GAAG,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC;QACtC,OAAO,IAAI,QAAQ,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,KAAa,EAAE,aAA4B,aAAa,CAAC,MAAM;QAC/E,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;IACzE,CAAC;;AAhBH,gCAiBC;AAhBgB,qBAAU,GAAG,CAAC,CAAC"}