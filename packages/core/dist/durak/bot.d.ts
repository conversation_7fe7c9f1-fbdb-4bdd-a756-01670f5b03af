/**
 * Система ботов для игры "Дурак"
 */
import { Player, GameState, PlayerAction } from "../types";
/**
 * Уровни сложности бота
 */
export declare enum BotDifficulty {
    EASY = "easy",
    MEDIUM = "medium",
    HARD = "hard"
}
/**
 * Интерфейс для принятия решений ботом
 */
export interface BotDecision {
    action: PlayerAction;
    cardIndex?: number;
    reasoning?: string;
}
/**
 * Базовый класс бота для игры "Дурак"
 */
export declare class DurakBot {
    readonly id: string;
    readonly difficulty: BotDifficulty;
    private readonly name;
    constructor(id: string, difficulty?: BotDifficulty);
    /**
     * Создать игрока-бота
     */
    createPlayer(): Player;
    /**
     * Принять решение о следующем ходе
     */
    makeDecision(gameState: GameState, playerId: string): BotDecision;
    /**
     * Решение об атаке
     */
    private decideAttack;
    /**
     * Решение о защите или взятии карт
     */
    private decideDefendOrTake;
    /**
     * Решение о подкидывании или пасе
     */
    private decideThrowOrPass;
    /**
     * Решение о подкидывании (для не-атакующих игроков)
     */
    private decideThrow;
    private hasUndefendedCards;
    private allCardsDefended;
    private getValidAttackCards;
    private getValidDefenseCards;
    private canDefend;
    private getValidThrowCards;
    private shouldDefend;
    private shouldThrow;
    private selectLowestCard;
    private selectStrategicAttackCard;
    private selectDefenseCard;
    private strategicDefendDecision;
    private strategicThrowDecision;
}
/**
 * Фабрика для создания ботов
 */
export declare class BotFactory {
    private static botCounter;
    /**
     * Создать бота с указанной сложностью
     */
    static createBot(difficulty?: BotDifficulty): DurakBot;
    /**
     * Создать несколько ботов
     */
    static createBots(count: number, difficulty?: BotDifficulty): DurakBot[];
}
