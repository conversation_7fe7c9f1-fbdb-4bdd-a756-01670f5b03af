/**
 * Модуль игры "Дурак"
 *
 * Содержит основную логику и правила игры "Дурак"
 */
import { Card, CardSuit, Player, GameRules, GameState, PlayerAction, GameEventHandler } from "../types";
/**
 * Класс игры "Дурак"
 */
export declare class DurakGame {
    private state;
    private rules;
    private eventHandlers;
    constructor(players: Player[], rules: GameRules);
    /**
     * Добавить обработчик событий
     */
    addEventListener(handler: GameEventHandler): void;
    /**
     * Удалить обработчик событий
     */
    removeEventListener(handler: GameEventHandler): void;
    /**
     * Отправить событие всем обработчикам
     */
    private emitEvent;
    /**
     * Инициализация игры
     */
    private initializeGame;
    /**
     * Создание колоды карт
     */
    private createDeck;
    /**
     * Перемешивание колоды
     */
    private shuffleDeck;
    /**
     * Раздача карт игрокам
     */
    private dealCards;
    /**
     * Определение первого игрока
     */
    private determineFirstPlayer;
    /**
     * Получение числового значения ранга карты
     */
    private getRankValue;
    /**
     * Получение текущего состояния игры
     */
    getState(): GameState;
    /**
     * Обновление статуса активного игрока
     */
    private updateActivePlayer;
    /**
     * Начало игры
     */
    startGame(): void;
    /**
     * Проверка, разрешено ли действие игроку в текущем состоянии игры.
     * @param playerIndex Индекс игрока, выполняющего действие.
     * @param action Тип действия (атака, защита, пас, взять).
     * @returns Объект с флагом `allowed` (true/false) и опциональным сообщением об ошибке `error`.
     */
    private _isActionAllowed;
    /**
     * Выполнение хода игрока
     */
    makeMove(playerId: string, action: PlayerAction, cardIndex?: number): boolean;
    /**
     * Проверка валидности атакующей карты
     */
    private isValidAttack;
    /**
     * Обработка атаки
     */
    private handleAttack;
    /**
     * Проверка валидности защищающейся карты
     */
    isValidDefense(attackCard: Card, defendCard: Card, trumpSuit: CardSuit): boolean;
    /**
     * Обработка защиты
     */
    private handleDefend;
    /**
     * Вспомогательный метод: Защитник берет карты со стола
     */
    private _defenderTakesCards;
    /**
     * Вспомогательный метод: Обновляет роли после того, как защитник взял карты.
     * Ход переходит к следующему игроку после взявшего.
     * @returns {boolean} Возвращает true, если игра окончена, иначе false.
     */
    private _updateRolesAfterTake;
    /**
     * Обработка взятия карт (защитник берет)
     */
    private handleTake;
    /**
     * Вспомогательный метод: Перемещает карты со стола в отбой
     */
    private _clearTableToDiscardPile;
    /**
     * Вспомогательный метод: Определяет следующего атакующего и защитника
     * @returns {boolean} Возвращает true, если удалось определить роли (игра продолжается), иначе false (игра окончена).
     */
    private _determineNextRoles;
    /**
     * Обработка паса (бито) - атакующий завершает раунд после успешной защиты
     */
    private handlePass;
    /**
     * Обработка завершения хода (атакующий говорит "бито" или "пас")
     */
    private handleDone;
    /**
     * Пополнение рук игроков из колоды до нужного количества
     */
    private replenishHands;
    /**
     * Проверка окончания игры
     */
    private checkGameEnd;
}
