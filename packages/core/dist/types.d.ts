/**
 * Общие типы и интерфейсы для игрового ядра "Козырь Мастер"
 */
export declare enum CardSuit {
    HEARTS = "hearts",
    DIAMONDS = "diamonds",
    CLUBS = "clubs",
    SPADES = "spades"
}
export declare enum CardRank {
    SIX = "6",
    SEVEN = "7",
    EIGHT = "8",
    NINE = "9",
    TEN = "10",
    JACK = "jack",
    QUEEN = "queen",
    KING = "king",
    ACE = "ace"
}
export interface Card {
    suit: CardSuit;
    rank: CardRank;
}
export interface Player {
    id: string;
    name?: string;
    hand: Card[];
    isActive: boolean;
}
export declare enum DurakVariant {
    CLASSIC = "classic",// Классический
    THROWING = "throwing",// Подкидной
    TRANSFERABLE = "transferable",// Переводной
    TEAM = "team"
}
export interface GameRules {
    variant: DurakVariant;
    numberOfPlayers: number;
    deckSize?: 36 | 52;
    initialHandSize: number;
    attackLimit: number;
    maxTableCards?: number;
}
export interface GameState {
    players: Player[];
    deck: Card[];
    tableCards: Card[][];
    discardPile: Card[];
    trumpCard?: Card;
    trumpSuit: CardSuit;
    currentPlayerIndex: number;
    attackerIndex: number;
    defenderIndex: number;
    gameStatus: GameStatus;
    winner?: Player;
    loser?: Player;
    passCount?: number;
    defenderTookCards?: boolean;
}
export declare enum GameStatus {
    NOT_STARTED = "not_started",
    IN_PROGRESS = "in_progress",
    FINISHED = "finished"
}
export declare enum PlayerAction {
    ATTACK = "attack",// Атака (ход)
    DEFEND = "defend",// Защита
    TAKE = "take",// Взять карты
    PASS = "pass"
}
export declare enum GameEvent {
    GAME_STARTED = "game_started",
    GAME_ENDED = "game_ended",
    PLAYER_MOVED = "player_moved",
    TURN_CHANGED = "turn_changed",
    CARDS_DEALT = "cards_dealt",
    ROUND_ENDED = "round_ended"
}
export interface GameEventData {
    type: GameEvent;
    gameState: GameState;
    playerId?: string;
    action?: PlayerAction;
    cardIndex?: number;
    message?: string;
}
export type GameEventHandler = (eventData: GameEventData) => void;
