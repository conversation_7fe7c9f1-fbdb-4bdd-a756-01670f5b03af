"use strict";
/**
 * Игровое ядро "Козырь Мастер"
 *
 * Этот файл экспортирует основные классы и интерфейсы игрового ядра
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BotDifficulty = exports.BotFactory = exports.DurakBot = void 0;
// Экспорт общих типов
__exportStar(require("./types"), exports);
// Экспорт основной логики игры Дурак
__exportStar(require("./durak"), exports);
// Экспорт системы ботов
var bot_1 = require("./durak/bot");
Object.defineProperty(exports, "DurakBot", { enumerable: true, get: function () { return bot_1.DurakBot; } });
Object.defineProperty(exports, "BotFactory", { enumerable: true, get: function () { return bot_1.BotFactory; } });
Object.defineProperty(exports, "BotDifficulty", { enumerable: true, get: function () { return bot_1.BotDifficulty; } });
//# sourceMappingURL=index.js.map