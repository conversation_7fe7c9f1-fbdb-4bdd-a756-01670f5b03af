/**
 * Общие типы и интерфейсы для игрового ядра "Козырь Мастер"
 */

// Типы карт
export enum CardSuit {
  HEARTS = "hearts",
  DIAMONDS = "diamonds",
  CLUBS = "clubs",
  SPADES = "spades",
}

export enum CardRank {
  SIX = "6",
  SEVEN = "7",
  EIGHT = "8",
  NINE = "9",
  TEN = "10",
  JACK = "jack",
  QUEEN = "queen",
  KING = "king",
  ACE = "ace",
}

export interface Card {
  suit: CardSuit;
  rank: CardRank;
}

// Игрок
export interface Player {
  id: string;
  name?: string; // Сделаем имя опциональным, так как оно не используется в логике DurakGame
  hand: Card[];
  isActive: boolean;
}

// Варианты игры Дурак
export enum DurakVariant {
  CLASSIC = "classic", // Классический
  THROWING = "throwing", // Подкидной
  TRANSFERABLE = "transferable", // Переводной
  TEAM = "team", // Командный
}

// Правила игры
export interface GameRules {
  variant: DurakVariant;
  numberOfPlayers: number; // Добавим для ясности
  deckSize?: 36 | 52; // Опционально, по умолчанию 36
  initialHandSize: number; // Количество карт при раздаче
  attackLimit: number; // Максимальное количество карт для атаки/подкидывания за раунд
  maxTableCards?: number; // Максимальное количество карт на столе (опционально, по умолчанию 6)
}

// Состояние игры
export interface GameState {
  players: Player[];
  deck: Card[];
  tableCards: Card[][];
  discardPile: Card[];
  trumpCard?: Card;
  trumpSuit: CardSuit;
  currentPlayerIndex: number;
  attackerIndex: number;
  defenderIndex: number;
  gameStatus: GameStatus;
  winner?: Player;
  loser?: Player; // Add loser property
  passCount?: number; // Добавим для отслеживания пасов
  defenderTookCards?: boolean; // Add flag for defender taking cards
}

// Статус игры
export enum GameStatus {
  NOT_STARTED = "not_started",
  IN_PROGRESS = "in_progress",
  FINISHED = "finished",
}

// Действия игрока
export enum PlayerAction {
  ATTACK = "attack", // Атака (ход)
  DEFEND = "defend", // Защита
  TAKE = "take", // Взять карты
  PASS = "pass", // Пас (бито)
}

// События игры
export enum GameEvent {
  GAME_STARTED = "game_started",
  GAME_ENDED = "game_ended",
  PLAYER_MOVED = "player_moved",
  TURN_CHANGED = "turn_changed",
  CARDS_DEALT = "cards_dealt",
  ROUND_ENDED = "round_ended",
}

// Данные события
export interface GameEventData {
  type: GameEvent;
  gameState: GameState;
  playerId?: string;
  action?: PlayerAction;
  cardIndex?: number;
  message?: string;
}

// Обработчик событий
export type GameEventHandler = (eventData: GameEventData) => void;
