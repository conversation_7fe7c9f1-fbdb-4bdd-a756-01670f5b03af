/**
 * Модуль игры "Дурак"
 *
 * Содержит основную логику и правила игры "Дурак"
 */

// Импортируем общие типы
import {
  Card,
  CardRank,
  CardSuit,
  Player,
  GameRules,
  GameState,
  GameStatus,
  PlayerAction,
  GameEvent,
  GameEventData,
  GameEventHandler,
} from "../types";

/**
 * Класс игры "Дурак"
 */
export class DurakGame {
  private state: GameState;
  private rules: GameRules;
  private eventHandlers: GameEventHandler[] = [];

  constructor(players: Player[], rules: GameRules) {
    this.rules = rules;
    this.state = this.initializeGame(players);
  }

  /**
   * Добавить обработчик событий
   */
  public addEventListener(handler: GameEventHandler): void {
    this.eventHandlers.push(handler);
  }

  /**
   * Удалить обработчик событий
   */
  public removeEventListener(handler: GameEvent<PERSON><PERSON><PERSON>): void {
    const index = this.eventHandlers.indexOf(handler);
    if (index > -1) {
      this.eventHandlers.splice(index, 1);
    }
  }

  /**
   * Отправить событие всем обработчикам
   */
  private emitEvent(eventData: GameEventData): void {
    this.eventHandlers.forEach(handler => {
      try {
        handler(eventData);
      } catch (error) {
        console.error('Error in game event handler:', error);
      }
    });
  }

  /**
   * Инициализация игры
   */
  private initializeGame(players: Player[]): GameState {
    // Создание и перемешивание колоды
    const deck = this.createDeck();
    this.shuffleDeck(deck);

    // Определение козырной карты
    const trumpCard = deck[deck.length - 1];
    const trumpSuit = trumpCard.suit;

    // Раздача карт игрокам
    this.dealCards(players, deck);

    // Определение первого игрока (у кого наименьший козырь)
    const firstPlayerIndex = this.determineFirstPlayer(players, trumpSuit);

    return {
      players,
      deck,
      tableCards: [],
      discardPile: [],
      trumpCard,
      trumpSuit,
      currentPlayerIndex: firstPlayerIndex,
      attackerIndex: firstPlayerIndex,
      defenderIndex: (firstPlayerIndex + 1) % players.length,
      gameStatus: GameStatus.NOT_STARTED,
    };
  }

  /**
   * Создание колоды карт
   */
  private createDeck(): Card[] {
    const deck: Card[] = [];
    const suits = Object.values(CardSuit);
    const ranks = Object.values(CardRank);

    for (const suit of suits) {
      for (const rank of ranks) {
        deck.push({ suit, rank });
      }
    }

    return deck;
  }

  /**
   * Перемешивание колоды
   */
  private shuffleDeck(deck: Card[]): void {
    for (let i = deck.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [deck[i], deck[j]] = [deck[j], deck[i]];
    }
  }

  /**
   * Раздача карт игрокам
   */
  private dealCards(players: Player[], deck: Card[]): void {
    const cardsPerPlayer = this.rules.initialHandSize;

    for (let i = 0; i < cardsPerPlayer; i++) {
      for (const player of players) {
        if (deck.length > 0) {
          const card = deck.shift();
          if (card) {
            player.hand.push(card);
          }
        }
      }
    }
  }

  /**
   * Определение первого игрока
   */
  private determineFirstPlayer(players: Player[], trumpSuit: CardSuit): number {
    let minTrumpRankIndex = -1;
    let minTrumpRankValue = Infinity;

    // Поиск игрока с наименьшим козырем
    for (let i = 0; i < players.length; i++) {
      const player = players[i];
      for (const card of player.hand) {
        if (card.suit === trumpSuit) {
          const rankValue = this.getRankValue(card.rank);
          if (rankValue < minTrumpRankValue) {
            minTrumpRankValue = rankValue;
            minTrumpRankIndex = i;
          }
        }
      }
    }

    // Если ни у кого нет козырей, выбираем случайного игрока
    if (minTrumpRankIndex === -1) {
      minTrumpRankIndex = Math.floor(Math.random() * players.length);
    }

    return minTrumpRankIndex;
  }

  /**
   * Получение числового значения ранга карты
   */
  private getRankValue(rank: CardRank): number {
    const rankValues: Record<CardRank, number> = {
      [CardRank.SIX]: 6,
      [CardRank.SEVEN]: 7,
      [CardRank.EIGHT]: 8,
      [CardRank.NINE]: 9,
      [CardRank.TEN]: 10,
      [CardRank.JACK]: 11,
      [CardRank.QUEEN]: 12,
      [CardRank.KING]: 13,
      [CardRank.ACE]: 14,
    };

    return rankValues[rank];
  }

  /**
   * Получение текущего состояния игры
   */
  public getState(): GameState {
    return { ...this.state };
  }

  /**
   * Обновление статуса активного игрока
   */
  private updateActivePlayer(): void {
    this.state.players.forEach((player, index) => {
      player.isActive = index === this.state.currentPlayerIndex;
    });
  }

  /**
   * Начало игры
   */
  public startGame(): void {
    if (this.state.gameStatus === GameStatus.NOT_STARTED) {
      this.state.gameStatus = GameStatus.IN_PROGRESS;
      this.updateActivePlayer(); // Устанавливаем первого активного игрока

      // Отправляем событие о начале игры
      this.emitEvent({
        type: GameEvent.GAME_STARTED,
        gameState: this.getState(),
        message: `Game started. First turn: Player ${this.state.players[this.state.currentPlayerIndex].id}`,
      });

      console.log(
        `Game started. First turn: Player ${this.state.players[this.state.currentPlayerIndex].id}`,
      );
    } else {
      console.warn("Game already started or finished.");
    }
  }

  /**
   * Проверка, разрешено ли действие игроку в текущем состоянии игры.
   * @param playerIndex Индекс игрока, выполняющего действие.
   * @param action Тип действия (атака, защита, пас, взять).
   * @returns Объект с флагом `allowed` (true/false) и опциональным сообщением об ошибке `error`.
   */
  private _isActionAllowed(
    playerIndex: number,
    action: PlayerAction,
  ): { allowed: boolean; error?: string } {
    const isCurrentPlayerTurn = playerIndex === this.state.currentPlayerIndex;
    const isAttacker = playerIndex === this.state.attackerIndex;
    const isDefender = playerIndex === this.state.defenderIndex;
    const lastPair = this.state.tableCards.at(-1);
    const lastPairDefended = !!lastPair && lastPair.length === 2;
    const tableIsEmpty = this.state.tableCards.length === 0;

    // 1. Ход текущего игрока (атакующий или защитник)
    if (isCurrentPlayerTurn) {
      // Атакующий может атаковать, если стол пуст или после взятия карт защитником
      if (
        isAttacker &&
        action === PlayerAction.ATTACK &&
        (tableIsEmpty || this.state.defenderTookCards)
      ) {
        return { allowed: true };
      }
      // Атакующий может пасовать (бито), если защита была успешной
      else if (isAttacker && action === PlayerAction.PASS && lastPairDefended) {
        return { allowed: true };
      }
      // Защитник может защищаться или взять карты
      else if (
        isDefender &&
        (action === PlayerAction.DEFEND || action === PlayerAction.TAKE)
      ) {
        // Дополнительная проверка для TAKE: можно брать только если есть что брать
        if (action === PlayerAction.TAKE && tableIsEmpty) {
          return {
            allowed: false,
            error: `Error: Cannot TAKE, no cards on the table.`,
          };
        }
        // Дополнительная проверка для DEFEND: можно защищаться только если есть атакующая карта
        if (
          action === PlayerAction.DEFEND &&
          (!lastPair || lastPair.length !== 1)
        ) {
          return {
            allowed: false,
            error: `Error: Cannot DEFEND, no attacking card found.`,
          };
        }
        return { allowed: true };
      } else {
        return {
          allowed: false,
          error: `Error: Player ${this.state.players[playerIndex].id} (current: ${this.state.currentPlayerIndex}) cannot perform action ${action} at this stage.`,
        };
      }
    }
    // 2. Подкидывание (не защитник, после успешной защиты, ход у защитника)
    else if (
      action === PlayerAction.ATTACK &&
      !isDefender &&
      lastPairDefended &&
      this.state.currentPlayerIndex === this.state.defenderIndex
    ) {
      // Дополнительная проверка: количество карт на столе не должно превышать лимит атаки
      // И не больше, чем карт у защитника на руках в начале раунда атаки (если стол пуст)
      const defender = this.state.players[this.state.defenderIndex];
      const currentAttackLimit = this.state.tableCards.length === 0
                               ? Math.min(this.rules.attackLimit, defender.hand.length)
                               : this.rules.attackLimit;

      if (this.state.tableCards.flat().length / 2 >= currentAttackLimit) { // Считаем пары карт (атака+защита) или одиночные карты атаки
        return {
          allowed: false,
          error: `Error: Cannot podkidnut, attack limit (${currentAttackLimit}) reached.`,
        };
      }
      return { allowed: true }; // Разрешаем подкидывание
    }
    // 3. Подкидывание карт (любым игроком, кроме защитника, после успешной защиты)
    else if (action === PlayerAction.ATTACK && !isDefender && lastPairDefended) {
        // Проверяем, есть ли у игрока карты, которые можно подкинуть
        const player = this.state.players[playerIndex];
        const validPodkidnoyCards = player.hand.filter(card =>
            this.state.tableCards.flat().some(tableCard => tableCard.rank === card.rank)
        );
        if (validPodkidnoyCards.length > 0) {
            // TODO: Логика выбора карты для подкидывания (если их несколько)
            // Пока просто проверяем возможность
            // const currentPodkidnoy = validPodkidnoyCards[0]; // Пример
            return { allowed: true };
        } else {
            return { allowed: false, error: `Error: Player ${player.id} has no valid cards to podkidnut.` };
        }
    }
    // 4. Невалидное действие
    else {
        return { allowed: false, error: `Error: Action ${action} is not allowed for player ${playerIndex} in the current state.` };
    }
  }


  /**
   * Выполнение хода игрока
   */
  public makeMove(playerId: string, action: PlayerAction, cardIndex?: number): boolean {
    // Проверка, что игра в процессе
    if (this.state.gameStatus !== GameStatus.IN_PROGRESS) {
        console.error("Error: Game is not in progress.");
        return false;
    }

    const playerIndex = this.state.players.findIndex(p => p.id === playerId);
    if (playerIndex === -1) {
        console.error(`Error: Player with ID ${playerId} not found.`);
        return false;
    }

    // Проверка, разрешено ли действие
    const { allowed, error } = this._isActionAllowed(playerIndex, action);
    if (!allowed) {
        console.error(error || `Error: Action ${action} is not allowed for player ${playerId} right now.`);
        return false;
    }

    // Логика хода в зависимости от действия
    let success = false;
    switch (action) {
      case PlayerAction.ATTACK:
        success = this.handleAttack(playerIndex, cardIndex);
        break;
      case PlayerAction.DEFEND:
        success = this.handleDefend(playerIndex, cardIndex);
        break;
      case PlayerAction.TAKE:
        success = this.handleTake(playerIndex);
        break;
      case PlayerAction.PASS:
        success = this.handlePass(playerIndex);
        break;
      default:
        console.error(`Error: Unknown PlayerAction: ${action}`);
        return false;
    }

    // Если ход был успешным, проверяем конец игры и обновляем активного игрока (если нужно)
    if (success) {
        // Отправляем событие о ходе игрока
        this.emitEvent({
            type: GameEvent.PLAYER_MOVED,
            gameState: this.getState(),
            playerId,
            action,
            cardIndex,
        });

        if (!this.checkGameEnd()) {
            // Логика перехода хода теперь полностью внутри handleTake и handlePass
            // (через _updateRolesAfterTake и _determineNextRoles соответственно)
            // Поэтому вызов _moveToNextTurn здесь больше не нужен.
            // if (action === PlayerAction.TAKE || action === PlayerAction.PASS) {
            //     // this._moveToNextTurn(); // Удалено
            // }
            // this.updateActivePlayer(); // Обновление происходит внутри handle-методов или методов перехода хода
        }
    }

    return success;
  }

  /**
   * Проверка валидности атакующей карты
   */
  private isValidAttack(
    card: Card,
    tableCards: Card[][],
    defenderHandSize: number,
  ): boolean {
    // Если стол пуст (первый ход атаки), любая карта валидна.
    if (tableCards.length === 0) {
      return true;
    }
    // Если стол не пуст (подкидывание), ранг карты должен совпадать
    // с рангом любой карты, уже лежащей на столе (атакующей или защитной).
    const ranksOnTable = new Set(tableCards.flat().map((c) => c.rank));
    if (!ranksOnTable.has(card.rank)) {
      return false;
    }
    // Нельзя подкидывать больше карт, чем у защитника на руках (минус уже отбитые в этом раунде)
    /* const cardsToDefendCount = tableCards.filter(
      (pair) => pair.length === 1,
    ).length; */ // УДАЛЕНО, ТАК КАК НЕ ИСПОЛЬЮТСЯ
    // const maxPodkidnoy = defenderHandSize - cardsToDefendCount; // УДАЛЕНО, ТАК КАК НЕ ИСПОЛЬЮТСЯ
    // const currentPodkidnoy = tableCards.length - cardsToDefendCount; // Сколько уже подкинули сверх первой атаки - УДАЛЕНО, ТАК КАК НЕ ИСПОЛЬЮТСЯ

    // Проверяем общее количество карт на столе против лимита
    const maxCardsOnTable = this.rules.maxTableCards ?? 6; // Используем правило или 6 по умолчанию
    if (tableCards.length >= Math.min(maxCardsOnTable, defenderHandSize)) {
      return false;
    }

    return true;
  }

  /**
   * Обработка атаки
   */
  private handleAttack(playerIndex: number, cardIndex?: number): boolean {
    // Проверка наличия cardIndex
    if (typeof cardIndex !== "number" || cardIndex < 0) {
      console.error(`Error: Valid cardIndex is required for ATTACK action.`);
      return false;
    }

    // Атаковать (или подкидывать) может любой игрок, кроме защищающегося
    // Эта проверка уже сделана в _isActionAllowed
    // if (playerIndex === this.state.defenderIndex) {
    //   console.error("Error: The defender cannot attack.");
    //   return false;
    // }

    const player = this.state.players[playerIndex];
    if (cardIndex >= player.hand.length) {
      console.error(
        `Error: Invalid card index ${cardIndex} for player ${player.id}.`,
      );
      return false;
    }
    const card = player.hand[cardIndex];
    const defender = this.state.players[this.state.defenderIndex];

    // Проверка валидности карты для атаки/подкидывания
    if (
      !this.isValidAttack(card, this.state.tableCards, defender.hand.length)
    ) {
      console.error(
        `Error: Card ${card.rank} ${card.suit} is not a valid attack/podkidnoy card.`,
      );
      return false;
    }

    // Перемещаем карту из руки на стол
    player.hand.splice(cardIndex, 1);
    this.state.tableCards.push([card]);

    // Сбрасываем флаг взятия карт, так как началась новая атака
    this.state.defenderTookCards = false;

    // Передаем ход защитнику
    this.state.currentPlayerIndex = this.state.defenderIndex;
    this.updateActivePlayer();

    console.log(
      `Player ${player.id} attacks with ${card.rank} ${card.suit}. Turn passes to defender ${defender.id}.`,
    );
    return true;
  }

  /**
   * Проверка валидности защищающейся карты
   */
  public isValidDefense(
    attackCard: Card,
    defendCard: Card,
    trumpSuit: CardSuit,
  ): boolean {
    // Карта той же масти, но старше
    if (
      attackCard.suit === defendCard.suit &&
      this.getRankValue(defendCard.rank) > this.getRankValue(attackCard.rank)
    ) {
      return true;
    }
    // Карта - козырь, а атакующая карта - нет
    if (defendCard.suit === trumpSuit && attackCard.suit !== trumpSuit) {
      return true;
    }
    // Обе карты козырные, защищающаяся карта старше
    if (
      attackCard.suit === trumpSuit &&
      defendCard.suit === trumpSuit &&
      this.getRankValue(defendCard.rank) > this.getRankValue(attackCard.rank)
    ) {
      return true;
    }
    return false;
  }

  /**
   * Обработка защиты
   */
  private handleDefend(playerIndex: number, cardIndex?: number): boolean {
    // Проверка наличия cardIndex
    if (typeof cardIndex !== "number" || cardIndex < 0) {
      console.error(`Error: Valid cardIndex is required for DEFEND action.`);
      return false;
    }

    // Защищаться может только защищающийся игрок
    // Эта проверка уже сделана в _isActionAllowed
    // if (playerIndex !== this.state.defenderIndex) {
    //   console.error("Error: Only the defender can defend.");
    //   return false;
    // }

    const player = this.state.players[playerIndex];
    if (cardIndex >= player.hand.length) {
      console.error(
        `Error: Invalid card index ${cardIndex} for player ${player.id}.`,
      );
      return false;
    }
    const defendingCard = player.hand[cardIndex];

    // Находим последнюю атакующую карту, которую нужно отбить
    const lastPair = this.state.tableCards.at(-1);
    if (!lastPair || lastPair.length !== 1) {
      console.error("Error: No attacking card to defend against.");
      return false;
    }
    const attackingCard = lastPair[0];

    // Проверяем, может ли выбранная карта отбить атакующую
    if (
      !this.isValidDefense(attackingCard, defendingCard, this.state.trumpSuit)
    ) {
      console.error(
        `Error: Card ${defendingCard.rank} ${defendingCard.suit} cannot defend against ${attackingCard.rank} ${attackingCard.suit}.`,
      );
      return false;
    }

    // Перемещаем карту из руки на стол к атакующей карте
    player.hand.splice(cardIndex, 1);
    lastPair.push(defendingCard);

    // Проверяем, все ли карты на столе отбиты
    const allDefended = this.state.tableCards.every(
      (pair) => pair.length === 2,
    );
    const defenderHasCards = player.hand.length > 0;
    const canPodkidnut =
      this.state.tableCards.length < (this.rules.maxTableCards ?? 6);

    // Если все отбито и у защитника нет карт ИЛИ нельзя больше подкидывать, ход атакующего (сказать пас/бито)
    if (allDefended && (!defenderHasCards || !canPodkidnut)) {
      this.state.currentPlayerIndex = this.state.attackerIndex;
      console.log(
        `Defender ${player.id} defended with ${defendingCard.rank} ${defendingCard.suit}. All cards defended. Turn passes to attacker ${this.state.players[this.state.attackerIndex].id} to pass.`,
      );
    }
    // Если все отбито, но можно подкидывать и у защитника есть карты, ход остается у защитника (ожидание подкидывания или паса)
    else if (allDefended) {
      this.state.currentPlayerIndex = this.state.defenderIndex; // Остается у защитника, но он ждет
      console.log(
        `Defender ${player.id} defended with ${defendingCard.rank} ${defendingCard.suit}. Waiting for podkidnoy or pass from attacker(s).`,
      );
    }
    // Если не все отбито (это не должно произойти здесь, т.к. мы только что добавили карту)
    // Оставляем ход у защитника для следующей защиты
    else {
      this.state.currentPlayerIndex = this.state.defenderIndex;
      console.log(
        `Defender ${player.id} defended with ${defendingCard.rank} ${defendingCard.suit}. Turn remains with defender.`,
      );
    }

    this.updateActivePlayer();
    return true;
  }

  /**
   * Вспомогательный метод: Защитник берет карты со стола
   */
  private _defenderTakesCards(playerIndex: number): void {
    const player = this.state.players[playerIndex];
    const cardsToTake = this.state.tableCards.flat();
    player.hand.push(...cardsToTake);
    this.state.tableCards = [];
    this.state.defenderTookCards = true; // Устанавливаем флаг, что защитник взял карты
    console.log(
      `Player ${player.id} takes ${cardsToTake.length} cards from the table.`,
    );
  }

  /**
   * Вспомогательный метод: Обновляет роли после того, как защитник взял карты.
   * Ход переходит к следующему игроку после взявшего.
   * @returns {boolean} Возвращает true, если игра окончена, иначе false.
   */
  private _updateRolesAfterTake(): boolean {
    const numPlayers = this.state.players.length;
    const playerWhoTookIndex = this.state.defenderIndex; // Индекс игрока, который только что взял карты

    // Определяем следующего атакующего, пропуская выбывших
    let nextAttackerIndex = (playerWhoTookIndex + 1) % numPlayers;
    let loopCheck = 0;
    while (
      this.state.players[nextAttackerIndex].hand.length === 0 &&
      this.state.deck.length === 0 &&
      loopCheck < numPlayers
    ) {
      if (nextAttackerIndex === playerWhoTookIndex) {
        // Обошли круг и вернулись к тому, кто взял - он единственный оставшийся
        console.log("Game ended: Only the player who took cards remains.");
        return this.checkGameEnd();
      }
      nextAttackerIndex = (nextAttackerIndex + 1) % numPlayers;
      loopCheck++;
    }

    // Если после цикла nextAttackerIndex совпадает с playerWhoTookIndex, игра окончена
    if (
      nextAttackerIndex === playerWhoTookIndex &&
      loopCheck >= numPlayers - 1
    ) {
      console.log("Game ended: All other players are out after take.");
      return this.checkGameEnd();
    }

    // Определяем следующего защитника, пропуская выбывших
    let nextDefenderIndex = (nextAttackerIndex + 1) % numPlayers;
    loopCheck = 0;
    while (
      this.state.players[nextDefenderIndex].hand.length === 0 &&
      this.state.deck.length === 0 &&
      loopCheck < numPlayers
    ) {
      if (nextDefenderIndex === nextAttackerIndex) {
        // Обошли круг и вернулись к атакующему - он единственный оставшийся
        console.log("Game ended: Only the next attacker remains.");
        return this.checkGameEnd();
      }
      nextDefenderIndex = (nextDefenderIndex + 1) % numPlayers;
      loopCheck++;
    }

    // Если после цикла nextDefenderIndex совпадает с nextAttackerIndex, игра окончена
    if (
      nextDefenderIndex === nextAttackerIndex &&
      loopCheck >= numPlayers - 1
    ) {
      console.log(
        "Game ended: Only attacker and defender remain, but defender cannot defend.",
      );
      return this.checkGameEnd();
    }

    this.state.attackerIndex = nextAttackerIndex;
    this.state.defenderIndex = nextDefenderIndex;
    this.state.currentPlayerIndex = this.state.attackerIndex; // Ход переходит к новому атакующему

    console.log(
      `Roles updated after take: Attacker=${this.state.players[this.state.attackerIndex].id}, Defender=${this.state.players[this.state.defenderIndex].id}`,
    );
    this.updateActivePlayer();
    return false; // Игра не закончена этим действием
  }

  /**
   * Обработка взятия карт (защитник берет)
   */
  private handleTake(playerIndex: number): boolean {
    // Проверки перенесены в _isActionAllowed
    // if (playerIndex !== this.state.defenderIndex) {
    //   console.error("Error: Only the defender can take cards.");
    //   return false;
    // }

    // Проверяем, есть ли карты на столе для взятия
    if (this.state.tableCards.length === 0) {
      console.error("Error: No cards on the table to take.");
      return false;
    }

    // Защитник берет карты
    this._defenderTakesCards(playerIndex);

    // Пополняем руки (начиная с атакующего, затем защитник, потом остальные)
    // Порядок: атакующий -> ... -> защитник
    this.replenishHands();

    // Проверяем окончание игры после пополнения рук
    if (this.checkGameEnd()) {
      return true;
    }

    // Обновляем роли атакующего и защитника
    const gameEnded = this._updateRolesAfterTake();
    if (gameEnded) {
      return true;
    }

    // Сбрасываем флаг ПОСЛЕ обновления ролей и перехода хода
    this.state.defenderTookCards = false;

    return true;
  }

  /**
   * Вспомогательный метод: Перемещает карты со стола в отбой
   */
  private _clearTableToDiscardPile(): void {
    this.state.discardPile.push(...this.state.tableCards.flat());
    this.state.tableCards = [];
  }

  /**
   * Вспомогательный метод: Определяет следующего атакующего и защитника
   * @returns {boolean} Возвращает true, если удалось определить роли (игра продолжается), иначе false (игра окончена).
   */
  private _determineNextRoles(): boolean {
    const numPlayers = this.state.players.length;
    const previousDefender = this.state.defenderIndex;

    // Новый атакующий - это предыдущий защитник
    this.state.attackerIndex = previousDefender;

    // Определяем нового защитника, пропуская выбывших игроков
    let nextDefenderIndex = (this.state.attackerIndex + 1) % numPlayers;
    let loopCheck = 0; // Предотвращение бесконечного цикла, если что-то пойдет не так
    while (
      this.state.players[nextDefenderIndex].hand.length === 0 &&
      this.state.deck.length === 0 &&
      loopCheck < numPlayers // Проверяем не больше, чем количество игроков
    ) {
      if (nextDefenderIndex === this.state.attackerIndex) {
        // Если обошли круг и вернулись к атакующему, значит, все остальные выбыли
        console.log(
          "Game potentially ended: Only attacker remains with cards or deck is empty.",
        );
        return false; // Сигнализируем, что роли определить не удалось (игра окончена)
      }
      nextDefenderIndex = (nextDefenderIndex + 1) % numPlayers;
      loopCheck++;
    }

    // Если после цикла nextDefenderIndex совпадает с attackerIndex, игра окончена
    if (
      nextDefenderIndex === this.state.attackerIndex &&
      loopCheck >= numPlayers - 1
    ) {
      console.log("Game ended: All other players are out.");
      return false;
    }

    this.state.defenderIndex = nextDefenderIndex;
    this.state.currentPlayerIndex = this.state.attackerIndex; // Ход переходит к новому атакующему

    return true; // Роли успешно определены
  }

  /**
   * Обработка паса (бито) - атакующий завершает раунд после успешной защиты
   */
  private handlePass(playerIndex: number): boolean {
    // Проверяем, что это атакующий игрок
    if (playerIndex !== this.state.attackerIndex) {
      console.error("Error: Only the attacker can pass (finish the round).");
      return false;
    }

    // Проверяем, есть ли карты на столе и все ли они отбиты
    if (this.state.tableCards.length === 0) {
      console.error("Error: Cannot pass, no cards on the table.");
      return false;
    }
    const allDefended = this.state.tableCards.every(
      (pair) => pair.length === 2,
    );
    if (!allDefended) {
      console.error("Error: Cannot pass, not all cards are defended.");
      return false;
    }

    // Перемещаем карты со стола в отбой
    this._clearTableToDiscardPile();

    // Пополняем руки игроков
    this.replenishHands();

    // Сбрасываем флаг перед проверкой конца игры и определением ролей
    this.state.defenderTookCards = false;

    // Проверяем окончание игры после пополнения рук
    if (this.checkGameEnd()) {
      return true; // Игра завершена
    }

    // Определяем следующие роли и передаем ход
    if (!this._determineNextRoles()) {
      // Если определить роли не удалось (например, остался 1 игрок), проверяем конец игры еще раз
      return this.checkGameEnd();
    }

    // Обновляем статус активного игрока
    this.updateActivePlayer();

    console.log(
      `Round finished (Pass). New attacker: ${this.state.players[this.state.attackerIndex].id}, New defender: ${this.state.players[this.state.defenderIndex].id}`,
    );
    return true;
  }

  /**
   * Обработка завершения хода (атакующий говорит "бито" или "пас")
   */
  private handleDone(playerIndex: number): boolean {
    // Проверяем, что это действительно атакующий игрок завершает ход
    if (playerIndex !== this.state.attackerIndex) {
      console.error(
        "Error: Only the attacker can finish the turn with 'Done'.",
      );
      return false;
    }

    // Проверяем, есть ли карты на столе (был ли хотя бы один ход атаки)
    if (this.state.tableCards.length > 0) {
      // Перемещаем карты со стола в отбой
      this._clearTableToDiscardPile();
    } else {
      // Если стол пуст, значит атакующий спасовал сразу
      console.log(
        `Player ${this.state.players[playerIndex].id} passed the turn immediately.`,
      );
      // Ничего не делаем с картами, просто передаем ход
    }

    // Пополняем руки игроков
    this.replenishHands();

    // Проверяем окончание игры после пополнения рук
    if (this.checkGameEnd()) {
      return true; // Игра завершена
    }

    // Определяем следующие роли и передаем ход
    // Логика такая же, как при 'Pass', т.к. защитник успешно отбился (или атаки не было)
    if (!this._determineNextRoles()) {
      // Если определить роли не удалось (например, остался 1 игрок), проверяем конец игры еще раз
      return this.checkGameEnd();
    }

    // Обновляем статус активного игрока
    this.updateActivePlayer();

    console.log(
      `Round finished (Done/Pass). New attacker: ${this.state.players[this.state.attackerIndex].id}, New defender: ${this.state.players[this.state.defenderIndex].id}`,
    );
    return true;
  }

  /**
   * Пополнение рук игроков из колоды до нужного количества
   */
  private replenishHands(): void {
    const cardsNeeded = this.rules.initialHandSize;
    const numPlayers = this.state.players.length;
    let currentPlayerToCheck = this.state.attackerIndex; // Начинаем с атакующего

    for (let i = 0; i < numPlayers; i++) {
      const player = this.state.players[currentPlayerToCheck];
      // Пополняем руку, только если игрок еще в игре (есть карты или есть колода)
      if (player.hand.length > 0 || this.state.deck.length > 0) {
        while (player.hand.length < cardsNeeded && this.state.deck.length > 0) {
          const card = this.state.deck.shift();
          if (card) {
            player.hand.push(card);
          }
        }
      }
      // Переходим к следующему игроку по кругу
      currentPlayerToCheck = (currentPlayerToCheck + 1) % numPlayers;
    }

    // Если колода закончилась и козырь был под ней, добавляем его в state
    if (
      this.state.deck.length === 0 &&
      this.state.trumpCard &&
      !this.state.players.some((p) => p.hand.includes(this.state.trumpCard!))
    ) {
      // Козырь забирает игрок, который последним пополнил руку (если ему нужно)
      // В нашей логике пополнения это будет игрок перед атакующим, если круг полный
      // Но проще отдать его текущему атакующему, если у него меньше 6 карт.
      // Или просто оставить его видимым, но не в игре? Правила разнятся.
      // Пока оставим его видимым в state.trumpCard, но не в руках.
      // Убираем TODO, т.к. конкретная реализация зависит от выбранных правил.
      // this.state.trumpCard = undefined;
    }
  }

  /**
   * Проверка окончания игры
   */
  private checkGameEnd(): boolean {
    // Проверка условий окончания игры
    const playersWithCards = this.state.players.filter(
      (p) => p.hand.length > 0,
    );
    const playersWithoutCards = this.state.players.filter(
      (p) => p.hand.length === 0,
    );

    // Игра заканчивается, если колода пуста и не более одного игрока с картами
    if (this.state.deck.length === 0 && playersWithCards.length <= 1) {
      this.state.gameStatus = GameStatus.FINISHED;

      let message = "";
      if (playersWithCards.length === 1) {
        // Проигравший - тот, у кого остались карты
        this.state.loser = playersWithCards[0];
        console.log(`Game finished. Loser: ${this.state.loser.id}`);

        // Победитель - первый игрок, который избавился от всех карт
        // В классическом дураке первый вышедший считается победителем
        this.state.winner = playersWithoutCards.length > 0 ? playersWithoutCards[0] : undefined;

        if (this.state.winner) {
          console.log(`Winner: ${this.state.winner.id}`);
          message = `Game finished. Winner: ${this.state.winner.id}, Loser: ${this.state.loser.id}`;
        } else {
          message = `Game finished. Loser: ${this.state.loser.id}`;
        }
      } else {
        // Ничья (все сбросили карты одновременно) - очень редкий случай
        console.log("Game finished. Draw!");
        this.state.winner = undefined;
        this.state.loser = undefined;
        message = "Game finished. Draw!";
      }

      // Отправляем событие окончания игры
      this.emitEvent({
        type: GameEvent.GAME_ENDED,
        gameState: this.getState(),
        message,
      });
      // Обнуляем активного игрока, т.к. игра завершена
      this.state.currentPlayerIndex = -1;
      this.updateActivePlayer();
      return true;
    }
    return false;
  }

  /**
   * Обновление состояния игры после хода (устарело, логика встроена)
   */
  /*
  private updateGameState(): void {
    // Обновление состояния игры
    // TODO: Добавить полную реализацию
  }
  */
}
