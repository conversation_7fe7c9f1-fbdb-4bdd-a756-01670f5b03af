/**
 * Система ботов для игры "Дурак"
 */

import {
  Card,
  CardRank,
  CardSuit,
  Player,
  GameState,
  PlayerAction,
} from "../types";

/**
 * Уровни сложности бота
 */
export enum BotDifficulty {
  EASY = "easy",
  MEDIUM = "medium",
  HARD = "hard",
}

/**
 * Интерфейс для принятия решений ботом
 */
export interface BotDecision {
  action: PlayerAction;
  cardIndex?: number;
  reasoning?: string; // Для отладки
}

/**
 * Базовый класс бота для игры "Дурак"
 */
export class DurakBot {
  public readonly id: string;
  public readonly difficulty: BotDifficulty;
  private readonly name: string;

  constructor(id: string, difficulty: BotDifficulty = BotDifficulty.MEDIUM) {
    this.id = id;
    this.difficulty = difficulty;
    this.name = `Bot_${difficulty}_${id}`;
  }

  /**
   * Создать игрока-бота
   */
  public createPlayer(): Player {
    return {
      id: this.id,
      name: this.name,
      hand: [],
      isActive: false,
    };
  }

  /**
   * Принять решение о следующем ходе
   */
  public makeDecision(gameState: GameState, playerId: string): BotDecision {
    const player = gameState.players.find(p => p.id === playerId);
    if (!player) {
      throw new Error(`Player ${playerId} not found`);
    }

    const isAttacker = gameState.attackerIndex === gameState.players.indexOf(player);
    const isDefender = gameState.defenderIndex === gameState.players.indexOf(player);

    // Определяем возможные действия
    if (isAttacker && gameState.tableCards.length === 0) {
      // Атакующий должен атаковать
      return this.decideAttack(gameState, player);
    } else if (isDefender && this.hasUndefendedCards(gameState)) {
      // Защитник должен защищаться или брать карты
      return this.decideDefendOrTake(gameState, player);
    } else if (isAttacker && this.allCardsDefended(gameState)) {
      // Атакующий может подкинуть карты или сказать "бито"
      return this.decideThrowOrPass(gameState, player);
    } else if (!isDefender && this.allCardsDefended(gameState)) {
      // Другие игроки могут подкинуть карты
      return this.decideThrow(gameState, player);
    }

    // По умолчанию - пас
    return {
      action: PlayerAction.PASS,
      reasoning: "Default pass action",
    };
  }

  /**
   * Решение об атаке
   */
  private decideAttack(gameState: GameState, player: Player): BotDecision {
    const validCards = this.getValidAttackCards(gameState, player);
    
    if (validCards.length === 0) {
      return {
        action: PlayerAction.PASS,
        reasoning: "No valid attack cards",
      };
    }

    // Выбираем карту в зависимости от сложности
    let cardIndex: number;
    
    switch (this.difficulty) {
      case BotDifficulty.EASY:
        // Легкий бот играет случайно
        cardIndex = validCards[Math.floor(Math.random() * validCards.length)];
        break;
        
      case BotDifficulty.MEDIUM:
        // Средний бот предпочитает младшие карты
        cardIndex = this.selectLowestCard(player, validCards, gameState.trumpSuit);
        break;
        
      case BotDifficulty.HARD:
        // Сложный бот использует стратегию
        cardIndex = this.selectStrategicAttackCard(player, validCards, gameState);
        break;
        
      default:
        cardIndex = validCards[0];
    }

    return {
      action: PlayerAction.ATTACK,
      cardIndex,
      reasoning: `Attack with card at index ${cardIndex}`,
    };
  }

  /**
   * Решение о защите или взятии карт
   */
  private decideDefendOrTake(gameState: GameState, player: Player): BotDecision {
    const lastPair = gameState.tableCards.at(-1);
    if (!lastPair || lastPair.length !== 1) {
      return {
        action: PlayerAction.TAKE,
        reasoning: "No card to defend against",
      };
    }

    const attackingCard = lastPair[0];
    const validDefenseCards = this.getValidDefenseCards(player, attackingCard, gameState.trumpSuit);

    if (validDefenseCards.length === 0) {
      return {
        action: PlayerAction.TAKE,
        reasoning: "No valid defense cards",
      };
    }

    // Решаем, защищаться или брать карты
    const shouldDefend = this.shouldDefend(gameState, player, validDefenseCards);
    
    if (shouldDefend) {
      const cardIndex = this.selectDefenseCard(validDefenseCards, attackingCard, gameState.trumpSuit);
      return {
        action: PlayerAction.DEFEND,
        cardIndex,
        reasoning: `Defend with card at index ${cardIndex}`,
      };
    } else {
      return {
        action: PlayerAction.TAKE,
        reasoning: "Decided to take cards instead of defending",
      };
    }
  }

  /**
   * Решение о подкидывании или пасе
   */
  private decideThrowOrPass(gameState: GameState, player: Player): BotDecision {
    const throwCards = this.getValidThrowCards(gameState, player);
    
    if (throwCards.length === 0 || !this.shouldThrow(gameState, player)) {
      return {
        action: PlayerAction.PASS,
        reasoning: "No valid throw cards or decided not to throw",
      };
    }

    const cardIndex = throwCards[0]; // Простая стратегия - первая подходящая карта
    return {
      action: PlayerAction.ATTACK,
      cardIndex,
      reasoning: `Throw card at index ${cardIndex}`,
    };
  }

  /**
   * Решение о подкидывании (для не-атакующих игроков)
   */
  private decideThrow(gameState: GameState, player: Player): BotDecision {
    // Пока что не-атакующие игроки не подкидывают (упрощение)
    return {
      action: PlayerAction.PASS,
      reasoning: "Non-attacker decided not to throw",
    };
  }

  // Вспомогательные методы

  private hasUndefendedCards(gameState: GameState): boolean {
    return gameState.tableCards.some(pair => pair.length === 1);
  }

  private allCardsDefended(gameState: GameState): boolean {
    return gameState.tableCards.length > 0 && gameState.tableCards.every(pair => pair.length === 2);
  }

  private getValidAttackCards(gameState: GameState, player: Player): number[] {
    if (gameState.tableCards.length === 0) {
      // Первая атака - любая карта
      return player.hand.map((_, index) => index);
    }

    // Подкидывание - карты с рангами, уже лежащими на столе
    const ranksOnTable = new Set(gameState.tableCards.flat().map(card => card.rank));
    return player.hand
      .map((card, index) => ({ card, index }))
      .filter(({ card }) => ranksOnTable.has(card.rank))
      .map(({ index }) => index);
  }

  private getValidDefenseCards(player: Player, attackingCard: Card, trumpSuit: CardSuit): number[] {
    return player.hand
      .map((card, index) => ({ card, index }))
      .filter(({ card }) => this.canDefend(attackingCard, card, trumpSuit))
      .map(({ index }) => index);
  }

  private canDefend(attackCard: Card, defendCard: Card, trumpSuit: CardSuit): boolean {
    const getRankValue = (rank: CardRank): number => {
      const values: Record<CardRank, number> = {
        [CardRank.SIX]: 6,
        [CardRank.SEVEN]: 7,
        [CardRank.EIGHT]: 8,
        [CardRank.NINE]: 9,
        [CardRank.TEN]: 10,
        [CardRank.JACK]: 11,
        [CardRank.QUEEN]: 12,
        [CardRank.KING]: 13,
        [CardRank.ACE]: 14,
      };
      return values[rank];
    };

    // Карта той же масти, но старше
    if (attackCard.suit === defendCard.suit && getRankValue(defendCard.rank) > getRankValue(attackCard.rank)) {
      return true;
    }
    
    // Козырь бьет не-козырь
    if (defendCard.suit === trumpSuit && attackCard.suit !== trumpSuit) {
      return true;
    }
    
    // Козырь бьет козырь, если старше
    if (attackCard.suit === trumpSuit && defendCard.suit === trumpSuit && 
        getRankValue(defendCard.rank) > getRankValue(attackCard.rank)) {
      return true;
    }
    
    return false;
  }

  private getValidThrowCards(gameState: GameState, player: Player): number[] {
    const ranksOnTable = new Set(gameState.tableCards.flat().map(card => card.rank));
    return player.hand
      .map((card, index) => ({ card, index }))
      .filter(({ card }) => ranksOnTable.has(card.rank))
      .map(({ index }) => index);
  }

  private shouldDefend(gameState: GameState, player: Player, validDefenseCards: number[]): boolean {
    switch (this.difficulty) {
      case BotDifficulty.EASY:
        return Math.random() > 0.3; // 70% шанс защищаться
        
      case BotDifficulty.MEDIUM:
        // Защищается, если у него мало карт или много карт на столе
        return player.hand.length <= 3 || gameState.tableCards.length >= 3;
        
      case BotDifficulty.HARD:
        // Более сложная логика
        return this.strategicDefendDecision(gameState, player, validDefenseCards);
        
      default:
        return true;
    }
  }

  private shouldThrow(gameState: GameState, player: Player): boolean {
    switch (this.difficulty) {
      case BotDifficulty.EASY:
        return Math.random() > 0.7; // 30% шанс подкинуть
        
      case BotDifficulty.MEDIUM:
        return player.hand.length > 5; // Подкидывает, если много карт
        
      case BotDifficulty.HARD:
        return this.strategicThrowDecision(gameState, player);
        
      default:
        return false;
    }
  }

  private selectLowestCard(player: Player, validIndices: number[], trumpSuit: CardSuit): number {
    const getRankValue = (rank: CardRank): number => {
      const values: Record<CardRank, number> = {
        [CardRank.SIX]: 6, [CardRank.SEVEN]: 7, [CardRank.EIGHT]: 8, [CardRank.NINE]: 9,
        [CardRank.TEN]: 10, [CardRank.JACK]: 11, [CardRank.QUEEN]: 12, [CardRank.KING]: 13, [CardRank.ACE]: 14,
      };
      return values[rank];
    };

    return validIndices.reduce((lowestIndex, currentIndex) => {
      const lowestCard = player.hand[lowestIndex];
      const currentCard = player.hand[currentIndex];
      
      // Предпочитаем не-козыри
      if (lowestCard.suit === trumpSuit && currentCard.suit !== trumpSuit) {
        return currentIndex;
      }
      if (lowestCard.suit !== trumpSuit && currentCard.suit === trumpSuit) {
        return lowestIndex;
      }
      
      // Если обе карты одного типа (козыри или не-козыри), выбираем младшую
      return getRankValue(currentCard.rank) < getRankValue(lowestCard.rank) ? currentIndex : lowestIndex;
    });
  }

  private selectStrategicAttackCard(player: Player, validIndices: number[], gameState: GameState): number {
    // Пока что используем простую стратегию - младшая карта
    return this.selectLowestCard(player, validIndices, gameState.trumpSuit);
  }

  private selectDefenseCard(validIndices: number[], attackingCard: Card, trumpSuit: CardSuit): number {
    // Выбираем первую подходящую карту (можно улучшить)
    return validIndices[0];
  }

  private strategicDefendDecision(gameState: GameState, player: Player, validDefenseCards: number[]): boolean {
    // Упрощенная стратегическая логика
    const cardsOnTable = gameState.tableCards.flat().length;
    const playerCardCount = player.hand.length;
    
    // Не защищается, если на столе много карт и у игрока мало карт
    if (cardsOnTable >= 6 && playerCardCount <= 4) {
      return false;
    }
    
    return true;
  }

  private strategicThrowDecision(gameState: GameState, player: Player): boolean {
    // Подкидывает, если у защитника много карт
    const defender = gameState.players[gameState.defenderIndex];
    return defender.hand.length > 6;
  }
}

/**
 * Фабрика для создания ботов
 */
export class BotFactory {
  private static botCounter = 0;

  /**
   * Создать бота с указанной сложностью
   */
  static createBot(difficulty: BotDifficulty = BotDifficulty.MEDIUM): DurakBot {
    const id = `bot_${++this.botCounter}`;
    return new DurakBot(id, difficulty);
  }

  /**
   * Создать несколько ботов
   */
  static createBots(count: number, difficulty: BotDifficulty = BotDifficulty.MEDIUM): DurakBot[] {
    return Array.from({ length: count }, () => this.createBot(difficulty));
  }
}
