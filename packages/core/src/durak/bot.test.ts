/**
 * Тесты для системы ботов
 */

import { DurakBot, BotFactory, BotDifficulty } from "./bot";
import { DurakGame } from "./index";
import {
  CardSuit,
  CardRank,
  Durak<PERSON>t,
  GameStatus,
  PlayerAction,
} from "../types";

describe("DurakBot", () => {
  let bot: DurakBot;

  beforeEach(() => {
    bot = new DurakBot("test_bot", BotDifficulty.MEDIUM);
  });

  describe("constructor", () => {
    it("should create a bot with correct properties", () => {
      expect(bot.id).toBe("test_bot");
      expect(bot.difficulty).toBe(BotDifficulty.MEDIUM);
    });
  });

  describe("createPlayer", () => {
    it("should create a player object", () => {
      const player = bot.createPlayer();
      
      expect(player.id).toBe("test_bot");
      expect(player.name).toContain("Bot_medium_test_bot");
      expect(player.hand).toEqual([]);
      expect(player.isActive).toBe(false);
    });
  });

  describe("makeDecision", () => {
    let game: DurakGame;
    let players: any[];

    beforeEach(() => {
      // Создаем игру с ботом
      const bot1 = BotFactory.createBot(BotDifficulty.EASY);
      const bot2 = BotFactory.createBot(BotDifficulty.MEDIUM);
      
      players = [bot1.createPlayer(), bot2.createPlayer()];
      
      const rules = {
        variant: DurakVariant.CLASSIC,
        numberOfPlayers: 2,
        initialHandSize: 6,
        attackLimit: 6,
      };

      game = new DurakGame(players, rules);
      game.startGame();
    });

    it("should make a valid attack decision", () => {
      const gameState = game.getState();
      const attackerId = gameState.players[gameState.attackerIndex].id;
      const bot = new DurakBot(attackerId, BotDifficulty.MEDIUM);

      const decision = bot.makeDecision(gameState, attackerId);

      expect(decision.action).toBe(PlayerAction.ATTACK);
      expect(typeof decision.cardIndex).toBe("number");
      expect(decision.cardIndex).toBeGreaterThanOrEqual(0);
      expect(decision.cardIndex).toBeLessThan(gameState.players[gameState.attackerIndex].hand.length);
    });

    it("should make a valid defense decision", () => {
      const gameState = game.getState();
      const attackerId = gameState.players[gameState.attackerIndex].id;
      const defenderId = gameState.players[gameState.defenderIndex].id;

      // Сначала атакующий делает ход
      const attackerBot = new DurakBot(attackerId, BotDifficulty.MEDIUM);
      const attackDecision = attackerBot.makeDecision(gameState, attackerId);
      
      // Выполняем атаку
      game.makeMove(attackerId, attackDecision.action, attackDecision.cardIndex);

      // Теперь защитник должен принять решение
      const updatedGameState = game.getState();
      const defenderBot = new DurakBot(defenderId, BotDifficulty.MEDIUM);
      const defenseDecision = defenderBot.makeDecision(updatedGameState, defenderId);

      expect([PlayerAction.DEFEND, PlayerAction.TAKE]).toContain(defenseDecision.action);
      
      if (defenseDecision.action === PlayerAction.DEFEND) {
        expect(typeof defenseDecision.cardIndex).toBe("number");
        expect(defenseDecision.cardIndex).toBeGreaterThanOrEqual(0);
        expect(defenseDecision.cardIndex).toBeLessThan(updatedGameState.players[updatedGameState.defenderIndex].hand.length);
      }
    });
  });
});

describe("BotFactory", () => {
  describe("createBot", () => {
    it("should create a bot with default difficulty", () => {
      const bot = BotFactory.createBot();
      expect(bot.difficulty).toBe(BotDifficulty.MEDIUM);
      expect(bot.id).toContain("bot_");
    });

    it("should create a bot with specified difficulty", () => {
      const bot = BotFactory.createBot(BotDifficulty.HARD);
      expect(bot.difficulty).toBe(BotDifficulty.HARD);
    });

    it("should create bots with unique IDs", () => {
      const bot1 = BotFactory.createBot();
      const bot2 = BotFactory.createBot();
      expect(bot1.id).not.toBe(bot2.id);
    });
  });

  describe("createBots", () => {
    it("should create multiple bots", () => {
      const bots = BotFactory.createBots(3, BotDifficulty.EASY);
      
      expect(bots).toHaveLength(3);
      expect(bots.every(bot => bot.difficulty === BotDifficulty.EASY)).toBe(true);
      
      // Проверяем, что все ID уникальны
      const ids = bots.map(bot => bot.id);
      const uniqueIds = new Set(ids);
      expect(uniqueIds.size).toBe(3);
    });
  });
});

describe("Bot Integration Tests", () => {
  it("should be able to play a complete game with bots", () => {
    // Создаем игру только с ботами
    const bot1 = BotFactory.createBot(BotDifficulty.EASY);
    const bot2 = BotFactory.createBot(BotDifficulty.MEDIUM);
    
    const players = [bot1.createPlayer(), bot2.createPlayer()];
    
    const rules = {
      variant: DurakVariant.CLASSIC,
      numberOfPlayers: 2,
      initialHandSize: 6,
      attackLimit: 6,
    };

    const game = new DurakGame(players, rules);
    game.startGame();

    // Симулируем игру с ботами
    let moveCount = 0;
    const maxMoves = 100; // Предотвращаем бесконечный цикл

    while (game.getState().gameStatus === GameStatus.IN_PROGRESS && moveCount < maxMoves) {
      const gameState = game.getState();
      const currentPlayerId = gameState.players[gameState.currentPlayerIndex].id;
      
      // Определяем, какой бот должен ходить
      const currentBot = currentPlayerId === bot1.id ? bot1 : bot2;
      
      // Бот принимает решение
      const decision = currentBot.makeDecision(gameState, currentPlayerId);
      
      // Выполняем ход
      const success = game.makeMove(currentPlayerId, decision.action, decision.cardIndex);
      
      if (!success) {
        console.warn(`Bot move failed: ${decision.action} by ${currentPlayerId}`);
        break;
      }
      
      moveCount++;
    }

    // Проверяем, что игра завершилась корректно
    expect(moveCount).toBeLessThan(maxMoves);
    
    if (game.getState().gameStatus === GameStatus.FINISHED) {
      const finalState = game.getState();
      expect([finalState.winner?.id, finalState.loser?.id]).toContain(bot1.id);
      expect([finalState.winner?.id, finalState.loser?.id]).toContain(bot2.id);
    }
  });

  it("should handle different bot difficulties", () => {
    const easyBot = BotFactory.createBot(BotDifficulty.EASY);
    const hardBot = BotFactory.createBot(BotDifficulty.HARD);
    
    const players = [easyBot.createPlayer(), hardBot.createPlayer()];
    
    const rules = {
      variant: DurakVariant.CLASSIC,
      numberOfPlayers: 2,
      initialHandSize: 6,
      attackLimit: 6,
    };

    const game = new DurakGame(players, rules);
    game.startGame();

    // Проверяем, что боты разной сложности могут принимать решения
    const gameState = game.getState();
    
    const easyDecision = easyBot.makeDecision(gameState, easyBot.id);
    const hardDecision = hardBot.makeDecision(gameState, hardBot.id);
    
    expect(easyDecision.action).toBeDefined();
    expect(hardDecision.action).toBeDefined();
  });
});
