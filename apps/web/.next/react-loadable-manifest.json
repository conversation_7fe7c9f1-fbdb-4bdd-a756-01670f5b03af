{"../../node_modules/@react-three/drei/core/VideoTexture.js -> hls.js": {"id": "../../node_modules/@react-three/drei/core/VideoTexture.js -> hls.js", "files": []}, "../../node_modules/@react-three/drei/web/FaceLandmarker.js -> @mediapipe/tasks-vision": {"id": "../../node_modules/@react-three/drei/web/FaceLandmarker.js -> @mediapipe/tasks-vision", "files": []}, "src/pages/index.tsx -> ../components/AIShowcase": {"id": "src/pages/index.tsx -> ../components/AIShowcase", "files": ["static/chunks/src_components_AIShowcase_tsx.js"]}, "src/pages/index.tsx -> ../components/Footer": {"id": "src/pages/index.tsx -> ../components/Footer", "files": ["static/chunks/src_components_Footer_tsx.js"]}, "src/pages/index.tsx -> ../components/GameDemo": {"id": "src/pages/index.tsx -> ../components/GameDemo", "files": ["static/chunks/src_components_GameDemo_tsx.js"]}, "src/pages/index.tsx -> ../components/Hero3D": {"id": "src/pages/index.tsx -> ../components/Hero3D", "files": ["static/chunks/src_components_Hero3D_tsx.js"]}, "src/pages/index.tsx -> ../components/LoadingScreen": {"id": "src/pages/index.tsx -> ../components/LoadingScreen", "files": ["static/chunks/src_components_LoadingScreen_tsx.js"]}, "src/pages/index.tsx -> ../components/MetaversePreview-simple": {"id": "src/pages/index.tsx -> ../components/MetaversePreview-simple", "files": ["static/chunks/src_components_MetaversePreview-simple_tsx.js"]}, "src/pages/index.tsx -> ../components/RevolutionaryFeatures": {"id": "src/pages/index.tsx -> ../components/RevolutionaryFeatures", "files": ["static/chunks/src_components_RevolutionaryFeatures_tsx.js"]}, "src/pages/index.tsx -> ../components/StreamingPlatform": {"id": "src/pages/index.tsx -> ../components/StreamingPlatform", "files": ["static/chunks/src_components_StreamingPlatform_tsx.js"]}, "src/pages/index.tsx -> ../components/Web3Dashboard": {"id": "src/pages/index.tsx -> ../components/Web3Dashboard", "files": ["static/chunks/src_components_Web3Dashboard_tsx.js"]}}