"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_Footer_tsx"],{

/***/ "./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst Footer = (param)=>{\n    let { onSubscribe } = param;\n    _s();\n    const [email, setEmail] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(\"\");\n    const handleSubscribe = (e)=>{\n        e.preventDefault();\n        if (email && onSubscribe) {\n            onSubscribe(email);\n            setEmail(\"\");\n        }\n    };\n    const socialLinks = [\n        {\n            name: \"Discord\",\n            icon: \"\\uD83D\\uDCAC\",\n            url: \"https://discord.gg/kozyrmasterr\",\n            color: \"#5865F2\"\n        },\n        {\n            name: \"Telegram\",\n            icon: \"\\uD83D\\uDCF1\",\n            url: \"https://t.me/kozyrmasterr\",\n            color: \"#0088cc\"\n        },\n        {\n            name: \"Twitter\",\n            icon: \"\\uD83D\\uDC26\",\n            url: \"https://twitter.com/kozyrmasterr\",\n            color: \"#1DA1F2\"\n        },\n        {\n            name: \"YouTube\",\n            icon: \"\\uD83D\\uDCFA\",\n            url: \"https://youtube.com/@kozyrmasterr\",\n            color: \"#FF0000\"\n        },\n        {\n            name: \"Twitch\",\n            icon: \"\\uD83C\\uDFAE\",\n            url: \"https://twitch.tv/kozyrmasterr\",\n            color: \"#9146FF\"\n        },\n        {\n            name: \"GitHub\",\n            icon: \"\\uD83D\\uDCBB\",\n            url: \"https://github.com/kozyrmasterr\",\n            color: \"#333\"\n        }\n    ];\n    const quickLinks = [\n        {\n            name: \"Игры\",\n            url: \"/games\"\n        },\n        {\n            name: \"Турниры\",\n            url: \"/tournaments\"\n        },\n        {\n            name: \"Обучение\",\n            url: \"/tutorials\"\n        },\n        {\n            name: \"Рейтинг\",\n            url: \"/leaderboard\"\n        },\n        {\n            name: \"Профиль\",\n            url: \"/profile\"\n        },\n        {\n            name: \"Поддержка\",\n            url: \"/support\"\n        }\n    ];\n    const legalLinks = [\n        {\n            name: \"Пользовательское соглашение\",\n            url: \"/terms\"\n        },\n        {\n            name: \"Политика конфиденциальности\",\n            url: \"/privacy\"\n        },\n        {\n            name: \"Правила игры\",\n            url: \"/rules\"\n        },\n        {\n            name: \"FAQ\",\n            url: \"/faq\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterContainer, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterSection, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BrandSection, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Logo, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogoIcon, {\n                                                children: \"\\uD83C\\uDFAE\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogoText, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogoTitle, {\n                                                        children: \"Козырь Мастер\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogoVersion, {\n                                                        children: \"4.0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BrandDescription, {\n                                        children: \"Революционная платформа карточных игр с квантовой случайностью, эмоциональным ИИ и 3D метавселенной. Будущее игр уже здесь!\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechBadges, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechBadge, {\n                                                children: \"⚛️ Квантовые технологии\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechBadge, {\n                                                children: \"\\uD83E\\uDDE0 Эмоциональный ИИ\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechBadge, {\n                                                children: \"\\uD83C\\uDF0D 3D Метавселенная\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechBadge, {\n                                                children: \"⛓️ Web3 & NFT\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterSection, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionTitle, {\n                                    children: \"Быстрые ссылки\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LinksList, {\n                                    children: quickLinks.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LinkItem, {\n                                            href: link.url,\n                                            whileHover: {\n                                                x: 5,\n                                                color: \"#4a90e2\"\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            children: link.name\n                                        }, link.name, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterSection, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionTitle, {\n                                    children: \"Сообщество\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocialLinks, {\n                                    children: socialLinks.map((social, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocialLink, {\n                                            href: social.url,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            color: social.color,\n                                            whileHover: {\n                                                scale: 1.1,\n                                                y: -2\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                scale: 1\n                                            },\n                                            transition: {\n                                                delay: index * 0.1\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocialIcon, {\n                                                    children: social.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocialName, {\n                                                    children: social.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, social.name, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CommunityStats, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatItem, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                    children: \"50K+\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                    children: \"Игроков\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatItem, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                    children: \"15K+\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                    children: \"Discord\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatItem, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                    children: \"25K+\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                    children: \"Подписчиков\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterSection, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.3\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionTitle, {\n                                    children: \"Новости и обновления\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewsletterDescription, {\n                                    children: \"Будьте в курсе последних новостей, турниров и обновлений платформы\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewsletterForm, {\n                                    onSubmit: handleSubscribe,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EmailInput, {\n                                            type: \"email\",\n                                            placeholder: \"Ваш email адрес\",\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SubscribeButton, {\n                                            type: \"submit\",\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            children: \"\\uD83D\\uDE80 Подписаться\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewsletterBenefits, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BenefitItem, {\n                                            children: \"✨ Эксклюзивные турниры\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BenefitItem, {\n                                            children: \"\\uD83C\\uDF81 Бонусы и промокоды\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BenefitItem, {\n                                            children: \"\\uD83D\\uDCF0 Первыми узнавайте новости\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterBottom, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BottomContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LegalLinks, {\n                            children: legalLinks.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LegalLink, {\n                                    href: link.url,\n                                    whileHover: {\n                                        color: \"#4a90e2\"\n                                    },\n                                    children: link.name\n                                }, link.name, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Copyright, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CopyrightText, {\n                                    children: \"\\xa9 2024 Козырь Мастер 4.0. Все права защищены.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechInfo, {\n                                    children: \"Работает на квантовых технологиях и эмоциональном ИИ\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoweredBy, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoweredText, {\n                                    children: \"Создано с ❤️ командой Козырь Мастер\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechStack, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechItem, {\n                                            children: \"React\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechItem, {\n                                            children: \"Three.js\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechItem, {\n                                            children: \"Web3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechItem, {\n                                            children: \"AI/ML\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Footer.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Footer, \"qu4bovk5U4+JuhY7vxbmswqixrc=\");\n_c = Footer;\n// Стилизованные компоненты\nconst FooterContainer = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].footer.withConfig({\n    displayName: \"Footer__FooterContainer\",\n    componentId: \"sc-d8be31fd-0\"\n})([\n    \"background:linear-gradient(135deg,#0f0f23 0%,#1a1a2e 50%,#16213e 100%);color:white;position:relative;overflow:hidden;&::before{content:'';position:absolute;top:0;left:0;right:0;height:1px;background:linear-gradient(90deg,transparent,#4a90e2,transparent);}\"\n]);\n_c1 = FooterContainer;\nconst FooterContent = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__FooterContent\",\n    componentId: \"sc-d8be31fd-1\"\n})([\n    \"max-width:1400px;margin:0 auto;padding:4rem 2rem 2rem;display:grid;grid-template-columns:2fr 1fr 1fr 1.5fr;gap:3rem;@media (max-width:1024px){grid-template-columns:repeat(2,1fr);gap:2rem;}@media (max-width:768px){grid-template-columns:1fr;gap:2rem;padding:2rem 1rem 1rem;}\"\n]);\n_c2 = FooterContent;\nconst FooterSection = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__FooterSection\",\n    componentId: \"sc-d8be31fd-2\"\n})([\n    \"\"\n]);\n_c3 = FooterSection;\nconst BrandSection = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__BrandSection\",\n    componentId: \"sc-d8be31fd-3\"\n})([\n    \"\"\n]);\n_c4 = BrandSection;\nconst Logo = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__Logo\",\n    componentId: \"sc-d8be31fd-4\"\n})([\n    \"display:flex;align-items:center;gap:1rem;margin-bottom:1.5rem;\"\n]);\n_c5 = Logo;\nconst LogoIcon = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__LogoIcon\",\n    componentId: \"sc-d8be31fd-5\"\n})([\n    \"font-size:3rem;filter:drop-shadow(0 0 20px rgba(74,144,226,0.5));\"\n]);\n_c6 = LogoIcon;\nconst LogoText = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__LogoText\",\n    componentId: \"sc-d8be31fd-6\"\n})([\n    \"\"\n]);\n_c7 = LogoText;\nconst LogoTitle = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__LogoTitle\",\n    componentId: \"sc-d8be31fd-7\"\n})([\n    \"font-size:1.8rem;font-weight:900;background:linear-gradient(45deg,#4a90e2,#7b68ee);-webkit-background-clip:text;-webkit-text-fill-color:transparent;line-height:1;\"\n]);\n_c8 = LogoTitle;\nconst LogoVersion = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__LogoVersion\",\n    componentId: \"sc-d8be31fd-8\"\n})([\n    \"font-size:1rem;color:#4a90e2;font-weight:700;\"\n]);\n_c9 = LogoVersion;\nconst BrandDescription = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].p.withConfig({\n    displayName: \"Footer__BrandDescription\",\n    componentId: \"sc-d8be31fd-9\"\n})([\n    \"color:rgba(255,255,255,0.8);line-height:1.6;margin-bottom:1.5rem;font-size:0.95rem;\"\n]);\n_c10 = BrandDescription;\nconst TechBadges = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__TechBadges\",\n    componentId: \"sc-d8be31fd-10\"\n})([\n    \"display:flex;flex-wrap:wrap;gap:0.5rem;\"\n]);\n_c11 = TechBadges;\nconst TechBadge = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].span.withConfig({\n    displayName: \"Footer__TechBadge\",\n    componentId: \"sc-d8be31fd-11\"\n})([\n    \"background:rgba(74,144,226,0.2);color:#4a90e2;padding:0.3rem 0.8rem;border-radius:15px;font-size:0.8rem;font-weight:600;border:1px solid rgba(74,144,226,0.3);\"\n]);\n_c12 = TechBadge;\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].h4.withConfig({\n    displayName: \"Footer__SectionTitle\",\n    componentId: \"sc-d8be31fd-12\"\n})([\n    \"font-size:1.3rem;font-weight:700;margin-bottom:1.5rem;color:white;\"\n]);\n_c13 = SectionTitle;\nconst LinksList = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__LinksList\",\n    componentId: \"sc-d8be31fd-13\"\n})([\n    \"display:flex;flex-direction:column;gap:0.75rem;\"\n]);\n_c14 = LinksList;\nconst LinkItem = (0,styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a).withConfig({\n    displayName: \"Footer__LinkItem\",\n    componentId: \"sc-d8be31fd-14\"\n})([\n    \"color:rgba(255,255,255,0.7);text-decoration:none;font-size:0.95rem;transition:all 0.3s ease;&:hover{color:#4a90e2;}\"\n]);\n_c15 = LinkItem;\nconst SocialLinks = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__SocialLinks\",\n    componentId: \"sc-d8be31fd-15\"\n})([\n    \"display:grid;grid-template-columns:repeat(2,1fr);gap:0.75rem;margin-bottom:1.5rem;\"\n]);\n_c16 = SocialLinks;\nconst SocialLink = (0,styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a).withConfig({\n    displayName: \"Footer__SocialLink\",\n    componentId: \"sc-d8be31fd-16\"\n})([\n    \"display:flex;align-items:center;gap:0.5rem;padding:0.75rem;background:rgba(255,255,255,0.05);border-radius:10px;text-decoration:none;color:white;border:1px solid rgba(255,255,255,0.1);transition:all 0.3s ease;&:hover{background:\",\n    \";border-color:\",\n    \";}\"\n], (props)=>\"\".concat(props.color, \"20\"), (props)=>props.color);\n_c17 = SocialLink;\nconst SocialIcon = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].span.withConfig({\n    displayName: \"Footer__SocialIcon\",\n    componentId: \"sc-d8be31fd-17\"\n})([\n    \"font-size:1.2rem;\"\n]);\n_c18 = SocialIcon;\nconst SocialName = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].span.withConfig({\n    displayName: \"Footer__SocialName\",\n    componentId: \"sc-d8be31fd-18\"\n})([\n    \"font-size:0.85rem;font-weight:600;\"\n]);\n_c19 = SocialName;\nconst CommunityStats = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__CommunityStats\",\n    componentId: \"sc-d8be31fd-19\"\n})([\n    \"display:grid;grid-template-columns:repeat(3,1fr);gap:1rem;\"\n]);\n_c20 = CommunityStats;\nconst StatItem = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__StatItem\",\n    componentId: \"sc-d8be31fd-20\"\n})([\n    \"text-align:center;background:rgba(255,255,255,0.05);border-radius:10px;padding:1rem 0.5rem;\"\n]);\n_c21 = StatItem;\nconst StatValue = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__StatValue\",\n    componentId: \"sc-d8be31fd-21\"\n})([\n    \"font-size:1.2rem;font-weight:700;color:#4a90e2;margin-bottom:0.25rem;\"\n]);\n_c22 = StatValue;\nconst StatLabel = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__StatLabel\",\n    componentId: \"sc-d8be31fd-22\"\n})([\n    \"font-size:0.8rem;color:rgba(255,255,255,0.7);\"\n]);\n_c23 = StatLabel;\nconst NewsletterDescription = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].p.withConfig({\n    displayName: \"Footer__NewsletterDescription\",\n    componentId: \"sc-d8be31fd-23\"\n})([\n    \"color:rgba(255,255,255,0.8);margin-bottom:1.5rem;font-size:0.9rem;line-height:1.5;\"\n]);\n_c24 = NewsletterDescription;\nconst NewsletterForm = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].form.withConfig({\n    displayName: \"Footer__NewsletterForm\",\n    componentId: \"sc-d8be31fd-24\"\n})([\n    \"display:flex;gap:0.5rem;margin-bottom:1rem;\"\n]);\n_c25 = NewsletterForm;\nconst EmailInput = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].input.withConfig({\n    displayName: \"Footer__EmailInput\",\n    componentId: \"sc-d8be31fd-25\"\n})([\n    \"flex:1;background:rgba(255,255,255,0.1);border:1px solid rgba(255,255,255,0.2);border-radius:8px;padding:0.75rem;color:white;font-size:0.9rem;&::placeholder{color:rgba(255,255,255,0.5);}&:focus{outline:none;border-color:#4a90e2;background:rgba(255,255,255,0.15);}\"\n]);\n_c26 = EmailInput;\nconst SubscribeButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"Footer__SubscribeButton\",\n    componentId: \"sc-d8be31fd-26\"\n})([\n    \"background:linear-gradient(135deg,#4a90e2,#7b68ee);color:white;border:none;border-radius:8px;padding:0.75rem 1.5rem;font-weight:600;cursor:pointer;white-space:nowrap;\"\n]);\n_c27 = SubscribeButton;\nconst NewsletterBenefits = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__NewsletterBenefits\",\n    componentId: \"sc-d8be31fd-27\"\n})([\n    \"display:flex;flex-direction:column;gap:0.5rem;\"\n]);\n_c28 = NewsletterBenefits;\nconst BenefitItem = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__BenefitItem\",\n    componentId: \"sc-d8be31fd-28\"\n})([\n    \"color:rgba(255,255,255,0.7);font-size:0.85rem;\"\n]);\n_c29 = BenefitItem;\nconst FooterBottom = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__FooterBottom\",\n    componentId: \"sc-d8be31fd-29\"\n})([\n    \"border-top:1px solid rgba(255,255,255,0.1);padding:2rem 0;\"\n]);\n_c30 = FooterBottom;\nconst BottomContent = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__BottomContent\",\n    componentId: \"sc-d8be31fd-30\"\n})([\n    \"max-width:1400px;margin:0 auto;padding:0 2rem;display:grid;grid-template-columns:1fr auto 1fr;gap:2rem;align-items:center;@media (max-width:768px){grid-template-columns:1fr;text-align:center;gap:1rem;padding:0 1rem;}\"\n]);\n_c31 = BottomContent;\nconst LegalLinks = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__LegalLinks\",\n    componentId: \"sc-d8be31fd-31\"\n})([\n    \"display:flex;flex-wrap:wrap;gap:1.5rem;@media (max-width:768px){justify-content:center;}\"\n]);\n_c32 = LegalLinks;\nconst LegalLink = (0,styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a).withConfig({\n    displayName: \"Footer__LegalLink\",\n    componentId: \"sc-d8be31fd-32\"\n})([\n    \"color:rgba(255,255,255,0.6);text-decoration:none;font-size:0.8rem;transition:color 0.3s ease;&:hover{color:#4a90e2;}\"\n]);\n_c33 = LegalLink;\nconst Copyright = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__Copyright\",\n    componentId: \"sc-d8be31fd-33\"\n})([\n    \"text-align:center;\"\n]);\n_c34 = Copyright;\nconst CopyrightText = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__CopyrightText\",\n    componentId: \"sc-d8be31fd-34\"\n})([\n    \"color:rgba(255,255,255,0.8);font-size:0.9rem;margin-bottom:0.25rem;\"\n]);\n_c35 = CopyrightText;\nconst TechInfo = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__TechInfo\",\n    componentId: \"sc-d8be31fd-35\"\n})([\n    \"color:rgba(255,255,255,0.5);font-size:0.75rem;\"\n]);\n_c36 = TechInfo;\nconst PoweredBy = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__PoweredBy\",\n    componentId: \"sc-d8be31fd-36\"\n})([\n    \"text-align:right;@media (max-width:768px){text-align:center;}\"\n]);\n_c37 = PoweredBy;\nconst PoweredText = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__PoweredText\",\n    componentId: \"sc-d8be31fd-37\"\n})([\n    \"color:rgba(255,255,255,0.7);font-size:0.85rem;margin-bottom:0.5rem;\"\n]);\n_c38 = PoweredText;\nconst TechStack = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"Footer__TechStack\",\n    componentId: \"sc-d8be31fd-38\"\n})([\n    \"display:flex;gap:0.5rem;justify-content:flex-end;@media (max-width:768px){justify-content:center;}\"\n]);\n_c39 = TechStack;\nconst TechItem = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].span.withConfig({\n    displayName: \"Footer__TechItem\",\n    componentId: \"sc-d8be31fd-39\"\n})([\n    \"background:rgba(74,144,226,0.1);color:#4a90e2;padding:0.2rem 0.5rem;border-radius:10px;font-size:0.7rem;font-weight:600;\"\n]);\n_c40 = TechItem;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Footer);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40;\n$RefreshReg$(_c, \"Footer\");\n$RefreshReg$(_c1, \"FooterContainer\");\n$RefreshReg$(_c2, \"FooterContent\");\n$RefreshReg$(_c3, \"FooterSection\");\n$RefreshReg$(_c4, \"BrandSection\");\n$RefreshReg$(_c5, \"Logo\");\n$RefreshReg$(_c6, \"LogoIcon\");\n$RefreshReg$(_c7, \"LogoText\");\n$RefreshReg$(_c8, \"LogoTitle\");\n$RefreshReg$(_c9, \"LogoVersion\");\n$RefreshReg$(_c10, \"BrandDescription\");\n$RefreshReg$(_c11, \"TechBadges\");\n$RefreshReg$(_c12, \"TechBadge\");\n$RefreshReg$(_c13, \"SectionTitle\");\n$RefreshReg$(_c14, \"LinksList\");\n$RefreshReg$(_c15, \"LinkItem\");\n$RefreshReg$(_c16, \"SocialLinks\");\n$RefreshReg$(_c17, \"SocialLink\");\n$RefreshReg$(_c18, \"SocialIcon\");\n$RefreshReg$(_c19, \"SocialName\");\n$RefreshReg$(_c20, \"CommunityStats\");\n$RefreshReg$(_c21, \"StatItem\");\n$RefreshReg$(_c22, \"StatValue\");\n$RefreshReg$(_c23, \"StatLabel\");\n$RefreshReg$(_c24, \"NewsletterDescription\");\n$RefreshReg$(_c25, \"NewsletterForm\");\n$RefreshReg$(_c26, \"EmailInput\");\n$RefreshReg$(_c27, \"SubscribeButton\");\n$RefreshReg$(_c28, \"NewsletterBenefits\");\n$RefreshReg$(_c29, \"BenefitItem\");\n$RefreshReg$(_c30, \"FooterBottom\");\n$RefreshReg$(_c31, \"BottomContent\");\n$RefreshReg$(_c32, \"LegalLinks\");\n$RefreshReg$(_c33, \"LegalLink\");\n$RefreshReg$(_c34, \"Copyright\");\n$RefreshReg$(_c35, \"CopyrightText\");\n$RefreshReg$(_c36, \"TechInfo\");\n$RefreshReg$(_c37, \"PoweredBy\");\n$RefreshReg$(_c38, \"PoweredText\");\n$RefreshReg$(_c39, \"TechStack\");\n$RefreshReg$(_c40, \"TechItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9Gb290ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTBCO0FBQ2E7QUFDQTtBQU12QyxNQUFNRyxTQUFnQztRQUFDLEVBQUVDLFdBQVcsRUFBRTs7SUFDcEQsTUFBTSxDQUFDQyxPQUFPQyxTQUFTLEdBQUdOLHFEQUFjLENBQUM7SUFFekMsTUFBTVEsa0JBQWtCLENBQUNDO1FBQ3ZCQSxFQUFFQyxjQUFjO1FBQ2hCLElBQUlMLFNBQVNELGFBQWE7WUFDeEJBLFlBQVlDO1lBQ1pDLFNBQVM7UUFDWDtJQUNGO0lBRUEsTUFBTUssY0FBYztRQUNsQjtZQUFFQyxNQUFNO1lBQVdDLE1BQU07WUFBTUMsS0FBSztZQUFtQ0MsT0FBTztRQUFVO1FBQ3hGO1lBQUVILE1BQU07WUFBWUMsTUFBTTtZQUFNQyxLQUFLO1lBQTZCQyxPQUFPO1FBQVU7UUFDbkY7WUFBRUgsTUFBTTtZQUFXQyxNQUFNO1lBQU1DLEtBQUs7WUFBb0NDLE9BQU87UUFBVTtRQUN6RjtZQUFFSCxNQUFNO1lBQVdDLE1BQU07WUFBTUMsS0FBSztZQUFxQ0MsT0FBTztRQUFVO1FBQzFGO1lBQUVILE1BQU07WUFBVUMsTUFBTTtZQUFNQyxLQUFLO1lBQWtDQyxPQUFPO1FBQVU7UUFDdEY7WUFBRUgsTUFBTTtZQUFVQyxNQUFNO1lBQU1DLEtBQUs7WUFBbUNDLE9BQU87UUFBTztLQUNyRjtJQUVELE1BQU1DLGFBQWE7UUFDakI7WUFBRUosTUFBTTtZQUFRRSxLQUFLO1FBQVM7UUFDOUI7WUFBRUYsTUFBTTtZQUFXRSxLQUFLO1FBQWU7UUFDdkM7WUFBRUYsTUFBTTtZQUFZRSxLQUFLO1FBQWE7UUFDdEM7WUFBRUYsTUFBTTtZQUFXRSxLQUFLO1FBQWU7UUFDdkM7WUFBRUYsTUFBTTtZQUFXRSxLQUFLO1FBQVc7UUFDbkM7WUFBRUYsTUFBTTtZQUFhRSxLQUFLO1FBQVc7S0FDdEM7SUFFRCxNQUFNRyxhQUFhO1FBQ2pCO1lBQUVMLE1BQU07WUFBK0JFLEtBQUs7UUFBUztRQUNyRDtZQUFFRixNQUFNO1lBQStCRSxLQUFLO1FBQVc7UUFDdkQ7WUFBRUYsTUFBTTtZQUFnQkUsS0FBSztRQUFTO1FBQ3RDO1lBQUVGLE1BQU07WUFBT0UsS0FBSztRQUFPO0tBQzVCO0lBRUQscUJBQ0UsOERBQUNJOzswQkFDQyw4REFBQ0M7O2tDQUVDLDhEQUFDQztrQ0FDQyw0RUFBQ25CLGlEQUFNQSxDQUFDb0IsR0FBRzs0QkFDVEMsU0FBUztnQ0FBRUMsU0FBUztnQ0FBR0MsR0FBRzs0QkFBRzs0QkFDN0JDLGFBQWE7Z0NBQUVGLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUU7NEJBQ2hDRSxZQUFZO2dDQUFFQyxVQUFVOzRCQUFJOzRCQUM1QkMsVUFBVTtnQ0FBRUMsTUFBTTs0QkFBSztzQ0FFdkIsNEVBQUNDOztrREFDQyw4REFBQ0M7OzBEQUNDLDhEQUFDQzswREFBUzs7Ozs7OzBEQUNWLDhEQUFDQzs7a0VBQ0MsOERBQUNDO2tFQUFVOzs7Ozs7a0VBQ1gsOERBQUNDO2tFQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSWpCLDhEQUFDQztrREFBaUI7Ozs7OztrREFLbEIsOERBQUNDOzswREFDQyw4REFBQ0M7MERBQVU7Ozs7OzswREFDWCw4REFBQ0E7MERBQVU7Ozs7OzswREFDWCw4REFBQ0E7MERBQVU7Ozs7OzswREFDWCw4REFBQ0E7MERBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT25CLDhEQUFDbEI7a0NBQ0MsNEVBQUNuQixpREFBTUEsQ0FBQ29CLEdBQUc7NEJBQ1RDLFNBQVM7Z0NBQUVDLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUc7NEJBQzdCQyxhQUFhO2dDQUFFRixTQUFTO2dDQUFHQyxHQUFHOzRCQUFFOzRCQUNoQ0UsWUFBWTtnQ0FBRUMsVUFBVTtnQ0FBS1ksT0FBTzs0QkFBSTs0QkFDeENYLFVBQVU7Z0NBQUVDLE1BQU07NEJBQUs7OzhDQUV2Qiw4REFBQ1c7OENBQWE7Ozs7Ozs4Q0FDZCw4REFBQ0M7OENBQ0V6QixXQUFXMEIsR0FBRyxDQUFDLENBQUNDLE1BQU1DLHNCQUNyQiw4REFBQ0M7NENBRUNDLE1BQU1ILEtBQUs3QixHQUFHOzRDQUNkaUMsWUFBWTtnREFBRUMsR0FBRztnREFBR2pDLE9BQU87NENBQVU7NENBQ3JDVyxZQUFZO2dEQUFFQyxVQUFVOzRDQUFJO3NEQUUzQmdCLEtBQUsvQixJQUFJOzJDQUxMK0IsS0FBSy9CLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FheEIsOERBQUNRO2tDQUNDLDRFQUFDbkIsaURBQU1BLENBQUNvQixHQUFHOzRCQUNUQyxTQUFTO2dDQUFFQyxTQUFTO2dDQUFHQyxHQUFHOzRCQUFHOzRCQUM3QkMsYUFBYTtnQ0FBRUYsU0FBUztnQ0FBR0MsR0FBRzs0QkFBRTs0QkFDaENFLFlBQVk7Z0NBQUVDLFVBQVU7Z0NBQUtZLE9BQU87NEJBQUk7NEJBQ3hDWCxVQUFVO2dDQUFFQyxNQUFNOzRCQUFLOzs4Q0FFdkIsOERBQUNXOzhDQUFhOzs7Ozs7OENBQ2QsOERBQUNTOzhDQUNFdEMsWUFBWStCLEdBQUcsQ0FBQyxDQUFDUSxRQUFRTixzQkFDeEIsOERBQUNPOzRDQUVDTCxNQUFNSSxPQUFPcEMsR0FBRzs0Q0FDaEJzQyxRQUFPOzRDQUNQQyxLQUFJOzRDQUNKdEMsT0FBT21DLE9BQU9uQyxLQUFLOzRDQUNuQmdDLFlBQVk7Z0RBQUVPLE9BQU87Z0RBQUs5QixHQUFHLENBQUM7NENBQUU7NENBQ2hDK0IsVUFBVTtnREFBRUQsT0FBTzs0Q0FBSzs0Q0FDeEJoQyxTQUFTO2dEQUFFQyxTQUFTO2dEQUFHK0IsT0FBTzs0Q0FBRTs0Q0FDaEM3QixhQUFhO2dEQUFFRixTQUFTO2dEQUFHK0IsT0FBTzs0Q0FBRTs0Q0FDcEM1QixZQUFZO2dEQUFFYSxPQUFPSyxRQUFROzRDQUFJOzRDQUNqQ2hCLFVBQVU7Z0RBQUVDLE1BQU07NENBQUs7OzhEQUV2Qiw4REFBQzJCOzhEQUFZTixPQUFPckMsSUFBSTs7Ozs7OzhEQUN4Qiw4REFBQzRDOzhEQUFZUCxPQUFPdEMsSUFBSTs7Ozs7OzsyQ0FibkJzQyxPQUFPdEMsSUFBSTs7Ozs7Ozs7Ozs4Q0FrQnRCLDhEQUFDOEM7O3NEQUNDLDhEQUFDQzs7OERBQ0MsOERBQUNDOzhEQUFVOzs7Ozs7OERBQ1gsOERBQUNDOzhEQUFVOzs7Ozs7Ozs7Ozs7c0RBRWIsOERBQUNGOzs4REFDQyw4REFBQ0M7OERBQVU7Ozs7Ozs4REFDWCw4REFBQ0M7OERBQVU7Ozs7Ozs7Ozs7OztzREFFYiw4REFBQ0Y7OzhEQUNDLDhEQUFDQzs4REFBVTs7Ozs7OzhEQUNYLDhEQUFDQzs4REFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT25CLDhEQUFDekM7a0NBQ0MsNEVBQUNuQixpREFBTUEsQ0FBQ29CLEdBQUc7NEJBQ1RDLFNBQVM7Z0NBQUVDLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUc7NEJBQzdCQyxhQUFhO2dDQUFFRixTQUFTO2dDQUFHQyxHQUFHOzRCQUFFOzRCQUNoQ0UsWUFBWTtnQ0FBRUMsVUFBVTtnQ0FBS1ksT0FBTzs0QkFBSTs0QkFDeENYLFVBQVU7Z0NBQUVDLE1BQU07NEJBQUs7OzhDQUV2Qiw4REFBQ1c7OENBQWE7Ozs7Ozs4Q0FDZCw4REFBQ3NCOzhDQUFzQjs7Ozs7OzhDQUl2Qiw4REFBQ0M7b0NBQWVDLFVBQVV4RDs7c0RBQ3hCLDhEQUFDeUQ7NENBQ0NDLE1BQUs7NENBQ0xDLGFBQVk7NENBQ1pDLE9BQU8vRDs0Q0FDUGdFLFVBQVUsQ0FBQzVELElBQU1ILFNBQVNHLEVBQUUyQyxNQUFNLENBQUNnQixLQUFLOzRDQUN4Q0UsUUFBUTs7Ozs7O3NEQUVWLDhEQUFDQzs0Q0FDQ0wsTUFBSzs0Q0FDTG5CLFlBQVk7Z0RBQUVPLE9BQU87NENBQUs7NENBQzFCQyxVQUFVO2dEQUFFRCxPQUFPOzRDQUFLO3NEQUN6Qjs7Ozs7Ozs7Ozs7OzhDQUtILDhEQUFDa0I7O3NEQUNDLDhEQUFDQztzREFBWTs7Ozs7O3NEQUNiLDhEQUFDQTtzREFBWTs7Ozs7O3NEQUNiLDhEQUFDQTtzREFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT3JCLDhEQUFDQzswQkFDQyw0RUFBQ0M7O3NDQUNDLDhEQUFDQztzQ0FDRTNELFdBQVd5QixHQUFHLENBQUMsQ0FBQ0MsTUFBTUMsc0JBQ3JCLDhEQUFDaUM7b0NBRUMvQixNQUFNSCxLQUFLN0IsR0FBRztvQ0FDZGlDLFlBQVk7d0NBQUVoQyxPQUFPO29DQUFVOzhDQUU5QjRCLEtBQUsvQixJQUFJO21DQUpMK0IsS0FBSy9CLElBQUk7Ozs7Ozs7Ozs7c0NBU3BCLDhEQUFDa0U7OzhDQUNDLDhEQUFDQzs4Q0FBYzs7Ozs7OzhDQUdmLDhEQUFDQzs4Q0FBUzs7Ozs7Ozs7Ozs7O3NDQUtaLDhEQUFDQzs7OENBQ0MsOERBQUNDOzhDQUFZOzs7Ozs7OENBQ2IsOERBQUNDOztzREFDQyw4REFBQ0M7c0RBQVM7Ozs7OztzREFDViw4REFBQ0E7c0RBQVM7Ozs7OztzREFDViw4REFBQ0E7c0RBQVM7Ozs7OztzREFDViw4REFBQ0E7c0RBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT3hCO0dBMU5NakY7S0FBQUE7QUE0Tk4sMkJBQTJCO0FBQzNCLE1BQU1lLGtCQUFrQmhCLHlEQUFNQSxDQUFDbUYsTUFBTTs7Ozs7O01BQS9CbkU7QUFpQk4sTUFBTUMsZ0JBQWdCakIseURBQU1BLENBQUNtQixHQUFHOzs7Ozs7TUFBMUJGO0FBb0JOLE1BQU1DLGdCQUFnQmxCLHlEQUFNQSxDQUFDbUIsR0FBRzs7Ozs7O01BQTFCRDtBQUVOLE1BQU1VLGVBQWU1Qix5REFBTUEsQ0FBQ21CLEdBQUc7Ozs7OztNQUF6QlM7QUFFTixNQUFNQyxPQUFPN0IseURBQU1BLENBQUNtQixHQUFHOzs7Ozs7TUFBakJVO0FBT04sTUFBTUMsV0FBVzlCLHlEQUFNQSxDQUFDbUIsR0FBRzs7Ozs7O01BQXJCVztBQUtOLE1BQU1DLFdBQVcvQix5REFBTUEsQ0FBQ21CLEdBQUc7Ozs7OztNQUFyQlk7QUFFTixNQUFNQyxZQUFZaEMseURBQU1BLENBQUNtQixHQUFHOzs7Ozs7TUFBdEJhO0FBU04sTUFBTUMsY0FBY2pDLHlEQUFNQSxDQUFDbUIsR0FBRzs7Ozs7O01BQXhCYztBQU1OLE1BQU1DLG1CQUFtQmxDLHlEQUFNQSxDQUFDb0YsQ0FBQzs7Ozs7O09BQTNCbEQ7QUFPTixNQUFNQyxhQUFhbkMseURBQU1BLENBQUNtQixHQUFHOzs7Ozs7T0FBdkJnQjtBQU1OLE1BQU1DLFlBQVlwQyx5REFBTUEsQ0FBQ3FGLElBQUk7Ozs7OztPQUF2QmpEO0FBVU4sTUFBTUUsZUFBZXRDLHlEQUFNQSxDQUFDc0YsRUFBRTs7Ozs7O09BQXhCaEQ7QUFPTixNQUFNQyxZQUFZdkMseURBQU1BLENBQUNtQixHQUFHOzs7Ozs7T0FBdEJvQjtBQU1OLE1BQU1JLFdBQVczQyw2REFBTUEsQ0FBQ0QsaURBQU1BLENBQUN3RixDQUFDOzs7Ozs7T0FBMUI1QztBQVdOLE1BQU1JLGNBQWMvQyx5REFBTUEsQ0FBQ21CLEdBQUc7Ozs7OztPQUF4QjRCO0FBT04sTUFBTUUsYUFBYWpELDZEQUFNQSxDQUFDRCxpREFBTUEsQ0FBQ3dGLENBQUM7Ozs7Ozs7R0FhaEJDLENBQUFBLFFBQVMsR0FBZSxPQUFaQSxNQUFNM0UsS0FBSyxFQUFDLE9BQ3RCMkUsQ0FBQUEsUUFBU0EsTUFBTTNFLEtBQUs7T0FkbENvQztBQWtCTixNQUFNSyxhQUFhdEQseURBQU1BLENBQUNxRixJQUFJOzs7Ozs7T0FBeEIvQjtBQUlOLE1BQU1DLGFBQWF2RCx5REFBTUEsQ0FBQ3FGLElBQUk7Ozs7OztPQUF4QjlCO0FBS04sTUFBTUMsaUJBQWlCeEQseURBQU1BLENBQUNtQixHQUFHOzs7Ozs7T0FBM0JxQztBQU1OLE1BQU1DLFdBQVd6RCx5REFBTUEsQ0FBQ21CLEdBQUc7Ozs7OztPQUFyQnNDO0FBT04sTUFBTUMsWUFBWTFELHlEQUFNQSxDQUFDbUIsR0FBRzs7Ozs7O09BQXRCdUM7QUFPTixNQUFNQyxZQUFZM0QseURBQU1BLENBQUNtQixHQUFHOzs7Ozs7T0FBdEJ3QztBQUtOLE1BQU1DLHdCQUF3QjVELHlEQUFNQSxDQUFDb0YsQ0FBQzs7Ozs7O09BQWhDeEI7QUFPTixNQUFNQyxpQkFBaUI3RCx5REFBTUEsQ0FBQ3lGLElBQUk7Ozs7OztPQUE1QjVCO0FBTU4sTUFBTUUsYUFBYS9ELHlEQUFNQSxDQUFDMEYsS0FBSzs7Ozs7O09BQXpCM0I7QUFvQk4sTUFBTU0sa0JBQWtCckUsNkRBQU1BLENBQUNELGlEQUFNQSxDQUFDNEYsTUFBTTs7Ozs7O09BQXRDdEI7QUFXTixNQUFNQyxxQkFBcUJ0RSx5REFBTUEsQ0FBQ21CLEdBQUc7Ozs7OztPQUEvQm1EO0FBTU4sTUFBTUMsY0FBY3ZFLHlEQUFNQSxDQUFDbUIsR0FBRzs7Ozs7O09BQXhCb0Q7QUFLTixNQUFNQyxlQUFleEUseURBQU1BLENBQUNtQixHQUFHOzs7Ozs7T0FBekJxRDtBQUtOLE1BQU1DLGdCQUFnQnpFLHlEQUFNQSxDQUFDbUIsR0FBRzs7Ozs7O09BQTFCc0Q7QUFpQk4sTUFBTUMsYUFBYTFFLHlEQUFNQSxDQUFDbUIsR0FBRzs7Ozs7O09BQXZCdUQ7QUFVTixNQUFNQyxZQUFZM0UsNkRBQU1BLENBQUNELGlEQUFNQSxDQUFDd0YsQ0FBQzs7Ozs7O09BQTNCWjtBQVdOLE1BQU1DLFlBQVk1RSx5REFBTUEsQ0FBQ21CLEdBQUc7Ozs7OztPQUF0QnlEO0FBSU4sTUFBTUMsZ0JBQWdCN0UseURBQU1BLENBQUNtQixHQUFHOzs7Ozs7T0FBMUIwRDtBQU1OLE1BQU1DLFdBQVc5RSx5REFBTUEsQ0FBQ21CLEdBQUc7Ozs7OztPQUFyQjJEO0FBS04sTUFBTUMsWUFBWS9FLHlEQUFNQSxDQUFDbUIsR0FBRzs7Ozs7O09BQXRCNEQ7QUFRTixNQUFNQyxjQUFjaEYseURBQU1BLENBQUNtQixHQUFHOzs7Ozs7T0FBeEI2RDtBQU1OLE1BQU1DLFlBQVlqRix5REFBTUEsQ0FBQ21CLEdBQUc7Ozs7OztPQUF0QjhEO0FBVU4sTUFBTUMsV0FBV2xGLHlEQUFNQSxDQUFDcUYsSUFBSTs7Ozs7O09BQXRCSDtBQVNOLCtEQUFlakYsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9Gb290ZXIudHN4PzM1MWYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nO1xuaW1wb3J0IHN0eWxlZCBmcm9tICdzdHlsZWQtY29tcG9uZW50cyc7XG5cbmludGVyZmFjZSBGb290ZXJQcm9wcyB7XG4gIG9uU3Vic2NyaWJlPzogKGVtYWlsOiBzdHJpbmcpID0+IHZvaWQ7XG59XG5cbmNvbnN0IEZvb3RlcjogUmVhY3QuRkM8Rm9vdGVyUHJvcHM+ID0gKHsgb25TdWJzY3JpYmUgfSkgPT4ge1xuICBjb25zdCBbZW1haWwsIHNldEVtYWlsXSA9IFJlYWN0LnVzZVN0YXRlKCcnKTtcblxuICBjb25zdCBoYW5kbGVTdWJzY3JpYmUgPSAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIGlmIChlbWFpbCAmJiBvblN1YnNjcmliZSkge1xuICAgICAgb25TdWJzY3JpYmUoZW1haWwpO1xuICAgICAgc2V0RW1haWwoJycpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBzb2NpYWxMaW5rcyA9IFtcbiAgICB7IG5hbWU6ICdEaXNjb3JkJywgaWNvbjogJ/CfkqwnLCB1cmw6ICdodHRwczovL2Rpc2NvcmQuZ2cva296eXJtYXN0ZXJyJywgY29sb3I6ICcjNTg2NUYyJyB9LFxuICAgIHsgbmFtZTogJ1RlbGVncmFtJywgaWNvbjogJ/Cfk7EnLCB1cmw6ICdodHRwczovL3QubWUva296eXJtYXN0ZXJyJywgY29sb3I6ICcjMDA4OGNjJyB9LFxuICAgIHsgbmFtZTogJ1R3aXR0ZXInLCBpY29uOiAn8J+QpicsIHVybDogJ2h0dHBzOi8vdHdpdHRlci5jb20va296eXJtYXN0ZXJyJywgY29sb3I6ICcjMURBMUYyJyB9LFxuICAgIHsgbmFtZTogJ1lvdVR1YmUnLCBpY29uOiAn8J+TuicsIHVybDogJ2h0dHBzOi8veW91dHViZS5jb20vQGtvenlybWFzdGVycicsIGNvbG9yOiAnI0ZGMDAwMCcgfSxcbiAgICB7IG5hbWU6ICdUd2l0Y2gnLCBpY29uOiAn8J+OricsIHVybDogJ2h0dHBzOi8vdHdpdGNoLnR2L2tvenlybWFzdGVycicsIGNvbG9yOiAnIzkxNDZGRicgfSxcbiAgICB7IG5hbWU6ICdHaXRIdWInLCBpY29uOiAn8J+SuycsIHVybDogJ2h0dHBzOi8vZ2l0aHViLmNvbS9rb3p5cm1hc3RlcnInLCBjb2xvcjogJyMzMzMnIH1cbiAgXTtcblxuICBjb25zdCBxdWlja0xpbmtzID0gW1xuICAgIHsgbmFtZTogJ9CY0LPRgNGLJywgdXJsOiAnL2dhbWVzJyB9LFxuICAgIHsgbmFtZTogJ9Ci0YPRgNC90LjRgNGLJywgdXJsOiAnL3RvdXJuYW1lbnRzJyB9LFxuICAgIHsgbmFtZTogJ9Ce0LHRg9GH0LXQvdC40LUnLCB1cmw6ICcvdHV0b3JpYWxzJyB9LFxuICAgIHsgbmFtZTogJ9Cg0LXQudGC0LjQvdCzJywgdXJsOiAnL2xlYWRlcmJvYXJkJyB9LFxuICAgIHsgbmFtZTogJ9Cf0YDQvtGE0LjQu9GMJywgdXJsOiAnL3Byb2ZpbGUnIH0sXG4gICAgeyBuYW1lOiAn0J/QvtC00LTQtdGA0LbQutCwJywgdXJsOiAnL3N1cHBvcnQnIH1cbiAgXTtcblxuICBjb25zdCBsZWdhbExpbmtzID0gW1xuICAgIHsgbmFtZTogJ9Cf0L7Qu9GM0LfQvtCy0LDRgtC10LvRjNGB0LrQvtC1INGB0L7Qs9C70LDRiNC10L3QuNC1JywgdXJsOiAnL3Rlcm1zJyB9LFxuICAgIHsgbmFtZTogJ9Cf0L7Qu9C40YLQuNC60LAg0LrQvtC90YTQuNC00LXQvdGG0LjQsNC70YzQvdC+0YHRgtC4JywgdXJsOiAnL3ByaXZhY3knIH0sXG4gICAgeyBuYW1lOiAn0J/RgNCw0LLQuNC70LAg0LjQs9GA0YsnLCB1cmw6ICcvcnVsZXMnIH0sXG4gICAgeyBuYW1lOiAnRkFRJywgdXJsOiAnL2ZhcScgfVxuICBdO1xuXG4gIHJldHVybiAoXG4gICAgPEZvb3RlckNvbnRhaW5lcj5cbiAgICAgIDxGb290ZXJDb250ZW50PlxuICAgICAgICB7Lyog0J7RgdC90L7QstC90LDRjyDQuNC90YTQvtGA0LzQsNGG0LjRjyAqL31cbiAgICAgICAgPEZvb3RlclNlY3Rpb24+XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogNTAgfX1cbiAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuOCB9fVxuICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxCcmFuZFNlY3Rpb24+XG4gICAgICAgICAgICAgIDxMb2dvPlxuICAgICAgICAgICAgICAgIDxMb2dvSWNvbj7wn46uPC9Mb2dvSWNvbj5cbiAgICAgICAgICAgICAgICA8TG9nb1RleHQ+XG4gICAgICAgICAgICAgICAgICA8TG9nb1RpdGxlPtCa0L7Qt9GL0YDRjCDQnNCw0YHRgtC10YA8L0xvZ29UaXRsZT5cbiAgICAgICAgICAgICAgICAgIDxMb2dvVmVyc2lvbj40LjA8L0xvZ29WZXJzaW9uPlxuICAgICAgICAgICAgICAgIDwvTG9nb1RleHQ+XG4gICAgICAgICAgICAgIDwvTG9nbz5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxCcmFuZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgINCg0LXQstC+0LvRjtGG0LjQvtC90L3QsNGPINC/0LvQsNGC0YTQvtGA0LzQsCDQutCw0YDRgtC+0YfQvdGL0YUg0LjQs9GAINGBINC60LLQsNC90YLQvtCy0L7QuSDRgdC70YPRh9Cw0LnQvdC+0YHRgtGM0Y4sIFxuICAgICAgICAgICAgICAgINGN0LzQvtGG0LjQvtC90LDQu9GM0L3Ri9C8INCY0Jgg0LggM0Qg0LzQtdGC0LDQstGB0LXQu9C10L3QvdC+0LkuINCR0YPQtNGD0YnQtdC1INC40LPRgCDRg9C20LUg0LfQtNC10YHRjCFcbiAgICAgICAgICAgICAgPC9CcmFuZERlc2NyaXB0aW9uPlxuXG4gICAgICAgICAgICAgIDxUZWNoQmFkZ2VzPlxuICAgICAgICAgICAgICAgIDxUZWNoQmFkZ2U+4pqb77iPINCa0LLQsNC90YLQvtCy0YvQtSDRgtC10YXQvdC+0LvQvtCz0LjQuDwvVGVjaEJhZGdlPlxuICAgICAgICAgICAgICAgIDxUZWNoQmFkZ2U+8J+noCDQrdC80L7RhtC40L7QvdCw0LvRjNC90YvQuSDQmNCYPC9UZWNoQmFkZ2U+XG4gICAgICAgICAgICAgICAgPFRlY2hCYWRnZT7wn4yNIDNEINCc0LXRgtCw0LLRgdC10LvQtdC90L3QsNGPPC9UZWNoQmFkZ2U+XG4gICAgICAgICAgICAgICAgPFRlY2hCYWRnZT7im5PvuI8gV2ViMyAmIE5GVDwvVGVjaEJhZGdlPlxuICAgICAgICAgICAgICA8L1RlY2hCYWRnZXM+XG4gICAgICAgICAgICA8L0JyYW5kU2VjdGlvbj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgIDwvRm9vdGVyU2VjdGlvbj5cblxuICAgICAgICB7Lyog0JHRi9GB0YLRgNGL0LUg0YHRgdGL0LvQutC4ICovfVxuICAgICAgICA8Rm9vdGVyU2VjdGlvbj5cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiA1MCB9fVxuICAgICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44LCBkZWxheTogMC4xIH19XG4gICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNlY3Rpb25UaXRsZT7QkdGL0YHRgtGA0YvQtSDRgdGB0YvQu9C60Lg8L1NlY3Rpb25UaXRsZT5cbiAgICAgICAgICAgIDxMaW5rc0xpc3Q+XG4gICAgICAgICAgICAgIHtxdWlja0xpbmtzLm1hcCgobGluaywgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICA8TGlua0l0ZW1cbiAgICAgICAgICAgICAgICAgIGtleT17bGluay5uYW1lfVxuICAgICAgICAgICAgICAgICAgaHJlZj17bGluay51cmx9XG4gICAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHg6IDUsIGNvbG9yOiAnIzRhOTBlMicgfX1cbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMiB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtsaW5rLm5hbWV9XG4gICAgICAgICAgICAgICAgPC9MaW5rSXRlbT5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L0xpbmtzTGlzdD5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgIDwvRm9vdGVyU2VjdGlvbj5cblxuICAgICAgICB7Lyog0KHQvtC+0LHRidC10YHRgtCy0L4gKi99XG4gICAgICAgIDxGb290ZXJTZWN0aW9uPlxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDUwIH19XG4gICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjgsIGRlbGF5OiAwLjIgfX1cbiAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U2VjdGlvblRpdGxlPtCh0L7QvtCx0YnQtdGB0YLQstC+PC9TZWN0aW9uVGl0bGU+XG4gICAgICAgICAgICA8U29jaWFsTGlua3M+XG4gICAgICAgICAgICAgIHtzb2NpYWxMaW5rcy5tYXAoKHNvY2lhbCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICA8U29jaWFsTGlua1xuICAgICAgICAgICAgICAgICAga2V5PXtzb2NpYWwubmFtZX1cbiAgICAgICAgICAgICAgICAgIGhyZWY9e3NvY2lhbC51cmx9XG4gICAgICAgICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXG4gICAgICAgICAgICAgICAgICBjb2xvcj17c29jaWFsLmNvbG9yfVxuICAgICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4xLCB5OiAtMiB9fVxuICAgICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTUgfX1cbiAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgc2NhbGU6IDAgfX1cbiAgICAgICAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHNjYWxlOiAxIH19XG4gICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGRlbGF5OiBpbmRleCAqIDAuMSB9fVxuICAgICAgICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxTb2NpYWxJY29uPntzb2NpYWwuaWNvbn08L1NvY2lhbEljb24+XG4gICAgICAgICAgICAgICAgICA8U29jaWFsTmFtZT57c29jaWFsLm5hbWV9PC9Tb2NpYWxOYW1lPlxuICAgICAgICAgICAgICAgIDwvU29jaWFsTGluaz5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L1NvY2lhbExpbmtzPlxuXG4gICAgICAgICAgICA8Q29tbXVuaXR5U3RhdHM+XG4gICAgICAgICAgICAgIDxTdGF0SXRlbT5cbiAgICAgICAgICAgICAgICA8U3RhdFZhbHVlPjUwSys8L1N0YXRWYWx1ZT5cbiAgICAgICAgICAgICAgICA8U3RhdExhYmVsPtCY0LPRgNC+0LrQvtCyPC9TdGF0TGFiZWw+XG4gICAgICAgICAgICAgIDwvU3RhdEl0ZW0+XG4gICAgICAgICAgICAgIDxTdGF0SXRlbT5cbiAgICAgICAgICAgICAgICA8U3RhdFZhbHVlPjE1Sys8L1N0YXRWYWx1ZT5cbiAgICAgICAgICAgICAgICA8U3RhdExhYmVsPkRpc2NvcmQ8L1N0YXRMYWJlbD5cbiAgICAgICAgICAgICAgPC9TdGF0SXRlbT5cbiAgICAgICAgICAgICAgPFN0YXRJdGVtPlxuICAgICAgICAgICAgICAgIDxTdGF0VmFsdWU+MjVLKzwvU3RhdFZhbHVlPlxuICAgICAgICAgICAgICAgIDxTdGF0TGFiZWw+0J/QvtC00L/QuNGB0YfQuNC60L7QsjwvU3RhdExhYmVsPlxuICAgICAgICAgICAgICA8L1N0YXRJdGVtPlxuICAgICAgICAgICAgPC9Db21tdW5pdHlTdGF0cz5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgIDwvRm9vdGVyU2VjdGlvbj5cblxuICAgICAgICB7Lyog0J/QvtC00L/QuNGB0LrQsCDQvdCwINC90L7QstC+0YHRgtC4ICovfVxuICAgICAgICA8Rm9vdGVyU2VjdGlvbj5cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiA1MCB9fVxuICAgICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44LCBkZWxheTogMC4zIH19XG4gICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNlY3Rpb25UaXRsZT7QndC+0LLQvtGB0YLQuCDQuCDQvtCx0L3QvtCy0LvQtdC90LjRjzwvU2VjdGlvblRpdGxlPlxuICAgICAgICAgICAgPE5ld3NsZXR0ZXJEZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAg0JHRg9C00YzRgtC1INCyINC60YPRgNGB0LUg0L/QvtGB0LvQtdC00L3QuNGFINC90L7QstC+0YHRgtC10LksINGC0YPRgNC90LjRgNC+0LIg0Lgg0L7QsdC90L7QstC70LXQvdC40Lkg0L/Qu9Cw0YLRhNC+0YDQvNGLXG4gICAgICAgICAgICA8L05ld3NsZXR0ZXJEZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPE5ld3NsZXR0ZXJGb3JtIG9uU3VibWl0PXtoYW5kbGVTdWJzY3JpYmV9PlxuICAgICAgICAgICAgICA8RW1haWxJbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLQktCw0YggZW1haWwg0LDQtNGA0LXRgVwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2VtYWlsfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RW1haWwoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDxTdWJzY3JpYmVCdXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjA1IH19XG4gICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTUgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIPCfmoAg0J/QvtC00L/QuNGB0LDRgtGM0YHRj1xuICAgICAgICAgICAgICA8L1N1YnNjcmliZUJ1dHRvbj5cbiAgICAgICAgICAgIDwvTmV3c2xldHRlckZvcm0+XG5cbiAgICAgICAgICAgIDxOZXdzbGV0dGVyQmVuZWZpdHM+XG4gICAgICAgICAgICAgIDxCZW5lZml0SXRlbT7inKgg0K3QutGB0LrQu9GO0LfQuNCy0L3Ri9C1INGC0YPRgNC90LjRgNGLPC9CZW5lZml0SXRlbT5cbiAgICAgICAgICAgICAgPEJlbmVmaXRJdGVtPvCfjoEg0JHQvtC90YPRgdGLINC4INC/0YDQvtC80L7QutC+0LTRizwvQmVuZWZpdEl0ZW0+XG4gICAgICAgICAgICAgIDxCZW5lZml0SXRlbT7wn5OwINCf0LXRgNCy0YvQvNC4INGD0LfQvdCw0LLQsNC50YLQtSDQvdC+0LLQvtGB0YLQuDwvQmVuZWZpdEl0ZW0+XG4gICAgICAgICAgICA8L05ld3NsZXR0ZXJCZW5lZml0cz5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgIDwvRm9vdGVyU2VjdGlvbj5cbiAgICAgIDwvRm9vdGVyQ29udGVudD5cblxuICAgICAgey8qINCd0LjQttC90Y/RjyDRh9Cw0YHRgtGMICovfVxuICAgICAgPEZvb3RlckJvdHRvbT5cbiAgICAgICAgPEJvdHRvbUNvbnRlbnQ+XG4gICAgICAgICAgPExlZ2FsTGlua3M+XG4gICAgICAgICAgICB7bGVnYWxMaW5rcy5tYXAoKGxpbmssIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgIDxMZWdhbExpbmtcbiAgICAgICAgICAgICAgICBrZXk9e2xpbmsubmFtZX1cbiAgICAgICAgICAgICAgICBocmVmPXtsaW5rLnVybH1cbiAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IGNvbG9yOiAnIzRhOTBlMicgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtsaW5rLm5hbWV9XG4gICAgICAgICAgICAgIDwvTGVnYWxMaW5rPlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9MZWdhbExpbmtzPlxuXG4gICAgICAgICAgPENvcHlyaWdodD5cbiAgICAgICAgICAgIDxDb3B5cmlnaHRUZXh0PlxuICAgICAgICAgICAgICDCqSAyMDI0INCa0L7Qt9GL0YDRjCDQnNCw0YHRgtC10YAgNC4wLiDQktGB0LUg0L/RgNCw0LLQsCDQt9Cw0YnQuNGJ0LXQvdGLLlxuICAgICAgICAgICAgPC9Db3B5cmlnaHRUZXh0PlxuICAgICAgICAgICAgPFRlY2hJbmZvPlxuICAgICAgICAgICAgICDQoNCw0LHQvtGC0LDQtdGCINC90LAg0LrQstCw0L3RgtC+0LLRi9GFINGC0LXRhdC90L7Qu9C+0LPQuNGP0YUg0Lgg0Y3QvNC+0YbQuNC+0L3QsNC70YzQvdC+0Lwg0JjQmFxuICAgICAgICAgICAgPC9UZWNoSW5mbz5cbiAgICAgICAgICA8L0NvcHlyaWdodD5cblxuICAgICAgICAgIDxQb3dlcmVkQnk+XG4gICAgICAgICAgICA8UG93ZXJlZFRleHQ+0KHQvtC30LTQsNC90L4g0YEg4p2k77iPINC60L7QvNCw0L3QtNC+0Lkg0JrQvtC30YvRgNGMINCc0LDRgdGC0LXRgDwvUG93ZXJlZFRleHQ+XG4gICAgICAgICAgICA8VGVjaFN0YWNrPlxuICAgICAgICAgICAgICA8VGVjaEl0ZW0+UmVhY3Q8L1RlY2hJdGVtPlxuICAgICAgICAgICAgICA8VGVjaEl0ZW0+VGhyZWUuanM8L1RlY2hJdGVtPlxuICAgICAgICAgICAgICA8VGVjaEl0ZW0+V2ViMzwvVGVjaEl0ZW0+XG4gICAgICAgICAgICAgIDxUZWNoSXRlbT5BSS9NTDwvVGVjaEl0ZW0+XG4gICAgICAgICAgICA8L1RlY2hTdGFjaz5cbiAgICAgICAgICA8L1Bvd2VyZWRCeT5cbiAgICAgICAgPC9Cb3R0b21Db250ZW50PlxuICAgICAgPC9Gb290ZXJCb3R0b20+XG4gICAgPC9Gb290ZXJDb250YWluZXI+XG4gICk7XG59O1xuXG4vLyDQodGC0LjQu9C40LfQvtCy0LDQvdC90YvQtSDQutC+0LzQv9C+0L3QtdC90YLRi1xuY29uc3QgRm9vdGVyQ29udGFpbmVyID0gc3R5bGVkLmZvb3RlcmBcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzBmMGYyMyAwJSwgIzFhMWEyZSA1MCUsICMxNjIxM2UgMTAwJSk7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBcbiAgJjo6YmVmb3JlIHtcbiAgICBjb250ZW50OiAnJztcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgdG9wOiAwO1xuICAgIGxlZnQ6IDA7XG4gICAgcmlnaHQ6IDA7XG4gICAgaGVpZ2h0OiAxcHg7XG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCB0cmFuc3BhcmVudCwgIzRhOTBlMiwgdHJhbnNwYXJlbnQpO1xuICB9XG5gO1xuXG5jb25zdCBGb290ZXJDb250ZW50ID0gc3R5bGVkLmRpdmBcbiAgbWF4LXdpZHRoOiAxNDAwcHg7XG4gIG1hcmdpbjogMCBhdXRvO1xuICBwYWRkaW5nOiA0cmVtIDJyZW0gMnJlbTtcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAyZnIgMWZyIDFmciAxLjVmcjtcbiAgZ2FwOiAzcmVtO1xuICBcbiAgQG1lZGlhIChtYXgtd2lkdGg6IDEwMjRweCkge1xuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDIsIDFmcik7XG4gICAgZ2FwOiAycmVtO1xuICB9XG4gIFxuICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgICBnYXA6IDJyZW07XG4gICAgcGFkZGluZzogMnJlbSAxcmVtIDFyZW07XG4gIH1cbmA7XG5cbmNvbnN0IEZvb3RlclNlY3Rpb24gPSBzdHlsZWQuZGl2YGA7XG5cbmNvbnN0IEJyYW5kU2VjdGlvbiA9IHN0eWxlZC5kaXZgYDtcblxuY29uc3QgTG9nbyA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMXJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuYDtcblxuY29uc3QgTG9nb0ljb24gPSBzdHlsZWQuZGl2YFxuICBmb250LXNpemU6IDNyZW07XG4gIGZpbHRlcjogZHJvcC1zaGFkb3coMCAwIDIwcHggcmdiYSg3NCwgMTQ0LCAyMjYsIDAuNSkpO1xuYDtcblxuY29uc3QgTG9nb1RleHQgPSBzdHlsZWQuZGl2YGA7XG5cbmNvbnN0IExvZ29UaXRsZSA9IHN0eWxlZC5kaXZgXG4gIGZvbnQtc2l6ZTogMS44cmVtO1xuICBmb250LXdlaWdodDogOTAwO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoNDVkZWcsICM0YTkwZTIsICM3YjY4ZWUpO1xuICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcbiAgLXdlYmtpdC10ZXh0LWZpbGwtY29sb3I6IHRyYW5zcGFyZW50O1xuICBsaW5lLWhlaWdodDogMTtcbmA7XG5cbmNvbnN0IExvZ29WZXJzaW9uID0gc3R5bGVkLmRpdmBcbiAgZm9udC1zaXplOiAxcmVtO1xuICBjb2xvcjogIzRhOTBlMjtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbmA7XG5cbmNvbnN0IEJyYW5kRGVzY3JpcHRpb24gPSBzdHlsZWQucGBcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KTtcbiAgbGluZS1oZWlnaHQ6IDEuNjtcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuICBmb250LXNpemU6IDAuOTVyZW07XG5gO1xuXG5jb25zdCBUZWNoQmFkZ2VzID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC13cmFwOiB3cmFwO1xuICBnYXA6IDAuNXJlbTtcbmA7XG5cbmNvbnN0IFRlY2hCYWRnZSA9IHN0eWxlZC5zcGFuYFxuICBiYWNrZ3JvdW5kOiByZ2JhKDc0LCAxNDQsIDIyNiwgMC4yKTtcbiAgY29sb3I6ICM0YTkwZTI7XG4gIHBhZGRpbmc6IDAuM3JlbSAwLjhyZW07XG4gIGJvcmRlci1yYWRpdXM6IDE1cHg7XG4gIGZvbnQtc2l6ZTogMC44cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDc0LCAxNDQsIDIyNiwgMC4zKTtcbmA7XG5cbmNvbnN0IFNlY3Rpb25UaXRsZSA9IHN0eWxlZC5oNGBcbiAgZm9udC1zaXplOiAxLjNyZW07XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcbiAgY29sb3I6IHdoaXRlO1xuYDtcblxuY29uc3QgTGlua3NMaXN0ID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiAwLjc1cmVtO1xuYDtcblxuY29uc3QgTGlua0l0ZW0gPSBzdHlsZWQobW90aW9uLmEpYFxuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpO1xuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gIGZvbnQtc2l6ZTogMC45NXJlbTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgXG4gICY6aG92ZXIge1xuICAgIGNvbG9yOiAjNGE5MGUyO1xuICB9XG5gO1xuXG5jb25zdCBTb2NpYWxMaW5rcyA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDIsIDFmcik7XG4gIGdhcDogMC43NXJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuYDtcblxuY29uc3QgU29jaWFsTGluayA9IHN0eWxlZChtb3Rpb24uYSk8eyBjb2xvcjogc3RyaW5nIH0+YFxuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDAuNXJlbTtcbiAgcGFkZGluZzogMC43NXJlbTtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KTtcbiAgYm9yZGVyLXJhZGl1czogMTBweDtcbiAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICBjb2xvcjogd2hpdGU7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgXG4gICY6aG92ZXIge1xuICAgIGJhY2tncm91bmQ6ICR7cHJvcHMgPT4gYCR7cHJvcHMuY29sb3J9MjBgfTtcbiAgICBib3JkZXItY29sb3I6ICR7cHJvcHMgPT4gcHJvcHMuY29sb3J9O1xuICB9XG5gO1xuXG5jb25zdCBTb2NpYWxJY29uID0gc3R5bGVkLnNwYW5gXG4gIGZvbnQtc2l6ZTogMS4ycmVtO1xuYDtcblxuY29uc3QgU29jaWFsTmFtZSA9IHN0eWxlZC5zcGFuYFxuICBmb250LXNpemU6IDAuODVyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG5gO1xuXG5jb25zdCBDb21tdW5pdHlTdGF0cyA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDMsIDFmcik7XG4gIGdhcDogMXJlbTtcbmA7XG5cbmNvbnN0IFN0YXRJdGVtID0gc3R5bGVkLmRpdmBcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpO1xuICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICBwYWRkaW5nOiAxcmVtIDAuNXJlbTtcbmA7XG5cbmNvbnN0IFN0YXRWYWx1ZSA9IHN0eWxlZC5kaXZgXG4gIGZvbnQtc2l6ZTogMS4ycmVtO1xuICBmb250LXdlaWdodDogNzAwO1xuICBjb2xvcjogIzRhOTBlMjtcbiAgbWFyZ2luLWJvdHRvbTogMC4yNXJlbTtcbmA7XG5cbmNvbnN0IFN0YXRMYWJlbCA9IHN0eWxlZC5kaXZgXG4gIGZvbnQtc2l6ZTogMC44cmVtO1xuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpO1xuYDtcblxuY29uc3QgTmV3c2xldHRlckRlc2NyaXB0aW9uID0gc3R5bGVkLnBgXG4gIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCk7XG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcbiAgZm9udC1zaXplOiAwLjlyZW07XG4gIGxpbmUtaGVpZ2h0OiAxLjU7XG5gO1xuXG5jb25zdCBOZXdzbGV0dGVyRm9ybSA9IHN0eWxlZC5mb3JtYFxuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDAuNXJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbmA7XG5cbmNvbnN0IEVtYWlsSW5wdXQgPSBzdHlsZWQuaW5wdXRgXG4gIGZsZXg6IDE7XG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIHBhZGRpbmc6IDAuNzVyZW07XG4gIGNvbG9yOiB3aGl0ZTtcbiAgZm9udC1zaXplOiAwLjlyZW07XG4gIFxuICAmOjpwbGFjZWhvbGRlciB7XG4gICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC41KTtcbiAgfVxuICBcbiAgJjpmb2N1cyB7XG4gICAgb3V0bGluZTogbm9uZTtcbiAgICBib3JkZXItY29sb3I6ICM0YTkwZTI7XG4gICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjE1KTtcbiAgfVxuYDtcblxuY29uc3QgU3Vic2NyaWJlQnV0dG9uID0gc3R5bGVkKG1vdGlvbi5idXR0b24pYFxuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNGE5MGUyLCAjN2I2OGVlKTtcbiAgY29sb3I6IHdoaXRlO1xuICBib3JkZXI6IG5vbmU7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgcGFkZGluZzogMC43NXJlbSAxLjVyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbmA7XG5cbmNvbnN0IE5ld3NsZXR0ZXJCZW5lZml0cyA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogMC41cmVtO1xuYDtcblxuY29uc3QgQmVuZWZpdEl0ZW0gPSBzdHlsZWQuZGl2YFxuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpO1xuICBmb250LXNpemU6IDAuODVyZW07XG5gO1xuXG5jb25zdCBGb290ZXJCb3R0b20gPSBzdHlsZWQuZGl2YFxuICBib3JkZXItdG9wOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuICBwYWRkaW5nOiAycmVtIDA7XG5gO1xuXG5jb25zdCBCb3R0b21Db250ZW50ID0gc3R5bGVkLmRpdmBcbiAgbWF4LXdpZHRoOiAxNDAwcHg7XG4gIG1hcmdpbjogMCBhdXRvO1xuICBwYWRkaW5nOiAwIDJyZW07XG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyIGF1dG8gMWZyO1xuICBnYXA6IDJyZW07XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIFxuICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgZ2FwOiAxcmVtO1xuICAgIHBhZGRpbmc6IDAgMXJlbTtcbiAgfVxuYDtcblxuY29uc3QgTGVnYWxMaW5rcyA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtd3JhcDogd3JhcDtcbiAgZ2FwOiAxLjVyZW07XG4gIFxuICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgfVxuYDtcblxuY29uc3QgTGVnYWxMaW5rID0gc3R5bGVkKG1vdGlvbi5hKWBcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC42KTtcbiAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICBmb250LXNpemU6IDAuOHJlbTtcbiAgdHJhbnNpdGlvbjogY29sb3IgMC4zcyBlYXNlO1xuICBcbiAgJjpob3ZlciB7XG4gICAgY29sb3I6ICM0YTkwZTI7XG4gIH1cbmA7XG5cbmNvbnN0IENvcHlyaWdodCA9IHN0eWxlZC5kaXZgXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbmA7XG5cbmNvbnN0IENvcHlyaWdodFRleHQgPSBzdHlsZWQuZGl2YFxuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xuICBmb250LXNpemU6IDAuOXJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMC4yNXJlbTtcbmA7XG5cbmNvbnN0IFRlY2hJbmZvID0gc3R5bGVkLmRpdmBcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC41KTtcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xuYDtcblxuY29uc3QgUG93ZXJlZEJ5ID0gc3R5bGVkLmRpdmBcbiAgdGV4dC1hbGlnbjogcmlnaHQ7XG4gIFxuICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIH1cbmA7XG5cbmNvbnN0IFBvd2VyZWRUZXh0ID0gc3R5bGVkLmRpdmBcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC43KTtcbiAgZm9udC1zaXplOiAwLjg1cmVtO1xuICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XG5gO1xuXG5jb25zdCBUZWNoU3RhY2sgPSBzdHlsZWQuZGl2YFxuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDAuNXJlbTtcbiAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDtcbiAgXG4gIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICB9XG5gO1xuXG5jb25zdCBUZWNoSXRlbSA9IHN0eWxlZC5zcGFuYFxuICBiYWNrZ3JvdW5kOiByZ2JhKDc0LCAxNDQsIDIyNiwgMC4xKTtcbiAgY29sb3I6ICM0YTkwZTI7XG4gIHBhZGRpbmc6IDAuMnJlbSAwLjVyZW07XG4gIGJvcmRlci1yYWRpdXM6IDEwcHg7XG4gIGZvbnQtc2l6ZTogMC43cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuYDtcblxuZXhwb3J0IGRlZmF1bHQgRm9vdGVyO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwibW90aW9uIiwic3R5bGVkIiwiRm9vdGVyIiwib25TdWJzY3JpYmUiLCJlbWFpbCIsInNldEVtYWlsIiwidXNlU3RhdGUiLCJoYW5kbGVTdWJzY3JpYmUiLCJlIiwicHJldmVudERlZmF1bHQiLCJzb2NpYWxMaW5rcyIsIm5hbWUiLCJpY29uIiwidXJsIiwiY29sb3IiLCJxdWlja0xpbmtzIiwibGVnYWxMaW5rcyIsIkZvb3RlckNvbnRhaW5lciIsIkZvb3RlckNvbnRlbnQiLCJGb290ZXJTZWN0aW9uIiwiZGl2IiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ5Iiwid2hpbGVJblZpZXciLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJ2aWV3cG9ydCIsIm9uY2UiLCJCcmFuZFNlY3Rpb24iLCJMb2dvIiwiTG9nb0ljb24iLCJMb2dvVGV4dCIsIkxvZ29UaXRsZSIsIkxvZ29WZXJzaW9uIiwiQnJhbmREZXNjcmlwdGlvbiIsIlRlY2hCYWRnZXMiLCJUZWNoQmFkZ2UiLCJkZWxheSIsIlNlY3Rpb25UaXRsZSIsIkxpbmtzTGlzdCIsIm1hcCIsImxpbmsiLCJpbmRleCIsIkxpbmtJdGVtIiwiaHJlZiIsIndoaWxlSG92ZXIiLCJ4IiwiU29jaWFsTGlua3MiLCJzb2NpYWwiLCJTb2NpYWxMaW5rIiwidGFyZ2V0IiwicmVsIiwic2NhbGUiLCJ3aGlsZVRhcCIsIlNvY2lhbEljb24iLCJTb2NpYWxOYW1lIiwiQ29tbXVuaXR5U3RhdHMiLCJTdGF0SXRlbSIsIlN0YXRWYWx1ZSIsIlN0YXRMYWJlbCIsIk5ld3NsZXR0ZXJEZXNjcmlwdGlvbiIsIk5ld3NsZXR0ZXJGb3JtIiwib25TdWJtaXQiLCJFbWFpbElucHV0IiwidHlwZSIsInBsYWNlaG9sZGVyIiwidmFsdWUiLCJvbkNoYW5nZSIsInJlcXVpcmVkIiwiU3Vic2NyaWJlQnV0dG9uIiwiTmV3c2xldHRlckJlbmVmaXRzIiwiQmVuZWZpdEl0ZW0iLCJGb290ZXJCb3R0b20iLCJCb3R0b21Db250ZW50IiwiTGVnYWxMaW5rcyIsIkxlZ2FsTGluayIsIkNvcHlyaWdodCIsIkNvcHlyaWdodFRleHQiLCJUZWNoSW5mbyIsIlBvd2VyZWRCeSIsIlBvd2VyZWRUZXh0IiwiVGVjaFN0YWNrIiwiVGVjaEl0ZW0iLCJmb290ZXIiLCJwIiwic3BhbiIsImg0IiwiYSIsInByb3BzIiwiZm9ybSIsImlucHV0IiwiYnV0dG9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/Footer.tsx\n"));

/***/ })

}]);