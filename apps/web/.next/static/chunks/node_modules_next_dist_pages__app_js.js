"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_next_dist_pages__app_js"],{

/***/ "../../node_modules/next/dist/pages/_app.js":
/*!**************************************************!*\
  !*** ../../node_modules/next/dist/pages/_app.js ***!
  \**************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n  enumerable: true,\n  get: function () {\n    return App;\n  }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"../../node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"../../node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/_interop_require_default._(__webpack_require__(/*! react */ \"../../node_modules/react/index.js\"));\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"../../node_modules/next/dist/shared/lib/utils.js\");\n/**\n * `App` component is used for initialize of pages. It allows for overwriting and full control of the `page` initialization.\n * This allows for keeping state between navigation, custom error handling, injecting additional data.\n */\nasync function appGetInitialProps(param) {\n  let {\n    Component,\n    ctx\n  } = param;\n  const pageProps = await (0, _utils.loadGetInitialProps)(Component, ctx);\n  return {\n    pageProps\n  };\n}\nclass App extends _react.default.Component {\n  render() {\n    const {\n      Component,\n      pageProps\n    } = this.props;\n    return /*#__PURE__*/(0, _jsxruntime.jsx)(Component, {\n      ...pageProps\n    });\n  }\n}\nApp.origGetInitialProps = appGetInitialProps;\nApp.getInitialProps = appGetInitialProps;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n  Object.defineProperty(exports.default, '__esModule', {\n    value: true\n  });\n  Object.assign(exports.default, exports);\n  module.exports = exports.default;\n}\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/pages/_app.js\n"));

/***/ })

}]);