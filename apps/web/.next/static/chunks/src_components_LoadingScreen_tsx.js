"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_LoadingScreen_tsx"],{

/***/ "./src/components/LoadingScreen.tsx":
/*!******************************************!*\
  !*** ./src/components/LoadingScreen.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingScreen: function() { return /* binding */ LoadingScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst LoadingScreen = (param)=>{\n    let { message = \"Инициализация квантовых систем...\" } = param;\n    _s();\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentMessage, setCurrentMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(message);\n    const loadingMessages = [\n        \"Подключение к квантовым источникам...\",\n        \"Инициализация эмоционального ИИ...\",\n        \"Загрузка 3D метавселенной...\",\n        \"Настройка блокчейн соединения...\",\n        \"Активация предиктивной аналитики...\",\n        \"Запуск систем безопасности...\",\n        \"Финализация загрузки...\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            setProgress((prev)=>{\n                const newProgress = prev + Math.random() * 15;\n                if (newProgress >= 100) {\n                    clearInterval(interval);\n                    return 100;\n                }\n                // Обновляем сообщение в зависимости от прогресса\n                const messageIndex = Math.floor(newProgress / 100 * loadingMessages.length);\n                if (messageIndex < loadingMessages.length) {\n                    setCurrentMessage(loadingMessages[messageIndex]);\n                }\n                return newProgress;\n            });\n        }, 200);\n        return ()=>clearInterval(interval);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingContainer, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ParticlesBackground, {\n                children: Array.from({\n                    length: 50\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Particle, {\n                        style: {\n                            left: \"\".concat(Math.random() * 100, \"%\"),\n                            top: \"\".concat(Math.random() * 100, \"%\"),\n                            animationDelay: \"\".concat(Math.random() * 3, \"s\"),\n                            animationDuration: \"\".concat(3 + Math.random() * 4, \"s\")\n                        }\n                    }, i, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentWrapper, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogoContainer, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                animate: {\n                                    rotate: 360,\n                                    scale: [\n                                        1,\n                                        1.1,\n                                        1\n                                    ]\n                                },\n                                transition: {\n                                    rotate: {\n                                        duration: 4,\n                                        repeat: Infinity,\n                                        ease: \"linear\"\n                                    },\n                                    scale: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    }\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Logo, {\n                                    children: \"\\uD83C\\uDFAE\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h1, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.5\n                                },\n                                children: \"Козырь Мастер 4.0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuantumIndicator, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuantumRing, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    animate: {\n                                        rotate: 360\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity,\n                                        ease: \"linear\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuantumOrb, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuantumText, {\n                                children: \"Квантовая инициализация\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgressContainer, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgressBar, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        width: 0\n                                    },\n                                    animate: {\n                                        width: \"\".concat(progress, \"%\")\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    style: {\n                                        height: \"100%\",\n                                        background: \"linear-gradient(90deg, #4a90e2, #7b68ee, #9370db)\",\n                                        borderRadius: \"10px\",\n                                        position: \"relative\",\n                                        overflow: \"hidden\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgressGlow, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgressText, {\n                                children: [\n                                    Math.round(progress),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingMessage, {\n                            children: currentMessage\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined)\n                    }, currentMessage, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SystemIndicators, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SystemIndicator, {\n                                active: progress > 20,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IndicatorDot, {\n                                        active: progress > 20\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Квантовый движок\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SystemIndicator, {\n                                active: progress > 40,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IndicatorDot, {\n                                        active: progress > 40\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Эмоциональный ИИ\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SystemIndicator, {\n                                active: progress > 60,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IndicatorDot, {\n                                        active: progress > 60\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Метавселенная\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SystemIndicator, {\n                                active: progress > 80,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IndicatorDot, {\n                                        active: progress > 80\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Web3 экосистема\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechDetails, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechItem, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechLabel, {\n                                        children: \"Энтропия:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechValue, {\n                                        children: (progress / 100 * 0.999).toFixed(3)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechItem, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechLabel, {\n                                        children: \"Квантовые источники:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechValue, {\n                                        children: [\n                                            Math.min(5, Math.floor(progress / 20)),\n                                            \"/5\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechItem, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechLabel, {\n                                        children: \"ИИ модели:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechValue, {\n                                        children: [\n                                            Math.min(6, Math.floor(progress / 16)),\n                                            \"/6\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LoadingScreen, \"Q/zLV6iY06zjgKH0+W4MtuICA18=\");\n_c = LoadingScreen;\n// Стилизованные компоненты\nconst LoadingContainer = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__LoadingContainer\",\n    componentId: \"sc-52bb7eb-0\"\n})([\n    \"position:fixed;top:0;left:0;width:100vw;height:100vh;background:linear-gradient(135deg,#0c0c0c 0%,#1a1a2e 50%,#16213e 100%);display:flex;align-items:center;justify-content:center;z-index:9999;overflow:hidden;\"\n]);\n_c1 = LoadingContainer;\nconst ParticlesBackground = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__ParticlesBackground\",\n    componentId: \"sc-52bb7eb-1\"\n})([\n    \"position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none;\"\n]);\n_c2 = ParticlesBackground;\nconst Particle = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__Particle\",\n    componentId: \"sc-52bb7eb-2\"\n})([\n    \"position:absolute;width:2px;height:2px;background:rgba(74,144,226,0.6);border-radius:50%;animation:float linear infinite;@keyframes float{0%{transform:translateY(100vh) scale(0);opacity:0;}10%{opacity:1;}90%{opacity:1;}100%{transform:translateY(-100px) scale(1);opacity:0;}}\"\n]);\n_c3 = Particle;\nconst ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__ContentWrapper\",\n    componentId: \"sc-52bb7eb-3\"\n})([\n    \"text-align:center;color:white;z-index:1;\"\n]);\n_c4 = ContentWrapper;\nconst LogoContainer = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__LogoContainer\",\n    componentId: \"sc-52bb7eb-4\"\n})([\n    \"margin-bottom:3rem;h1{font-size:2.5rem;font-weight:700;background:linear-gradient(45deg,#4a90e2,#7b68ee,#9370db);-webkit-background-clip:text;-webkit-text-fill-color:transparent;margin-top:1rem;}\"\n]);\n_c5 = LogoContainer;\nconst Logo = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__Logo\",\n    componentId: \"sc-52bb7eb-5\"\n})([\n    \"font-size:4rem;margin-bottom:1rem;filter:drop-shadow(0 0 20px rgba(74,144,226,0.5));\"\n]);\n_c6 = Logo;\nconst QuantumIndicator = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__QuantumIndicator\",\n    componentId: \"sc-52bb7eb-6\"\n})([\n    \"margin-bottom:3rem;display:flex;flex-direction:column;align-items:center;\"\n]);\n_c7 = QuantumIndicator;\nconst QuantumRing = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__QuantumRing\",\n    componentId: \"sc-52bb7eb-7\"\n})([\n    \"width:80px;height:80px;border:2px solid rgba(74,144,226,0.3);border-radius:50%;display:flex;align-items:center;justify-content:center;position:relative;margin-bottom:1rem;&::before{content:'';position:absolute;top:-2px;left:-2px;right:-2px;bottom:-2px;border-radius:50%;background:conic-gradient(from 0deg,transparent,#4a90e2,transparent);animation:spin 2s linear infinite;}@keyframes spin{to{transform:rotate(360deg);}}\"\n]);\n_c8 = QuantumRing;\nconst QuantumOrb = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__QuantumOrb\",\n    componentId: \"sc-52bb7eb-8\"\n})([\n    \"width:20px;height:20px;background:radial-gradient(circle,#4a90e2,#7b68ee);border-radius:50%;box-shadow:0 0 20px rgba(74,144,226,0.8);z-index:1;\"\n]);\n_c9 = QuantumOrb;\nconst QuantumText = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__QuantumText\",\n    componentId: \"sc-52bb7eb-9\"\n})([\n    \"font-size:0.9rem;color:rgba(255,255,255,0.7);\"\n]);\n_c10 = QuantumText;\nconst ProgressContainer = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__ProgressContainer\",\n    componentId: \"sc-52bb7eb-10\"\n})([\n    \"width:400px;margin-bottom:2rem;@media (max-width:480px){width:300px;}\"\n]);\n_c11 = ProgressContainer;\nconst ProgressBar = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__ProgressBar\",\n    componentId: \"sc-52bb7eb-11\"\n})([\n    \"width:100%;height:8px;background:rgba(255,255,255,0.1);border-radius:10px;overflow:hidden;margin-bottom:0.5rem;position:relative;\"\n]);\n_c12 = ProgressBar;\nconst ProgressGlow = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__ProgressGlow\",\n    componentId: \"sc-52bb7eb-12\"\n})([\n    \"position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.3),transparent);animation:shimmer 2s infinite;@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}\"\n]);\n_c13 = ProgressGlow;\nconst ProgressText = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__ProgressText\",\n    componentId: \"sc-52bb7eb-13\"\n})([\n    \"font-size:1.2rem;font-weight:600;color:#4a90e2;\"\n]);\n_c14 = ProgressText;\nconst LoadingMessage = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__LoadingMessage\",\n    componentId: \"sc-52bb7eb-14\"\n})([\n    \"font-size:1.1rem;color:rgba(255,255,255,0.8);margin-bottom:2rem;min-height:1.5rem;\"\n]);\n_c15 = LoadingMessage;\nconst SystemIndicators = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__SystemIndicators\",\n    componentId: \"sc-52bb7eb-15\"\n})([\n    \"display:grid;grid-template-columns:repeat(2,1fr);gap:1rem;margin-bottom:2rem;max-width:400px;@media (max-width:480px){grid-template-columns:1fr;}\"\n]);\n_c16 = SystemIndicators;\nconst SystemIndicator = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__SystemIndicator\",\n    componentId: \"sc-52bb7eb-16\"\n})([\n    \"display:flex;align-items:center;gap:0.5rem;font-size:0.9rem;color:\",\n    \";transition:color 0.3s ease;\"\n], (props)=>props.active ? \"#4a90e2\" : \"rgba(255, 255, 255, 0.5)\");\n_c17 = SystemIndicator;\nconst IndicatorDot = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__IndicatorDot\",\n    componentId: \"sc-52bb7eb-17\"\n})([\n    \"width:8px;height:8px;border-radius:50%;background:\",\n    \";box-shadow:\",\n    \";transition:all 0.3s ease;\"\n], (props)=>props.active ? \"#4a90e2\" : \"rgba(255, 255, 255, 0.3)\", (props)=>props.active ? \"0 0 10px rgba(74, 144, 226, 0.5)\" : \"none\");\n_c18 = IndicatorDot;\nconst TechDetails = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__TechDetails\",\n    componentId: \"sc-52bb7eb-18\"\n})([\n    \"display:flex;justify-content:center;gap:2rem;font-size:0.8rem;@media (max-width:480px){flex-direction:column;gap:0.5rem;}\"\n]);\n_c19 = TechDetails;\nconst TechItem = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"LoadingScreen__TechItem\",\n    componentId: \"sc-52bb7eb-19\"\n})([\n    \"display:flex;gap:0.5rem;\"\n]);\n_c20 = TechItem;\nconst TechLabel = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].span.withConfig({\n    displayName: \"LoadingScreen__TechLabel\",\n    componentId: \"sc-52bb7eb-20\"\n})([\n    \"color:rgba(255,255,255,0.6);\"\n]);\n_c21 = TechLabel;\nconst TechValue = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].span.withConfig({\n    displayName: \"LoadingScreen__TechValue\",\n    componentId: \"sc-52bb7eb-21\"\n})([\n    \"color:#4a90e2;font-weight:600;\"\n]);\n_c22 = TechValue;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22;\n$RefreshReg$(_c, \"LoadingScreen\");\n$RefreshReg$(_c1, \"LoadingContainer\");\n$RefreshReg$(_c2, \"ParticlesBackground\");\n$RefreshReg$(_c3, \"Particle\");\n$RefreshReg$(_c4, \"ContentWrapper\");\n$RefreshReg$(_c5, \"LogoContainer\");\n$RefreshReg$(_c6, \"Logo\");\n$RefreshReg$(_c7, \"QuantumIndicator\");\n$RefreshReg$(_c8, \"QuantumRing\");\n$RefreshReg$(_c9, \"QuantumOrb\");\n$RefreshReg$(_c10, \"QuantumText\");\n$RefreshReg$(_c11, \"ProgressContainer\");\n$RefreshReg$(_c12, \"ProgressBar\");\n$RefreshReg$(_c13, \"ProgressGlow\");\n$RefreshReg$(_c14, \"ProgressText\");\n$RefreshReg$(_c15, \"LoadingMessage\");\n$RefreshReg$(_c16, \"SystemIndicators\");\n$RefreshReg$(_c17, \"SystemIndicator\");\n$RefreshReg$(_c18, \"IndicatorDot\");\n$RefreshReg$(_c19, \"TechDetails\");\n$RefreshReg$(_c20, \"TechItem\");\n$RefreshReg$(_c21, \"TechLabel\");\n$RefreshReg$(_c22, \"TechValue\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/LoadingScreen.tsx\n"));

/***/ })

}]);