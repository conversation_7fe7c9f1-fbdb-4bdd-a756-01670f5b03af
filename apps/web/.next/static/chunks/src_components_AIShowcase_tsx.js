"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_AIShowcase_tsx"],{

/***/ "./src/components/AIShowcase.tsx":
/*!***************************************!*\
  !*** ./src/components/AIShowcase.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst emotionalMetrics = [\n    {\n        name: \"Счастье\",\n        value: 0.85,\n        color: \"#4ade80\",\n        icon: \"\\uD83D\\uDE0A\",\n        description: \"Уровень позитивных эмоций игрока\"\n    },\n    {\n        name: \"Фокус\",\n        value: 0.92,\n        color: \"#3b82f6\",\n        icon: \"\\uD83C\\uDFAF\",\n        description: \"Концентрация и внимание к игре\"\n    },\n    {\n        name: \"Уверенность\",\n        value: 0.78,\n        color: \"#8b5cf6\",\n        icon: \"\\uD83D\\uDCAA\",\n        description: \"Уверенность в принятии решений\"\n    },\n    {\n        name: \"Стресс\",\n        value: 0.23,\n        color: \"#ef4444\",\n        icon: \"\\uD83D\\uDE30\",\n        description: \"Уровень стресса и напряжения\"\n    },\n    {\n        name: \"Мотивация\",\n        value: 0.89,\n        color: \"#f59e0b\",\n        icon: \"\\uD83D\\uDD25\",\n        description: \"Желание продолжать игру\"\n    },\n    {\n        name: \"Усталость\",\n        value: 0.31,\n        color: \"#6b7280\",\n        icon: \"\\uD83D\\uDE34\",\n        description: \"Уровень усталости игрока\"\n    }\n];\nconst aiFeatures = [\n    {\n        title: \"Анализ эмоций в реальном времени\",\n        description: \"ИИ анализирует микровыражения, тон голоса и поведенческие паттерны\",\n        icon: \"\\uD83E\\uDDE0\",\n        metrics: [\n            \"Точность: 95%\",\n            \"Задержка: <100мс\",\n            \"Источников данных: 8\"\n        ]\n    },\n    {\n        title: \"Предиктивная аналитика\",\n        description: \"Предсказание следующих ходов и вероятности победы\",\n        icon: \"\\uD83D\\uDD2E\",\n        metrics: [\n            \"Точность предсказаний: 85%\",\n            \"Анализируемых факторов: 50+\",\n            \"Обновлений/сек: 1000+\"\n        ]\n    },\n    {\n        title: \"Персонализированное обучение\",\n        description: \"Адаптивные уроки и рекомендации на основе стиля игры\",\n        icon: \"\\uD83D\\uDCDA\",\n        metrics: [\n            \"Персональных путей: ∞\",\n            \"Адаптация: в реальном времени\",\n            \"Эффективность: +300%\"\n        ]\n    },\n    {\n        title: \"Детекция тильта и выгорания\",\n        description: \"Раннее обнаружение негативных состояний и превентивные меры\",\n        icon: \"\\uD83D\\uDEE1️\",\n        metrics: [\n            \"Точность детекции: 99%\",\n            \"Время реакции: <1сек\",\n            \"Предотвращённых тильтов: 10K+\"\n        ]\n    }\n];\nconst AIShowcase = (param)=>{\n    let { emotionalState } = param;\n    _s();\n    const [activeFeature, setActiveFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentMetrics, setCurrentMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(emotionalMetrics);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Симуляция обновления метрик в реальном времени\n        const interval = setInterval(()=>{\n            setCurrentMetrics((prev)=>prev.map((metric)=>({\n                        ...metric,\n                        value: Math.max(0, Math.min(1, metric.value + (Math.random() - 0.5) * 0.1))\n                    })));\n        }, 2000);\n        return ()=>clearInterval(interval);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Автоматическое переключение функций\n        const interval = setInterval(()=>{\n            setActiveFeature((prev)=>(prev + 1) % aiFeatures.length);\n        }, 4000);\n        return ()=>clearInterval(interval);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIContainer, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentWrapper, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionTitle, {\n                            children: \"Эмоциональный ИИ\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSubtitle, {\n                            children: \"Революционная система понимания и анализа игроков\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MainContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricsPanel, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PanelHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PanelTitle, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalysisIndicator, {\n                                                    active: isAnalyzing\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Анализ в реальном времени\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PanelSubtitle, {\n                                            children: \"ИИ анализирует 8 эмоциональных состояний одновременно\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricsGrid, {\n                                    children: currentMetrics.map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.8\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                scale: 1\n                                            },\n                                            transition: {\n                                                delay: index * 0.1\n                                            },\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricIcon, {\n                                                            children: metric.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricName, {\n                                                            children: metric.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricValue, {\n                                                    color: metric.color,\n                                                    children: [\n                                                        (metric.value * 100).toFixed(0),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricBar, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricProgress, {\n                                                        color: metric.color,\n                                                        width: metric.value * 100,\n                                                        initial: {\n                                                            width: 0\n                                                        },\n                                                        animate: {\n                                                            width: \"\".concat(metric.value * 100, \"%\")\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricDescription, {\n                                                    children: metric.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, metric.name, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesPanel, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureSelector, {\n                                    children: aiFeatures.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureTab, {\n                                            active: index === activeFeature,\n                                            onClick: ()=>setActiveFeature(index),\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureTabIcon, {\n                                                    children: feature.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureTabTitle, {\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 50\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -50\n                                            },\n                                            transition: {\n                                                duration: 0.5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureIcon, {\n                                                            children: aiFeatures[activeFeature].icon\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureTitle, {\n                                                                    children: aiFeatures[activeFeature].title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureDescription, {\n                                                                    children: aiFeatures[activeFeature].description\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureMetrics, {\n                                                    children: aiFeatures[activeFeature].metrics.map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureMetric, {\n                                                            initial: {\n                                                                opacity: 0,\n                                                                y: 20\n                                                            },\n                                                            animate: {\n                                                                opacity: 1,\n                                                                y: 0\n                                                            },\n                                                            transition: {\n                                                                delay: index * 0.1\n                                                            },\n                                                            children: [\n                                                                \"✨ \",\n                                                                metric\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, activeFeature, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoSection, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoTitle, {\n                            children: \"Попробуйте ИИ в действии\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoGrid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCard, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoIcon, {\n                                            children: \"\\uD83C\\uDFAE\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCardTitle, {\n                                            children: \"Игровая сессия\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCardDescription, {\n                                            children: \"Начните игру и наблюдайте за анализом ИИ\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCard, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoIcon, {\n                                            children: \"\\uD83D\\uDCCA\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCardTitle, {\n                                            children: \"Аналитика\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCardDescription, {\n                                            children: \"Изучите детальную аналитику своей игры\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCard, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoIcon, {\n                                            children: \"\\uD83C\\uDFAF\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCardTitle, {\n                                            children: \"Персонализация\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCardDescription, {\n                                            children: \"Получите персональные рекомендации\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AIShowcase, \"FjnxRK5S3iqx0fF16F3gEj++vRk=\");\n_c = AIShowcase;\n// Стилизованные компоненты\nconst AIContainer = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__AIContainer\",\n    componentId: \"sc-ce6ca944-0\"\n})([\n    \"min-height:100vh;background:linear-gradient(135deg,#1a1a2e 0%,#16213e 50%,#0f0f23 100%);padding:4rem 2rem;display:flex;align-items:center;justify-content:center;\"\n]);\n_c1 = AIContainer;\nconst ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__ContentWrapper\",\n    componentId: \"sc-ce6ca944-1\"\n})([\n    \"max-width:1400px;width:100%;\"\n]);\n_c2 = ContentWrapper;\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h2.withConfig({\n    displayName: \"AIShowcase__SectionTitle\",\n    componentId: \"sc-ce6ca944-2\"\n})([\n    \"font-size:3.5rem;font-weight:900;text-align:center;margin-bottom:1rem;background:linear-gradient(45deg,#4a90e2,#7b68ee,#9370db);-webkit-background-clip:text;-webkit-text-fill-color:transparent;@media (max-width:768px){font-size:2.5rem;}\"\n]);\n_c3 = SectionTitle;\nconst SectionSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p.withConfig({\n    displayName: \"AIShowcase__SectionSubtitle\",\n    componentId: \"sc-ce6ca944-3\"\n})([\n    \"font-size:1.3rem;color:rgba(255,255,255,0.7);text-align:center;margin-bottom:4rem;max-width:800px;margin-left:auto;margin-right:auto;\"\n]);\n_c4 = SectionSubtitle;\nconst MainContent = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__MainContent\",\n    componentId: \"sc-ce6ca944-4\"\n})([\n    \"display:grid;grid-template-columns:1fr 1fr;gap:3rem;margin-bottom:4rem;@media (max-width:1024px){grid-template-columns:1fr;gap:2rem;}\"\n]);\n_c5 = MainContent;\nconst MetricsPanel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__MetricsPanel\",\n    componentId: \"sc-ce6ca944-5\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:20px;padding:2rem;backdrop-filter:blur(20px);border:1px solid rgba(255,255,255,0.1);\"\n]);\n_c6 = MetricsPanel;\nconst PanelHeader = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__PanelHeader\",\n    componentId: \"sc-ce6ca944-6\"\n})([\n    \"margin-bottom:2rem;\"\n]);\n_c7 = PanelHeader;\nconst PanelTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h3.withConfig({\n    displayName: \"AIShowcase__PanelTitle\",\n    componentId: \"sc-ce6ca944-7\"\n})([\n    \"font-size:1.5rem;font-weight:700;color:white;margin-bottom:0.5rem;display:flex;align-items:center;gap:0.5rem;\"\n]);\n_c8 = PanelTitle;\nconst PanelSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p.withConfig({\n    displayName: \"AIShowcase__PanelSubtitle\",\n    componentId: \"sc-ce6ca944-8\"\n})([\n    \"color:rgba(255,255,255,0.7);font-size:0.9rem;\"\n]);\n_c9 = PanelSubtitle;\nconst AnalysisIndicator = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__AnalysisIndicator\",\n    componentId: \"sc-ce6ca944-9\"\n})([\n    \"width:12px;height:12px;border-radius:50%;background:\",\n    \";animation:\",\n    \";\"\n], (props)=>props.active ? \"#4ade80\" : \"#6b7280\", (props)=>props.active ? \"pulse 2s infinite\" : \"none\");\n_c10 = AnalysisIndicator;\nconst MetricsGrid = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__MetricsGrid\",\n    componentId: \"sc-ce6ca944-10\"\n})([\n    \"display:grid;grid-template-columns:repeat(2,1fr);gap:1rem;@media (max-width:640px){grid-template-columns:1fr;}\"\n]);\n_c11 = MetricsGrid;\nconst MetricCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"AIShowcase__MetricCard\",\n    componentId: \"sc-ce6ca944-11\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;border:1px solid rgba(255,255,255,0.1);\"\n]);\n_c12 = MetricCard;\nconst MetricHeader = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__MetricHeader\",\n    componentId: \"sc-ce6ca944-12\"\n})([\n    \"display:flex;align-items:center;gap:0.5rem;margin-bottom:1rem;\"\n]);\n_c13 = MetricHeader;\nconst MetricIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].span.withConfig({\n    displayName: \"AIShowcase__MetricIcon\",\n    componentId: \"sc-ce6ca944-13\"\n})([\n    \"font-size:1.5rem;\"\n]);\n_c14 = MetricIcon;\nconst MetricName = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].span.withConfig({\n    displayName: \"AIShowcase__MetricName\",\n    componentId: \"sc-ce6ca944-14\"\n})([\n    \"font-weight:600;color:white;\"\n]);\n_c15 = MetricName;\nconst MetricValue = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__MetricValue\",\n    componentId: \"sc-ce6ca944-15\"\n})([\n    \"font-size:2rem;font-weight:700;color:\",\n    \";margin-bottom:0.5rem;\"\n], (props)=>props.color);\n_c16 = MetricValue;\nconst MetricBar = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__MetricBar\",\n    componentId: \"sc-ce6ca944-16\"\n})([\n    \"width:100%;height:6px;background:rgba(255,255,255,0.1);border-radius:3px;overflow:hidden;margin-bottom:0.5rem;\"\n]);\n_c17 = MetricBar;\nconst MetricProgress = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"AIShowcase__MetricProgress\",\n    componentId: \"sc-ce6ca944-17\"\n})([\n    \"height:100%;background:\",\n    \";border-radius:3px;\"\n], (props)=>props.color);\n_c18 = MetricProgress;\nconst MetricDescription = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p.withConfig({\n    displayName: \"AIShowcase__MetricDescription\",\n    componentId: \"sc-ce6ca944-18\"\n})([\n    \"font-size:0.8rem;color:rgba(255,255,255,0.6);line-height:1.4;\"\n]);\n_c19 = MetricDescription;\nconst FeaturesPanel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__FeaturesPanel\",\n    componentId: \"sc-ce6ca944-19\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:20px;padding:2rem;backdrop-filter:blur(20px);border:1px solid rgba(255,255,255,0.1);\"\n]);\n_c20 = FeaturesPanel;\nconst FeatureSelector = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__FeatureSelector\",\n    componentId: \"sc-ce6ca944-20\"\n})([\n    \"display:grid;grid-template-columns:repeat(2,1fr);gap:0.5rem;margin-bottom:2rem;@media (max-width:640px){grid-template-columns:1fr;}\"\n]);\n_c21 = FeatureSelector;\nconst FeatureTab = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"AIShowcase__FeatureTab\",\n    componentId: \"sc-ce6ca944-21\"\n})([\n    \"background:\",\n    \";border:1px solid \",\n    \";border-radius:10px;padding:1rem;color:\",\n    \";cursor:pointer;transition:all 0.3s ease;text-align:left;\"\n], (props)=>props.active ? \"rgba(74, 144, 226, 0.2)\" : \"transparent\", (props)=>props.active ? \"#4a90e2\" : \"rgba(255, 255, 255, 0.1)\", (props)=>props.active ? \"#4a90e2\" : \"rgba(255, 255, 255, 0.8)\");\n_c22 = FeatureTab;\nconst FeatureTabIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__FeatureTabIcon\",\n    componentId: \"sc-ce6ca944-22\"\n})([\n    \"font-size:1.5rem;margin-bottom:0.5rem;\"\n]);\n_c23 = FeatureTabIcon;\nconst FeatureTabTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__FeatureTabTitle\",\n    componentId: \"sc-ce6ca944-23\"\n})([\n    \"font-size:0.9rem;font-weight:600;\"\n]);\n_c24 = FeatureTabTitle;\nconst FeatureContent = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__FeatureContent\",\n    componentId: \"sc-ce6ca944-24\"\n})([\n    \"min-height:200px;\"\n]);\n_c25 = FeatureContent;\nconst FeatureHeader = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__FeatureHeader\",\n    componentId: \"sc-ce6ca944-25\"\n})([\n    \"display:flex;align-items:flex-start;gap:1rem;margin-bottom:1.5rem;\"\n]);\n_c26 = FeatureHeader;\nconst FeatureIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__FeatureIcon\",\n    componentId: \"sc-ce6ca944-26\"\n})([\n    \"font-size:3rem;color:#4a90e2;\"\n]);\n_c27 = FeatureIcon;\nconst FeatureTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h4.withConfig({\n    displayName: \"AIShowcase__FeatureTitle\",\n    componentId: \"sc-ce6ca944-27\"\n})([\n    \"font-size:1.5rem;font-weight:700;color:white;margin-bottom:0.5rem;\"\n]);\n_c28 = FeatureTitle;\nconst FeatureDescription = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p.withConfig({\n    displayName: \"AIShowcase__FeatureDescription\",\n    componentId: \"sc-ce6ca944-28\"\n})([\n    \"color:rgba(255,255,255,0.7);line-height:1.6;\"\n]);\n_c29 = FeatureDescription;\nconst FeatureMetrics = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__FeatureMetrics\",\n    componentId: \"sc-ce6ca944-29\"\n})([\n    \"display:flex;flex-direction:column;gap:0.5rem;\"\n]);\n_c30 = FeatureMetrics;\nconst FeatureMetric = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"AIShowcase__FeatureMetric\",\n    componentId: \"sc-ce6ca944-30\"\n})([\n    \"color:rgba(255,255,255,0.8);font-size:0.9rem;\"\n]);\n_c31 = FeatureMetric;\nconst DemoSection = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__DemoSection\",\n    componentId: \"sc-ce6ca944-31\"\n})([\n    \"text-align:center;\"\n]);\n_c32 = DemoSection;\nconst DemoTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h3.withConfig({\n    displayName: \"AIShowcase__DemoTitle\",\n    componentId: \"sc-ce6ca944-32\"\n})([\n    \"font-size:2rem;font-weight:700;color:white;margin-bottom:2rem;\"\n]);\n_c33 = DemoTitle;\nconst DemoGrid = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__DemoGrid\",\n    componentId: \"sc-ce6ca944-33\"\n})([\n    \"display:grid;grid-template-columns:repeat(3,1fr);gap:2rem;@media (max-width:768px){grid-template-columns:1fr;}\"\n]);\n_c34 = DemoGrid;\nconst DemoCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"AIShowcase__DemoCard\",\n    componentId: \"sc-ce6ca944-34\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;padding:2rem;text-align:center;cursor:pointer;border:1px solid rgba(255,255,255,0.1);transition:all 0.3s ease;&:hover{background:rgba(255,255,255,0.1);}\"\n]);\n_c35 = DemoCard;\nconst DemoIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"AIShowcase__DemoIcon\",\n    componentId: \"sc-ce6ca944-35\"\n})([\n    \"font-size:3rem;margin-bottom:1rem;\"\n]);\n_c36 = DemoIcon;\nconst DemoCardTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h4.withConfig({\n    displayName: \"AIShowcase__DemoCardTitle\",\n    componentId: \"sc-ce6ca944-36\"\n})([\n    \"font-size:1.2rem;font-weight:600;color:white;margin-bottom:0.5rem;\"\n]);\n_c37 = DemoCardTitle;\nconst DemoCardDescription = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p.withConfig({\n    displayName: \"AIShowcase__DemoCardDescription\",\n    componentId: \"sc-ce6ca944-37\"\n})([\n    \"color:rgba(255,255,255,0.7);font-size:0.9rem;line-height:1.5;\"\n]);\n_c38 = DemoCardDescription;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AIShowcase);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38;\n$RefreshReg$(_c, \"AIShowcase\");\n$RefreshReg$(_c1, \"AIContainer\");\n$RefreshReg$(_c2, \"ContentWrapper\");\n$RefreshReg$(_c3, \"SectionTitle\");\n$RefreshReg$(_c4, \"SectionSubtitle\");\n$RefreshReg$(_c5, \"MainContent\");\n$RefreshReg$(_c6, \"MetricsPanel\");\n$RefreshReg$(_c7, \"PanelHeader\");\n$RefreshReg$(_c8, \"PanelTitle\");\n$RefreshReg$(_c9, \"PanelSubtitle\");\n$RefreshReg$(_c10, \"AnalysisIndicator\");\n$RefreshReg$(_c11, \"MetricsGrid\");\n$RefreshReg$(_c12, \"MetricCard\");\n$RefreshReg$(_c13, \"MetricHeader\");\n$RefreshReg$(_c14, \"MetricIcon\");\n$RefreshReg$(_c15, \"MetricName\");\n$RefreshReg$(_c16, \"MetricValue\");\n$RefreshReg$(_c17, \"MetricBar\");\n$RefreshReg$(_c18, \"MetricProgress\");\n$RefreshReg$(_c19, \"MetricDescription\");\n$RefreshReg$(_c20, \"FeaturesPanel\");\n$RefreshReg$(_c21, \"FeatureSelector\");\n$RefreshReg$(_c22, \"FeatureTab\");\n$RefreshReg$(_c23, \"FeatureTabIcon\");\n$RefreshReg$(_c24, \"FeatureTabTitle\");\n$RefreshReg$(_c25, \"FeatureContent\");\n$RefreshReg$(_c26, \"FeatureHeader\");\n$RefreshReg$(_c27, \"FeatureIcon\");\n$RefreshReg$(_c28, \"FeatureTitle\");\n$RefreshReg$(_c29, \"FeatureDescription\");\n$RefreshReg$(_c30, \"FeatureMetrics\");\n$RefreshReg$(_c31, \"FeatureMetric\");\n$RefreshReg$(_c32, \"DemoSection\");\n$RefreshReg$(_c33, \"DemoTitle\");\n$RefreshReg$(_c34, \"DemoGrid\");\n$RefreshReg$(_c35, \"DemoCard\");\n$RefreshReg$(_c36, \"DemoIcon\");\n$RefreshReg$(_c37, \"DemoCardTitle\");\n$RefreshReg$(_c38, \"DemoCardDescription\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/AIShowcase.tsx\n"));

/***/ })

}]);