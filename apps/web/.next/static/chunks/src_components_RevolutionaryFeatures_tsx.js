"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_RevolutionaryFeatures_tsx"],{

/***/ "./src/components/RevolutionaryFeatures.tsx":
/*!**************************************************!*\
  !*** ./src/components/RevolutionaryFeatures.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst features = [\n    {\n        id: \"quantum\",\n        title: \"Квантовый игровой движок\",\n        subtitle: \"Истинная случайность из квантовых источников\",\n        description: \"Первая в мире игровая платформа, использующая реальные квантовые источники случайности для абсолютной честности игр.\",\n        icon: \"⚛️\",\n        color: \"#4a90e2\",\n        stats: [\n            {\n                label: \"Энтропия\",\n                value: \"99.99%\"\n            },\n            {\n                label: \"Квантовых источников\",\n                value: \"5\"\n            },\n            {\n                label: \"Тестов качества\",\n                value: \"15+\"\n            }\n        ],\n        technologies: [\n            \"ANU Quantum RNG\",\n            \"ID Quantique\",\n            \"PicoQuant\",\n            \"Quantum Dice\",\n            \"NIST Tests\"\n        ],\n        benefits: [\n            \"Абсолютная честность\",\n            \"Невозможность предсказания\",\n            \"Научная валидация\"\n        ]\n    },\n    {\n        id: \"ai\",\n        title: \"Эмоциональный ИИ\",\n        subtitle: \"Глубокое понимание каждого игрока\",\n        description: \"Революционная система искусственного интеллекта, анализирующая эмоциональное состояние и персонализирующая игровой опыт.\",\n        icon: \"\\uD83E\\uDDE0\",\n        color: \"#7b68ee\",\n        stats: [\n            {\n                label: \"Эмоциональных состояний\",\n                value: \"8\"\n            },\n            {\n                label: \"Точность анализа\",\n                value: \"95%\"\n            },\n            {\n                label: \"ML моделей\",\n                value: \"6\"\n            }\n        ],\n        technologies: [\n            \"TensorFlow\",\n            \"OpenAI GPT-4\",\n            \"Computer Vision\",\n            \"NLP\",\n            \"Behavioral Analysis\"\n        ],\n        benefits: [\n            \"Персонализация\",\n            \"Предотвращение тильта\",\n            \"Адаптивное обучение\"\n        ]\n    },\n    {\n        id: \"metaverse\",\n        title: \"3D Метавселенная\",\n        subtitle: \"Иммерсивные игровые миры\",\n        description: \"Полноценная метавселенная с 3D мирами, VR/AR поддержкой и социальными пространствами для революционного игрового опыта.\",\n        icon: \"\\uD83C\\uDF0D\",\n        color: \"#9370db\",\n        stats: [\n            {\n                label: \"Тематических миров\",\n                value: \"6\"\n            },\n            {\n                label: \"Одновременных игроков\",\n                value: \"10K+\"\n            },\n            {\n                label: \"VR/AR поддержка\",\n                value: \"100%\"\n            }\n        ],\n        technologies: [\n            \"Three.js\",\n            \"WebXR\",\n            \"WebGL\",\n            \"Physics Engine\",\n            \"Spatial Audio\"\n        ],\n        benefits: [\n            \"Иммерсивность\",\n            \"Социальное взаимодействие\",\n            \"Новый опыт\"\n        ]\n    },\n    {\n        id: \"security\",\n        title: \"Квантовая безопасность\",\n        subtitle: \"Непробиваемая защита\",\n        description: \"Передовые системы безопасности с квантовым шифрованием, биометрией и ИИ-детекцией угроз.\",\n        icon: \"\\uD83D\\uDEE1️\",\n        color: \"#ff6b6b\",\n        stats: [\n            {\n                label: \"Точность детекции\",\n                value: \"99.9%\"\n            },\n            {\n                label: \"Типов угроз\",\n                value: \"6\"\n            },\n            {\n                label: \"Время реакции\",\n                value: \"<1с\"\n            }\n        ],\n        technologies: [\n            \"Post-Quantum Crypto\",\n            \"Zero-Knowledge Proofs\",\n            \"Biometrics\",\n            \"AI Detection\"\n        ],\n        benefits: [\n            \"Абсолютная защита\",\n            \"Приватность\",\n            \"Доверие\"\n        ]\n    },\n    {\n        id: \"analytics\",\n        title: \"Предиктивная аналитика\",\n        subtitle: \"ИИ предсказывает будущее\",\n        description: \"Мощная система машинного обучения, предсказывающая игровые события и персонализирующая опыт.\",\n        icon: \"\\uD83D\\uDCCA\",\n        color: \"#4ecdc4\",\n        stats: [\n            {\n                label: \"Точность предсказаний\",\n                value: \"85%\"\n            },\n            {\n                label: \"Анализируемых метрик\",\n                value: \"100+\"\n            },\n            {\n                label: \"Обновлений в секунду\",\n                value: \"1000+\"\n            }\n        ],\n        technologies: [\n            \"Machine Learning\",\n            \"Real-time Analytics\",\n            \"Predictive Models\",\n            \"Big Data\"\n        ],\n        benefits: [\n            \"Персонализация\",\n            \"Оптимизация\",\n            \"Инсайты\"\n        ]\n    },\n    {\n        id: \"web3\",\n        title: \"Web3 экосистема\",\n        subtitle: \"Децентрализованное будущее\",\n        description: \"Полная интеграция с блокчейном, NFT картами, DeFi протоколами и DAO управлением.\",\n        icon: \"⛓️\",\n        color: \"#feca57\",\n        stats: [\n            {\n                label: \"Смарт-контрактов\",\n                value: \"6\"\n            },\n            {\n                label: \"Поддерживаемых сетей\",\n                value: \"5+\"\n            },\n            {\n                label: \"NFT коллекций\",\n                value: \"10+\"\n            }\n        ],\n        technologies: [\n            \"Ethereum\",\n            \"Polygon\",\n            \"IPFS\",\n            \"Smart Contracts\",\n            \"DeFi\"\n        ],\n        benefits: [\n            \"Владение активами\",\n            \"Децентрализация\",\n            \"Новая экономика\"\n        ]\n    }\n];\nconst RevolutionaryFeatures = (param)=>{\n    let { currentSection } = param;\n    _s();\n    const [activeFeature, setActiveFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hoveredFeature, setHoveredFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            setActiveFeature((prev)=>(prev + 1) % features.length);\n        }, 5000);\n        return ()=>clearInterval(interval);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesContainer, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentWrapper, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionTitle, {\n                            children: \"Революционные технологии\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSubtitle, {\n                            children: \"Мы объединили передовые технологии будущего в одной платформе\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesGrid, {\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                            active: index === activeFeature,\n                            hovered: hoveredFeature === feature.id,\n                            color: feature.color,\n                            onMouseEnter: ()=>setHoveredFeature(feature.id),\n                            onMouseLeave: ()=>setHoveredFeature(null),\n                            onClick: ()=>setActiveFeature(index),\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureIcon, {\n                                    active: index === activeFeature,\n                                    children: feature.icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureTitle, {\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureSubtitle, {\n                                    children: feature.subtitle\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                                    children: (index === activeFeature || hoveredFeature === feature.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureDetails, {\n                                        initial: {\n                                            opacity: 0,\n                                            height: 0\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            height: \"auto\"\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            height: 0\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureDescription, {\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsGrid, {\n                                                children: feature.stats.map((stat, statIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatItem, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                                children: stat.value\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                                children: stat.label\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, statIndex, true, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, feature.id, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedView, {\n                        initial: {\n                            opacity: 0,\n                            x: 50\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            x: -50\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedIcon, {\n                                            color: features[activeFeature].color,\n                                            children: features[activeFeature].icon\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedTitle, {\n                                                    children: features[activeFeature].title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedSubtitle, {\n                                                    children: features[activeFeature].subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedDescription, {\n                                    children: features[activeFeature].description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechnologiesSection, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionLabel, {\n                                            children: \"Технологии:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechnologiesList, {\n                                            children: features[activeFeature].technologies.map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechTag, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        scale: 0\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        scale: 1\n                                                    },\n                                                    transition: {\n                                                        delay: index * 0.1\n                                                    },\n                                                    children: tech\n                                                }, index, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BenefitsSection, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionLabel, {\n                                            children: \"Преимущества:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BenefitsList, {\n                                            children: features[activeFeature].benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BenefitItem, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: index * 0.1\n                                                    },\n                                                    children: [\n                                                        \"✨ \",\n                                                        benefit\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, undefined)\n                    }, activeFeature, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgressIndicators, {\n                    children: features.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgressDot, {\n                            active: index === activeFeature,\n                            onClick: ()=>setActiveFeature(index),\n                            whileHover: {\n                                scale: 1.2\n                            },\n                            whileTap: {\n                                scale: 0.9\n                            }\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n            lineNumber: 128,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RevolutionaryFeatures, \"oJYL2UD5jAPoC9xZxSqVom7Ddwc=\");\n_c = RevolutionaryFeatures;\n// Стилизованные компоненты\nconst FeaturesContainer = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"RevolutionaryFeatures__FeaturesContainer\",\n    componentId: \"sc-146f8b4-0\"\n})([\n    \"min-height:100vh;background:linear-gradient(135deg,#0f0f23 0%,#1a1a2e 50%,#16213e 100%);padding:4rem 2rem;display:flex;align-items:center;justify-content:center;\"\n]);\n_c1 = FeaturesContainer;\nconst ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"RevolutionaryFeatures__ContentWrapper\",\n    componentId: \"sc-146f8b4-1\"\n})([\n    \"max-width:1400px;width:100%;\"\n]);\n_c2 = ContentWrapper;\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h2.withConfig({\n    displayName: \"RevolutionaryFeatures__SectionTitle\",\n    componentId: \"sc-146f8b4-2\"\n})([\n    \"font-size:3.5rem;font-weight:900;text-align:center;margin-bottom:1rem;background:linear-gradient(45deg,#4a90e2,#7b68ee,#9370db);-webkit-background-clip:text;-webkit-text-fill-color:transparent;@media (max-width:768px){font-size:2.5rem;}\"\n]);\n_c3 = SectionTitle;\nconst SectionSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p.withConfig({\n    displayName: \"RevolutionaryFeatures__SectionSubtitle\",\n    componentId: \"sc-146f8b4-3\"\n})([\n    \"font-size:1.3rem;color:rgba(255,255,255,0.7);text-align:center;margin-bottom:4rem;max-width:800px;margin-left:auto;margin-right:auto;\"\n]);\n_c4 = SectionSubtitle;\nconst FeaturesGrid = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"RevolutionaryFeatures__FeaturesGrid\",\n    componentId: \"sc-146f8b4-4\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:2rem;margin-bottom:4rem;\"\n]);\n_c5 = FeaturesGrid;\nconst FeatureCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"RevolutionaryFeatures__FeatureCard\",\n    componentId: \"sc-146f8b4-5\"\n})([\n    \"background:\",\n    \";border:2px solid \",\n    \";border-radius:20px;padding:2rem;cursor:pointer;transition:all 0.3s ease;backdrop-filter:blur(10px);overflow:hidden;position:relative;&::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:\",\n    \";opacity:\",\n    \";transition:opacity 0.3s ease;}\"\n], (props)=>props.active || props.hovered ? \"linear-gradient(135deg, \".concat(props.color, \"20, \").concat(props.color, \"10)\") : \"rgba(255, 255, 255, 0.05)\", (props)=>props.active || props.hovered ? props.color : \"rgba(255, 255, 255, 0.1)\", (props)=>\"linear-gradient(135deg, \".concat(props.color, \"10, transparent)\"), (props)=>props.active || props.hovered ? 1 : 0);\n_c6 = FeatureCard;\nconst FeatureIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"RevolutionaryFeatures__FeatureIcon\",\n    componentId: \"sc-146f8b4-6\"\n})([\n    \"font-size:3rem;margin-bottom:1rem;text-align:center;filter:\",\n    \";transition:all 0.3s ease;\"\n], (props)=>props.active ? \"drop-shadow(0 0 20px currentColor)\" : \"none\");\n_c7 = FeatureIcon;\nconst FeatureTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h3.withConfig({\n    displayName: \"RevolutionaryFeatures__FeatureTitle\",\n    componentId: \"sc-146f8b4-7\"\n})([\n    \"font-size:1.5rem;font-weight:700;color:white;margin-bottom:0.5rem;text-align:center;\"\n]);\n_c8 = FeatureTitle;\nconst FeatureSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p.withConfig({\n    displayName: \"RevolutionaryFeatures__FeatureSubtitle\",\n    componentId: \"sc-146f8b4-8\"\n})([\n    \"font-size:1rem;color:rgba(255,255,255,0.7);text-align:center;margin-bottom:1rem;\"\n]);\n_c9 = FeatureSubtitle;\nconst FeatureDetails = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"RevolutionaryFeatures__FeatureDetails\",\n    componentId: \"sc-146f8b4-9\"\n})([\n    \"position:relative;z-index:1;\"\n]);\n_c10 = FeatureDetails;\nconst FeatureDescription = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p.withConfig({\n    displayName: \"RevolutionaryFeatures__FeatureDescription\",\n    componentId: \"sc-146f8b4-10\"\n})([\n    \"font-size:0.9rem;color:rgba(255,255,255,0.8);line-height:1.6;margin-bottom:1.5rem;\"\n]);\n_c11 = FeatureDescription;\nconst StatsGrid = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"RevolutionaryFeatures__StatsGrid\",\n    componentId: \"sc-146f8b4-11\"\n})([\n    \"display:grid;grid-template-columns:repeat(3,1fr);gap:1rem;\"\n]);\n_c12 = StatsGrid;\nconst StatItem = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"RevolutionaryFeatures__StatItem\",\n    componentId: \"sc-146f8b4-12\"\n})([\n    \"text-align:center;\"\n]);\n_c13 = StatItem;\nconst StatValue = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"RevolutionaryFeatures__StatValue\",\n    componentId: \"sc-146f8b4-13\"\n})([\n    \"font-size:1.5rem;font-weight:700;color:white;\"\n]);\n_c14 = StatValue;\nconst StatLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"RevolutionaryFeatures__StatLabel\",\n    componentId: \"sc-146f8b4-14\"\n})([\n    \"font-size:0.8rem;color:rgba(255,255,255,0.6);\"\n]);\n_c15 = StatLabel;\nconst DetailedView = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"RevolutionaryFeatures__DetailedView\",\n    componentId: \"sc-146f8b4-15\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:20px;padding:3rem;backdrop-filter:blur(20px);border:1px solid rgba(255,255,255,0.1);margin-bottom:3rem;\"\n]);\n_c16 = DetailedView;\nconst DetailedContent = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"RevolutionaryFeatures__DetailedContent\",\n    componentId: \"sc-146f8b4-16\"\n})([\n    \"\"\n]);\n_c17 = DetailedContent;\nconst DetailedHeader = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"RevolutionaryFeatures__DetailedHeader\",\n    componentId: \"sc-146f8b4-17\"\n})([\n    \"display:flex;align-items:center;gap:1.5rem;margin-bottom:2rem;\"\n]);\n_c18 = DetailedHeader;\nconst DetailedIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"RevolutionaryFeatures__DetailedIcon\",\n    componentId: \"sc-146f8b4-18\"\n})([\n    \"font-size:4rem;color:\",\n    \";filter:drop-shadow(0 0 20px \",\n    \"50);\"\n], (props)=>props.color, (props)=>props.color);\n_c19 = DetailedIcon;\nconst DetailedTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h3.withConfig({\n    displayName: \"RevolutionaryFeatures__DetailedTitle\",\n    componentId: \"sc-146f8b4-19\"\n})([\n    \"font-size:2.5rem;font-weight:700;color:white;margin-bottom:0.5rem;\"\n]);\n_c20 = DetailedTitle;\nconst DetailedSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p.withConfig({\n    displayName: \"RevolutionaryFeatures__DetailedSubtitle\",\n    componentId: \"sc-146f8b4-20\"\n})([\n    \"font-size:1.2rem;color:rgba(255,255,255,0.7);\"\n]);\n_c21 = DetailedSubtitle;\nconst DetailedDescription = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p.withConfig({\n    displayName: \"RevolutionaryFeatures__DetailedDescription\",\n    componentId: \"sc-146f8b4-21\"\n})([\n    \"font-size:1.1rem;color:rgba(255,255,255,0.8);line-height:1.8;margin-bottom:2rem;\"\n]);\n_c22 = DetailedDescription;\nconst TechnologiesSection = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"RevolutionaryFeatures__TechnologiesSection\",\n    componentId: \"sc-146f8b4-22\"\n})([\n    \"margin-bottom:2rem;\"\n]);\n_c23 = TechnologiesSection;\nconst BenefitsSection = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"RevolutionaryFeatures__BenefitsSection\",\n    componentId: \"sc-146f8b4-23\"\n})([\n    \"\"\n]);\n_c24 = BenefitsSection;\nconst SectionLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h4.withConfig({\n    displayName: \"RevolutionaryFeatures__SectionLabel\",\n    componentId: \"sc-146f8b4-24\"\n})([\n    \"font-size:1.2rem;font-weight:600;color:white;margin-bottom:1rem;\"\n]);\n_c25 = SectionLabel;\nconst TechnologiesList = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"RevolutionaryFeatures__TechnologiesList\",\n    componentId: \"sc-146f8b4-25\"\n})([\n    \"display:flex;flex-wrap:wrap;gap:0.5rem;\"\n]);\n_c26 = TechnologiesList;\nconst TechTag = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span).withConfig({\n    displayName: \"RevolutionaryFeatures__TechTag\",\n    componentId: \"sc-146f8b4-26\"\n})([\n    \"background:rgba(74,144,226,0.2);color:#4a90e2;padding:0.5rem 1rem;border-radius:20px;font-size:0.9rem;font-weight:500;border:1px solid rgba(74,144,226,0.3);\"\n]);\n_c27 = TechTag;\nconst BenefitsList = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"RevolutionaryFeatures__BenefitsList\",\n    componentId: \"sc-146f8b4-27\"\n})([\n    \"display:flex;flex-direction:column;gap:0.5rem;\"\n]);\n_c28 = BenefitsList;\nconst BenefitItem = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"RevolutionaryFeatures__BenefitItem\",\n    componentId: \"sc-146f8b4-28\"\n})([\n    \"color:rgba(255,255,255,0.8);font-size:1rem;\"\n]);\n_c29 = BenefitItem;\nconst ProgressIndicators = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"RevolutionaryFeatures__ProgressIndicators\",\n    componentId: \"sc-146f8b4-29\"\n})([\n    \"display:flex;justify-content:center;gap:1rem;\"\n]);\n_c30 = ProgressIndicators;\nconst ProgressDot = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"RevolutionaryFeatures__ProgressDot\",\n    componentId: \"sc-146f8b4-30\"\n})([\n    \"width:12px;height:12px;border-radius:50%;background:\",\n    \";cursor:pointer;transition:all 0.3s ease;\"\n], (props)=>props.active ? \"#4a90e2\" : \"rgba(255, 255, 255, 0.3)\");\n_c31 = ProgressDot;\n/* harmony default export */ __webpack_exports__[\"default\"] = (RevolutionaryFeatures);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31;\n$RefreshReg$(_c, \"RevolutionaryFeatures\");\n$RefreshReg$(_c1, \"FeaturesContainer\");\n$RefreshReg$(_c2, \"ContentWrapper\");\n$RefreshReg$(_c3, \"SectionTitle\");\n$RefreshReg$(_c4, \"SectionSubtitle\");\n$RefreshReg$(_c5, \"FeaturesGrid\");\n$RefreshReg$(_c6, \"FeatureCard\");\n$RefreshReg$(_c7, \"FeatureIcon\");\n$RefreshReg$(_c8, \"FeatureTitle\");\n$RefreshReg$(_c9, \"FeatureSubtitle\");\n$RefreshReg$(_c10, \"FeatureDetails\");\n$RefreshReg$(_c11, \"FeatureDescription\");\n$RefreshReg$(_c12, \"StatsGrid\");\n$RefreshReg$(_c13, \"StatItem\");\n$RefreshReg$(_c14, \"StatValue\");\n$RefreshReg$(_c15, \"StatLabel\");\n$RefreshReg$(_c16, \"DetailedView\");\n$RefreshReg$(_c17, \"DetailedContent\");\n$RefreshReg$(_c18, \"DetailedHeader\");\n$RefreshReg$(_c19, \"DetailedIcon\");\n$RefreshReg$(_c20, \"DetailedTitle\");\n$RefreshReg$(_c21, \"DetailedSubtitle\");\n$RefreshReg$(_c22, \"DetailedDescription\");\n$RefreshReg$(_c23, \"TechnologiesSection\");\n$RefreshReg$(_c24, \"BenefitsSection\");\n$RefreshReg$(_c25, \"SectionLabel\");\n$RefreshReg$(_c26, \"TechnologiesList\");\n$RefreshReg$(_c27, \"TechTag\");\n$RefreshReg$(_c28, \"BenefitsList\");\n$RefreshReg$(_c29, \"BenefitItem\");\n$RefreshReg$(_c30, \"ProgressIndicators\");\n$RefreshReg$(_c31, \"ProgressDot\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/RevolutionaryFeatures.tsx\n"));

/***/ })

}]);