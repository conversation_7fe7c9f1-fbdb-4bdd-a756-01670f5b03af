/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_next_dist_pages__error_js"],{

/***/ "../../node_modules/next/dist/pages/_error.js":
/*!****************************************************!*\
  !*** ../../node_modules/next/dist/pages/_error.js ***!
  \****************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n  enumerable: true,\n  get: function () {\n    return Error;\n  }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"../../node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"../../node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/_interop_require_default._(__webpack_require__(/*! react */ \"../../node_modules/react/index.js\"));\nconst _head = /*#__PURE__*/_interop_require_default._(__webpack_require__(/*! ../shared/lib/head */ \"../../node_modules/next/dist/shared/lib/head.js\"));\nconst statusCodes = {\n  400: \"Bad Request\",\n  404: \"This page could not be found\",\n  405: \"Method Not Allowed\",\n  500: \"Internal Server Error\"\n};\nfunction _getInitialProps(param) {\n  let {\n    res,\n    err\n  } = param;\n  const statusCode = res && res.statusCode ? res.statusCode : err ? err.statusCode : 404;\n  return {\n    statusCode\n  };\n}\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily: 'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: \"100vh\",\n    textAlign: \"center\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\"\n  },\n  desc: {\n    lineHeight: \"48px\"\n  },\n  h1: {\n    display: \"inline-block\",\n    margin: \"0 20px 0 0\",\n    paddingRight: 23,\n    fontSize: 24,\n    fontWeight: 500,\n    verticalAlign: \"top\"\n  },\n  h2: {\n    fontSize: 14,\n    fontWeight: 400,\n    lineHeight: \"28px\"\n  },\n  wrap: {\n    display: \"inline-block\"\n  }\n};\nclass Error extends _react.default.Component {\n  render() {\n    const {\n      statusCode,\n      withDarkMode = true\n    } = this.props;\n    const title = this.props.title || statusCodes[statusCode] || \"An unexpected error has occurred\";\n    return /*#__PURE__*/(0, _jsxruntime.jsxs)(\"div\", {\n      style: styles.error,\n      children: [/*#__PURE__*/(0, _jsxruntime.jsx)(_head.default, {\n        children: /*#__PURE__*/(0, _jsxruntime.jsx)(\"title\", {\n          children: statusCode ? statusCode + \": \" + title : \"Application error: a client-side exception has occurred\"\n        })\n      }), /*#__PURE__*/(0, _jsxruntime.jsxs)(\"div\", {\n        style: styles.desc,\n        children: [/*#__PURE__*/(0, _jsxruntime.jsx)(\"style\", {\n          dangerouslySetInnerHTML: {\n            /* CSS minified from\n            body { margin: 0; color: #000; background: #fff; }\n            .next-error-h1 {\n            border-right: 1px solid rgba(0, 0, 0, .3);\n            }\n            ${\n            withDarkMode\n            ? `@media (prefers-color-scheme: dark) {\n            body { color: #fff; background: #000; }\n            .next-error-h1 {\n            border-right: 1px solid rgba(255, 255, 255, .3);\n            }\n            }`\n            : ''\n            }\n            */\n            __html: \"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}\" + (withDarkMode ? \"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\" : \"\")\n          }\n        }), statusCode ? /*#__PURE__*/(0, _jsxruntime.jsx)(\"h1\", {\n          className: \"next-error-h1\",\n          style: styles.h1,\n          children: statusCode\n        }) : null, /*#__PURE__*/(0, _jsxruntime.jsx)(\"div\", {\n          style: styles.wrap,\n          children: /*#__PURE__*/(0, _jsxruntime.jsxs)(\"h2\", {\n            style: styles.h2,\n            children: [this.props.title || statusCode ? title : /*#__PURE__*/(0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n              children: \"Application error: a client-side exception has occurred (see the browser console for more information)\"\n            }), \".\"]\n          })\n        })]\n      })]\n    });\n  }\n}\nError.displayName = \"ErrorPage\";\nError.getInitialProps = _getInitialProps;\nError.origGetInitialProps = _getInitialProps;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n  Object.defineProperty(exports.default, '__esModule', {\n    value: true\n  });\n  Object.assign(exports.default, exports);\n  module.exports = exports.default;\n}\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9wYWdlcy9fZXJyb3IuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBQ2JBLDhDQUE2QztFQUN6Q0csS0FBSyxFQUFFO0FBQ1gsQ0FBQyxFQUFDO0FBQ0ZILDJDQUEwQztFQUN0Q0ksVUFBVSxFQUFFLElBQUk7RUFDaEJDLEdBQUcsRUFBRSxTQUFBQSxDQUFBLEVBQVc7SUFDWixPQUFPQyxLQUFLO0VBQ2hCO0FBQ0osQ0FBQyxFQUFDO0FBQ0YsTUFBTUMsd0JBQXdCLEdBQUdDLG1CQUFPLENBQUMsZ0hBQXlDLENBQUM7QUFDbkYsTUFBTUMsV0FBVyxHQUFHRCxtQkFBTyxDQUFDLGtFQUFtQixDQUFDO0FBQ2hELE1BQU1FLE1BQU0sR0FBRyxhQUFjSCx3QkFBd0IsQ0FBQ0ksQ0FBQyxDQUFDSCxtQkFBTyxDQUFDLGdEQUFPLENBQUMsQ0FBQztBQUN6RSxNQUFNSSxLQUFLLEdBQUcsYUFBY0wsd0JBQXdCLENBQUNJLENBQUMsQ0FBQ0gsbUJBQU8sQ0FBQywyRUFBb0IsQ0FBQyxDQUFDO0FBQ3JGLE1BQU1LLFdBQVcsR0FBRztFQUNoQixHQUFHLEVBQUUsYUFBYTtFQUNsQixHQUFHLEVBQUUsOEJBQThCO0VBQ25DLEdBQUcsRUFBRSxvQkFBb0I7RUFDekIsR0FBRyxFQUFFO0FBQ1QsQ0FBQztBQUNELFNBQVNDLGdCQUFnQkEsQ0FBQ0MsS0FBSyxFQUFFO0VBQzdCLElBQUk7SUFBRUMsR0FBRztJQUFFQztFQUFJLENBQUMsR0FBR0YsS0FBSztFQUN4QixNQUFNRyxVQUFVLEdBQUdGLEdBQUcsSUFBSUEsR0FBRyxDQUFDRSxVQUFVLEdBQUdGLEdBQUcsQ0FBQ0UsVUFBVSxHQUFHRCxHQUFHLEdBQUdBLEdBQUcsQ0FBQ0MsVUFBVSxHQUFHLEdBQUc7RUFDdEYsT0FBTztJQUNIQTtFQUNKLENBQUM7QUFDTDtBQUNBLE1BQU1DLE1BQU0sR0FBRztFQUNYQyxLQUFLLEVBQUU7SUFDSDtJQUNBQyxVQUFVLEVBQUUsNkZBQTZGO0lBQ3pHQyxNQUFNLEVBQUUsT0FBTztJQUNmQyxTQUFTLEVBQUUsUUFBUTtJQUNuQkMsT0FBTyxFQUFFLE1BQU07SUFDZkMsYUFBYSxFQUFFLFFBQVE7SUFDdkJDLFVBQVUsRUFBRSxRQUFRO0lBQ3BCQyxjQUFjLEVBQUU7RUFDcEIsQ0FBQztFQUNEQyxJQUFJLEVBQUU7SUFDRkMsVUFBVSxFQUFFO0VBQ2hCLENBQUM7RUFDREMsRUFBRSxFQUFFO0lBQ0FOLE9BQU8sRUFBRSxjQUFjO0lBQ3ZCTyxNQUFNLEVBQUUsWUFBWTtJQUNwQkMsWUFBWSxFQUFFLEVBQUU7SUFDaEJDLFFBQVEsRUFBRSxFQUFFO0lBQ1pDLFVBQVUsRUFBRSxHQUFHO0lBQ2ZDLGFBQWEsRUFBRTtFQUNuQixDQUFDO0VBQ0RDLEVBQUUsRUFBRTtJQUNBSCxRQUFRLEVBQUUsRUFBRTtJQUNaQyxVQUFVLEVBQUUsR0FBRztJQUNmTCxVQUFVLEVBQUU7RUFDaEIsQ0FBQztFQUNEUSxJQUFJLEVBQUU7SUFDRmIsT0FBTyxFQUFFO0VBQ2I7QUFDSixDQUFDO0FBQ0QsTUFBTWxCLEtBQUssU0FBU0ksTUFBTSxDQUFDNEIsT0FBTyxDQUFDQyxTQUFTLENBQUM7RUFDekNDLE1BQU1BLENBQUEsRUFBRztJQUNMLE1BQU07TUFBRXRCLFVBQVU7TUFBRXVCLFlBQVksR0FBRztJQUFLLENBQUMsR0FBRyxJQUFJLENBQUNDLEtBQUs7SUFDdEQsTUFBTUMsS0FBSyxHQUFHLElBQUksQ0FBQ0QsS0FBSyxDQUFDQyxLQUFLLElBQUk5QixXQUFXLENBQUNLLFVBQVUsQ0FBQyxJQUFJLGtDQUFrQztJQUMvRixPQUFPLGFBQWMsQ0FBQyxDQUFDLEVBQUVULFdBQVcsQ0FBQ21DLElBQUksRUFBRSxLQUFLLEVBQUU7TUFDOUNDLEtBQUssRUFBRTFCLE1BQU0sQ0FBQ0MsS0FBSztNQUNuQjBCLFFBQVEsRUFBRSxDQUNOLGFBQWMsQ0FBQyxDQUFDLEVBQUVyQyxXQUFXLENBQUNzQyxHQUFHLEVBQUVuQyxLQUFLLENBQUMwQixPQUFPLEVBQUU7UUFDOUNRLFFBQVEsRUFBRSxhQUFjLENBQUMsQ0FBQyxFQUFFckMsV0FBVyxDQUFDc0MsR0FBRyxFQUFFLE9BQU8sRUFBRTtVQUNsREQsUUFBUSxFQUFFNUIsVUFBVSxHQUFHQSxVQUFVLEdBQUcsSUFBSSxHQUFHeUIsS0FBSyxHQUFHO1FBQ3ZELENBQUM7TUFDTCxDQUFDLENBQUMsRUFDRixhQUFjLENBQUMsQ0FBQyxFQUFFbEMsV0FBVyxDQUFDbUMsSUFBSSxFQUFFLEtBQUssRUFBRTtRQUN2Q0MsS0FBSyxFQUFFMUIsTUFBTSxDQUFDUyxJQUFJO1FBQ2xCa0IsUUFBUSxFQUFFLENBQ04sYUFBYyxDQUFDLENBQUMsRUFBRXJDLFdBQVcsQ0FBQ3NDLEdBQUcsRUFBRSxPQUFPLEVBQUU7VUFDeENDLHVCQUF1QixFQUFFO1lBQ3JCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtZQUNrQkMsTUFBTSxFQUFFLGdHQUFnRyxJQUFJUixZQUFZLEdBQUcsaUlBQWlJLEdBQUcsRUFBRTtVQUN2UDtRQUNKLENBQUMsQ0FBQyxFQUNGdkIsVUFBVSxHQUFHLGFBQWMsQ0FBQyxDQUFDLEVBQUVULFdBQVcsQ0FBQ3NDLEdBQUcsRUFBRSxJQUFJLEVBQUU7VUFDbERHLFNBQVMsRUFBRSxlQUFlO1VBQzFCTCxLQUFLLEVBQUUxQixNQUFNLENBQUNXLEVBQUU7VUFDaEJnQixRQUFRLEVBQUU1QjtRQUNkLENBQUMsQ0FBQyxHQUFHLElBQUksRUFDVCxhQUFjLENBQUMsQ0FBQyxFQUFFVCxXQUFXLENBQUNzQyxHQUFHLEVBQUUsS0FBSyxFQUFFO1VBQ3RDRixLQUFLLEVBQUUxQixNQUFNLENBQUNrQixJQUFJO1VBQ2xCUyxRQUFRLEVBQUUsYUFBYyxDQUFDLENBQUMsRUFBRXJDLFdBQVcsQ0FBQ21DLElBQUksRUFBRSxJQUFJLEVBQUU7WUFDaERDLEtBQUssRUFBRTFCLE1BQU0sQ0FBQ2lCLEVBQUU7WUFDaEJVLFFBQVEsRUFBRSxDQUNOLElBQUksQ0FBQ0osS0FBSyxDQUFDQyxLQUFLLElBQUl6QixVQUFVLEdBQUd5QixLQUFLLEdBQUcsYUFBYyxDQUFDLENBQUMsRUFBRWxDLFdBQVcsQ0FBQ3NDLEdBQUcsRUFBRXRDLFdBQVcsQ0FBQzBDLFFBQVEsRUFBRTtjQUM5RkwsUUFBUSxFQUFFO1lBQ2QsQ0FBQyxDQUFDLEVBQ0YsR0FBRztVQUVYLENBQUM7UUFDTCxDQUFDLENBQUM7TUFFVixDQUFDLENBQUM7SUFFVixDQUFDLENBQUM7RUFDTjtBQUNKO0FBQ0F4QyxLQUFLLENBQUM4QyxXQUFXLEdBQUcsV0FBVztBQUMvQjlDLEtBQUssQ0FBQytDLGVBQWUsR0FBR3ZDLGdCQUFnQjtBQUN4Q1IsS0FBSyxDQUFDZ0QsbUJBQW1CLEdBQUd4QyxnQkFBZ0I7QUFFNUMsSUFBSSxDQUFDLE9BQU9aLE9BQU8sQ0FBQ29DLE9BQU8sS0FBSyxVQUFVLElBQUssT0FBT3BDLE9BQU8sQ0FBQ29DLE9BQU8sS0FBSyxRQUFRLElBQUlwQyxPQUFPLENBQUNvQyxPQUFPLEtBQUssSUFBSyxLQUFLLE9BQU9wQyxPQUFPLENBQUNvQyxPQUFPLENBQUNpQixVQUFVLEtBQUssV0FBVyxFQUFFO0VBQ3JLdkQsTUFBTSxDQUFDQyxjQUFjLENBQUNDLE9BQU8sQ0FBQ29DLE9BQU8sRUFBRSxZQUFZLEVBQUU7SUFBRW5DLEtBQUssRUFBRTtFQUFLLENBQUMsQ0FBQztFQUNyRUgsTUFBTSxDQUFDd0QsTUFBTSxDQUFDdEQsT0FBTyxDQUFDb0MsT0FBTyxFQUFFcEMsT0FBTyxDQUFDO0VBQ3ZDdUQsTUFBTSxDQUFDdkQsT0FBTyxHQUFHQSxPQUFPLENBQUNvQyxPQUFPO0FBQ2xDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3BhZ2VzL19lcnJvci5qcz9mMWMxIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiZGVmYXVsdFwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gRXJyb3I7XG4gICAgfVxufSk7XG5jb25zdCBfaW50ZXJvcF9yZXF1aXJlX2RlZmF1bHQgPSByZXF1aXJlKFwiQHN3Yy9oZWxwZXJzL18vX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0XCIpO1xuY29uc3QgX2pzeHJ1bnRpbWUgPSByZXF1aXJlKFwicmVhY3QvanN4LXJ1bnRpbWVcIik7XG5jb25zdCBfcmVhY3QgPSAvKiNfX1BVUkVfXyovIF9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdC5fKHJlcXVpcmUoXCJyZWFjdFwiKSk7XG5jb25zdCBfaGVhZCA9IC8qI19fUFVSRV9fKi8gX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0Ll8ocmVxdWlyZShcIi4uL3NoYXJlZC9saWIvaGVhZFwiKSk7XG5jb25zdCBzdGF0dXNDb2RlcyA9IHtcbiAgICA0MDA6IFwiQmFkIFJlcXVlc3RcIixcbiAgICA0MDQ6IFwiVGhpcyBwYWdlIGNvdWxkIG5vdCBiZSBmb3VuZFwiLFxuICAgIDQwNTogXCJNZXRob2QgTm90IEFsbG93ZWRcIixcbiAgICA1MDA6IFwiSW50ZXJuYWwgU2VydmVyIEVycm9yXCJcbn07XG5mdW5jdGlvbiBfZ2V0SW5pdGlhbFByb3BzKHBhcmFtKSB7XG4gICAgbGV0IHsgcmVzLCBlcnIgfSA9IHBhcmFtO1xuICAgIGNvbnN0IHN0YXR1c0NvZGUgPSByZXMgJiYgcmVzLnN0YXR1c0NvZGUgPyByZXMuc3RhdHVzQ29kZSA6IGVyciA/IGVyci5zdGF0dXNDb2RlIDogNDA0O1xuICAgIHJldHVybiB7XG4gICAgICAgIHN0YXR1c0NvZGVcbiAgICB9O1xufVxuY29uc3Qgc3R5bGVzID0ge1xuICAgIGVycm9yOiB7XG4gICAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9zaW5kcmVzb3JodXMvbW9kZXJuLW5vcm1hbGl6ZS9ibG9iL21haW4vbW9kZXJuLW5vcm1hbGl6ZS5jc3MjTDM4LUw1MlxuICAgICAgICBmb250RmFtaWx5OiAnc3lzdGVtLXVpLFwiU2Vnb2UgVUlcIixSb2JvdG8sSGVsdmV0aWNhLEFyaWFsLHNhbnMtc2VyaWYsXCJBcHBsZSBDb2xvciBFbW9qaVwiLFwiU2Vnb2UgVUkgRW1vamlcIicsXG4gICAgICAgIGhlaWdodDogXCIxMDB2aFwiLFxuICAgICAgICB0ZXh0QWxpZ246IFwiY2VudGVyXCIsXG4gICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxuICAgICAgICBmbGV4RGlyZWN0aW9uOiBcImNvbHVtblwiLFxuICAgICAgICBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLFxuICAgICAgICBqdXN0aWZ5Q29udGVudDogXCJjZW50ZXJcIlxuICAgIH0sXG4gICAgZGVzYzoge1xuICAgICAgICBsaW5lSGVpZ2h0OiBcIjQ4cHhcIlxuICAgIH0sXG4gICAgaDE6IHtcbiAgICAgICAgZGlzcGxheTogXCJpbmxpbmUtYmxvY2tcIixcbiAgICAgICAgbWFyZ2luOiBcIjAgMjBweCAwIDBcIixcbiAgICAgICAgcGFkZGluZ1JpZ2h0OiAyMyxcbiAgICAgICAgZm9udFNpemU6IDI0LFxuICAgICAgICBmb250V2VpZ2h0OiA1MDAsXG4gICAgICAgIHZlcnRpY2FsQWxpZ246IFwidG9wXCJcbiAgICB9LFxuICAgIGgyOiB7XG4gICAgICAgIGZvbnRTaXplOiAxNCxcbiAgICAgICAgZm9udFdlaWdodDogNDAwLFxuICAgICAgICBsaW5lSGVpZ2h0OiBcIjI4cHhcIlxuICAgIH0sXG4gICAgd3JhcDoge1xuICAgICAgICBkaXNwbGF5OiBcImlubGluZS1ibG9ja1wiXG4gICAgfVxufTtcbmNsYXNzIEVycm9yIGV4dGVuZHMgX3JlYWN0LmRlZmF1bHQuQ29tcG9uZW50IHtcbiAgICByZW5kZXIoKSB7XG4gICAgICAgIGNvbnN0IHsgc3RhdHVzQ29kZSwgd2l0aERhcmtNb2RlID0gdHJ1ZSB9ID0gdGhpcy5wcm9wcztcbiAgICAgICAgY29uc3QgdGl0bGUgPSB0aGlzLnByb3BzLnRpdGxlIHx8IHN0YXR1c0NvZGVzW3N0YXR1c0NvZGVdIHx8IFwiQW4gdW5leHBlY3RlZCBlcnJvciBoYXMgb2NjdXJyZWRcIjtcbiAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gKDAsIF9qc3hydW50aW1lLmpzeHMpKFwiZGl2XCIsIHtcbiAgICAgICAgICAgIHN0eWxlOiBzdHlsZXMuZXJyb3IsXG4gICAgICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAgICAgICAgIC8qI19fUFVSRV9fKi8gKDAsIF9qc3hydW50aW1lLmpzeCkoX2hlYWQuZGVmYXVsdCwge1xuICAgICAgICAgICAgICAgICAgICBjaGlsZHJlbjogLyojX19QVVJFX18qLyAoMCwgX2pzeHJ1bnRpbWUuanN4KShcInRpdGxlXCIsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBzdGF0dXNDb2RlID8gc3RhdHVzQ29kZSArIFwiOiBcIiArIHRpdGxlIDogXCJBcHBsaWNhdGlvbiBlcnJvcjogYSBjbGllbnQtc2lkZSBleGNlcHRpb24gaGFzIG9jY3VycmVkXCJcbiAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICB9KSxcbiAgICAgICAgICAgICAgICAvKiNfX1BVUkVfXyovICgwLCBfanN4cnVudGltZS5qc3hzKShcImRpdlwiLCB7XG4gICAgICAgICAgICAgICAgICAgIHN0eWxlOiBzdHlsZXMuZGVzYyxcbiAgICAgICAgICAgICAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8qI19fUFVSRV9fKi8gKDAsIF9qc3hydW50aW1lLmpzeCkoXCJzdHlsZVwiLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLyogQ1NTIG1pbmlmaWVkIGZyb21cbiAgICAgICAgICAgICAgICBib2R5IHsgbWFyZ2luOiAwOyBjb2xvcjogIzAwMDsgYmFja2dyb3VuZDogI2ZmZjsgfVxuICAgICAgICAgICAgICAgIC5uZXh0LWVycm9yLWgxIHtcbiAgICAgICAgICAgICAgICAgIGJvcmRlci1yaWdodDogMXB4IHNvbGlkIHJnYmEoMCwgMCwgMCwgLjMpO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICR7XG4gICAgICAgICAgICAgICAgICB3aXRoRGFya01vZGVcbiAgICAgICAgICAgICAgICAgICAgPyBgQG1lZGlhIChwcmVmZXJzLWNvbG9yLXNjaGVtZTogZGFyaykge1xuICAgICAgICAgICAgICAgICAgYm9keSB7IGNvbG9yOiAjZmZmOyBiYWNrZ3JvdW5kOiAjMDAwOyB9XG4gICAgICAgICAgICAgICAgICAubmV4dC1lcnJvci1oMSB7XG4gICAgICAgICAgICAgICAgICAgIGJvcmRlci1yaWdodDogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgLjMpO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1gXG4gICAgICAgICAgICAgICAgICAgIDogJydcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAqLyBfX2h0bWw6IFwiYm9keXtjb2xvcjojMDAwO2JhY2tncm91bmQ6I2ZmZjttYXJnaW46MH0ubmV4dC1lcnJvci1oMXtib3JkZXItcmlnaHQ6MXB4IHNvbGlkIHJnYmEoMCwwLDAsLjMpfVwiICsgKHdpdGhEYXJrTW9kZSA/IFwiQG1lZGlhIChwcmVmZXJzLWNvbG9yLXNjaGVtZTpkYXJrKXtib2R5e2NvbG9yOiNmZmY7YmFja2dyb3VuZDojMDAwfS5uZXh0LWVycm9yLWgxe2JvcmRlci1yaWdodDoxcHggc29saWQgcmdiYSgyNTUsMjU1LDI1NSwuMyl9fVwiIDogXCJcIilcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9KSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1c0NvZGUgPyAvKiNfX1BVUkVfXyovICgwLCBfanN4cnVudGltZS5qc3gpKFwiaDFcIiwge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZTogXCJuZXh0LWVycm9yLWgxXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU6IHN0eWxlcy5oMSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGlsZHJlbjogc3RhdHVzQ29kZVxuICAgICAgICAgICAgICAgICAgICAgICAgfSkgOiBudWxsLFxuICAgICAgICAgICAgICAgICAgICAgICAgLyojX19QVVJFX18qLyAoMCwgX2pzeHJ1bnRpbWUuanN4KShcImRpdlwiLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU6IHN0eWxlcy53cmFwLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiAvKiNfX1BVUkVfXyovICgwLCBfanN4cnVudGltZS5qc3hzKShcImgyXCIsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU6IHN0eWxlcy5oMixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucHJvcHMudGl0bGUgfHwgc3RhdHVzQ29kZSA/IHRpdGxlIDogLyojX19QVVJFX18qLyAoMCwgX2pzeHJ1bnRpbWUuanN4KShfanN4cnVudGltZS5GcmFnbWVudCwge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBcIkFwcGxpY2F0aW9uIGVycm9yOiBhIGNsaWVudC1zaWRlIGV4Y2VwdGlvbiBoYXMgb2NjdXJyZWQgKHNlZSB0aGUgYnJvd3NlciBjb25zb2xlIGZvciBtb3JlIGluZm9ybWF0aW9uKVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiLlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgICAgXVxuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICBdXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbkVycm9yLmRpc3BsYXlOYW1lID0gXCJFcnJvclBhZ2VcIjtcbkVycm9yLmdldEluaXRpYWxQcm9wcyA9IF9nZXRJbml0aWFsUHJvcHM7XG5FcnJvci5vcmlnR2V0SW5pdGlhbFByb3BzID0gX2dldEluaXRpYWxQcm9wcztcblxuaWYgKCh0eXBlb2YgZXhwb3J0cy5kZWZhdWx0ID09PSAnZnVuY3Rpb24nIHx8ICh0eXBlb2YgZXhwb3J0cy5kZWZhdWx0ID09PSAnb2JqZWN0JyAmJiBleHBvcnRzLmRlZmF1bHQgIT09IG51bGwpKSAmJiB0eXBlb2YgZXhwb3J0cy5kZWZhdWx0Ll9fZXNNb2R1bGUgPT09ICd1bmRlZmluZWQnKSB7XG4gIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLmRlZmF1bHQsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcbiAgT2JqZWN0LmFzc2lnbihleHBvcnRzLmRlZmF1bHQsIGV4cG9ydHMpO1xuICBtb2R1bGUuZXhwb3J0cyA9IGV4cG9ydHMuZGVmYXVsdDtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9X2Vycm9yLmpzLm1hcCJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsImVudW1lcmFibGUiLCJnZXQiLCJFcnJvciIsIl9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdCIsInJlcXVpcmUiLCJfanN4cnVudGltZSIsIl9yZWFjdCIsIl8iLCJfaGVhZCIsInN0YXR1c0NvZGVzIiwiX2dldEluaXRpYWxQcm9wcyIsInBhcmFtIiwicmVzIiwiZXJyIiwic3RhdHVzQ29kZSIsInN0eWxlcyIsImVycm9yIiwiZm9udEZhbWlseSIsImhlaWdodCIsInRleHRBbGlnbiIsImRpc3BsYXkiLCJmbGV4RGlyZWN0aW9uIiwiYWxpZ25JdGVtcyIsImp1c3RpZnlDb250ZW50IiwiZGVzYyIsImxpbmVIZWlnaHQiLCJoMSIsIm1hcmdpbiIsInBhZGRpbmdSaWdodCIsImZvbnRTaXplIiwiZm9udFdlaWdodCIsInZlcnRpY2FsQWxpZ24iLCJoMiIsIndyYXAiLCJkZWZhdWx0IiwiQ29tcG9uZW50IiwicmVuZGVyIiwid2l0aERhcmtNb2RlIiwicHJvcHMiLCJ0aXRsZSIsImpzeHMiLCJzdHlsZSIsImNoaWxkcmVuIiwianN4IiwiZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwiLCJfX2h0bWwiLCJjbGFzc05hbWUiLCJGcmFnbWVudCIsImRpc3BsYXlOYW1lIiwiZ2V0SW5pdGlhbFByb3BzIiwib3JpZ0dldEluaXRpYWxQcm9wcyIsIl9fZXNNb2R1bGUiLCJhc3NpZ24iLCJtb2R1bGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/pages/_error.js\n"));

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/amp-context.shared-runtime.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/amp-context.shared-runtime.js ***!
  \*****************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"AmpStateContext\", ({\n  enumerable: true,\n  get: function () {\n    return AmpStateContext;\n  }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"../../node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/_interop_require_default._(__webpack_require__(/*! react */ \"../../node_modules/react/index.js\"));\nconst AmpStateContext = _react.default.createContext({});\nif (true) {\n  AmpStateContext.displayName = \"AmpStateContext\";\n}\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL2FtcC1jb250ZXh0LnNoYXJlZC1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUNiQSw4Q0FBNkM7RUFDekNHLEtBQUssRUFBRTtBQUNYLENBQUMsRUFBQztBQUNGSCxtREFBa0Q7RUFDOUNJLFVBQVUsRUFBRSxJQUFJO0VBQ2hCQyxHQUFHLEVBQUUsU0FBQUEsQ0FBQSxFQUFXO0lBQ1osT0FBT0MsZUFBZTtFQUMxQjtBQUNKLENBQUMsRUFBQztBQUNGLE1BQU1DLHdCQUF3QixHQUFHQyxtQkFBTyxDQUFDLGdIQUF5QyxDQUFDO0FBQ25GLE1BQU1DLE1BQU0sR0FBRyxhQUFjRix3QkFBd0IsQ0FBQ0csQ0FBQyxDQUFDRixtQkFBTyxDQUFDLGdEQUFPLENBQUMsQ0FBQztBQUN6RSxNQUFNRixlQUFlLEdBQUdHLE1BQU0sQ0FBQ0UsT0FBTyxDQUFDQyxhQUFhLENBQUMsQ0FBQyxDQUFDLENBQUM7QUFDeEQsSUFBSSxNQUF1QztFQUN2Q04sZUFBZSxDQUFDTyxXQUFXLEdBQUcsaUJBQWlCO0FBQ25EIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvYW1wLWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUuanM/Mzk2NSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIkFtcFN0YXRlQ29udGV4dFwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gQW1wU3RhdGVDb250ZXh0O1xuICAgIH1cbn0pO1xuY29uc3QgX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0ID0gcmVxdWlyZShcIkBzd2MvaGVscGVycy9fL19pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdFwiKTtcbmNvbnN0IF9yZWFjdCA9IC8qI19fUFVSRV9fKi8gX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0Ll8ocmVxdWlyZShcInJlYWN0XCIpKTtcbmNvbnN0IEFtcFN0YXRlQ29udGV4dCA9IF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUNvbnRleHQoe30pO1xuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAgIEFtcFN0YXRlQ29udGV4dC5kaXNwbGF5TmFtZSA9IFwiQW1wU3RhdGVDb250ZXh0XCI7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFtcC1jb250ZXh0LnNoYXJlZC1ydW50aW1lLmpzLm1hcCJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsImVudW1lcmFibGUiLCJnZXQiLCJBbXBTdGF0ZUNvbnRleHQiLCJfaW50ZXJvcF9yZXF1aXJlX2RlZmF1bHQiLCJyZXF1aXJlIiwiX3JlYWN0IiwiXyIsImRlZmF1bHQiLCJjcmVhdGVDb250ZXh0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/amp-context.shared-runtime.js\n"));

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/amp-mode.js":
/*!***********************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/amp-mode.js ***!
  \***********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"isInAmpMode\", ({\n  enumerable: true,\n  get: function () {\n    return isInAmpMode;\n  }\n}));\nfunction isInAmpMode(param) {\n  let {\n    ampFirst = false,\n    hybrid = false,\n    hasQuery = false\n  } = param === void 0 ? {} : param;\n  return ampFirst || hybrid && hasQuery;\n}\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL2FtcC1tb2RlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUNiQSw4Q0FBNkM7RUFDekNHLEtBQUssRUFBRTtBQUNYLENBQUMsRUFBQztBQUNGSCwrQ0FBOEM7RUFDMUNJLFVBQVUsRUFBRSxJQUFJO0VBQ2hCQyxHQUFHLEVBQUUsU0FBQUEsQ0FBQSxFQUFXO0lBQ1osT0FBT0MsV0FBVztFQUN0QjtBQUNKLENBQUMsRUFBQztBQUNGLFNBQVNBLFdBQVdBLENBQUNDLEtBQUssRUFBRTtFQUN4QixJQUFJO0lBQUVDLFFBQVEsR0FBRyxLQUFLO0lBQUVDLE1BQU0sR0FBRyxLQUFLO0lBQUVDLFFBQVEsR0FBRztFQUFNLENBQUMsR0FBR0gsS0FBSyxLQUFLLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHQSxLQUFLO0VBQzFGLE9BQU9DLFFBQVEsSUFBSUMsTUFBTSxJQUFJQyxRQUFRO0FBQ3pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvYW1wLW1vZGUuanM/MjEzNSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImlzSW5BbXBNb2RlXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBpc0luQW1wTW9kZTtcbiAgICB9XG59KTtcbmZ1bmN0aW9uIGlzSW5BbXBNb2RlKHBhcmFtKSB7XG4gICAgbGV0IHsgYW1wRmlyc3QgPSBmYWxzZSwgaHlicmlkID0gZmFsc2UsIGhhc1F1ZXJ5ID0gZmFsc2UgfSA9IHBhcmFtID09PSB2b2lkIDAgPyB7fSA6IHBhcmFtO1xuICAgIHJldHVybiBhbXBGaXJzdCB8fCBoeWJyaWQgJiYgaGFzUXVlcnk7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFtcC1tb2RlLmpzLm1hcCJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsImVudW1lcmFibGUiLCJnZXQiLCJpc0luQW1wTW9kZSIsInBhcmFtIiwiYW1wRmlyc3QiLCJoeWJyaWQiLCJoYXNRdWVyeSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/amp-mode.js\n"));

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/head.js":
/*!*******************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/head.js ***!
  \*******************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\"use client\";\n\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  default: function () {\n    return _default;\n  },\n  defaultHead: function () {\n    return defaultHead;\n  }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"../../node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"../../node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"../../node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/_interop_require_wildcard._(__webpack_require__(/*! react */ \"../../node_modules/react/index.js\"));\nconst _sideeffect = /*#__PURE__*/_interop_require_default._(__webpack_require__(/*! ./side-effect */ \"../../node_modules/next/dist/shared/lib/side-effect.js\"));\nconst _ampcontextsharedruntime = __webpack_require__(/*! ./amp-context.shared-runtime */ \"../../node_modules/next/dist/shared/lib/amp-context.shared-runtime.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ./head-manager-context.shared-runtime */ \"../../node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _ampmode = __webpack_require__(/*! ./amp-mode */ \"../../node_modules/next/dist/shared/lib/amp-mode.js\");\nconst _warnonce = __webpack_require__(/*! ./utils/warn-once */ \"../../node_modules/next/dist/shared/lib/utils/warn-once.js\");\nfunction defaultHead(inAmpMode) {\n  if (inAmpMode === void 0) inAmpMode = false;\n  const head = [/*#__PURE__*/(0, _jsxruntime.jsx)(\"meta\", {\n    charSet: \"utf-8\"\n  })];\n  if (!inAmpMode) {\n    head.push( /*#__PURE__*/(0, _jsxruntime.jsx)(\"meta\", {\n      name: \"viewport\",\n      content: \"width=device-width\"\n    }));\n  }\n  return head;\n}\nfunction onlyReactElement(list, child) {\n  // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n  if (typeof child === \"string\" || typeof child === \"number\") {\n    return list;\n  }\n  // Adds support for React.Fragment\n  if (child.type === _react.default.Fragment) {\n    return list.concat(\n    // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n    _react.default.Children.toArray(child.props.children).reduce(\n    // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n    (fragmentList, fragmentChild) => {\n      if (typeof fragmentChild === \"string\" || typeof fragmentChild === \"number\") {\n        return fragmentList;\n      }\n      return fragmentList.concat(fragmentChild);\n    }, []));\n  }\n  return list.concat(child);\n}\nconst METATYPES = [\"name\", \"httpEquiv\", \"charSet\", \"itemProp\"];\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/\nfunction unique() {\n  const keys = new Set();\n  const tags = new Set();\n  const metaTypes = new Set();\n  const metaCategories = {};\n  return h => {\n    let isUnique = true;\n    let hasKey = false;\n    if (h.key && typeof h.key !== \"number\" && h.key.indexOf(\"$\") > 0) {\n      hasKey = true;\n      const key = h.key.slice(h.key.indexOf(\"$\") + 1);\n      if (keys.has(key)) {\n        isUnique = false;\n      } else {\n        keys.add(key);\n      }\n    }\n    // eslint-disable-next-line default-case\n    switch (h.type) {\n      case \"title\":\n      case \"base\":\n        if (tags.has(h.type)) {\n          isUnique = false;\n        } else {\n          tags.add(h.type);\n        }\n        break;\n      case \"meta\":\n        for (let i = 0, len = METATYPES.length; i < len; i++) {\n          const metatype = METATYPES[i];\n          if (!h.props.hasOwnProperty(metatype)) continue;\n          if (metatype === \"charSet\") {\n            if (metaTypes.has(metatype)) {\n              isUnique = false;\n            } else {\n              metaTypes.add(metatype);\n            }\n          } else {\n            const category = h.props[metatype];\n            const categories = metaCategories[metatype] || new Set();\n            if ((metatype !== \"name\" || !hasKey) && categories.has(category)) {\n              isUnique = false;\n            } else {\n              categories.add(category);\n              metaCategories[metatype] = categories;\n            }\n          }\n        }\n        break;\n    }\n    return isUnique;\n  };\n}\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */\nfunction reduceComponents(headChildrenElements, props) {\n  const {\n    inAmpMode\n  } = props;\n  return headChildrenElements.reduce(onlyReactElement, []).reverse().concat(defaultHead(inAmpMode).reverse()).filter(unique()).reverse().map((c, i) => {\n    const key = c.key || i;\n    if (false) {}\n    if (true) {\n      // omit JSON-LD structured data snippets from the warning\n      if (c.type === \"script\" && c.props[\"type\"] !== \"application/ld+json\") {\n        const srcMessage = c.props[\"src\"] ? '<script> tag with src=\"' + c.props[\"src\"] + '\"' : \"inline <script>\";\n        (0, _warnonce.warnOnce)(\"Do not add <script> tags using next/head (see \" + srcMessage + \"). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component\");\n      } else if (c.type === \"link\" && c.props[\"rel\"] === \"stylesheet\") {\n        (0, _warnonce.warnOnce)('Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"' + c.props[\"href\"] + '\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component');\n      }\n    }\n    return /*#__PURE__*/_react.default.cloneElement(c, {\n      key\n    });\n  });\n}\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */\nfunction Head(param) {\n  let {\n    children\n  } = param;\n  const ampState = (0, _react.useContext)(_ampcontextsharedruntime.AmpStateContext);\n  const headManager = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);\n  return /*#__PURE__*/(0, _jsxruntime.jsx)(_sideeffect.default, {\n    reduceComponentsToState: reduceComponents,\n    headManager: headManager,\n    inAmpMode: (0, _ampmode.isInAmpMode)(ampState),\n    children: children\n  });\n}\n_c = Head;\nconst _default = Head;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n  Object.defineProperty(exports.default, '__esModule', {\n    value: true\n  });\n  Object.assign(exports.default, exports);\n  module.exports = exports.default;\n}\nvar _c;\n$RefreshReg$(_c, \"Head\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/head.js\n"));

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/side-effect.js":
/*!**************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/side-effect.js ***!
  \**************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nvar _s = $RefreshSig$();\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n  enumerable: true,\n  get: function () {\n    return SideEffect;\n  }\n}));\nconst _react = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\nconst isServer = false;\nconst useClientOnlyLayoutEffect = isServer ? () => {} : _react.useLayoutEffect;\nconst useClientOnlyEffect = isServer ? () => {} : _react.useEffect;\nfunction SideEffect(props) {\n  _s();\n  const {\n    headManager,\n    reduceComponentsToState\n  } = props;\n  function emitChange() {\n    if (headManager && headManager.mountedInstances) {\n      const headElements = _react.Children.toArray(Array.from(headManager.mountedInstances).filter(Boolean));\n      headManager.updateHead(reduceComponentsToState(headElements, props));\n    }\n  }\n  if (isServer) {\n    var _headManager_mountedInstances;\n    headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n    emitChange();\n  }\n  useClientOnlyLayoutEffect(() => {\n    var _headManager_mountedInstances;\n    headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n    return () => {\n      var _headManager_mountedInstances;\n      headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.delete(props.children);\n    };\n  });\n  // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n  // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n  // being rendered, we only trigger the method from the last one.\n  // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n  // singleton in the layout effect pass, and actually trigger it in the effect pass.\n  useClientOnlyLayoutEffect(() => {\n    if (headManager) {\n      headManager._pendingUpdate = emitChange;\n    }\n    return () => {\n      if (headManager) {\n        headManager._pendingUpdate = emitChange;\n      }\n    };\n  });\n  useClientOnlyEffect(() => {\n    if (headManager && headManager._pendingUpdate) {\n      headManager._pendingUpdate();\n      headManager._pendingUpdate = null;\n    }\n    return () => {\n      if (headManager && headManager._pendingUpdate) {\n        headManager._pendingUpdate();\n        headManager._pendingUpdate = null;\n      }\n    };\n  });\n  return null;\n}\n_s(SideEffect, \"gHVkikNHNxjVdD11eJBzaqkCiPY=\", false, function () {\n  return [useClientOnlyLayoutEffect, useClientOnlyLayoutEffect, useClientOnlyEffect];\n});\n_c = SideEffect;\nvar _c;\n$RefreshReg$(_c, \"SideEffect\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/side-effect.js\n"));

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/utils/warn-once.js":
/*!******************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/utils/warn-once.js ***!
  \******************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"warnOnce\", ({\n  enumerable: true,\n  get: function () {\n    return warnOnce;\n  }\n}));\nlet warnOnce = _ => {};\nif (true) {\n  const warnings = new Set();\n  warnOnce = msg => {\n    if (!warnings.has(msg)) {\n      console.warn(msg);\n    }\n    warnings.add(msg);\n  };\n}\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL3V0aWxzL3dhcm4tb25jZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFDYkEsOENBQTZDO0VBQ3pDRyxLQUFLLEVBQUU7QUFDWCxDQUFDLEVBQUM7QUFDRkgsNENBQTJDO0VBQ3ZDSSxVQUFVLEVBQUUsSUFBSTtFQUNoQkMsR0FBRyxFQUFFLFNBQUFBLENBQUEsRUFBVztJQUNaLE9BQU9DLFFBQVE7RUFDbkI7QUFDSixDQUFDLEVBQUM7QUFDRixJQUFJQSxRQUFRLEdBQUlDLENBQUMsSUFBRyxDQUFDLENBQUM7QUFDdEIsSUFBSSxNQUF1QztFQUN2QyxNQUFNQyxRQUFRLEdBQUcsSUFBSUMsR0FBRyxDQUFDLENBQUM7RUFDMUJILFFBQVEsR0FBSUksR0FBRyxJQUFHO0lBQ2QsSUFBSSxDQUFDRixRQUFRLENBQUNHLEdBQUcsQ0FBQ0QsR0FBRyxDQUFDLEVBQUU7TUFDcEJFLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDSCxHQUFHLENBQUM7SUFDckI7SUFDQUYsUUFBUSxDQUFDTSxHQUFHLENBQUNKLEdBQUcsQ0FBQztFQUNyQixDQUFDO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy93YXJuLW9uY2UuanM/Njk3MyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIndhcm5PbmNlXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB3YXJuT25jZTtcbiAgICB9XG59KTtcbmxldCB3YXJuT25jZSA9IChfKT0+e307XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiKSB7XG4gICAgY29uc3Qgd2FybmluZ3MgPSBuZXcgU2V0KCk7XG4gICAgd2Fybk9uY2UgPSAobXNnKT0+e1xuICAgICAgICBpZiAoIXdhcm5pbmdzLmhhcyhtc2cpKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4obXNnKTtcbiAgICAgICAgfVxuICAgICAgICB3YXJuaW5ncy5hZGQobXNnKTtcbiAgICB9O1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD13YXJuLW9uY2UuanMubWFwIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiZW51bWVyYWJsZSIsImdldCIsIndhcm5PbmNlIiwiXyIsIndhcm5pbmdzIiwiU2V0IiwibXNnIiwiaGFzIiwiY29uc29sZSIsIndhcm4iLCJhZGQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/utils/warn-once.js\n"));

/***/ })

}]);