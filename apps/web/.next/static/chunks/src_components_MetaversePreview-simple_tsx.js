"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_MetaversePreview-simple_tsx"],{

/***/ "./src/components/MetaversePreview-simple.tsx":
/*!****************************************************!*\
  !*** ./src/components/MetaversePreview-simple.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst worlds = [\n    {\n        id: \"casino\",\n        name: \"Королевское казино\",\n        description: \"Роскошное казино с золотыми столами и кристальными люстрами\",\n        theme: \"Классика\",\n        color: \"#ffd700\",\n        environment: \"sunset\",\n        features: [\n            \"Покерные столы\",\n            \"VIP зоны\",\n            \"Живая музыка\",\n            \"Бар\"\n        ],\n        playerCount: 1247,\n        preview: \"\\uD83C\\uDFF0\"\n    },\n    {\n        id: \"medieval\",\n        name: \"Средневековая таверна\",\n        description: \"Уютная таверна с каменными стенами и горящим камином\",\n        theme: \"Фантазия\",\n        color: \"#8b4513\",\n        environment: \"forest\",\n        features: [\n            \"Деревянные столы\",\n            \"Камин\",\n            \"Бард\",\n            \"Эль\"\n        ],\n        playerCount: 892,\n        preview: \"\\uD83C\\uDFDB️\"\n    },\n    {\n        id: \"futuristic\",\n        name: \"Киберпространство\",\n        description: \"Футуристическая станция с голографическими интерфейсами\",\n        theme: \"Sci-Fi\",\n        color: \"#00ffff\",\n        environment: \"city\",\n        features: [\n            \"Голограммы\",\n            \"Неон\",\n            \"ИИ дилеры\",\n            \"Антигравитация\"\n        ],\n        playerCount: 2156,\n        preview: \"\\uD83D\\uDE80\"\n    }\n];\n// Стилизованные компоненты\nconst MetaverseContainer = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__MetaverseContainer\",\n    componentId: \"sc-449e83ba-0\"\n})([\n    \"min-height:100vh;background:linear-gradient(135deg,#0f0f23 0%,#1a1a2e 50%,#16213e 100%);padding:4rem 2rem;display:flex;align-items:center;justify-content:center;\"\n]);\n_c = MetaverseContainer;\nconst ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__ContentWrapper\",\n    componentId: \"sc-449e83ba-1\"\n})([\n    \"max-width:1400px;width:100%;\"\n]);\n_c1 = ContentWrapper;\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].h2.withConfig({\n    displayName: \"MetaversePreview-simple__SectionTitle\",\n    componentId: \"sc-449e83ba-2\"\n})([\n    \"font-size:3.5rem;font-weight:900;text-align:center;margin-bottom:1rem;background:linear-gradient(45deg,#4a90e2,#7b68ee,#9370db);-webkit-background-clip:text;-webkit-text-fill-color:transparent;@media (max-width:768px){font-size:2.5rem;}\"\n]);\n_c2 = SectionTitle;\nconst SectionSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].p.withConfig({\n    displayName: \"MetaversePreview-simple__SectionSubtitle\",\n    componentId: \"sc-449e83ba-3\"\n})([\n    \"font-size:1.3rem;color:rgba(255,255,255,0.7);text-align:center;margin-bottom:4rem;max-width:800px;margin-left:auto;margin-right:auto;\"\n]);\n_c3 = SectionSubtitle;\nconst MainContent = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__MainContent\",\n    componentId: \"sc-449e83ba-4\"\n})([\n    \"display:grid;grid-template-columns:1fr 1fr;gap:4rem;margin-bottom:4rem;@media (max-width:1024px){grid-template-columns:1fr;gap:2rem;}\"\n]);\n_c4 = MainContent;\nconst PreviewContainer = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__PreviewContainer\",\n    componentId: \"sc-449e83ba-5\"\n})([\n    \"position:relative;height:500px;border-radius:20px;overflow:hidden;border:2px solid rgba(255,255,255,0.1);background:linear-gradient(135deg,#1a1a2e,#16213e);display:flex;align-items:center;justify-content:center;\"\n]);\n_c5 = PreviewContainer;\nconst PreviewPlaceholder = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__PreviewPlaceholder\",\n    componentId: \"sc-449e83ba-6\"\n})([\n    \"font-size:8rem;filter:drop-shadow(0 0 30px currentColor);\"\n]);\n_c6 = PreviewPlaceholder;\nconst WorldInfo = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__WorldInfo\",\n    componentId: \"sc-449e83ba-7\"\n})([\n    \"display:flex;flex-direction:column;justify-content:center;\"\n]);\n_c7 = WorldInfo;\nconst WorldHeader = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__WorldHeader\",\n    componentId: \"sc-449e83ba-8\"\n})([\n    \"display:flex;align-items:center;gap:1.5rem;margin-bottom:2rem;\"\n]);\n_c8 = WorldHeader;\nconst WorldIcon = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__WorldIcon\",\n    componentId: \"sc-449e83ba-9\"\n})([\n    \"font-size:4rem;filter:drop-shadow(0 0 20px currentColor);\"\n]);\n_c9 = WorldIcon;\nconst WorldName = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].h3.withConfig({\n    displayName: \"MetaversePreview-simple__WorldName\",\n    componentId: \"sc-449e83ba-10\"\n})([\n    \"font-size:2.5rem;font-weight:700;color:white;margin-bottom:0.5rem;\"\n]);\n_c10 = WorldName;\nconst WorldTheme = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].p.withConfig({\n    displayName: \"MetaversePreview-simple__WorldTheme\",\n    componentId: \"sc-449e83ba-11\"\n})([\n    \"font-size:1.2rem;color:rgba(255,255,255,0.7);\"\n]);\n_c11 = WorldTheme;\nconst WorldDescription = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].p.withConfig({\n    displayName: \"MetaversePreview-simple__WorldDescription\",\n    componentId: \"sc-449e83ba-12\"\n})([\n    \"font-size:1.1rem;color:rgba(255,255,255,0.8);line-height:1.6;margin-bottom:2rem;\"\n]);\n_c12 = WorldDescription;\nconst WorldStats = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__WorldStats\",\n    componentId: \"sc-449e83ba-13\"\n})([\n    \"display:grid;grid-template-columns:repeat(3,1fr);gap:1rem;margin-bottom:2rem;\"\n]);\n_c13 = WorldStats;\nconst StatItem = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__StatItem\",\n    componentId: \"sc-449e83ba-14\"\n})([\n    \"text-align:center;background:rgba(255,255,255,0.05);border-radius:10px;padding:1rem;\"\n]);\n_c14 = StatItem;\nconst StatValue = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__StatValue\",\n    componentId: \"sc-449e83ba-15\"\n})([\n    \"font-size:2rem;font-weight:700;color:#4a90e2;\"\n]);\n_c15 = StatValue;\nconst StatLabel = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__StatLabel\",\n    componentId: \"sc-449e83ba-16\"\n})([\n    \"font-size:0.9rem;color:rgba(255,255,255,0.6);\"\n]);\n_c16 = StatLabel;\nconst FeaturesList = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__FeaturesList\",\n    componentId: \"sc-449e83ba-17\"\n})([\n    \"margin-bottom:2rem;\"\n]);\n_c17 = FeaturesList;\nconst FeaturesTitle = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].h4.withConfig({\n    displayName: \"MetaversePreview-simple__FeaturesTitle\",\n    componentId: \"sc-449e83ba-18\"\n})([\n    \"font-size:1.2rem;font-weight:600;color:white;margin-bottom:1rem;\"\n]);\n_c18 = FeaturesTitle;\nconst FeatureItem = (0,styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div).withConfig({\n    displayName: \"MetaversePreview-simple__FeatureItem\",\n    componentId: \"sc-449e83ba-19\"\n})([\n    \"color:rgba(255,255,255,0.8);font-size:1rem;margin-bottom:0.5rem;\"\n]);\n_c19 = FeatureItem;\nconst ActionButtons = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__ActionButtons\",\n    componentId: \"sc-449e83ba-20\"\n})([\n    \"display:flex;gap:1rem;\"\n]);\n_c20 = ActionButtons;\nconst PrimaryButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button).withConfig({\n    displayName: \"MetaversePreview-simple__PrimaryButton\",\n    componentId: \"sc-449e83ba-21\"\n})([\n    \"background:linear-gradient(135deg,#4a90e2,#7b68ee);color:white;border:none;border-radius:10px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;\"\n]);\n_c21 = PrimaryButton;\nconst SecondaryButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button).withConfig({\n    displayName: \"MetaversePreview-simple__SecondaryButton\",\n    componentId: \"sc-449e83ba-22\"\n})([\n    \"background:transparent;color:white;border:2px solid rgba(255,255,255,0.3);border-radius:10px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;\"\n]);\n_c22 = SecondaryButton;\nconst WorldSelector = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__WorldSelector\",\n    componentId: \"sc-449e83ba-23\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1rem;margin-bottom:3rem;\"\n]);\n_c23 = WorldSelector;\nconst WorldCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div).withConfig({\n    displayName: \"MetaversePreview-simple__WorldCard\",\n    componentId: \"sc-449e83ba-24\"\n})([\n    \"background:\",\n    \";border:2px solid \",\n    \";border-radius:15px;padding:1.5rem;text-align:center;cursor:pointer;transition:all 0.3s ease;\"\n], (props)=>props.active ? \"\".concat(props.color, \"20\") : \"rgba(255, 255, 255, 0.05)\", (props)=>props.active ? props.color : \"rgba(255, 255, 255, 0.1)\");\n_c24 = WorldCard;\nconst WorldCardIcon = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__WorldCardIcon\",\n    componentId: \"sc-449e83ba-25\"\n})([\n    \"font-size:2rem;margin-bottom:0.5rem;\"\n]);\n_c25 = WorldCardIcon;\nconst WorldCardName = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__WorldCardName\",\n    componentId: \"sc-449e83ba-26\"\n})([\n    \"font-size:1rem;font-weight:600;color:white;margin-bottom:0.5rem;\"\n]);\n_c26 = WorldCardName;\nconst WorldCardPlayers = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"MetaversePreview-simple__WorldCardPlayers\",\n    componentId: \"sc-449e83ba-27\"\n})([\n    \"font-size:0.8rem;color:rgba(255,255,255,0.6);\"\n]);\n_c27 = WorldCardPlayers;\nconst MetaversePreview = ()=>{\n    _s();\n    const [selectedWorld, setSelectedWorld] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetaverseContainer, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentWrapper, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionTitle, {\n                            children: \"Метавселенная карточных игр\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSubtitle, {\n                            children: \"Исследуйте уникальные 3D миры и играйте в иммерсивной среде\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MainContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PreviewContainer, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PreviewPlaceholder, {\n                                children: worlds[selectedWorld].preview\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldInfo, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                                mode: \"wait\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 50\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        x: -50\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldIcon, {\n                                                    children: worlds[selectedWorld].preview\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldName, {\n                                                            children: worlds[selectedWorld].name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldTheme, {\n                                                            children: worlds[selectedWorld].theme\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldDescription, {\n                                            children: worlds[selectedWorld].description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldStats, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                            children: worlds[selectedWorld].playerCount\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                            children: \"Игроков онлайн\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                            children: worlds[selectedWorld].features.length\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                            children: \"Уникальных функций\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                            children: \"4.9\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                            children: \"Рейтинг\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesList, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesTitle, {\n                                                    children: \"Особенности мира:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                worlds[selectedWorld].features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureItem, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            x: -20\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            x: 0\n                                                        },\n                                                        transition: {\n                                                            delay: index * 0.1\n                                                        },\n                                                        children: [\n                                                            \"✨ \",\n                                                            feature\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButtons, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PrimaryButton, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    children: \"Войти в мир\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SecondaryButton, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    children: \"Виртуальный тур\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, selectedWorld, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldSelector, {\n                    children: worlds.map((world, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldCard, {\n                            active: index === selectedWorld,\n                            color: world.color,\n                            onClick: ()=>setSelectedWorld(index),\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldCardIcon, {\n                                    children: world.preview\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldCardName, {\n                                    children: world.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldCardPlayers, {\n                                    children: [\n                                        world.playerCount,\n                                        \" игроков\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, world.id, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n            lineNumber: 266,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MetaversePreview, \"Pt4605gosn06TEI3FzfmFug6rPQ=\");\n_c28 = MetaversePreview;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MetaversePreview);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28;\n$RefreshReg$(_c, \"MetaverseContainer\");\n$RefreshReg$(_c1, \"ContentWrapper\");\n$RefreshReg$(_c2, \"SectionTitle\");\n$RefreshReg$(_c3, \"SectionSubtitle\");\n$RefreshReg$(_c4, \"MainContent\");\n$RefreshReg$(_c5, \"PreviewContainer\");\n$RefreshReg$(_c6, \"PreviewPlaceholder\");\n$RefreshReg$(_c7, \"WorldInfo\");\n$RefreshReg$(_c8, \"WorldHeader\");\n$RefreshReg$(_c9, \"WorldIcon\");\n$RefreshReg$(_c10, \"WorldName\");\n$RefreshReg$(_c11, \"WorldTheme\");\n$RefreshReg$(_c12, \"WorldDescription\");\n$RefreshReg$(_c13, \"WorldStats\");\n$RefreshReg$(_c14, \"StatItem\");\n$RefreshReg$(_c15, \"StatValue\");\n$RefreshReg$(_c16, \"StatLabel\");\n$RefreshReg$(_c17, \"FeaturesList\");\n$RefreshReg$(_c18, \"FeaturesTitle\");\n$RefreshReg$(_c19, \"FeatureItem\");\n$RefreshReg$(_c20, \"ActionButtons\");\n$RefreshReg$(_c21, \"PrimaryButton\");\n$RefreshReg$(_c22, \"SecondaryButton\");\n$RefreshReg$(_c23, \"WorldSelector\");\n$RefreshReg$(_c24, \"WorldCard\");\n$RefreshReg$(_c25, \"WorldCardIcon\");\n$RefreshReg$(_c26, \"WorldCardName\");\n$RefreshReg$(_c27, \"WorldCardPlayers\");\n$RefreshReg$(_c28, \"MetaversePreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/MetaversePreview-simple.tsx\n"));

/***/ })

}]);