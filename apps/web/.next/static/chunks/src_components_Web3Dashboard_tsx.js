"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_Web3Dashboard_tsx"],{

/***/ "./src/components/Web3Dashboard.tsx":
/*!******************************************!*\
  !*** ./src/components/Web3Dashboard.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst Web3Dashboard = (param)=>{\n    let { web3Status, onConnectWallet } = param;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"wallet\");\n    const [walletConnected, setWalletConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userAddress, setUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tokenBalances, setTokenBalances] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [nftCollection, setNftCollection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [defiPools, setDefiPools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [totalPortfolioValue, setTotalPortfolioValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Симуляция данных Web3\n        if (walletConnected) {\n            setUserAddress(\"******************************************\");\n            setTokenBalances([\n                {\n                    symbol: \"KOZYR\",\n                    balance: 15420.5,\n                    value: 7710.25,\n                    change24h: 12.5\n                },\n                {\n                    symbol: \"ETH\",\n                    balance: 2.45,\n                    value: 4900,\n                    change24h: -3.2\n                },\n                {\n                    symbol: \"USDC\",\n                    balance: 1250,\n                    value: 1250,\n                    change24h: 0.1\n                }\n            ]);\n            setNftCollection([\n                {\n                    id: \"1\",\n                    name: \"Legendary King\",\n                    image: \"\\uD83D\\uDC51\",\n                    rarity: \"legendary\",\n                    power: 95,\n                    price: 2.5,\n                    isStaked: true\n                },\n                {\n                    id: \"2\",\n                    name: \"Epic Queen\",\n                    image: \"\\uD83D\\uDC78\",\n                    rarity: \"epic\",\n                    power: 85,\n                    price: 1.2,\n                    isStaked: false\n                },\n                {\n                    id: \"3\",\n                    name: \"Rare Ace\",\n                    image: \"\\uD83C\\uDCCF\",\n                    rarity: \"rare\",\n                    power: 75,\n                    price: 0.8,\n                    isStaked: true\n                }\n            ]);\n            setDefiPools([\n                {\n                    id: \"1\",\n                    name: \"KOZYR/ETH\",\n                    apr: 145.2,\n                    tvl: 2500000,\n                    userStaked: 1500,\n                    rewards: 25.4\n                },\n                {\n                    id: \"2\",\n                    name: \"KOZYR/USDC\",\n                    apr: 89.7,\n                    tvl: 1800000,\n                    userStaked: 800,\n                    rewards: 12.1\n                }\n            ]);\n            setTotalPortfolioValue(13860.25);\n        }\n    }, [\n        walletConnected\n    ]);\n    const connectWallet = async ()=>{\n        // Симуляция подключения кошелька\n        await new Promise((resolve)=>setTimeout(resolve, 1500));\n        setWalletConnected(true);\n        onConnectWallet();\n    };\n    const getRarityColor = (rarity)=>{\n        switch(rarity){\n            case \"legendary\":\n                return \"#ffd700\";\n            case \"epic\":\n                return \"#9370db\";\n            case \"rare\":\n                return \"#4a90e2\";\n            default:\n                return \"#6b7280\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Web3Container, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentWrapper, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionTitle, {\n                            children: \"Web3 Экосистема\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSubtitle, {\n                            children: \"Децентрализованное будущее карточных игр\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, undefined),\n                !walletConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletConnection, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConnectionCard, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.8\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConnectionIcon, {\n                                children: \"\\uD83D\\uDD17\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConnectionTitle, {\n                                children: \"Подключите кошелёк\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConnectionDescription, {\n                                children: \"Подключите Web3 кошелёк для доступа к NFT, DeFi и DAO функциям\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletOptions, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletOption, {\n                                        onClick: connectWallet,\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletIcon, {\n                                                children: \"\\uD83E\\uDD8A\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletName, {\n                                                children: \"MetaMask\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletOption, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletIcon, {\n                                                children: \"\\uD83C\\uDF08\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletName, {\n                                                children: \"Rainbow\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletOption, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletIcon, {\n                                                children: \"\\uD83D\\uDC99\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletName, {\n                                                children: \"Coinbase\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserInfo, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserAvatar, {\n                                            children: \"\\uD83D\\uDC64\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserDetails, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserAddress, {\n                                                    children: [\n                                                        userAddress.slice(0, 6),\n                                                        \"...\",\n                                                        userAddress.slice(-4)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PortfolioValue, {\n                                                    children: [\n                                                        \"$\",\n                                                        totalPortfolioValue.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NetworkInfo, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NetworkIndicator, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NetworkName, {\n                                            children: \"Ethereum Mainnet\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabNavigation, {\n                            children: [\n                                {\n                                    id: \"wallet\",\n                                    label: \"Кошелёк\",\n                                    icon: \"\\uD83D\\uDCB0\"\n                                },\n                                {\n                                    id: \"nft\",\n                                    label: \"NFT\",\n                                    icon: \"\\uD83C\\uDFB4\"\n                                },\n                                {\n                                    id: \"defi\",\n                                    label: \"DeFi\",\n                                    icon: \"\\uD83C\\uDFE6\"\n                                },\n                                {\n                                    id: \"dao\",\n                                    label: \"DAO\",\n                                    icon: \"\\uD83D\\uDDF3️\"\n                                }\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabButton, {\n                                    active: activeTab === tab.id,\n                                    onClick: ()=>setActiveTab(tab.id),\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabIcon, {\n                                            children: tab.icon\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabLabel, {\n                                            children: tab.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, tab.id, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                                mode: \"wait\",\n                                children: [\n                                    activeTab === \"wallet\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TokenGrid, {\n                                            children: tokenBalances.map((token)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TokenCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TokenHeader, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TokenSymbol, {\n                                                                    children: token.symbol\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TokenChange, {\n                                                                    positive: token.change24h > 0,\n                                                                    children: [\n                                                                        token.change24h > 0 ? \"↗\" : \"↘\",\n                                                                        \" \",\n                                                                        Math.abs(token.change24h),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TokenBalance, {\n                                                            children: token.balance.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TokenValue, {\n                                                            children: [\n                                                                \"$\",\n                                                                token.value.toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, token.symbol, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, \"wallet\", false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    activeTab === \"nft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTGrid, {\n                                            children: nftCollection.map((nft)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTCard, {\n                                                    rarity: nft.rarity,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTImage, {\n                                                            children: nft.image\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTInfo, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTName, {\n                                                                    children: nft.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTRarity, {\n                                                                    rarity: nft.rarity,\n                                                                    children: nft.rarity.toUpperCase()\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTPower, {\n                                                                    children: [\n                                                                        \"⚡ \",\n                                                                        nft.power\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTPrice, {\n                                                                    children: [\n                                                                        nft.price,\n                                                                        \" ETH\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        nft.isStaked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StakedBadge, {\n                                                            children: \"STAKED\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 44\n                                                        }, undefined)\n                                                    ]\n                                                }, nft.id, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, \"nft\", false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    activeTab === \"defi\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeFiGrid, {\n                                            children: defiPools.map((pool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolHeader, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolName, {\n                                                                    children: pool.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolAPR, {\n                                                                    children: [\n                                                                        pool.apr,\n                                                                        \"% APR\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStats, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStat, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStatLabel, {\n                                                                            children: \"TVL\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                            lineNumber: 290,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStatValue, {\n                                                                            children: [\n                                                                                \"$\",\n                                                                                (pool.tvl / 1000000).toFixed(1),\n                                                                                \"M\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                            lineNumber: 291,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStat, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStatLabel, {\n                                                                            children: \"Ваш стейк\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                            lineNumber: 294,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStatValue, {\n                                                                            children: [\n                                                                                \"$\",\n                                                                                pool.userStaked\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                            lineNumber: 295,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 293,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStat, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStatLabel, {\n                                                                            children: \"Награды\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                            lineNumber: 298,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStatValue, {\n                                                                            children: [\n                                                                                pool.rewards,\n                                                                                \" KOZYR\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolActions, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolButton, {\n                                                                    whileHover: {\n                                                                        scale: 1.02\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.98\n                                                                    },\n                                                                    children: \"Добавить\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolButton, {\n                                                                    secondary: true,\n                                                                    whileHover: {\n                                                                        scale: 1.02\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.98\n                                                                    },\n                                                                    children: \"Забрать\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, pool.id, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, \"defi\", false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    activeTab === \"dao\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOSection, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStats, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStat, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStatValue, {\n                                                                    children: \"15,420\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStatLabel, {\n                                                                    children: \"Ваша voting power\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStat, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStatValue, {\n                                                                    children: \"7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStatLabel, {\n                                                                    children: \"Активных предложений\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStat, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStatValue, {\n                                                                    children: \"89%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStatLabel, {\n                                                                    children: \"Участие в голосовании\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProposalsList, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProposalCard, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProposalTitle, {\n                                                                children: \"Добавить новую игру: Блэкджек\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProposalDescription, {\n                                                                children: \"Предложение о добавлении блэкджека в платформу\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProposalVotes, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoteOption, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoteLabel, {\n                                                                                children: \"За: 85%\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                                lineNumber: 355,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoteBar, {\n                                                                                width: 85\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                                lineNumber: 356,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoteOption, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoteLabel, {\n                                                                                children: \"Против: 15%\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                                lineNumber: 359,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoteBar, {\n                                                                                width: 15\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                                lineNumber: 360,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProposalActions, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoteButton, {\n                                                                        whileHover: {\n                                                                            scale: 1.02\n                                                                        },\n                                                                        whileTap: {\n                                                                            scale: 0.98\n                                                                        },\n                                                                        children: \"Голосовать ЗА\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoteButton, {\n                                                                        secondary: true,\n                                                                        whileHover: {\n                                                                            scale: 1.02\n                                                                        },\n                                                                        whileTap: {\n                                                                            scale: 0.98\n                                                                        },\n                                                                        children: \"Голосовать ПРОТИВ\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, \"dao\", false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Web3Dashboard, \"d3HFTTZRphU7pr3dylTnrNDO5lw=\");\n_c = Web3Dashboard;\n// Стилизованные компоненты (первая часть)\nconst Web3Container = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__Web3Container\",\n    componentId: \"sc-d33395ac-0\"\n})([\n    \"min-height:100vh;background:linear-gradient(135deg,#16213e 0%,#0f0f23 50%,#1a1a2e 100%);padding:4rem 2rem;display:flex;align-items:center;justify-content:center;\"\n]);\n_c1 = Web3Container;\nconst ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__ContentWrapper\",\n    componentId: \"sc-d33395ac-1\"\n})([\n    \"max-width:1400px;width:100%;\"\n]);\n_c2 = ContentWrapper;\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h2.withConfig({\n    displayName: \"Web3Dashboard__SectionTitle\",\n    componentId: \"sc-d33395ac-2\"\n})([\n    \"font-size:3.5rem;font-weight:900;text-align:center;margin-bottom:1rem;background:linear-gradient(45deg,#4a90e2,#7b68ee,#9370db);-webkit-background-clip:text;-webkit-text-fill-color:transparent;@media (max-width:768px){font-size:2.5rem;}\"\n]);\n_c3 = SectionTitle;\nconst SectionSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p.withConfig({\n    displayName: \"Web3Dashboard__SectionSubtitle\",\n    componentId: \"sc-d33395ac-3\"\n})([\n    \"font-size:1.3rem;color:rgba(255,255,255,0.7);text-align:center;margin-bottom:4rem;max-width:800px;margin-left:auto;margin-right:auto;\"\n]);\n_c4 = SectionSubtitle;\nconst WalletConnection = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__WalletConnection\",\n    componentId: \"sc-d33395ac-4\"\n})([\n    \"display:flex;justify-content:center;align-items:center;min-height:400px;\"\n]);\n_c5 = WalletConnection;\nconst ConnectionCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"Web3Dashboard__ConnectionCard\",\n    componentId: \"sc-d33395ac-5\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:20px;padding:3rem;text-align:center;backdrop-filter:blur(20px);border:1px solid rgba(255,255,255,0.1);max-width:500px;width:100%;\"\n]);\n_c6 = ConnectionCard;\nconst ConnectionIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__ConnectionIcon\",\n    componentId: \"sc-d33395ac-6\"\n})([\n    \"font-size:4rem;margin-bottom:1.5rem;\"\n]);\n_c7 = ConnectionIcon;\nconst ConnectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h3.withConfig({\n    displayName: \"Web3Dashboard__ConnectionTitle\",\n    componentId: \"sc-d33395ac-7\"\n})([\n    \"font-size:2rem;font-weight:700;color:white;margin-bottom:1rem;\"\n]);\n_c8 = ConnectionTitle;\nconst ConnectionDescription = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p.withConfig({\n    displayName: \"Web3Dashboard__ConnectionDescription\",\n    componentId: \"sc-d33395ac-8\"\n})([\n    \"color:rgba(255,255,255,0.7);margin-bottom:2rem;line-height:1.6;\"\n]);\n_c9 = ConnectionDescription;\nconst WalletOptions = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__WalletOptions\",\n    componentId: \"sc-d33395ac-9\"\n})([\n    \"display:grid;grid-template-columns:repeat(3,1fr);gap:1rem;@media (max-width:640px){grid-template-columns:1fr;}\"\n]);\n_c10 = WalletOptions;\nconst WalletOption = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"Web3Dashboard__WalletOption\",\n    componentId: \"sc-d33395ac-10\"\n})([\n    \"background:rgba(255,255,255,0.05);border:1px solid rgba(255,255,255,0.1);border-radius:15px;padding:1.5rem 1rem;cursor:pointer;transition:all 0.3s ease;&:hover{background:rgba(255,255,255,0.1);border-color:#4a90e2;}\"\n]);\n_c11 = WalletOption;\nconst WalletIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__WalletIcon\",\n    componentId: \"sc-d33395ac-11\"\n})([\n    \"font-size:2rem;margin-bottom:0.5rem;\"\n]);\n_c12 = WalletIcon;\nconst WalletName = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__WalletName\",\n    componentId: \"sc-d33395ac-12\"\n})([\n    \"color:white;font-weight:600;font-size:0.9rem;\"\n]);\n_c13 = WalletName;\n// Стилизованные компоненты (продолжение)\nconst DashboardContent = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__DashboardContent\",\n    componentId: \"sc-d33395ac-13\"\n})([\n    \"\"\n]);\n_c14 = DashboardContent;\nconst DashboardHeader = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__DashboardHeader\",\n    componentId: \"sc-d33395ac-14\"\n})([\n    \"display:flex;justify-content:space-between;align-items:center;margin-bottom:2rem;padding:1.5rem;background:rgba(255,255,255,0.05);border-radius:15px;backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,0.1);\"\n]);\n_c15 = DashboardHeader;\nconst UserInfo = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__UserInfo\",\n    componentId: \"sc-d33395ac-15\"\n})([\n    \"display:flex;align-items:center;gap:1rem;\"\n]);\n_c16 = UserInfo;\nconst UserAvatar = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__UserAvatar\",\n    componentId: \"sc-d33395ac-16\"\n})([\n    \"width:50px;height:50px;background:linear-gradient(135deg,#4a90e2,#7b68ee);border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:1.5rem;\"\n]);\n_c17 = UserAvatar;\nconst UserDetails = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__UserDetails\",\n    componentId: \"sc-d33395ac-17\"\n})([\n    \"\"\n]);\n_c18 = UserDetails;\nconst UserAddress = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__UserAddress\",\n    componentId: \"sc-d33395ac-18\"\n})([\n    \"color:white;font-weight:600;font-size:1.1rem;\"\n]);\n_c19 = UserAddress;\nconst PortfolioValue = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__PortfolioValue\",\n    componentId: \"sc-d33395ac-19\"\n})([\n    \"color:#4ade80;font-weight:700;font-size:1.3rem;\"\n]);\n_c20 = PortfolioValue;\nconst NetworkInfo = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__NetworkInfo\",\n    componentId: \"sc-d33395ac-20\"\n})([\n    \"display:flex;align-items:center;gap:0.5rem;\"\n]);\n_c21 = NetworkInfo;\nconst NetworkIndicator = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__NetworkIndicator\",\n    componentId: \"sc-d33395ac-21\"\n})([\n    \"width:12px;height:12px;background:#4ade80;border-radius:50%;animation:pulse 2s infinite;\"\n]);\n_c22 = NetworkIndicator;\nconst NetworkName = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__NetworkName\",\n    componentId: \"sc-d33395ac-22\"\n})([\n    \"color:rgba(255,255,255,0.8);font-size:0.9rem;\"\n]);\n_c23 = NetworkName;\nconst TabNavigation = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__TabNavigation\",\n    componentId: \"sc-d33395ac-23\"\n})([\n    \"display:flex;gap:0.5rem;margin-bottom:2rem;background:rgba(255,255,255,0.05);border-radius:15px;padding:0.5rem;\"\n]);\n_c24 = TabNavigation;\nconst TabButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"Web3Dashboard__TabButton\",\n    componentId: \"sc-d33395ac-24\"\n})([\n    \"flex:1;display:flex;align-items:center;justify-content:center;gap:0.5rem;padding:1rem;border:none;background:\",\n    \";color:\",\n    \";border-radius:10px;cursor:pointer;transition:all 0.3s ease;font-weight:600;&:hover{background:rgba(74,144,226,0.1);color:#4a90e2;}\"\n], (props)=>props.active ? \"rgba(74, 144, 226, 0.3)\" : \"transparent\", (props)=>props.active ? \"#4a90e2\" : \"rgba(255, 255, 255, 0.7)\");\n_c25 = TabButton;\nconst TabIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].span.withConfig({\n    displayName: \"Web3Dashboard__TabIcon\",\n    componentId: \"sc-d33395ac-25\"\n})([\n    \"font-size:1.2rem;\"\n]);\n_c26 = TabIcon;\nconst TabLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].span.withConfig({\n    displayName: \"Web3Dashboard__TabLabel\",\n    componentId: \"sc-d33395ac-26\"\n})([\n    \"font-size:0.9rem;\"\n]);\n_c27 = TabLabel;\nconst TabContent = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__TabContent\",\n    componentId: \"sc-d33395ac-27\"\n})([\n    \"min-height:400px;\"\n]);\n_c28 = TabContent;\nconst TokenGrid = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__TokenGrid\",\n    componentId: \"sc-d33395ac-28\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:1.5rem;\"\n]);\n_c29 = TokenGrid;\nconst TokenCard = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__TokenCard\",\n    componentId: \"sc-d33395ac-29\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;border:1px solid rgba(255,255,255,0.1);\"\n]);\n_c30 = TokenCard;\nconst TokenHeader = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__TokenHeader\",\n    componentId: \"sc-d33395ac-30\"\n})([\n    \"display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem;\"\n]);\n_c31 = TokenHeader;\nconst TokenSymbol = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__TokenSymbol\",\n    componentId: \"sc-d33395ac-31\"\n})([\n    \"color:white;font-weight:700;font-size:1.2rem;\"\n]);\n_c32 = TokenSymbol;\nconst TokenChange = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__TokenChange\",\n    componentId: \"sc-d33395ac-32\"\n})([\n    \"color:\",\n    \";font-weight:600;font-size:0.9rem;\"\n], (props)=>props.positive ? \"#4ade80\" : \"#ef4444\");\n_c33 = TokenChange;\nconst TokenBalance = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__TokenBalance\",\n    componentId: \"sc-d33395ac-33\"\n})([\n    \"color:white;font-size:1.5rem;font-weight:700;margin-bottom:0.5rem;\"\n]);\n_c34 = TokenBalance;\nconst TokenValue = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__TokenValue\",\n    componentId: \"sc-d33395ac-34\"\n})([\n    \"color:rgba(255,255,255,0.7);font-size:1rem;\"\n]);\n_c35 = TokenValue;\nconst NFTGrid = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__NFTGrid\",\n    componentId: \"sc-d33395ac-35\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1.5rem;\"\n]);\n_c36 = NFTGrid;\nconst NFTCard = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__NFTCard\",\n    componentId: \"sc-d33395ac-36\"\n})([\n    \"position:relative;background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;border:2px solid \",\n    \";overflow:hidden;&::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,\",\n    \"20,transparent);pointer-events:none;}\"\n], (props)=>getRarityColor(props.rarity), (props)=>getRarityColor(props.rarity));\n_c37 = NFTCard;\nconst NFTImage = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__NFTImage\",\n    componentId: \"sc-d33395ac-37\"\n})([\n    \"font-size:4rem;text-align:center;margin-bottom:1rem;filter:drop-shadow(0 0 20px currentColor);\"\n]);\n_c38 = NFTImage;\nconst NFTInfo = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__NFTInfo\",\n    componentId: \"sc-d33395ac-38\"\n})([\n    \"position:relative;z-index:1;\"\n]);\n_c39 = NFTInfo;\nconst NFTName = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__NFTName\",\n    componentId: \"sc-d33395ac-39\"\n})([\n    \"color:white;font-weight:700;font-size:1.1rem;margin-bottom:0.5rem;\"\n]);\n_c40 = NFTName;\nconst NFTRarity = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__NFTRarity\",\n    componentId: \"sc-d33395ac-40\"\n})([\n    \"color:\",\n    \";font-weight:600;font-size:0.8rem;text-transform:uppercase;margin-bottom:0.5rem;\"\n], (props)=>getRarityColor(props.rarity));\n_c41 = NFTRarity;\nconst NFTPower = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__NFTPower\",\n    componentId: \"sc-d33395ac-41\"\n})([\n    \"color:#fbbf24;font-weight:600;margin-bottom:0.5rem;\"\n]);\n_c42 = NFTPower;\nconst NFTPrice = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__NFTPrice\",\n    componentId: \"sc-d33395ac-42\"\n})([\n    \"color:rgba(255,255,255,0.8);font-size:0.9rem;\"\n]);\n_c43 = NFTPrice;\nconst StakedBadge = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__StakedBadge\",\n    componentId: \"sc-d33395ac-43\"\n})([\n    \"position:absolute;top:0.5rem;right:0.5rem;background:#4ade80;color:black;padding:0.25rem 0.5rem;border-radius:5px;font-size:0.7rem;font-weight:700;\"\n]);\n_c44 = StakedBadge;\nconst DeFiGrid = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__DeFiGrid\",\n    componentId: \"sc-d33395ac-44\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:1.5rem;\"\n]);\n_c45 = DeFiGrid;\nconst PoolCard = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__PoolCard\",\n    componentId: \"sc-d33395ac-45\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;border:1px solid rgba(255,255,255,0.1);\"\n]);\n_c46 = PoolCard;\nconst PoolHeader = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__PoolHeader\",\n    componentId: \"sc-d33395ac-46\"\n})([\n    \"display:flex;justify-content:space-between;align-items:center;margin-bottom:1.5rem;\"\n]);\n_c47 = PoolHeader;\nconst PoolName = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__PoolName\",\n    componentId: \"sc-d33395ac-47\"\n})([\n    \"color:white;font-weight:700;font-size:1.2rem;\"\n]);\n_c48 = PoolName;\nconst PoolAPR = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__PoolAPR\",\n    componentId: \"sc-d33395ac-48\"\n})([\n    \"color:#4ade80;font-weight:700;font-size:1.1rem;\"\n]);\n_c49 = PoolAPR;\nconst PoolStats = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__PoolStats\",\n    componentId: \"sc-d33395ac-49\"\n})([\n    \"display:grid;grid-template-columns:repeat(3,1fr);gap:1rem;margin-bottom:1.5rem;\"\n]);\n_c50 = PoolStats;\nconst PoolStat = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__PoolStat\",\n    componentId: \"sc-d33395ac-50\"\n})([\n    \"text-align:center;\"\n]);\n_c51 = PoolStat;\nconst PoolStatLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__PoolStatLabel\",\n    componentId: \"sc-d33395ac-51\"\n})([\n    \"color:rgba(255,255,255,0.6);font-size:0.8rem;margin-bottom:0.25rem;\"\n]);\n_c52 = PoolStatLabel;\nconst PoolStatValue = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__PoolStatValue\",\n    componentId: \"sc-d33395ac-52\"\n})([\n    \"color:white;font-weight:600;\"\n]);\n_c53 = PoolStatValue;\nconst PoolActions = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__PoolActions\",\n    componentId: \"sc-d33395ac-53\"\n})([\n    \"display:flex;gap:1rem;\"\n]);\n_c54 = PoolActions;\nconst PoolButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"Web3Dashboard__PoolButton\",\n    componentId: \"sc-d33395ac-54\"\n})([\n    \"flex:1;background:\",\n    \";color:white;border:\",\n    \";border-radius:8px;padding:0.75rem;font-weight:600;cursor:pointer;\"\n], (props)=>props.secondary ? \"transparent\" : \"linear-gradient(135deg, #4a90e2, #7b68ee)\", (props)=>props.secondary ? \"1px solid rgba(255, 255, 255, 0.3)\" : \"none\");\n_c55 = PoolButton;\nconst DAOSection = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__DAOSection\",\n    componentId: \"sc-d33395ac-55\"\n})([\n    \"\"\n]);\n_c56 = DAOSection;\nconst DAOStats = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__DAOStats\",\n    componentId: \"sc-d33395ac-56\"\n})([\n    \"display:grid;grid-template-columns:repeat(3,1fr);gap:1.5rem;margin-bottom:2rem;\"\n]);\n_c57 = DAOStats;\nconst DAOStat = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__DAOStat\",\n    componentId: \"sc-d33395ac-57\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;text-align:center;border:1px solid rgba(255,255,255,0.1);\"\n]);\n_c58 = DAOStat;\nconst DAOStatValue = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__DAOStatValue\",\n    componentId: \"sc-d33395ac-58\"\n})([\n    \"color:#4a90e2;font-size:2rem;font-weight:700;margin-bottom:0.5rem;\"\n]);\n_c59 = DAOStatValue;\nconst DAOStatLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__DAOStatLabel\",\n    componentId: \"sc-d33395ac-59\"\n})([\n    \"color:rgba(255,255,255,0.7);font-size:0.9rem;\"\n]);\n_c60 = DAOStatLabel;\nconst ProposalsList = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__ProposalsList\",\n    componentId: \"sc-d33395ac-60\"\n})([\n    \"\"\n]);\n_c61 = ProposalsList;\nconst ProposalCard = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__ProposalCard\",\n    componentId: \"sc-d33395ac-61\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;border:1px solid rgba(255,255,255,0.1);\"\n]);\n_c62 = ProposalCard;\nconst ProposalTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h4.withConfig({\n    displayName: \"Web3Dashboard__ProposalTitle\",\n    componentId: \"sc-d33395ac-62\"\n})([\n    \"color:white;font-size:1.2rem;font-weight:700;margin-bottom:0.5rem;\"\n]);\n_c63 = ProposalTitle;\nconst ProposalDescription = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p.withConfig({\n    displayName: \"Web3Dashboard__ProposalDescription\",\n    componentId: \"sc-d33395ac-63\"\n})([\n    \"color:rgba(255,255,255,0.7);margin-bottom:1.5rem;line-height:1.5;\"\n]);\n_c64 = ProposalDescription;\nconst ProposalVotes = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__ProposalVotes\",\n    componentId: \"sc-d33395ac-64\"\n})([\n    \"margin-bottom:1.5rem;\"\n]);\n_c65 = ProposalVotes;\nconst VoteOption = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__VoteOption\",\n    componentId: \"sc-d33395ac-65\"\n})([\n    \"margin-bottom:1rem;\"\n]);\n_c66 = VoteOption;\nconst VoteLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__VoteLabel\",\n    componentId: \"sc-d33395ac-66\"\n})([\n    \"color:white;font-size:0.9rem;margin-bottom:0.5rem;\"\n]);\n_c67 = VoteLabel;\nconst VoteBar = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__VoteBar\",\n    componentId: \"sc-d33395ac-67\"\n})([\n    \"width:100%;height:8px;background:rgba(255,255,255,0.1);border-radius:4px;overflow:hidden;&::after{content:'';display:block;width:\",\n    \"%;height:100%;background:linear-gradient(90deg,#4a90e2,#7b68ee);transition:width 0.8s ease;}\"\n], (props)=>props.width);\n_c68 = VoteBar;\nconst ProposalActions = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div.withConfig({\n    displayName: \"Web3Dashboard__ProposalActions\",\n    componentId: \"sc-d33395ac-68\"\n})([\n    \"display:flex;gap:1rem;\"\n]);\n_c69 = ProposalActions;\nconst VoteButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"Web3Dashboard__VoteButton\",\n    componentId: \"sc-d33395ac-69\"\n})([\n    \"flex:1;background:\",\n    \";color:white;border:\",\n    \";border-radius:8px;padding:0.75rem;font-weight:600;cursor:pointer;\"\n], (props)=>props.secondary ? \"transparent\" : \"linear-gradient(135deg, #4a90e2, #7b68ee)\", (props)=>props.secondary ? \"1px solid rgba(255, 255, 255, 0.3)\" : \"none\");\n_c70 = VoteButton;\n// Вспомогательная функция\nconst getRarityColor = (rarity)=>{\n    switch(rarity){\n        case \"legendary\":\n            return \"#ffd700\";\n        case \"epic\":\n            return \"#9370db\";\n        case \"rare\":\n            return \"#4a90e2\";\n        default:\n            return \"#6b7280\";\n    }\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (Web3Dashboard);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40, _c41, _c42, _c43, _c44, _c45, _c46, _c47, _c48, _c49, _c50, _c51, _c52, _c53, _c54, _c55, _c56, _c57, _c58, _c59, _c60, _c61, _c62, _c63, _c64, _c65, _c66, _c67, _c68, _c69, _c70;\n$RefreshReg$(_c, \"Web3Dashboard\");\n$RefreshReg$(_c1, \"Web3Container\");\n$RefreshReg$(_c2, \"ContentWrapper\");\n$RefreshReg$(_c3, \"SectionTitle\");\n$RefreshReg$(_c4, \"SectionSubtitle\");\n$RefreshReg$(_c5, \"WalletConnection\");\n$RefreshReg$(_c6, \"ConnectionCard\");\n$RefreshReg$(_c7, \"ConnectionIcon\");\n$RefreshReg$(_c8, \"ConnectionTitle\");\n$RefreshReg$(_c9, \"ConnectionDescription\");\n$RefreshReg$(_c10, \"WalletOptions\");\n$RefreshReg$(_c11, \"WalletOption\");\n$RefreshReg$(_c12, \"WalletIcon\");\n$RefreshReg$(_c13, \"WalletName\");\n$RefreshReg$(_c14, \"DashboardContent\");\n$RefreshReg$(_c15, \"DashboardHeader\");\n$RefreshReg$(_c16, \"UserInfo\");\n$RefreshReg$(_c17, \"UserAvatar\");\n$RefreshReg$(_c18, \"UserDetails\");\n$RefreshReg$(_c19, \"UserAddress\");\n$RefreshReg$(_c20, \"PortfolioValue\");\n$RefreshReg$(_c21, \"NetworkInfo\");\n$RefreshReg$(_c22, \"NetworkIndicator\");\n$RefreshReg$(_c23, \"NetworkName\");\n$RefreshReg$(_c24, \"TabNavigation\");\n$RefreshReg$(_c25, \"TabButton\");\n$RefreshReg$(_c26, \"TabIcon\");\n$RefreshReg$(_c27, \"TabLabel\");\n$RefreshReg$(_c28, \"TabContent\");\n$RefreshReg$(_c29, \"TokenGrid\");\n$RefreshReg$(_c30, \"TokenCard\");\n$RefreshReg$(_c31, \"TokenHeader\");\n$RefreshReg$(_c32, \"TokenSymbol\");\n$RefreshReg$(_c33, \"TokenChange\");\n$RefreshReg$(_c34, \"TokenBalance\");\n$RefreshReg$(_c35, \"TokenValue\");\n$RefreshReg$(_c36, \"NFTGrid\");\n$RefreshReg$(_c37, \"NFTCard\");\n$RefreshReg$(_c38, \"NFTImage\");\n$RefreshReg$(_c39, \"NFTInfo\");\n$RefreshReg$(_c40, \"NFTName\");\n$RefreshReg$(_c41, \"NFTRarity\");\n$RefreshReg$(_c42, \"NFTPower\");\n$RefreshReg$(_c43, \"NFTPrice\");\n$RefreshReg$(_c44, \"StakedBadge\");\n$RefreshReg$(_c45, \"DeFiGrid\");\n$RefreshReg$(_c46, \"PoolCard\");\n$RefreshReg$(_c47, \"PoolHeader\");\n$RefreshReg$(_c48, \"PoolName\");\n$RefreshReg$(_c49, \"PoolAPR\");\n$RefreshReg$(_c50, \"PoolStats\");\n$RefreshReg$(_c51, \"PoolStat\");\n$RefreshReg$(_c52, \"PoolStatLabel\");\n$RefreshReg$(_c53, \"PoolStatValue\");\n$RefreshReg$(_c54, \"PoolActions\");\n$RefreshReg$(_c55, \"PoolButton\");\n$RefreshReg$(_c56, \"DAOSection\");\n$RefreshReg$(_c57, \"DAOStats\");\n$RefreshReg$(_c58, \"DAOStat\");\n$RefreshReg$(_c59, \"DAOStatValue\");\n$RefreshReg$(_c60, \"DAOStatLabel\");\n$RefreshReg$(_c61, \"ProposalsList\");\n$RefreshReg$(_c62, \"ProposalCard\");\n$RefreshReg$(_c63, \"ProposalTitle\");\n$RefreshReg$(_c64, \"ProposalDescription\");\n$RefreshReg$(_c65, \"ProposalVotes\");\n$RefreshReg$(_c66, \"VoteOption\");\n$RefreshReg$(_c67, \"VoteLabel\");\n$RefreshReg$(_c68, \"VoteBar\");\n$RefreshReg$(_c69, \"ProposalActions\");\n$RefreshReg$(_c70, \"VoteButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Web3Dashboard.tsx\n"));

/***/ })

}]);