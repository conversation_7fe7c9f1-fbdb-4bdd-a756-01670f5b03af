"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\n\nconst HomePage = ()=>{\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleStartPlaying = ()=>{\n        router.push(\"/games/durak\");\n    };\n    const handleMultiplayer = ()=>{\n        router.push(\"/multiplayer\");\n    };\n    const handleViewTournaments = ()=>{\n        router.push(\"/tournaments\");\n    };\n    const handleViewTutorials = ()=>{\n        router.push(\"/tutorials\");\n    };\n    const handleViewProfile = ()=>{\n        router.push(\"/profile\");\n    };\n    const handleViewLeaderboard = ()=>{\n        router.push(\"/leaderboard\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Козырь Мастер - Карточные игры онлайн\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Играйте в популярные карточные игры онлайн с друзьями\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Main, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Hero, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                children: \"Добро пожаловать в Козырь Мастер\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Subtitle, {\n                                children: \"Платформа для игры в популярные карточные игры онлайн\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContainer, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDFAE\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Одиночная игра\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Играйте против бота и оттачивайте свои навыки\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StartButton, {\n                                        onClick: handleStartPlaying,\n                                        children: \"Играть против бота\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDF10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Многопользовательская игра\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Играйте с другими игроками онлайн в реальном времени\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MultiplayerButton, {\n                                        onClick: handleMultiplayer,\n                                        children: \"Играть онлайн\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDFC6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Турниры\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Участвуйте в турнирах и выигрывайте призы\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewTournaments,\n                                        children: \"Смотреть турниры\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83D\\uDCDA\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Обучение\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Изучите правила игр и стратегии\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewTutorials,\n                                        children: \"Изучить правила\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83D\\uDC64\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Профиль\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Просмотрите свою статистику и достижения\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewProfile,\n                                        children: \"Мой профиль\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDFC6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Лидеры\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Таблица лидеров и лучшие игроки\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewLeaderboard,\n                                        children: \"Посмотреть рейтинг\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"\\xa9 2023 Козырь Мастер. Все права защищены.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HomePage, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = HomePage;\n// Стилизованные компоненты\nconst Container = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__Container\",\n    componentId: \"sc-287450ca-0\"\n})([\n    \"min-height:100vh;display:flex;flex-direction:column;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);\"\n]);\n_c1 = Container;\nconst Main = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].main.withConfig({\n    displayName: \"pages__Main\",\n    componentId: \"sc-287450ca-1\"\n})([\n    \"flex:1;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:2rem;\"\n]);\n_c2 = Main;\nconst Hero = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__Hero\",\n    componentId: \"sc-287450ca-2\"\n})([\n    \"text-align:center;margin-bottom:4rem;\"\n]);\n_c3 = Hero;\nconst Title = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].h1.withConfig({\n    displayName: \"pages__Title\",\n    componentId: \"sc-287450ca-3\"\n})([\n    \"font-size:3.5rem;margin-bottom:1rem;color:white;text-shadow:2px 2px 4px rgba(0,0,0,0.3);font-weight:700;@media (max-width:768px){font-size:2.5rem;}\"\n]);\n_c4 = Title;\nconst Subtitle = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].p.withConfig({\n    displayName: \"pages__Subtitle\",\n    componentId: \"sc-287450ca-4\"\n})([\n    \"font-size:1.5rem;color:rgba(255,255,255,0.9);text-shadow:1px 1px 2px rgba(0,0,0,0.3);max-width:600px;margin:0 auto;@media (max-width:768px){font-size:1.2rem;}\"\n]);\n_c5 = Subtitle;\nconst CardContainer = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__CardContainer\",\n    componentId: \"sc-287450ca-5\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:2rem;max-width:1200px;width:100%;\"\n]);\n_c6 = CardContainer;\nconst GameCard = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__GameCard\",\n    componentId: \"sc-287450ca-6\"\n})([\n    \"background:rgba(255,255,255,0.95);border-radius:16px;box-shadow:0 8px 32px rgba(0,0,0,0.1);padding:2rem;text-align:center;transition:all 0.3s ease;backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,0.2);&:hover{transform:translateY(-8px);box-shadow:0 16px 48px rgba(0,0,0,0.2);}\"\n]);\n_c7 = GameCard;\nconst CardIcon = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__CardIcon\",\n    componentId: \"sc-287450ca-7\"\n})([\n    \"font-size:3rem;margin-bottom:1rem;\"\n]);\n_c8 = CardIcon;\nconst CardTitle = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].h2.withConfig({\n    displayName: \"pages__CardTitle\",\n    componentId: \"sc-287450ca-8\"\n})([\n    \"font-size:1.8rem;margin-bottom:1rem;color:#333;font-weight:600;\"\n]);\n_c9 = CardTitle;\nconst CardDescription = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].p.withConfig({\n    displayName: \"pages__CardDescription\",\n    componentId: \"sc-287450ca-9\"\n})([\n    \"font-size:1rem;color:#666;margin-bottom:2rem;line-height:1.6;\"\n]);\n_c10 = CardDescription;\nconst StartButton = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].button.withConfig({\n    displayName: \"pages__StartButton\",\n    componentId: \"sc-287450ca-10\"\n})([\n    \"background:linear-gradient(135deg,#4CAF50,#45a049);color:white;border:none;border-radius:8px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(76,175,80,0.3);&:hover{background:linear-gradient(135deg,#45a049,#4CAF50);transform:translateY(-2px);box-shadow:0 6px 20px rgba(76,175,80,0.4);}&:active{transform:translateY(0);}\"\n]);\n_c11 = StartButton;\nconst MultiplayerButton = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].button.withConfig({\n    displayName: \"pages__MultiplayerButton\",\n    componentId: \"sc-287450ca-11\"\n})([\n    \"background:linear-gradient(135deg,#FF6B35,#F7931E);color:white;border:none;border-radius:8px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(255,107,53,0.3);&:hover{background:linear-gradient(135deg,#F7931E,#FF6B35);transform:translateY(-2px);box-shadow:0 6px 20px rgba(255,107,53,0.4);}&:active{transform:translateY(0);}\"\n]);\n_c12 = MultiplayerButton;\nconst ActionButton = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].button.withConfig({\n    displayName: \"pages__ActionButton\",\n    componentId: \"sc-287450ca-12\"\n})([\n    \"background:linear-gradient(135deg,#2196F3,#1976D2);color:white;border:none;border-radius:8px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(33,150,243,0.3);&:hover{background:linear-gradient(135deg,#1976D2,#2196F3);transform:translateY(-2px);box-shadow:0 6px 20px rgba(33,150,243,0.4);}&:active{transform:translateY(0);}\"\n]);\n_c13 = ActionButton;\nconst Footer = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].footer.withConfig({\n    displayName: \"pages__Footer\",\n    componentId: \"sc-287450ca-13\"\n})([\n    \"width:100%;padding:1.5rem;text-align:center;background:rgba(0,0,0,0.1);color:rgba(255,255,255,0.8);backdrop-filter:blur(10px);\"\n]);\n_c14 = Footer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"HomePage\");\n$RefreshReg$(_c1, \"Container\");\n$RefreshReg$(_c2, \"Main\");\n$RefreshReg$(_c3, \"Hero\");\n$RefreshReg$(_c4, \"Title\");\n$RefreshReg$(_c5, \"Subtitle\");\n$RefreshReg$(_c6, \"CardContainer\");\n$RefreshReg$(_c7, \"GameCard\");\n$RefreshReg$(_c8, \"CardIcon\");\n$RefreshReg$(_c9, \"CardTitle\");\n$RefreshReg$(_c10, \"CardDescription\");\n$RefreshReg$(_c11, \"StartButton\");\n$RefreshReg$(_c12, \"MultiplayerButton\");\n$RefreshReg$(_c13, \"ActionButton\");\n$RefreshReg$(_c14, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n"));

/***/ })

});