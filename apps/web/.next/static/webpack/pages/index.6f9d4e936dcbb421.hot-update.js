"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\n\nconst HomePage = ()=>{\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleStartPlaying = ()=>{\n        router.push(\"/games/durak\");\n    };\n    const handleMultiplayer = ()=>{\n        router.push(\"/multiplayer\");\n    };\n    const handleViewTournaments = ()=>{\n        router.push(\"/tournaments\");\n    };\n    const handleViewTutorials = ()=>{\n        router.push(\"/tutorials\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Козырь Мастер - Карточные игры онлайн\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Играйте в популярные карточные игры онлайн с друзьями\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Main, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Hero, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                children: \"Добро пожаловать в Козырь Мастер\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Subtitle, {\n                                children: \"Платформа для игры в популярные карточные игры онлайн\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContainer, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDFAE\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Одиночная игра\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Играйте против бота и оттачивайте свои навыки\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StartButton, {\n                                        onClick: handleStartPlaying,\n                                        children: \"Играть против бота\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDF10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Многопользовательская игра\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Играйте с другими игроками онлайн в реальном времени\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MultiplayerButton, {\n                                        onClick: handleMultiplayer,\n                                        children: \"Играть онлайн\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDFC6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Турниры\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Участвуйте в турнирах и выигрывайте призы\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewTournaments,\n                                        children: \"Смотреть турниры\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83D\\uDCDA\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Обучение\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Изучите правила игр и стратегии\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewTutorials,\n                                        children: \"Изучить правила\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"\\xa9 2023 Козырь Мастер. Все права защищены.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HomePage, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = HomePage;\n// Стилизованные компоненты\nconst Container = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__Container\",\n    componentId: \"sc-1e819ad2-0\"\n})([\n    \"min-height:100vh;display:flex;flex-direction:column;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);\"\n]);\n_c1 = Container;\nconst Main = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].main.withConfig({\n    displayName: \"pages__Main\",\n    componentId: \"sc-1e819ad2-1\"\n})([\n    \"flex:1;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:2rem;\"\n]);\n_c2 = Main;\nconst Hero = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__Hero\",\n    componentId: \"sc-1e819ad2-2\"\n})([\n    \"text-align:center;margin-bottom:4rem;\"\n]);\n_c3 = Hero;\nconst Title = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].h1.withConfig({\n    displayName: \"pages__Title\",\n    componentId: \"sc-1e819ad2-3\"\n})([\n    \"font-size:3.5rem;margin-bottom:1rem;color:white;text-shadow:2px 2px 4px rgba(0,0,0,0.3);font-weight:700;@media (max-width:768px){font-size:2.5rem;}\"\n]);\n_c4 = Title;\nconst Subtitle = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].p.withConfig({\n    displayName: \"pages__Subtitle\",\n    componentId: \"sc-1e819ad2-4\"\n})([\n    \"font-size:1.5rem;color:rgba(255,255,255,0.9);text-shadow:1px 1px 2px rgba(0,0,0,0.3);max-width:600px;margin:0 auto;@media (max-width:768px){font-size:1.2rem;}\"\n]);\n_c5 = Subtitle;\nconst CardContainer = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__CardContainer\",\n    componentId: \"sc-1e819ad2-5\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:2rem;max-width:1200px;width:100%;\"\n]);\n_c6 = CardContainer;\nconst GameCard = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__GameCard\",\n    componentId: \"sc-1e819ad2-6\"\n})([\n    \"background:rgba(255,255,255,0.95);border-radius:16px;box-shadow:0 8px 32px rgba(0,0,0,0.1);padding:2rem;text-align:center;transition:all 0.3s ease;backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,0.2);&:hover{transform:translateY(-8px);box-shadow:0 16px 48px rgba(0,0,0,0.2);}\"\n]);\n_c7 = GameCard;\nconst CardIcon = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__CardIcon\",\n    componentId: \"sc-1e819ad2-7\"\n})([\n    \"font-size:3rem;margin-bottom:1rem;\"\n]);\n_c8 = CardIcon;\nconst CardTitle = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].h2.withConfig({\n    displayName: \"pages__CardTitle\",\n    componentId: \"sc-1e819ad2-8\"\n})([\n    \"font-size:1.8rem;margin-bottom:1rem;color:#333;font-weight:600;\"\n]);\n_c9 = CardTitle;\nconst CardDescription = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].p.withConfig({\n    displayName: \"pages__CardDescription\",\n    componentId: \"sc-1e819ad2-9\"\n})([\n    \"font-size:1rem;color:#666;margin-bottom:2rem;line-height:1.6;\"\n]);\n_c10 = CardDescription;\nconst StartButton = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].button.withConfig({\n    displayName: \"pages__StartButton\",\n    componentId: \"sc-1e819ad2-10\"\n})([\n    \"background:linear-gradient(135deg,#4CAF50,#45a049);color:white;border:none;border-radius:8px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(76,175,80,0.3);&:hover{background:linear-gradient(135deg,#45a049,#4CAF50);transform:translateY(-2px);box-shadow:0 6px 20px rgba(76,175,80,0.4);}&:active{transform:translateY(0);}\"\n]);\n_c11 = StartButton;\nconst MultiplayerButton = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].button.withConfig({\n    displayName: \"pages__MultiplayerButton\",\n    componentId: \"sc-1e819ad2-11\"\n})([\n    \"background:linear-gradient(135deg,#FF6B35,#F7931E);color:white;border:none;border-radius:8px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(255,107,53,0.3);&:hover{background:linear-gradient(135deg,#F7931E,#FF6B35);transform:translateY(-2px);box-shadow:0 6px 20px rgba(255,107,53,0.4);}&:active{transform:translateY(0);}\"\n]);\n_c12 = MultiplayerButton;\nconst ActionButton = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].button.withConfig({\n    displayName: \"pages__ActionButton\",\n    componentId: \"sc-1e819ad2-12\"\n})([\n    \"background:linear-gradient(135deg,#2196F3,#1976D2);color:white;border:none;border-radius:8px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(33,150,243,0.3);&:hover{background:linear-gradient(135deg,#1976D2,#2196F3);transform:translateY(-2px);box-shadow:0 6px 20px rgba(33,150,243,0.4);}&:active{transform:translateY(0);}\"\n]);\n_c13 = ActionButton;\nconst Footer = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].footer.withConfig({\n    displayName: \"pages__Footer\",\n    componentId: \"sc-1e819ad2-13\"\n})([\n    \"width:100%;padding:1.5rem;text-align:center;background:rgba(0,0,0,0.1);color:rgba(255,255,255,0.8);backdrop-filter:blur(10px);\"\n]);\n_c14 = Footer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"HomePage\");\n$RefreshReg$(_c1, \"Container\");\n$RefreshReg$(_c2, \"Main\");\n$RefreshReg$(_c3, \"Hero\");\n$RefreshReg$(_c4, \"Title\");\n$RefreshReg$(_c5, \"Subtitle\");\n$RefreshReg$(_c6, \"CardContainer\");\n$RefreshReg$(_c7, \"GameCard\");\n$RefreshReg$(_c8, \"CardIcon\");\n$RefreshReg$(_c9, \"CardTitle\");\n$RefreshReg$(_c10, \"CardDescription\");\n$RefreshReg$(_c11, \"StartButton\");\n$RefreshReg$(_c12, \"MultiplayerButton\");\n$RefreshReg$(_c13, \"ActionButton\");\n$RefreshReg$(_c14, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n"));

/***/ })

});