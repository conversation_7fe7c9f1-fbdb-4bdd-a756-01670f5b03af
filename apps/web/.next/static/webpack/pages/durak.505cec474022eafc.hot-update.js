"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/durak",{

/***/ "./src/hooks/useEmotionalAI.ts":
/*!*************************************!*\
  !*** ./src/hooks/useEmotionalAI.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEmotionalAI: function() { return /* binding */ useEmotionalAI; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nvar _s = $RefreshSig$();\n\nconst useEmotionalAI = () => {\n  _s();\n  const [emotionalState, setEmotionalState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    metrics: {\n      happiness: 0.5,\n      focus: 0.5,\n      confidence: 0.5,\n      stress: 0.5,\n      motivation: 0.5,\n      fatigue: 0.5\n    },\n    overallMood: 'neutral',\n    recommendations: [],\n    lastAnalysis: new Date(),\n    isAnalyzing: false\n  });\n  const [gameBehavior, setGameBehavior] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    decisionTime: 5000,\n    riskTolerance: 0.5,\n    aggressiveness: 0.5,\n    consistency: 0.5\n  });\n  const analyzeUser = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async inputData => {\n    setEmotionalState(prev => ({\n      ...prev,\n      isAnalyzing: true\n    }));\n    try {\n      // Симуляция анализа эмоций\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      const newMetrics = {\n        happiness: Math.max(0, Math.min(1, 0.7 + (Math.random() - 0.5) * 0.4)),\n        focus: Math.max(0, Math.min(1, 0.8 + (Math.random() - 0.5) * 0.3)),\n        confidence: Math.max(0, Math.min(1, 0.6 + (Math.random() - 0.5) * 0.5)),\n        stress: Math.max(0, Math.min(1, 0.3 + (Math.random() - 0.5) * 0.4)),\n        motivation: Math.max(0, Math.min(1, 0.75 + (Math.random() - 0.5) * 0.3)),\n        fatigue: Math.max(0, Math.min(1, 0.4 + (Math.random() - 0.5) * 0.4))\n      };\n\n      // Определяем общее настроение\n      const averagePositive = (newMetrics.happiness + newMetrics.focus + newMetrics.confidence + newMetrics.motivation) / 4;\n      const averageNegative = (newMetrics.stress + newMetrics.fatigue) / 2;\n      const overallScore = (averagePositive - averageNegative + 1) / 2; // Нормализация от 0 до 1\n\n      let overallMood;\n      if (overallScore > 0.8) overallMood = 'excellent';else if (overallScore > 0.6) overallMood = 'good';else if (overallScore > 0.4) overallMood = 'neutral';else if (overallScore > 0.2) overallMood = 'poor';else overallMood = 'critical';\n\n      // Генерируем рекомендации\n      const recommendations = generateRecommendations(newMetrics, overallMood);\n\n      // Обновляем игровое поведение\n      setGameBehavior({\n        decisionTime: 3000 + (1 - newMetrics.focus) * 5000,\n        riskTolerance: newMetrics.confidence * 0.8 + newMetrics.motivation * 0.2,\n        aggressiveness: newMetrics.confidence * 0.6 + (1 - newMetrics.stress) * 0.4,\n        consistency: newMetrics.focus * 0.7 + (1 - newMetrics.fatigue) * 0.3\n      });\n      setEmotionalState({\n        metrics: newMetrics,\n        overallMood,\n        recommendations,\n        lastAnalysis: new Date(),\n        isAnalyzing: false\n      });\n    } catch (error) {\n      console.error('Emotional analysis error:', error);\n      setEmotionalState(prev => ({\n        ...prev,\n        isAnalyzing: false\n      }));\n    }\n  }, []);\n  const generateRecommendations = (metrics, mood) => {\n    const recommendations = [];\n    if (metrics.stress > 0.7) {\n      recommendations.push('Рекомендуем сделать перерыв для снижения стресса');\n    }\n    if (metrics.fatigue > 0.8) {\n      recommendations.push('Высокий уровень усталости - время отдохнуть');\n    }\n    if (metrics.focus < 0.4) {\n      recommendations.push('Попробуйте упражнения на концентрацию');\n    }\n    if (metrics.confidence < 0.3) {\n      recommendations.push('Начните с более простых игр для повышения уверенности');\n    }\n    if (metrics.motivation < 0.4) {\n      recommendations.push('Установите небольшие достижимые цели');\n    }\n    if (mood === 'excellent') {\n      recommendations.push('Отличное состояние! Время для сложных вызовов');\n    }\n    return recommendations;\n  };\n\n  // Автоматическое обновление метрик\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const interval = setInterval(() => {\n      setEmotionalState(prev => {\n        if (prev.isAnalyzing) return prev;\n        const updatedMetrics = {\n          ...prev.metrics\n        };\n\n        // Небольшие случайные изменения\n        Object.keys(updatedMetrics).forEach(key => {\n          const currentValue = updatedMetrics[key];\n          const change = (Math.random() - 0.5) * 0.05;\n          updatedMetrics[key] = Math.max(0, Math.min(1, currentValue + change));\n        });\n        return {\n          ...prev,\n          metrics: updatedMetrics\n        };\n      });\n    }, 10000);\n    return () => clearInterval(interval);\n  }, []);\n  const getEmotionalInsight = () => {\n    const {\n      metrics,\n      overallMood\n    } = emotionalState;\n    return {\n      primaryEmotion: Object.entries(metrics).reduce((a, b) => metrics[a[0]] > metrics[b[0]] ? a : b)[0],\n      moodDescription: getMoodDescription(overallMood),\n      playabilityScore: (metrics.focus + metrics.motivation + (1 - metrics.fatigue) + (1 - metrics.stress)) / 4,\n      suggestedGameType: getSuggestedGameType(metrics)\n    };\n  };\n  const getMoodDescription = mood => {\n    switch (mood) {\n      case 'excellent':\n        return 'Превосходное настроение! Готовы к любым вызовам';\n      case 'good':\n        return 'Хорошее настроение, отличное время для игр';\n      case 'neutral':\n        return 'Нейтральное состояние, можно играть в спокойном темпе';\n      case 'poor':\n        return 'Не лучшее настроение, рекомендуем легкие игры';\n      case 'critical':\n        return 'Критическое состояние, лучше отдохнуть';\n      default:\n        return 'Анализ настроения...';\n    }\n  };\n  const getSuggestedGameType = metrics => {\n    if (metrics.focus > 0.8 && metrics.confidence > 0.7) {\n      return 'Турнирные игры высокого уровня';\n    } else if (metrics.stress < 0.3 && metrics.motivation > 0.6) {\n      return 'Соревновательные игры';\n    } else if (metrics.fatigue > 0.7) {\n      return 'Казуальные игры';\n    } else {\n      return 'Обучающие игры';\n    }\n  };\n  return {\n    emotionalState,\n    gameBehavior,\n    analyzeUser,\n    getEmotionalInsight\n  };\n};\n_s(useEmotionalAI, \"CvSOEtnuDhwVXQmLMQ0b8B5RuOc=\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvaG9va3MvdXNlRW1vdGlvbmFsQUkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF5RDtBQTBCbEQsTUFBTUcsY0FBYyxHQUFHQSxDQUFBLEtBQU07RUFBQUMsRUFBQTtFQUNsQyxNQUFNLENBQUNDLGNBQWMsRUFBRUMsaUJBQWlCLENBQUMsR0FBR04sK0NBQVEsQ0FBaUI7SUFDbkVPLE9BQU8sRUFBRTtNQUNQQyxTQUFTLEVBQUUsR0FBRztNQUNkQyxLQUFLLEVBQUUsR0FBRztNQUNWQyxVQUFVLEVBQUUsR0FBRztNQUNmQyxNQUFNLEVBQUUsR0FBRztNQUNYQyxVQUFVLEVBQUUsR0FBRztNQUNmQyxPQUFPLEVBQUU7SUFDWCxDQUFDO0lBQ0RDLFdBQVcsRUFBRSxTQUFTO0lBQ3RCQyxlQUFlLEVBQUUsRUFBRTtJQUNuQkMsWUFBWSxFQUFFLElBQUlDLElBQUksQ0FBQyxDQUFDO0lBQ3hCQyxXQUFXLEVBQUU7RUFDZixDQUFDLENBQUM7RUFFRixNQUFNLENBQUNDLFlBQVksRUFBRUMsZUFBZSxDQUFDLEdBQUdwQiwrQ0FBUSxDQUFlO0lBQzdEcUIsWUFBWSxFQUFFLElBQUk7SUFDbEJDLGFBQWEsRUFBRSxHQUFHO0lBQ2xCQyxjQUFjLEVBQUUsR0FBRztJQUNuQkMsV0FBVyxFQUFFO0VBQ2YsQ0FBQyxDQUFDO0VBRUYsTUFBTUMsV0FBVyxHQUFHdkIsa0RBQVcsQ0FBQyxNQUFPd0IsU0FBZSxJQUFLO0lBQ3pEcEIsaUJBQWlCLENBQUNxQixJQUFJLEtBQUs7TUFBRSxHQUFHQSxJQUFJO01BQUVULFdBQVcsRUFBRTtJQUFLLENBQUMsQ0FBQyxDQUFDO0lBRTNELElBQUk7TUFDRjtNQUNBLE1BQU0sSUFBSVUsT0FBTyxDQUFDQyxPQUFPLElBQUlDLFVBQVUsQ0FBQ0QsT0FBTyxFQUFFLElBQUksQ0FBQyxDQUFDO01BRXZELE1BQU1FLFVBQTRCLEdBQUc7UUFDbkN2QixTQUFTLEVBQUV3QixJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDLEVBQUVELElBQUksQ0FBQ0UsR0FBRyxDQUFDLENBQUMsRUFBRSxHQUFHLEdBQUcsQ0FBQ0YsSUFBSSxDQUFDRyxNQUFNLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBSSxHQUFHLENBQUMsQ0FBQztRQUN0RTFCLEtBQUssRUFBRXVCLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUMsRUFBRUQsSUFBSSxDQUFDRSxHQUFHLENBQUMsQ0FBQyxFQUFFLEdBQUcsR0FBRyxDQUFDRixJQUFJLENBQUNHLE1BQU0sQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFJLEdBQUcsQ0FBQyxDQUFDO1FBQ2xFekIsVUFBVSxFQUFFc0IsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxFQUFFRCxJQUFJLENBQUNFLEdBQUcsQ0FBQyxDQUFDLEVBQUUsR0FBRyxHQUFHLENBQUNGLElBQUksQ0FBQ0csTUFBTSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUksR0FBRyxDQUFDLENBQUM7UUFDdkV4QixNQUFNLEVBQUVxQixJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDLEVBQUVELElBQUksQ0FBQ0UsR0FBRyxDQUFDLENBQUMsRUFBRSxHQUFHLEdBQUcsQ0FBQ0YsSUFBSSxDQUFDRyxNQUFNLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBSSxHQUFHLENBQUMsQ0FBQztRQUNuRXZCLFVBQVUsRUFBRW9CLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUMsRUFBRUQsSUFBSSxDQUFDRSxHQUFHLENBQUMsQ0FBQyxFQUFFLElBQUksR0FBRyxDQUFDRixJQUFJLENBQUNHLE1BQU0sQ0FBQyxDQUFDLEdBQUcsR0FBRyxJQUFJLEdBQUcsQ0FBQyxDQUFDO1FBQ3hFdEIsT0FBTyxFQUFFbUIsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxFQUFFRCxJQUFJLENBQUNFLEdBQUcsQ0FBQyxDQUFDLEVBQUUsR0FBRyxHQUFHLENBQUNGLElBQUksQ0FBQ0csTUFBTSxDQUFDLENBQUMsR0FBRyxHQUFHLElBQUksR0FBRyxDQUFDO01BQ3JFLENBQUM7O01BRUQ7TUFDQSxNQUFNQyxlQUFlLEdBQUcsQ0FBQ0wsVUFBVSxDQUFDdkIsU0FBUyxHQUFHdUIsVUFBVSxDQUFDdEIsS0FBSyxHQUFHc0IsVUFBVSxDQUFDckIsVUFBVSxHQUFHcUIsVUFBVSxDQUFDbkIsVUFBVSxJQUFJLENBQUM7TUFDckgsTUFBTXlCLGVBQWUsR0FBRyxDQUFDTixVQUFVLENBQUNwQixNQUFNLEdBQUdvQixVQUFVLENBQUNsQixPQUFPLElBQUksQ0FBQztNQUNwRSxNQUFNeUIsWUFBWSxHQUFHLENBQUNGLGVBQWUsR0FBR0MsZUFBZSxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQzs7TUFFbEUsSUFBSXZCLFdBQTBDO01BQzlDLElBQUl3QixZQUFZLEdBQUcsR0FBRyxFQUFFeEIsV0FBVyxHQUFHLFdBQVcsQ0FBQyxLQUM3QyxJQUFJd0IsWUFBWSxHQUFHLEdBQUcsRUFBRXhCLFdBQVcsR0FBRyxNQUFNLENBQUMsS0FDN0MsSUFBSXdCLFlBQVksR0FBRyxHQUFHLEVBQUV4QixXQUFXLEdBQUcsU0FBUyxDQUFDLEtBQ2hELElBQUl3QixZQUFZLEdBQUcsR0FBRyxFQUFFeEIsV0FBVyxHQUFHLE1BQU0sQ0FBQyxLQUM3Q0EsV0FBVyxHQUFHLFVBQVU7O01BRTdCO01BQ0EsTUFBTUMsZUFBZSxHQUFHd0IsdUJBQXVCLENBQUNSLFVBQVUsRUFBRWpCLFdBQVcsQ0FBQzs7TUFFeEU7TUFDQU0sZUFBZSxDQUFDO1FBQ2RDLFlBQVksRUFBRSxJQUFJLEdBQUcsQ0FBQyxDQUFDLEdBQUdVLFVBQVUsQ0FBQ3RCLEtBQUssSUFBSSxJQUFJO1FBQ2xEYSxhQUFhLEVBQUVTLFVBQVUsQ0FBQ3JCLFVBQVUsR0FBRyxHQUFHLEdBQUdxQixVQUFVLENBQUNuQixVQUFVLEdBQUcsR0FBRztRQUN4RVcsY0FBYyxFQUFFUSxVQUFVLENBQUNyQixVQUFVLEdBQUcsR0FBRyxHQUFHLENBQUMsQ0FBQyxHQUFHcUIsVUFBVSxDQUFDcEIsTUFBTSxJQUFJLEdBQUc7UUFDM0VhLFdBQVcsRUFBRU8sVUFBVSxDQUFDdEIsS0FBSyxHQUFHLEdBQUcsR0FBRyxDQUFDLENBQUMsR0FBR3NCLFVBQVUsQ0FBQ2xCLE9BQU8sSUFBSTtNQUNuRSxDQUFDLENBQUM7TUFFRlAsaUJBQWlCLENBQUM7UUFDaEJDLE9BQU8sRUFBRXdCLFVBQVU7UUFDbkJqQixXQUFXO1FBQ1hDLGVBQWU7UUFDZkMsWUFBWSxFQUFFLElBQUlDLElBQUksQ0FBQyxDQUFDO1FBQ3hCQyxXQUFXLEVBQUU7TUFDZixDQUFDLENBQUM7SUFFSixDQUFDLENBQUMsT0FBT3NCLEtBQUssRUFBRTtNQUNkQyxPQUFPLENBQUNELEtBQUssQ0FBQywyQkFBMkIsRUFBRUEsS0FBSyxDQUFDO01BQ2pEbEMsaUJBQWlCLENBQUNxQixJQUFJLEtBQUs7UUFBRSxHQUFHQSxJQUFJO1FBQUVULFdBQVcsRUFBRTtNQUFNLENBQUMsQ0FBQyxDQUFDO0lBQzlEO0VBQ0YsQ0FBQyxFQUFFLEVBQUUsQ0FBQztFQUVOLE1BQU1xQix1QkFBdUIsR0FBR0EsQ0FBQ2hDLE9BQXlCLEVBQUVtQyxJQUFtQyxLQUFlO0lBQzVHLE1BQU0zQixlQUF5QixHQUFHLEVBQUU7SUFFcEMsSUFBSVIsT0FBTyxDQUFDSSxNQUFNLEdBQUcsR0FBRyxFQUFFO01BQ3hCSSxlQUFlLENBQUM0QixJQUFJLENBQUMsa0RBQWtELENBQUM7SUFDMUU7SUFFQSxJQUFJcEMsT0FBTyxDQUFDTSxPQUFPLEdBQUcsR0FBRyxFQUFFO01BQ3pCRSxlQUFlLENBQUM0QixJQUFJLENBQUMsNkNBQTZDLENBQUM7SUFDckU7SUFFQSxJQUFJcEMsT0FBTyxDQUFDRSxLQUFLLEdBQUcsR0FBRyxFQUFFO01BQ3ZCTSxlQUFlLENBQUM0QixJQUFJLENBQUMsdUNBQXVDLENBQUM7SUFDL0Q7SUFFQSxJQUFJcEMsT0FBTyxDQUFDRyxVQUFVLEdBQUcsR0FBRyxFQUFFO01BQzVCSyxlQUFlLENBQUM0QixJQUFJLENBQUMsdURBQXVELENBQUM7SUFDL0U7SUFFQSxJQUFJcEMsT0FBTyxDQUFDSyxVQUFVLEdBQUcsR0FBRyxFQUFFO01BQzVCRyxlQUFlLENBQUM0QixJQUFJLENBQUMsc0NBQXNDLENBQUM7SUFDOUQ7SUFFQSxJQUFJRCxJQUFJLEtBQUssV0FBVyxFQUFFO01BQ3hCM0IsZUFBZSxDQUFDNEIsSUFBSSxDQUFDLCtDQUErQyxDQUFDO0lBQ3ZFO0lBRUEsT0FBTzVCLGVBQWU7RUFDeEIsQ0FBQzs7RUFFRDtFQUNBZCxnREFBUyxDQUFDLE1BQU07SUFDZCxNQUFNMkMsUUFBUSxHQUFHQyxXQUFXLENBQUMsTUFBTTtNQUNqQ3ZDLGlCQUFpQixDQUFDcUIsSUFBSSxJQUFJO1FBQ3hCLElBQUlBLElBQUksQ0FBQ1QsV0FBVyxFQUFFLE9BQU9TLElBQUk7UUFFakMsTUFBTW1CLGNBQWMsR0FBRztVQUFFLEdBQUduQixJQUFJLENBQUNwQjtRQUFRLENBQUM7O1FBRTFDO1FBQ0F3QyxNQUFNLENBQUNDLElBQUksQ0FBQ0YsY0FBYyxDQUFDLENBQUNHLE9BQU8sQ0FBQ0MsR0FBRyxJQUFJO1VBQ3pDLE1BQU1DLFlBQVksR0FBR0wsY0FBYyxDQUFDSSxHQUFHLENBQTJCO1VBQ2xFLE1BQU1FLE1BQU0sR0FBRyxDQUFDcEIsSUFBSSxDQUFDRyxNQUFNLENBQUMsQ0FBQyxHQUFHLEdBQUcsSUFBSSxJQUFJO1VBQzNDVyxjQUFjLENBQUNJLEdBQUcsQ0FBMkIsR0FBR2xCLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUMsRUFBRUQsSUFBSSxDQUFDRSxHQUFHLENBQUMsQ0FBQyxFQUFFaUIsWUFBWSxHQUFHQyxNQUFNLENBQUMsQ0FBQztRQUNqRyxDQUFDLENBQUM7UUFFRixPQUFPO1VBQ0wsR0FBR3pCLElBQUk7VUFDUHBCLE9BQU8sRUFBRXVDO1FBQ1gsQ0FBQztNQUNILENBQUMsQ0FBQztJQUNKLENBQUMsRUFBRSxLQUFLLENBQUM7SUFFVCxPQUFPLE1BQU1PLGFBQWEsQ0FBQ1QsUUFBUSxDQUFDO0VBQ3RDLENBQUMsRUFBRSxFQUFFLENBQUM7RUFFTixNQUFNVSxtQkFBbUIsR0FBR0EsQ0FBQSxLQUFNO0lBQ2hDLE1BQU07TUFBRS9DLE9BQU87TUFBRU87SUFBWSxDQUFDLEdBQUdULGNBQWM7SUFFL0MsT0FBTztNQUNMa0QsY0FBYyxFQUFFUixNQUFNLENBQUNTLE9BQU8sQ0FBQ2pELE9BQU8sQ0FBQyxDQUFDa0QsTUFBTSxDQUFDLENBQUNDLENBQUMsRUFBRUMsQ0FBQyxLQUFLcEQsT0FBTyxDQUFDbUQsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUEyQixHQUFHbkQsT0FBTyxDQUFDb0QsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUEyQixHQUFHRCxDQUFDLEdBQUdDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztNQUN0SkMsZUFBZSxFQUFFQyxrQkFBa0IsQ0FBQy9DLFdBQVcsQ0FBQztNQUNoRGdELGdCQUFnQixFQUFFLENBQUN2RCxPQUFPLENBQUNFLEtBQUssR0FBR0YsT0FBTyxDQUFDSyxVQUFVLElBQUksQ0FBQyxHQUFHTCxPQUFPLENBQUNNLE9BQU8sQ0FBQyxJQUFJLENBQUMsR0FBR04sT0FBTyxDQUFDSSxNQUFNLENBQUMsSUFBSSxDQUFDO01BQ3pHb0QsaUJBQWlCLEVBQUVDLG9CQUFvQixDQUFDekQsT0FBTztJQUNqRCxDQUFDO0VBQ0gsQ0FBQztFQUVELE1BQU1zRCxrQkFBa0IsR0FBSW5CLElBQW1DLElBQWE7SUFDMUUsUUFBUUEsSUFBSTtNQUNWLEtBQUssV0FBVztRQUFFLE9BQU8saURBQWlEO01BQzFFLEtBQUssTUFBTTtRQUFFLE9BQU8sNENBQTRDO01BQ2hFLEtBQUssU0FBUztRQUFFLE9BQU8sdURBQXVEO01BQzlFLEtBQUssTUFBTTtRQUFFLE9BQU8sK0NBQStDO01BQ25FLEtBQUssVUFBVTtRQUFFLE9BQU8sd0NBQXdDO01BQ2hFO1FBQVMsT0FBTyxzQkFBc0I7SUFDeEM7RUFDRixDQUFDO0VBRUQsTUFBTXNCLG9CQUFvQixHQUFJekQsT0FBeUIsSUFBYTtJQUNsRSxJQUFJQSxPQUFPLENBQUNFLEtBQUssR0FBRyxHQUFHLElBQUlGLE9BQU8sQ0FBQ0csVUFBVSxHQUFHLEdBQUcsRUFBRTtNQUNuRCxPQUFPLGdDQUFnQztJQUN6QyxDQUFDLE1BQU0sSUFBSUgsT0FBTyxDQUFDSSxNQUFNLEdBQUcsR0FBRyxJQUFJSixPQUFPLENBQUNLLFVBQVUsR0FBRyxHQUFHLEVBQUU7TUFDM0QsT0FBTyx1QkFBdUI7SUFDaEMsQ0FBQyxNQUFNLElBQUlMLE9BQU8sQ0FBQ00sT0FBTyxHQUFHLEdBQUcsRUFBRTtNQUNoQyxPQUFPLGlCQUFpQjtJQUMxQixDQUFDLE1BQU07TUFDTCxPQUFPLGdCQUFnQjtJQUN6QjtFQUNGLENBQUM7RUFFRCxPQUFPO0lBQ0xSLGNBQWM7SUFDZGMsWUFBWTtJQUNaTSxXQUFXO0lBQ1g2QjtFQUNGLENBQUM7QUFDSCxDQUFDO0FBQUNsRCxFQUFBLENBM0tXRCxjQUFjIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9ob29rcy91c2VFbW90aW9uYWxBSS50cz82MWY4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuXG5pbnRlcmZhY2UgRW1vdGlvbmFsTWV0cmljcyB7XG4gIGhhcHBpbmVzczogbnVtYmVyO1xuICBmb2N1czogbnVtYmVyO1xuICBjb25maWRlbmNlOiBudW1iZXI7XG4gIHN0cmVzczogbnVtYmVyO1xuICBtb3RpdmF0aW9uOiBudW1iZXI7XG4gIGZhdGlndWU6IG51bWJlcjtcbn1cblxuaW50ZXJmYWNlIEVtb3Rpb25hbFN0YXRlIHtcbiAgbWV0cmljczogRW1vdGlvbmFsTWV0cmljcztcbiAgb3ZlcmFsbE1vb2Q6ICdleGNlbGxlbnQnIHwgJ2dvb2QnIHwgJ25ldXRyYWwnIHwgJ3Bvb3InIHwgJ2NyaXRpY2FsJztcbiAgcmVjb21tZW5kYXRpb25zOiBzdHJpbmdbXTtcbiAgbGFzdEFuYWx5c2lzOiBEYXRlO1xuICBpc0FuYWx5emluZzogYm9vbGVhbjtcbn1cblxuaW50ZXJmYWNlIEdhbWVCZWhhdmlvciB7XG4gIGRlY2lzaW9uVGltZTogbnVtYmVyO1xuICByaXNrVG9sZXJhbmNlOiBudW1iZXI7XG4gIGFnZ3Jlc3NpdmVuZXNzOiBudW1iZXI7XG4gIGNvbnNpc3RlbmN5OiBudW1iZXI7XG59XG5cbmV4cG9ydCBjb25zdCB1c2VFbW90aW9uYWxBSSA9ICgpID0+IHtcbiAgY29uc3QgW2Vtb3Rpb25hbFN0YXRlLCBzZXRFbW90aW9uYWxTdGF0ZV0gPSB1c2VTdGF0ZTxFbW90aW9uYWxTdGF0ZT4oe1xuICAgIG1ldHJpY3M6IHtcbiAgICAgIGhhcHBpbmVzczogMC41LFxuICAgICAgZm9jdXM6IDAuNSxcbiAgICAgIGNvbmZpZGVuY2U6IDAuNSxcbiAgICAgIHN0cmVzczogMC41LFxuICAgICAgbW90aXZhdGlvbjogMC41LFxuICAgICAgZmF0aWd1ZTogMC41XG4gICAgfSxcbiAgICBvdmVyYWxsTW9vZDogJ25ldXRyYWwnLFxuICAgIHJlY29tbWVuZGF0aW9uczogW10sXG4gICAgbGFzdEFuYWx5c2lzOiBuZXcgRGF0ZSgpLFxuICAgIGlzQW5hbHl6aW5nOiBmYWxzZVxuICB9KTtcblxuICBjb25zdCBbZ2FtZUJlaGF2aW9yLCBzZXRHYW1lQmVoYXZpb3JdID0gdXNlU3RhdGU8R2FtZUJlaGF2aW9yPih7XG4gICAgZGVjaXNpb25UaW1lOiA1MDAwLFxuICAgIHJpc2tUb2xlcmFuY2U6IDAuNSxcbiAgICBhZ2dyZXNzaXZlbmVzczogMC41LFxuICAgIGNvbnNpc3RlbmN5OiAwLjVcbiAgfSk7XG5cbiAgY29uc3QgYW5hbHl6ZVVzZXIgPSB1c2VDYWxsYmFjayhhc3luYyAoaW5wdXREYXRhPzogYW55KSA9PiB7XG4gICAgc2V0RW1vdGlvbmFsU3RhdGUocHJldiA9PiAoeyAuLi5wcmV2LCBpc0FuYWx5emluZzogdHJ1ZSB9KSk7XG5cbiAgICB0cnkge1xuICAgICAgLy8g0KHQuNC80YPQu9GP0YbQuNGPINCw0L3QsNC70LjQt9CwINGN0LzQvtGG0LjQuVxuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDIwMDApKTtcblxuICAgICAgY29uc3QgbmV3TWV0cmljczogRW1vdGlvbmFsTWV0cmljcyA9IHtcbiAgICAgICAgaGFwcGluZXNzOiBNYXRoLm1heCgwLCBNYXRoLm1pbigxLCAwLjcgKyAoTWF0aC5yYW5kb20oKSAtIDAuNSkgKiAwLjQpKSxcbiAgICAgICAgZm9jdXM6IE1hdGgubWF4KDAsIE1hdGgubWluKDEsIDAuOCArIChNYXRoLnJhbmRvbSgpIC0gMC41KSAqIDAuMykpLFxuICAgICAgICBjb25maWRlbmNlOiBNYXRoLm1heCgwLCBNYXRoLm1pbigxLCAwLjYgKyAoTWF0aC5yYW5kb20oKSAtIDAuNSkgKiAwLjUpKSxcbiAgICAgICAgc3RyZXNzOiBNYXRoLm1heCgwLCBNYXRoLm1pbigxLCAwLjMgKyAoTWF0aC5yYW5kb20oKSAtIDAuNSkgKiAwLjQpKSxcbiAgICAgICAgbW90aXZhdGlvbjogTWF0aC5tYXgoMCwgTWF0aC5taW4oMSwgMC43NSArIChNYXRoLnJhbmRvbSgpIC0gMC41KSAqIDAuMykpLFxuICAgICAgICBmYXRpZ3VlOiBNYXRoLm1heCgwLCBNYXRoLm1pbigxLCAwLjQgKyAoTWF0aC5yYW5kb20oKSAtIDAuNSkgKiAwLjQpKVxuICAgICAgfTtcblxuICAgICAgLy8g0J7Qv9GA0LXQtNC10LvRj9C10Lwg0L7QsdGJ0LXQtSDQvdCw0YHRgtGA0L7QtdC90LjQtVxuICAgICAgY29uc3QgYXZlcmFnZVBvc2l0aXZlID0gKG5ld01ldHJpY3MuaGFwcGluZXNzICsgbmV3TWV0cmljcy5mb2N1cyArIG5ld01ldHJpY3MuY29uZmlkZW5jZSArIG5ld01ldHJpY3MubW90aXZhdGlvbikgLyA0O1xuICAgICAgY29uc3QgYXZlcmFnZU5lZ2F0aXZlID0gKG5ld01ldHJpY3Muc3RyZXNzICsgbmV3TWV0cmljcy5mYXRpZ3VlKSAvIDI7XG4gICAgICBjb25zdCBvdmVyYWxsU2NvcmUgPSAoYXZlcmFnZVBvc2l0aXZlIC0gYXZlcmFnZU5lZ2F0aXZlICsgMSkgLyAyOyAvLyDQndC+0YDQvNCw0LvQuNC30LDRhtC40Y8g0L7RgiAwINC00L4gMVxuXG4gICAgICBsZXQgb3ZlcmFsbE1vb2Q6IEVtb3Rpb25hbFN0YXRlWydvdmVyYWxsTW9vZCddO1xuICAgICAgaWYgKG92ZXJhbGxTY29yZSA+IDAuOCkgb3ZlcmFsbE1vb2QgPSAnZXhjZWxsZW50JztcbiAgICAgIGVsc2UgaWYgKG92ZXJhbGxTY29yZSA+IDAuNikgb3ZlcmFsbE1vb2QgPSAnZ29vZCc7XG4gICAgICBlbHNlIGlmIChvdmVyYWxsU2NvcmUgPiAwLjQpIG92ZXJhbGxNb29kID0gJ25ldXRyYWwnO1xuICAgICAgZWxzZSBpZiAob3ZlcmFsbFNjb3JlID4gMC4yKSBvdmVyYWxsTW9vZCA9ICdwb29yJztcbiAgICAgIGVsc2Ugb3ZlcmFsbE1vb2QgPSAnY3JpdGljYWwnO1xuXG4gICAgICAvLyDQk9C10L3QtdGA0LjRgNGD0LXQvCDRgNC10LrQvtC80LXQvdC00LDRhtC40LhcbiAgICAgIGNvbnN0IHJlY29tbWVuZGF0aW9ucyA9IGdlbmVyYXRlUmVjb21tZW5kYXRpb25zKG5ld01ldHJpY3MsIG92ZXJhbGxNb29kKTtcblxuICAgICAgLy8g0J7QsdC90L7QstC70Y/QtdC8INC40LPRgNC+0LLQvtC1INC/0L7QstC10LTQtdC90LjQtVxuICAgICAgc2V0R2FtZUJlaGF2aW9yKHtcbiAgICAgICAgZGVjaXNpb25UaW1lOiAzMDAwICsgKDEgLSBuZXdNZXRyaWNzLmZvY3VzKSAqIDUwMDAsXG4gICAgICAgIHJpc2tUb2xlcmFuY2U6IG5ld01ldHJpY3MuY29uZmlkZW5jZSAqIDAuOCArIG5ld01ldHJpY3MubW90aXZhdGlvbiAqIDAuMixcbiAgICAgICAgYWdncmVzc2l2ZW5lc3M6IG5ld01ldHJpY3MuY29uZmlkZW5jZSAqIDAuNiArICgxIC0gbmV3TWV0cmljcy5zdHJlc3MpICogMC40LFxuICAgICAgICBjb25zaXN0ZW5jeTogbmV3TWV0cmljcy5mb2N1cyAqIDAuNyArICgxIC0gbmV3TWV0cmljcy5mYXRpZ3VlKSAqIDAuM1xuICAgICAgfSk7XG5cbiAgICAgIHNldEVtb3Rpb25hbFN0YXRlKHtcbiAgICAgICAgbWV0cmljczogbmV3TWV0cmljcyxcbiAgICAgICAgb3ZlcmFsbE1vb2QsXG4gICAgICAgIHJlY29tbWVuZGF0aW9ucyxcbiAgICAgICAgbGFzdEFuYWx5c2lzOiBuZXcgRGF0ZSgpLFxuICAgICAgICBpc0FuYWx5emluZzogZmFsc2VcbiAgICAgIH0pO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vtb3Rpb25hbCBhbmFseXNpcyBlcnJvcjonLCBlcnJvcik7XG4gICAgICBzZXRFbW90aW9uYWxTdGF0ZShwcmV2ID0+ICh7IC4uLnByZXYsIGlzQW5hbHl6aW5nOiBmYWxzZSB9KSk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgY29uc3QgZ2VuZXJhdGVSZWNvbW1lbmRhdGlvbnMgPSAobWV0cmljczogRW1vdGlvbmFsTWV0cmljcywgbW9vZDogRW1vdGlvbmFsU3RhdGVbJ292ZXJhbGxNb29kJ10pOiBzdHJpbmdbXSA9PiB7XG4gICAgY29uc3QgcmVjb21tZW5kYXRpb25zOiBzdHJpbmdbXSA9IFtdO1xuXG4gICAgaWYgKG1ldHJpY3Muc3RyZXNzID4gMC43KSB7XG4gICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCgn0KDQtdC60L7QvNC10L3QtNGD0LXQvCDRgdC00LXQu9Cw0YLRjCDQv9C10YDQtdGA0YvQsiDQtNC70Y8g0YHQvdC40LbQtdC90LjRjyDRgdGC0YDQtdGB0YHQsCcpO1xuICAgIH1cblxuICAgIGlmIChtZXRyaWNzLmZhdGlndWUgPiAwLjgpIHtcbiAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKCfQktGL0YHQvtC60LjQuSDRg9GA0L7QstC10L3RjCDRg9GB0YLQsNC70L7RgdGC0LggLSDQstGA0LXQvNGPINC+0YLQtNC+0YXQvdGD0YLRjCcpO1xuICAgIH1cblxuICAgIGlmIChtZXRyaWNzLmZvY3VzIDwgMC40KSB7XG4gICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCgn0J/QvtC/0YDQvtCx0YPQudGC0LUg0YPQv9GA0LDQttC90LXQvdC40Y8g0L3QsCDQutC+0L3RhtC10L3RgtGA0LDRhtC40Y4nKTtcbiAgICB9XG5cbiAgICBpZiAobWV0cmljcy5jb25maWRlbmNlIDwgMC4zKSB7XG4gICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCgn0J3QsNGH0L3QuNGC0LUg0YEg0LHQvtC70LXQtSDQv9GA0L7RgdGC0YvRhSDQuNCz0YAg0LTQu9GPINC/0L7QstGL0YjQtdC90LjRjyDRg9Cy0LXRgNC10L3QvdC+0YHRgtC4Jyk7XG4gICAgfVxuXG4gICAgaWYgKG1ldHJpY3MubW90aXZhdGlvbiA8IDAuNCkge1xuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goJ9Cj0YHRgtCw0L3QvtCy0LjRgtC1INC90LXQsdC+0LvRjNGI0LjQtSDQtNC+0YHRgtC40LbQuNC80YvQtSDRhtC10LvQuCcpO1xuICAgIH1cblxuICAgIGlmIChtb29kID09PSAnZXhjZWxsZW50Jykge1xuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goJ9Ce0YLQu9C40YfQvdC+0LUg0YHQvtGB0YLQvtGP0L3QuNC1ISDQktGA0LXQvNGPINC00LvRjyDRgdC70L7QttC90YvRhSDQstGL0LfQvtCy0L7QsicpO1xuICAgIH1cblxuICAgIHJldHVybiByZWNvbW1lbmRhdGlvbnM7XG4gIH07XG5cbiAgLy8g0JDQstGC0L7QvNCw0YLQuNGH0LXRgdC60L7QtSDQvtCx0L3QvtCy0LvQtdC90LjQtSDQvNC10YLRgNC40LpcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgIHNldEVtb3Rpb25hbFN0YXRlKHByZXYgPT4ge1xuICAgICAgICBpZiAocHJldi5pc0FuYWx5emluZykgcmV0dXJuIHByZXY7XG5cbiAgICAgICAgY29uc3QgdXBkYXRlZE1ldHJpY3MgPSB7IC4uLnByZXYubWV0cmljcyB9O1xuICAgICAgICBcbiAgICAgICAgLy8g0J3QtdCx0L7Qu9GM0YjQuNC1INGB0LvRg9GH0LDQudC90YvQtSDQuNC30LzQtdC90LXQvdC40Y9cbiAgICAgICAgT2JqZWN0LmtleXModXBkYXRlZE1ldHJpY3MpLmZvckVhY2goa2V5ID0+IHtcbiAgICAgICAgICBjb25zdCBjdXJyZW50VmFsdWUgPSB1cGRhdGVkTWV0cmljc1trZXkgYXMga2V5b2YgRW1vdGlvbmFsTWV0cmljc107XG4gICAgICAgICAgY29uc3QgY2hhbmdlID0gKE1hdGgucmFuZG9tKCkgLSAwLjUpICogMC4wNTtcbiAgICAgICAgICB1cGRhdGVkTWV0cmljc1trZXkgYXMga2V5b2YgRW1vdGlvbmFsTWV0cmljc10gPSBNYXRoLm1heCgwLCBNYXRoLm1pbigxLCBjdXJyZW50VmFsdWUgKyBjaGFuZ2UpKTtcbiAgICAgICAgfSk7XG5cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIG1ldHJpY3M6IHVwZGF0ZWRNZXRyaWNzXG4gICAgICAgIH07XG4gICAgICB9KTtcbiAgICB9LCAxMDAwMCk7XG5cbiAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbCk7XG4gIH0sIFtdKTtcblxuICBjb25zdCBnZXRFbW90aW9uYWxJbnNpZ2h0ID0gKCkgPT4ge1xuICAgIGNvbnN0IHsgbWV0cmljcywgb3ZlcmFsbE1vb2QgfSA9IGVtb3Rpb25hbFN0YXRlO1xuICAgIFxuICAgIHJldHVybiB7XG4gICAgICBwcmltYXJ5RW1vdGlvbjogT2JqZWN0LmVudHJpZXMobWV0cmljcykucmVkdWNlKChhLCBiKSA9PiBtZXRyaWNzW2FbMF0gYXMga2V5b2YgRW1vdGlvbmFsTWV0cmljc10gPiBtZXRyaWNzW2JbMF0gYXMga2V5b2YgRW1vdGlvbmFsTWV0cmljc10gPyBhIDogYilbMF0sXG4gICAgICBtb29kRGVzY3JpcHRpb246IGdldE1vb2REZXNjcmlwdGlvbihvdmVyYWxsTW9vZCksXG4gICAgICBwbGF5YWJpbGl0eVNjb3JlOiAobWV0cmljcy5mb2N1cyArIG1ldHJpY3MubW90aXZhdGlvbiArICgxIC0gbWV0cmljcy5mYXRpZ3VlKSArICgxIC0gbWV0cmljcy5zdHJlc3MpKSAvIDQsXG4gICAgICBzdWdnZXN0ZWRHYW1lVHlwZTogZ2V0U3VnZ2VzdGVkR2FtZVR5cGUobWV0cmljcylcbiAgICB9O1xuICB9O1xuXG4gIGNvbnN0IGdldE1vb2REZXNjcmlwdGlvbiA9IChtb29kOiBFbW90aW9uYWxTdGF0ZVsnb3ZlcmFsbE1vb2QnXSk6IHN0cmluZyA9PiB7XG4gICAgc3dpdGNoIChtb29kKSB7XG4gICAgICBjYXNlICdleGNlbGxlbnQnOiByZXR1cm4gJ9Cf0YDQtdCy0L7RgdGF0L7QtNC90L7QtSDQvdCw0YHRgtGA0L7QtdC90LjQtSEg0JPQvtGC0L7QstGLINC6INC70Y7QsdGL0Lwg0LLRi9C30L7QstCw0LwnO1xuICAgICAgY2FzZSAnZ29vZCc6IHJldHVybiAn0KXQvtGA0L7RiNC10LUg0L3QsNGB0YLRgNC+0LXQvdC40LUsINC+0YLQu9C40YfQvdC+0LUg0LLRgNC10LzRjyDQtNC70Y8g0LjQs9GAJztcbiAgICAgIGNhc2UgJ25ldXRyYWwnOiByZXR1cm4gJ9Cd0LXQudGC0YDQsNC70YzQvdC+0LUg0YHQvtGB0YLQvtGP0L3QuNC1LCDQvNC+0LbQvdC+INC40LPRgNCw0YLRjCDQsiDRgdC/0L7QutC+0LnQvdC+0Lwg0YLQtdC80L/QtSc7XG4gICAgICBjYXNlICdwb29yJzogcmV0dXJuICfQndC1INC70YPRh9GI0LXQtSDQvdCw0YHRgtGA0L7QtdC90LjQtSwg0YDQtdC60L7QvNC10L3QtNGD0LXQvCDQu9C10LPQutC40LUg0LjQs9GA0YsnO1xuICAgICAgY2FzZSAnY3JpdGljYWwnOiByZXR1cm4gJ9Ca0YDQuNGC0LjRh9C10YHQutC+0LUg0YHQvtGB0YLQvtGP0L3QuNC1LCDQu9GD0YfRiNC1INC+0YLQtNC+0YXQvdGD0YLRjCc7XG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ9CQ0L3QsNC70LjQtyDQvdCw0YHRgtGA0L7QtdC90LjRjy4uLic7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGdldFN1Z2dlc3RlZEdhbWVUeXBlID0gKG1ldHJpY3M6IEVtb3Rpb25hbE1ldHJpY3MpOiBzdHJpbmcgPT4ge1xuICAgIGlmIChtZXRyaWNzLmZvY3VzID4gMC44ICYmIG1ldHJpY3MuY29uZmlkZW5jZSA+IDAuNykge1xuICAgICAgcmV0dXJuICfQotGD0YDQvdC40YDQvdGL0LUg0LjQs9GA0Ysg0LLRi9GB0L7QutC+0LPQviDRg9GA0L7QstC90Y8nO1xuICAgIH0gZWxzZSBpZiAobWV0cmljcy5zdHJlc3MgPCAwLjMgJiYgbWV0cmljcy5tb3RpdmF0aW9uID4gMC42KSB7XG4gICAgICByZXR1cm4gJ9Ch0L7RgNC10LLQvdC+0LLQsNGC0LXQu9GM0L3Ri9C1INC40LPRgNGLJztcbiAgICB9IGVsc2UgaWYgKG1ldHJpY3MuZmF0aWd1ZSA+IDAuNykge1xuICAgICAgcmV0dXJuICfQmtCw0LfRg9Cw0LvRjNC90YvQtSDQuNCz0YDRiyc7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiAn0J7QsdGD0YfQsNGO0YnQuNC1INC40LPRgNGLJztcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIHtcbiAgICBlbW90aW9uYWxTdGF0ZSxcbiAgICBnYW1lQmVoYXZpb3IsXG4gICAgYW5hbHl6ZVVzZXIsXG4gICAgZ2V0RW1vdGlvbmFsSW5zaWdodFxuICB9O1xufTtcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwidXNlRW1vdGlvbmFsQUkiLCJfcyIsImVtb3Rpb25hbFN0YXRlIiwic2V0RW1vdGlvbmFsU3RhdGUiLCJtZXRyaWNzIiwiaGFwcGluZXNzIiwiZm9jdXMiLCJjb25maWRlbmNlIiwic3RyZXNzIiwibW90aXZhdGlvbiIsImZhdGlndWUiLCJvdmVyYWxsTW9vZCIsInJlY29tbWVuZGF0aW9ucyIsImxhc3RBbmFseXNpcyIsIkRhdGUiLCJpc0FuYWx5emluZyIsImdhbWVCZWhhdmlvciIsInNldEdhbWVCZWhhdmlvciIsImRlY2lzaW9uVGltZSIsInJpc2tUb2xlcmFuY2UiLCJhZ2dyZXNzaXZlbmVzcyIsImNvbnNpc3RlbmN5IiwiYW5hbHl6ZVVzZXIiLCJpbnB1dERhdGEiLCJwcmV2IiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwibmV3TWV0cmljcyIsIk1hdGgiLCJtYXgiLCJtaW4iLCJyYW5kb20iLCJhdmVyYWdlUG9zaXRpdmUiLCJhdmVyYWdlTmVnYXRpdmUiLCJvdmVyYWxsU2NvcmUiLCJnZW5lcmF0ZVJlY29tbWVuZGF0aW9ucyIsImVycm9yIiwiY29uc29sZSIsIm1vb2QiLCJwdXNoIiwiaW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsInVwZGF0ZWRNZXRyaWNzIiwiT2JqZWN0Iiwia2V5cyIsImZvckVhY2giLCJrZXkiLCJjdXJyZW50VmFsdWUiLCJjaGFuZ2UiLCJjbGVhckludGVydmFsIiwiZ2V0RW1vdGlvbmFsSW5zaWdodCIsInByaW1hcnlFbW90aW9uIiwiZW50cmllcyIsInJlZHVjZSIsImEiLCJiIiwibW9vZERlc2NyaXB0aW9uIiwiZ2V0TW9vZERlc2NyaXB0aW9uIiwicGxheWFiaWxpdHlTY29yZSIsInN1Z2dlc3RlZEdhbWVUeXBlIiwiZ2V0U3VnZ2VzdGVkR2FtZVR5cGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/hooks/useEmotionalAI.ts\n"));

/***/ }),

/***/ "./src/hooks/useGameState.ts":
/*!***********************************!*\
  !*** ./src/hooks/useGameState.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGameState: function() { return /* binding */ useGameState; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _game_gameLogic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../game/gameLogic */ \"./src/game/gameLogic.ts\");\n/* harmony import */ var _useEmotionalAI__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useEmotionalAI */ \"./src/hooks/useEmotionalAI.ts\");\n/* harmony import */ var _useQuantumRandom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useQuantumRandom */ \"./src/hooks/useQuantumRandom.ts\");\nvar _s = $RefreshSig$();\n\n\n\n\nconst useGameState = playerId => {\n  _s();\n  const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameEvents, setGameEvents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [currentPlayerId, setCurrentPlayerId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(playerId || `player-${Date.now()}`);\n\n  // Интеграция с эмоциональным ИИ\n  const emotionalAI = (0,_useEmotionalAI__WEBPACK_IMPORTED_MODULE_2__.useEmotionalAI)();\n  const quantumRandom = (0,_useQuantumRandom__WEBPACK_IMPORTED_MODULE_3__.useQuantumRandom)();\n  const aiPlayerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n\n  // Добавление события в лог\n  const addEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(event => {\n    const newEvent = {\n      ...event,\n      timestamp: Date.now()\n    };\n    setGameEvents(prev => [...prev, newEvent]);\n  }, []);\n\n  // Создание новой игры\n  const createNewGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(settings => {\n    const newGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.createGame)(settings);\n    setGameState(newGame);\n    setGameEvents([]);\n    addEvent({\n      type: 'game_started',\n      message: 'Новая игра создана'\n    });\n  }, [addEvent]);\n\n  // Присоединение к игре\n  const joinGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(playerName => {\n    if (!gameState) {\n      throw new Error('Игра не создана');\n    }\n    const player = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.createPlayer)(currentPlayerId, playerName, false);\n    try {\n      const updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.addPlayerToGame)(gameState, player);\n      setGameState(updatedGame);\n      addEvent({\n        type: 'player_joined',\n        playerId: currentPlayerId,\n        message: `${playerName} присоединился к игре`\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка присоединения'\n      });\n    }\n  }, [gameState, currentPlayerId, addEvent]);\n\n  // Добавление бота\n  const addBot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(botName => {\n    if (!gameState) return;\n    const bot = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.createPlayer)(`bot-${Date.now()}`, botName, true);\n    try {\n      const updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.addPlayerToGame)(gameState, bot);\n      setGameState(updatedGame);\n      addEvent({\n        type: 'player_joined',\n        playerId: bot.id,\n        message: `Бот ${botName} добавлен в игру`\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка добавления бота'\n      });\n    }\n  }, [gameState, addEvent]);\n\n  // Начало игры\n  const startGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!gameState) {\n      throw new Error('Игра не создана');\n    }\n    if (gameState.players.length < 2) {\n      // Добавляем бота если недостаточно игроков\n      addBot('ИИ Противник');\n      return;\n    }\n    try {\n      let updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.dealCards)(gameState);\n      updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.drawCards)(updatedGame);\n      setGameState(updatedGame);\n      addEvent({\n        type: 'cards_dealt',\n        message: 'Карты розданы, игра началась!'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка начала игры'\n      });\n    }\n  }, [gameState, addBot, addEvent]);\n\n  // Выполнение игрового действия\n  const playAction = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(action => {\n    if (!gameState) {\n      throw new Error('Игра не создана');\n    }\n    try {\n      let updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.executeAction)(gameState, action);\n\n      // Добираем карты после хода\n      if (action.type === 'finish_turn' || action.type === 'take_cards') {\n        updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.drawCards)(updatedGame);\n      }\n\n      // Проверяем окончание игры\n      updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.checkGameEnd)(updatedGame);\n      setGameState(updatedGame);\n      addEvent({\n        type: 'card_played',\n        playerId: action.playerId,\n        message: `Игрок выполнил действие: ${action.type}`,\n        data: action\n      });\n      if (updatedGame.phase === 'finished') {\n        addEvent({\n          type: 'game_finished',\n          message: updatedGame.winner ? `Игра окончена! Победитель: ${updatedGame.players.find(p => p.id === updatedGame.winner)?.name}` : 'Игра окончена ничьей'\n        });\n      }\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        playerId: action.playerId,\n        message: error instanceof Error ? error.message : 'Ошибка выполнения действия'\n      });\n    }\n  }, [gameState, addEvent]);\n\n  // Проверка, можно ли сыграть карту\n  const canPlayCard = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(card => {\n    if (!gameState || !currentPlayer) return false;\n\n    // Проверяем, есть ли карта у игрока\n    const hasCard = currentPlayer.cards.some(c => c.id === card.id);\n    if (!hasCard) return false;\n\n    // Проверяем, наш ли ход\n    if (!isMyTurn) return false;\n\n    // Дополнительные проверки в зависимости от фазы игры\n    switch (gameState.phase) {\n      case 'attacking':\n        return gameState.currentPlayerIndex === gameState.players.findIndex(p => p.id === currentPlayerId);\n      case 'defending':\n        return gameState.defendingPlayerIndex === gameState.players.findIndex(p => p.id === currentPlayerId);\n      case 'adding':\n        return gameState.currentPlayerIndex !== gameState.defendingPlayerIndex;\n      default:\n        return false;\n    }\n  }, [gameState, currentPlayerId]);\n\n  // Получение доступных действий\n  const getValidActions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!gameState || !currentPlayer || !isMyTurn) return [];\n    const actions = [];\n    const playerIndex = gameState.players.findIndex(p => p.id === currentPlayerId);\n    switch (gameState.phase) {\n      case 'attacking':\n        // Можем атаковать любой картой (если стол пуст) или подходящей картой\n        currentPlayer.cards.forEach(card => {\n          actions.push({\n            type: 'attack',\n            playerId: currentPlayerId,\n            card\n          });\n        });\n\n        // Можем пропустить ход\n        actions.push({\n          type: 'pass_turn',\n          playerId: currentPlayerId\n        });\n        break;\n      case 'defending':\n        if (playerIndex === gameState.defendingPlayerIndex) {\n          // Можем защищаться подходящими картами\n          gameState.table.forEach((tableCard, position) => {\n            if (!tableCard.defendCard) {\n              currentPlayer.cards.forEach(card => {\n                actions.push({\n                  type: 'defend',\n                  playerId: currentPlayerId,\n                  card,\n                  targetPosition: position\n                });\n              });\n            }\n          });\n\n          // Можем взять карты\n          actions.push({\n            type: 'take_cards',\n            playerId: currentPlayerId\n          });\n        }\n        break;\n      case 'adding':\n        if (playerIndex !== gameState.defendingPlayerIndex) {\n          // Можем подкидывать подходящие карты\n          currentPlayer.cards.forEach(card => {\n            actions.push({\n              type: 'add_card',\n              playerId: currentPlayerId,\n              card\n            });\n          });\n        }\n\n        // Можем завершить ход\n        actions.push({\n          type: 'finish_turn',\n          playerId: currentPlayerId\n        });\n        break;\n    }\n    return actions;\n  }, [gameState, currentPlayer, isMyTurn, currentPlayerId]);\n\n  // Сброс игры\n  const resetGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setGameState(null);\n    setGameEvents([]);\n  }, []);\n\n  // Вычисляемые значения\n  const currentPlayer = gameState?.players.find(p => p.id === currentPlayerId) || null;\n  const isMyTurn = gameState ? gameState.players[gameState.currentPlayerIndex]?.id === currentPlayerId : false;\n\n  // Автоматические действия ботов\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!gameState || gameState.phase === 'finished') return;\n    const currentGamePlayer = gameState.players[gameState.currentPlayerIndex];\n    if (!currentGamePlayer?.isBot) return;\n\n    // Простая логика бота - случайное действие через небольшую задержку\n    const timer = setTimeout(() => {\n      const validActions = getValidActions();\n      if (validActions.length > 0) {\n        const randomAction = validActions[Math.floor(Math.random() * validActions.length)];\n        playAction(randomAction);\n      }\n    }, 1000 + Math.random() * 2000); // 1-3 секунды задержка\n\n    return () => clearTimeout(timer);\n  }, [gameState, getValidActions, playAction]);\n  return {\n    gameState,\n    currentPlayer,\n    isMyTurn,\n    gameEvents,\n    createNewGame,\n    joinGame,\n    startGame,\n    playAction,\n    canPlayCard,\n    getValidActions,\n    resetGame\n  };\n};\n_s(useGameState, \"VkVvJHuj+Pfb5lPvzO409jy1TvI=\", false, function () {\n  return [_useEmotionalAI__WEBPACK_IMPORTED_MODULE_2__.useEmotionalAI, _useQuantumRandom__WEBPACK_IMPORTED_MODULE_3__.useQuantumRandom];\n});\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useGameState.ts\n"));

/***/ }),

/***/ "./src/hooks/useQuantumRandom.ts":
/*!***************************************!*\
  !*** ./src/hooks/useQuantumRandom.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuantumRandom: function() { return /* binding */ useQuantumRandom; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nvar _s = $RefreshSig$();\n\nconst useQuantumRandom = () => {\n  _s();\n  const [quantumStatus, setQuantumStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    isQuantumAvailable: false,\n    metrics: {\n      entropy: 0,\n      coherence: 0,\n      entanglement: 0\n    },\n    lastUpdate: null,\n    connectionStrength: 0\n  });\n  const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const generateQuantumSeed = async () => {\n    setIsGenerating(true);\n    try {\n      // Симуляция квантовой генерации\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      const entropy = Math.random() * 0.2 + 0.8; // 0.8-1.0\n      const coherence = Math.random() * 0.3 + 0.7; // 0.7-1.0\n      const entanglement = Math.random() * 0.4 + 0.6; // 0.6-1.0\n\n      setQuantumStatus({\n        isQuantumAvailable: true,\n        metrics: {\n          entropy,\n          coherence,\n          entanglement\n        },\n        lastUpdate: new Date(),\n        connectionStrength: (entropy + coherence + entanglement) / 3\n      });\n\n      // Генерируем квантовое случайное число\n      const quantumSeed = Math.floor(Math.random() * 1000000);\n      return quantumSeed;\n    } catch (error) {\n      console.error('Quantum generation error:', error);\n      return Math.floor(Math.random() * 1000000);\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n  const generateQuantumArray = async length => {\n    const array = [];\n    for (let i = 0; i < length; i++) {\n      array.push(await generateQuantumSeed());\n    }\n    return array;\n  };\n\n  // Автоматическое обновление метрик\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const interval = setInterval(() => {\n      if (quantumStatus.isQuantumAvailable) {\n        setQuantumStatus(prev => ({\n          ...prev,\n          metrics: {\n            entropy: Math.max(0.7, prev.metrics.entropy + (Math.random() - 0.5) * 0.1),\n            coherence: Math.max(0.6, prev.metrics.coherence + (Math.random() - 0.5) * 0.1),\n            entanglement: Math.max(0.5, prev.metrics.entanglement + (Math.random() - 0.5) * 0.1)\n          },\n          lastUpdate: new Date()\n        }));\n      }\n    }, 5000);\n    return () => clearInterval(interval);\n  }, [quantumStatus.isQuantumAvailable]);\n  return {\n    quantumStatus,\n    isGenerating,\n    generateQuantumSeed,\n    generateQuantumArray\n  };\n};\n_s(useQuantumRandom, \"BZajTdmiRcTYXkGyAxEe3/Jymqc=\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useQuantumRandom.ts\n"));

/***/ })

});