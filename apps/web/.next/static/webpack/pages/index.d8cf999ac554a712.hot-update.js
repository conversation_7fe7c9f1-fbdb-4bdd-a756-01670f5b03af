"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\n\nconst HomePage = ()=>{\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleStartPlaying = ()=>{\n        router.push(\"/games/durak\");\n    };\n    const handleMultiplayer = ()=>{\n        router.push(\"/multiplayer\");\n    };\n    const handleViewTournaments = ()=>{\n        router.push(\"/tournaments\");\n    };\n    const handleViewTutorials = ()=>{\n        router.push(\"/tutorials\");\n    };\n    const handleViewProfile = ()=>{\n        router.push(\"/profile\");\n    };\n    const handleViewLeaderboard = ()=>{\n        router.push(\"/leaderboard\");\n    };\n    const handleViewSpectate = ()=>{\n        router.push(\"/spectate\");\n    };\n    const handleViewFriends = ()=>{\n        router.push(\"/friends\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Козырь Мастер - Карточные игры онлайн\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Играйте в популярные карточные игры онлайн с друзьями\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Main, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Hero, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                children: \"Добро пожаловать в Козырь Мастер\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Subtitle, {\n                                children: \"Платформа для игры в популярные карточные игры онлайн\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContainer, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDFAE\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Одиночная игра\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Играйте против бота и оттачивайте свои навыки\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StartButton, {\n                                        onClick: handleStartPlaying,\n                                        children: \"Играть против бота\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDF10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Многопользовательская игра\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Играйте с другими игроками онлайн в реальном времени\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MultiplayerButton, {\n                                        onClick: handleMultiplayer,\n                                        children: \"Играть онлайн\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDFC6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Турниры\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Участвуйте в турнирах и выигрывайте призы\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewTournaments,\n                                        children: \"Смотреть турниры\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83D\\uDCDA\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Обучение\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Изучите правила игр и стратегии\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewTutorials,\n                                        children: \"Изучить правила\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83D\\uDC64\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Профиль\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Просмотрите свою статистику и достижения\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewProfile,\n                                        children: \"Мой профиль\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDFC6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Лидеры\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Таблица лидеров и лучшие игроки\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewLeaderboard,\n                                        children: \"Посмотреть рейтинг\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83D\\uDC41️\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Наблюдение\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Наблюдайте за играми других игроков\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewSpectate,\n                                        children: \"Смотреть игры\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"\\xa9 2023 Козырь Мастер. Все права защищены.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HomePage, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = HomePage;\n// Стилизованные компоненты\nconst Container = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__Container\",\n    componentId: \"sc-2c4b2a84-0\"\n})([\n    \"min-height:100vh;display:flex;flex-direction:column;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);\"\n]);\n_c1 = Container;\nconst Main = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].main.withConfig({\n    displayName: \"pages__Main\",\n    componentId: \"sc-2c4b2a84-1\"\n})([\n    \"flex:1;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:2rem;\"\n]);\n_c2 = Main;\nconst Hero = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__Hero\",\n    componentId: \"sc-2c4b2a84-2\"\n})([\n    \"text-align:center;margin-bottom:4rem;\"\n]);\n_c3 = Hero;\nconst Title = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].h1.withConfig({\n    displayName: \"pages__Title\",\n    componentId: \"sc-2c4b2a84-3\"\n})([\n    \"font-size:3.5rem;margin-bottom:1rem;color:white;text-shadow:2px 2px 4px rgba(0,0,0,0.3);font-weight:700;@media (max-width:768px){font-size:2.5rem;}\"\n]);\n_c4 = Title;\nconst Subtitle = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].p.withConfig({\n    displayName: \"pages__Subtitle\",\n    componentId: \"sc-2c4b2a84-4\"\n})([\n    \"font-size:1.5rem;color:rgba(255,255,255,0.9);text-shadow:1px 1px 2px rgba(0,0,0,0.3);max-width:600px;margin:0 auto;@media (max-width:768px){font-size:1.2rem;}\"\n]);\n_c5 = Subtitle;\nconst CardContainer = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__CardContainer\",\n    componentId: \"sc-2c4b2a84-5\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:2rem;max-width:1200px;width:100%;\"\n]);\n_c6 = CardContainer;\nconst GameCard = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__GameCard\",\n    componentId: \"sc-2c4b2a84-6\"\n})([\n    \"background:rgba(255,255,255,0.95);border-radius:16px;box-shadow:0 8px 32px rgba(0,0,0,0.1);padding:2rem;text-align:center;transition:all 0.3s ease;backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,0.2);&:hover{transform:translateY(-8px);box-shadow:0 16px 48px rgba(0,0,0,0.2);}\"\n]);\n_c7 = GameCard;\nconst CardIcon = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__CardIcon\",\n    componentId: \"sc-2c4b2a84-7\"\n})([\n    \"font-size:3rem;margin-bottom:1rem;\"\n]);\n_c8 = CardIcon;\nconst CardTitle = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].h2.withConfig({\n    displayName: \"pages__CardTitle\",\n    componentId: \"sc-2c4b2a84-8\"\n})([\n    \"font-size:1.8rem;margin-bottom:1rem;color:#333;font-weight:600;\"\n]);\n_c9 = CardTitle;\nconst CardDescription = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].p.withConfig({\n    displayName: \"pages__CardDescription\",\n    componentId: \"sc-2c4b2a84-9\"\n})([\n    \"font-size:1rem;color:#666;margin-bottom:2rem;line-height:1.6;\"\n]);\n_c10 = CardDescription;\nconst StartButton = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].button.withConfig({\n    displayName: \"pages__StartButton\",\n    componentId: \"sc-2c4b2a84-10\"\n})([\n    \"background:linear-gradient(135deg,#4CAF50,#45a049);color:white;border:none;border-radius:8px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(76,175,80,0.3);&:hover{background:linear-gradient(135deg,#45a049,#4CAF50);transform:translateY(-2px);box-shadow:0 6px 20px rgba(76,175,80,0.4);}&:active{transform:translateY(0);}\"\n]);\n_c11 = StartButton;\nconst MultiplayerButton = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].button.withConfig({\n    displayName: \"pages__MultiplayerButton\",\n    componentId: \"sc-2c4b2a84-11\"\n})([\n    \"background:linear-gradient(135deg,#FF6B35,#F7931E);color:white;border:none;border-radius:8px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(255,107,53,0.3);&:hover{background:linear-gradient(135deg,#F7931E,#FF6B35);transform:translateY(-2px);box-shadow:0 6px 20px rgba(255,107,53,0.4);}&:active{transform:translateY(0);}\"\n]);\n_c12 = MultiplayerButton;\nconst ActionButton = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].button.withConfig({\n    displayName: \"pages__ActionButton\",\n    componentId: \"sc-2c4b2a84-12\"\n})([\n    \"background:linear-gradient(135deg,#2196F3,#1976D2);color:white;border:none;border-radius:8px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(33,150,243,0.3);&:hover{background:linear-gradient(135deg,#1976D2,#2196F3);transform:translateY(-2px);box-shadow:0 6px 20px rgba(33,150,243,0.4);}&:active{transform:translateY(0);}\"\n]);\n_c13 = ActionButton;\nconst Footer = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].footer.withConfig({\n    displayName: \"pages__Footer\",\n    componentId: \"sc-2c4b2a84-13\"\n})([\n    \"width:100%;padding:1.5rem;text-align:center;background:rgba(0,0,0,0.1);color:rgba(255,255,255,0.8);backdrop-filter:blur(10px);\"\n]);\n_c14 = Footer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"HomePage\");\n$RefreshReg$(_c1, \"Container\");\n$RefreshReg$(_c2, \"Main\");\n$RefreshReg$(_c3, \"Hero\");\n$RefreshReg$(_c4, \"Title\");\n$RefreshReg$(_c5, \"Subtitle\");\n$RefreshReg$(_c6, \"CardContainer\");\n$RefreshReg$(_c7, \"GameCard\");\n$RefreshReg$(_c8, \"CardIcon\");\n$RefreshReg$(_c9, \"CardTitle\");\n$RefreshReg$(_c10, \"CardDescription\");\n$RefreshReg$(_c11, \"StartButton\");\n$RefreshReg$(_c12, \"MultiplayerButton\");\n$RefreshReg$(_c13, \"ActionButton\");\n$RefreshReg$(_c14, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n"));

/***/ })

});