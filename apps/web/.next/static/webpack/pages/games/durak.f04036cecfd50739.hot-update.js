"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/games/durak",{

/***/ "./src/pages/games/durak.tsx":
/*!***********************************!*\
  !*** ./src/pages/games/durak.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Card_AnimatedCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Card/AnimatedCard */ \"./src/components/Card/AnimatedCard.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n// Временно используем прямой импорт для отладки\n// import {\n//   DurakGame,\n//   BotFactory,\n//   BotDifficulty,\n//   GameStatus,\n//   PlayerAction,\n//   DurakVariant,\n//   GameEventData,\n//   GameEvent\n// } from \"@kozyr-master/core\";\n// Создаем заглушки для тестирования UI\nconst DurakGame = null;\nconst BotFactory = null;\nconst BotDifficulty = {\n    MEDIUM: \"medium\"\n};\nconst GameStatus = {\n    NOT_STARTED: \"not_started\",\n    IN_PROGRESS: \"in_progress\",\n    FINISHED: \"finished\"\n};\nconst PlayerAction = {\n    ATTACK: \"attack\",\n    DEFEND: \"defend\",\n    TAKE: \"take\",\n    PASS: \"pass\"\n};\nconst DurakVariant = {\n    CLASSIC: \"classic\"\n};\nconst GameEvent = {\n    GAME_STARTED: \"game_started\",\n    GAME_ENDED: \"game_ended\",\n    PLAYER_MOVED: \"player_moved\"\n};\nconst DurakGamePage = ()=>{\n    var _gameState_players_gameState_currentPlayerIndex, _gameState_winner;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [game, setGame] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [selectedCard, setSelectedCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [gameLog, setGameLog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isGameStarted, setIsGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Обработчик событий игры\n    const handleGameEvent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((eventData)=>{\n        console.log(\"Game event:\", eventData);\n        setGameState(eventData.gameState);\n        // Добавляем сообщение в лог\n        if (eventData.message) {\n            setGameLog((prev)=>[\n                    ...prev.slice(-9),\n                    eventData.message\n                ]);\n        }\n        // Обрабатываем разные типы событий\n        switch(eventData.type){\n            case GameEvent.GAME_STARTED:\n                setIsGameStarted(true);\n                break;\n            case GameEvent.GAME_ENDED:\n                setIsGameStarted(false);\n                break;\n        }\n    }, []);\n    // Создание новой игры\n    const createNewGame = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        // Создаем игрока-человека и бота\n        const humanPlayer = {\n            id: \"human\",\n            name: \"Вы\",\n            hand: [],\n            isActive: false\n        };\n        const bot = BotFactory.createBot(BotDifficulty.MEDIUM);\n        const botPlayer = bot.createPlayer();\n        botPlayer.name = \"Бот\";\n        const players = [\n            humanPlayer,\n            botPlayer\n        ];\n        const rules = {\n            variant: DurakVariant.CLASSIC,\n            numberOfPlayers: 2,\n            initialHandSize: 6,\n            attackLimit: 6\n        };\n        const newGame = new DurakGame(players, rules);\n        // Подписываемся на события\n        newGame.addEventListener(handleGameEvent);\n        setGame(newGame);\n        setGameState(newGame.getState());\n        setGameLog([]);\n        setSelectedCard(null);\n        setIsGameStarted(false);\n        return newGame;\n    }, [\n        handleGameEvent\n    ]);\n    // Запуск игры\n    const startGame = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (game) {\n            game.startGame();\n        }\n    }, [\n        game\n    ]);\n    // Ход игрока\n    const makePlayerMove = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((action, cardIndex)=>{\n        if (!game || !gameState) return false;\n        const success = game.makeMove(\"human\", action, cardIndex);\n        if (success) {\n            setSelectedCard(null);\n            // Если игра продолжается и сейчас ход бота, делаем ход бота\n            setTimeout(()=>{\n                const currentState = game.getState();\n                if (currentState.gameStatus === GameStatus.IN_PROGRESS) {\n                    const currentPlayerId = currentState.players[currentState.currentPlayerIndex].id;\n                    if (currentPlayerId !== \"human\") {\n                        // Ход бота\n                        const bot = BotFactory.createBot(BotDifficulty.MEDIUM);\n                        const decision = bot.makeDecision(currentState, currentPlayerId);\n                        game.makeMove(currentPlayerId, decision.action, decision.cardIndex);\n                    }\n                }\n            }, 1000); // Небольшая задержка для реалистичности\n        }\n        return success;\n    }, [\n        game,\n        gameState\n    ]);\n    // Инициализация игры при загрузке компонента\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Временная заглушка для тестирования UI\n        const mockGameState = {\n            gameStatus: GameStatus.NOT_STARTED,\n            players: [\n                {\n                    id: \"human\",\n                    name: \"Вы\",\n                    hand: [\n                        {\n                            rank: \"7\",\n                            suit: \"♠\"\n                        },\n                        {\n                            rank: \"K\",\n                            suit: \"♥\"\n                        },\n                        {\n                            rank: \"9\",\n                            suit: \"♦\"\n                        },\n                        {\n                            rank: \"A\",\n                            suit: \"♣\"\n                        },\n                        {\n                            rank: \"10\",\n                            suit: \"♠\"\n                        },\n                        {\n                            rank: \"J\",\n                            suit: \"♥\"\n                        }\n                    ],\n                    isActive: true\n                },\n                {\n                    id: \"bot\",\n                    name: \"Бот\",\n                    hand: [\n                        {\n                            rank: \"?\",\n                            suit: \"?\"\n                        },\n                        {\n                            rank: \"?\",\n                            suit: \"?\"\n                        },\n                        {\n                            rank: \"?\",\n                            suit: \"?\"\n                        },\n                        {\n                            rank: \"?\",\n                            suit: \"?\"\n                        },\n                        {\n                            rank: \"?\",\n                            suit: \"?\"\n                        },\n                        {\n                            rank: \"?\",\n                            suit: \"?\"\n                        }\n                    ],\n                    isActive: false\n                }\n            ],\n            currentPlayerIndex: 0,\n            attackerIndex: 0,\n            defenderIndex: 1,\n            tableCards: [],\n            deck: Array(24).fill({\n                rank: \"?\",\n                suit: \"?\"\n            }),\n            trumpCard: {\n                rank: \"6\",\n                suit: \"♠\"\n            },\n            trumpSuit: \"♠\"\n        };\n        setGameState(mockGameState);\n        setGameLog([\n            \"Игра готова к запуску\"\n        ]);\n    }, []);\n    // Обработчики действий\n    const handleCardClick = (cardIndex)=>{\n        if (!gameState || gameState.gameStatus !== GameStatus.IN_PROGRESS) return;\n        const currentPlayer = gameState.players[gameState.currentPlayerIndex];\n        if (currentPlayer.id !== \"human\") return;\n        setSelectedCard(cardIndex);\n    };\n    const handleAttack = ()=>{\n        if (selectedCard !== null) {\n            makePlayerMove(PlayerAction.ATTACK, selectedCard);\n        }\n    };\n    const handleDefend = ()=>{\n        if (selectedCard !== null) {\n            makePlayerMove(PlayerAction.DEFEND, selectedCard);\n        }\n    };\n    const handleTake = ()=>{\n        makePlayerMove(PlayerAction.TAKE);\n    };\n    const handlePass = ()=>{\n        makePlayerMove(PlayerAction.PASS);\n    };\n    const handleBackToHome = ()=>{\n        router.push(\"/\");\n    };\n    if (!gameState) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingContainer, {\n            children: \"Загрузка игры...\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n            lineNumber: 210,\n            columnNumber: 12\n        }, undefined);\n    }\n    const humanPlayer = gameState.players.find((p)=>p.id === \"human\");\n    const botPlayer = gameState.players.find((p)=>p.id !== \"human\");\n    const isHumanTurn = ((_gameState_players_gameState_currentPlayerIndex = gameState.players[gameState.currentPlayerIndex]) === null || _gameState_players_gameState_currentPlayerIndex === void 0 ? void 0 : _gameState_players_gameState_currentPlayerIndex.id) === \"human\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Дурак - Козырь Мастер\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Играйте в Дурака против бота\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BackButton, {\n                        onClick: handleBackToHome,\n                        children: \"← Назад\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameTitle, {\n                        children: \"Дурак\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewGameButton, {\n                        onClick: createNewGame,\n                        children: \"Новая игра\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameBoard, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BotArea, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerInfo, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerName, {\n                                        children: botPlayer === null || botPlayer === void 0 ? void 0 : botPlayer.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardCount, {\n                                        children: [\n                                            \"Карт: \",\n                                            botPlayer === null || botPlayer === void 0 ? void 0 : botPlayer.hand.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BotCards, {\n                                children: Array.from({\n                                    length: (botPlayer === null || botPlayer === void 0 ? void 0 : botPlayer.hand.length) || 0\n                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card_AnimatedCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        rank: \"?\",\n                                        suit: \"?\",\n                                        isFlipped: true,\n                                        size: \"small\",\n                                        disabled: true\n                                    }, index, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CenterArea, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameInfo, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrumpInfo, {\n                                        children: [\n                                            \"Козырь: \",\n                                            gameState.trumpCard ? \"\".concat(gameState.trumpCard.rank, \" \").concat(gameState.trumpCard.suit) : \"Неизвестно\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeckInfo, {\n                                        children: [\n                                            \"Колода: \",\n                                            gameState.deck.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusInfo, {\n                                        children: [\n                                            gameState.gameStatus === GameStatus.NOT_STARTED && \"Игра не начата\",\n                                            gameState.gameStatus === GameStatus.IN_PROGRESS && (isHumanTurn ? \"Ваш ход\" : \"Ход бота\"),\n                                            gameState.gameStatus === GameStatus.FINISHED && \"Игра окончена! \".concat(((_gameState_winner = gameState.winner) === null || _gameState_winner === void 0 ? void 0 : _gameState_winner.name) === \"Вы\" ? \"Вы победили!\" : \"Бот победил!\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableArea, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableTitle, {\n                                        children: \"Стол\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCards, {\n                                        children: gameState.tableCards.map((pair, pairIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardPair, {\n                                                children: pair.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCardWrapper, {\n                                                        isDefense: cardIndex === 1,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card_AnimatedCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            rank: card.rank,\n                                                            suit: card.suit,\n                                                            size: \"medium\",\n                                                            animationType: \"slide\",\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, cardIndex, false, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, pairIndex, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerArea, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerInfo, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerName, {\n                                        children: humanPlayer === null || humanPlayer === void 0 ? void 0 : humanPlayer.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardCount, {\n                                        children: [\n                                            \"Карт: \",\n                                            humanPlayer === null || humanPlayer === void 0 ? void 0 : humanPlayer.hand.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerCards, {\n                                children: humanPlayer === null || humanPlayer === void 0 ? void 0 : humanPlayer.hand.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card_AnimatedCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        rank: card.rank,\n                                        suit: card.suit,\n                                        isSelected: selectedCard === index,\n                                        onClick: ()=>handleCardClick(index),\n                                        animationType: selectedCard === index ? \"glow\" : \"none\",\n                                        size: \"medium\"\n                                    }, index, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ControlPanel, {\n                children: !isGameStarted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StartButton, {\n                    onClick: startGame,\n                    children: \"Начать игру\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButtons, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                            onClick: handleAttack,\n                            disabled: !isHumanTurn || selectedCard === null,\n                            children: \"Атаковать\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                            onClick: handleDefend,\n                            disabled: !isHumanTurn || selectedCard === null,\n                            children: \"Защищаться\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                            onClick: handleTake,\n                            disabled: !isHumanTurn,\n                            children: \"Взять\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                            onClick: handlePass,\n                            disabled: !isHumanTurn,\n                            children: \"Пас\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameLogArea, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogTitle, {\n                        children: \"Лог игры\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogContent, {\n                        children: gameLog.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogMessage, {\n                                children: message\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n        lineNumber: 218,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DurakGamePage, \"mApp90sMEw3JjHVR9FQc1wv79yc=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = DurakGamePage;\n// Стилизованные компоненты\nconst Container = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__Container\",\n    componentId: \"sc-ed46f6bd-0\"\n})([\n    \"min-height:100vh;background:linear-gradient(135deg,#1e3c72 0%,#2a5298 100%);color:white;display:flex;flex-direction:column;\"\n]);\n_c1 = Container;\nconst LoadingContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__LoadingContainer\",\n    componentId: \"sc-ed46f6bd-1\"\n})([\n    \"min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#1e3c72 0%,#2a5298 100%);color:white;font-size:1.5rem;\"\n]);\n_c2 = LoadingContainer;\nconst GameHeader = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].header.withConfig({\n    displayName: \"durak__GameHeader\",\n    componentId: \"sc-ed46f6bd-2\"\n})([\n    \"display:flex;justify-content:space-between;align-items:center;padding:1rem 2rem;background:rgba(0,0,0,0.2);backdrop-filter:blur(10px);\"\n]);\n_c3 = GameHeader;\nconst BackButton = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].button.withConfig({\n    displayName: \"durak__BackButton\",\n    componentId: \"sc-ed46f6bd-3\"\n})([\n    \"background:rgba(255,255,255,0.1);border:1px solid rgba(255,255,255,0.2);color:white;padding:0.5rem 1rem;border-radius:8px;cursor:pointer;transition:all 0.3s ease;&:hover{background:rgba(255,255,255,0.2);}\"\n]);\n_c4 = BackButton;\nconst GameTitle = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].h1.withConfig({\n    displayName: \"durak__GameTitle\",\n    componentId: \"sc-ed46f6bd-4\"\n})([\n    \"font-size:2rem;margin:0;text-shadow:2px 2px 4px rgba(0,0,0,0.3);\"\n]);\n_c5 = GameTitle;\nconst NewGameButton = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].button.withConfig({\n    displayName: \"durak__NewGameButton\",\n    componentId: \"sc-ed46f6bd-5\"\n})([\n    \"background:linear-gradient(135deg,#4CAF50,#45a049);border:none;color:white;padding:0.5rem 1rem;border-radius:8px;cursor:pointer;transition:all 0.3s ease;font-weight:600;&:hover{background:linear-gradient(135deg,#45a049,#4CAF50);transform:translateY(-2px);}\"\n]);\n_c6 = NewGameButton;\nconst GameBoard = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__GameBoard\",\n    componentId: \"sc-ed46f6bd-6\"\n})([\n    \"flex:1;display:flex;flex-direction:column;padding:1rem;gap:1rem;\"\n]);\n_c7 = GameBoard;\nconst BotArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__BotArea\",\n    componentId: \"sc-ed46f6bd-7\"\n})([\n    \"display:flex;flex-direction:column;align-items:center;gap:1rem;\"\n]);\n_c8 = BotArea;\nconst PlayerArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__PlayerArea\",\n    componentId: \"sc-ed46f6bd-8\"\n})([\n    \"display:flex;flex-direction:column;align-items:center;gap:1rem;\"\n]);\n_c9 = PlayerArea;\nconst PlayerInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__PlayerInfo\",\n    componentId: \"sc-ed46f6bd-9\"\n})([\n    \"display:flex;gap:2rem;align-items:center;\"\n]);\n_c10 = PlayerInfo;\nconst PlayerName = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].h3.withConfig({\n    displayName: \"durak__PlayerName\",\n    componentId: \"sc-ed46f6bd-10\"\n})([\n    \"margin:0;font-size:1.2rem;\"\n]);\n_c11 = PlayerName;\nconst CardCount = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].span.withConfig({\n    displayName: \"durak__CardCount\",\n    componentId: \"sc-ed46f6bd-11\"\n})([\n    \"background:rgba(255,255,255,0.1);padding:0.25rem 0.5rem;border-radius:4px;font-size:0.9rem;\"\n]);\n_c12 = CardCount;\nconst BotCards = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__BotCards\",\n    componentId: \"sc-ed46f6bd-12\"\n})([\n    \"display:flex;gap:0.5rem;flex-wrap:wrap;justify-content:center;\"\n]);\n_c13 = BotCards;\nconst BotCard = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__BotCard\",\n    componentId: \"sc-ed46f6bd-13\"\n})([\n    'width:60px;height:84px;background:linear-gradient(135deg,#8B4513,#A0522D);border:2px solid #654321;border-radius:8px;display:flex;align-items:center;justify-content:center;color:white;font-weight:bold;box-shadow:0 4px 8px rgba(0,0,0,0.3);position:relative;&::after{content:\"\\uD83C\\uDCA0\";font-size:2rem;}'\n]);\nconst PlayerCards = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__PlayerCards\",\n    componentId: \"sc-ed46f6bd-14\"\n})([\n    \"display:flex;gap:0.5rem;flex-wrap:wrap;justify-content:center;max-width:100%;\"\n]);\n_c14 = PlayerCards;\nconst PlayerCard = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__PlayerCard\",\n    componentId: \"sc-ed46f6bd-15\"\n})([\n    \"width:80px;height:112px;background:\",\n    \";border:3px solid \",\n    \";border-radius:12px;display:flex;flex-direction:column;align-items:center;justify-content:space-between;padding:0.5rem;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 12px rgba(0,0,0,0.2);color:#333;&:hover{transform:translateY(-4px);box-shadow:0 8px 20px rgba(0,0,0,0.3);}\"\n], (props)=>props.isSelected ? \"linear-gradient(135deg, #FFD700, #FFA500)\" : \"linear-gradient(135deg, #FFFFFF, #F0F0F0)\", (props)=>props.isSelected ? \"#FF6B35\" : \"#DDD\");\nconst CardRank = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__CardRank\",\n    componentId: \"sc-ed46f6bd-16\"\n})([\n    \"font-size:1.2rem;font-weight:bold;\"\n]);\nconst CardSuit = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__CardSuit\",\n    componentId: \"sc-ed46f6bd-17\"\n})([\n    \"font-size:1.5rem;\"\n]);\nconst CenterArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__CenterArea\",\n    componentId: \"sc-ed46f6bd-18\"\n})([\n    \"flex:1;display:flex;flex-direction:column;align-items:center;gap:2rem;padding:2rem 0;\"\n]);\n_c15 = CenterArea;\nconst GameInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__GameInfo\",\n    componentId: \"sc-ed46f6bd-19\"\n})([\n    \"display:flex;gap:2rem;flex-wrap:wrap;justify-content:center;background:rgba(255,255,255,0.1);padding:1rem;border-radius:12px;backdrop-filter:blur(10px);\"\n]);\n_c16 = GameInfo;\nconst TrumpInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__TrumpInfo\",\n    componentId: \"sc-ed46f6bd-20\"\n})([\n    \"background:rgba(255,215,0,0.2);padding:0.5rem 1rem;border-radius:8px;border:1px solid rgba(255,215,0,0.3);\"\n]);\n_c17 = TrumpInfo;\nconst DeckInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__DeckInfo\",\n    componentId: \"sc-ed46f6bd-21\"\n})([\n    \"background:rgba(255,255,255,0.1);padding:0.5rem 1rem;border-radius:8px;border:1px solid rgba(255,255,255,0.2);\"\n]);\n_c18 = DeckInfo;\nconst StatusInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__StatusInfo\",\n    componentId: \"sc-ed46f6bd-22\"\n})([\n    \"background:rgba(76,175,80,0.2);padding:0.5rem 1rem;border-radius:8px;border:1px solid rgba(76,175,80,0.3);font-weight:600;\"\n]);\n_c19 = StatusInfo;\nconst TableArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__TableArea\",\n    componentId: \"sc-ed46f6bd-23\"\n})([\n    \"display:flex;flex-direction:column;align-items:center;gap:1rem;min-height:150px;\"\n]);\n_c20 = TableArea;\nconst TableTitle = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].h3.withConfig({\n    displayName: \"durak__TableTitle\",\n    componentId: \"sc-ed46f6bd-24\"\n})([\n    \"margin:0;color:rgba(255,255,255,0.8);\"\n]);\n_c21 = TableTitle;\nconst TableCards = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__TableCards\",\n    componentId: \"sc-ed46f6bd-25\"\n})([\n    \"display:flex;gap:1rem;flex-wrap:wrap;justify-content:center;min-height:100px;align-items:center;\"\n]);\n_c22 = TableCards;\nconst CardPair = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__CardPair\",\n    componentId: \"sc-ed46f6bd-26\"\n})([\n    \"display:flex;gap:0.25rem;position:relative;\"\n]);\n_c23 = CardPair;\nconst TableCardWrapper = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__TableCardWrapper\",\n    componentId: \"sc-ed46f6bd-27\"\n})([\n    \"transform:\",\n    \";z-index:\",\n    \";transition:transform 0.3s ease;\"\n], (props)=>props.isDefense ? \"rotate(15deg) translateX(-20px)\" : \"none\", (props)=>props.isDefense ? 2 : 1);\n_c24 = TableCardWrapper;\nconst TableCard = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__TableCard\",\n    componentId: \"sc-ed46f6bd-28\"\n})([\n    \"width:70px;height:98px;background:linear-gradient(135deg,#FFFFFF,#F0F0F0);border:2px solid #DDD;border-radius:10px;display:flex;align-items:center;justify-content:center;color:#333;font-weight:bold;font-size:0.9rem;box-shadow:0 4px 8px rgba(0,0,0,0.2);\"\n]);\nconst ControlPanel = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__ControlPanel\",\n    componentId: \"sc-ed46f6bd-29\"\n})([\n    \"padding:1rem 2rem;background:rgba(0,0,0,0.2);backdrop-filter:blur(10px);display:flex;justify-content:center;\"\n]);\n_c25 = ControlPanel;\nconst StartButton = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].button.withConfig({\n    displayName: \"durak__StartButton\",\n    componentId: \"sc-ed46f6bd-30\"\n})([\n    \"background:linear-gradient(135deg,#4CAF50,#45a049);border:none;color:white;padding:1rem 2rem;border-radius:12px;font-size:1.2rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(76,175,80,0.3);&:hover{background:linear-gradient(135deg,#45a049,#4CAF50);transform:translateY(-2px);box-shadow:0 6px 20px rgba(76,175,80,0.4);}\"\n]);\n_c26 = StartButton;\nconst ActionButtons = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__ActionButtons\",\n    componentId: \"sc-ed46f6bd-31\"\n})([\n    \"display:flex;gap:1rem;flex-wrap:wrap;justify-content:center;\"\n]);\n_c27 = ActionButtons;\nconst ActionButton = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].button.withConfig({\n    displayName: \"durak__ActionButton\",\n    componentId: \"sc-ed46f6bd-32\"\n})([\n    \"background:\",\n    \";border:none;color:\",\n    \";padding:0.75rem 1.5rem;border-radius:8px;font-weight:600;cursor:\",\n    \";transition:all 0.3s ease;box-shadow:\",\n    \";&:hover{\",\n    \"}\"\n], (props)=>props.disabled ? \"rgba(255, 255, 255, 0.1)\" : \"linear-gradient(135deg, #2196F3, #1976D2)\", (props)=>props.disabled ? \"rgba(255, 255, 255, 0.5)\" : \"white\", (props)=>props.disabled ? \"not-allowed\" : \"pointer\", (props)=>props.disabled ? \"none\" : \"0 4px 12px rgba(33, 150, 243, 0.3)\", (props)=>!props.disabled && \"\\n      background: linear-gradient(135deg, #1976D2, #2196F3);\\n      transform: translateY(-2px);\\n      box-shadow: 0 6px 16px rgba(33, 150, 243, 0.4);\\n    \");\n_c28 = ActionButton;\nconst GameLogArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__GameLogArea\",\n    componentId: \"sc-ed46f6bd-33\"\n})([\n    \"background:rgba(0,0,0,0.3);padding:1rem;max-height:200px;overflow-y:auto;\"\n]);\n_c29 = GameLogArea;\nconst LogTitle = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].h4.withConfig({\n    displayName: \"durak__LogTitle\",\n    componentId: \"sc-ed46f6bd-34\"\n})([\n    \"margin:0 0 1rem 0;color:rgba(255,255,255,0.8);\"\n]);\n_c30 = LogTitle;\nconst LogContent = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__LogContent\",\n    componentId: \"sc-ed46f6bd-35\"\n})([\n    \"display:flex;flex-direction:column;gap:0.25rem;\"\n]);\n_c31 = LogContent;\nconst LogMessage = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__LogMessage\",\n    componentId: \"sc-ed46f6bd-36\"\n})([\n    \"font-size:0.9rem;color:rgba(255,255,255,0.9);padding:0.25rem 0;border-bottom:1px solid rgba(255,255,255,0.1);\"\n]);\n_c32 = LogMessage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DurakGamePage);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32;\n$RefreshReg$(_c, \"DurakGamePage\");\n$RefreshReg$(_c1, \"Container\");\n$RefreshReg$(_c2, \"LoadingContainer\");\n$RefreshReg$(_c3, \"GameHeader\");\n$RefreshReg$(_c4, \"BackButton\");\n$RefreshReg$(_c5, \"GameTitle\");\n$RefreshReg$(_c6, \"NewGameButton\");\n$RefreshReg$(_c7, \"GameBoard\");\n$RefreshReg$(_c8, \"BotArea\");\n$RefreshReg$(_c9, \"PlayerArea\");\n$RefreshReg$(_c10, \"PlayerInfo\");\n$RefreshReg$(_c11, \"PlayerName\");\n$RefreshReg$(_c12, \"CardCount\");\n$RefreshReg$(_c13, \"BotCards\");\n$RefreshReg$(_c14, \"PlayerCards\");\n$RefreshReg$(_c15, \"CenterArea\");\n$RefreshReg$(_c16, \"GameInfo\");\n$RefreshReg$(_c17, \"TrumpInfo\");\n$RefreshReg$(_c18, \"DeckInfo\");\n$RefreshReg$(_c19, \"StatusInfo\");\n$RefreshReg$(_c20, \"TableArea\");\n$RefreshReg$(_c21, \"TableTitle\");\n$RefreshReg$(_c22, \"TableCards\");\n$RefreshReg$(_c23, \"CardPair\");\n$RefreshReg$(_c24, \"TableCardWrapper\");\n$RefreshReg$(_c25, \"ControlPanel\");\n$RefreshReg$(_c26, \"StartButton\");\n$RefreshReg$(_c27, \"ActionButtons\");\n$RefreshReg$(_c28, \"ActionButton\");\n$RefreshReg$(_c29, \"GameLogArea\");\n$RefreshReg$(_c30, \"LogTitle\");\n$RefreshReg$(_c31, \"LogContent\");\n$RefreshReg$(_c32, \"LogMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/games/durak.tsx\n"));

/***/ })

});