"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/games/durak",{

/***/ "./src/pages/games/durak.tsx":
/*!***********************************!*\
  !*** ./src/pages/games/durak.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Card_AnimatedCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Card/AnimatedCard */ \"./src/components/Card/AnimatedCard.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n// Временно используем прямой импорт для отладки\n// import {\n//   DurakGame,\n//   BotFactory,\n//   BotDifficulty,\n//   GameStatus,\n//   PlayerAction,\n//   DurakVariant,\n//   GameEventData,\n//   GameEvent\n// } from \"@kozyr-master/core\";\n// Создаем заглушки для тестирования UI\nconst DurakGame = null;\nconst BotFactory = null;\nconst BotDifficulty = {\n    MEDIUM: \"medium\"\n};\nconst GameStatus = {\n    NOT_STARTED: \"not_started\",\n    IN_PROGRESS: \"in_progress\",\n    FINISHED: \"finished\"\n};\nconst PlayerAction = {\n    ATTACK: \"attack\",\n    DEFEND: \"defend\",\n    TAKE: \"take\",\n    PASS: \"pass\"\n};\nconst DurakVariant = {\n    CLASSIC: \"classic\"\n};\nconst GameEvent = {\n    GAME_STARTED: \"game_started\",\n    GAME_ENDED: \"game_ended\",\n    PLAYER_MOVED: \"player_moved\"\n};\nconst DurakGamePage = ()=>{\n    var _gameState_players_gameState_currentPlayerIndex, _gameState_winner;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [game, setGame] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [selectedCard, setSelectedCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [gameLog, setGameLog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isGameStarted, setIsGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Обработчик событий игры\n    const handleGameEvent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((eventData)=>{\n        console.log(\"Game event:\", eventData);\n        setGameState(eventData.gameState);\n        // Добавляем сообщение в лог\n        if (eventData.message) {\n            setGameLog((prev)=>[\n                    ...prev.slice(-9),\n                    eventData.message\n                ]);\n        }\n        // Обрабатываем разные типы событий\n        switch(eventData.type){\n            case GameEvent.GAME_STARTED:\n                setIsGameStarted(true);\n                break;\n            case GameEvent.GAME_ENDED:\n                setIsGameStarted(false);\n                break;\n        }\n    }, []);\n    // Создание новой игры\n    const createNewGame = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        // Создаем игрока-человека и бота\n        const humanPlayer = {\n            id: \"human\",\n            name: \"Вы\",\n            hand: [],\n            isActive: false\n        };\n        const bot = BotFactory.createBot(BotDifficulty.MEDIUM);\n        const botPlayer = bot.createPlayer();\n        botPlayer.name = \"Бот\";\n        const players = [\n            humanPlayer,\n            botPlayer\n        ];\n        const rules = {\n            variant: DurakVariant.CLASSIC,\n            numberOfPlayers: 2,\n            initialHandSize: 6,\n            attackLimit: 6\n        };\n        const newGame = new DurakGame(players, rules);\n        // Подписываемся на события\n        newGame.addEventListener(handleGameEvent);\n        setGame(newGame);\n        setGameState(newGame.getState());\n        setGameLog([]);\n        setSelectedCard(null);\n        setIsGameStarted(false);\n        return newGame;\n    }, [\n        handleGameEvent\n    ]);\n    // Запуск игры\n    const startGame = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (game) {\n            game.startGame();\n        }\n    }, [\n        game\n    ]);\n    // Ход игрока\n    const makePlayerMove = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((action, cardIndex)=>{\n        if (!game || !gameState) return false;\n        const success = game.makeMove(\"human\", action, cardIndex);\n        if (success) {\n            setSelectedCard(null);\n            // Если игра продолжается и сейчас ход бота, делаем ход бота\n            setTimeout(()=>{\n                const currentState = game.getState();\n                if (currentState.gameStatus === GameStatus.IN_PROGRESS) {\n                    const currentPlayerId = currentState.players[currentState.currentPlayerIndex].id;\n                    if (currentPlayerId !== \"human\") {\n                        // Ход бота\n                        const bot = BotFactory.createBot(BotDifficulty.MEDIUM);\n                        const decision = bot.makeDecision(currentState, currentPlayerId);\n                        game.makeMove(currentPlayerId, decision.action, decision.cardIndex);\n                    }\n                }\n            }, 1000); // Небольшая задержка для реалистичности\n        }\n        return success;\n    }, [\n        game,\n        gameState\n    ]);\n    // Инициализация игры при загрузке компонента\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        createNewGame();\n    }, [\n        createNewGame\n    ]);\n    // Обработчики действий\n    const handleCardClick = (cardIndex)=>{\n        if (!gameState || gameState.gameStatus !== GameStatus.IN_PROGRESS) return;\n        const currentPlayer = gameState.players[gameState.currentPlayerIndex];\n        if (currentPlayer.id !== \"human\") return;\n        setSelectedCard(cardIndex);\n    };\n    const handleAttack = ()=>{\n        if (selectedCard !== null) {\n            makePlayerMove(PlayerAction.ATTACK, selectedCard);\n        }\n    };\n    const handleDefend = ()=>{\n        if (selectedCard !== null) {\n            makePlayerMove(PlayerAction.DEFEND, selectedCard);\n        }\n    };\n    const handleTake = ()=>{\n        makePlayerMove(PlayerAction.TAKE);\n    };\n    const handlePass = ()=>{\n        makePlayerMove(PlayerAction.PASS);\n    };\n    const handleBackToHome = ()=>{\n        router.push(\"/\");\n    };\n    if (!gameState) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingContainer, {\n            children: \"Загрузка игры...\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n            lineNumber: 169,\n            columnNumber: 12\n        }, undefined);\n    }\n    const humanPlayer = gameState.players.find((p)=>p.id === \"human\");\n    const botPlayer = gameState.players.find((p)=>p.id !== \"human\");\n    const isHumanTurn = ((_gameState_players_gameState_currentPlayerIndex = gameState.players[gameState.currentPlayerIndex]) === null || _gameState_players_gameState_currentPlayerIndex === void 0 ? void 0 : _gameState_players_gameState_currentPlayerIndex.id) === \"human\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Дурак - Козырь Мастер\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Играйте в Дурака против бота\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BackButton, {\n                        onClick: handleBackToHome,\n                        children: \"← Назад\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameTitle, {\n                        children: \"Дурак\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewGameButton, {\n                        onClick: createNewGame,\n                        children: \"Новая игра\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameBoard, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BotArea, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerInfo, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerName, {\n                                        children: botPlayer === null || botPlayer === void 0 ? void 0 : botPlayer.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardCount, {\n                                        children: [\n                                            \"Карт: \",\n                                            botPlayer === null || botPlayer === void 0 ? void 0 : botPlayer.hand.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BotCards, {\n                                children: Array.from({\n                                    length: (botPlayer === null || botPlayer === void 0 ? void 0 : botPlayer.hand.length) || 0\n                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card_AnimatedCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        rank: \"?\",\n                                        suit: \"?\",\n                                        isFlipped: true,\n                                        size: \"small\",\n                                        disabled: true\n                                    }, index, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CenterArea, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameInfo, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrumpInfo, {\n                                        children: [\n                                            \"Козырь: \",\n                                            gameState.trumpCard ? \"\".concat(gameState.trumpCard.rank, \" \").concat(gameState.trumpCard.suit) : \"Неизвестно\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeckInfo, {\n                                        children: [\n                                            \"Колода: \",\n                                            gameState.deck.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusInfo, {\n                                        children: [\n                                            gameState.gameStatus === GameStatus.NOT_STARTED && \"Игра не начата\",\n                                            gameState.gameStatus === GameStatus.IN_PROGRESS && (isHumanTurn ? \"Ваш ход\" : \"Ход бота\"),\n                                            gameState.gameStatus === GameStatus.FINISHED && \"Игра окончена! \".concat(((_gameState_winner = gameState.winner) === null || _gameState_winner === void 0 ? void 0 : _gameState_winner.name) === \"Вы\" ? \"Вы победили!\" : \"Бот победил!\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableArea, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableTitle, {\n                                        children: \"Стол\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCards, {\n                                        children: gameState.tableCards.map((pair, pairIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardPair, {\n                                                children: pair.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCardWrapper, {\n                                                        isDefense: cardIndex === 1,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card_AnimatedCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            rank: card.rank,\n                                                            suit: card.suit,\n                                                            size: \"medium\",\n                                                            animationType: \"slide\",\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, cardIndex, false, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, pairIndex, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerArea, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerInfo, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerName, {\n                                        children: humanPlayer === null || humanPlayer === void 0 ? void 0 : humanPlayer.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardCount, {\n                                        children: [\n                                            \"Карт: \",\n                                            humanPlayer === null || humanPlayer === void 0 ? void 0 : humanPlayer.hand.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerCards, {\n                                children: humanPlayer === null || humanPlayer === void 0 ? void 0 : humanPlayer.hand.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card_AnimatedCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        rank: card.rank,\n                                        suit: card.suit,\n                                        isSelected: selectedCard === index,\n                                        onClick: ()=>handleCardClick(index),\n                                        animationType: selectedCard === index ? \"glow\" : \"none\",\n                                        size: \"medium\"\n                                    }, index, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ControlPanel, {\n                children: !isGameStarted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StartButton, {\n                    onClick: startGame,\n                    children: \"Начать игру\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButtons, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                            onClick: handleAttack,\n                            disabled: !isHumanTurn || selectedCard === null,\n                            children: \"Атаковать\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                            onClick: handleDefend,\n                            disabled: !isHumanTurn || selectedCard === null,\n                            children: \"Защищаться\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                            onClick: handleTake,\n                            disabled: !isHumanTurn,\n                            children: \"Взять\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                            onClick: handlePass,\n                            disabled: !isHumanTurn,\n                            children: \"Пас\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameLogArea, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogTitle, {\n                        children: \"Лог игры\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogContent, {\n                        children: gameLog.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogMessage, {\n                                children: message\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                lineNumber: 306,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DurakGamePage, \"mApp90sMEw3JjHVR9FQc1wv79yc=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = DurakGamePage;\n// Стилизованные компоненты\nconst Container = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__Container\",\n    componentId: \"sc-8c9539e6-0\"\n})([\n    \"min-height:100vh;background:linear-gradient(135deg,#1e3c72 0%,#2a5298 100%);color:white;display:flex;flex-direction:column;\"\n]);\n_c1 = Container;\nconst LoadingContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__LoadingContainer\",\n    componentId: \"sc-8c9539e6-1\"\n})([\n    \"min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#1e3c72 0%,#2a5298 100%);color:white;font-size:1.5rem;\"\n]);\n_c2 = LoadingContainer;\nconst GameHeader = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].header.withConfig({\n    displayName: \"durak__GameHeader\",\n    componentId: \"sc-8c9539e6-2\"\n})([\n    \"display:flex;justify-content:space-between;align-items:center;padding:1rem 2rem;background:rgba(0,0,0,0.2);backdrop-filter:blur(10px);\"\n]);\n_c3 = GameHeader;\nconst BackButton = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].button.withConfig({\n    displayName: \"durak__BackButton\",\n    componentId: \"sc-8c9539e6-3\"\n})([\n    \"background:rgba(255,255,255,0.1);border:1px solid rgba(255,255,255,0.2);color:white;padding:0.5rem 1rem;border-radius:8px;cursor:pointer;transition:all 0.3s ease;&:hover{background:rgba(255,255,255,0.2);}\"\n]);\n_c4 = BackButton;\nconst GameTitle = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].h1.withConfig({\n    displayName: \"durak__GameTitle\",\n    componentId: \"sc-8c9539e6-4\"\n})([\n    \"font-size:2rem;margin:0;text-shadow:2px 2px 4px rgba(0,0,0,0.3);\"\n]);\n_c5 = GameTitle;\nconst NewGameButton = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].button.withConfig({\n    displayName: \"durak__NewGameButton\",\n    componentId: \"sc-8c9539e6-5\"\n})([\n    \"background:linear-gradient(135deg,#4CAF50,#45a049);border:none;color:white;padding:0.5rem 1rem;border-radius:8px;cursor:pointer;transition:all 0.3s ease;font-weight:600;&:hover{background:linear-gradient(135deg,#45a049,#4CAF50);transform:translateY(-2px);}\"\n]);\n_c6 = NewGameButton;\nconst GameBoard = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__GameBoard\",\n    componentId: \"sc-8c9539e6-6\"\n})([\n    \"flex:1;display:flex;flex-direction:column;padding:1rem;gap:1rem;\"\n]);\n_c7 = GameBoard;\nconst BotArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__BotArea\",\n    componentId: \"sc-8c9539e6-7\"\n})([\n    \"display:flex;flex-direction:column;align-items:center;gap:1rem;\"\n]);\n_c8 = BotArea;\nconst PlayerArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__PlayerArea\",\n    componentId: \"sc-8c9539e6-8\"\n})([\n    \"display:flex;flex-direction:column;align-items:center;gap:1rem;\"\n]);\n_c9 = PlayerArea;\nconst PlayerInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__PlayerInfo\",\n    componentId: \"sc-8c9539e6-9\"\n})([\n    \"display:flex;gap:2rem;align-items:center;\"\n]);\n_c10 = PlayerInfo;\nconst PlayerName = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].h3.withConfig({\n    displayName: \"durak__PlayerName\",\n    componentId: \"sc-8c9539e6-10\"\n})([\n    \"margin:0;font-size:1.2rem;\"\n]);\n_c11 = PlayerName;\nconst CardCount = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].span.withConfig({\n    displayName: \"durak__CardCount\",\n    componentId: \"sc-8c9539e6-11\"\n})([\n    \"background:rgba(255,255,255,0.1);padding:0.25rem 0.5rem;border-radius:4px;font-size:0.9rem;\"\n]);\n_c12 = CardCount;\nconst BotCards = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__BotCards\",\n    componentId: \"sc-8c9539e6-12\"\n})([\n    \"display:flex;gap:0.5rem;flex-wrap:wrap;justify-content:center;\"\n]);\n_c13 = BotCards;\nconst BotCard = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__BotCard\",\n    componentId: \"sc-8c9539e6-13\"\n})([\n    'width:60px;height:84px;background:linear-gradient(135deg,#8B4513,#A0522D);border:2px solid #654321;border-radius:8px;display:flex;align-items:center;justify-content:center;color:white;font-weight:bold;box-shadow:0 4px 8px rgba(0,0,0,0.3);position:relative;&::after{content:\"\\uD83C\\uDCA0\";font-size:2rem;}'\n]);\nconst PlayerCards = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__PlayerCards\",\n    componentId: \"sc-8c9539e6-14\"\n})([\n    \"display:flex;gap:0.5rem;flex-wrap:wrap;justify-content:center;max-width:100%;\"\n]);\n_c14 = PlayerCards;\nconst PlayerCard = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__PlayerCard\",\n    componentId: \"sc-8c9539e6-15\"\n})([\n    \"width:80px;height:112px;background:\",\n    \";border:3px solid \",\n    \";border-radius:12px;display:flex;flex-direction:column;align-items:center;justify-content:space-between;padding:0.5rem;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 12px rgba(0,0,0,0.2);color:#333;&:hover{transform:translateY(-4px);box-shadow:0 8px 20px rgba(0,0,0,0.3);}\"\n], (props)=>props.isSelected ? \"linear-gradient(135deg, #FFD700, #FFA500)\" : \"linear-gradient(135deg, #FFFFFF, #F0F0F0)\", (props)=>props.isSelected ? \"#FF6B35\" : \"#DDD\");\nconst CardRank = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__CardRank\",\n    componentId: \"sc-8c9539e6-16\"\n})([\n    \"font-size:1.2rem;font-weight:bold;\"\n]);\nconst CardSuit = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__CardSuit\",\n    componentId: \"sc-8c9539e6-17\"\n})([\n    \"font-size:1.5rem;\"\n]);\nconst CenterArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__CenterArea\",\n    componentId: \"sc-8c9539e6-18\"\n})([\n    \"flex:1;display:flex;flex-direction:column;align-items:center;gap:2rem;padding:2rem 0;\"\n]);\n_c15 = CenterArea;\nconst GameInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__GameInfo\",\n    componentId: \"sc-8c9539e6-19\"\n})([\n    \"display:flex;gap:2rem;flex-wrap:wrap;justify-content:center;background:rgba(255,255,255,0.1);padding:1rem;border-radius:12px;backdrop-filter:blur(10px);\"\n]);\n_c16 = GameInfo;\nconst TrumpInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__TrumpInfo\",\n    componentId: \"sc-8c9539e6-20\"\n})([\n    \"background:rgba(255,215,0,0.2);padding:0.5rem 1rem;border-radius:8px;border:1px solid rgba(255,215,0,0.3);\"\n]);\n_c17 = TrumpInfo;\nconst DeckInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__DeckInfo\",\n    componentId: \"sc-8c9539e6-21\"\n})([\n    \"background:rgba(255,255,255,0.1);padding:0.5rem 1rem;border-radius:8px;border:1px solid rgba(255,255,255,0.2);\"\n]);\n_c18 = DeckInfo;\nconst StatusInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__StatusInfo\",\n    componentId: \"sc-8c9539e6-22\"\n})([\n    \"background:rgba(76,175,80,0.2);padding:0.5rem 1rem;border-radius:8px;border:1px solid rgba(76,175,80,0.3);font-weight:600;\"\n]);\n_c19 = StatusInfo;\nconst TableArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__TableArea\",\n    componentId: \"sc-8c9539e6-23\"\n})([\n    \"display:flex;flex-direction:column;align-items:center;gap:1rem;min-height:150px;\"\n]);\n_c20 = TableArea;\nconst TableTitle = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].h3.withConfig({\n    displayName: \"durak__TableTitle\",\n    componentId: \"sc-8c9539e6-24\"\n})([\n    \"margin:0;color:rgba(255,255,255,0.8);\"\n]);\n_c21 = TableTitle;\nconst TableCards = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__TableCards\",\n    componentId: \"sc-8c9539e6-25\"\n})([\n    \"display:flex;gap:1rem;flex-wrap:wrap;justify-content:center;min-height:100px;align-items:center;\"\n]);\n_c22 = TableCards;\nconst CardPair = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__CardPair\",\n    componentId: \"sc-8c9539e6-26\"\n})([\n    \"display:flex;gap:0.25rem;position:relative;\"\n]);\n_c23 = CardPair;\nconst TableCardWrapper = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__TableCardWrapper\",\n    componentId: \"sc-8c9539e6-27\"\n})([\n    \"transform:\",\n    \";z-index:\",\n    \";transition:transform 0.3s ease;\"\n], (props)=>props.isDefense ? \"rotate(15deg) translateX(-20px)\" : \"none\", (props)=>props.isDefense ? 2 : 1);\n_c24 = TableCardWrapper;\nconst TableCard = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__TableCard\",\n    componentId: \"sc-8c9539e6-28\"\n})([\n    \"width:70px;height:98px;background:linear-gradient(135deg,#FFFFFF,#F0F0F0);border:2px solid #DDD;border-radius:10px;display:flex;align-items:center;justify-content:center;color:#333;font-weight:bold;font-size:0.9rem;box-shadow:0 4px 8px rgba(0,0,0,0.2);\"\n]);\nconst ControlPanel = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__ControlPanel\",\n    componentId: \"sc-8c9539e6-29\"\n})([\n    \"padding:1rem 2rem;background:rgba(0,0,0,0.2);backdrop-filter:blur(10px);display:flex;justify-content:center;\"\n]);\n_c25 = ControlPanel;\nconst StartButton = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].button.withConfig({\n    displayName: \"durak__StartButton\",\n    componentId: \"sc-8c9539e6-30\"\n})([\n    \"background:linear-gradient(135deg,#4CAF50,#45a049);border:none;color:white;padding:1rem 2rem;border-radius:12px;font-size:1.2rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(76,175,80,0.3);&:hover{background:linear-gradient(135deg,#45a049,#4CAF50);transform:translateY(-2px);box-shadow:0 6px 20px rgba(76,175,80,0.4);}\"\n]);\n_c26 = StartButton;\nconst ActionButtons = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__ActionButtons\",\n    componentId: \"sc-8c9539e6-31\"\n})([\n    \"display:flex;gap:1rem;flex-wrap:wrap;justify-content:center;\"\n]);\n_c27 = ActionButtons;\nconst ActionButton = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].button.withConfig({\n    displayName: \"durak__ActionButton\",\n    componentId: \"sc-8c9539e6-32\"\n})([\n    \"background:\",\n    \";border:none;color:\",\n    \";padding:0.75rem 1.5rem;border-radius:8px;font-weight:600;cursor:\",\n    \";transition:all 0.3s ease;box-shadow:\",\n    \";&:hover{\",\n    \"}\"\n], (props)=>props.disabled ? \"rgba(255, 255, 255, 0.1)\" : \"linear-gradient(135deg, #2196F3, #1976D2)\", (props)=>props.disabled ? \"rgba(255, 255, 255, 0.5)\" : \"white\", (props)=>props.disabled ? \"not-allowed\" : \"pointer\", (props)=>props.disabled ? \"none\" : \"0 4px 12px rgba(33, 150, 243, 0.3)\", (props)=>!props.disabled && \"\\n      background: linear-gradient(135deg, #1976D2, #2196F3);\\n      transform: translateY(-2px);\\n      box-shadow: 0 6px 16px rgba(33, 150, 243, 0.4);\\n    \");\n_c28 = ActionButton;\nconst GameLogArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__GameLogArea\",\n    componentId: \"sc-8c9539e6-33\"\n})([\n    \"background:rgba(0,0,0,0.3);padding:1rem;max-height:200px;overflow-y:auto;\"\n]);\n_c29 = GameLogArea;\nconst LogTitle = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].h4.withConfig({\n    displayName: \"durak__LogTitle\",\n    componentId: \"sc-8c9539e6-34\"\n})([\n    \"margin:0 0 1rem 0;color:rgba(255,255,255,0.8);\"\n]);\n_c30 = LogTitle;\nconst LogContent = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__LogContent\",\n    componentId: \"sc-8c9539e6-35\"\n})([\n    \"display:flex;flex-direction:column;gap:0.25rem;\"\n]);\n_c31 = LogContent;\nconst LogMessage = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__LogMessage\",\n    componentId: \"sc-8c9539e6-36\"\n})([\n    \"font-size:0.9rem;color:rgba(255,255,255,0.9);padding:0.25rem 0;border-bottom:1px solid rgba(255,255,255,0.1);\"\n]);\n_c32 = LogMessage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DurakGamePage);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32;\n$RefreshReg$(_c, \"DurakGamePage\");\n$RefreshReg$(_c1, \"Container\");\n$RefreshReg$(_c2, \"LoadingContainer\");\n$RefreshReg$(_c3, \"GameHeader\");\n$RefreshReg$(_c4, \"BackButton\");\n$RefreshReg$(_c5, \"GameTitle\");\n$RefreshReg$(_c6, \"NewGameButton\");\n$RefreshReg$(_c7, \"GameBoard\");\n$RefreshReg$(_c8, \"BotArea\");\n$RefreshReg$(_c9, \"PlayerArea\");\n$RefreshReg$(_c10, \"PlayerInfo\");\n$RefreshReg$(_c11, \"PlayerName\");\n$RefreshReg$(_c12, \"CardCount\");\n$RefreshReg$(_c13, \"BotCards\");\n$RefreshReg$(_c14, \"PlayerCards\");\n$RefreshReg$(_c15, \"CenterArea\");\n$RefreshReg$(_c16, \"GameInfo\");\n$RefreshReg$(_c17, \"TrumpInfo\");\n$RefreshReg$(_c18, \"DeckInfo\");\n$RefreshReg$(_c19, \"StatusInfo\");\n$RefreshReg$(_c20, \"TableArea\");\n$RefreshReg$(_c21, \"TableTitle\");\n$RefreshReg$(_c22, \"TableCards\");\n$RefreshReg$(_c23, \"CardPair\");\n$RefreshReg$(_c24, \"TableCardWrapper\");\n$RefreshReg$(_c25, \"ControlPanel\");\n$RefreshReg$(_c26, \"StartButton\");\n$RefreshReg$(_c27, \"ActionButtons\");\n$RefreshReg$(_c28, \"ActionButton\");\n$RefreshReg$(_c29, \"GameLogArea\");\n$RefreshReg$(_c30, \"LogTitle\");\n$RefreshReg$(_c31, \"LogContent\");\n$RefreshReg$(_c32, \"LogMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/games/durak.tsx\n"));

/***/ })

});