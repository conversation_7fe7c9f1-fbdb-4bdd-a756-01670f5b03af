"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/games/durak",{

/***/ "./src/pages/games/durak.tsx":
/*!***********************************!*\
  !*** ./src/pages/games/durak.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Card_AnimatedCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Card/AnimatedCard */ \"./src/components/Card/AnimatedCard.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n// Временно используем прямой импорт для отладки\n// import {\n//   DurakGame,\n//   BotFactory,\n//   BotDifficulty,\n//   GameStatus,\n//   PlayerAction,\n//   DurakVariant,\n//   GameEventData,\n//   GameEvent\n// } from \"@kozyr-master/core\";\n// Создаем заглушки для тестирования UI\nconst DurakGame = null;\nconst BotFactory = null;\nconst BotDifficulty = {\n    MEDIUM: \"medium\"\n};\nconst GameStatus = {\n    NOT_STARTED: \"not_started\",\n    IN_PROGRESS: \"in_progress\",\n    FINISHED: \"finished\"\n};\nconst PlayerAction = {\n    ATTACK: \"attack\",\n    DEFEND: \"defend\",\n    TAKE: \"take\",\n    PASS: \"pass\"\n};\nconst DurakVariant = {\n    CLASSIC: \"classic\"\n};\nconst GameEvent = {\n    GAME_STARTED: \"game_started\",\n    GAME_ENDED: \"game_ended\",\n    PLAYER_MOVED: \"player_moved\"\n};\nconst DurakGamePage = ()=>{\n    var _gameState_players_gameState_currentPlayerIndex, _gameState_winner;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [game, setGame] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [selectedCard, setSelectedCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [gameLog, setGameLog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isGameStarted, setIsGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Обработчик событий игры\n    const handleGameEvent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((eventData)=>{\n        console.log(\"Game event:\", eventData);\n        setGameState(eventData.gameState);\n        // Добавляем сообщение в лог\n        if (eventData.message) {\n            setGameLog((prev)=>[\n                    ...prev.slice(-9),\n                    eventData.message\n                ]);\n        }\n        // Обрабатываем разные типы событий\n        switch(eventData.type){\n            case GameEvent.GAME_STARTED:\n                setIsGameStarted(true);\n                break;\n            case GameEvent.GAME_ENDED:\n                setIsGameStarted(false);\n                break;\n        }\n    }, []);\n    // Создание новой игры\n    const createNewGame = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        // Временная заглушка\n        console.log(\"Создание новой игры (заглушка)\");\n        const mockGameState = {\n            gameStatus: GameStatus.NOT_STARTED,\n            players: [\n                {\n                    id: \"human\",\n                    name: \"Вы\",\n                    hand: [\n                        {\n                            rank: \"7\",\n                            suit: \"♠\"\n                        },\n                        {\n                            rank: \"K\",\n                            suit: \"♥\"\n                        },\n                        {\n                            rank: \"9\",\n                            suit: \"♦\"\n                        },\n                        {\n                            rank: \"A\",\n                            suit: \"♣\"\n                        },\n                        {\n                            rank: \"10\",\n                            suit: \"♠\"\n                        },\n                        {\n                            rank: \"J\",\n                            suit: \"♥\"\n                        }\n                    ],\n                    isActive: true\n                },\n                {\n                    id: \"bot\",\n                    name: \"Бот\",\n                    hand: Array(6).fill({\n                        rank: \"?\",\n                        suit: \"?\"\n                    }),\n                    isActive: false\n                }\n            ],\n            currentPlayerIndex: 0,\n            attackerIndex: 0,\n            defenderIndex: 1,\n            tableCards: [],\n            deck: Array(24).fill({\n                rank: \"?\",\n                suit: \"?\"\n            }),\n            trumpCard: {\n                rank: \"6\",\n                suit: \"♠\"\n            },\n            trumpSuit: \"♠\"\n        };\n        setGameState(mockGameState);\n        setGameLog([\n            \"Новая игра создана\"\n        ]);\n        setSelectedCard(null);\n        setIsGameStarted(false);\n        return null;\n    }, []);\n    // Запуск игры\n    const startGame = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (game) {\n            game.startGame();\n        }\n    }, [\n        game\n    ]);\n    // Ход игрока\n    const makePlayerMove = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((action, cardIndex)=>{\n        if (!game || !gameState) return false;\n        const success = game.makeMove(\"human\", action, cardIndex);\n        if (success) {\n            setSelectedCard(null);\n            // Если игра продолжается и сейчас ход бота, делаем ход бота\n            setTimeout(()=>{\n                const currentState = game.getState();\n                if (currentState.gameStatus === GameStatus.IN_PROGRESS) {\n                    const currentPlayerId = currentState.players[currentState.currentPlayerIndex].id;\n                    if (currentPlayerId !== \"human\") {\n                        // Ход бота\n                        const bot = BotFactory.createBot(BotDifficulty.MEDIUM);\n                        const decision = bot.makeDecision(currentState, currentPlayerId);\n                        game.makeMove(currentPlayerId, decision.action, decision.cardIndex);\n                    }\n                }\n            }, 1000); // Небольшая задержка для реалистичности\n        }\n        return success;\n    }, [\n        game,\n        gameState\n    ]);\n    // Инициализация игры при загрузке компонента\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Временная заглушка для тестирования UI\n        const mockGameState = {\n            gameStatus: GameStatus.NOT_STARTED,\n            players: [\n                {\n                    id: \"human\",\n                    name: \"Вы\",\n                    hand: [\n                        {\n                            rank: \"7\",\n                            suit: \"♠\"\n                        },\n                        {\n                            rank: \"K\",\n                            suit: \"♥\"\n                        },\n                        {\n                            rank: \"9\",\n                            suit: \"♦\"\n                        },\n                        {\n                            rank: \"A\",\n                            suit: \"♣\"\n                        },\n                        {\n                            rank: \"10\",\n                            suit: \"♠\"\n                        },\n                        {\n                            rank: \"J\",\n                            suit: \"♥\"\n                        }\n                    ],\n                    isActive: true\n                },\n                {\n                    id: \"bot\",\n                    name: \"Бот\",\n                    hand: [\n                        {\n                            rank: \"?\",\n                            suit: \"?\"\n                        },\n                        {\n                            rank: \"?\",\n                            suit: \"?\"\n                        },\n                        {\n                            rank: \"?\",\n                            suit: \"?\"\n                        },\n                        {\n                            rank: \"?\",\n                            suit: \"?\"\n                        },\n                        {\n                            rank: \"?\",\n                            suit: \"?\"\n                        },\n                        {\n                            rank: \"?\",\n                            suit: \"?\"\n                        }\n                    ],\n                    isActive: false\n                }\n            ],\n            currentPlayerIndex: 0,\n            attackerIndex: 0,\n            defenderIndex: 1,\n            tableCards: [],\n            deck: Array(24).fill({\n                rank: \"?\",\n                suit: \"?\"\n            }),\n            trumpCard: {\n                rank: \"6\",\n                suit: \"♠\"\n            },\n            trumpSuit: \"♠\"\n        };\n        setGameState(mockGameState);\n        setGameLog([\n            \"Игра готова к запуску\"\n        ]);\n    }, []);\n    // Обработчики действий\n    const handleCardClick = (cardIndex)=>{\n        if (!gameState || gameState.gameStatus !== GameStatus.IN_PROGRESS) return;\n        const currentPlayer = gameState.players[gameState.currentPlayerIndex];\n        if (currentPlayer.id !== \"human\") return;\n        setSelectedCard(cardIndex);\n    };\n    const handleAttack = ()=>{\n        if (selectedCard !== null) {\n            makePlayerMove(PlayerAction.ATTACK, selectedCard);\n        }\n    };\n    const handleDefend = ()=>{\n        if (selectedCard !== null) {\n            makePlayerMove(PlayerAction.DEFEND, selectedCard);\n        }\n    };\n    const handleTake = ()=>{\n        makePlayerMove(PlayerAction.TAKE);\n    };\n    const handlePass = ()=>{\n        makePlayerMove(PlayerAction.PASS);\n    };\n    const handleBackToHome = ()=>{\n        router.push(\"/\");\n    };\n    if (!gameState) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingContainer, {\n            children: \"Загрузка игры...\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n            lineNumber: 218,\n            columnNumber: 12\n        }, undefined);\n    }\n    const humanPlayer = gameState.players.find((p)=>p.id === \"human\");\n    const botPlayer = gameState.players.find((p)=>p.id !== \"human\");\n    const isHumanTurn = ((_gameState_players_gameState_currentPlayerIndex = gameState.players[gameState.currentPlayerIndex]) === null || _gameState_players_gameState_currentPlayerIndex === void 0 ? void 0 : _gameState_players_gameState_currentPlayerIndex.id) === \"human\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Дурак - Козырь Мастер\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Играйте в Дурака против бота\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BackButton, {\n                        onClick: handleBackToHome,\n                        children: \"← Назад\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameTitle, {\n                        children: \"Дурак\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewGameButton, {\n                        onClick: createNewGame,\n                        children: \"Новая игра\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameBoard, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BotArea, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerInfo, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerName, {\n                                        children: botPlayer === null || botPlayer === void 0 ? void 0 : botPlayer.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardCount, {\n                                        children: [\n                                            \"Карт: \",\n                                            botPlayer === null || botPlayer === void 0 ? void 0 : botPlayer.hand.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BotCards, {\n                                children: Array.from({\n                                    length: (botPlayer === null || botPlayer === void 0 ? void 0 : botPlayer.hand.length) || 0\n                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card_AnimatedCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        rank: \"?\",\n                                        suit: \"?\",\n                                        isFlipped: true,\n                                        size: \"small\",\n                                        disabled: true\n                                    }, index, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CenterArea, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameInfo, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrumpInfo, {\n                                        children: [\n                                            \"Козырь: \",\n                                            gameState.trumpCard ? \"\".concat(gameState.trumpCard.rank, \" \").concat(gameState.trumpCard.suit) : \"Неизвестно\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeckInfo, {\n                                        children: [\n                                            \"Колода: \",\n                                            gameState.deck.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusInfo, {\n                                        children: [\n                                            gameState.gameStatus === GameStatus.NOT_STARTED && \"Игра не начата\",\n                                            gameState.gameStatus === GameStatus.IN_PROGRESS && (isHumanTurn ? \"Ваш ход\" : \"Ход бота\"),\n                                            gameState.gameStatus === GameStatus.FINISHED && \"Игра окончена! \".concat(((_gameState_winner = gameState.winner) === null || _gameState_winner === void 0 ? void 0 : _gameState_winner.name) === \"Вы\" ? \"Вы победили!\" : \"Бот победил!\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableArea, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableTitle, {\n                                        children: \"Стол\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCards, {\n                                        children: gameState.tableCards.map((pair, pairIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardPair, {\n                                                children: pair.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCardWrapper, {\n                                                        isDefense: cardIndex === 1,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card_AnimatedCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            rank: card.rank,\n                                                            suit: card.suit,\n                                                            size: \"medium\",\n                                                            animationType: \"slide\",\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, cardIndex, false, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, pairIndex, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerArea, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerInfo, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerName, {\n                                        children: humanPlayer === null || humanPlayer === void 0 ? void 0 : humanPlayer.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardCount, {\n                                        children: [\n                                            \"Карт: \",\n                                            humanPlayer === null || humanPlayer === void 0 ? void 0 : humanPlayer.hand.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayerCards, {\n                                children: humanPlayer === null || humanPlayer === void 0 ? void 0 : humanPlayer.hand.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card_AnimatedCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        rank: card.rank,\n                                        suit: card.suit,\n                                        isSelected: selectedCard === index,\n                                        onClick: ()=>handleCardClick(index),\n                                        animationType: selectedCard === index ? \"glow\" : \"none\",\n                                        size: \"medium\"\n                                    }, index, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ControlPanel, {\n                children: !isGameStarted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StartButton, {\n                    onClick: startGame,\n                    children: \"Начать игру\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButtons, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                            onClick: handleAttack,\n                            disabled: !isHumanTurn || selectedCard === null,\n                            children: \"Атаковать\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                            onClick: handleDefend,\n                            disabled: !isHumanTurn || selectedCard === null,\n                            children: \"Защищаться\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                            onClick: handleTake,\n                            disabled: !isHumanTurn,\n                            children: \"Взять\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                            onClick: handlePass,\n                            disabled: !isHumanTurn,\n                            children: \"Пас\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameLogArea, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogTitle, {\n                        children: \"Лог игры\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogContent, {\n                        children: gameLog.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogMessage, {\n                                children: message\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/games/durak.tsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DurakGamePage, \"mApp90sMEw3JjHVR9FQc1wv79yc=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = DurakGamePage;\n// Стилизованные компоненты\nconst Container = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__Container\",\n    componentId: \"sc-6ea5094b-0\"\n})([\n    \"min-height:100vh;background:linear-gradient(135deg,#1e3c72 0%,#2a5298 100%);color:white;display:flex;flex-direction:column;\"\n]);\n_c1 = Container;\nconst LoadingContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__LoadingContainer\",\n    componentId: \"sc-6ea5094b-1\"\n})([\n    \"min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#1e3c72 0%,#2a5298 100%);color:white;font-size:1.5rem;\"\n]);\n_c2 = LoadingContainer;\nconst GameHeader = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].header.withConfig({\n    displayName: \"durak__GameHeader\",\n    componentId: \"sc-6ea5094b-2\"\n})([\n    \"display:flex;justify-content:space-between;align-items:center;padding:1rem 2rem;background:rgba(0,0,0,0.2);backdrop-filter:blur(10px);\"\n]);\n_c3 = GameHeader;\nconst BackButton = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].button.withConfig({\n    displayName: \"durak__BackButton\",\n    componentId: \"sc-6ea5094b-3\"\n})([\n    \"background:rgba(255,255,255,0.1);border:1px solid rgba(255,255,255,0.2);color:white;padding:0.5rem 1rem;border-radius:8px;cursor:pointer;transition:all 0.3s ease;&:hover{background:rgba(255,255,255,0.2);}\"\n]);\n_c4 = BackButton;\nconst GameTitle = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].h1.withConfig({\n    displayName: \"durak__GameTitle\",\n    componentId: \"sc-6ea5094b-4\"\n})([\n    \"font-size:2rem;margin:0;text-shadow:2px 2px 4px rgba(0,0,0,0.3);\"\n]);\n_c5 = GameTitle;\nconst NewGameButton = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].button.withConfig({\n    displayName: \"durak__NewGameButton\",\n    componentId: \"sc-6ea5094b-5\"\n})([\n    \"background:linear-gradient(135deg,#4CAF50,#45a049);border:none;color:white;padding:0.5rem 1rem;border-radius:8px;cursor:pointer;transition:all 0.3s ease;font-weight:600;&:hover{background:linear-gradient(135deg,#45a049,#4CAF50);transform:translateY(-2px);}\"\n]);\n_c6 = NewGameButton;\nconst GameBoard = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__GameBoard\",\n    componentId: \"sc-6ea5094b-6\"\n})([\n    \"flex:1;display:flex;flex-direction:column;padding:1rem;gap:1rem;\"\n]);\n_c7 = GameBoard;\nconst BotArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__BotArea\",\n    componentId: \"sc-6ea5094b-7\"\n})([\n    \"display:flex;flex-direction:column;align-items:center;gap:1rem;\"\n]);\n_c8 = BotArea;\nconst PlayerArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__PlayerArea\",\n    componentId: \"sc-6ea5094b-8\"\n})([\n    \"display:flex;flex-direction:column;align-items:center;gap:1rem;\"\n]);\n_c9 = PlayerArea;\nconst PlayerInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__PlayerInfo\",\n    componentId: \"sc-6ea5094b-9\"\n})([\n    \"display:flex;gap:2rem;align-items:center;\"\n]);\n_c10 = PlayerInfo;\nconst PlayerName = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].h3.withConfig({\n    displayName: \"durak__PlayerName\",\n    componentId: \"sc-6ea5094b-10\"\n})([\n    \"margin:0;font-size:1.2rem;\"\n]);\n_c11 = PlayerName;\nconst CardCount = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].span.withConfig({\n    displayName: \"durak__CardCount\",\n    componentId: \"sc-6ea5094b-11\"\n})([\n    \"background:rgba(255,255,255,0.1);padding:0.25rem 0.5rem;border-radius:4px;font-size:0.9rem;\"\n]);\n_c12 = CardCount;\nconst BotCards = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__BotCards\",\n    componentId: \"sc-6ea5094b-12\"\n})([\n    \"display:flex;gap:0.5rem;flex-wrap:wrap;justify-content:center;\"\n]);\n_c13 = BotCards;\nconst BotCard = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__BotCard\",\n    componentId: \"sc-6ea5094b-13\"\n})([\n    'width:60px;height:84px;background:linear-gradient(135deg,#8B4513,#A0522D);border:2px solid #654321;border-radius:8px;display:flex;align-items:center;justify-content:center;color:white;font-weight:bold;box-shadow:0 4px 8px rgba(0,0,0,0.3);position:relative;&::after{content:\"\\uD83C\\uDCA0\";font-size:2rem;}'\n]);\nconst PlayerCards = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__PlayerCards\",\n    componentId: \"sc-6ea5094b-14\"\n})([\n    \"display:flex;gap:0.5rem;flex-wrap:wrap;justify-content:center;max-width:100%;\"\n]);\n_c14 = PlayerCards;\nconst PlayerCard = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__PlayerCard\",\n    componentId: \"sc-6ea5094b-15\"\n})([\n    \"width:80px;height:112px;background:\",\n    \";border:3px solid \",\n    \";border-radius:12px;display:flex;flex-direction:column;align-items:center;justify-content:space-between;padding:0.5rem;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 12px rgba(0,0,0,0.2);color:#333;&:hover{transform:translateY(-4px);box-shadow:0 8px 20px rgba(0,0,0,0.3);}\"\n], (props)=>props.isSelected ? \"linear-gradient(135deg, #FFD700, #FFA500)\" : \"linear-gradient(135deg, #FFFFFF, #F0F0F0)\", (props)=>props.isSelected ? \"#FF6B35\" : \"#DDD\");\nconst CardRank = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__CardRank\",\n    componentId: \"sc-6ea5094b-16\"\n})([\n    \"font-size:1.2rem;font-weight:bold;\"\n]);\nconst CardSuit = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__CardSuit\",\n    componentId: \"sc-6ea5094b-17\"\n})([\n    \"font-size:1.5rem;\"\n]);\nconst CenterArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__CenterArea\",\n    componentId: \"sc-6ea5094b-18\"\n})([\n    \"flex:1;display:flex;flex-direction:column;align-items:center;gap:2rem;padding:2rem 0;\"\n]);\n_c15 = CenterArea;\nconst GameInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__GameInfo\",\n    componentId: \"sc-6ea5094b-19\"\n})([\n    \"display:flex;gap:2rem;flex-wrap:wrap;justify-content:center;background:rgba(255,255,255,0.1);padding:1rem;border-radius:12px;backdrop-filter:blur(10px);\"\n]);\n_c16 = GameInfo;\nconst TrumpInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__TrumpInfo\",\n    componentId: \"sc-6ea5094b-20\"\n})([\n    \"background:rgba(255,215,0,0.2);padding:0.5rem 1rem;border-radius:8px;border:1px solid rgba(255,215,0,0.3);\"\n]);\n_c17 = TrumpInfo;\nconst DeckInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__DeckInfo\",\n    componentId: \"sc-6ea5094b-21\"\n})([\n    \"background:rgba(255,255,255,0.1);padding:0.5rem 1rem;border-radius:8px;border:1px solid rgba(255,255,255,0.2);\"\n]);\n_c18 = DeckInfo;\nconst StatusInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__StatusInfo\",\n    componentId: \"sc-6ea5094b-22\"\n})([\n    \"background:rgba(76,175,80,0.2);padding:0.5rem 1rem;border-radius:8px;border:1px solid rgba(76,175,80,0.3);font-weight:600;\"\n]);\n_c19 = StatusInfo;\nconst TableArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__TableArea\",\n    componentId: \"sc-6ea5094b-23\"\n})([\n    \"display:flex;flex-direction:column;align-items:center;gap:1rem;min-height:150px;\"\n]);\n_c20 = TableArea;\nconst TableTitle = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].h3.withConfig({\n    displayName: \"durak__TableTitle\",\n    componentId: \"sc-6ea5094b-24\"\n})([\n    \"margin:0;color:rgba(255,255,255,0.8);\"\n]);\n_c21 = TableTitle;\nconst TableCards = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__TableCards\",\n    componentId: \"sc-6ea5094b-25\"\n})([\n    \"display:flex;gap:1rem;flex-wrap:wrap;justify-content:center;min-height:100px;align-items:center;\"\n]);\n_c22 = TableCards;\nconst CardPair = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__CardPair\",\n    componentId: \"sc-6ea5094b-26\"\n})([\n    \"display:flex;gap:0.25rem;position:relative;\"\n]);\n_c23 = CardPair;\nconst TableCardWrapper = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__TableCardWrapper\",\n    componentId: \"sc-6ea5094b-27\"\n})([\n    \"transform:\",\n    \";z-index:\",\n    \";transition:transform 0.3s ease;\"\n], (props)=>props.isDefense ? \"rotate(15deg) translateX(-20px)\" : \"none\", (props)=>props.isDefense ? 2 : 1);\n_c24 = TableCardWrapper;\nconst TableCard = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__TableCard\",\n    componentId: \"sc-6ea5094b-28\"\n})([\n    \"width:70px;height:98px;background:linear-gradient(135deg,#FFFFFF,#F0F0F0);border:2px solid #DDD;border-radius:10px;display:flex;align-items:center;justify-content:center;color:#333;font-weight:bold;font-size:0.9rem;box-shadow:0 4px 8px rgba(0,0,0,0.2);\"\n]);\nconst ControlPanel = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__ControlPanel\",\n    componentId: \"sc-6ea5094b-29\"\n})([\n    \"padding:1rem 2rem;background:rgba(0,0,0,0.2);backdrop-filter:blur(10px);display:flex;justify-content:center;\"\n]);\n_c25 = ControlPanel;\nconst StartButton = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].button.withConfig({\n    displayName: \"durak__StartButton\",\n    componentId: \"sc-6ea5094b-30\"\n})([\n    \"background:linear-gradient(135deg,#4CAF50,#45a049);border:none;color:white;padding:1rem 2rem;border-radius:12px;font-size:1.2rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(76,175,80,0.3);&:hover{background:linear-gradient(135deg,#45a049,#4CAF50);transform:translateY(-2px);box-shadow:0 6px 20px rgba(76,175,80,0.4);}\"\n]);\n_c26 = StartButton;\nconst ActionButtons = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__ActionButtons\",\n    componentId: \"sc-6ea5094b-31\"\n})([\n    \"display:flex;gap:1rem;flex-wrap:wrap;justify-content:center;\"\n]);\n_c27 = ActionButtons;\nconst ActionButton = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].button.withConfig({\n    displayName: \"durak__ActionButton\",\n    componentId: \"sc-6ea5094b-32\"\n})([\n    \"background:\",\n    \";border:none;color:\",\n    \";padding:0.75rem 1.5rem;border-radius:8px;font-weight:600;cursor:\",\n    \";transition:all 0.3s ease;box-shadow:\",\n    \";&:hover{\",\n    \"}\"\n], (props)=>props.disabled ? \"rgba(255, 255, 255, 0.1)\" : \"linear-gradient(135deg, #2196F3, #1976D2)\", (props)=>props.disabled ? \"rgba(255, 255, 255, 0.5)\" : \"white\", (props)=>props.disabled ? \"not-allowed\" : \"pointer\", (props)=>props.disabled ? \"none\" : \"0 4px 12px rgba(33, 150, 243, 0.3)\", (props)=>!props.disabled && \"\\n      background: linear-gradient(135deg, #1976D2, #2196F3);\\n      transform: translateY(-2px);\\n      box-shadow: 0 6px 16px rgba(33, 150, 243, 0.4);\\n    \");\n_c28 = ActionButton;\nconst GameLogArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__GameLogArea\",\n    componentId: \"sc-6ea5094b-33\"\n})([\n    \"background:rgba(0,0,0,0.3);padding:1rem;max-height:200px;overflow-y:auto;\"\n]);\n_c29 = GameLogArea;\nconst LogTitle = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].h4.withConfig({\n    displayName: \"durak__LogTitle\",\n    componentId: \"sc-6ea5094b-34\"\n})([\n    \"margin:0 0 1rem 0;color:rgba(255,255,255,0.8);\"\n]);\n_c30 = LogTitle;\nconst LogContent = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__LogContent\",\n    componentId: \"sc-6ea5094b-35\"\n})([\n    \"display:flex;flex-direction:column;gap:0.25rem;\"\n]);\n_c31 = LogContent;\nconst LogMessage = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div.withConfig({\n    displayName: \"durak__LogMessage\",\n    componentId: \"sc-6ea5094b-36\"\n})([\n    \"font-size:0.9rem;color:rgba(255,255,255,0.9);padding:0.25rem 0;border-bottom:1px solid rgba(255,255,255,0.1);\"\n]);\n_c32 = LogMessage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DurakGamePage);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32;\n$RefreshReg$(_c, \"DurakGamePage\");\n$RefreshReg$(_c1, \"Container\");\n$RefreshReg$(_c2, \"LoadingContainer\");\n$RefreshReg$(_c3, \"GameHeader\");\n$RefreshReg$(_c4, \"BackButton\");\n$RefreshReg$(_c5, \"GameTitle\");\n$RefreshReg$(_c6, \"NewGameButton\");\n$RefreshReg$(_c7, \"GameBoard\");\n$RefreshReg$(_c8, \"BotArea\");\n$RefreshReg$(_c9, \"PlayerArea\");\n$RefreshReg$(_c10, \"PlayerInfo\");\n$RefreshReg$(_c11, \"PlayerName\");\n$RefreshReg$(_c12, \"CardCount\");\n$RefreshReg$(_c13, \"BotCards\");\n$RefreshReg$(_c14, \"PlayerCards\");\n$RefreshReg$(_c15, \"CenterArea\");\n$RefreshReg$(_c16, \"GameInfo\");\n$RefreshReg$(_c17, \"TrumpInfo\");\n$RefreshReg$(_c18, \"DeckInfo\");\n$RefreshReg$(_c19, \"StatusInfo\");\n$RefreshReg$(_c20, \"TableArea\");\n$RefreshReg$(_c21, \"TableTitle\");\n$RefreshReg$(_c22, \"TableCards\");\n$RefreshReg$(_c23, \"CardPair\");\n$RefreshReg$(_c24, \"TableCardWrapper\");\n$RefreshReg$(_c25, \"ControlPanel\");\n$RefreshReg$(_c26, \"StartButton\");\n$RefreshReg$(_c27, \"ActionButtons\");\n$RefreshReg$(_c28, \"ActionButton\");\n$RefreshReg$(_c29, \"GameLogArea\");\n$RefreshReg$(_c30, \"LogTitle\");\n$RefreshReg$(_c31, \"LogContent\");\n$RefreshReg$(_c32, \"LogMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/games/durak.tsx\n"));

/***/ })

});