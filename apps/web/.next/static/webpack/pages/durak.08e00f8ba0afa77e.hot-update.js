"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/durak",{

/***/ "./src/game/gameLogic.ts":
/*!*******************************!*\
  !*** ./src/game/gameLogic.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPlayerToGame: function() { return /* binding */ addPlayerToGame; },\n/* harmony export */   canAddCard: function() { return /* binding */ canAddCard; },\n/* harmony export */   canDefend: function() { return /* binding */ canDefend; },\n/* harmony export */   checkGameEnd: function() { return /* binding */ checkGameEnd; },\n/* harmony export */   createDeck: function() { return /* binding */ createDeck; },\n/* harmony export */   createGame: function() { return /* binding */ createGame; },\n/* harmony export */   createPlayer: function() { return /* binding */ createPlayer; },\n/* harmony export */   dealCards: function() { return /* binding */ dealCards; },\n/* harmony export */   drawCards: function() { return /* binding */ drawCards; },\n/* harmony export */   executeAction: function() { return /* binding */ executeAction; },\n/* harmony export */   shuffleDeck: function() { return /* binding */ shuffleDeck; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"./src/game/types.ts\");\n\n\n// Создание колоды карт\nfunction createDeck(deckSize = 36) {\n  const suits = ['hearts', 'diamonds', 'clubs', 'spades'];\n  const ranks = deckSize === 36 ? ['6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'] : ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];\n  const deck = [];\n  suits.forEach(suit => {\n    ranks.forEach(rank => {\n      deck.push({\n        suit,\n        rank,\n        id: `${suit}-${rank}`,\n        value: _types__WEBPACK_IMPORTED_MODULE_0__.RANK_VALUES[rank]\n      });\n    });\n  });\n  return shuffleDeck(deck);\n}\n\n// Перемешивание колоды\nfunction shuffleDeck(deck, quantumSeed) {\n  const shuffled = [...deck];\n\n  // Используем квантовое семя если доступно, иначе обычный Math.random\n  const getRandom = () => {\n    if (quantumSeed) {\n      // Простой PRNG на основе квантового семени\n      const seed = (quantumSeed * 9301 + 49297) % 233280;\n      return seed / 233280;\n    }\n    return Math.random();\n  };\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(getRandom() * (i + 1));\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\n  }\n  return shuffled;\n}\n\n// Создание нового игрока\nfunction createPlayer(id, name, isBot = false) {\n  return {\n    id,\n    name,\n    cards: [],\n    isBot,\n    emotionalState: isBot ? {\n      confidence: 0.5,\n      aggression: 0.5,\n      patience: 0.5\n    } : undefined\n  };\n}\n\n// Создание новой игры\nfunction createGame(settings) {\n  const deck = createDeck(settings.deckSize);\n  const trumpCard = deck[deck.length - 1]; // Последняя карта - козырь\n\n  return {\n    id: `game-${Date.now()}`,\n    players: [],\n    currentPlayerIndex: 0,\n    attackingPlayerIndex: 0,\n    defendingPlayerIndex: 1,\n    trumpSuit: trumpCard.suit,\n    trumpCard,\n    deck,\n    table: [],\n    discardPile: [],\n    phase: 'waiting',\n    winner: null,\n    gameSettings: settings\n  };\n}\n\n// Добавление игрока в игру\nfunction addPlayerToGame(gameState, player) {\n  if (gameState.players.length >= gameState.gameSettings.maxPlayers) {\n    throw new Error('Игра уже заполнена');\n  }\n  return {\n    ...gameState,\n    players: [...gameState.players, player]\n  };\n}\n\n// Раздача карт\nfunction dealCards(gameState) {\n  if (gameState.players.length < 2) {\n    throw new Error('Недостаточно игроков для начала игры');\n  }\n  const newGameState = {\n    ...gameState\n  };\n  const cardsPerPlayer = 6;\n  let deckIndex = 0;\n\n  // Раздаем по 6 карт каждому игроку\n  newGameState.players = newGameState.players.map(player => ({\n    ...player,\n    cards: newGameState.deck.slice(deckIndex, deckIndex + cardsPerPlayer)\n  }));\n  deckIndex += newGameState.players.length * cardsPerPlayer;\n\n  // Оставшиеся карты в колоде (кроме козырной)\n  newGameState.deck = newGameState.deck.slice(deckIndex, -1);\n  newGameState.phase = 'attacking';\n  return newGameState;\n}\n\n// Проверка, может ли карта бить другую карту\nfunction canDefend(attackCard, defendCard, trumpSuit) {\n  // Если обе карты одной масти, защищающая должна быть старше\n  if (attackCard.suit === defendCard.suit) {\n    return defendCard.value > attackCard.value;\n  }\n\n  // Если атакующая карта не козырь, а защищающая козырь - можно бить\n  if (attackCard.suit !== trumpSuit && defendCard.suit === trumpSuit) {\n    return true;\n  }\n\n  // В остальных случаях нельзя\n  return false;\n}\n\n// Проверка, можно ли подкинуть карту\nfunction canAddCard(card, tableCards) {\n  if (tableCards.length === 0) return false;\n\n  // Можно подкидывать карты того же ранга, что уже есть на столе\n  const existingRanks = new Set();\n  tableCards.forEach(tableCard => {\n    existingRanks.add(tableCard.attackCard.rank);\n    if (tableCard.defendCard) {\n      existingRanks.add(tableCard.defendCard.rank);\n    }\n  });\n  return existingRanks.has(card.rank);\n}\n\n// Выполнение игрового действия\nfunction executeAction(gameState, action) {\n  const newGameState = {\n    ...gameState\n  };\n  switch (action.type) {\n    case 'attack':\n      return executeAttack(newGameState, action);\n    case 'defend':\n      return executeDefend(newGameState, action);\n    case 'add_card':\n      return executeAddCard(newGameState, action);\n    case 'take_cards':\n      return executeTakeCards(newGameState, action);\n    case 'pass_turn':\n      return executePassTurn(newGameState, action);\n    case 'finish_turn':\n      return executeFinishTurn(newGameState, action);\n    default:\n      throw new Error(`Неизвестное действие: ${action.type}`);\n  }\n}\n\n// Выполнение атаки\nfunction executeAttack(gameState, action) {\n  if (!action.card) throw new Error('Карта не указана');\n  const player = gameState.players[gameState.currentPlayerIndex];\n  const cardIndex = player.cards.findIndex(c => c.id === action.card.id);\n  if (cardIndex === -1) throw new Error('Карта не найдена у игрока');\n\n  // Проверяем, можно ли атаковать этой картой\n  if (gameState.table.length > 0 && !canAddCard(action.card, gameState.table)) {\n    throw new Error('Нельзя атаковать этой картой');\n  }\n\n  // Убираем карту у игрока\n  const newPlayer = {\n    ...player,\n    cards: player.cards.filter((_, index) => index !== cardIndex)\n  };\n\n  // Добавляем карту на стол\n  const newTableCard = {\n    attackCard: action.card,\n    position: gameState.table.length\n  };\n  return {\n    ...gameState,\n    players: gameState.players.map((p, index) => index === gameState.currentPlayerIndex ? newPlayer : p),\n    table: [...gameState.table, newTableCard],\n    phase: 'defending'\n  };\n}\n\n// Выполнение защиты\nfunction executeDefend(gameState, action) {\n  if (!action.card || action.targetPosition === undefined) {\n    throw new Error('Карта или позиция не указаны');\n  }\n  const player = gameState.players[gameState.defendingPlayerIndex];\n  const cardIndex = player.cards.findIndex(c => c.id === action.card.id);\n  if (cardIndex === -1) throw new Error('Карта не найдена у игрока');\n  const tableCard = gameState.table[action.targetPosition];\n  if (!tableCard || tableCard.defendCard) {\n    throw new Error('Неверная позиция для защиты');\n  }\n\n  // Проверяем, можно ли защититься этой картой\n  if (!canDefend(tableCard.attackCard, action.card, gameState.trumpSuit)) {\n    throw new Error('Нельзя защититься этой картой');\n  }\n\n  // Убираем карту у игрока\n  const newPlayer = {\n    ...player,\n    cards: player.cards.filter((_, index) => index !== cardIndex)\n  };\n\n  // Добавляем карту защиты на стол\n  const newTable = gameState.table.map((tc, index) => index === action.targetPosition ? {\n    ...tc,\n    defendCard: action.card\n  } : tc);\n\n  // Проверяем, все ли карты защищены\n  const allDefended = newTable.every(tc => tc.defendCard);\n  return {\n    ...gameState,\n    players: gameState.players.map((p, index) => index === gameState.defendingPlayerIndex ? newPlayer : p),\n    table: newTable,\n    phase: allDefended ? 'adding' : 'defending'\n  };\n}\n\n// Выполнение подкидывания карты\nfunction executeAddCard(gameState, action) {\n  if (!action.card) throw new Error('Карта не указана');\n  const player = gameState.players[gameState.currentPlayerIndex];\n  const cardIndex = player.cards.findIndex(c => c.id === action.card.id);\n  if (cardIndex === -1) throw new Error('Карта не найдена у игрока');\n\n  // Проверяем, можно ли подкинуть эту карту\n  if (!canAddCard(action.card, gameState.table)) {\n    throw new Error('Нельзя подкинуть эту карту');\n  }\n\n  // Проверяем лимит карт на столе\n  if (gameState.table.length >= gameState.gameSettings.maxAttackCards) {\n    throw new Error('Достигнут лимит карт в атаке');\n  }\n\n  // Убираем карту у игрока\n  const newPlayer = {\n    ...player,\n    cards: player.cards.filter((_, index) => index !== cardIndex)\n  };\n\n  // Добавляем карту на стол\n  const newTableCard = {\n    attackCard: action.card,\n    position: gameState.table.length\n  };\n  return {\n    ...gameState,\n    players: gameState.players.map((p, index) => index === gameState.currentPlayerIndex ? newPlayer : p),\n    table: [...gameState.table, newTableCard],\n    phase: 'defending'\n  };\n}\n\n// Выполнение взятия карт\nfunction executeTakeCards(gameState, action) {\n  const defendingPlayer = gameState.players[gameState.defendingPlayerIndex];\n\n  // Собираем все карты со стола\n  const cardsFromTable = [];\n  gameState.table.forEach(tableCard => {\n    cardsFromTable.push(tableCard.attackCard);\n    if (tableCard.defendCard) {\n      cardsFromTable.push(tableCard.defendCard);\n    }\n  });\n\n  // Добавляем карты защищающемуся игроку\n  const newDefendingPlayer = {\n    ...defendingPlayer,\n    cards: [...defendingPlayer.cards, ...cardsFromTable]\n  };\n  return {\n    ...gameState,\n    players: gameState.players.map((p, index) => index === gameState.defendingPlayerIndex ? newDefendingPlayer : p),\n    table: [],\n    phase: 'attacking',\n    currentPlayerIndex: getNextPlayerIndex(gameState, gameState.attackingPlayerIndex),\n    attackingPlayerIndex: getNextPlayerIndex(gameState, gameState.attackingPlayerIndex),\n    defendingPlayerIndex: getNextPlayerIndex(gameState, getNextPlayerIndex(gameState, gameState.attackingPlayerIndex))\n  };\n}\n\n// Выполнение завершения хода\nfunction executeFinishTurn(gameState, action) {\n  // Все карты со стола идут в отбой\n  const cardsFromTable = [];\n  gameState.table.forEach(tableCard => {\n    cardsFromTable.push(tableCard.attackCard);\n    if (tableCard.defendCard) {\n      cardsFromTable.push(tableCard.defendCard);\n    }\n  });\n  return {\n    ...gameState,\n    table: [],\n    discardPile: [...gameState.discardPile, ...cardsFromTable],\n    phase: 'attacking',\n    currentPlayerIndex: gameState.defendingPlayerIndex,\n    attackingPlayerIndex: gameState.defendingPlayerIndex,\n    defendingPlayerIndex: getNextPlayerIndex(gameState, gameState.defendingPlayerIndex)\n  };\n}\n\n// Выполнение пропуска хода\nfunction executePassTurn(gameState, action) {\n  const nextPlayerIndex = getNextPlayerIndex(gameState, gameState.currentPlayerIndex);\n  return {\n    ...gameState,\n    currentPlayerIndex: nextPlayerIndex\n  };\n}\n\n// Получение индекса следующего игрока\nfunction getNextPlayerIndex(gameState, currentIndex) {\n  return (currentIndex + 1) % gameState.players.length;\n}\n\n// Проверка окончания игры\nfunction checkGameEnd(gameState) {\n  // Игра заканчивается, когда у игрока закончились карты\n  const playersWithCards = gameState.players.filter(p => p.cards.length > 0);\n  if (playersWithCards.length <= 1) {\n    const winner = playersWithCards.length === 1 ? playersWithCards[0] : null;\n    return {\n      ...gameState,\n      phase: 'finished',\n      winner: winner?.id || null\n    };\n  }\n  return gameState;\n}\n\n// Добор карт из колоды\nfunction drawCards(gameState) {\n  if (gameState.deck.length === 0) return gameState;\n  const newGameState = {\n    ...gameState\n  };\n  const targetHandSize = 6;\n\n  // Добираем карты всем игрокам до 6 карт (или пока есть карты в колоде)\n  newGameState.players = newGameState.players.map(player => {\n    const cardsNeeded = Math.max(0, targetHandSize - player.cards.length);\n    const cardsToTake = Math.min(cardsNeeded, newGameState.deck.length);\n    if (cardsToTake > 0) {\n      const newCards = newGameState.deck.splice(0, cardsToTake);\n      return {\n        ...player,\n        cards: [...player.cards, ...newCards]\n      };\n    }\n    return player;\n  });\n  return newGameState;\n}\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/game/gameLogic.ts\n"));

/***/ })

});