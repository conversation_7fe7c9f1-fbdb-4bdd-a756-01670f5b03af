"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/durak",{

/***/ "./src/hooks/useGameState.ts":
/*!***********************************!*\
  !*** ./src/hooks/useGameState.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGameState: function() { return /* binding */ useGameState; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _game_gameLogic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../game/gameLogic */ \"./src/game/gameLogic.ts\");\nvar _s = $RefreshSig$();\n\n\nconst useGameState = playerId => {\n  _s();\n  const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameEvents, setGameEvents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [currentPlayerId, setCurrentPlayerId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(playerId || `player-${Date.now()}`);\n\n  // Добавление события в лог\n  const addEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(event => {\n    const newEvent = {\n      ...event,\n      timestamp: Date.now()\n    };\n    setGameEvents(prev => [...prev, newEvent]);\n  }, []);\n\n  // Создание новой игры\n  const createNewGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(settings => {\n    const newGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.createGame)(settings);\n    setGameState(newGame);\n    setGameEvents([]);\n    addEvent({\n      type: 'game_started',\n      message: 'Новая игра создана'\n    });\n  }, [addEvent]);\n\n  // Присоединение к игре\n  const joinGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(playerName => {\n    if (!gameState) {\n      throw new Error('Игра не создана');\n    }\n    const player = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.createPlayer)(currentPlayerId, playerName, false);\n    try {\n      const updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.addPlayerToGame)(gameState, player);\n      setGameState(updatedGame);\n      addEvent({\n        type: 'player_joined',\n        playerId: currentPlayerId,\n        message: `${playerName} присоединился к игре`\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка присоединения'\n      });\n    }\n  }, [gameState, currentPlayerId, addEvent]);\n\n  // Добавление бота\n  const addBot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(botName => {\n    if (!gameState) return;\n    const bot = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.createPlayer)(`bot-${Date.now()}`, botName, true);\n    try {\n      const updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.addPlayerToGame)(gameState, bot);\n      setGameState(updatedGame);\n      addEvent({\n        type: 'player_joined',\n        playerId: bot.id,\n        message: `Бот ${botName} добавлен в игру`\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка добавления бота'\n      });\n    }\n  }, [gameState, addEvent]);\n\n  // Начало игры\n  const startGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!gameState) {\n      throw new Error('Игра не создана');\n    }\n    if (gameState.players.length < 2) {\n      // Добавляем бота если недостаточно игроков\n      addBot('ИИ Противник');\n      return;\n    }\n    try {\n      let updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.dealCards)(gameState);\n      updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.drawCards)(updatedGame);\n      setGameState(updatedGame);\n      addEvent({\n        type: 'cards_dealt',\n        message: 'Карты розданы, игра началась!'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка начала игры'\n      });\n    }\n  }, [gameState, addBot, addEvent]);\n\n  // Выполнение игрового действия\n  const playAction = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(action => {\n    if (!gameState) {\n      throw new Error('Игра не создана');\n    }\n    try {\n      let updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.executeAction)(gameState, action);\n\n      // Добираем карты после хода\n      if (action.type === 'finish_turn' || action.type === 'take_cards') {\n        updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.drawCards)(updatedGame);\n      }\n\n      // Проверяем окончание игры\n      updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.checkGameEnd)(updatedGame);\n      setGameState(updatedGame);\n      addEvent({\n        type: 'card_played',\n        playerId: action.playerId,\n        message: `Игрок выполнил действие: ${action.type}`,\n        data: action\n      });\n      if (updatedGame.phase === 'finished') {\n        addEvent({\n          type: 'game_finished',\n          message: updatedGame.winner ? `Игра окончена! Победитель: ${updatedGame.players.find(p => p.id === updatedGame.winner)?.name}` : 'Игра окончена ничьей'\n        });\n      }\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        playerId: action.playerId,\n        message: error instanceof Error ? error.message : 'Ошибка выполнения действия'\n      });\n    }\n  }, [gameState, addEvent]);\n\n  // Проверка, можно ли сыграть карту\n  const canPlayCard = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(card => {\n    if (!gameState || !currentPlayer) return false;\n\n    // Проверяем, есть ли карта у игрока\n    const hasCard = currentPlayer.cards.some(c => c.id === card.id);\n    if (!hasCard) return false;\n\n    // Проверяем, наш ли ход\n    if (!isMyTurn) return false;\n\n    // Дополнительные проверки в зависимости от фазы игры\n    switch (gameState.phase) {\n      case 'attacking':\n        return gameState.currentPlayerIndex === gameState.players.findIndex(p => p.id === currentPlayerId);\n      case 'defending':\n        return gameState.defendingPlayerIndex === gameState.players.findIndex(p => p.id === currentPlayerId);\n      case 'adding':\n        return gameState.currentPlayerIndex !== gameState.defendingPlayerIndex;\n      default:\n        return false;\n    }\n  }, [gameState, currentPlayerId]);\n\n  // Получение доступных действий\n  const getValidActions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!gameState || !currentPlayer || !isMyTurn) return [];\n    const actions = [];\n    const playerIndex = gameState.players.findIndex(p => p.id === currentPlayerId);\n    switch (gameState.phase) {\n      case 'attacking':\n        // Можем атаковать любой картой (если стол пуст) или подходящей картой\n        currentPlayer.cards.forEach(card => {\n          actions.push({\n            type: 'attack',\n            playerId: currentPlayerId,\n            card\n          });\n        });\n\n        // Можем пропустить ход\n        actions.push({\n          type: 'pass_turn',\n          playerId: currentPlayerId\n        });\n        break;\n      case 'defending':\n        if (playerIndex === gameState.defendingPlayerIndex) {\n          // Можем защищаться подходящими картами\n          gameState.table.forEach((tableCard, position) => {\n            if (!tableCard.defendCard) {\n              currentPlayer.cards.forEach(card => {\n                actions.push({\n                  type: 'defend',\n                  playerId: currentPlayerId,\n                  card,\n                  targetPosition: position\n                });\n              });\n            }\n          });\n\n          // Можем взять карты\n          actions.push({\n            type: 'take_cards',\n            playerId: currentPlayerId\n          });\n        }\n        break;\n      case 'adding':\n        if (playerIndex !== gameState.defendingPlayerIndex) {\n          // Можем подкидывать подходящие карты\n          currentPlayer.cards.forEach(card => {\n            actions.push({\n              type: 'add_card',\n              playerId: currentPlayerId,\n              card\n            });\n          });\n        }\n\n        // Можем завершить ход\n        actions.push({\n          type: 'finish_turn',\n          playerId: currentPlayerId\n        });\n        break;\n    }\n    return actions;\n  }, [gameState, currentPlayer, isMyTurn, currentPlayerId]);\n\n  // Сброс игры\n  const resetGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setGameState(null);\n    setGameEvents([]);\n  }, []);\n\n  // Вычисляемые значения\n  const currentPlayer = gameState?.players.find(p => p.id === currentPlayerId) || null;\n  const isMyTurn = gameState ? gameState.players[gameState.currentPlayerIndex]?.id === currentPlayerId : false;\n\n  // Автоматические действия ботов\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!gameState || gameState.phase === 'finished') return;\n    const currentGamePlayer = gameState.players[gameState.currentPlayerIndex];\n    if (!currentGamePlayer?.isBot) return;\n\n    // Простая логика бота - случайное действие через небольшую задержку\n    const timer = setTimeout(() => {\n      const validActions = getValidActions();\n      if (validActions.length > 0) {\n        const randomAction = validActions[Math.floor(Math.random() * validActions.length)];\n        playAction(randomAction);\n      }\n    }, 1000 + Math.random() * 2000); // 1-3 секунды задержка\n\n    return () => clearTimeout(timer);\n  }, [gameState, getValidActions, playAction]);\n  return {\n    gameState,\n    currentPlayer,\n    isMyTurn,\n    gameEvents,\n    createNewGame,\n    joinGame,\n    startGame,\n    playAction,\n    canPlayCard,\n    getValidActions,\n    resetGame\n  };\n};\n_s(useGameState, \"yvSnHUgRFXEqVBBc0rHwckfVwiY=\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useGameState.ts\n"));

/***/ })

});