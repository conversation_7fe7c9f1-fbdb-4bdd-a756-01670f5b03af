"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\n\nconst HomePage = ()=>{\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleStartPlaying = ()=>{\n        router.push(\"/games/durak\");\n    };\n    const handleMultiplayer = ()=>{\n        router.push(\"/multiplayer\");\n    };\n    const handleViewTournaments = ()=>{\n        router.push(\"/tournaments\");\n    };\n    const handleViewTutorials = ()=>{\n        router.push(\"/tutorials\");\n    };\n    const handleViewProfile = ()=>{\n        router.push(\"/profile\");\n    };\n    const handleViewLeaderboard = ()=>{\n        router.push(\"/leaderboard\");\n    };\n    const handleViewSpectate = ()=>{\n        router.push(\"/spectate\");\n    };\n    const handleViewFriends = ()=>{\n        router.push(\"/friends\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Козырь Мастер - Карточные игры онлайн\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Играйте в популярные карточные игры онлайн с друзьями\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Main, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Hero, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                children: \"Добро пожаловать в Козырь Мастер\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Subtitle, {\n                                children: \"Платформа для игры в популярные карточные игры онлайн\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContainer, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDFAE\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Одиночная игра\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Играйте против бота и оттачивайте свои навыки\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StartButton, {\n                                        onClick: handleStartPlaying,\n                                        children: \"Играть против бота\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDF10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Многопользовательская игра\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Играйте с другими игроками онлайн в реальном времени\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MultiplayerButton, {\n                                        onClick: handleMultiplayer,\n                                        children: \"Играть онлайн\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDFC6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Турниры\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Участвуйте в турнирах и выигрывайте призы\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewTournaments,\n                                        children: \"Смотреть турниры\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83D\\uDCDA\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Обучение\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Изучите правила игр и стратегии\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewTutorials,\n                                        children: \"Изучить правила\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83D\\uDC64\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Профиль\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Просмотрите свою статистику и достижения\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewProfile,\n                                        children: \"Мой профиль\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDFC6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Лидеры\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Таблица лидеров и лучшие игроки\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewLeaderboard,\n                                        children: \"Посмотреть рейтинг\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83D\\uDC41️\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Наблюдение\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Наблюдайте за играми других игроков\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewSpectate,\n                                        children: \"Смотреть игры\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83D\\uDC65\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Друзья\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Управляйте друзьями и приглашениями\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewFriends,\n                                        children: \"Мои друзья\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"\\xa9 2023 Козырь Мастер. Все права защищены.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HomePage, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = HomePage;\n// Стилизованные компоненты\nconst Container = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__Container\",\n    componentId: \"sc-67dfd783-0\"\n})([\n    \"min-height:100vh;display:flex;flex-direction:column;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);\"\n]);\n_c1 = Container;\nconst Main = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].main.withConfig({\n    displayName: \"pages__Main\",\n    componentId: \"sc-67dfd783-1\"\n})([\n    \"flex:1;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:2rem;\"\n]);\n_c2 = Main;\nconst Hero = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__Hero\",\n    componentId: \"sc-67dfd783-2\"\n})([\n    \"text-align:center;margin-bottom:4rem;\"\n]);\n_c3 = Hero;\nconst Title = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].h1.withConfig({\n    displayName: \"pages__Title\",\n    componentId: \"sc-67dfd783-3\"\n})([\n    \"font-size:3.5rem;margin-bottom:1rem;color:white;text-shadow:2px 2px 4px rgba(0,0,0,0.3);font-weight:700;@media (max-width:768px){font-size:2.5rem;}\"\n]);\n_c4 = Title;\nconst Subtitle = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].p.withConfig({\n    displayName: \"pages__Subtitle\",\n    componentId: \"sc-67dfd783-4\"\n})([\n    \"font-size:1.5rem;color:rgba(255,255,255,0.9);text-shadow:1px 1px 2px rgba(0,0,0,0.3);max-width:600px;margin:0 auto;@media (max-width:768px){font-size:1.2rem;}\"\n]);\n_c5 = Subtitle;\nconst CardContainer = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__CardContainer\",\n    componentId: \"sc-67dfd783-5\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:2rem;max-width:1200px;width:100%;\"\n]);\n_c6 = CardContainer;\nconst GameCard = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__GameCard\",\n    componentId: \"sc-67dfd783-6\"\n})([\n    \"background:rgba(255,255,255,0.95);border-radius:16px;box-shadow:0 8px 32px rgba(0,0,0,0.1);padding:2rem;text-align:center;transition:all 0.3s ease;backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,0.2);&:hover{transform:translateY(-8px);box-shadow:0 16px 48px rgba(0,0,0,0.2);}\"\n]);\n_c7 = GameCard;\nconst CardIcon = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div.withConfig({\n    displayName: \"pages__CardIcon\",\n    componentId: \"sc-67dfd783-7\"\n})([\n    \"font-size:3rem;margin-bottom:1rem;\"\n]);\n_c8 = CardIcon;\nconst CardTitle = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].h2.withConfig({\n    displayName: \"pages__CardTitle\",\n    componentId: \"sc-67dfd783-8\"\n})([\n    \"font-size:1.8rem;margin-bottom:1rem;color:#333;font-weight:600;\"\n]);\n_c9 = CardTitle;\nconst CardDescription = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].p.withConfig({\n    displayName: \"pages__CardDescription\",\n    componentId: \"sc-67dfd783-9\"\n})([\n    \"font-size:1rem;color:#666;margin-bottom:2rem;line-height:1.6;\"\n]);\n_c10 = CardDescription;\nconst StartButton = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].button.withConfig({\n    displayName: \"pages__StartButton\",\n    componentId: \"sc-67dfd783-10\"\n})([\n    \"background:linear-gradient(135deg,#4CAF50,#45a049);color:white;border:none;border-radius:8px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(76,175,80,0.3);&:hover{background:linear-gradient(135deg,#45a049,#4CAF50);transform:translateY(-2px);box-shadow:0 6px 20px rgba(76,175,80,0.4);}&:active{transform:translateY(0);}\"\n]);\n_c11 = StartButton;\nconst MultiplayerButton = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].button.withConfig({\n    displayName: \"pages__MultiplayerButton\",\n    componentId: \"sc-67dfd783-11\"\n})([\n    \"background:linear-gradient(135deg,#FF6B35,#F7931E);color:white;border:none;border-radius:8px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(255,107,53,0.3);&:hover{background:linear-gradient(135deg,#F7931E,#FF6B35);transform:translateY(-2px);box-shadow:0 6px 20px rgba(255,107,53,0.4);}&:active{transform:translateY(0);}\"\n]);\n_c12 = MultiplayerButton;\nconst ActionButton = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].button.withConfig({\n    displayName: \"pages__ActionButton\",\n    componentId: \"sc-67dfd783-12\"\n})([\n    \"background:linear-gradient(135deg,#2196F3,#1976D2);color:white;border:none;border-radius:8px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(33,150,243,0.3);&:hover{background:linear-gradient(135deg,#1976D2,#2196F3);transform:translateY(-2px);box-shadow:0 6px 20px rgba(33,150,243,0.4);}&:active{transform:translateY(0);}\"\n]);\n_c13 = ActionButton;\nconst Footer = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].footer.withConfig({\n    displayName: \"pages__Footer\",\n    componentId: \"sc-67dfd783-13\"\n})([\n    \"width:100%;padding:1.5rem;text-align:center;background:rgba(0,0,0,0.1);color:rgba(255,255,255,0.8);backdrop-filter:blur(10px);\"\n]);\n_c14 = Footer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"HomePage\");\n$RefreshReg$(_c1, \"Container\");\n$RefreshReg$(_c2, \"Main\");\n$RefreshReg$(_c3, \"Hero\");\n$RefreshReg$(_c4, \"Title\");\n$RefreshReg$(_c5, \"Subtitle\");\n$RefreshReg$(_c6, \"CardContainer\");\n$RefreshReg$(_c7, \"GameCard\");\n$RefreshReg$(_c8, \"CardIcon\");\n$RefreshReg$(_c9, \"CardTitle\");\n$RefreshReg$(_c10, \"CardDescription\");\n$RefreshReg$(_c11, \"StartButton\");\n$RefreshReg$(_c12, \"MultiplayerButton\");\n$RefreshReg$(_c13, \"ActionButton\");\n$RefreshReg$(_c14, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n"));

/***/ })

});