"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/durak",{

/***/ "./src/components/game/EmotionalDisplay.tsx":
/*!**************************************************!*\
  !*** ./src/components/game/EmotionalDisplay.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmotionalAnalysisPanel: function() { return /* binding */ EmotionalAnalysisPanel; },\n/* harmony export */   EmotionalDisplay: function() { return /* binding */ EmotionalDisplay; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"../../node_modules/react/jsx-runtime.js\");\n\n\n\n\nconst EmotionalContainer = (0,styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1rem;\n  border: 2px solid rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  min-width: 250px;\n`;\n_c = EmotionalContainer;\nconst PlayerTitle = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div`\n  font-weight: bold;\n  color: white;\n  margin-bottom: 0.75rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;\n_c2 = PlayerTitle;\nconst BotIcon = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].span`\n  background: linear-gradient(45deg, #4a90e2, #7b68ee);\n  color: white;\n  padding: 0.2rem 0.5rem;\n  border-radius: 10px;\n  font-size: 0.7rem;\n  font-weight: bold;\n`;\n_c3 = BotIcon;\nconst MetricRow = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.5rem;\n`;\n_c4 = MetricRow;\nconst MetricLabel = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].span`\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n`;\n_c5 = MetricLabel;\nconst MetricBar = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div`\n  width: 100px;\n  height: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n  overflow: hidden;\n`;\n_c6 = MetricBar;\nconst MetricFill = (0,styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div)`\n  height: 100%;\n  background: ${props => props.color};\n  border-radius: 4px;\n`;\n_c7 = MetricFill;\nconst StrategyBadge = (0,styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div)`\n  background: ${props => {\n  switch (props.strategy) {\n    case 'aggressive':\n      return 'linear-gradient(45deg, #dc3545, #c82333)';\n    case 'defensive':\n      return 'linear-gradient(45deg, #28a745, #20c997)';\n    default:\n      return 'linear-gradient(45deg, #ffc107, #fd7e14)';\n  }\n}};\n  color: white;\n  padding: 0.3rem 0.8rem;\n  border-radius: 15px;\n  font-size: 0.8rem;\n  font-weight: bold;\n  text-align: center;\n  margin-top: 0.5rem;\n`;\n_c8 = StrategyBadge;\nconst PhaseIndicator = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div`\n  color: ${props => {\n  switch (props.phase) {\n    case 'early':\n      return '#28a745';\n    case 'mid':\n      return '#ffc107';\n    case 'late':\n      return '#dc3545';\n    default:\n      return '#6c757d';\n  }\n}};\n  font-weight: bold;\n  font-size: 0.8rem;\n`;\n_c9 = PhaseIndicator;\nconst StressIndicator = (0,styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div)`\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  background: ${props => {\n  if (props.level < 0.3) return '#28a745';\n  if (props.level < 0.7) return '#ffc107';\n  return '#dc3545';\n}};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.7rem;\n  color: white;\n  font-weight: bold;\n`;\n_c10 = StressIndicator;\nconst getEmotionEmoji = state => {\n  const {\n    confidence,\n    aggression,\n    patience\n  } = state;\n  if (confidence > 0.8 && aggression > 0.7) return '😤'; // Агрессивный и уверенный\n  if (confidence > 0.8 && patience > 0.7) return '😌'; // Уверенный и терпеливый\n  if (confidence < 0.3 && patience < 0.3) return '😰'; // Неуверенный и нетерпеливый\n  if (aggression > 0.8) return '😠'; // Очень агрессивный\n  if (patience > 0.8) return '🧘'; // Очень терпеливый\n  if (confidence > 0.7) return '😊'; // Уверенный\n  if (confidence < 0.3) return '😟'; // Неуверенный\n  return '😐'; // Нейтральный\n};\n\nconst getStrategyText = strategy => {\n  switch (strategy) {\n    case 'aggressive':\n      return 'Агрессивная';\n    case 'defensive':\n      return 'Защитная';\n    case 'balanced':\n      return 'Сбалансированная';\n    default:\n      return 'Неизвестная';\n  }\n};\nconst getPhaseText = phase => {\n  switch (phase) {\n    case 'early':\n      return 'Начало';\n    case 'mid':\n      return 'Середина';\n    case 'late':\n      return 'Конец';\n    default:\n      return 'Неизвестно';\n  }\n};\nconst EmotionalDisplay = ({\n  playerName,\n  emotionalState,\n  isBot = false,\n  analysis\n}) => {\n  const emotionEmoji = getEmotionEmoji(emotionalState);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(EmotionalContainer, {\n    initial: {\n      opacity: 0,\n      scale: 0.9\n    },\n    animate: {\n      opacity: 1,\n      scale: 1\n    },\n    transition: {\n      duration: 0.3\n    },\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PlayerTitle, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        children: emotionEmoji\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        children: playerName\n      }), isBot && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(BotIcon, {\n        children: \"\\u0418\\u0418\"\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(MetricRow, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricLabel, {\n        children: \"\\u0423\\u0432\\u0435\\u0440\\u0435\\u043D\\u043D\\u043E\\u0441\\u0442\\u044C\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricBar, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricFill, {\n          color: \"linear-gradient(90deg, #4a90e2, #7b68ee)\",\n          initial: {\n            width: 0\n          },\n          animate: {\n            width: `${emotionalState.confidence * 100}%`\n          },\n          transition: {\n            duration: 0.5\n          }\n        })\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(MetricRow, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricLabel, {\n        children: \"\\u0410\\u0433\\u0440\\u0435\\u0441\\u0441\\u0438\\u044F\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricBar, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricFill, {\n          color: \"linear-gradient(90deg, #dc3545, #c82333)\",\n          initial: {\n            width: 0\n          },\n          animate: {\n            width: `${emotionalState.aggression * 100}%`\n          },\n          transition: {\n            duration: 0.5\n          }\n        })\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(MetricRow, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricLabel, {\n        children: \"\\u0422\\u0435\\u0440\\u043F\\u0435\\u043D\\u0438\\u0435\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricBar, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricFill, {\n          color: \"linear-gradient(90deg, #28a745, #20c997)\",\n          initial: {\n            width: 0\n          },\n          animate: {\n            width: `${emotionalState.patience * 100}%`\n          },\n          transition: {\n            duration: 0.5\n          }\n        })\n      })]\n    }), analysis && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(MetricRow, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricLabel, {\n          children: \"\\u0421\\u0442\\u0440\\u0435\\u0441\\u0441\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StressIndicator, {\n          level: analysis.playerStress,\n          animate: {\n            scale: [1, 1.1, 1]\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity\n          },\n          children: Math.round(analysis.playerStress * 100)\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(MetricRow, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricLabel, {\n          children: \"\\u0424\\u0430\\u0437\\u0430 \\u0438\\u0433\\u0440\\u044B\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PhaseIndicator, {\n          phase: analysis.gamePhase,\n          children: getPhaseText(analysis.gamePhase)\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StrategyBadge, {\n        strategy: analysis.recommendedStrategy,\n        initial: {\n          scale: 0\n        },\n        animate: {\n          scale: 1\n        },\n        transition: {\n          delay: 0.3\n        },\n        children: getStrategyText(analysis.recommendedStrategy)\n      })]\n    })]\n  });\n};\n\n// Компонент для отображения эмоционального анализа в реальном времени\n_c11 = EmotionalDisplay;\nconst EmotionalAnalysisPanel = ({\n  botState,\n  playerAnalysis,\n  gamePhase\n}) => {\n  if (!botState && !playerAnalysis) return null;\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n    style: {\n      position: 'fixed',\n      top: '20px',\n      right: '20px',\n      zIndex: 1000,\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '1rem'\n    },\n    initial: {\n      x: 300,\n      opacity: 0\n    },\n    animate: {\n      x: 0,\n      opacity: 1\n    },\n    transition: {\n      duration: 0.5\n    },\n    children: [botState && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(EmotionalDisplay, {\n      playerName: \"\\u0418\\u0418 \\u041F\\u0440\\u043E\\u0442\\u0438\\u0432\\u043D\\u0438\\u043A\",\n      emotionalState: botState,\n      isBot: true\n    }), playerAnalysis && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(EmotionalDisplay, {\n      playerName: \"\\u0412\\u0430\\u0448 \\u0430\\u043D\\u0430\\u043B\\u0438\\u0437\",\n      emotionalState: {\n        confidence: playerAnalysis.playerConfidence,\n        aggression: 1 - playerAnalysis.playerStress,\n        // Инвертируем стресс\n        patience: playerAnalysis.riskLevel < 0.5 ? 0.8 : 0.3\n      },\n      analysis: playerAnalysis\n    })]\n  });\n};\n_c12 = EmotionalAnalysisPanel;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12;\n$RefreshReg$(_c, \"EmotionalContainer\");\n$RefreshReg$(_c2, \"PlayerTitle\");\n$RefreshReg$(_c3, \"BotIcon\");\n$RefreshReg$(_c4, \"MetricRow\");\n$RefreshReg$(_c5, \"MetricLabel\");\n$RefreshReg$(_c6, \"MetricBar\");\n$RefreshReg$(_c7, \"MetricFill\");\n$RefreshReg$(_c8, \"StrategyBadge\");\n$RefreshReg$(_c9, \"PhaseIndicator\");\n$RefreshReg$(_c10, \"StressIndicator\");\n$RefreshReg$(_c11, \"EmotionalDisplay\");\n$RefreshReg$(_c12, \"EmotionalAnalysisPanel\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/game/EmotionalDisplay.tsx\n"));

/***/ }),

/***/ "./src/components/game/GameBoard.tsx":
/*!*******************************************!*\
  !*** ./src/components/game/GameBoard.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GameBoardComponent: function() { return /* binding */ GameBoardComponent; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Card */ \"./src/components/game/Card.tsx\");\n/* harmony import */ var _EmotionalDisplay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EmotionalDisplay */ \"./src/components/game/EmotionalDisplay.tsx\");\n/* harmony import */ var _hooks_useGameState__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useGameState */ \"./src/hooks/useGameState.ts\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"../../node_modules/react/jsx-runtime.js\");\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst GameContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\n  padding: 2rem;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  color: white;\n`;\n_c = GameContainer;\nconst GameHeader = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n  max-width: 1200px;\n  margin-bottom: 2rem;\n`;\n_c2 = GameHeader;\nconst GameTitle = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].h1`\n  font-size: 2rem;\n  font-weight: bold;\n  background: linear-gradient(45deg, #4a90e2, #7b68ee);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n`;\n_c3 = GameTitle;\nconst GameInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  gap: 2rem;\n  align-items: center;\n`;\n_c4 = GameInfo;\nconst InfoItem = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  text-align: center;\n`;\n_c5 = InfoItem;\nconst InfoLabel = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  font-size: 0.8rem;\n  color: rgba(255, 255, 255, 0.6);\n  margin-bottom: 0.25rem;\n`;\n_c6 = InfoLabel;\nconst InfoValue = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #4a90e2;\n`;\n_c7 = InfoValue;\nconst GameBoard = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  width: 100%;\n  max-width: 1200px;\n  height: 600px;\n  position: relative;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 20px;\n  border: 2px solid rgba(255, 255, 255, 0.1);\n  margin-bottom: 2rem;\n`;\n_c8 = GameBoard;\nconst OpponentArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  position: absolute;\n  top: 20px;\n  left: 50%;\n  transform: translateX(-50%);\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n`;\n_c9 = OpponentArea;\nconst PlayerName = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  font-weight: bold;\n  color: white;\n  margin-bottom: 0.5rem;\n`;\n_c10 = PlayerName;\nconst CardCount = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  font-size: 0.8rem;\n  color: rgba(255, 255, 255, 0.6);\n`;\n_c11 = CardCount;\nconst TableArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n  min-height: 120px;\n`;\n_c12 = TableArea;\nconst TableCard = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n  align-items: center;\n`;\n_c13 = TableCard;\nconst DeckArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  align-items: center;\n`;\n_c14 = DeckArea;\nconst TrumpCard = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  transform: rotate(90deg);\n  margin-top: 1rem;\n`;\n_c15 = TrumpCard;\nconst PlayerArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  position: absolute;\n  bottom: 20px;\n  left: 50%;\n  transform: translateX(-50%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n`;\n_c16 = PlayerArea;\nconst PlayerHand = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  gap: 0.5rem;\n  flex-wrap: wrap;\n  justify-content: center;\n`;\n_c17 = PlayerHand;\nconst ActionButtons = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  gap: 1rem;\n  margin-top: 1rem;\n`;\n_c18 = ActionButtons;\nconst ActionButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button)`\n  background: ${props => {\n  switch (props.variant) {\n    case 'primary':\n      return 'linear-gradient(135deg, #4a90e2, #7b68ee)';\n    case 'danger':\n      return 'linear-gradient(135deg, #dc3545, #c82333)';\n    default:\n      return 'rgba(255, 255, 255, 0.1)';\n  }\n}};\n  color: white;\n  border: none;\n  border-radius: 10px;\n  padding: 0.75rem 1.5rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n_c19 = ActionButton;\nconst GameStatus = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  text-align: center;\n  margin-bottom: 2rem;\n`;\n_c20 = GameStatus;\nconst StatusText = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  font-size: 1.2rem;\n  margin-bottom: 0.5rem;\n`;\n_c21 = StatusText;\nconst PhaseText = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.7);\n`;\n_c22 = PhaseText;\nconst SetupArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 2rem;\n  padding: 4rem 2rem;\n`;\n_c23 = SetupArea;\nconst SetupForm = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  align-items: center;\n`;\n_c24 = SetupForm;\nconst Input = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].input`\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid rgba(255, 255, 255, 0.2);\n  border-radius: 10px;\n  padding: 0.75rem 1rem;\n  color: white;\n  font-size: 1rem;\n  text-align: center;\n\n  &::placeholder {\n    color: rgba(255, 255, 255, 0.5);\n  }\n\n  &:focus {\n    outline: none;\n    border-color: #4a90e2;\n  }\n`;\n_c25 = Input;\nconst GameBoardComponent = () => {\n  _s();\n  const [selectedCard, setSelectedCard] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [playerName, setPlayerName] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n  const gameState = (0,_hooks_useGameState__WEBPACK_IMPORTED_MODULE_3__.useGameState)();\n  const defaultSettings = {\n    maxPlayers: 2,\n    deckSize: 36,\n    allowAddCards: true,\n    maxAttackCards: 6\n  };\n  const handleCreateGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    gameState.createNewGame(defaultSettings);\n  }, [gameState]);\n  const handleJoinGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (playerName.trim()) {\n      gameState.joinGame(playerName.trim());\n    }\n  }, [gameState, playerName]);\n  const handleCardClick = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(card => {\n    if (gameState.canPlayCard(card)) {\n      setSelectedCard(card);\n    }\n  }, [gameState]);\n  const handleAttack = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (selectedCard && gameState.gameState) {\n      gameState.playAction({\n        type: 'attack',\n        playerId: gameState.currentPlayer?.id || '',\n        card: selectedCard\n      });\n      setSelectedCard(null);\n    }\n  }, [selectedCard, gameState]);\n  const handleDefend = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(position => {\n    if (selectedCard && gameState.gameState) {\n      gameState.playAction({\n        type: 'defend',\n        playerId: gameState.currentPlayer?.id || '',\n        card: selectedCard,\n        targetPosition: position\n      });\n      setSelectedCard(null);\n    }\n  }, [selectedCard, gameState]);\n  const handleTakeCards = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (gameState.gameState) {\n      gameState.playAction({\n        type: 'take_cards',\n        playerId: gameState.currentPlayer?.id || ''\n      });\n    }\n  }, [gameState]);\n  const handleFinishTurn = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (gameState.gameState) {\n      gameState.playAction({\n        type: 'finish_turn',\n        playerId: gameState.currentPlayer?.id || ''\n      });\n    }\n  }, [gameState]);\n\n  // Если игра не создана\n  if (!gameState.gameState) {\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(GameContainer, {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(SetupArea, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(GameTitle, {\n          children: \"\\uD83C\\uDFAE \\u0414\\u0443\\u0440\\u0430\\u043A - \\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440 4.0\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(SetupForm, {\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ActionButton, {\n            variant: \"primary\",\n            onClick: handleCreateGame,\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: \"\\u0421\\u043E\\u0437\\u0434\\u0430\\u0442\\u044C \\u043D\\u043E\\u0432\\u0443\\u044E \\u0438\\u0433\\u0440\\u0443\"\n          })\n        })]\n      })\n    });\n  }\n\n  // Если игрок не присоединился\n  if (!gameState.currentPlayer) {\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(GameContainer, {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(SetupArea, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(GameTitle, {\n          children: \"\\u041F\\u0440\\u0438\\u0441\\u043E\\u0435\\u0434\\u0438\\u043D\\u0438\\u0442\\u044C\\u0441\\u044F \\u043A \\u0438\\u0433\\u0440\\u0435\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(SetupForm, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Input, {\n            type: \"text\",\n            placeholder: \"\\u0412\\u0432\\u0435\\u0434\\u0438\\u0442\\u0435 \\u0432\\u0430\\u0448\\u0435 \\u0438\\u043C\\u044F\",\n            value: playerName,\n            onChange: e => setPlayerName(e.target.value),\n            onKeyPress: e => e.key === 'Enter' && handleJoinGame()\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ActionButton, {\n            variant: \"primary\",\n            onClick: handleJoinGame,\n            disabled: !playerName.trim(),\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: \"\\u041F\\u0440\\u0438\\u0441\\u043E\\u0435\\u0434\\u0438\\u043D\\u0438\\u0442\\u044C\\u0441\\u044F\"\n          })]\n        })]\n      })\n    });\n  }\n\n  // Если игра не началась\n  if (gameState.gameState.phase === 'waiting') {\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(GameContainer, {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(SetupArea, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(GameTitle, {\n          children: \"\\u041E\\u0436\\u0438\\u0434\\u0430\\u043D\\u0438\\u0435 \\u0438\\u0433\\u0440\\u043E\\u043A\\u043E\\u0432...\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"div\", {\n          children: [\"\\u0418\\u0433\\u0440\\u043E\\u043A\\u043E\\u0432: \", gameState.gameState.players.length, \"/\", gameState.gameState.gameSettings.maxPlayers]\n        }), gameState.gameState.players.length >= 2 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ActionButton, {\n          variant: \"primary\",\n          onClick: gameState.startGame,\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: \"\\u041D\\u0430\\u0447\\u0430\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443\"\n        })]\n      })\n    });\n  }\n  const currentGame = gameState.gameState;\n  const opponent = currentGame.players.find(p => p.id !== gameState.currentPlayer?.id);\n  const isMyTurn = gameState.isMyTurn;\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(GameContainer, {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_EmotionalDisplay__WEBPACK_IMPORTED_MODULE_2__.EmotionalAnalysisPanel, {\n      botState: gameState.botEmotionalState,\n      playerAnalysis: gameState.playerEmotionalAnalysis,\n      gamePhase: currentGame.phase\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(GameHeader, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(GameTitle, {\n        children: \"\\uD83C\\uDFAE \\u0414\\u0443\\u0440\\u0430\\u043A\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(GameInfo, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(InfoItem, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(InfoLabel, {\n            children: \"\\u041A\\u043E\\u0437\\u044B\\u0440\\u044C\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(InfoValue, {\n            children: [currentGame.trumpCard.suit, \" \", currentGame.trumpCard.rank]\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(InfoItem, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(InfoLabel, {\n            children: \"\\u0424\\u0430\\u0437\\u0430\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(InfoValue, {\n            children: currentGame.phase\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(InfoItem, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(InfoLabel, {\n            children: \"\\u0425\\u043E\\u0434\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(InfoValue, {\n            children: isMyTurn ? 'Ваш' : 'Противника'\n          })]\n        })]\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(GameStatus, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(StatusText, {\n        children: currentGame.phase === 'finished' ? `Игра окончена! ${currentGame.winner ? 'Победитель: ' + currentGame.players.find(p => p.id === currentGame.winner)?.name : 'Ничья'}` : isMyTurn ? 'Ваш ход' : `Ход игрока ${currentGame.players[currentGame.currentPlayerIndex]?.name}`\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(PhaseText, {\n        children: [currentGame.phase === 'attacking' && 'Фаза атаки', currentGame.phase === 'defending' && 'Фаза защиты', currentGame.phase === 'adding' && 'Подкидывание карт']\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(GameBoard, {\n      children: [opponent && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(OpponentArea, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"div\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(PlayerName, {\n            children: opponent.name\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(CardCount, {\n            children: [opponent.cards.length, \" \\u043A\\u0430\\u0440\\u0442\"]\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_Card__WEBPACK_IMPORTED_MODULE_1__.CardStack, {\n          count: opponent.cards.length,\n          size: \"small\"\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(TableArea, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n          children: currentGame.table.map((tableCard, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(TableCard, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_Card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n              card: tableCard.attackCard,\n              size: \"medium\"\n            }), tableCard.defendCard && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_Card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n              card: tableCard.defendCard,\n              size: \"medium\"\n            }), !tableCard.defendCard && selectedCard && currentGame.phase === 'defending' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ActionButton, {\n              variant: \"secondary\",\n              onClick: () => handleDefend(index),\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"\\u0417\\u0430\\u0449\\u0438\\u0442\\u0438\\u0442\\u044C\"\n            })]\n          }, index))\n        })\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(DeckArea, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_Card__WEBPACK_IMPORTED_MODULE_1__.CardStack, {\n          count: currentGame.deck.length,\n          size: \"small\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(TrumpCard, {\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_Card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n            card: currentGame.trumpCard,\n            size: \"small\"\n          })\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(PlayerArea, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"div\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(PlayerName, {\n            children: gameState.currentPlayer.name\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(CardCount, {\n            children: [gameState.currentPlayer.cards.length, \" \\u043A\\u0430\\u0440\\u0442\"]\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(PlayerHand, {\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n            children: gameState.currentPlayer.cards.map(card => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_Card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n              card: card,\n              isPlayable: gameState.canPlayCard(card),\n              isSelected: selectedCard?.id === card.id,\n              onClick: () => handleCardClick(card),\n              size: \"medium\"\n            }, card.id))\n          })\n        }), isMyTurn && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(ActionButtons, {\n          children: [currentGame.phase === 'attacking' && selectedCard && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ActionButton, {\n            variant: \"primary\",\n            onClick: handleAttack,\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: \"\\u0410\\u0442\\u0430\\u043A\\u043E\\u0432\\u0430\\u0442\\u044C\"\n          }), currentGame.phase === 'defending' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ActionButton, {\n            variant: \"danger\",\n            onClick: handleTakeCards,\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: \"\\u0412\\u0437\\u044F\\u0442\\u044C \\u043A\\u0430\\u0440\\u0442\\u044B\"\n          }), currentGame.phase === 'adding' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ActionButton, {\n            variant: \"primary\",\n            onClick: handleFinishTurn,\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: \"\\u0417\\u0430\\u0432\\u0435\\u0440\\u0448\\u0438\\u0442\\u044C \\u0445\\u043E\\u0434\"\n          })]\n        })]\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ActionButton, {\n      variant: \"secondary\",\n      onClick: gameState.resetGame,\n      whileHover: {\n        scale: 1.05\n      },\n      whileTap: {\n        scale: 0.95\n      },\n      children: \"\\u041D\\u043E\\u0432\\u0430\\u044F \\u0438\\u0433\\u0440\\u0430\"\n    })]\n  });\n};\n_s(GameBoardComponent, \"hrT6+F3AcE8UtVNzYdcK5GsBilM=\", false, function () {\n  return [_hooks_useGameState__WEBPACK_IMPORTED_MODULE_3__.useGameState];\n});\n_c26 = GameBoardComponent;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26;\n$RefreshReg$(_c, \"GameContainer\");\n$RefreshReg$(_c2, \"GameHeader\");\n$RefreshReg$(_c3, \"GameTitle\");\n$RefreshReg$(_c4, \"GameInfo\");\n$RefreshReg$(_c5, \"InfoItem\");\n$RefreshReg$(_c6, \"InfoLabel\");\n$RefreshReg$(_c7, \"InfoValue\");\n$RefreshReg$(_c8, \"GameBoard\");\n$RefreshReg$(_c9, \"OpponentArea\");\n$RefreshReg$(_c10, \"PlayerName\");\n$RefreshReg$(_c11, \"CardCount\");\n$RefreshReg$(_c12, \"TableArea\");\n$RefreshReg$(_c13, \"TableCard\");\n$RefreshReg$(_c14, \"DeckArea\");\n$RefreshReg$(_c15, \"TrumpCard\");\n$RefreshReg$(_c16, \"PlayerArea\");\n$RefreshReg$(_c17, \"PlayerHand\");\n$RefreshReg$(_c18, \"ActionButtons\");\n$RefreshReg$(_c19, \"ActionButton\");\n$RefreshReg$(_c20, \"GameStatus\");\n$RefreshReg$(_c21, \"StatusText\");\n$RefreshReg$(_c22, \"PhaseText\");\n$RefreshReg$(_c23, \"SetupArea\");\n$RefreshReg$(_c24, \"SetupForm\");\n$RefreshReg$(_c25, \"Input\");\n$RefreshReg$(_c26, \"GameBoardComponent\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/game/GameBoard.tsx\n"));

/***/ })

});