"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/durak",{

/***/ "./src/hooks/useGameState.ts":
/*!***********************************!*\
  !*** ./src/hooks/useGameState.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGameState: function() { return /* binding */ useGameState; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _game_gameLogic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../game/gameLogic */ \"./src/game/gameLogic.ts\");\n/* harmony import */ var _game_aiPlayer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../game/aiPlayer */ \"./src/game/aiPlayer.ts\");\n/* harmony import */ var _useEmotionalAI__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useEmotionalAI */ \"./src/hooks/useEmotionalAI.ts\");\n/* harmony import */ var _useQuantumRandom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useQuantumRandom */ \"./src/hooks/useQuantumRandom.ts\");\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst useGameState = playerId => {\n  _s();\n  const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameEvents, setGameEvents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [currentPlayerId, setCurrentPlayerId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(playerId || `player-${Date.now()}`);\n\n  // Интеграция с эмоциональным ИИ\n  const emotionalAI = (0,_useEmotionalAI__WEBPACK_IMPORTED_MODULE_3__.useEmotionalAI)();\n  const quantumRandom = (0,_useQuantumRandom__WEBPACK_IMPORTED_MODULE_4__.useQuantumRandom)();\n  const aiPlayerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n\n  // Добавление события в лог\n  const addEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(event => {\n    const newEvent = {\n      ...event,\n      timestamp: Date.now()\n    };\n    setGameEvents(prev => [...prev, newEvent]);\n  }, []);\n\n  // Создание новой игры\n  const createNewGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(settings => {\n    const newGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.createGame)(settings);\n    setGameState(newGame);\n    setGameEvents([]);\n    addEvent({\n      type: 'game_started',\n      message: 'Новая игра создана'\n    });\n  }, [addEvent]);\n\n  // Присоединение к игре\n  const joinGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(playerName => {\n    if (!gameState) {\n      throw new Error('Игра не создана');\n    }\n    const player = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.createPlayer)(currentPlayerId, playerName, false);\n    try {\n      const updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.addPlayerToGame)(gameState, player);\n      setGameState(updatedGame);\n      addEvent({\n        type: 'player_joined',\n        playerId: currentPlayerId,\n        message: `${playerName} присоединился к игре`\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка присоединения'\n      });\n    }\n  }, [gameState, currentPlayerId, addEvent]);\n\n  // Добавление бота с эмоциональным ИИ\n  const addBot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(botName => {\n    if (!gameState) return;\n    const bot = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.createPlayer)(`bot-${Date.now()}`, botName, true);\n\n    // Создаем ИИ для бота с уникальной личностью\n    if (!aiPlayerRef.current) {\n      aiPlayerRef.current = new _game_aiPlayer__WEBPACK_IMPORTED_MODULE_2__.EmotionalAI({\n        aggression: 0.4 + Math.random() * 0.4,\n        // 0.4-0.8\n        caution: 0.3 + Math.random() * 0.4,\n        // 0.3-0.7\n        bluffing: Math.random() * 0.6,\n        // 0-0.6\n        adaptability: 0.6 + Math.random() * 0.4 // 0.6-1.0\n      });\n    }\n\n    try {\n      const updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.addPlayerToGame)(gameState, bot);\n      setGameState(updatedGame);\n      addEvent({\n        type: 'player_joined',\n        playerId: bot.id,\n        message: `ИИ ${botName} присоединился с уникальной личностью`\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка добавления бота'\n      });\n    }\n  }, [gameState, addEvent]);\n\n  // Начало игры\n  const startGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!gameState) {\n      throw new Error('Игра не создана');\n    }\n    if (gameState.players.length < 2) {\n      // Добавляем бота если недостаточно игроков\n      addBot('ИИ Противник');\n      return;\n    }\n    try {\n      let updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.dealCards)(gameState);\n      updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.drawCards)(updatedGame);\n      setGameState(updatedGame);\n      addEvent({\n        type: 'cards_dealt',\n        message: 'Карты розданы, игра началась!'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка начала игры'\n      });\n    }\n  }, [gameState, addBot, addEvent]);\n\n  // Выполнение игрового действия\n  const playAction = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(action => {\n    if (!gameState) {\n      throw new Error('Игра не создана');\n    }\n    try {\n      let updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.executeAction)(gameState, action);\n\n      // Добираем карты после хода\n      if (action.type === 'finish_turn' || action.type === 'take_cards') {\n        updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.drawCards)(updatedGame);\n      }\n\n      // Проверяем окончание игры\n      updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.checkGameEnd)(updatedGame);\n      setGameState(updatedGame);\n      addEvent({\n        type: 'card_played',\n        playerId: action.playerId,\n        message: `Игрок выполнил действие: ${action.type}`,\n        data: action\n      });\n      if (updatedGame.phase === 'finished') {\n        addEvent({\n          type: 'game_finished',\n          message: updatedGame.winner ? `Игра окончена! Победитель: ${updatedGame.players.find(p => p.id === updatedGame.winner)?.name}` : 'Игра окончена ничьей'\n        });\n      }\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        playerId: action.playerId,\n        message: error instanceof Error ? error.message : 'Ошибка выполнения действия'\n      });\n    }\n  }, [gameState, addEvent]);\n\n  // Проверка, можно ли сыграть карту\n  const canPlayCard = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(card => {\n    if (!gameState || !currentPlayer) return false;\n\n    // Проверяем, есть ли карта у игрока\n    const hasCard = currentPlayer.cards.some(c => c.id === card.id);\n    if (!hasCard) return false;\n\n    // Проверяем, наш ли ход\n    if (!isMyTurn) return false;\n\n    // Дополнительные проверки в зависимости от фазы игры\n    switch (gameState.phase) {\n      case 'attacking':\n        return gameState.currentPlayerIndex === gameState.players.findIndex(p => p.id === currentPlayerId);\n      case 'defending':\n        return gameState.defendingPlayerIndex === gameState.players.findIndex(p => p.id === currentPlayerId);\n      case 'adding':\n        return gameState.currentPlayerIndex !== gameState.defendingPlayerIndex;\n      default:\n        return false;\n    }\n  }, [gameState, currentPlayerId]);\n\n  // Получение доступных действий\n  const getValidActions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!gameState || !currentPlayer || !isMyTurn) return [];\n    const actions = [];\n    const playerIndex = gameState.players.findIndex(p => p.id === currentPlayerId);\n    switch (gameState.phase) {\n      case 'attacking':\n        // Можем атаковать любой картой (если стол пуст) или подходящей картой\n        currentPlayer.cards.forEach(card => {\n          actions.push({\n            type: 'attack',\n            playerId: currentPlayerId,\n            card\n          });\n        });\n\n        // Можем пропустить ход\n        actions.push({\n          type: 'pass_turn',\n          playerId: currentPlayerId\n        });\n        break;\n      case 'defending':\n        if (playerIndex === gameState.defendingPlayerIndex) {\n          // Можем защищаться подходящими картами\n          gameState.table.forEach((tableCard, position) => {\n            if (!tableCard.defendCard) {\n              currentPlayer.cards.forEach(card => {\n                actions.push({\n                  type: 'defend',\n                  playerId: currentPlayerId,\n                  card,\n                  targetPosition: position\n                });\n              });\n            }\n          });\n\n          // Можем взять карты\n          actions.push({\n            type: 'take_cards',\n            playerId: currentPlayerId\n          });\n        }\n        break;\n      case 'adding':\n        if (playerIndex !== gameState.defendingPlayerIndex) {\n          // Можем подкидывать подходящие карты\n          currentPlayer.cards.forEach(card => {\n            actions.push({\n              type: 'add_card',\n              playerId: currentPlayerId,\n              card\n            });\n          });\n        }\n\n        // Можем завершить ход\n        actions.push({\n          type: 'finish_turn',\n          playerId: currentPlayerId\n        });\n        break;\n    }\n    return actions;\n  }, [gameState, currentPlayer, isMyTurn, currentPlayerId]);\n\n  // Сброс игры\n  const resetGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setGameState(null);\n    setGameEvents([]);\n  }, []);\n\n  // Вычисляемые значения\n  const currentPlayer = gameState?.players.find(p => p.id === currentPlayerId) || null;\n  const isMyTurn = gameState ? gameState.players[gameState.currentPlayerIndex]?.id === currentPlayerId : false;\n\n  // Автоматические действия ботов с эмоциональным ИИ\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!gameState || gameState.phase === 'finished') return;\n    const currentGamePlayer = gameState.players[gameState.currentPlayerIndex];\n    if (!currentGamePlayer?.isBot || !aiPlayerRef.current) return;\n\n    // Анализируем эмоциональное состояние игрока\n    emotionalAI.analyzePlayerBehavior({\n      gamePhase: gameState.phase,\n      cardsInHand: gameState.currentPlayer?.cards.length || 0,\n      recentActions: gameEvents.slice(-5).map(e => e.type),\n      timeSpent: Date.now() // В реальной игре это было бы время принятия решения\n    });\n\n    // ИИ принимает решение\n    const timer = setTimeout(() => {\n      const aiDecision = aiPlayerRef.current?.makeDecision(gameState, currentGamePlayer);\n      if (aiDecision) {\n        // Добавляем эмоциональный контекст к действию\n        addEvent({\n          type: 'card_played',\n          playerId: currentGamePlayer.id,\n          message: `ИИ ${currentGamePlayer.name} анализирует ситуацию и принимает решение...`,\n          data: {\n            action: aiDecision,\n            emotionalState: aiPlayerRef.current?.getEmotionalState(),\n            confidence: emotionalAI.emotionalState.metrics.confidence\n          }\n        });\n        playAction(aiDecision);\n      }\n    }, 800 + Math.random() * 1500); // 0.8-2.3 секунды (более реалистично)\n\n    return () => clearTimeout(timer);\n  }, [gameState, emotionalAI, gameEvents, playAction, addEvent]);\n\n  // Получение эмоционального состояния бота\n  const botEmotionalState = aiPlayerRef.current?.getEmotionalState() || null;\n\n  // Анализ эмоционального состояния игрока\n  const playerEmotionalAnalysis = gameState && currentPlayer ? aiPlayerRef.current?.analyzeGameState(gameState, currentPlayer.id) : null;\n  return {\n    gameState,\n    currentPlayer,\n    isMyTurn,\n    gameEvents,\n    createNewGame,\n    joinGame,\n    startGame,\n    playAction,\n    canPlayCard,\n    getValidActions,\n    resetGame,\n    // ИИ и эмоциональный анализ\n    botEmotionalState,\n    playerEmotionalAnalysis\n  };\n};\n_s(useGameState, \"VkVvJHuj+Pfb5lPvzO409jy1TvI=\", false, function () {\n  return [_useEmotionalAI__WEBPACK_IMPORTED_MODULE_3__.useEmotionalAI, _useQuantumRandom__WEBPACK_IMPORTED_MODULE_4__.useQuantumRandom];\n});\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useGameState.ts\n"));

/***/ })

});