{"c": ["webpack"], "r": ["pages/multiplayer"], "m": ["../../node_modules/@socket.io/component-emitter/lib/esm/index.js", "../../node_modules/engine.io-client/build/esm/contrib/has-cors.js", "../../node_modules/engine.io-client/build/esm/contrib/parseqs.js", "../../node_modules/engine.io-client/build/esm/contrib/parseuri.js", "../../node_modules/engine.io-client/build/esm/globals.js", "../../node_modules/engine.io-client/build/esm/index.js", "../../node_modules/engine.io-client/build/esm/socket.js", "../../node_modules/engine.io-client/build/esm/transport.js", "../../node_modules/engine.io-client/build/esm/transports/index.js", "../../node_modules/engine.io-client/build/esm/transports/polling-fetch.js", "../../node_modules/engine.io-client/build/esm/transports/polling-xhr.js", "../../node_modules/engine.io-client/build/esm/transports/polling.js", "../../node_modules/engine.io-client/build/esm/transports/websocket.js", "../../node_modules/engine.io-client/build/esm/transports/webtransport.js", "../../node_modules/engine.io-client/build/esm/util.js", "../../node_modules/engine.io-parser/build/esm/commons.js", "../../node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../../node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../../node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../../node_modules/engine.io-parser/build/esm/index.js", "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fdemon%2FS%2Fa%2FA1-K%2Fapps%2Fweb%2Fpages%2Fmultiplayer.tsx&page=%2Fmultiplayer!", "../../node_modules/socket.io-client/build/esm/contrib/backo2.js", "../../node_modules/socket.io-client/build/esm/index.js", "../../node_modules/socket.io-client/build/esm/manager.js", "../../node_modules/socket.io-client/build/esm/on.js", "../../node_modules/socket.io-client/build/esm/socket.js", "../../node_modules/socket.io-client/build/esm/url.js", "../../node_modules/socket.io-parser/build/esm/binary.js", "../../node_modules/socket.io-parser/build/esm/index.js", "../../node_modules/socket.io-parser/build/esm/is-binary.js", "./src/hooks/useSocket.ts", "./src/pages/multiplayer.tsx"]}