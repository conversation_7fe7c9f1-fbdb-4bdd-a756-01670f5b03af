/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/durak";
exports.ids = ["pages/durak"];
exports.modules = {

/***/ "./src/app/store/index.ts":
/*!********************************!*\
  !*** ./src/app/store/index.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   store: () => (/* binding */ store)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"@reduxjs/toolkit\");\n/* harmony import */ var _entities_game_model_durakSlice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @entities/game/model/durakSlice */ \"./src/entities/game/model/durakSlice.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__, _entities_game_model_durakSlice__WEBPACK_IMPORTED_MODULE_1__]);\n([_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__, _entities_game_model_durakSlice__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n// Импорт редьюсеров\n// import { authReducer } from '@features/auth/model/slice';\n// import { gameReducer } from \"@entities/game/model/slice\"; // Removed old game slice\n // Import the new Durak slice\n\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.configureStore)({\n  reducer: {\n    // auth: authReducer,\n    // game: gameReducer, // Removed old game slice\n    durak: _entities_game_model_durakSlice__WEBPACK_IMPORTED_MODULE_1__[\"default\"] // Add the Durak game slice\n    // Здесь будут добавлены другие редьюсеры\n  },\n\n  middleware: getDefaultMiddleware => getDefaultMiddleware({\n    serializableCheck: false\n  })\n});\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvYXBwL3N0b3JlL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFrRDs7QUFFbEQ7QUFDQTtBQUNBO0FBQzJELENBQUM7O0FBRXJELE1BQU1FLEtBQUssR0FBR0YsZ0VBQWMsQ0FBQztFQUNsQ0csT0FBTyxFQUFFO0lBQ1A7SUFDQTtJQUNBQyxLQUFLLEVBQUVILHVFQUFZLENBQUU7SUFDckI7RUFDRixDQUFDOztFQUNESSxVQUFVLEVBQUdDLG9CQUFvQixJQUMvQkEsb0JBQW9CLENBQUM7SUFDbkJDLGlCQUFpQixFQUFFO0VBQ3JCLENBQUM7QUFDTCxDQUFDLENBQUMsQyIsInNvdXJjZXMiOlsid2VicGFjazovL2tvenlyLW1hc3Rlci13ZWIvLi9zcmMvYXBwL3N0b3JlL2luZGV4LnRzPzljZTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29uZmlndXJlU3RvcmUgfSBmcm9tIFwiQHJlZHV4anMvdG9vbGtpdFwiO1xuXG4vLyDQmNC80L/QvtGA0YIg0YDQtdC00YzRjtGB0LXRgNC+0LJcbi8vIGltcG9ydCB7IGF1dGhSZWR1Y2VyIH0gZnJvbSAnQGZlYXR1cmVzL2F1dGgvbW9kZWwvc2xpY2UnO1xuLy8gaW1wb3J0IHsgZ2FtZVJlZHVjZXIgfSBmcm9tIFwiQGVudGl0aWVzL2dhbWUvbW9kZWwvc2xpY2VcIjsgLy8gUmVtb3ZlZCBvbGQgZ2FtZSBzbGljZVxuaW1wb3J0IGR1cmFrUmVkdWNlciBmcm9tICdAZW50aXRpZXMvZ2FtZS9tb2RlbC9kdXJha1NsaWNlJzsgLy8gSW1wb3J0IHRoZSBuZXcgRHVyYWsgc2xpY2VcblxuZXhwb3J0IGNvbnN0IHN0b3JlID0gY29uZmlndXJlU3RvcmUoe1xuICByZWR1Y2VyOiB7XG4gICAgLy8gYXV0aDogYXV0aFJlZHVjZXIsXG4gICAgLy8gZ2FtZTogZ2FtZVJlZHVjZXIsIC8vIFJlbW92ZWQgb2xkIGdhbWUgc2xpY2VcbiAgICBkdXJhazogZHVyYWtSZWR1Y2VyLCAvLyBBZGQgdGhlIER1cmFrIGdhbWUgc2xpY2VcbiAgICAvLyDQl9C00LXRgdGMINCx0YPQtNGD0YIg0LTQvtCx0LDQstC70LXQvdGLINC00YDRg9Cz0LjQtSDRgNC10LTRjNGO0YHQtdGA0YtcbiAgfSxcbiAgbWlkZGxld2FyZTogKGdldERlZmF1bHRNaWRkbGV3YXJlKSA9PlxuICAgIGdldERlZmF1bHRNaWRkbGV3YXJlKHtcbiAgICAgIHNlcmlhbGl6YWJsZUNoZWNrOiBmYWxzZSxcbiAgICB9KSxcbn0pO1xuXG5leHBvcnQgdHlwZSBSb290U3RhdGUgPSBSZXR1cm5UeXBlPHR5cGVvZiBzdG9yZS5nZXRTdGF0ZT47XG5leHBvcnQgdHlwZSBBcHBEaXNwYXRjaCA9IHR5cGVvZiBzdG9yZS5kaXNwYXRjaDtcbiJdLCJuYW1lcyI6WyJjb25maWd1cmVTdG9yZSIsImR1cmFrUmVkdWNlciIsInN0b3JlIiwicmVkdWNlciIsImR1cmFrIiwibWlkZGxld2FyZSIsImdldERlZmF1bHRNaWRkbGV3YXJlIiwic2VyaWFsaXphYmxlQ2hlY2siXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/app/store/index.ts\n");

/***/ }),

/***/ "./src/components/game/Card.tsx":
/*!**************************************!*\
  !*** ./src/components/game/Card.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardStack: () => (/* binding */ CardStack)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _game_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../game/types */ \"./src/game/types.ts\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nconst CardContainer = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div)`\n  width: ${props => {\n  switch (props.size) {\n    case 'small':\n      return '60px';\n    case 'large':\n      return '120px';\n    default:\n      return '80px';\n  }\n}};\n  height: ${props => {\n  switch (props.size) {\n    case 'small':\n      return '84px';\n    case 'large':\n      return '168px';\n    default:\n      return '112px';\n  }\n}};\n  \n  background: ${props => props.isHidden ? 'linear-gradient(135deg, #1a1a2e, #16213e)' : 'linear-gradient(135deg, #ffffff, #f8f9fa)'};\n  \n  border: 2px solid ${props => {\n  if (props.isSelected) return '#4a90e2';\n  if (props.isPlayable) return '#28a745';\n  return props.isHidden ? '#444' : '#ddd';\n}};\n  \n  border-radius: 8px;\n  cursor: ${props => props.isPlayable ? 'pointer' : 'default'};\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  padding: 4px;\n  \n  box-shadow: ${props => {\n  if (props.isSelected) return '0 0 20px rgba(74, 144, 226, 0.5)';\n  if (props.isPlayable) return '0 0 15px rgba(40, 167, 69, 0.3)';\n  return '0 2px 8px rgba(0, 0, 0, 0.1)';\n}};\n  \n  color: ${props => props.isHidden ? '#fff' : props.color === 'red' ? '#dc3545' : '#212529'};\n  \n  transition: all 0.2s ease;\n  \n  &:hover {\n    transform: ${props => props.isPlayable ? 'translateY(-5px) scale(1.05)' : 'none'};\n    box-shadow: ${props => props.isPlayable ? '0 8px 25px rgba(0, 0, 0, 0.2)' : '0 2px 8px rgba(0, 0, 0, 0.1)'};\n  }\n`;\nconst CardRank = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: ${props => {\n  switch (props.size) {\n    case 'small':\n      return '12px';\n    case 'large':\n      return '20px';\n    default:\n      return '16px';\n  }\n}};\n  font-weight: bold;\n  line-height: 1;\n`;\nconst CardSuit = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: ${props => {\n  switch (props.size) {\n    case 'small':\n      return '16px';\n    case 'large':\n      return '32px';\n    default:\n      return '24px';\n  }\n}};\n  line-height: 1;\n`;\nconst CardCenter = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  font-size: ${props => {\n  switch (props.size) {\n    case 'small':\n      return '20px';\n    case 'large':\n      return '40px';\n    default:\n      return '30px';\n  }\n}};\n  opacity: 0.3;\n`;\nconst CardBack = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  font-size: ${props => {\n  switch (props.size) {\n    case 'small':\n      return '24px';\n    case 'large':\n      return '48px';\n    default:\n      return '36px';\n  }\n}};\n  color: #4a90e2;\n`;\nconst PlayableIndicator = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div)`\n  position: absolute;\n  top: -5px;\n  right: -5px;\n  width: 20px;\n  height: 20px;\n  background: #28a745;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 12px;\n  font-weight: bold;\n`;\nconst SelectedIndicator = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div)`\n  position: absolute;\n  top: -5px;\n  left: -5px;\n  width: 20px;\n  height: 20px;\n  background: #4a90e2;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 12px;\n  font-weight: bold;\n`;\nconst Card = ({\n  card,\n  isPlayable = false,\n  isSelected = false,\n  isHidden = false,\n  size = 'medium',\n  onClick,\n  onHover,\n  style\n}) => {\n  const suitColor = _game_types__WEBPACK_IMPORTED_MODULE_3__.SUIT_COLORS[card.suit];\n  const suitSymbol = _game_types__WEBPACK_IMPORTED_MODULE_3__.SUIT_SYMBOLS[card.suit];\n  const handleClick = () => {\n    if (isPlayable && onClick) {\n      onClick();\n    }\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(CardContainer, {\n    isPlayable: isPlayable,\n    isSelected: isSelected,\n    isHidden: isHidden,\n    size: size,\n    color: suitColor,\n    onClick: handleClick,\n    onMouseEnter: onHover,\n    style: style,\n    whileHover: isPlayable ? {\n      scale: 1.05,\n      y: -5\n    } : {},\n    whileTap: isPlayable ? {\n      scale: 0.95\n    } : {},\n    initial: {\n      opacity: 0,\n      scale: 0.8\n    },\n    animate: {\n      opacity: 1,\n      scale: 1\n    },\n    exit: {\n      opacity: 0,\n      scale: 0.8\n    },\n    transition: {\n      duration: 0.2\n    },\n    children: [isHidden ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(CardBack, {\n      size: size,\n      children: \"\\uD83C\\uDCA0\"\n    }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"div\", {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(CardRank, {\n          size: size,\n          children: card.rank\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(CardSuit, {\n          size: size,\n          children: suitSymbol\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(CardCenter, {\n        size: size,\n        children: suitSymbol\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"div\", {\n        style: {\n          transform: 'rotate(180deg)',\n          alignSelf: 'flex-end'\n        },\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(CardRank, {\n          size: size,\n          children: card.rank\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(CardSuit, {\n          size: size,\n          children: suitSymbol\n        })]\n      })]\n    }), isPlayable && !isSelected && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(PlayableIndicator, {\n      initial: {\n        scale: 0\n      },\n      animate: {\n        scale: 1\n      },\n      transition: {\n        delay: 0.1\n      },\n      children: \"\\u2713\"\n    }), isSelected && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(SelectedIndicator, {\n      initial: {\n        scale: 0\n      },\n      animate: {\n        scale: 1\n      },\n      transition: {\n        delay: 0.1\n      },\n      children: \"\\u2605\"\n    })]\n  });\n};\n\n// Компонент для отображения стопки карт\nconst CardStack = ({\n  count,\n  size = 'medium',\n  topCard\n}) => {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"div\", {\n    style: {\n      position: 'relative'\n    },\n    children: [Array.from({\n      length: Math.min(count, 3)\n    }).map((_, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Card, {\n      card: topCard || {\n        suit: 'spades',\n        rank: 'A',\n        id: 'stack',\n        value: 14\n      },\n      isHidden: !topCard,\n      size: size,\n      style: {\n        position: index > 0 ? 'absolute' : 'relative',\n        top: index * -2,\n        left: index * -2,\n        zIndex: 3 - index\n      }\n    }, index)), count > 3 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", {\n      style: {\n        position: 'absolute',\n        bottom: -10,\n        right: -10,\n        background: '#4a90e2',\n        color: 'white',\n        borderRadius: '50%',\n        width: 24,\n        height: 24,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        fontSize: '12px',\n        fontWeight: 'bold'\n      },\n      children: count\n    })]\n  });\n};\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/game/Card.tsx\n");

/***/ }),

/***/ "./src/components/game/GameBoard.tsx":
/*!*******************************************!*\
  !*** ./src/components/game/GameBoard.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GameBoardComponent: () => (/* binding */ GameBoardComponent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Card */ \"./src/components/game/Card.tsx\");\n/* harmony import */ var _hooks_useSimpleGame__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/useSimpleGame */ \"./src/hooks/useSimpleGame.ts\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__, _Card__WEBPACK_IMPORTED_MODULE_3__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_1__, _Card__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst GameContainer = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\n  padding: 2rem;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  color: white;\n`;\nconst GameHeader = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n  max-width: 1200px;\n  margin-bottom: 2rem;\n`;\nconst GameTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h1)`\n  font-size: 2rem;\n  font-weight: bold;\n  background: linear-gradient(45deg, #4a90e2, #7b68ee);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n`;\nconst GameInfo = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  gap: 2rem;\n  align-items: center;\n`;\nconst InfoItem = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  text-align: center;\n`;\nconst InfoLabel = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 0.8rem;\n  color: rgba(255, 255, 255, 0.6);\n  margin-bottom: 0.25rem;\n`;\nconst InfoValue = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #4a90e2;\n`;\nconst GameBoard = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  width: 100%;\n  max-width: 1200px;\n  height: 600px;\n  position: relative;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 20px;\n  border: 2px solid rgba(255, 255, 255, 0.1);\n  margin-bottom: 2rem;\n`;\nconst OpponentArea = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: absolute;\n  top: 20px;\n  left: 50%;\n  transform: translateX(-50%);\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n`;\nconst PlayerName = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-weight: bold;\n  color: white;\n  margin-bottom: 0.5rem;\n`;\nconst CardCount = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 0.8rem;\n  color: rgba(255, 255, 255, 0.6);\n`;\nconst TableArea = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n  min-height: 120px;\n`;\nconst TableCard = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n  align-items: center;\n`;\nconst DeckArea = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  align-items: center;\n`;\nconst TrumpCard = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  transform: rotate(90deg);\n  margin-top: 1rem;\n`;\nconst PlayerArea = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: absolute;\n  bottom: 20px;\n  left: 50%;\n  transform: translateX(-50%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n`;\nconst PlayerHand = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  gap: 0.5rem;\n  flex-wrap: wrap;\n  justify-content: center;\n`;\nconst ActionButtons = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  gap: 1rem;\n  margin-top: 1rem;\n`;\nconst ActionButton = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button)`\n  background: ${props => {\n  switch (props.variant) {\n    case 'primary':\n      return 'linear-gradient(135deg, #4a90e2, #7b68ee)';\n    case 'danger':\n      return 'linear-gradient(135deg, #dc3545, #c82333)';\n    default:\n      return 'rgba(255, 255, 255, 0.1)';\n  }\n}};\n  color: white;\n  border: none;\n  border-radius: 10px;\n  padding: 0.75rem 1.5rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\nconst GameStatus = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  text-align: center;\n  margin-bottom: 2rem;\n`;\nconst StatusText = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 1.2rem;\n  margin-bottom: 0.5rem;\n`;\nconst PhaseText = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.7);\n`;\nconst SetupArea = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 2rem;\n  padding: 4rem 2rem;\n`;\nconst SetupForm = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  align-items: center;\n`;\nconst Input = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().input)`\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid rgba(255, 255, 255, 0.2);\n  border-radius: 10px;\n  padding: 0.75rem 1rem;\n  color: white;\n  font-size: 1rem;\n  text-align: center;\n\n  &::placeholder {\n    color: rgba(255, 255, 255, 0.5);\n  }\n\n  &:focus {\n    outline: none;\n    border-color: #4a90e2;\n  }\n`;\nconst GameBoardComponent = () => {\n  const [selectedCard, setSelectedCard] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [playerName, setPlayerName] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n  const gameState = (0,_hooks_useSimpleGame__WEBPACK_IMPORTED_MODULE_4__.useSimpleGame)();\n  const defaultSettings = {\n    maxPlayers: 2,\n    deckSize: 36,\n    allowAddCards: true,\n    maxAttackCards: 6\n  };\n  const handleCreateGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    gameState.createNewGame(defaultSettings);\n  }, [gameState]);\n  const handleJoinGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (playerName.trim()) {\n      gameState.joinGame(playerName.trim());\n    }\n  }, [gameState, playerName]);\n  const handleCardClick = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(card => {\n    if (gameState.canPlayCard(card)) {\n      setSelectedCard(card);\n    }\n  }, [gameState]);\n  const handleAttack = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (selectedCard && gameState.gameState) {\n      gameState.playAction({\n        type: 'attack',\n        playerId: gameState.currentPlayer?.id || '',\n        card: selectedCard\n      });\n      setSelectedCard(null);\n    }\n  }, [selectedCard, gameState]);\n  const handleDefend = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(position => {\n    if (selectedCard && gameState.gameState) {\n      gameState.playAction({\n        type: 'defend',\n        playerId: gameState.currentPlayer?.id || '',\n        card: selectedCard,\n        targetPosition: position\n      });\n      setSelectedCard(null);\n    }\n  }, [selectedCard, gameState]);\n  const handleTakeCards = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (gameState.gameState) {\n      gameState.playAction({\n        type: 'take_cards',\n        playerId: gameState.currentPlayer?.id || ''\n      });\n    }\n  }, [gameState]);\n  const handleFinishTurn = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (gameState.gameState) {\n      gameState.playAction({\n        type: 'finish_turn',\n        playerId: gameState.currentPlayer?.id || ''\n      });\n    }\n  }, [gameState]);\n\n  // Если игра не создана\n  if (!gameState.gameState) {\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(GameContainer, {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(SetupArea, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(GameTitle, {\n          children: \"\\uD83C\\uDFAE \\u0414\\u0443\\u0440\\u0430\\u043A - \\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440 4.0\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(SetupForm, {\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ActionButton, {\n            variant: \"primary\",\n            onClick: handleCreateGame,\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: \"\\u0421\\u043E\\u0437\\u0434\\u0430\\u0442\\u044C \\u043D\\u043E\\u0432\\u0443\\u044E \\u0438\\u0433\\u0440\\u0443\"\n          })\n        })]\n      })\n    });\n  }\n\n  // Если игрок не присоединился\n  if (!gameState.currentPlayer) {\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(GameContainer, {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(SetupArea, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(GameTitle, {\n          children: \"\\u041F\\u0440\\u0438\\u0441\\u043E\\u0435\\u0434\\u0438\\u043D\\u0438\\u0442\\u044C\\u0441\\u044F \\u043A \\u0438\\u0433\\u0440\\u0435\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(SetupForm, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(Input, {\n            type: \"text\",\n            placeholder: \"\\u0412\\u0432\\u0435\\u0434\\u0438\\u0442\\u0435 \\u0432\\u0430\\u0448\\u0435 \\u0438\\u043C\\u044F\",\n            value: playerName,\n            onChange: e => setPlayerName(e.target.value),\n            onKeyPress: e => e.key === 'Enter' && handleJoinGame()\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ActionButton, {\n            variant: \"primary\",\n            onClick: handleJoinGame,\n            disabled: !playerName.trim(),\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: \"\\u041F\\u0440\\u0438\\u0441\\u043E\\u0435\\u0434\\u0438\\u043D\\u0438\\u0442\\u044C\\u0441\\u044F\"\n          })]\n        })]\n      })\n    });\n  }\n\n  // Если игра не началась\n  if (gameState.gameState.phase === 'waiting') {\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(GameContainer, {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(SetupArea, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(GameTitle, {\n          children: \"\\u041E\\u0436\\u0438\\u0434\\u0430\\u043D\\u0438\\u0435 \\u0438\\u0433\\u0440\\u043E\\u043A\\u043E\\u0432...\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(\"div\", {\n          children: [\"\\u0418\\u0433\\u0440\\u043E\\u043A\\u043E\\u0432: \", gameState.gameState.players.length, \"/\", gameState.gameState.gameSettings.maxPlayers]\n        }), gameState.gameState.players.length >= 2 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ActionButton, {\n          variant: \"primary\",\n          onClick: gameState.startGame,\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: \"\\u041D\\u0430\\u0447\\u0430\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443\"\n        })]\n      })\n    });\n  }\n  const currentGame = gameState.gameState;\n  const opponent = currentGame.players.find(p => p.id !== gameState.currentPlayer?.id);\n  const isMyTurn = gameState.isMyTurn;\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(GameContainer, {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(GameHeader, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(GameTitle, {\n        children: \"\\uD83C\\uDFAE \\u0414\\u0443\\u0440\\u0430\\u043A\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(GameInfo, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(InfoItem, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(InfoLabel, {\n            children: \"\\u041A\\u043E\\u0437\\u044B\\u0440\\u044C\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(InfoValue, {\n            children: [currentGame.trumpCard.suit, \" \", currentGame.trumpCard.rank]\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(InfoItem, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(InfoLabel, {\n            children: \"\\u0424\\u0430\\u0437\\u0430\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(InfoValue, {\n            children: currentGame.phase\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(InfoItem, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(InfoLabel, {\n            children: \"\\u0425\\u043E\\u0434\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(InfoValue, {\n            children: isMyTurn ? 'Ваш' : 'Противника'\n          })]\n        })]\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(GameStatus, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatusText, {\n        children: currentGame.phase === 'finished' ? `Игра окончена! ${currentGame.winner ? 'Победитель: ' + currentGame.players.find(p => p.id === currentGame.winner)?.name : 'Ничья'}` : isMyTurn ? 'Ваш ход' : `Ход игрока ${currentGame.players[currentGame.currentPlayerIndex]?.name}`\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(PhaseText, {\n        children: [currentGame.phase === 'attacking' && 'Фаза атаки', currentGame.phase === 'defending' && 'Фаза защиты', currentGame.phase === 'adding' && 'Подкидывание карт']\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(GameBoard, {\n      children: [opponent && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(OpponentArea, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(\"div\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(PlayerName, {\n            children: opponent.name\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(CardCount, {\n            children: [opponent.cards.length, \" \\u043A\\u0430\\u0440\\u0442\"]\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_Card__WEBPACK_IMPORTED_MODULE_3__.CardStack, {\n          count: opponent.cards.length,\n          size: \"small\"\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(TableArea, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.AnimatePresence, {\n          children: currentGame.table.map((tableCard, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(TableCard, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n              card: tableCard.attackCard,\n              size: \"medium\"\n            }), tableCard.defendCard && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n              card: tableCard.defendCard,\n              size: \"medium\"\n            }), !tableCard.defendCard && selectedCard && currentGame.phase === 'defending' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ActionButton, {\n              variant: \"secondary\",\n              onClick: () => handleDefend(index),\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"\\u0417\\u0430\\u0449\\u0438\\u0442\\u0438\\u0442\\u044C\"\n            })]\n          }, index))\n        })\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(DeckArea, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_Card__WEBPACK_IMPORTED_MODULE_3__.CardStack, {\n          count: currentGame.deck.length,\n          size: \"small\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(TrumpCard, {\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            card: currentGame.trumpCard,\n            size: \"small\"\n          })\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(PlayerArea, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(\"div\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(PlayerName, {\n            children: gameState.currentPlayer.name\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(CardCount, {\n            children: [gameState.currentPlayer.cards.length, \" \\u043A\\u0430\\u0440\\u0442\"]\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(PlayerHand, {\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.AnimatePresence, {\n            children: gameState.currentPlayer.cards.map(card => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n              card: card,\n              isPlayable: gameState.canPlayCard(card),\n              isSelected: selectedCard?.id === card.id,\n              onClick: () => handleCardClick(card),\n              size: \"medium\"\n            }, card.id))\n          })\n        }), isMyTurn && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(ActionButtons, {\n          children: [currentGame.phase === 'attacking' && selectedCard && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ActionButton, {\n            variant: \"primary\",\n            onClick: handleAttack,\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: \"\\u0410\\u0442\\u0430\\u043A\\u043E\\u0432\\u0430\\u0442\\u044C\"\n          }), currentGame.phase === 'defending' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ActionButton, {\n            variant: \"danger\",\n            onClick: handleTakeCards,\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: \"\\u0412\\u0437\\u044F\\u0442\\u044C \\u043A\\u0430\\u0440\\u0442\\u044B\"\n          }), currentGame.phase === 'adding' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ActionButton, {\n            variant: \"primary\",\n            onClick: handleFinishTurn,\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: \"\\u0417\\u0430\\u0432\\u0435\\u0440\\u0448\\u0438\\u0442\\u044C \\u0445\\u043E\\u0434\"\n          })]\n        })]\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ActionButton, {\n      variant: \"secondary\",\n      onClick: gameState.resetGame,\n      whileHover: {\n        scale: 1.05\n      },\n      whileTap: {\n        scale: 0.95\n      },\n      children: \"\\u041D\\u043E\\u0432\\u0430\\u044F \\u0438\\u0433\\u0440\\u0430\"\n    })]\n  });\n};\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/game/GameBoard.tsx\n");

/***/ }),

/***/ "./src/entities/game/model/durakSlice.ts":
/*!***********************************************!*\
  !*** ./src/entities/game/model/durakSlice.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initializeGame: () => (/* binding */ initializeGame),\n/* harmony export */   makeMove: () => (/* binding */ makeMove),\n/* harmony export */   setError: () => (/* binding */ setError),\n/* harmony export */   startGame: () => (/* binding */ startGame)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"@reduxjs/toolkit\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @kozyr-master/core */ \"../../packages/core/dist/index.js\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__]);\n_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n // Импортируем типы из пакета core\n\n// Начальное состояние для среза\nconst initialState = {\n  players: [],\n  deck: [],\n  tableCards: [],\n  discardPile: [],\n  trumpCard: undefined,\n  trumpSuit: null,\n  // Инициализируем как null или подходящим значением по умолчанию\n  currentPlayerIndex: -1,\n  attackerIndex: -1,\n  defenderIndex: -1,\n  gameStatus: _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.GameStatus.NOT_STARTED,\n  winner: undefined,\n  passCount: 0,\n  gameInstance: null,\n  // Добавляем для хранения экземпляра игры\n  error: null // Для хранения сообщений об ошибках\n};\n\nconst durakSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n  name: 'durak',\n  initialState,\n  reducers: {\n    // Редьюсер для инициализации новой игры\n    initializeGame: (state, action) => {\n      try {\n        const game = new _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakGame(action.payload.players, action.payload.rules);\n        const gameState = game.getState();\n        // Обновляем состояние Redux из состояния игры\n        Object.assign(state, gameState);\n        state.gameInstance = game; // Сохраняем экземпляр игры\n        state.gameStatus = _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.GameStatus.NOT_STARTED; // Устанавливаем статус\n        state.error = null;\n      } catch (e) {\n        state.error = e.message || 'Failed to initialize game';\n        console.error(\"Error initializing game:\", e);\n      }\n    },\n    // Редьюсер для старта игры\n    startGame: state => {\n      if (state.gameInstance && state.gameStatus === _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.GameStatus.NOT_STARTED) {\n        try {\n          state.gameInstance.startGame();\n          const newState = state.gameInstance.getState();\n          Object.assign(state, newState);\n          state.error = null;\n        } catch (e) {\n          state.error = e.message || 'Failed to start game';\n          console.error(\"Error starting game:\", e);\n        }\n      } else {\n        state.error = 'Game instance not available or game already started/finished.';\n      }\n    },\n    // Редьюсер для выполнения хода\n    makeMove: (state, action) => {\n      if (state.gameInstance && state.gameStatus === _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.GameStatus.IN_PROGRESS) {\n        try {\n          const {\n            playerId,\n            action: playerAction,\n            cardIndex\n          } = action.payload;\n          const moveSuccessful = state.gameInstance.makeMove(playerId, playerAction, cardIndex);\n          if (moveSuccessful) {\n            const newState = state.gameInstance.getState();\n            Object.assign(state, newState);\n            state.error = null;\n          } else {\n            // Можно установить ошибку, если ход не удался, но DurakGame уже логирует ошибки\n            // state.error = 'Invalid move';\n            console.warn('Move was not successful according to game logic.');\n          }\n        } catch (e) {\n          state.error = e.message || 'Failed to make move';\n          console.error(\"Error making move:\", e);\n        }\n      } else {\n        state.error = 'Game instance not available or game not in progress.';\n      }\n    },\n    // Можно добавить другие редьюсеры по мере необходимости\n    setError: (state, action) => {\n      state.error = action.payload;\n    }\n  }\n});\nconst {\n  initializeGame,\n  startGame,\n  makeMove,\n  setError\n} = durakSlice.actions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (durakSlice.reducer);\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/entities/game/model/durakSlice.ts\n");

/***/ }),

/***/ "./src/game/gameLogic.ts":
/*!*******************************!*\
  !*** ./src/game/gameLogic.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPlayerToGame: () => (/* binding */ addPlayerToGame),\n/* harmony export */   canAddCard: () => (/* binding */ canAddCard),\n/* harmony export */   canDefend: () => (/* binding */ canDefend),\n/* harmony export */   checkGameEnd: () => (/* binding */ checkGameEnd),\n/* harmony export */   createDeck: () => (/* binding */ createDeck),\n/* harmony export */   createGame: () => (/* binding */ createGame),\n/* harmony export */   createPlayer: () => (/* binding */ createPlayer),\n/* harmony export */   dealCards: () => (/* binding */ dealCards),\n/* harmony export */   drawCards: () => (/* binding */ drawCards),\n/* harmony export */   executeAction: () => (/* binding */ executeAction),\n/* harmony export */   shuffleDeck: () => (/* binding */ shuffleDeck)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"./src/game/types.ts\");\n\n\n// Создание колоды карт\nfunction createDeck(deckSize = 36, quantumSeed) {\n  const suits = ['hearts', 'diamonds', 'clubs', 'spades'];\n  const ranks = deckSize === 36 ? ['6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'] : ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];\n  const deck = [];\n  suits.forEach(suit => {\n    ranks.forEach(rank => {\n      deck.push({\n        suit,\n        rank,\n        id: `${suit}-${rank}`,\n        value: _types__WEBPACK_IMPORTED_MODULE_0__.RANK_VALUES[rank]\n      });\n    });\n  });\n  return shuffleDeck(deck, quantumSeed);\n}\n\n// Перемешивание колоды\nfunction shuffleDeck(deck, quantumSeed) {\n  const shuffled = [...deck];\n\n  // Используем квантовое семя если доступно, иначе обычный Math.random\n  const getRandom = () => {\n    if (quantumSeed) {\n      // Простой PRNG на основе квантового семени\n      const seed = (quantumSeed * 9301 + 49297) % 233280;\n      return seed / 233280;\n    }\n    return Math.random();\n  };\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(getRandom() * (i + 1));\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\n  }\n  return shuffled;\n}\n\n// Создание нового игрока\nfunction createPlayer(id, name, isBot = false) {\n  return {\n    id,\n    name,\n    cards: [],\n    isBot,\n    emotionalState: isBot ? {\n      confidence: 0.5,\n      aggression: 0.5,\n      patience: 0.5\n    } : undefined\n  };\n}\n\n// Создание новой игры\nfunction createGame(settings) {\n  const deck = createDeck(settings.deckSize);\n  const trumpCard = deck[deck.length - 1]; // Последняя карта - козырь\n\n  return {\n    id: `game-${Date.now()}`,\n    players: [],\n    currentPlayerIndex: 0,\n    attackingPlayerIndex: 0,\n    defendingPlayerIndex: 1,\n    trumpSuit: trumpCard.suit,\n    trumpCard,\n    deck,\n    table: [],\n    discardPile: [],\n    phase: 'waiting',\n    winner: null,\n    gameSettings: settings\n  };\n}\n\n// Добавление игрока в игру\nfunction addPlayerToGame(gameState, player) {\n  if (gameState.players.length >= gameState.gameSettings.maxPlayers) {\n    throw new Error('Игра уже заполнена');\n  }\n  return {\n    ...gameState,\n    players: [...gameState.players, player]\n  };\n}\n\n// Раздача карт\nfunction dealCards(gameState) {\n  if (gameState.players.length < 2) {\n    throw new Error('Недостаточно игроков для начала игры');\n  }\n  const newGameState = {\n    ...gameState\n  };\n  const cardsPerPlayer = 6;\n  let deckIndex = 0;\n\n  // Раздаем по 6 карт каждому игроку\n  newGameState.players = newGameState.players.map(player => ({\n    ...player,\n    cards: newGameState.deck.slice(deckIndex, deckIndex + cardsPerPlayer)\n  }));\n  deckIndex += newGameState.players.length * cardsPerPlayer;\n\n  // Оставшиеся карты в колоде (кроме козырной)\n  newGameState.deck = newGameState.deck.slice(deckIndex, -1);\n  newGameState.phase = 'attacking';\n  return newGameState;\n}\n\n// Проверка, может ли карта бить другую карту\nfunction canDefend(attackCard, defendCard, trumpSuit) {\n  // Если обе карты одной масти, защищающая должна быть старше\n  if (attackCard.suit === defendCard.suit) {\n    return defendCard.value > attackCard.value;\n  }\n\n  // Если атакующая карта не козырь, а защищающая козырь - можно бить\n  if (attackCard.suit !== trumpSuit && defendCard.suit === trumpSuit) {\n    return true;\n  }\n\n  // В остальных случаях нельзя\n  return false;\n}\n\n// Проверка, можно ли подкинуть карту\nfunction canAddCard(card, tableCards) {\n  if (tableCards.length === 0) return false;\n\n  // Можно подкидывать карты того же ранга, что уже есть на столе\n  const existingRanks = new Set();\n  tableCards.forEach(tableCard => {\n    existingRanks.add(tableCard.attackCard.rank);\n    if (tableCard.defendCard) {\n      existingRanks.add(tableCard.defendCard.rank);\n    }\n  });\n  return existingRanks.has(card.rank);\n}\n\n// Выполнение игрового действия\nfunction executeAction(gameState, action) {\n  const newGameState = {\n    ...gameState\n  };\n  switch (action.type) {\n    case 'attack':\n      return executeAttack(newGameState, action);\n    case 'defend':\n      return executeDefend(newGameState, action);\n    case 'add_card':\n      return executeAddCard(newGameState, action);\n    case 'take_cards':\n      return executeTakeCards(newGameState, action);\n    case 'pass_turn':\n      return executePassTurn(newGameState, action);\n    case 'finish_turn':\n      return executeFinishTurn(newGameState, action);\n    default:\n      throw new Error(`Неизвестное действие: ${action.type}`);\n  }\n}\n\n// Выполнение атаки\nfunction executeAttack(gameState, action) {\n  if (!action.card) throw new Error('Карта не указана');\n  const player = gameState.players[gameState.currentPlayerIndex];\n  const cardIndex = player.cards.findIndex(c => c.id === action.card.id);\n  if (cardIndex === -1) throw new Error('Карта не найдена у игрока');\n\n  // Проверяем, можно ли атаковать этой картой\n  if (gameState.table.length > 0 && !canAddCard(action.card, gameState.table)) {\n    throw new Error('Нельзя атаковать этой картой');\n  }\n\n  // Убираем карту у игрока\n  const newPlayer = {\n    ...player,\n    cards: player.cards.filter((_, index) => index !== cardIndex)\n  };\n\n  // Добавляем карту на стол\n  const newTableCard = {\n    attackCard: action.card,\n    position: gameState.table.length\n  };\n  return {\n    ...gameState,\n    players: gameState.players.map((p, index) => index === gameState.currentPlayerIndex ? newPlayer : p),\n    table: [...gameState.table, newTableCard],\n    phase: 'defending'\n  };\n}\n\n// Выполнение защиты\nfunction executeDefend(gameState, action) {\n  if (!action.card || action.targetPosition === undefined) {\n    throw new Error('Карта или позиция не указаны');\n  }\n  const player = gameState.players[gameState.defendingPlayerIndex];\n  const cardIndex = player.cards.findIndex(c => c.id === action.card.id);\n  if (cardIndex === -1) throw new Error('Карта не найдена у игрока');\n  const tableCard = gameState.table[action.targetPosition];\n  if (!tableCard || tableCard.defendCard) {\n    throw new Error('Неверная позиция для защиты');\n  }\n\n  // Проверяем, можно ли защититься этой картой\n  if (!canDefend(tableCard.attackCard, action.card, gameState.trumpSuit)) {\n    throw new Error('Нельзя защититься этой картой');\n  }\n\n  // Убираем карту у игрока\n  const newPlayer = {\n    ...player,\n    cards: player.cards.filter((_, index) => index !== cardIndex)\n  };\n\n  // Добавляем карту защиты на стол\n  const newTable = gameState.table.map((tc, index) => index === action.targetPosition ? {\n    ...tc,\n    defendCard: action.card\n  } : tc);\n\n  // Проверяем, все ли карты защищены\n  const allDefended = newTable.every(tc => tc.defendCard);\n  return {\n    ...gameState,\n    players: gameState.players.map((p, index) => index === gameState.defendingPlayerIndex ? newPlayer : p),\n    table: newTable,\n    phase: allDefended ? 'adding' : 'defending'\n  };\n}\n\n// Выполнение подкидывания карты\nfunction executeAddCard(gameState, action) {\n  if (!action.card) throw new Error('Карта не указана');\n  const player = gameState.players[gameState.currentPlayerIndex];\n  const cardIndex = player.cards.findIndex(c => c.id === action.card.id);\n  if (cardIndex === -1) throw new Error('Карта не найдена у игрока');\n\n  // Проверяем, можно ли подкинуть эту карту\n  if (!canAddCard(action.card, gameState.table)) {\n    throw new Error('Нельзя подкинуть эту карту');\n  }\n\n  // Проверяем лимит карт на столе\n  if (gameState.table.length >= gameState.gameSettings.maxAttackCards) {\n    throw new Error('Достигнут лимит карт в атаке');\n  }\n\n  // Убираем карту у игрока\n  const newPlayer = {\n    ...player,\n    cards: player.cards.filter((_, index) => index !== cardIndex)\n  };\n\n  // Добавляем карту на стол\n  const newTableCard = {\n    attackCard: action.card,\n    position: gameState.table.length\n  };\n  return {\n    ...gameState,\n    players: gameState.players.map((p, index) => index === gameState.currentPlayerIndex ? newPlayer : p),\n    table: [...gameState.table, newTableCard],\n    phase: 'defending'\n  };\n}\n\n// Выполнение взятия карт\nfunction executeTakeCards(gameState, action) {\n  const defendingPlayer = gameState.players[gameState.defendingPlayerIndex];\n\n  // Собираем все карты со стола\n  const cardsFromTable = [];\n  gameState.table.forEach(tableCard => {\n    cardsFromTable.push(tableCard.attackCard);\n    if (tableCard.defendCard) {\n      cardsFromTable.push(tableCard.defendCard);\n    }\n  });\n\n  // Добавляем карты защищающемуся игроку\n  const newDefendingPlayer = {\n    ...defendingPlayer,\n    cards: [...defendingPlayer.cards, ...cardsFromTable]\n  };\n  return {\n    ...gameState,\n    players: gameState.players.map((p, index) => index === gameState.defendingPlayerIndex ? newDefendingPlayer : p),\n    table: [],\n    phase: 'attacking',\n    currentPlayerIndex: getNextPlayerIndex(gameState, gameState.attackingPlayerIndex),\n    attackingPlayerIndex: getNextPlayerIndex(gameState, gameState.attackingPlayerIndex),\n    defendingPlayerIndex: getNextPlayerIndex(gameState, getNextPlayerIndex(gameState, gameState.attackingPlayerIndex))\n  };\n}\n\n// Выполнение завершения хода\nfunction executeFinishTurn(gameState, action) {\n  // Все карты со стола идут в отбой\n  const cardsFromTable = [];\n  gameState.table.forEach(tableCard => {\n    cardsFromTable.push(tableCard.attackCard);\n    if (tableCard.defendCard) {\n      cardsFromTable.push(tableCard.defendCard);\n    }\n  });\n  return {\n    ...gameState,\n    table: [],\n    discardPile: [...gameState.discardPile, ...cardsFromTable],\n    phase: 'attacking',\n    currentPlayerIndex: gameState.defendingPlayerIndex,\n    attackingPlayerIndex: gameState.defendingPlayerIndex,\n    defendingPlayerIndex: getNextPlayerIndex(gameState, gameState.defendingPlayerIndex)\n  };\n}\n\n// Выполнение пропуска хода\nfunction executePassTurn(gameState, action) {\n  const nextPlayerIndex = getNextPlayerIndex(gameState, gameState.currentPlayerIndex);\n  return {\n    ...gameState,\n    currentPlayerIndex: nextPlayerIndex\n  };\n}\n\n// Получение индекса следующего игрока\nfunction getNextPlayerIndex(gameState, currentIndex) {\n  return (currentIndex + 1) % gameState.players.length;\n}\n\n// Проверка окончания игры\nfunction checkGameEnd(gameState) {\n  // Игра заканчивается, когда у игрока закончились карты\n  const playersWithCards = gameState.players.filter(p => p.cards.length > 0);\n  if (playersWithCards.length <= 1) {\n    const winner = playersWithCards.length === 1 ? playersWithCards[0] : null;\n    return {\n      ...gameState,\n      phase: 'finished',\n      winner: winner?.id || null\n    };\n  }\n  return gameState;\n}\n\n// Добор карт из колоды\nfunction drawCards(gameState) {\n  if (gameState.deck.length === 0) return gameState;\n  const newGameState = {\n    ...gameState\n  };\n  const targetHandSize = 6;\n\n  // Добираем карты всем игрокам до 6 карт (или пока есть карты в колоде)\n  newGameState.players = newGameState.players.map(player => {\n    const cardsNeeded = Math.max(0, targetHandSize - player.cards.length);\n    const cardsToTake = Math.min(cardsNeeded, newGameState.deck.length);\n    if (cardsToTake > 0) {\n      const newCards = newGameState.deck.splice(0, cardsToTake);\n      return {\n        ...player,\n        cards: [...player.cards, ...newCards]\n      };\n    }\n    return player;\n  });\n  return newGameState;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/game/gameLogic.ts\n");

/***/ }),

/***/ "./src/game/types.ts":
/*!***************************!*\
  !*** ./src/game/types.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RANK_NAMES: () => (/* binding */ RANK_NAMES),\n/* harmony export */   RANK_VALUES: () => (/* binding */ RANK_VALUES),\n/* harmony export */   SUIT_COLORS: () => (/* binding */ SUIT_COLORS),\n/* harmony export */   SUIT_NAMES: () => (/* binding */ SUIT_NAMES),\n/* harmony export */   SUIT_SYMBOLS: () => (/* binding */ SUIT_SYMBOLS)\n/* harmony export */ });\n// Типы для игры в дурака\n\n// Игра окончена\n\n// Завершение хода\n\n// Константы для игры\nconst SUIT_SYMBOLS = {\n  hearts: '♥️',\n  diamonds: '♦️',\n  clubs: '♣️',\n  spades: '♠️'\n};\nconst SUIT_COLORS = {\n  hearts: 'red',\n  diamonds: 'red',\n  clubs: 'black',\n  spades: 'black'\n};\nconst RANK_VALUES = {\n  '2': 2,\n  '3': 3,\n  '4': 4,\n  '5': 5,\n  '6': 6,\n  '7': 7,\n  '8': 8,\n  '9': 9,\n  '10': 10,\n  'J': 11,\n  'Q': 12,\n  'K': 13,\n  'A': 14\n};\nconst RANK_NAMES = {\n  '2': '2',\n  '3': '3',\n  '4': '4',\n  '5': '5',\n  '6': '6',\n  '7': '7',\n  '8': '8',\n  '9': '9',\n  '10': '10',\n  'J': 'Валет',\n  'Q': 'Дама',\n  'K': 'Король',\n  'A': 'Туз'\n};\nconst SUIT_NAMES = {\n  hearts: 'Червы',\n  diamonds: 'Бубны',\n  clubs: 'Трефы',\n  spades: 'Пики'\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/game/types.ts\n");

/***/ }),

/***/ "./src/hooks/useSimpleGame.ts":
/*!************************************!*\
  !*** ./src/hooks/useSimpleGame.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSimpleGame: () => (/* binding */ useSimpleGame)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _game_gameLogic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../game/gameLogic */ \"./src/game/gameLogic.ts\");\n\n\nconst useSimpleGame = playerId => {\n  const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameEvents, setGameEvents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [currentPlayerId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(playerId || `player-${Date.now()}`);\n\n  // Добавление события в лог\n  const addEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(event => {\n    const newEvent = {\n      ...event,\n      timestamp: Date.now()\n    };\n    setGameEvents(prev => [...prev, newEvent]);\n  }, []);\n\n  // Вычисляемые значения\n  const currentPlayer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => gameState?.players.find(p => p.id === currentPlayerId) || null, [gameState, currentPlayerId]);\n  const isMyTurn = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => gameState ? gameState.players[gameState.currentPlayerIndex]?.id === currentPlayerId : false, [gameState, currentPlayerId]);\n\n  // Создание новой игры\n  const createNewGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(settings => {\n    const newGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.createGame)(settings);\n    setGameState(newGame);\n    setGameEvents([]);\n    addEvent({\n      type: 'game_started',\n      message: 'Новая игра создана'\n    });\n  }, [addEvent]);\n\n  // Присоединение к игре\n  const joinGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(playerName => {\n    if (!gameState) {\n      throw new Error('Игра не создана');\n    }\n    const player = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.createPlayer)(currentPlayerId, playerName, false);\n    try {\n      const updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.addPlayerToGame)(gameState, player);\n      setGameState(updatedGame);\n      addEvent({\n        type: 'player_joined',\n        playerId: currentPlayerId,\n        message: `${playerName} присоединился к игре`\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка присоединения'\n      });\n    }\n  }, [gameState, currentPlayerId, addEvent]);\n\n  // Добавление бота\n  const addBot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(botName => {\n    if (!gameState) return;\n    const bot = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.createPlayer)(`bot-${Date.now()}`, botName, true);\n    try {\n      const updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.addPlayerToGame)(gameState, bot);\n      setGameState(updatedGame);\n      addEvent({\n        type: 'player_joined',\n        playerId: bot.id,\n        message: `Бот ${botName} добавлен в игру`\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка добавления бота'\n      });\n    }\n  }, [gameState, addEvent]);\n\n  // Начало игры\n  const startGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!gameState) {\n      throw new Error('Игра не создана');\n    }\n    if (gameState.players.length < 2) {\n      // Добавляем бота если недостаточно игроков\n      addBot('ИИ Противник');\n      return;\n    }\n    try {\n      let updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.dealCards)(gameState);\n      updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.drawCards)(updatedGame);\n      setGameState(updatedGame);\n      addEvent({\n        type: 'cards_dealt',\n        message: 'Карты розданы, игра началась!'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка начала игры'\n      });\n    }\n  }, [gameState, addBot, addEvent]);\n\n  // Выполнение игрового действия\n  const playAction = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(action => {\n    if (!gameState) {\n      throw new Error('Игра не создана');\n    }\n    try {\n      let updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.executeAction)(gameState, action);\n\n      // Добираем карты после хода\n      if (action.type === 'finish_turn' || action.type === 'take_cards') {\n        updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.drawCards)(updatedGame);\n      }\n\n      // Проверяем окончание игры\n      updatedGame = (0,_game_gameLogic__WEBPACK_IMPORTED_MODULE_1__.checkGameEnd)(updatedGame);\n      setGameState(updatedGame);\n      addEvent({\n        type: 'card_played',\n        playerId: action.playerId,\n        message: `Игрок выполнил действие: ${action.type}`,\n        data: action\n      });\n      if (updatedGame.phase === 'finished') {\n        addEvent({\n          type: 'game_finished',\n          message: updatedGame.winner ? `Игра окончена! Победитель: ${updatedGame.players.find(p => p.id === updatedGame.winner)?.name}` : 'Игра окончена ничьей'\n        });\n      }\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        playerId: action.playerId,\n        message: error instanceof Error ? error.message : 'Ошибка выполнения действия'\n      });\n    }\n  }, [gameState, addEvent]);\n\n  // Проверка, можно ли сыграть карту\n  const canPlayCard = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(card => {\n    if (!gameState || !currentPlayer) return false;\n\n    // Проверяем, есть ли карта у игрока\n    const hasCard = currentPlayer.cards.some(c => c.id === card.id);\n    if (!hasCard) return false;\n\n    // Проверяем, наш ли ход\n    if (!isMyTurn) return false;\n    return true;\n  }, [gameState, currentPlayer, isMyTurn]);\n\n  // Получение доступных действий\n  const getValidActions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!gameState || !currentPlayer || !isMyTurn) return [];\n    const actions = [];\n    const playerIndex = gameState.players.findIndex(p => p.id === currentPlayerId);\n    switch (gameState.phase) {\n      case 'attacking':\n        // Можем атаковать любой картой\n        currentPlayer.cards.forEach(card => {\n          actions.push({\n            type: 'attack',\n            playerId: currentPlayerId,\n            card\n          });\n        });\n\n        // Можем пропустить ход\n        actions.push({\n          type: 'pass_turn',\n          playerId: currentPlayerId\n        });\n        break;\n      case 'defending':\n        if (playerIndex === gameState.defendingPlayerIndex) {\n          // Можем защищаться\n          gameState.table.forEach((tableCard, position) => {\n            if (!tableCard.defendCard) {\n              currentPlayer.cards.forEach(card => {\n                actions.push({\n                  type: 'defend',\n                  playerId: currentPlayerId,\n                  card,\n                  targetPosition: position\n                });\n              });\n            }\n          });\n\n          // Можем взять карты\n          actions.push({\n            type: 'take_cards',\n            playerId: currentPlayerId\n          });\n        }\n        break;\n      case 'adding':\n        if (playerIndex !== gameState.defendingPlayerIndex) {\n          // Можем подкидывать карты\n          currentPlayer.cards.forEach(card => {\n            actions.push({\n              type: 'add_card',\n              playerId: currentPlayerId,\n              card\n            });\n          });\n        }\n\n        // Можем завершить ход\n        actions.push({\n          type: 'finish_turn',\n          playerId: currentPlayerId\n        });\n        break;\n    }\n    return actions;\n  }, [gameState, currentPlayer, isMyTurn, currentPlayerId]);\n\n  // Сброс игры\n  const resetGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setGameState(null);\n    setGameEvents([]);\n  }, []);\n\n  // Автоматические действия ботов\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!gameState || gameState.phase === 'finished') return;\n    const currentGamePlayer = gameState.players[gameState.currentPlayerIndex];\n    if (!currentGamePlayer?.isBot) return;\n\n    // Простая логика бота - случайное действие через небольшую задержку\n    const timer = setTimeout(() => {\n      // Получаем доступные действия для бота\n      const botActions = [];\n      const playerIndex = gameState.players.findIndex(p => p.id === currentGamePlayer.id);\n      switch (gameState.phase) {\n        case 'attacking':\n          if (currentGamePlayer.cards.length > 0) {\n            const randomCard = currentGamePlayer.cards[Math.floor(Math.random() * currentGamePlayer.cards.length)];\n            botActions.push({\n              type: 'attack',\n              playerId: currentGamePlayer.id,\n              card: randomCard\n            });\n          }\n          break;\n        case 'defending':\n          if (playerIndex === gameState.defendingPlayerIndex) {\n            // Простая логика: берем карты\n            botActions.push({\n              type: 'take_cards',\n              playerId: currentGamePlayer.id\n            });\n          }\n          break;\n        case 'adding':\n          if (playerIndex !== gameState.defendingPlayerIndex) {\n            botActions.push({\n              type: 'finish_turn',\n              playerId: currentGamePlayer.id\n            });\n          }\n          break;\n      }\n      if (botActions.length > 0) {\n        const randomAction = botActions[Math.floor(Math.random() * botActions.length)];\n        playAction(randomAction);\n      }\n    }, 1000 + Math.random() * 2000); // 1-3 секунды задержка\n\n    return () => clearTimeout(timer);\n  }, [gameState, playAction]);\n  return {\n    gameState,\n    currentPlayer,\n    isMyTurn,\n    gameEvents,\n    createNewGame,\n    joinGame,\n    startGame,\n    playAction,\n    canPlayCard,\n    getValidActions,\n    resetGame\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useSimpleGame.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-redux */ \"react-redux\");\n/* harmony import */ var _app_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @app/store */ \"./src/app/store/index.ts\");\n/* harmony import */ var _app_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @app/styles/globals.css */ \"./src/app/styles/globals.css\");\n/* harmony import */ var _app_styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_app_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_redux__WEBPACK_IMPORTED_MODULE_0__, _app_store__WEBPACK_IMPORTED_MODULE_1__]);\n([react_redux__WEBPACK_IMPORTED_MODULE_0__, _app_store__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction MyApp({\n  Component,\n  pageProps\n}) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(react_redux__WEBPACK_IMPORTED_MODULE_0__.Provider, {\n    store: _app_store__WEBPACK_IMPORTED_MODULE_1__.store,\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Component, {\n      ...pageProps\n    })\n  });\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUN1QztBQUNKO0FBQ0Y7QUFBQTtBQUVqQyxTQUFTSSxLQUFLQSxDQUFDO0VBQUVDLFNBQVM7RUFBRUM7QUFBb0IsQ0FBQyxFQUFFO0VBQ2pELG9CQUNFSCxzREFBQSxDQUFDSCxpREFBUTtJQUFDQyxLQUFLLEVBQUVBLDZDQUFNO0lBQUFNLFFBQUEsZUFDckJKLHNEQUFBLENBQUNFLFNBQVM7TUFBQSxHQUFLQztJQUFTLENBQUc7RUFBQyxDQUNwQixDQUFDO0FBRWY7QUFFQSxpRUFBZUYsS0FBSyxFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va296eXItbWFzdGVyLXdlYi8uL3NyYy9wYWdlcy9fYXBwLnRzeD9mOWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFByb3BzIH0gZnJvbSBcIm5leHQvYXBwXCI7XG5pbXBvcnQgeyBQcm92aWRlciB9IGZyb20gXCJyZWFjdC1yZWR1eFwiO1xuaW1wb3J0IHsgc3RvcmUgfSBmcm9tIFwiQGFwcC9zdG9yZVwiO1xuaW1wb3J0IFwiQGFwcC9zdHlsZXMvZ2xvYmFscy5jc3NcIjtcblxuZnVuY3Rpb24gTXlBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxQcm92aWRlciBzdG9yZT17c3RvcmV9PlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvUHJvdmlkZXI+XG4gICk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IE15QXBwO1xuIl0sIm5hbWVzIjpbIlByb3ZpZGVyIiwic3RvcmUiLCJqc3giLCJfanN4IiwiTXlBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/durak.tsx":
/*!*****************************!*\
  !*** ./src/pages/durak.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_game_GameBoard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/game/GameBoard */ \"./src/components/game/GameBoard.tsx\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_game_GameBoard__WEBPACK_IMPORTED_MODULE_2__]);\n_components_game_GameBoard__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst DurakPage = () => {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.Fragment, {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"title\", {\n        children: \"\\u0418\\u0433\\u0440\\u0430 \\u0432 \\u0414\\u0443\\u0440\\u0430\\u043A\\u0430 - \\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440 4.0\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"meta\", {\n        name: \"description\",\n        content: \"\\u0418\\u0433\\u0440\\u0430\\u0439\\u0442\\u0435 \\u0432 \\u0434\\u0443\\u0440\\u0430\\u043A\\u0430 \\u0441 \\u0440\\u0435\\u0432\\u043E\\u043B\\u044E\\u0446\\u0438\\u043E\\u043D\\u043D\\u044B\\u043C \\u0418\\u0418 \\u0438 Web3 \\u0438\\u043D\\u0442\\u0435\\u0433\\u0440\\u0430\\u0446\\u0438\\u0435\\u0439\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"meta\", {\n        name: \"viewport\",\n        content: \"width=device-width, initial-scale=1\"\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_components_game_GameBoard__WEBPACK_IMPORTED_MODULE_2__.GameBoardComponent, {})]\n  });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DurakPage);\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvZHVyYWsudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQTBCO0FBQ0c7QUFDcUM7QUFBQTtBQUVsRSxNQUFNUyxTQUFtQixHQUFHQSxDQUFBLEtBQU07RUFDaEMsb0JBQ0VILHVEQUFBLENBQUFFLHVEQUFBO0lBQUFFLFFBQUEsZ0JBQ0VKLHVEQUFBLENBQUNMLGtEQUFJO01BQUFTLFFBQUEsZ0JBQ0hOLHNEQUFBO1FBQUFNLFFBQUEsRUFBTztNQUFpQyxDQUFPLENBQUMsZUFDaEROLHNEQUFBO1FBQU1PLElBQUksRUFBQyxhQUFhO1FBQUNDLE9BQU8sRUFBQztNQUF3RCxDQUFFLENBQUMsZUFDNUZSLHNEQUFBO1FBQU1PLElBQUksRUFBQyxVQUFVO1FBQUNDLE9BQU8sRUFBQztNQUFxQyxDQUFFLENBQUM7SUFBQSxDQUNsRSxDQUFDLGVBRVBSLHNEQUFBLENBQUNGLDBFQUFrQixJQUFFLENBQUM7RUFBQSxDQUN0QixDQUFDO0FBRVAsQ0FBQztBQUVELGlFQUFlTyxTQUFTLEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb3p5ci1tYXN0ZXItd2ViLy4vc3JjL3BhZ2VzL2R1cmFrLnRzeD85NTBlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgSGVhZCBmcm9tICduZXh0L2hlYWQnO1xuaW1wb3J0IHsgR2FtZUJvYXJkQ29tcG9uZW50IH0gZnJvbSAnLi4vY29tcG9uZW50cy9nYW1lL0dhbWVCb2FyZCc7XG5cbmNvbnN0IER1cmFrUGFnZTogUmVhY3QuRkMgPSAoKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIDxIZWFkPlxuICAgICAgICA8dGl0bGU+0JjQs9GA0LAg0LIg0JTRg9GA0LDQutCwIC0g0JrQvtC30YvRgNGMINCc0LDRgdGC0LXRgCA0LjA8L3RpdGxlPlxuICAgICAgICA8bWV0YSBuYW1lPVwiZGVzY3JpcHRpb25cIiBjb250ZW50PVwi0JjQs9GA0LDQudGC0LUg0LIg0LTRg9GA0LDQutCwINGBINGA0LXQstC+0LvRjtGG0LjQvtC90L3Ri9C8INCY0Jgg0LggV2ViMyDQuNC90YLQtdCz0YDQsNGG0LjQtdC5XCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cInZpZXdwb3J0XCIgY29udGVudD1cIndpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xXCIgLz5cbiAgICAgIDwvSGVhZD5cbiAgICAgIFxuICAgICAgPEdhbWVCb2FyZENvbXBvbmVudCAvPlxuICAgIDwvPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgRHVyYWtQYWdlO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiSGVhZCIsIkdhbWVCb2FyZENvbXBvbmVudCIsImpzeCIsIl9qc3giLCJqc3hzIiwiX2pzeHMiLCJGcmFnbWVudCIsIl9GcmFnbWVudCIsIkR1cmFrUGFnZSIsImNoaWxkcmVuIiwibmFtZSIsImNvbnRlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/pages/durak.tsx\n");

/***/ }),

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdurak&preferredRegion=&absolutePagePath=.%2Fpages%2Fdurak.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdurak&preferredRegion=&absolutePagePath=.%2Fpages%2Fdurak.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"../../node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _pages_durak_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/durak.tsx */ \"./src/pages/durak.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_durak_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_durak_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_durak_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_durak_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_durak_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_durak_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_durak_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_durak_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_durak_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_durak_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_durak_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_durak_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_durak_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/durak\",\n        pathname: \"/durak\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_durak_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1yb3V0ZS1sb2FkZXIvaW5kZXguanM/a2luZD1QQUdFUyZwYWdlPSUyRmR1cmFrJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGcGFnZXMlMkZkdXJhay50c3gmYWJzb2x1dGVBcHBQYXRoPXByaXZhdGUtbmV4dC1wYWdlcyUyRl9hcHAmYWJzb2x1dGVEb2N1bWVudFBhdGg9cHJpdmF0ZS1uZXh0LXBhZ2VzJTJGX2RvY3VtZW50Jm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUErRjtBQUNoQztBQUNMO0FBQzFEO0FBQ29EO0FBQ1Y7QUFDMUM7QUFDOEM7QUFDOUM7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLDZDQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLHVCQUF1Qix3RUFBSyxDQUFDLDZDQUFRO0FBQ3JDLHVCQUF1Qix3RUFBSyxDQUFDLDZDQUFRO0FBQ3JDLDJCQUEyQix3RUFBSyxDQUFDLDZDQUFRO0FBQ3pDLGVBQWUsd0VBQUssQ0FBQyw2Q0FBUTtBQUM3Qix3QkFBd0Isd0VBQUssQ0FBQyw2Q0FBUTtBQUM3QztBQUNPLGdDQUFnQyx3RUFBSyxDQUFDLDZDQUFRO0FBQzlDLGdDQUFnQyx3RUFBSyxDQUFDLDZDQUFRO0FBQzlDLGlDQUFpQyx3RUFBSyxDQUFDLDZDQUFRO0FBQy9DLGdDQUFnQyx3RUFBSyxDQUFDLDZDQUFRO0FBQzlDLG9DQUFvQyx3RUFBSyxDQUFDLDZDQUFRO0FBQ3pEO0FBQ08sd0JBQXdCLHlHQUFnQjtBQUMvQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLFdBQVc7QUFDWCxnQkFBZ0I7QUFDaEIsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVELGlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va296eXItbWFzdGVyLXdlYi8/ZjVjNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc1JvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIGFwcCBhbmQgZG9jdW1lbnQgbW9kdWxlcy5cbmltcG9ydCBEb2N1bWVudCBmcm9tIFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19kb2N1bWVudFwiO1xuaW1wb3J0IEFwcCBmcm9tIFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19hcHBcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3BhZ2VzL2R1cmFrLnRzeFwiO1xuLy8gUmUtZXhwb3J0IHRoZSBjb21wb25lbnQgKHNob3VsZCBiZSB0aGUgZGVmYXVsdCBleHBvcnQpLlxuZXhwb3J0IGRlZmF1bHQgaG9pc3QodXNlcmxhbmQsIFwiZGVmYXVsdFwiKTtcbi8vIFJlLWV4cG9ydCBtZXRob2RzLlxuZXhwb3J0IGNvbnN0IGdldFN0YXRpY1Byb3BzID0gaG9pc3QodXNlcmxhbmQsIFwiZ2V0U3RhdGljUHJvcHNcIik7XG5leHBvcnQgY29uc3QgZ2V0U3RhdGljUGF0aHMgPSBob2lzdCh1c2VybGFuZCwgXCJnZXRTdGF0aWNQYXRoc1wiKTtcbmV4cG9ydCBjb25zdCBnZXRTZXJ2ZXJTaWRlUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgXCJnZXRTZXJ2ZXJTaWRlUHJvcHNcIik7XG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsIFwiY29uZmlnXCIpO1xuZXhwb3J0IGNvbnN0IHJlcG9ydFdlYlZpdGFscyA9IGhvaXN0KHVzZXJsYW5kLCBcInJlcG9ydFdlYlZpdGFsc1wiKTtcbi8vIFJlLWV4cG9ydCBsZWdhY3kgbWV0aG9kcy5cbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCBcInVuc3RhYmxlX2dldFN0YXRpY1Byb3BzXCIpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1BhdGhzID0gaG9pc3QodXNlcmxhbmQsIFwidW5zdGFibGVfZ2V0U3RhdGljUGF0aHNcIik7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U3RhdGljUGFyYW1zID0gaG9pc3QodXNlcmxhbmQsIFwidW5zdGFibGVfZ2V0U3RhdGljUGFyYW1zXCIpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFNlcnZlclByb3BzID0gaG9pc3QodXNlcmxhbmQsIFwidW5zdGFibGVfZ2V0U2VydmVyUHJvcHNcIik7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U2VydmVyU2lkZVByb3BzID0gaG9pc3QodXNlcmxhbmQsIFwidW5zdGFibGVfZ2V0U2VydmVyU2lkZVByb3BzXCIpO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgUGFnZXNSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuUEFHRVMsXG4gICAgICAgIHBhZ2U6IFwiL2R1cmFrXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9kdXJha1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiXG4gICAgfSxcbiAgICBjb21wb25lbnRzOiB7XG4gICAgICAgIEFwcCxcbiAgICAgICAgRG9jdW1lbnRcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdurak&preferredRegion=&absolutePagePath=.%2Fpages%2Fdurak.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/app/styles/globals.css":
/*!************************************!*\
  !*** ./src/app/styles/globals.css ***!
  \************************************/
/***/ (() => {



/***/ }),

/***/ "../../packages/core/dist/durak/bot.js":
/*!*********************************************!*\
  !*** ../../packages/core/dist/durak/bot.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n/**\n * Система ботов для игры \"Дурак\"\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BotFactory = exports.DurakBot = exports.BotDifficulty = void 0;\nconst types_1 = __webpack_require__(/*! ../types */ \"../../packages/core/dist/types.js\");\n/**\n * Уровни сложности бота\n */\nvar BotDifficulty;\n(function (BotDifficulty) {\n    BotDifficulty[\"EASY\"] = \"easy\";\n    BotDifficulty[\"MEDIUM\"] = \"medium\";\n    BotDifficulty[\"HARD\"] = \"hard\";\n})(BotDifficulty || (exports.BotDifficulty = BotDifficulty = {}));\n/**\n * Базовый класс бота для игры \"Дурак\"\n */\nclass DurakBot {\n    constructor(id, difficulty = BotDifficulty.MEDIUM) {\n        this.id = id;\n        this.difficulty = difficulty;\n        this.name = `Bot_${difficulty}_${id}`;\n    }\n    /**\n     * Создать игрока-бота\n     */\n    createPlayer() {\n        return {\n            id: this.id,\n            name: this.name,\n            hand: [],\n            isActive: false,\n        };\n    }\n    /**\n     * Принять решение о следующем ходе\n     */\n    makeDecision(gameState, playerId) {\n        const player = gameState.players.find(p => p.id === playerId);\n        if (!player) {\n            throw new Error(`Player ${playerId} not found`);\n        }\n        const isAttacker = gameState.attackerIndex === gameState.players.indexOf(player);\n        const isDefender = gameState.defenderIndex === gameState.players.indexOf(player);\n        // Определяем возможные действия\n        if (isAttacker && gameState.tableCards.length === 0) {\n            // Атакующий должен атаковать\n            return this.decideAttack(gameState, player);\n        }\n        else if (isDefender && this.hasUndefendedCards(gameState)) {\n            // Защитник должен защищаться или брать карты\n            return this.decideDefendOrTake(gameState, player);\n        }\n        else if (isAttacker && this.allCardsDefended(gameState)) {\n            // Атакующий может подкинуть карты или сказать \"бито\"\n            return this.decideThrowOrPass(gameState, player);\n        }\n        else if (!isDefender && this.allCardsDefended(gameState)) {\n            // Другие игроки могут подкинуть карты\n            return this.decideThrow(gameState, player);\n        }\n        // По умолчанию - пас\n        return {\n            action: types_1.PlayerAction.PASS,\n            reasoning: \"Default pass action\",\n        };\n    }\n    /**\n     * Решение об атаке\n     */\n    decideAttack(gameState, player) {\n        const validCards = this.getValidAttackCards(gameState, player);\n        if (validCards.length === 0) {\n            return {\n                action: types_1.PlayerAction.PASS,\n                reasoning: \"No valid attack cards\",\n            };\n        }\n        // Выбираем карту в зависимости от сложности\n        let cardIndex;\n        switch (this.difficulty) {\n            case BotDifficulty.EASY:\n                // Легкий бот играет случайно\n                cardIndex = validCards[Math.floor(Math.random() * validCards.length)];\n                break;\n            case BotDifficulty.MEDIUM:\n                // Средний бот предпочитает младшие карты\n                cardIndex = this.selectLowestCard(player, validCards, gameState.trumpSuit);\n                break;\n            case BotDifficulty.HARD:\n                // Сложный бот использует стратегию\n                cardIndex = this.selectStrategicAttackCard(player, validCards, gameState);\n                break;\n            default:\n                cardIndex = validCards[0];\n        }\n        return {\n            action: types_1.PlayerAction.ATTACK,\n            cardIndex,\n            reasoning: `Attack with card at index ${cardIndex}`,\n        };\n    }\n    /**\n     * Решение о защите или взятии карт\n     */\n    decideDefendOrTake(gameState, player) {\n        const lastPair = gameState.tableCards.at(-1);\n        if (!lastPair || lastPair.length !== 1) {\n            return {\n                action: types_1.PlayerAction.TAKE,\n                reasoning: \"No card to defend against\",\n            };\n        }\n        const attackingCard = lastPair[0];\n        const validDefenseCards = this.getValidDefenseCards(player, attackingCard, gameState.trumpSuit);\n        if (validDefenseCards.length === 0) {\n            return {\n                action: types_1.PlayerAction.TAKE,\n                reasoning: \"No valid defense cards\",\n            };\n        }\n        // Решаем, защищаться или брать карты\n        const shouldDefend = this.shouldDefend(gameState, player, validDefenseCards);\n        if (shouldDefend) {\n            const cardIndex = this.selectDefenseCard(validDefenseCards, attackingCard, gameState.trumpSuit);\n            return {\n                action: types_1.PlayerAction.DEFEND,\n                cardIndex,\n                reasoning: `Defend with card at index ${cardIndex}`,\n            };\n        }\n        else {\n            return {\n                action: types_1.PlayerAction.TAKE,\n                reasoning: \"Decided to take cards instead of defending\",\n            };\n        }\n    }\n    /**\n     * Решение о подкидывании или пасе\n     */\n    decideThrowOrPass(gameState, player) {\n        const throwCards = this.getValidThrowCards(gameState, player);\n        if (throwCards.length === 0 || !this.shouldThrow(gameState, player)) {\n            return {\n                action: types_1.PlayerAction.PASS,\n                reasoning: \"No valid throw cards or decided not to throw\",\n            };\n        }\n        const cardIndex = throwCards[0]; // Простая стратегия - первая подходящая карта\n        return {\n            action: types_1.PlayerAction.ATTACK,\n            cardIndex,\n            reasoning: `Throw card at index ${cardIndex}`,\n        };\n    }\n    /**\n     * Решение о подкидывании (для не-атакующих игроков)\n     */\n    decideThrow(gameState, player) {\n        // Пока что не-атакующие игроки не подкидывают (упрощение)\n        return {\n            action: types_1.PlayerAction.PASS,\n            reasoning: \"Non-attacker decided not to throw\",\n        };\n    }\n    // Вспомогательные методы\n    hasUndefendedCards(gameState) {\n        return gameState.tableCards.some(pair => pair.length === 1);\n    }\n    allCardsDefended(gameState) {\n        return gameState.tableCards.length > 0 && gameState.tableCards.every(pair => pair.length === 2);\n    }\n    getValidAttackCards(gameState, player) {\n        if (gameState.tableCards.length === 0) {\n            // Первая атака - любая карта\n            return player.hand.map((_, index) => index);\n        }\n        // Подкидывание - карты с рангами, уже лежащими на столе\n        const ranksOnTable = new Set(gameState.tableCards.flat().map(card => card.rank));\n        return player.hand\n            .map((card, index) => ({ card, index }))\n            .filter(({ card }) => ranksOnTable.has(card.rank))\n            .map(({ index }) => index);\n    }\n    getValidDefenseCards(player, attackingCard, trumpSuit) {\n        return player.hand\n            .map((card, index) => ({ card, index }))\n            .filter(({ card }) => this.canDefend(attackingCard, card, trumpSuit))\n            .map(({ index }) => index);\n    }\n    canDefend(attackCard, defendCard, trumpSuit) {\n        const getRankValue = (rank) => {\n            const values = {\n                [types_1.CardRank.SIX]: 6,\n                [types_1.CardRank.SEVEN]: 7,\n                [types_1.CardRank.EIGHT]: 8,\n                [types_1.CardRank.NINE]: 9,\n                [types_1.CardRank.TEN]: 10,\n                [types_1.CardRank.JACK]: 11,\n                [types_1.CardRank.QUEEN]: 12,\n                [types_1.CardRank.KING]: 13,\n                [types_1.CardRank.ACE]: 14,\n            };\n            return values[rank];\n        };\n        // Карта той же масти, но старше\n        if (attackCard.suit === defendCard.suit && getRankValue(defendCard.rank) > getRankValue(attackCard.rank)) {\n            return true;\n        }\n        // Козырь бьет не-козырь\n        if (defendCard.suit === trumpSuit && attackCard.suit !== trumpSuit) {\n            return true;\n        }\n        // Козырь бьет козырь, если старше\n        if (attackCard.suit === trumpSuit && defendCard.suit === trumpSuit &&\n            getRankValue(defendCard.rank) > getRankValue(attackCard.rank)) {\n            return true;\n        }\n        return false;\n    }\n    getValidThrowCards(gameState, player) {\n        const ranksOnTable = new Set(gameState.tableCards.flat().map(card => card.rank));\n        return player.hand\n            .map((card, index) => ({ card, index }))\n            .filter(({ card }) => ranksOnTable.has(card.rank))\n            .map(({ index }) => index);\n    }\n    shouldDefend(gameState, player, validDefenseCards) {\n        switch (this.difficulty) {\n            case BotDifficulty.EASY:\n                return Math.random() > 0.3; // 70% шанс защищаться\n            case BotDifficulty.MEDIUM:\n                // Защищается, если у него мало карт или много карт на столе\n                return player.hand.length <= 3 || gameState.tableCards.length >= 3;\n            case BotDifficulty.HARD:\n                // Более сложная логика\n                return this.strategicDefendDecision(gameState, player, validDefenseCards);\n            default:\n                return true;\n        }\n    }\n    shouldThrow(gameState, player) {\n        switch (this.difficulty) {\n            case BotDifficulty.EASY:\n                return Math.random() > 0.7; // 30% шанс подкинуть\n            case BotDifficulty.MEDIUM:\n                return player.hand.length > 5; // Подкидывает, если много карт\n            case BotDifficulty.HARD:\n                return this.strategicThrowDecision(gameState, player);\n            default:\n                return false;\n        }\n    }\n    selectLowestCard(player, validIndices, trumpSuit) {\n        const getRankValue = (rank) => {\n            const values = {\n                [types_1.CardRank.SIX]: 6, [types_1.CardRank.SEVEN]: 7, [types_1.CardRank.EIGHT]: 8, [types_1.CardRank.NINE]: 9,\n                [types_1.CardRank.TEN]: 10, [types_1.CardRank.JACK]: 11, [types_1.CardRank.QUEEN]: 12, [types_1.CardRank.KING]: 13, [types_1.CardRank.ACE]: 14,\n            };\n            return values[rank];\n        };\n        return validIndices.reduce((lowestIndex, currentIndex) => {\n            const lowestCard = player.hand[lowestIndex];\n            const currentCard = player.hand[currentIndex];\n            // Предпочитаем не-козыри\n            if (lowestCard.suit === trumpSuit && currentCard.suit !== trumpSuit) {\n                return currentIndex;\n            }\n            if (lowestCard.suit !== trumpSuit && currentCard.suit === trumpSuit) {\n                return lowestIndex;\n            }\n            // Если обе карты одного типа (козыри или не-козыри), выбираем младшую\n            return getRankValue(currentCard.rank) < getRankValue(lowestCard.rank) ? currentIndex : lowestIndex;\n        });\n    }\n    selectStrategicAttackCard(player, validIndices, gameState) {\n        // Пока что используем простую стратегию - младшая карта\n        return this.selectLowestCard(player, validIndices, gameState.trumpSuit);\n    }\n    selectDefenseCard(validIndices, attackingCard, trumpSuit) {\n        // Выбираем первую подходящую карту (можно улучшить)\n        return validIndices[0];\n    }\n    strategicDefendDecision(gameState, player, validDefenseCards) {\n        // Упрощенная стратегическая логика\n        const cardsOnTable = gameState.tableCards.flat().length;\n        const playerCardCount = player.hand.length;\n        // Не защищается, если на столе много карт и у игрока мало карт\n        if (cardsOnTable >= 6 && playerCardCount <= 4) {\n            return false;\n        }\n        return true;\n    }\n    strategicThrowDecision(gameState, player) {\n        // Подкидывает, если у защитника много карт\n        const defender = gameState.players[gameState.defenderIndex];\n        return defender.hand.length > 6;\n    }\n}\nexports.DurakBot = DurakBot;\n/**\n * Фабрика для создания ботов\n */\nclass BotFactory {\n    /**\n     * Создать бота с указанной сложностью\n     */\n    static createBot(difficulty = BotDifficulty.MEDIUM) {\n        const id = `bot_${++this.botCounter}`;\n        return new DurakBot(id, difficulty);\n    }\n    /**\n     * Создать несколько ботов\n     */\n    static createBots(count, difficulty = BotDifficulty.MEDIUM) {\n        return Array.from({ length: count }, () => this.createBot(difficulty));\n    }\n}\nexports.BotFactory = BotFactory;\nBotFactory.botCounter = 0;\n//# sourceMappingURL=bot.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvY29yZS9kaXN0L2R1cmFrL2JvdC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxrQkFBa0IsR0FBRyxnQkFBZ0IsR0FBRyxxQkFBcUI7QUFDN0QsZ0JBQWdCLG1CQUFPLENBQUMsbURBQVU7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsb0JBQW9CLHFCQUFxQixxQkFBcUI7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsV0FBVyxHQUFHLEdBQUc7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLFVBQVU7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvREFBb0QsVUFBVTtBQUM5RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdEQUF3RCxVQUFVO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5Q0FBeUM7QUFDekM7QUFDQTtBQUNBO0FBQ0EsOENBQThDLFVBQVU7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLGFBQWE7QUFDbEQsdUJBQXVCLE1BQU07QUFDN0Isb0JBQW9CLE9BQU87QUFDM0I7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLGFBQWE7QUFDbEQsdUJBQXVCLE1BQU07QUFDN0Isb0JBQW9CLE9BQU87QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsYUFBYTtBQUNsRCx1QkFBdUIsTUFBTTtBQUM3QixvQkFBb0IsT0FBTztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QztBQUM1QztBQUNBLCtDQUErQztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsa0JBQWtCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixlQUFlO0FBQzNDO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2tvenlyLW1hc3Rlci13ZWIvLi4vLi4vcGFja2FnZXMvY29yZS9kaXN0L2R1cmFrL2JvdC5qcz9kZDZjIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLyoqXG4gKiDQodC40YHRgtC10LzQsCDQsdC+0YLQvtCyINC00LvRjyDQuNCz0YDRiyBcItCU0YPRgNCw0LpcIlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkJvdEZhY3RvcnkgPSBleHBvcnRzLkR1cmFrQm90ID0gZXhwb3J0cy5Cb3REaWZmaWN1bHR5ID0gdm9pZCAwO1xuY29uc3QgdHlwZXNfMSA9IHJlcXVpcmUoXCIuLi90eXBlc1wiKTtcbi8qKlxuICog0KPRgNC+0LLQvdC4INGB0LvQvtC20L3QvtGB0YLQuCDQsdC+0YLQsFxuICovXG52YXIgQm90RGlmZmljdWx0eTtcbihmdW5jdGlvbiAoQm90RGlmZmljdWx0eSkge1xuICAgIEJvdERpZmZpY3VsdHlbXCJFQVNZXCJdID0gXCJlYXN5XCI7XG4gICAgQm90RGlmZmljdWx0eVtcIk1FRElVTVwiXSA9IFwibWVkaXVtXCI7XG4gICAgQm90RGlmZmljdWx0eVtcIkhBUkRcIl0gPSBcImhhcmRcIjtcbn0pKEJvdERpZmZpY3VsdHkgfHwgKGV4cG9ydHMuQm90RGlmZmljdWx0eSA9IEJvdERpZmZpY3VsdHkgPSB7fSkpO1xuLyoqXG4gKiDQkdCw0LfQvtCy0YvQuSDQutC70LDRgdGBINCx0L7RgtCwINC00LvRjyDQuNCz0YDRiyBcItCU0YPRgNCw0LpcIlxuICovXG5jbGFzcyBEdXJha0JvdCB7XG4gICAgY29uc3RydWN0b3IoaWQsIGRpZmZpY3VsdHkgPSBCb3REaWZmaWN1bHR5Lk1FRElVTSkge1xuICAgICAgICB0aGlzLmlkID0gaWQ7XG4gICAgICAgIHRoaXMuZGlmZmljdWx0eSA9IGRpZmZpY3VsdHk7XG4gICAgICAgIHRoaXMubmFtZSA9IGBCb3RfJHtkaWZmaWN1bHR5fV8ke2lkfWA7XG4gICAgfVxuICAgIC8qKlxuICAgICAqINCh0L7Qt9C00LDRgtGMINC40LPRgNC+0LrQsC3QsdC+0YLQsFxuICAgICAqL1xuICAgIGNyZWF0ZVBsYXllcigpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGlkOiB0aGlzLmlkLFxuICAgICAgICAgICAgbmFtZTogdGhpcy5uYW1lLFxuICAgICAgICAgICAgaGFuZDogW10sXG4gICAgICAgICAgICBpc0FjdGl2ZTogZmFsc2UsXG4gICAgICAgIH07XG4gICAgfVxuICAgIC8qKlxuICAgICAqINCf0YDQuNC90Y/RgtGMINGA0LXRiNC10L3QuNC1INC+INGB0LvQtdC00YPRjtGJ0LXQvCDRhdC+0LTQtVxuICAgICAqL1xuICAgIG1ha2VEZWNpc2lvbihnYW1lU3RhdGUsIHBsYXllcklkKSB7XG4gICAgICAgIGNvbnN0IHBsYXllciA9IGdhbWVTdGF0ZS5wbGF5ZXJzLmZpbmQocCA9PiBwLmlkID09PSBwbGF5ZXJJZCk7XG4gICAgICAgIGlmICghcGxheWVyKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFBsYXllciAke3BsYXllcklkfSBub3QgZm91bmRgKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBpc0F0dGFja2VyID0gZ2FtZVN0YXRlLmF0dGFja2VySW5kZXggPT09IGdhbWVTdGF0ZS5wbGF5ZXJzLmluZGV4T2YocGxheWVyKTtcbiAgICAgICAgY29uc3QgaXNEZWZlbmRlciA9IGdhbWVTdGF0ZS5kZWZlbmRlckluZGV4ID09PSBnYW1lU3RhdGUucGxheWVycy5pbmRleE9mKHBsYXllcik7XG4gICAgICAgIC8vINCe0L/RgNC10LTQtdC70Y/QtdC8INCy0L7Qt9C80L7QttC90YvQtSDQtNC10LnRgdGC0LLQuNGPXG4gICAgICAgIGlmIChpc0F0dGFja2VyICYmIGdhbWVTdGF0ZS50YWJsZUNhcmRzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgICAgLy8g0JDRgtCw0LrRg9GO0YnQuNC5INC00L7Qu9C20LXQvSDQsNGC0LDQutC+0LLQsNGC0YxcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmRlY2lkZUF0dGFjayhnYW1lU3RhdGUsIHBsYXllcik7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoaXNEZWZlbmRlciAmJiB0aGlzLmhhc1VuZGVmZW5kZWRDYXJkcyhnYW1lU3RhdGUpKSB7XG4gICAgICAgICAgICAvLyDQl9Cw0YnQuNGC0L3QuNC6INC00L7Qu9C20LXQvSDQt9Cw0YnQuNGJ0LDRgtGM0YHRjyDQuNC70Lgg0LHRgNCw0YLRjCDQutCw0YDRgtGLXG4gICAgICAgICAgICByZXR1cm4gdGhpcy5kZWNpZGVEZWZlbmRPclRha2UoZ2FtZVN0YXRlLCBwbGF5ZXIpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGlzQXR0YWNrZXIgJiYgdGhpcy5hbGxDYXJkc0RlZmVuZGVkKGdhbWVTdGF0ZSkpIHtcbiAgICAgICAgICAgIC8vINCQ0YLQsNC60YPRjtGJ0LjQuSDQvNC+0LbQtdGCINC/0L7QtNC60LjQvdGD0YLRjCDQutCw0YDRgtGLINC40LvQuCDRgdC60LDQt9Cw0YLRjCBcItCx0LjRgtC+XCJcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmRlY2lkZVRocm93T3JQYXNzKGdhbWVTdGF0ZSwgcGxheWVyKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICghaXNEZWZlbmRlciAmJiB0aGlzLmFsbENhcmRzRGVmZW5kZWQoZ2FtZVN0YXRlKSkge1xuICAgICAgICAgICAgLy8g0JTRgNGD0LPQuNC1INC40LPRgNC+0LrQuCDQvNC+0LPRg9GCINC/0L7QtNC60LjQvdGD0YLRjCDQutCw0YDRgtGLXG4gICAgICAgICAgICByZXR1cm4gdGhpcy5kZWNpZGVUaHJvdyhnYW1lU3RhdGUsIHBsYXllcik7XG4gICAgICAgIH1cbiAgICAgICAgLy8g0J/QviDRg9C80L7Qu9GH0LDQvdC40Y4gLSDQv9Cw0YFcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGFjdGlvbjogdHlwZXNfMS5QbGF5ZXJBY3Rpb24uUEFTUyxcbiAgICAgICAgICAgIHJlYXNvbmluZzogXCJEZWZhdWx0IHBhc3MgYWN0aW9uXCIsXG4gICAgICAgIH07XG4gICAgfVxuICAgIC8qKlxuICAgICAqINCg0LXRiNC10L3QuNC1INC+0LEg0LDRgtCw0LrQtVxuICAgICAqL1xuICAgIGRlY2lkZUF0dGFjayhnYW1lU3RhdGUsIHBsYXllcikge1xuICAgICAgICBjb25zdCB2YWxpZENhcmRzID0gdGhpcy5nZXRWYWxpZEF0dGFja0NhcmRzKGdhbWVTdGF0ZSwgcGxheWVyKTtcbiAgICAgICAgaWYgKHZhbGlkQ2FyZHMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIGFjdGlvbjogdHlwZXNfMS5QbGF5ZXJBY3Rpb24uUEFTUyxcbiAgICAgICAgICAgICAgICByZWFzb25pbmc6IFwiTm8gdmFsaWQgYXR0YWNrIGNhcmRzXCIsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIC8vINCS0YvQsdC40YDQsNC10Lwg0LrQsNGA0YLRgyDQsiDQt9Cw0LLQuNGB0LjQvNC+0YHRgtC4INC+0YIg0YHQu9C+0LbQvdC+0YHRgtC4XG4gICAgICAgIGxldCBjYXJkSW5kZXg7XG4gICAgICAgIHN3aXRjaCAodGhpcy5kaWZmaWN1bHR5KSB7XG4gICAgICAgICAgICBjYXNlIEJvdERpZmZpY3VsdHkuRUFTWTpcbiAgICAgICAgICAgICAgICAvLyDQm9C10LPQutC40Lkg0LHQvtGCINC40LPRgNCw0LXRgiDRgdC70YPRh9Cw0LnQvdC+XG4gICAgICAgICAgICAgICAgY2FyZEluZGV4ID0gdmFsaWRDYXJkc1tNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiB2YWxpZENhcmRzLmxlbmd0aCldO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSBCb3REaWZmaWN1bHR5Lk1FRElVTTpcbiAgICAgICAgICAgICAgICAvLyDQodGA0LXQtNC90LjQuSDQsdC+0YIg0L/RgNC10LTQv9C+0YfQuNGC0LDQtdGCINC80LvQsNC00YjQuNC1INC60LDRgNGC0YtcbiAgICAgICAgICAgICAgICBjYXJkSW5kZXggPSB0aGlzLnNlbGVjdExvd2VzdENhcmQocGxheWVyLCB2YWxpZENhcmRzLCBnYW1lU3RhdGUudHJ1bXBTdWl0KTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgQm90RGlmZmljdWx0eS5IQVJEOlxuICAgICAgICAgICAgICAgIC8vINCh0LvQvtC20L3Ri9C5INCx0L7RgiDQuNGB0L/QvtC70YzQt9GD0LXRgiDRgdGC0YDQsNGC0LXQs9C40Y5cbiAgICAgICAgICAgICAgICBjYXJkSW5kZXggPSB0aGlzLnNlbGVjdFN0cmF0ZWdpY0F0dGFja0NhcmQocGxheWVyLCB2YWxpZENhcmRzLCBnYW1lU3RhdGUpO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICBjYXJkSW5kZXggPSB2YWxpZENhcmRzWzBdO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBhY3Rpb246IHR5cGVzXzEuUGxheWVyQWN0aW9uLkFUVEFDSyxcbiAgICAgICAgICAgIGNhcmRJbmRleCxcbiAgICAgICAgICAgIHJlYXNvbmluZzogYEF0dGFjayB3aXRoIGNhcmQgYXQgaW5kZXggJHtjYXJkSW5kZXh9YCxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgLyoqXG4gICAgICog0KDQtdGI0LXQvdC40LUg0L4g0LfQsNGJ0LjRgtC1INC40LvQuCDQstC30Y/RgtC40Lgg0LrQsNGA0YJcbiAgICAgKi9cbiAgICBkZWNpZGVEZWZlbmRPclRha2UoZ2FtZVN0YXRlLCBwbGF5ZXIpIHtcbiAgICAgICAgY29uc3QgbGFzdFBhaXIgPSBnYW1lU3RhdGUudGFibGVDYXJkcy5hdCgtMSk7XG4gICAgICAgIGlmICghbGFzdFBhaXIgfHwgbGFzdFBhaXIubGVuZ3RoICE9PSAxKSB7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIGFjdGlvbjogdHlwZXNfMS5QbGF5ZXJBY3Rpb24uVEFLRSxcbiAgICAgICAgICAgICAgICByZWFzb25pbmc6IFwiTm8gY2FyZCB0byBkZWZlbmQgYWdhaW5zdFwiLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhdHRhY2tpbmdDYXJkID0gbGFzdFBhaXJbMF07XG4gICAgICAgIGNvbnN0IHZhbGlkRGVmZW5zZUNhcmRzID0gdGhpcy5nZXRWYWxpZERlZmVuc2VDYXJkcyhwbGF5ZXIsIGF0dGFja2luZ0NhcmQsIGdhbWVTdGF0ZS50cnVtcFN1aXQpO1xuICAgICAgICBpZiAodmFsaWREZWZlbnNlQ2FyZHMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIGFjdGlvbjogdHlwZXNfMS5QbGF5ZXJBY3Rpb24uVEFLRSxcbiAgICAgICAgICAgICAgICByZWFzb25pbmc6IFwiTm8gdmFsaWQgZGVmZW5zZSBjYXJkc1wiLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgICAvLyDQoNC10YjQsNC10LwsINC30LDRidC40YnQsNGC0YzRgdGPINC40LvQuCDQsdGA0LDRgtGMINC60LDRgNGC0YtcbiAgICAgICAgY29uc3Qgc2hvdWxkRGVmZW5kID0gdGhpcy5zaG91bGREZWZlbmQoZ2FtZVN0YXRlLCBwbGF5ZXIsIHZhbGlkRGVmZW5zZUNhcmRzKTtcbiAgICAgICAgaWYgKHNob3VsZERlZmVuZCkge1xuICAgICAgICAgICAgY29uc3QgY2FyZEluZGV4ID0gdGhpcy5zZWxlY3REZWZlbnNlQ2FyZCh2YWxpZERlZmVuc2VDYXJkcywgYXR0YWNraW5nQ2FyZCwgZ2FtZVN0YXRlLnRydW1wU3VpdCk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIGFjdGlvbjogdHlwZXNfMS5QbGF5ZXJBY3Rpb24uREVGRU5ELFxuICAgICAgICAgICAgICAgIGNhcmRJbmRleCxcbiAgICAgICAgICAgICAgICByZWFzb25pbmc6IGBEZWZlbmQgd2l0aCBjYXJkIGF0IGluZGV4ICR7Y2FyZEluZGV4fWAsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBhY3Rpb246IHR5cGVzXzEuUGxheWVyQWN0aW9uLlRBS0UsXG4gICAgICAgICAgICAgICAgcmVhc29uaW5nOiBcIkRlY2lkZWQgdG8gdGFrZSBjYXJkcyBpbnN0ZWFkIG9mIGRlZmVuZGluZ1wiLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiDQoNC10YjQtdC90LjQtSDQviDQv9C+0LTQutC40LTRi9Cy0LDQvdC40Lgg0LjQu9C4INC/0LDRgdC1XG4gICAgICovXG4gICAgZGVjaWRlVGhyb3dPclBhc3MoZ2FtZVN0YXRlLCBwbGF5ZXIpIHtcbiAgICAgICAgY29uc3QgdGhyb3dDYXJkcyA9IHRoaXMuZ2V0VmFsaWRUaHJvd0NhcmRzKGdhbWVTdGF0ZSwgcGxheWVyKTtcbiAgICAgICAgaWYgKHRocm93Q2FyZHMubGVuZ3RoID09PSAwIHx8ICF0aGlzLnNob3VsZFRocm93KGdhbWVTdGF0ZSwgcGxheWVyKSkge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBhY3Rpb246IHR5cGVzXzEuUGxheWVyQWN0aW9uLlBBU1MsXG4gICAgICAgICAgICAgICAgcmVhc29uaW5nOiBcIk5vIHZhbGlkIHRocm93IGNhcmRzIG9yIGRlY2lkZWQgbm90IHRvIHRocm93XCIsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGNhcmRJbmRleCA9IHRocm93Q2FyZHNbMF07IC8vINCf0YDQvtGB0YLQsNGPINGB0YLRgNCw0YLQtdCz0LjRjyAtINC/0LXRgNCy0LDRjyDQv9C+0LTRhdC+0LTRj9GJ0LDRjyDQutCw0YDRgtCwXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBhY3Rpb246IHR5cGVzXzEuUGxheWVyQWN0aW9uLkFUVEFDSyxcbiAgICAgICAgICAgIGNhcmRJbmRleCxcbiAgICAgICAgICAgIHJlYXNvbmluZzogYFRocm93IGNhcmQgYXQgaW5kZXggJHtjYXJkSW5kZXh9YCxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgLyoqXG4gICAgICog0KDQtdGI0LXQvdC40LUg0L4g0L/QvtC00LrQuNC00YvQstCw0L3QuNC4ICjQtNC70Y8g0L3QtS3QsNGC0LDQutGD0Y7RidC40YUg0LjQs9GA0L7QutC+0LIpXG4gICAgICovXG4gICAgZGVjaWRlVGhyb3coZ2FtZVN0YXRlLCBwbGF5ZXIpIHtcbiAgICAgICAgLy8g0J/QvtC60LAg0YfRgtC+INC90LUt0LDRgtCw0LrRg9GO0YnQuNC1INC40LPRgNC+0LrQuCDQvdC1INC/0L7QtNC60LjQtNGL0LLQsNGO0YIgKNGD0L/RgNC+0YnQtdC90LjQtSlcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGFjdGlvbjogdHlwZXNfMS5QbGF5ZXJBY3Rpb24uUEFTUyxcbiAgICAgICAgICAgIHJlYXNvbmluZzogXCJOb24tYXR0YWNrZXIgZGVjaWRlZCBub3QgdG8gdGhyb3dcIixcbiAgICAgICAgfTtcbiAgICB9XG4gICAgLy8g0JLRgdC/0L7QvNC+0LPQsNGC0LXQu9GM0L3Ri9C1INC80LXRgtC+0LTRi1xuICAgIGhhc1VuZGVmZW5kZWRDYXJkcyhnYW1lU3RhdGUpIHtcbiAgICAgICAgcmV0dXJuIGdhbWVTdGF0ZS50YWJsZUNhcmRzLnNvbWUocGFpciA9PiBwYWlyLmxlbmd0aCA9PT0gMSk7XG4gICAgfVxuICAgIGFsbENhcmRzRGVmZW5kZWQoZ2FtZVN0YXRlKSB7XG4gICAgICAgIHJldHVybiBnYW1lU3RhdGUudGFibGVDYXJkcy5sZW5ndGggPiAwICYmIGdhbWVTdGF0ZS50YWJsZUNhcmRzLmV2ZXJ5KHBhaXIgPT4gcGFpci5sZW5ndGggPT09IDIpO1xuICAgIH1cbiAgICBnZXRWYWxpZEF0dGFja0NhcmRzKGdhbWVTdGF0ZSwgcGxheWVyKSB7XG4gICAgICAgIGlmIChnYW1lU3RhdGUudGFibGVDYXJkcy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgIC8vINCf0LXRgNCy0LDRjyDQsNGC0LDQutCwIC0g0LvRjtCx0LDRjyDQutCw0YDRgtCwXG4gICAgICAgICAgICByZXR1cm4gcGxheWVyLmhhbmQubWFwKChfLCBpbmRleCkgPT4gaW5kZXgpO1xuICAgICAgICB9XG4gICAgICAgIC8vINCf0L7QtNC60LjQtNGL0LLQsNC90LjQtSAtINC60LDRgNGC0Ysg0YEg0YDQsNC90LPQsNC80LgsINGD0LbQtSDQu9C10LbQsNGJ0LjQvNC4INC90LAg0YHRgtC+0LvQtVxuICAgICAgICBjb25zdCByYW5rc09uVGFibGUgPSBuZXcgU2V0KGdhbWVTdGF0ZS50YWJsZUNhcmRzLmZsYXQoKS5tYXAoY2FyZCA9PiBjYXJkLnJhbmspKTtcbiAgICAgICAgcmV0dXJuIHBsYXllci5oYW5kXG4gICAgICAgICAgICAubWFwKChjYXJkLCBpbmRleCkgPT4gKHsgY2FyZCwgaW5kZXggfSkpXG4gICAgICAgICAgICAuZmlsdGVyKCh7IGNhcmQgfSkgPT4gcmFua3NPblRhYmxlLmhhcyhjYXJkLnJhbmspKVxuICAgICAgICAgICAgLm1hcCgoeyBpbmRleCB9KSA9PiBpbmRleCk7XG4gICAgfVxuICAgIGdldFZhbGlkRGVmZW5zZUNhcmRzKHBsYXllciwgYXR0YWNraW5nQ2FyZCwgdHJ1bXBTdWl0KSB7XG4gICAgICAgIHJldHVybiBwbGF5ZXIuaGFuZFxuICAgICAgICAgICAgLm1hcCgoY2FyZCwgaW5kZXgpID0+ICh7IGNhcmQsIGluZGV4IH0pKVxuICAgICAgICAgICAgLmZpbHRlcigoeyBjYXJkIH0pID0+IHRoaXMuY2FuRGVmZW5kKGF0dGFja2luZ0NhcmQsIGNhcmQsIHRydW1wU3VpdCkpXG4gICAgICAgICAgICAubWFwKCh7IGluZGV4IH0pID0+IGluZGV4KTtcbiAgICB9XG4gICAgY2FuRGVmZW5kKGF0dGFja0NhcmQsIGRlZmVuZENhcmQsIHRydW1wU3VpdCkge1xuICAgICAgICBjb25zdCBnZXRSYW5rVmFsdWUgPSAocmFuaykgPT4ge1xuICAgICAgICAgICAgY29uc3QgdmFsdWVzID0ge1xuICAgICAgICAgICAgICAgIFt0eXBlc18xLkNhcmRSYW5rLlNJWF06IDYsXG4gICAgICAgICAgICAgICAgW3R5cGVzXzEuQ2FyZFJhbmsuU0VWRU5dOiA3LFxuICAgICAgICAgICAgICAgIFt0eXBlc18xLkNhcmRSYW5rLkVJR0hUXTogOCxcbiAgICAgICAgICAgICAgICBbdHlwZXNfMS5DYXJkUmFuay5OSU5FXTogOSxcbiAgICAgICAgICAgICAgICBbdHlwZXNfMS5DYXJkUmFuay5URU5dOiAxMCxcbiAgICAgICAgICAgICAgICBbdHlwZXNfMS5DYXJkUmFuay5KQUNLXTogMTEsXG4gICAgICAgICAgICAgICAgW3R5cGVzXzEuQ2FyZFJhbmsuUVVFRU5dOiAxMixcbiAgICAgICAgICAgICAgICBbdHlwZXNfMS5DYXJkUmFuay5LSU5HXTogMTMsXG4gICAgICAgICAgICAgICAgW3R5cGVzXzEuQ2FyZFJhbmsuQUNFXTogMTQsXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgcmV0dXJuIHZhbHVlc1tyYW5rXTtcbiAgICAgICAgfTtcbiAgICAgICAgLy8g0JrQsNGA0YLQsCDRgtC+0Lkg0LbQtSDQvNCw0YHRgtC4LCDQvdC+INGB0YLQsNGA0YjQtVxuICAgICAgICBpZiAoYXR0YWNrQ2FyZC5zdWl0ID09PSBkZWZlbmRDYXJkLnN1aXQgJiYgZ2V0UmFua1ZhbHVlKGRlZmVuZENhcmQucmFuaykgPiBnZXRSYW5rVmFsdWUoYXR0YWNrQ2FyZC5yYW5rKSkge1xuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgLy8g0JrQvtC30YvRgNGMINCx0YzQtdGCINC90LUt0LrQvtC30YvRgNGMXG4gICAgICAgIGlmIChkZWZlbmRDYXJkLnN1aXQgPT09IHRydW1wU3VpdCAmJiBhdHRhY2tDYXJkLnN1aXQgIT09IHRydW1wU3VpdCkge1xuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgLy8g0JrQvtC30YvRgNGMINCx0YzQtdGCINC60L7Qt9GL0YDRjCwg0LXRgdC70Lgg0YHRgtCw0YDRiNC1XG4gICAgICAgIGlmIChhdHRhY2tDYXJkLnN1aXQgPT09IHRydW1wU3VpdCAmJiBkZWZlbmRDYXJkLnN1aXQgPT09IHRydW1wU3VpdCAmJlxuICAgICAgICAgICAgZ2V0UmFua1ZhbHVlKGRlZmVuZENhcmQucmFuaykgPiBnZXRSYW5rVmFsdWUoYXR0YWNrQ2FyZC5yYW5rKSkge1xuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBnZXRWYWxpZFRocm93Q2FyZHMoZ2FtZVN0YXRlLCBwbGF5ZXIpIHtcbiAgICAgICAgY29uc3QgcmFua3NPblRhYmxlID0gbmV3IFNldChnYW1lU3RhdGUudGFibGVDYXJkcy5mbGF0KCkubWFwKGNhcmQgPT4gY2FyZC5yYW5rKSk7XG4gICAgICAgIHJldHVybiBwbGF5ZXIuaGFuZFxuICAgICAgICAgICAgLm1hcCgoY2FyZCwgaW5kZXgpID0+ICh7IGNhcmQsIGluZGV4IH0pKVxuICAgICAgICAgICAgLmZpbHRlcigoeyBjYXJkIH0pID0+IHJhbmtzT25UYWJsZS5oYXMoY2FyZC5yYW5rKSlcbiAgICAgICAgICAgIC5tYXAoKHsgaW5kZXggfSkgPT4gaW5kZXgpO1xuICAgIH1cbiAgICBzaG91bGREZWZlbmQoZ2FtZVN0YXRlLCBwbGF5ZXIsIHZhbGlkRGVmZW5zZUNhcmRzKSB7XG4gICAgICAgIHN3aXRjaCAodGhpcy5kaWZmaWN1bHR5KSB7XG4gICAgICAgICAgICBjYXNlIEJvdERpZmZpY3VsdHkuRUFTWTpcbiAgICAgICAgICAgICAgICByZXR1cm4gTWF0aC5yYW5kb20oKSA+IDAuMzsgLy8gNzAlINGI0LDQvdGBINC30LDRidC40YnQsNGC0YzRgdGPXG4gICAgICAgICAgICBjYXNlIEJvdERpZmZpY3VsdHkuTUVESVVNOlxuICAgICAgICAgICAgICAgIC8vINCX0LDRidC40YnQsNC10YLRgdGPLCDQtdGB0LvQuCDRgyDQvdC10LPQviDQvNCw0LvQviDQutCw0YDRgiDQuNC70Lgg0LzQvdC+0LPQviDQutCw0YDRgiDQvdCwINGB0YLQvtC70LVcbiAgICAgICAgICAgICAgICByZXR1cm4gcGxheWVyLmhhbmQubGVuZ3RoIDw9IDMgfHwgZ2FtZVN0YXRlLnRhYmxlQ2FyZHMubGVuZ3RoID49IDM7XG4gICAgICAgICAgICBjYXNlIEJvdERpZmZpY3VsdHkuSEFSRDpcbiAgICAgICAgICAgICAgICAvLyDQkdC+0LvQtdC1INGB0LvQvtC20L3QsNGPINC70L7Qs9C40LrQsFxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLnN0cmF0ZWdpY0RlZmVuZERlY2lzaW9uKGdhbWVTdGF0ZSwgcGxheWVyLCB2YWxpZERlZmVuc2VDYXJkcyk7XG4gICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHNob3VsZFRocm93KGdhbWVTdGF0ZSwgcGxheWVyKSB7XG4gICAgICAgIHN3aXRjaCAodGhpcy5kaWZmaWN1bHR5KSB7XG4gICAgICAgICAgICBjYXNlIEJvdERpZmZpY3VsdHkuRUFTWTpcbiAgICAgICAgICAgICAgICByZXR1cm4gTWF0aC5yYW5kb20oKSA+IDAuNzsgLy8gMzAlINGI0LDQvdGBINC/0L7QtNC60LjQvdGD0YLRjFxuICAgICAgICAgICAgY2FzZSBCb3REaWZmaWN1bHR5Lk1FRElVTTpcbiAgICAgICAgICAgICAgICByZXR1cm4gcGxheWVyLmhhbmQubGVuZ3RoID4gNTsgLy8g0J/QvtC00LrQuNC00YvQstCw0LXRgiwg0LXRgdC70Lgg0LzQvdC+0LPQviDQutCw0YDRglxuICAgICAgICAgICAgY2FzZSBCb3REaWZmaWN1bHR5LkhBUkQ6XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuc3RyYXRlZ2ljVGhyb3dEZWNpc2lvbihnYW1lU3RhdGUsIHBsYXllcik7XG4gICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzZWxlY3RMb3dlc3RDYXJkKHBsYXllciwgdmFsaWRJbmRpY2VzLCB0cnVtcFN1aXQpIHtcbiAgICAgICAgY29uc3QgZ2V0UmFua1ZhbHVlID0gKHJhbmspID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHZhbHVlcyA9IHtcbiAgICAgICAgICAgICAgICBbdHlwZXNfMS5DYXJkUmFuay5TSVhdOiA2LCBbdHlwZXNfMS5DYXJkUmFuay5TRVZFTl06IDcsIFt0eXBlc18xLkNhcmRSYW5rLkVJR0hUXTogOCwgW3R5cGVzXzEuQ2FyZFJhbmsuTklORV06IDksXG4gICAgICAgICAgICAgICAgW3R5cGVzXzEuQ2FyZFJhbmsuVEVOXTogMTAsIFt0eXBlc18xLkNhcmRSYW5rLkpBQ0tdOiAxMSwgW3R5cGVzXzEuQ2FyZFJhbmsuUVVFRU5dOiAxMiwgW3R5cGVzXzEuQ2FyZFJhbmsuS0lOR106IDEzLCBbdHlwZXNfMS5DYXJkUmFuay5BQ0VdOiAxNCxcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICByZXR1cm4gdmFsdWVzW3JhbmtdO1xuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdmFsaWRJbmRpY2VzLnJlZHVjZSgobG93ZXN0SW5kZXgsIGN1cnJlbnRJbmRleCkgPT4ge1xuICAgICAgICAgICAgY29uc3QgbG93ZXN0Q2FyZCA9IHBsYXllci5oYW5kW2xvd2VzdEluZGV4XTtcbiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRDYXJkID0gcGxheWVyLmhhbmRbY3VycmVudEluZGV4XTtcbiAgICAgICAgICAgIC8vINCf0YDQtdC00L/QvtGH0LjRgtCw0LXQvCDQvdC1LdC60L7Qt9GL0YDQuFxuICAgICAgICAgICAgaWYgKGxvd2VzdENhcmQuc3VpdCA9PT0gdHJ1bXBTdWl0ICYmIGN1cnJlbnRDYXJkLnN1aXQgIT09IHRydW1wU3VpdCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBjdXJyZW50SW5kZXg7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAobG93ZXN0Q2FyZC5zdWl0ICE9PSB0cnVtcFN1aXQgJiYgY3VycmVudENhcmQuc3VpdCA9PT0gdHJ1bXBTdWl0KSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGxvd2VzdEluZGV4O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8g0JXRgdC70Lgg0L7QsdC1INC60LDRgNGC0Ysg0L7QtNC90L7Qs9C+INGC0LjQv9CwICjQutC+0LfRi9GA0Lgg0LjQu9C4INC90LUt0LrQvtC30YvRgNC4KSwg0LLRi9Cx0LjRgNCw0LXQvCDQvNC70LDQtNGI0YPRjlxuICAgICAgICAgICAgcmV0dXJuIGdldFJhbmtWYWx1ZShjdXJyZW50Q2FyZC5yYW5rKSA8IGdldFJhbmtWYWx1ZShsb3dlc3RDYXJkLnJhbmspID8gY3VycmVudEluZGV4IDogbG93ZXN0SW5kZXg7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBzZWxlY3RTdHJhdGVnaWNBdHRhY2tDYXJkKHBsYXllciwgdmFsaWRJbmRpY2VzLCBnYW1lU3RhdGUpIHtcbiAgICAgICAgLy8g0J/QvtC60LAg0YfRgtC+INC40YHQv9C+0LvRjNC30YPQtdC8INC/0YDQvtGB0YLRg9GOINGB0YLRgNCw0YLQtdCz0LjRjiAtINC80LvQsNC00YjQsNGPINC60LDRgNGC0LBcbiAgICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0TG93ZXN0Q2FyZChwbGF5ZXIsIHZhbGlkSW5kaWNlcywgZ2FtZVN0YXRlLnRydW1wU3VpdCk7XG4gICAgfVxuICAgIHNlbGVjdERlZmVuc2VDYXJkKHZhbGlkSW5kaWNlcywgYXR0YWNraW5nQ2FyZCwgdHJ1bXBTdWl0KSB7XG4gICAgICAgIC8vINCS0YvQsdC40YDQsNC10Lwg0L/QtdGA0LLRg9GOINC/0L7QtNGF0L7QtNGP0YnRg9GOINC60LDRgNGC0YMgKNC80L7QttC90L4g0YPQu9GD0YfRiNC40YLRjClcbiAgICAgICAgcmV0dXJuIHZhbGlkSW5kaWNlc1swXTtcbiAgICB9XG4gICAgc3RyYXRlZ2ljRGVmZW5kRGVjaXNpb24oZ2FtZVN0YXRlLCBwbGF5ZXIsIHZhbGlkRGVmZW5zZUNhcmRzKSB7XG4gICAgICAgIC8vINCj0L/RgNC+0YnQtdC90L3QsNGPINGB0YLRgNCw0YLQtdCz0LjRh9C10YHQutCw0Y8g0LvQvtCz0LjQutCwXG4gICAgICAgIGNvbnN0IGNhcmRzT25UYWJsZSA9IGdhbWVTdGF0ZS50YWJsZUNhcmRzLmZsYXQoKS5sZW5ndGg7XG4gICAgICAgIGNvbnN0IHBsYXllckNhcmRDb3VudCA9IHBsYXllci5oYW5kLmxlbmd0aDtcbiAgICAgICAgLy8g0J3QtSDQt9Cw0YnQuNGJ0LDQtdGC0YHRjywg0LXRgdC70Lgg0L3QsCDRgdGC0L7Qu9C1INC80L3QvtCz0L4g0LrQsNGA0YIg0Lgg0YMg0LjQs9GA0L7QutCwINC80LDQu9C+INC60LDRgNGCXG4gICAgICAgIGlmIChjYXJkc09uVGFibGUgPj0gNiAmJiBwbGF5ZXJDYXJkQ291bnQgPD0gNCkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICBzdHJhdGVnaWNUaHJvd0RlY2lzaW9uKGdhbWVTdGF0ZSwgcGxheWVyKSB7XG4gICAgICAgIC8vINCf0L7QtNC60LjQtNGL0LLQsNC10YIsINC10YHQu9C4INGDINC30LDRidC40YLQvdC40LrQsCDQvNC90L7Qs9C+INC60LDRgNGCXG4gICAgICAgIGNvbnN0IGRlZmVuZGVyID0gZ2FtZVN0YXRlLnBsYXllcnNbZ2FtZVN0YXRlLmRlZmVuZGVySW5kZXhdO1xuICAgICAgICByZXR1cm4gZGVmZW5kZXIuaGFuZC5sZW5ndGggPiA2O1xuICAgIH1cbn1cbmV4cG9ydHMuRHVyYWtCb3QgPSBEdXJha0JvdDtcbi8qKlxuICog0KTQsNCx0YDQuNC60LAg0LTQu9GPINGB0L7Qt9C00LDQvdC40Y8g0LHQvtGC0L7QslxuICovXG5jbGFzcyBCb3RGYWN0b3J5IHtcbiAgICAvKipcbiAgICAgKiDQodC+0LfQtNCw0YLRjCDQsdC+0YLQsCDRgSDRg9C60LDQt9Cw0L3QvdC+0Lkg0YHQu9C+0LbQvdC+0YHRgtGM0Y5cbiAgICAgKi9cbiAgICBzdGF0aWMgY3JlYXRlQm90KGRpZmZpY3VsdHkgPSBCb3REaWZmaWN1bHR5Lk1FRElVTSkge1xuICAgICAgICBjb25zdCBpZCA9IGBib3RfJHsrK3RoaXMuYm90Q291bnRlcn1gO1xuICAgICAgICByZXR1cm4gbmV3IER1cmFrQm90KGlkLCBkaWZmaWN1bHR5KTtcbiAgICB9XG4gICAgLyoqXG4gICAgICog0KHQvtC30LTQsNGC0Ywg0L3QtdGB0LrQvtC70YzQutC+INCx0L7RgtC+0LJcbiAgICAgKi9cbiAgICBzdGF0aWMgY3JlYXRlQm90cyhjb3VudCwgZGlmZmljdWx0eSA9IEJvdERpZmZpY3VsdHkuTUVESVVNKSB7XG4gICAgICAgIHJldHVybiBBcnJheS5mcm9tKHsgbGVuZ3RoOiBjb3VudCB9LCAoKSA9PiB0aGlzLmNyZWF0ZUJvdChkaWZmaWN1bHR5KSk7XG4gICAgfVxufVxuZXhwb3J0cy5Cb3RGYWN0b3J5ID0gQm90RmFjdG9yeTtcbkJvdEZhY3RvcnkuYm90Q291bnRlciA9IDA7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ib3QuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/core/dist/durak/bot.js\n");

/***/ }),

/***/ "../../packages/core/dist/durak/index.js":
/*!***********************************************!*\
  !*** ../../packages/core/dist/durak/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n/**\n * Модуль игры \"Дурак\"\n *\n * Содержит основную логику и правила игры \"Дурак\"\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DurakGame = void 0;\n// Импортируем общие типы\nconst types_1 = __webpack_require__(/*! ../types */ \"../../packages/core/dist/types.js\");\n/**\n * Класс игры \"Дурак\"\n */\nclass DurakGame {\n    constructor(players, rules) {\n        this.eventHandlers = [];\n        this.rules = rules;\n        this.state = this.initializeGame(players);\n    }\n    /**\n     * Добавить обработчик событий\n     */\n    addEventListener(handler) {\n        this.eventHandlers.push(handler);\n    }\n    /**\n     * Удалить обработчик событий\n     */\n    removeEventListener(handler) {\n        const index = this.eventHandlers.indexOf(handler);\n        if (index > -1) {\n            this.eventHandlers.splice(index, 1);\n        }\n    }\n    /**\n     * Отправить событие всем обработчикам\n     */\n    emitEvent(eventData) {\n        this.eventHandlers.forEach(handler => {\n            try {\n                handler(eventData);\n            }\n            catch (error) {\n                console.error('Error in game event handler:', error);\n            }\n        });\n    }\n    /**\n     * Инициализация игры\n     */\n    initializeGame(players) {\n        // Создание и перемешивание колоды\n        const deck = this.createDeck();\n        this.shuffleDeck(deck);\n        // Определение козырной карты\n        const trumpCard = deck[deck.length - 1];\n        const trumpSuit = trumpCard.suit;\n        // Раздача карт игрокам\n        this.dealCards(players, deck);\n        // Определение первого игрока (у кого наименьший козырь)\n        const firstPlayerIndex = this.determineFirstPlayer(players, trumpSuit);\n        return {\n            players,\n            deck,\n            tableCards: [],\n            discardPile: [],\n            trumpCard,\n            trumpSuit,\n            currentPlayerIndex: firstPlayerIndex,\n            attackerIndex: firstPlayerIndex,\n            defenderIndex: (firstPlayerIndex + 1) % players.length,\n            gameStatus: types_1.GameStatus.NOT_STARTED,\n        };\n    }\n    /**\n     * Создание колоды карт\n     */\n    createDeck() {\n        const deck = [];\n        const suits = Object.values(types_1.CardSuit);\n        const ranks = Object.values(types_1.CardRank);\n        for (const suit of suits) {\n            for (const rank of ranks) {\n                deck.push({ suit, rank });\n            }\n        }\n        return deck;\n    }\n    /**\n     * Перемешивание колоды\n     */\n    shuffleDeck(deck) {\n        for (let i = deck.length - 1; i > 0; i--) {\n            const j = Math.floor(Math.random() * (i + 1));\n            [deck[i], deck[j]] = [deck[j], deck[i]];\n        }\n    }\n    /**\n     * Раздача карт игрокам\n     */\n    dealCards(players, deck) {\n        const cardsPerPlayer = this.rules.initialHandSize;\n        for (let i = 0; i < cardsPerPlayer; i++) {\n            for (const player of players) {\n                if (deck.length > 0) {\n                    const card = deck.shift();\n                    if (card) {\n                        player.hand.push(card);\n                    }\n                }\n            }\n        }\n    }\n    /**\n     * Определение первого игрока\n     */\n    determineFirstPlayer(players, trumpSuit) {\n        let minTrumpRankIndex = -1;\n        let minTrumpRankValue = Infinity;\n        // Поиск игрока с наименьшим козырем\n        for (let i = 0; i < players.length; i++) {\n            const player = players[i];\n            for (const card of player.hand) {\n                if (card.suit === trumpSuit) {\n                    const rankValue = this.getRankValue(card.rank);\n                    if (rankValue < minTrumpRankValue) {\n                        minTrumpRankValue = rankValue;\n                        minTrumpRankIndex = i;\n                    }\n                }\n            }\n        }\n        // Если ни у кого нет козырей, выбираем случайного игрока\n        if (minTrumpRankIndex === -1) {\n            minTrumpRankIndex = Math.floor(Math.random() * players.length);\n        }\n        return minTrumpRankIndex;\n    }\n    /**\n     * Получение числового значения ранга карты\n     */\n    getRankValue(rank) {\n        const rankValues = {\n            [types_1.CardRank.SIX]: 6,\n            [types_1.CardRank.SEVEN]: 7,\n            [types_1.CardRank.EIGHT]: 8,\n            [types_1.CardRank.NINE]: 9,\n            [types_1.CardRank.TEN]: 10,\n            [types_1.CardRank.JACK]: 11,\n            [types_1.CardRank.QUEEN]: 12,\n            [types_1.CardRank.KING]: 13,\n            [types_1.CardRank.ACE]: 14,\n        };\n        return rankValues[rank];\n    }\n    /**\n     * Получение текущего состояния игры\n     */\n    getState() {\n        return { ...this.state };\n    }\n    /**\n     * Обновление статуса активного игрока\n     */\n    updateActivePlayer() {\n        this.state.players.forEach((player, index) => {\n            player.isActive = index === this.state.currentPlayerIndex;\n        });\n    }\n    /**\n     * Начало игры\n     */\n    startGame() {\n        if (this.state.gameStatus === types_1.GameStatus.NOT_STARTED) {\n            this.state.gameStatus = types_1.GameStatus.IN_PROGRESS;\n            this.updateActivePlayer(); // Устанавливаем первого активного игрока\n            // Отправляем событие о начале игры\n            this.emitEvent({\n                type: types_1.GameEvent.GAME_STARTED,\n                gameState: this.getState(),\n                message: `Game started. First turn: Player ${this.state.players[this.state.currentPlayerIndex].id}`,\n            });\n            console.log(`Game started. First turn: Player ${this.state.players[this.state.currentPlayerIndex].id}`);\n        }\n        else {\n            console.warn(\"Game already started or finished.\");\n        }\n    }\n    /**\n     * Проверка, разрешено ли действие игроку в текущем состоянии игры.\n     * @param playerIndex Индекс игрока, выполняющего действие.\n     * @param action Тип действия (атака, защита, пас, взять).\n     * @returns Объект с флагом `allowed` (true/false) и опциональным сообщением об ошибке `error`.\n     */\n    _isActionAllowed(playerIndex, action) {\n        const isCurrentPlayerTurn = playerIndex === this.state.currentPlayerIndex;\n        const isAttacker = playerIndex === this.state.attackerIndex;\n        const isDefender = playerIndex === this.state.defenderIndex;\n        const lastPair = this.state.tableCards.at(-1);\n        const lastPairDefended = !!lastPair && lastPair.length === 2;\n        const tableIsEmpty = this.state.tableCards.length === 0;\n        // 1. Ход текущего игрока (атакующий или защитник)\n        if (isCurrentPlayerTurn) {\n            // Атакующий может атаковать, если стол пуст или после взятия карт защитником\n            if (isAttacker &&\n                action === types_1.PlayerAction.ATTACK &&\n                (tableIsEmpty || this.state.defenderTookCards)) {\n                return { allowed: true };\n            }\n            // Атакующий может пасовать (бито), если защита была успешной\n            else if (isAttacker && action === types_1.PlayerAction.PASS && lastPairDefended) {\n                return { allowed: true };\n            }\n            // Защитник может защищаться или взять карты\n            else if (isDefender &&\n                (action === types_1.PlayerAction.DEFEND || action === types_1.PlayerAction.TAKE)) {\n                // Дополнительная проверка для TAKE: можно брать только если есть что брать\n                if (action === types_1.PlayerAction.TAKE && tableIsEmpty) {\n                    return {\n                        allowed: false,\n                        error: `Error: Cannot TAKE, no cards on the table.`,\n                    };\n                }\n                // Дополнительная проверка для DEFEND: можно защищаться только если есть атакующая карта\n                if (action === types_1.PlayerAction.DEFEND &&\n                    (!lastPair || lastPair.length !== 1)) {\n                    return {\n                        allowed: false,\n                        error: `Error: Cannot DEFEND, no attacking card found.`,\n                    };\n                }\n                return { allowed: true };\n            }\n            else {\n                return {\n                    allowed: false,\n                    error: `Error: Player ${this.state.players[playerIndex].id} (current: ${this.state.currentPlayerIndex}) cannot perform action ${action} at this stage.`,\n                };\n            }\n        }\n        // 2. Подкидывание (не защитник, после успешной защиты, ход у защитника)\n        else if (action === types_1.PlayerAction.ATTACK &&\n            !isDefender &&\n            lastPairDefended &&\n            this.state.currentPlayerIndex === this.state.defenderIndex) {\n            // Дополнительная проверка: количество карт на столе не должно превышать лимит атаки\n            // И не больше, чем карт у защитника на руках в начале раунда атаки (если стол пуст)\n            const defender = this.state.players[this.state.defenderIndex];\n            const currentAttackLimit = this.state.tableCards.length === 0\n                ? Math.min(this.rules.attackLimit, defender.hand.length)\n                : this.rules.attackLimit;\n            if (this.state.tableCards.flat().length / 2 >= currentAttackLimit) { // Считаем пары карт (атака+защита) или одиночные карты атаки\n                return {\n                    allowed: false,\n                    error: `Error: Cannot podkidnut, attack limit (${currentAttackLimit}) reached.`,\n                };\n            }\n            return { allowed: true }; // Разрешаем подкидывание\n        }\n        // 3. Подкидывание карт (любым игроком, кроме защитника, после успешной защиты)\n        else if (action === types_1.PlayerAction.ATTACK && !isDefender && lastPairDefended) {\n            // Проверяем, есть ли у игрока карты, которые можно подкинуть\n            const player = this.state.players[playerIndex];\n            const validPodkidnoyCards = player.hand.filter(card => this.state.tableCards.flat().some(tableCard => tableCard.rank === card.rank));\n            if (validPodkidnoyCards.length > 0) {\n                // TODO: Логика выбора карты для подкидывания (если их несколько)\n                // Пока просто проверяем возможность\n                // const currentPodkidnoy = validPodkidnoyCards[0]; // Пример\n                return { allowed: true };\n            }\n            else {\n                return { allowed: false, error: `Error: Player ${player.id} has no valid cards to podkidnut.` };\n            }\n        }\n        // 4. Невалидное действие\n        else {\n            return { allowed: false, error: `Error: Action ${action} is not allowed for player ${playerIndex} in the current state.` };\n        }\n    }\n    /**\n     * Выполнение хода игрока\n     */\n    makeMove(playerId, action, cardIndex) {\n        // Проверка, что игра в процессе\n        if (this.state.gameStatus !== types_1.GameStatus.IN_PROGRESS) {\n            console.error(\"Error: Game is not in progress.\");\n            return false;\n        }\n        const playerIndex = this.state.players.findIndex(p => p.id === playerId);\n        if (playerIndex === -1) {\n            console.error(`Error: Player with ID ${playerId} not found.`);\n            return false;\n        }\n        // Проверка, разрешено ли действие\n        const { allowed, error } = this._isActionAllowed(playerIndex, action);\n        if (!allowed) {\n            console.error(error || `Error: Action ${action} is not allowed for player ${playerId} right now.`);\n            return false;\n        }\n        // Логика хода в зависимости от действия\n        let success = false;\n        switch (action) {\n            case types_1.PlayerAction.ATTACK:\n                success = this.handleAttack(playerIndex, cardIndex);\n                break;\n            case types_1.PlayerAction.DEFEND:\n                success = this.handleDefend(playerIndex, cardIndex);\n                break;\n            case types_1.PlayerAction.TAKE:\n                success = this.handleTake(playerIndex);\n                break;\n            case types_1.PlayerAction.PASS:\n                success = this.handlePass(playerIndex);\n                break;\n            default:\n                console.error(`Error: Unknown PlayerAction: ${action}`);\n                return false;\n        }\n        // Если ход был успешным, проверяем конец игры и обновляем активного игрока (если нужно)\n        if (success) {\n            // Отправляем событие о ходе игрока\n            this.emitEvent({\n                type: types_1.GameEvent.PLAYER_MOVED,\n                gameState: this.getState(),\n                playerId,\n                action,\n                cardIndex,\n            });\n            if (!this.checkGameEnd()) {\n                // Логика перехода хода теперь полностью внутри handleTake и handlePass\n                // (через _updateRolesAfterTake и _determineNextRoles соответственно)\n                // Поэтому вызов _moveToNextTurn здесь больше не нужен.\n                // if (action === PlayerAction.TAKE || action === PlayerAction.PASS) {\n                //     // this._moveToNextTurn(); // Удалено\n                // }\n                // this.updateActivePlayer(); // Обновление происходит внутри handle-методов или методов перехода хода\n            }\n        }\n        return success;\n    }\n    /**\n     * Проверка валидности атакующей карты\n     */\n    isValidAttack(card, tableCards, defenderHandSize) {\n        var _a;\n        // Если стол пуст (первый ход атаки), любая карта валидна.\n        if (tableCards.length === 0) {\n            return true;\n        }\n        // Если стол не пуст (подкидывание), ранг карты должен совпадать\n        // с рангом любой карты, уже лежащей на столе (атакующей или защитной).\n        const ranksOnTable = new Set(tableCards.flat().map((c) => c.rank));\n        if (!ranksOnTable.has(card.rank)) {\n            return false;\n        }\n        // Нельзя подкидывать больше карт, чем у защитника на руках (минус уже отбитые в этом раунде)\n        /* const cardsToDefendCount = tableCards.filter(\n          (pair) => pair.length === 1,\n        ).length; */ // УДАЛЕНО, ТАК КАК НЕ ИСПОЛЬЮТСЯ\n        // const maxPodkidnoy = defenderHandSize - cardsToDefendCount; // УДАЛЕНО, ТАК КАК НЕ ИСПОЛЬЮТСЯ\n        // const currentPodkidnoy = tableCards.length - cardsToDefendCount; // Сколько уже подкинули сверх первой атаки - УДАЛЕНО, ТАК КАК НЕ ИСПОЛЬЮТСЯ\n        // Проверяем общее количество карт на столе против лимита\n        const maxCardsOnTable = (_a = this.rules.maxTableCards) !== null && _a !== void 0 ? _a : 6; // Используем правило или 6 по умолчанию\n        if (tableCards.length >= Math.min(maxCardsOnTable, defenderHandSize)) {\n            return false;\n        }\n        return true;\n    }\n    /**\n     * Обработка атаки\n     */\n    handleAttack(playerIndex, cardIndex) {\n        // Проверка наличия cardIndex\n        if (typeof cardIndex !== \"number\" || cardIndex < 0) {\n            console.error(`Error: Valid cardIndex is required for ATTACK action.`);\n            return false;\n        }\n        // Атаковать (или подкидывать) может любой игрок, кроме защищающегося\n        // Эта проверка уже сделана в _isActionAllowed\n        // if (playerIndex === this.state.defenderIndex) {\n        //   console.error(\"Error: The defender cannot attack.\");\n        //   return false;\n        // }\n        const player = this.state.players[playerIndex];\n        if (cardIndex >= player.hand.length) {\n            console.error(`Error: Invalid card index ${cardIndex} for player ${player.id}.`);\n            return false;\n        }\n        const card = player.hand[cardIndex];\n        const defender = this.state.players[this.state.defenderIndex];\n        // Проверка валидности карты для атаки/подкидывания\n        if (!this.isValidAttack(card, this.state.tableCards, defender.hand.length)) {\n            console.error(`Error: Card ${card.rank} ${card.suit} is not a valid attack/podkidnoy card.`);\n            return false;\n        }\n        // Перемещаем карту из руки на стол\n        player.hand.splice(cardIndex, 1);\n        this.state.tableCards.push([card]);\n        // Сбрасываем флаг взятия карт, так как началась новая атака\n        this.state.defenderTookCards = false;\n        // Передаем ход защитнику\n        this.state.currentPlayerIndex = this.state.defenderIndex;\n        this.updateActivePlayer();\n        console.log(`Player ${player.id} attacks with ${card.rank} ${card.suit}. Turn passes to defender ${defender.id}.`);\n        return true;\n    }\n    /**\n     * Проверка валидности защищающейся карты\n     */\n    isValidDefense(attackCard, defendCard, trumpSuit) {\n        // Карта той же масти, но старше\n        if (attackCard.suit === defendCard.suit &&\n            this.getRankValue(defendCard.rank) > this.getRankValue(attackCard.rank)) {\n            return true;\n        }\n        // Карта - козырь, а атакующая карта - нет\n        if (defendCard.suit === trumpSuit && attackCard.suit !== trumpSuit) {\n            return true;\n        }\n        // Обе карты козырные, защищающаяся карта старше\n        if (attackCard.suit === trumpSuit &&\n            defendCard.suit === trumpSuit &&\n            this.getRankValue(defendCard.rank) > this.getRankValue(attackCard.rank)) {\n            return true;\n        }\n        return false;\n    }\n    /**\n     * Обработка защиты\n     */\n    handleDefend(playerIndex, cardIndex) {\n        var _a;\n        // Проверка наличия cardIndex\n        if (typeof cardIndex !== \"number\" || cardIndex < 0) {\n            console.error(`Error: Valid cardIndex is required for DEFEND action.`);\n            return false;\n        }\n        // Защищаться может только защищающийся игрок\n        // Эта проверка уже сделана в _isActionAllowed\n        // if (playerIndex !== this.state.defenderIndex) {\n        //   console.error(\"Error: Only the defender can defend.\");\n        //   return false;\n        // }\n        const player = this.state.players[playerIndex];\n        if (cardIndex >= player.hand.length) {\n            console.error(`Error: Invalid card index ${cardIndex} for player ${player.id}.`);\n            return false;\n        }\n        const defendingCard = player.hand[cardIndex];\n        // Находим последнюю атакующую карту, которую нужно отбить\n        const lastPair = this.state.tableCards.at(-1);\n        if (!lastPair || lastPair.length !== 1) {\n            console.error(\"Error: No attacking card to defend against.\");\n            return false;\n        }\n        const attackingCard = lastPair[0];\n        // Проверяем, может ли выбранная карта отбить атакующую\n        if (!this.isValidDefense(attackingCard, defendingCard, this.state.trumpSuit)) {\n            console.error(`Error: Card ${defendingCard.rank} ${defendingCard.suit} cannot defend against ${attackingCard.rank} ${attackingCard.suit}.`);\n            return false;\n        }\n        // Перемещаем карту из руки на стол к атакующей карте\n        player.hand.splice(cardIndex, 1);\n        lastPair.push(defendingCard);\n        // Проверяем, все ли карты на столе отбиты\n        const allDefended = this.state.tableCards.every((pair) => pair.length === 2);\n        const defenderHasCards = player.hand.length > 0;\n        const canPodkidnut = this.state.tableCards.length < ((_a = this.rules.maxTableCards) !== null && _a !== void 0 ? _a : 6);\n        // Если все отбито и у защитника нет карт ИЛИ нельзя больше подкидывать, ход атакующего (сказать пас/бито)\n        if (allDefended && (!defenderHasCards || !canPodkidnut)) {\n            this.state.currentPlayerIndex = this.state.attackerIndex;\n            console.log(`Defender ${player.id} defended with ${defendingCard.rank} ${defendingCard.suit}. All cards defended. Turn passes to attacker ${this.state.players[this.state.attackerIndex].id} to pass.`);\n        }\n        // Если все отбито, но можно подкидывать и у защитника есть карты, ход остается у защитника (ожидание подкидывания или паса)\n        else if (allDefended) {\n            this.state.currentPlayerIndex = this.state.defenderIndex; // Остается у защитника, но он ждет\n            console.log(`Defender ${player.id} defended with ${defendingCard.rank} ${defendingCard.suit}. Waiting for podkidnoy or pass from attacker(s).`);\n        }\n        // Если не все отбито (это не должно произойти здесь, т.к. мы только что добавили карту)\n        // Оставляем ход у защитника для следующей защиты\n        else {\n            this.state.currentPlayerIndex = this.state.defenderIndex;\n            console.log(`Defender ${player.id} defended with ${defendingCard.rank} ${defendingCard.suit}. Turn remains with defender.`);\n        }\n        this.updateActivePlayer();\n        return true;\n    }\n    /**\n     * Вспомогательный метод: Защитник берет карты со стола\n     */\n    _defenderTakesCards(playerIndex) {\n        const player = this.state.players[playerIndex];\n        const cardsToTake = this.state.tableCards.flat();\n        player.hand.push(...cardsToTake);\n        this.state.tableCards = [];\n        this.state.defenderTookCards = true; // Устанавливаем флаг, что защитник взял карты\n        console.log(`Player ${player.id} takes ${cardsToTake.length} cards from the table.`);\n    }\n    /**\n     * Вспомогательный метод: Обновляет роли после того, как защитник взял карты.\n     * Ход переходит к следующему игроку после взявшего.\n     * @returns {boolean} Возвращает true, если игра окончена, иначе false.\n     */\n    _updateRolesAfterTake() {\n        const numPlayers = this.state.players.length;\n        const playerWhoTookIndex = this.state.defenderIndex; // Индекс игрока, который только что взял карты\n        // Определяем следующего атакующего, пропуская выбывших\n        let nextAttackerIndex = (playerWhoTookIndex + 1) % numPlayers;\n        let loopCheck = 0;\n        while (this.state.players[nextAttackerIndex].hand.length === 0 &&\n            this.state.deck.length === 0 &&\n            loopCheck < numPlayers) {\n            if (nextAttackerIndex === playerWhoTookIndex) {\n                // Обошли круг и вернулись к тому, кто взял - он единственный оставшийся\n                console.log(\"Game ended: Only the player who took cards remains.\");\n                return this.checkGameEnd();\n            }\n            nextAttackerIndex = (nextAttackerIndex + 1) % numPlayers;\n            loopCheck++;\n        }\n        // Если после цикла nextAttackerIndex совпадает с playerWhoTookIndex, игра окончена\n        if (nextAttackerIndex === playerWhoTookIndex &&\n            loopCheck >= numPlayers - 1) {\n            console.log(\"Game ended: All other players are out after take.\");\n            return this.checkGameEnd();\n        }\n        // Определяем следующего защитника, пропуская выбывших\n        let nextDefenderIndex = (nextAttackerIndex + 1) % numPlayers;\n        loopCheck = 0;\n        while (this.state.players[nextDefenderIndex].hand.length === 0 &&\n            this.state.deck.length === 0 &&\n            loopCheck < numPlayers) {\n            if (nextDefenderIndex === nextAttackerIndex) {\n                // Обошли круг и вернулись к атакующему - он единственный оставшийся\n                console.log(\"Game ended: Only the next attacker remains.\");\n                return this.checkGameEnd();\n            }\n            nextDefenderIndex = (nextDefenderIndex + 1) % numPlayers;\n            loopCheck++;\n        }\n        // Если после цикла nextDefenderIndex совпадает с nextAttackerIndex, игра окончена\n        if (nextDefenderIndex === nextAttackerIndex &&\n            loopCheck >= numPlayers - 1) {\n            console.log(\"Game ended: Only attacker and defender remain, but defender cannot defend.\");\n            return this.checkGameEnd();\n        }\n        this.state.attackerIndex = nextAttackerIndex;\n        this.state.defenderIndex = nextDefenderIndex;\n        this.state.currentPlayerIndex = this.state.attackerIndex; // Ход переходит к новому атакующему\n        console.log(`Roles updated after take: Attacker=${this.state.players[this.state.attackerIndex].id}, Defender=${this.state.players[this.state.defenderIndex].id}`);\n        this.updateActivePlayer();\n        return false; // Игра не закончена этим действием\n    }\n    /**\n     * Обработка взятия карт (защитник берет)\n     */\n    handleTake(playerIndex) {\n        // Проверки перенесены в _isActionAllowed\n        // if (playerIndex !== this.state.defenderIndex) {\n        //   console.error(\"Error: Only the defender can take cards.\");\n        //   return false;\n        // }\n        // Проверяем, есть ли карты на столе для взятия\n        if (this.state.tableCards.length === 0) {\n            console.error(\"Error: No cards on the table to take.\");\n            return false;\n        }\n        // Защитник берет карты\n        this._defenderTakesCards(playerIndex);\n        // Пополняем руки (начиная с атакующего, затем защитник, потом остальные)\n        // Порядок: атакующий -> ... -> защитник\n        this.replenishHands();\n        // Проверяем окончание игры после пополнения рук\n        if (this.checkGameEnd()) {\n            return true;\n        }\n        // Обновляем роли атакующего и защитника\n        const gameEnded = this._updateRolesAfterTake();\n        if (gameEnded) {\n            return true;\n        }\n        // Сбрасываем флаг ПОСЛЕ обновления ролей и перехода хода\n        this.state.defenderTookCards = false;\n        return true;\n    }\n    /**\n     * Вспомогательный метод: Перемещает карты со стола в отбой\n     */\n    _clearTableToDiscardPile() {\n        this.state.discardPile.push(...this.state.tableCards.flat());\n        this.state.tableCards = [];\n    }\n    /**\n     * Вспомогательный метод: Определяет следующего атакующего и защитника\n     * @returns {boolean} Возвращает true, если удалось определить роли (игра продолжается), иначе false (игра окончена).\n     */\n    _determineNextRoles() {\n        const numPlayers = this.state.players.length;\n        const previousDefender = this.state.defenderIndex;\n        // Новый атакующий - это предыдущий защитник\n        this.state.attackerIndex = previousDefender;\n        // Определяем нового защитника, пропуская выбывших игроков\n        let nextDefenderIndex = (this.state.attackerIndex + 1) % numPlayers;\n        let loopCheck = 0; // Предотвращение бесконечного цикла, если что-то пойдет не так\n        while (this.state.players[nextDefenderIndex].hand.length === 0 &&\n            this.state.deck.length === 0 &&\n            loopCheck < numPlayers // Проверяем не больше, чем количество игроков\n        ) {\n            if (nextDefenderIndex === this.state.attackerIndex) {\n                // Если обошли круг и вернулись к атакующему, значит, все остальные выбыли\n                console.log(\"Game potentially ended: Only attacker remains with cards or deck is empty.\");\n                return false; // Сигнализируем, что роли определить не удалось (игра окончена)\n            }\n            nextDefenderIndex = (nextDefenderIndex + 1) % numPlayers;\n            loopCheck++;\n        }\n        // Если после цикла nextDefenderIndex совпадает с attackerIndex, игра окончена\n        if (nextDefenderIndex === this.state.attackerIndex &&\n            loopCheck >= numPlayers - 1) {\n            console.log(\"Game ended: All other players are out.\");\n            return false;\n        }\n        this.state.defenderIndex = nextDefenderIndex;\n        this.state.currentPlayerIndex = this.state.attackerIndex; // Ход переходит к новому атакующему\n        return true; // Роли успешно определены\n    }\n    /**\n     * Обработка паса (бито) - атакующий завершает раунд после успешной защиты\n     */\n    handlePass(playerIndex) {\n        // Проверяем, что это атакующий игрок\n        if (playerIndex !== this.state.attackerIndex) {\n            console.error(\"Error: Only the attacker can pass (finish the round).\");\n            return false;\n        }\n        // Проверяем, есть ли карты на столе и все ли они отбиты\n        if (this.state.tableCards.length === 0) {\n            console.error(\"Error: Cannot pass, no cards on the table.\");\n            return false;\n        }\n        const allDefended = this.state.tableCards.every((pair) => pair.length === 2);\n        if (!allDefended) {\n            console.error(\"Error: Cannot pass, not all cards are defended.\");\n            return false;\n        }\n        // Перемещаем карты со стола в отбой\n        this._clearTableToDiscardPile();\n        // Пополняем руки игроков\n        this.replenishHands();\n        // Сбрасываем флаг перед проверкой конца игры и определением ролей\n        this.state.defenderTookCards = false;\n        // Проверяем окончание игры после пополнения рук\n        if (this.checkGameEnd()) {\n            return true; // Игра завершена\n        }\n        // Определяем следующие роли и передаем ход\n        if (!this._determineNextRoles()) {\n            // Если определить роли не удалось (например, остался 1 игрок), проверяем конец игры еще раз\n            return this.checkGameEnd();\n        }\n        // Обновляем статус активного игрока\n        this.updateActivePlayer();\n        console.log(`Round finished (Pass). New attacker: ${this.state.players[this.state.attackerIndex].id}, New defender: ${this.state.players[this.state.defenderIndex].id}`);\n        return true;\n    }\n    /**\n     * Обработка завершения хода (атакующий говорит \"бито\" или \"пас\")\n     */\n    handleDone(playerIndex) {\n        // Проверяем, что это действительно атакующий игрок завершает ход\n        if (playerIndex !== this.state.attackerIndex) {\n            console.error(\"Error: Only the attacker can finish the turn with 'Done'.\");\n            return false;\n        }\n        // Проверяем, есть ли карты на столе (был ли хотя бы один ход атаки)\n        if (this.state.tableCards.length > 0) {\n            // Перемещаем карты со стола в отбой\n            this._clearTableToDiscardPile();\n        }\n        else {\n            // Если стол пуст, значит атакующий спасовал сразу\n            console.log(`Player ${this.state.players[playerIndex].id} passed the turn immediately.`);\n            // Ничего не делаем с картами, просто передаем ход\n        }\n        // Пополняем руки игроков\n        this.replenishHands();\n        // Проверяем окончание игры после пополнения рук\n        if (this.checkGameEnd()) {\n            return true; // Игра завершена\n        }\n        // Определяем следующие роли и передаем ход\n        // Логика такая же, как при 'Pass', т.к. защитник успешно отбился (или атаки не было)\n        if (!this._determineNextRoles()) {\n            // Если определить роли не удалось (например, остался 1 игрок), проверяем конец игры еще раз\n            return this.checkGameEnd();\n        }\n        // Обновляем статус активного игрока\n        this.updateActivePlayer();\n        console.log(`Round finished (Done/Pass). New attacker: ${this.state.players[this.state.attackerIndex].id}, New defender: ${this.state.players[this.state.defenderIndex].id}`);\n        return true;\n    }\n    /**\n     * Пополнение рук игроков из колоды до нужного количества\n     */\n    replenishHands() {\n        const cardsNeeded = this.rules.initialHandSize;\n        const numPlayers = this.state.players.length;\n        let currentPlayerToCheck = this.state.attackerIndex; // Начинаем с атакующего\n        for (let i = 0; i < numPlayers; i++) {\n            const player = this.state.players[currentPlayerToCheck];\n            // Пополняем руку, только если игрок еще в игре (есть карты или есть колода)\n            if (player.hand.length > 0 || this.state.deck.length > 0) {\n                while (player.hand.length < cardsNeeded && this.state.deck.length > 0) {\n                    const card = this.state.deck.shift();\n                    if (card) {\n                        player.hand.push(card);\n                    }\n                }\n            }\n            // Переходим к следующему игроку по кругу\n            currentPlayerToCheck = (currentPlayerToCheck + 1) % numPlayers;\n        }\n        // Если колода закончилась и козырь был под ней, добавляем его в state\n        if (this.state.deck.length === 0 &&\n            this.state.trumpCard &&\n            !this.state.players.some((p) => p.hand.includes(this.state.trumpCard))) {\n            // Козырь забирает игрок, который последним пополнил руку (если ему нужно)\n            // В нашей логике пополнения это будет игрок перед атакующим, если круг полный\n            // Но проще отдать его текущему атакующему, если у него меньше 6 карт.\n            // Или просто оставить его видимым, но не в игре? Правила разнятся.\n            // Пока оставим его видимым в state.trumpCard, но не в руках.\n            // Убираем TODO, т.к. конкретная реализация зависит от выбранных правил.\n            // this.state.trumpCard = undefined;\n        }\n    }\n    /**\n     * Проверка окончания игры\n     */\n    checkGameEnd() {\n        // Проверка условий окончания игры\n        const playersWithCards = this.state.players.filter((p) => p.hand.length > 0);\n        const playersWithoutCards = this.state.players.filter((p) => p.hand.length === 0);\n        // Игра заканчивается, если колода пуста и не более одного игрока с картами\n        if (this.state.deck.length === 0 && playersWithCards.length <= 1) {\n            this.state.gameStatus = types_1.GameStatus.FINISHED;\n            let message = \"\";\n            if (playersWithCards.length === 1) {\n                // Проигравший - тот, у кого остались карты\n                this.state.loser = playersWithCards[0];\n                console.log(`Game finished. Loser: ${this.state.loser.id}`);\n                // Победитель - первый игрок, который избавился от всех карт\n                // В классическом дураке первый вышедший считается победителем\n                this.state.winner = playersWithoutCards.length > 0 ? playersWithoutCards[0] : undefined;\n                if (this.state.winner) {\n                    console.log(`Winner: ${this.state.winner.id}`);\n                    message = `Game finished. Winner: ${this.state.winner.id}, Loser: ${this.state.loser.id}`;\n                }\n                else {\n                    message = `Game finished. Loser: ${this.state.loser.id}`;\n                }\n            }\n            else {\n                // Ничья (все сбросили карты одновременно) - очень редкий случай\n                console.log(\"Game finished. Draw!\");\n                this.state.winner = undefined;\n                this.state.loser = undefined;\n                message = \"Game finished. Draw!\";\n            }\n            // Отправляем событие окончания игры\n            this.emitEvent({\n                type: types_1.GameEvent.GAME_ENDED,\n                gameState: this.getState(),\n                message,\n            });\n            // Обнуляем активного игрока, т.к. игра завершена\n            this.state.currentPlayerIndex = -1;\n            this.updateActivePlayer();\n            return true;\n        }\n        return false;\n    }\n}\nexports.DurakGame = DurakGame;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/core/dist/durak/index.js\n");

/***/ }),

/***/ "../../packages/core/dist/index.js":
/*!*****************************************!*\
  !*** ../../packages/core/dist/index.js ***!
  \*****************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\n/**\n * Игровое ядро \"Козырь Мастер\"\n *\n * Этот файл экспортирует основные классы и интерфейсы игрового ядра\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BotDifficulty = exports.BotFactory = exports.DurakBot = void 0;\n// Экспорт общих типов\n__exportStar(__webpack_require__(/*! ./types */ \"../../packages/core/dist/types.js\"), exports);\n// Экспорт основной логики игры Дурак\n__exportStar(__webpack_require__(/*! ./durak */ \"../../packages/core/dist/durak/index.js\"), exports);\n// Экспорт системы ботов\nvar bot_1 = __webpack_require__(/*! ./durak/bot */ \"../../packages/core/dist/durak/bot.js\");\nObject.defineProperty(exports, \"DurakBot\", ({ enumerable: true, get: function () { return bot_1.DurakBot; } }));\nObject.defineProperty(exports, \"BotFactory\", ({ enumerable: true, get: function () { return bot_1.BotFactory; } }));\nObject.defineProperty(exports, \"BotDifficulty\", ({ enumerable: true, get: function () { return bot_1.BotDifficulty; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/core/dist/index.js\n");

/***/ }),

/***/ "../../packages/core/dist/types.js":
/*!*****************************************!*\
  !*** ../../packages/core/dist/types.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n/**\n * Общие типы и интерфейсы для игрового ядра \"Козырь Мастер\"\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GameEvent = exports.PlayerAction = exports.GameStatus = exports.DurakVariant = exports.CardRank = exports.CardSuit = void 0;\n// Типы карт\nvar CardSuit;\n(function (CardSuit) {\n    CardSuit[\"HEARTS\"] = \"hearts\";\n    CardSuit[\"DIAMONDS\"] = \"diamonds\";\n    CardSuit[\"CLUBS\"] = \"clubs\";\n    CardSuit[\"SPADES\"] = \"spades\";\n})(CardSuit || (exports.CardSuit = CardSuit = {}));\nvar CardRank;\n(function (CardRank) {\n    CardRank[\"SIX\"] = \"6\";\n    CardRank[\"SEVEN\"] = \"7\";\n    CardRank[\"EIGHT\"] = \"8\";\n    CardRank[\"NINE\"] = \"9\";\n    CardRank[\"TEN\"] = \"10\";\n    CardRank[\"JACK\"] = \"jack\";\n    CardRank[\"QUEEN\"] = \"queen\";\n    CardRank[\"KING\"] = \"king\";\n    CardRank[\"ACE\"] = \"ace\";\n})(CardRank || (exports.CardRank = CardRank = {}));\n// Варианты игры Дурак\nvar DurakVariant;\n(function (DurakVariant) {\n    DurakVariant[\"CLASSIC\"] = \"classic\";\n    DurakVariant[\"THROWING\"] = \"throwing\";\n    DurakVariant[\"TRANSFERABLE\"] = \"transferable\";\n    DurakVariant[\"TEAM\"] = \"team\";\n})(DurakVariant || (exports.DurakVariant = DurakVariant = {}));\n// Статус игры\nvar GameStatus;\n(function (GameStatus) {\n    GameStatus[\"NOT_STARTED\"] = \"not_started\";\n    GameStatus[\"IN_PROGRESS\"] = \"in_progress\";\n    GameStatus[\"FINISHED\"] = \"finished\";\n})(GameStatus || (exports.GameStatus = GameStatus = {}));\n// Действия игрока\nvar PlayerAction;\n(function (PlayerAction) {\n    PlayerAction[\"ATTACK\"] = \"attack\";\n    PlayerAction[\"DEFEND\"] = \"defend\";\n    PlayerAction[\"TAKE\"] = \"take\";\n    PlayerAction[\"PASS\"] = \"pass\";\n})(PlayerAction || (exports.PlayerAction = PlayerAction = {}));\n// События игры\nvar GameEvent;\n(function (GameEvent) {\n    GameEvent[\"GAME_STARTED\"] = \"game_started\";\n    GameEvent[\"GAME_ENDED\"] = \"game_ended\";\n    GameEvent[\"PLAYER_MOVED\"] = \"player_moved\";\n    GameEvent[\"TURN_CHANGED\"] = \"turn_changed\";\n    GameEvent[\"CARDS_DEALT\"] = \"cards_dealt\";\n    GameEvent[\"ROUND_ENDED\"] = \"round_ended\";\n})(GameEvent || (exports.GameEvent = GameEvent = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/core/dist/types.js\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "styled-components":
/*!************************************!*\
  !*** external "styled-components" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("styled-components");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "@reduxjs/toolkit":
/*!***********************************!*\
  !*** external "@reduxjs/toolkit" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = import("@reduxjs/toolkit");;

/***/ }),

/***/ "framer-motion":
/*!********************************!*\
  !*** external "framer-motion" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("framer-motion");;

/***/ }),

/***/ "react-redux":
/*!******************************!*\
  !*** external "react-redux" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-redux");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdurak&preferredRegion=&absolutePagePath=.%2Fpages%2Fdurak.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();