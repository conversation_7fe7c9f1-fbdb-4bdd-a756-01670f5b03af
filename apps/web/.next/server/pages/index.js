/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"../../node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/index.tsx */ \"./src/pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__]);\nprivate_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/app/store/index.ts":
/*!********************************!*\
  !*** ./src/app/store/index.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   store: () => (/* binding */ store)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"@reduxjs/toolkit\");\n/* harmony import */ var _entities_game_model_durakSlice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @entities/game/model/durakSlice */ \"./src/entities/game/model/durakSlice.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__, _entities_game_model_durakSlice__WEBPACK_IMPORTED_MODULE_1__]);\n([_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__, _entities_game_model_durakSlice__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n// Импорт редьюсеров\n// import { authReducer } from '@features/auth/model/slice';\n// import { gameReducer } from \"@entities/game/model/slice\"; // Removed old game slice\n // Import the new Durak slice\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.configureStore)({\n    reducer: {\n        // auth: authReducer,\n        // game: gameReducer, // Removed old game slice\n        durak: _entities_game_model_durakSlice__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n    },\n    middleware: (getDefaultMiddleware)=>getDefaultMiddleware({\n            serializableCheck: false\n        })\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvYXBwL3N0b3JlL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFrRDtBQUVsRCxvQkFBb0I7QUFDcEIsNERBQTREO0FBQzVELHNGQUFzRjtBQUMzQixDQUFDLDZCQUE2QjtBQUVsRixNQUFNRSxRQUFRRixnRUFBY0EsQ0FBQztJQUNsQ0csU0FBUztRQUNQLHFCQUFxQjtRQUNyQiwrQ0FBK0M7UUFDL0NDLE9BQU9ILHVFQUFZQTtJQUVyQjtJQUNBSSxZQUFZLENBQUNDLHVCQUNYQSxxQkFBcUI7WUFDbkJDLG1CQUFtQjtRQUNyQjtBQUNKLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb3p5ci1tYXN0ZXItd2ViLy4vc3JjL2FwcC9zdG9yZS9pbmRleC50cz85Y2UxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNvbmZpZ3VyZVN0b3JlIH0gZnJvbSBcIkByZWR1eGpzL3Rvb2xraXRcIjtcblxuLy8g0JjQvNC/0L7RgNGCINGA0LXQtNGM0Y7RgdC10YDQvtCyXG4vLyBpbXBvcnQgeyBhdXRoUmVkdWNlciB9IGZyb20gJ0BmZWF0dXJlcy9hdXRoL21vZGVsL3NsaWNlJztcbi8vIGltcG9ydCB7IGdhbWVSZWR1Y2VyIH0gZnJvbSBcIkBlbnRpdGllcy9nYW1lL21vZGVsL3NsaWNlXCI7IC8vIFJlbW92ZWQgb2xkIGdhbWUgc2xpY2VcbmltcG9ydCBkdXJha1JlZHVjZXIgZnJvbSAnQGVudGl0aWVzL2dhbWUvbW9kZWwvZHVyYWtTbGljZSc7IC8vIEltcG9ydCB0aGUgbmV3IER1cmFrIHNsaWNlXG5cbmV4cG9ydCBjb25zdCBzdG9yZSA9IGNvbmZpZ3VyZVN0b3JlKHtcbiAgcmVkdWNlcjoge1xuICAgIC8vIGF1dGg6IGF1dGhSZWR1Y2VyLFxuICAgIC8vIGdhbWU6IGdhbWVSZWR1Y2VyLCAvLyBSZW1vdmVkIG9sZCBnYW1lIHNsaWNlXG4gICAgZHVyYWs6IGR1cmFrUmVkdWNlciwgLy8gQWRkIHRoZSBEdXJhayBnYW1lIHNsaWNlXG4gICAgLy8g0JfQtNC10YHRjCDQsdGD0LTRg9GCINC00L7QsdCw0LLQu9C10L3RiyDQtNGA0YPQs9C40LUg0YDQtdC00YzRjtGB0LXRgNGLXG4gIH0sXG4gIG1pZGRsZXdhcmU6IChnZXREZWZhdWx0TWlkZGxld2FyZSkgPT5cbiAgICBnZXREZWZhdWx0TWlkZGxld2FyZSh7XG4gICAgICBzZXJpYWxpemFibGVDaGVjazogZmFsc2UsXG4gICAgfSksXG59KTtcblxuZXhwb3J0IHR5cGUgUm9vdFN0YXRlID0gUmV0dXJuVHlwZTx0eXBlb2Ygc3RvcmUuZ2V0U3RhdGU+O1xuZXhwb3J0IHR5cGUgQXBwRGlzcGF0Y2ggPSB0eXBlb2Ygc3RvcmUuZGlzcGF0Y2g7XG4iXSwibmFtZXMiOlsiY29uZmlndXJlU3RvcmUiLCJkdXJha1JlZHVjZXIiLCJzdG9yZSIsInJlZHVjZXIiLCJkdXJhayIsIm1pZGRsZXdhcmUiLCJnZXREZWZhdWx0TWlkZGxld2FyZSIsInNlcmlhbGl6YWJsZUNoZWNrIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/app/store/index.ts\n");

/***/ }),

/***/ "./src/entities/game/model/durakSlice.ts":
/*!***********************************************!*\
  !*** ./src/entities/game/model/durakSlice.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initializeGame: () => (/* binding */ initializeGame),\n/* harmony export */   makeMove: () => (/* binding */ makeMove),\n/* harmony export */   setError: () => (/* binding */ setError),\n/* harmony export */   startGame: () => (/* binding */ startGame)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"@reduxjs/toolkit\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @kozyr-master/core */ \"../../packages/core/dist/index.js\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__]);\n_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n // Импортируем типы из пакета core\n// Начальное состояние для среза\nconst initialState = {\n    players: [],\n    deck: [],\n    tableCards: [],\n    discardPile: [],\n    trumpCard: undefined,\n    trumpSuit: null,\n    currentPlayerIndex: -1,\n    attackerIndex: -1,\n    defenderIndex: -1,\n    gameStatus: _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.GameStatus.NOT_STARTED,\n    winner: undefined,\n    passCount: 0,\n    gameInstance: null,\n    error: null\n};\nconst durakSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: \"durak\",\n    initialState,\n    reducers: {\n        // Редьюсер для инициализации новой игры\n        initializeGame: (state, action)=>{\n            try {\n                const game = new _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakGame(action.payload.players, action.payload.rules);\n                const gameState = game.getState();\n                // Обновляем состояние Redux из состояния игры\n                Object.assign(state, gameState);\n                state.gameInstance = game; // Сохраняем экземпляр игры\n                state.gameStatus = _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.GameStatus.NOT_STARTED; // Устанавливаем статус\n                state.error = null;\n            } catch (e) {\n                state.error = e.message || \"Failed to initialize game\";\n                console.error(\"Error initializing game:\", e);\n            }\n        },\n        // Редьюсер для старта игры\n        startGame: (state)=>{\n            if (state.gameInstance && state.gameStatus === _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.GameStatus.NOT_STARTED) {\n                try {\n                    state.gameInstance.startGame();\n                    const newState = state.gameInstance.getState();\n                    Object.assign(state, newState);\n                    state.error = null;\n                } catch (e) {\n                    state.error = e.message || \"Failed to start game\";\n                    console.error(\"Error starting game:\", e);\n                }\n            } else {\n                state.error = \"Game instance not available or game already started/finished.\";\n            }\n        },\n        // Редьюсер для выполнения хода\n        makeMove: (state, action)=>{\n            if (state.gameInstance && state.gameStatus === _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.GameStatus.IN_PROGRESS) {\n                try {\n                    const { playerId, action: playerAction, cardIndex } = action.payload;\n                    const moveSuccessful = state.gameInstance.makeMove(playerId, playerAction, cardIndex);\n                    if (moveSuccessful) {\n                        const newState = state.gameInstance.getState();\n                        Object.assign(state, newState);\n                        state.error = null;\n                    } else {\n                        // Можно установить ошибку, если ход не удался, но DurakGame уже логирует ошибки\n                        // state.error = 'Invalid move';\n                        console.warn(\"Move was not successful according to game logic.\");\n                    }\n                } catch (e) {\n                    state.error = e.message || \"Failed to make move\";\n                    console.error(\"Error making move:\", e);\n                }\n            } else {\n                state.error = \"Game instance not available or game not in progress.\";\n            }\n        },\n        // Можно добавить другие редьюсеры по мере необходимости\n        setError: (state, action)=>{\n            state.error = action.payload;\n        }\n    }\n});\nconst { initializeGame, startGame, makeMove, setError } = durakSlice.actions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (durakSlice.reducer);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/entities/game/model/durakSlice.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-redux */ \"react-redux\");\n/* harmony import */ var _app_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @app/store */ \"./src/app/store/index.ts\");\n/* harmony import */ var _app_styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @app/styles/globals.css */ \"./src/app/styles/globals.css\");\n/* harmony import */ var _app_styles_globals_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_app_styles_globals_css__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_redux__WEBPACK_IMPORTED_MODULE_1__, _app_store__WEBPACK_IMPORTED_MODULE_2__]);\n([react_redux__WEBPACK_IMPORTED_MODULE_1__, _app_store__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_1__.Provider, {\n        store: _app_store__WEBPACK_IMPORTED_MODULE_2__.store,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/_app.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFDdUM7QUFDSjtBQUNGO0FBRWpDLFNBQVNFLE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDL0MscUJBQ0UsOERBQUNKLGlEQUFRQTtRQUFDQyxPQUFPQSw2Q0FBS0E7a0JBQ3BCLDRFQUFDRTtZQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7O0FBRzlCO0FBRUEsaUVBQWVGLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb3p5ci1tYXN0ZXItd2ViLy4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUHJvcHMgfSBmcm9tIFwibmV4dC9hcHBcIjtcbmltcG9ydCB7IFByb3ZpZGVyIH0gZnJvbSBcInJlYWN0LXJlZHV4XCI7XG5pbXBvcnQgeyBzdG9yZSB9IGZyb20gXCJAYXBwL3N0b3JlXCI7XG5pbXBvcnQgXCJAYXBwL3N0eWxlcy9nbG9iYWxzLmNzc1wiO1xuXG5mdW5jdGlvbiBNeUFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH06IEFwcFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFByb3ZpZGVyIHN0b3JlPXtzdG9yZX0+XG4gICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG4gICAgPC9Qcm92aWRlcj5cbiAgKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgTXlBcHA7XG4iXSwibmFtZXMiOlsiUHJvdmlkZXIiLCJzdG9yZSIsIk15QXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst HomePage = ()=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleStartPlaying = ()=>{\n        router.push(\"/games/durak\");\n    };\n    const handleMultiplayer = ()=>{\n        router.push(\"/multiplayer\");\n    };\n    const handleViewTournaments = ()=>{\n        router.push(\"/tournaments\");\n    };\n    const handleViewTutorials = ()=>{\n        router.push(\"/tutorials\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Козырь Мастер - Карточные игры онлайн\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Играйте в популярные карточные игры онлайн с друзьями\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Main, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Hero, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                children: \"Добро пожаловать в Козырь Мастер\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Subtitle, {\n                                children: \"Платформа для игры в популярные карточные игры онлайн\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContainer, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDFAE\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Одиночная игра\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Играйте против бота и оттачивайте свои навыки\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StartButton, {\n                                        onClick: handleStartPlaying,\n                                        children: \"Играть против бота\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDF10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Многопользовательская игра\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Играйте с другими игроками онлайн в реальном времени\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MultiplayerButton, {\n                                        onClick: handleMultiplayer,\n                                        children: \"Играть онлайн\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83C\\uDFC6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Турниры\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Участвуйте в турнирах и выигрывайте призы\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewTournaments,\n                                        children: \"Смотреть турниры\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardIcon, {\n                                        children: \"\\uD83D\\uDCDA\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        children: \"Обучение\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                                        children: \"Изучите правила игр и стратегии\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                        onClick: handleViewTutorials,\n                                        children: \"Изучить правила\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"\\xa9 2023 Козырь Мастер. Все права защищены.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/pages/index.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n// Стилизованные компоненты\nconst Container = styled_components__WEBPACK_IMPORTED_MODULE_2___default().div.withConfig({\n    displayName: \"pages__Container\",\n    componentId: \"sc-1e819ad2-0\"\n})([\n    \"min-height:100vh;display:flex;flex-direction:column;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);\"\n]);\nconst Main = styled_components__WEBPACK_IMPORTED_MODULE_2___default().main.withConfig({\n    displayName: \"pages__Main\",\n    componentId: \"sc-1e819ad2-1\"\n})([\n    \"flex:1;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:2rem;\"\n]);\nconst Hero = styled_components__WEBPACK_IMPORTED_MODULE_2___default().div.withConfig({\n    displayName: \"pages__Hero\",\n    componentId: \"sc-1e819ad2-2\"\n})([\n    \"text-align:center;margin-bottom:4rem;\"\n]);\nconst Title = styled_components__WEBPACK_IMPORTED_MODULE_2___default().h1.withConfig({\n    displayName: \"pages__Title\",\n    componentId: \"sc-1e819ad2-3\"\n})([\n    \"font-size:3.5rem;margin-bottom:1rem;color:white;text-shadow:2px 2px 4px rgba(0,0,0,0.3);font-weight:700;@media (max-width:768px){font-size:2.5rem;}\"\n]);\nconst Subtitle = styled_components__WEBPACK_IMPORTED_MODULE_2___default().p.withConfig({\n    displayName: \"pages__Subtitle\",\n    componentId: \"sc-1e819ad2-4\"\n})([\n    \"font-size:1.5rem;color:rgba(255,255,255,0.9);text-shadow:1px 1px 2px rgba(0,0,0,0.3);max-width:600px;margin:0 auto;@media (max-width:768px){font-size:1.2rem;}\"\n]);\nconst CardContainer = styled_components__WEBPACK_IMPORTED_MODULE_2___default().div.withConfig({\n    displayName: \"pages__CardContainer\",\n    componentId: \"sc-1e819ad2-5\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:2rem;max-width:1200px;width:100%;\"\n]);\nconst GameCard = styled_components__WEBPACK_IMPORTED_MODULE_2___default().div.withConfig({\n    displayName: \"pages__GameCard\",\n    componentId: \"sc-1e819ad2-6\"\n})([\n    \"background:rgba(255,255,255,0.95);border-radius:16px;box-shadow:0 8px 32px rgba(0,0,0,0.1);padding:2rem;text-align:center;transition:all 0.3s ease;backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,0.2);&:hover{transform:translateY(-8px);box-shadow:0 16px 48px rgba(0,0,0,0.2);}\"\n]);\nconst CardIcon = styled_components__WEBPACK_IMPORTED_MODULE_2___default().div.withConfig({\n    displayName: \"pages__CardIcon\",\n    componentId: \"sc-1e819ad2-7\"\n})([\n    \"font-size:3rem;margin-bottom:1rem;\"\n]);\nconst CardTitle = styled_components__WEBPACK_IMPORTED_MODULE_2___default().h2.withConfig({\n    displayName: \"pages__CardTitle\",\n    componentId: \"sc-1e819ad2-8\"\n})([\n    \"font-size:1.8rem;margin-bottom:1rem;color:#333;font-weight:600;\"\n]);\nconst CardDescription = styled_components__WEBPACK_IMPORTED_MODULE_2___default().p.withConfig({\n    displayName: \"pages__CardDescription\",\n    componentId: \"sc-1e819ad2-9\"\n})([\n    \"font-size:1rem;color:#666;margin-bottom:2rem;line-height:1.6;\"\n]);\nconst StartButton = styled_components__WEBPACK_IMPORTED_MODULE_2___default().button.withConfig({\n    displayName: \"pages__StartButton\",\n    componentId: \"sc-1e819ad2-10\"\n})([\n    \"background:linear-gradient(135deg,#4CAF50,#45a049);color:white;border:none;border-radius:8px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(76,175,80,0.3);&:hover{background:linear-gradient(135deg,#45a049,#4CAF50);transform:translateY(-2px);box-shadow:0 6px 20px rgba(76,175,80,0.4);}&:active{transform:translateY(0);}\"\n]);\nconst MultiplayerButton = styled_components__WEBPACK_IMPORTED_MODULE_2___default().button.withConfig({\n    displayName: \"pages__MultiplayerButton\",\n    componentId: \"sc-1e819ad2-11\"\n})([\n    \"background:linear-gradient(135deg,#FF6B35,#F7931E);color:white;border:none;border-radius:8px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(255,107,53,0.3);&:hover{background:linear-gradient(135deg,#F7931E,#FF6B35);transform:translateY(-2px);box-shadow:0 6px 20px rgba(255,107,53,0.4);}&:active{transform:translateY(0);}\"\n]);\nconst ActionButton = styled_components__WEBPACK_IMPORTED_MODULE_2___default().button.withConfig({\n    displayName: \"pages__ActionButton\",\n    componentId: \"sc-1e819ad2-12\"\n})([\n    \"background:linear-gradient(135deg,#2196F3,#1976D2);color:white;border:none;border-radius:8px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 16px rgba(33,150,243,0.3);&:hover{background:linear-gradient(135deg,#1976D2,#2196F3);transform:translateY(-2px);box-shadow:0 6px 20px rgba(33,150,243,0.4);}&:active{transform:translateY(0);}\"\n]);\nconst Footer = styled_components__WEBPACK_IMPORTED_MODULE_2___default().footer.withConfig({\n    displayName: \"pages__Footer\",\n    componentId: \"sc-1e819ad2-13\"\n})([\n    \"width:100%;padding:1.5rem;text-align:center;background:rgba(0,0,0,0.1);color:rgba(255,255,255,0.8);backdrop-filter:blur(10px);\"\n]);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n");

/***/ }),

/***/ "./src/app/styles/globals.css":
/*!************************************!*\
  !*** ./src/app/styles/globals.css ***!
  \************************************/
/***/ (() => {



/***/ }),

/***/ "../../packages/core/dist/durak/bot.js":
/*!*********************************************!*\
  !*** ../../packages/core/dist/durak/bot.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n/**\n * Система ботов для игры \"Дурак\"\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BotFactory = exports.DurakBot = exports.BotDifficulty = void 0;\nconst types_1 = __webpack_require__(/*! ../types */ \"../../packages/core/dist/types.js\");\n/**\n * Уровни сложности бота\n */\nvar BotDifficulty;\n(function (BotDifficulty) {\n    BotDifficulty[\"EASY\"] = \"easy\";\n    BotDifficulty[\"MEDIUM\"] = \"medium\";\n    BotDifficulty[\"HARD\"] = \"hard\";\n})(BotDifficulty || (exports.BotDifficulty = BotDifficulty = {}));\n/**\n * Базовый класс бота для игры \"Дурак\"\n */\nclass DurakBot {\n    constructor(id, difficulty = BotDifficulty.MEDIUM) {\n        this.id = id;\n        this.difficulty = difficulty;\n        this.name = `Bot_${difficulty}_${id}`;\n    }\n    /**\n     * Создать игрока-бота\n     */\n    createPlayer() {\n        return {\n            id: this.id,\n            name: this.name,\n            hand: [],\n            isActive: false,\n        };\n    }\n    /**\n     * Принять решение о следующем ходе\n     */\n    makeDecision(gameState, playerId) {\n        const player = gameState.players.find(p => p.id === playerId);\n        if (!player) {\n            throw new Error(`Player ${playerId} not found`);\n        }\n        const isAttacker = gameState.attackerIndex === gameState.players.indexOf(player);\n        const isDefender = gameState.defenderIndex === gameState.players.indexOf(player);\n        // Определяем возможные действия\n        if (isAttacker && gameState.tableCards.length === 0) {\n            // Атакующий должен атаковать\n            return this.decideAttack(gameState, player);\n        }\n        else if (isDefender && this.hasUndefendedCards(gameState)) {\n            // Защитник должен защищаться или брать карты\n            return this.decideDefendOrTake(gameState, player);\n        }\n        else if (isAttacker && this.allCardsDefended(gameState)) {\n            // Атакующий может подкинуть карты или сказать \"бито\"\n            return this.decideThrowOrPass(gameState, player);\n        }\n        else if (!isDefender && this.allCardsDefended(gameState)) {\n            // Другие игроки могут подкинуть карты\n            return this.decideThrow(gameState, player);\n        }\n        // По умолчанию - пас\n        return {\n            action: types_1.PlayerAction.PASS,\n            reasoning: \"Default pass action\",\n        };\n    }\n    /**\n     * Решение об атаке\n     */\n    decideAttack(gameState, player) {\n        const validCards = this.getValidAttackCards(gameState, player);\n        if (validCards.length === 0) {\n            return {\n                action: types_1.PlayerAction.PASS,\n                reasoning: \"No valid attack cards\",\n            };\n        }\n        // Выбираем карту в зависимости от сложности\n        let cardIndex;\n        switch (this.difficulty) {\n            case BotDifficulty.EASY:\n                // Легкий бот играет случайно\n                cardIndex = validCards[Math.floor(Math.random() * validCards.length)];\n                break;\n            case BotDifficulty.MEDIUM:\n                // Средний бот предпочитает младшие карты\n                cardIndex = this.selectLowestCard(player, validCards, gameState.trumpSuit);\n                break;\n            case BotDifficulty.HARD:\n                // Сложный бот использует стратегию\n                cardIndex = this.selectStrategicAttackCard(player, validCards, gameState);\n                break;\n            default:\n                cardIndex = validCards[0];\n        }\n        return {\n            action: types_1.PlayerAction.ATTACK,\n            cardIndex,\n            reasoning: `Attack with card at index ${cardIndex}`,\n        };\n    }\n    /**\n     * Решение о защите или взятии карт\n     */\n    decideDefendOrTake(gameState, player) {\n        const lastPair = gameState.tableCards.at(-1);\n        if (!lastPair || lastPair.length !== 1) {\n            return {\n                action: types_1.PlayerAction.TAKE,\n                reasoning: \"No card to defend against\",\n            };\n        }\n        const attackingCard = lastPair[0];\n        const validDefenseCards = this.getValidDefenseCards(player, attackingCard, gameState.trumpSuit);\n        if (validDefenseCards.length === 0) {\n            return {\n                action: types_1.PlayerAction.TAKE,\n                reasoning: \"No valid defense cards\",\n            };\n        }\n        // Решаем, защищаться или брать карты\n        const shouldDefend = this.shouldDefend(gameState, player, validDefenseCards);\n        if (shouldDefend) {\n            const cardIndex = this.selectDefenseCard(validDefenseCards, attackingCard, gameState.trumpSuit);\n            return {\n                action: types_1.PlayerAction.DEFEND,\n                cardIndex,\n                reasoning: `Defend with card at index ${cardIndex}`,\n            };\n        }\n        else {\n            return {\n                action: types_1.PlayerAction.TAKE,\n                reasoning: \"Decided to take cards instead of defending\",\n            };\n        }\n    }\n    /**\n     * Решение о подкидывании или пасе\n     */\n    decideThrowOrPass(gameState, player) {\n        const throwCards = this.getValidThrowCards(gameState, player);\n        if (throwCards.length === 0 || !this.shouldThrow(gameState, player)) {\n            return {\n                action: types_1.PlayerAction.PASS,\n                reasoning: \"No valid throw cards or decided not to throw\",\n            };\n        }\n        const cardIndex = throwCards[0]; // Простая стратегия - первая подходящая карта\n        return {\n            action: types_1.PlayerAction.ATTACK,\n            cardIndex,\n            reasoning: `Throw card at index ${cardIndex}`,\n        };\n    }\n    /**\n     * Решение о подкидывании (для не-атакующих игроков)\n     */\n    decideThrow(gameState, player) {\n        // Пока что не-атакующие игроки не подкидывают (упрощение)\n        return {\n            action: types_1.PlayerAction.PASS,\n            reasoning: \"Non-attacker decided not to throw\",\n        };\n    }\n    // Вспомогательные методы\n    hasUndefendedCards(gameState) {\n        return gameState.tableCards.some(pair => pair.length === 1);\n    }\n    allCardsDefended(gameState) {\n        return gameState.tableCards.length > 0 && gameState.tableCards.every(pair => pair.length === 2);\n    }\n    getValidAttackCards(gameState, player) {\n        if (gameState.tableCards.length === 0) {\n            // Первая атака - любая карта\n            return player.hand.map((_, index) => index);\n        }\n        // Подкидывание - карты с рангами, уже лежащими на столе\n        const ranksOnTable = new Set(gameState.tableCards.flat().map(card => card.rank));\n        return player.hand\n            .map((card, index) => ({ card, index }))\n            .filter(({ card }) => ranksOnTable.has(card.rank))\n            .map(({ index }) => index);\n    }\n    getValidDefenseCards(player, attackingCard, trumpSuit) {\n        return player.hand\n            .map((card, index) => ({ card, index }))\n            .filter(({ card }) => this.canDefend(attackingCard, card, trumpSuit))\n            .map(({ index }) => index);\n    }\n    canDefend(attackCard, defendCard, trumpSuit) {\n        const getRankValue = (rank) => {\n            const values = {\n                [types_1.CardRank.SIX]: 6,\n                [types_1.CardRank.SEVEN]: 7,\n                [types_1.CardRank.EIGHT]: 8,\n                [types_1.CardRank.NINE]: 9,\n                [types_1.CardRank.TEN]: 10,\n                [types_1.CardRank.JACK]: 11,\n                [types_1.CardRank.QUEEN]: 12,\n                [types_1.CardRank.KING]: 13,\n                [types_1.CardRank.ACE]: 14,\n            };\n            return values[rank];\n        };\n        // Карта той же масти, но старше\n        if (attackCard.suit === defendCard.suit && getRankValue(defendCard.rank) > getRankValue(attackCard.rank)) {\n            return true;\n        }\n        // Козырь бьет не-козырь\n        if (defendCard.suit === trumpSuit && attackCard.suit !== trumpSuit) {\n            return true;\n        }\n        // Козырь бьет козырь, если старше\n        if (attackCard.suit === trumpSuit && defendCard.suit === trumpSuit &&\n            getRankValue(defendCard.rank) > getRankValue(attackCard.rank)) {\n            return true;\n        }\n        return false;\n    }\n    getValidThrowCards(gameState, player) {\n        const ranksOnTable = new Set(gameState.tableCards.flat().map(card => card.rank));\n        return player.hand\n            .map((card, index) => ({ card, index }))\n            .filter(({ card }) => ranksOnTable.has(card.rank))\n            .map(({ index }) => index);\n    }\n    shouldDefend(gameState, player, validDefenseCards) {\n        switch (this.difficulty) {\n            case BotDifficulty.EASY:\n                return Math.random() > 0.3; // 70% шанс защищаться\n            case BotDifficulty.MEDIUM:\n                // Защищается, если у него мало карт или много карт на столе\n                return player.hand.length <= 3 || gameState.tableCards.length >= 3;\n            case BotDifficulty.HARD:\n                // Более сложная логика\n                return this.strategicDefendDecision(gameState, player, validDefenseCards);\n            default:\n                return true;\n        }\n    }\n    shouldThrow(gameState, player) {\n        switch (this.difficulty) {\n            case BotDifficulty.EASY:\n                return Math.random() > 0.7; // 30% шанс подкинуть\n            case BotDifficulty.MEDIUM:\n                return player.hand.length > 5; // Подкидывает, если много карт\n            case BotDifficulty.HARD:\n                return this.strategicThrowDecision(gameState, player);\n            default:\n                return false;\n        }\n    }\n    selectLowestCard(player, validIndices, trumpSuit) {\n        const getRankValue = (rank) => {\n            const values = {\n                [types_1.CardRank.SIX]: 6, [types_1.CardRank.SEVEN]: 7, [types_1.CardRank.EIGHT]: 8, [types_1.CardRank.NINE]: 9,\n                [types_1.CardRank.TEN]: 10, [types_1.CardRank.JACK]: 11, [types_1.CardRank.QUEEN]: 12, [types_1.CardRank.KING]: 13, [types_1.CardRank.ACE]: 14,\n            };\n            return values[rank];\n        };\n        return validIndices.reduce((lowestIndex, currentIndex) => {\n            const lowestCard = player.hand[lowestIndex];\n            const currentCard = player.hand[currentIndex];\n            // Предпочитаем не-козыри\n            if (lowestCard.suit === trumpSuit && currentCard.suit !== trumpSuit) {\n                return currentIndex;\n            }\n            if (lowestCard.suit !== trumpSuit && currentCard.suit === trumpSuit) {\n                return lowestIndex;\n            }\n            // Если обе карты одного типа (козыри или не-козыри), выбираем младшую\n            return getRankValue(currentCard.rank) < getRankValue(lowestCard.rank) ? currentIndex : lowestIndex;\n        });\n    }\n    selectStrategicAttackCard(player, validIndices, gameState) {\n        // Пока что используем простую стратегию - младшая карта\n        return this.selectLowestCard(player, validIndices, gameState.trumpSuit);\n    }\n    selectDefenseCard(validIndices, attackingCard, trumpSuit) {\n        // Выбираем первую подходящую карту (можно улучшить)\n        return validIndices[0];\n    }\n    strategicDefendDecision(gameState, player, validDefenseCards) {\n        // Упрощенная стратегическая логика\n        const cardsOnTable = gameState.tableCards.flat().length;\n        const playerCardCount = player.hand.length;\n        // Не защищается, если на столе много карт и у игрока мало карт\n        if (cardsOnTable >= 6 && playerCardCount <= 4) {\n            return false;\n        }\n        return true;\n    }\n    strategicThrowDecision(gameState, player) {\n        // Подкидывает, если у защитника много карт\n        const defender = gameState.players[gameState.defenderIndex];\n        return defender.hand.length > 6;\n    }\n}\nexports.DurakBot = DurakBot;\n/**\n * Фабрика для создания ботов\n */\nclass BotFactory {\n    /**\n     * Создать бота с указанной сложностью\n     */\n    static createBot(difficulty = BotDifficulty.MEDIUM) {\n        const id = `bot_${++this.botCounter}`;\n        return new DurakBot(id, difficulty);\n    }\n    /**\n     * Создать несколько ботов\n     */\n    static createBots(count, difficulty = BotDifficulty.MEDIUM) {\n        return Array.from({ length: count }, () => this.createBot(difficulty));\n    }\n}\nexports.BotFactory = BotFactory;\nBotFactory.botCounter = 0;\n//# sourceMappingURL=bot.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/core/dist/durak/bot.js\n");

/***/ }),

/***/ "../../packages/core/dist/durak/index.js":
/*!***********************************************!*\
  !*** ../../packages/core/dist/durak/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n/**\n * Модуль игры \"Дурак\"\n *\n * Содержит основную логику и правила игры \"Дурак\"\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DurakGame = void 0;\n// Импортируем общие типы\nconst types_1 = __webpack_require__(/*! ../types */ \"../../packages/core/dist/types.js\");\n/**\n * Класс игры \"Дурак\"\n */\nclass DurakGame {\n    constructor(players, rules) {\n        this.eventHandlers = [];\n        this.rules = rules;\n        this.state = this.initializeGame(players);\n    }\n    /**\n     * Добавить обработчик событий\n     */\n    addEventListener(handler) {\n        this.eventHandlers.push(handler);\n    }\n    /**\n     * Удалить обработчик событий\n     */\n    removeEventListener(handler) {\n        const index = this.eventHandlers.indexOf(handler);\n        if (index > -1) {\n            this.eventHandlers.splice(index, 1);\n        }\n    }\n    /**\n     * Отправить событие всем обработчикам\n     */\n    emitEvent(eventData) {\n        this.eventHandlers.forEach(handler => {\n            try {\n                handler(eventData);\n            }\n            catch (error) {\n                console.error('Error in game event handler:', error);\n            }\n        });\n    }\n    /**\n     * Инициализация игры\n     */\n    initializeGame(players) {\n        // Создание и перемешивание колоды\n        const deck = this.createDeck();\n        this.shuffleDeck(deck);\n        // Определение козырной карты\n        const trumpCard = deck[deck.length - 1];\n        const trumpSuit = trumpCard.suit;\n        // Раздача карт игрокам\n        this.dealCards(players, deck);\n        // Определение первого игрока (у кого наименьший козырь)\n        const firstPlayerIndex = this.determineFirstPlayer(players, trumpSuit);\n        return {\n            players,\n            deck,\n            tableCards: [],\n            discardPile: [],\n            trumpCard,\n            trumpSuit,\n            currentPlayerIndex: firstPlayerIndex,\n            attackerIndex: firstPlayerIndex,\n            defenderIndex: (firstPlayerIndex + 1) % players.length,\n            gameStatus: types_1.GameStatus.NOT_STARTED,\n        };\n    }\n    /**\n     * Создание колоды карт\n     */\n    createDeck() {\n        const deck = [];\n        const suits = Object.values(types_1.CardSuit);\n        const ranks = Object.values(types_1.CardRank);\n        for (const suit of suits) {\n            for (const rank of ranks) {\n                deck.push({ suit, rank });\n            }\n        }\n        return deck;\n    }\n    /**\n     * Перемешивание колоды\n     */\n    shuffleDeck(deck) {\n        for (let i = deck.length - 1; i > 0; i--) {\n            const j = Math.floor(Math.random() * (i + 1));\n            [deck[i], deck[j]] = [deck[j], deck[i]];\n        }\n    }\n    /**\n     * Раздача карт игрокам\n     */\n    dealCards(players, deck) {\n        const cardsPerPlayer = this.rules.initialHandSize;\n        for (let i = 0; i < cardsPerPlayer; i++) {\n            for (const player of players) {\n                if (deck.length > 0) {\n                    const card = deck.shift();\n                    if (card) {\n                        player.hand.push(card);\n                    }\n                }\n            }\n        }\n    }\n    /**\n     * Определение первого игрока\n     */\n    determineFirstPlayer(players, trumpSuit) {\n        let minTrumpRankIndex = -1;\n        let minTrumpRankValue = Infinity;\n        // Поиск игрока с наименьшим козырем\n        for (let i = 0; i < players.length; i++) {\n            const player = players[i];\n            for (const card of player.hand) {\n                if (card.suit === trumpSuit) {\n                    const rankValue = this.getRankValue(card.rank);\n                    if (rankValue < minTrumpRankValue) {\n                        minTrumpRankValue = rankValue;\n                        minTrumpRankIndex = i;\n                    }\n                }\n            }\n        }\n        // Если ни у кого нет козырей, выбираем случайного игрока\n        if (minTrumpRankIndex === -1) {\n            minTrumpRankIndex = Math.floor(Math.random() * players.length);\n        }\n        return minTrumpRankIndex;\n    }\n    /**\n     * Получение числового значения ранга карты\n     */\n    getRankValue(rank) {\n        const rankValues = {\n            [types_1.CardRank.SIX]: 6,\n            [types_1.CardRank.SEVEN]: 7,\n            [types_1.CardRank.EIGHT]: 8,\n            [types_1.CardRank.NINE]: 9,\n            [types_1.CardRank.TEN]: 10,\n            [types_1.CardRank.JACK]: 11,\n            [types_1.CardRank.QUEEN]: 12,\n            [types_1.CardRank.KING]: 13,\n            [types_1.CardRank.ACE]: 14,\n        };\n        return rankValues[rank];\n    }\n    /**\n     * Получение текущего состояния игры\n     */\n    getState() {\n        return { ...this.state };\n    }\n    /**\n     * Обновление статуса активного игрока\n     */\n    updateActivePlayer() {\n        this.state.players.forEach((player, index) => {\n            player.isActive = index === this.state.currentPlayerIndex;\n        });\n    }\n    /**\n     * Начало игры\n     */\n    startGame() {\n        if (this.state.gameStatus === types_1.GameStatus.NOT_STARTED) {\n            this.state.gameStatus = types_1.GameStatus.IN_PROGRESS;\n            this.updateActivePlayer(); // Устанавливаем первого активного игрока\n            // Отправляем событие о начале игры\n            this.emitEvent({\n                type: types_1.GameEvent.GAME_STARTED,\n                gameState: this.getState(),\n                message: `Game started. First turn: Player ${this.state.players[this.state.currentPlayerIndex].id}`,\n            });\n            console.log(`Game started. First turn: Player ${this.state.players[this.state.currentPlayerIndex].id}`);\n        }\n        else {\n            console.warn(\"Game already started or finished.\");\n        }\n    }\n    /**\n     * Проверка, разрешено ли действие игроку в текущем состоянии игры.\n     * @param playerIndex Индекс игрока, выполняющего действие.\n     * @param action Тип действия (атака, защита, пас, взять).\n     * @returns Объект с флагом `allowed` (true/false) и опциональным сообщением об ошибке `error`.\n     */\n    _isActionAllowed(playerIndex, action) {\n        const isCurrentPlayerTurn = playerIndex === this.state.currentPlayerIndex;\n        const isAttacker = playerIndex === this.state.attackerIndex;\n        const isDefender = playerIndex === this.state.defenderIndex;\n        const lastPair = this.state.tableCards.at(-1);\n        const lastPairDefended = !!lastPair && lastPair.length === 2;\n        const tableIsEmpty = this.state.tableCards.length === 0;\n        // 1. Ход текущего игрока (атакующий или защитник)\n        if (isCurrentPlayerTurn) {\n            // Атакующий может атаковать, если стол пуст или после взятия карт защитником\n            if (isAttacker &&\n                action === types_1.PlayerAction.ATTACK &&\n                (tableIsEmpty || this.state.defenderTookCards)) {\n                return { allowed: true };\n            }\n            // Атакующий может пасовать (бито), если защита была успешной\n            else if (isAttacker && action === types_1.PlayerAction.PASS && lastPairDefended) {\n                return { allowed: true };\n            }\n            // Защитник может защищаться или взять карты\n            else if (isDefender &&\n                (action === types_1.PlayerAction.DEFEND || action === types_1.PlayerAction.TAKE)) {\n                // Дополнительная проверка для TAKE: можно брать только если есть что брать\n                if (action === types_1.PlayerAction.TAKE && tableIsEmpty) {\n                    return {\n                        allowed: false,\n                        error: `Error: Cannot TAKE, no cards on the table.`,\n                    };\n                }\n                // Дополнительная проверка для DEFEND: можно защищаться только если есть атакующая карта\n                if (action === types_1.PlayerAction.DEFEND &&\n                    (!lastPair || lastPair.length !== 1)) {\n                    return {\n                        allowed: false,\n                        error: `Error: Cannot DEFEND, no attacking card found.`,\n                    };\n                }\n                return { allowed: true };\n            }\n            else {\n                return {\n                    allowed: false,\n                    error: `Error: Player ${this.state.players[playerIndex].id} (current: ${this.state.currentPlayerIndex}) cannot perform action ${action} at this stage.`,\n                };\n            }\n        }\n        // 2. Подкидывание (не защитник, после успешной защиты, ход у защитника)\n        else if (action === types_1.PlayerAction.ATTACK &&\n            !isDefender &&\n            lastPairDefended &&\n            this.state.currentPlayerIndex === this.state.defenderIndex) {\n            // Дополнительная проверка: количество карт на столе не должно превышать лимит атаки\n            // И не больше, чем карт у защитника на руках в начале раунда атаки (если стол пуст)\n            const defender = this.state.players[this.state.defenderIndex];\n            const currentAttackLimit = this.state.tableCards.length === 0\n                ? Math.min(this.rules.attackLimit, defender.hand.length)\n                : this.rules.attackLimit;\n            if (this.state.tableCards.flat().length / 2 >= currentAttackLimit) { // Считаем пары карт (атака+защита) или одиночные карты атаки\n                return {\n                    allowed: false,\n                    error: `Error: Cannot podkidnut, attack limit (${currentAttackLimit}) reached.`,\n                };\n            }\n            return { allowed: true }; // Разрешаем подкидывание\n        }\n        // 3. Подкидывание карт (любым игроком, кроме защитника, после успешной защиты)\n        else if (action === types_1.PlayerAction.ATTACK && !isDefender && lastPairDefended) {\n            // Проверяем, есть ли у игрока карты, которые можно подкинуть\n            const player = this.state.players[playerIndex];\n            const validPodkidnoyCards = player.hand.filter(card => this.state.tableCards.flat().some(tableCard => tableCard.rank === card.rank));\n            if (validPodkidnoyCards.length > 0) {\n                // TODO: Логика выбора карты для подкидывания (если их несколько)\n                // Пока просто проверяем возможность\n                // const currentPodkidnoy = validPodkidnoyCards[0]; // Пример\n                return { allowed: true };\n            }\n            else {\n                return { allowed: false, error: `Error: Player ${player.id} has no valid cards to podkidnut.` };\n            }\n        }\n        // 4. Невалидное действие\n        else {\n            return { allowed: false, error: `Error: Action ${action} is not allowed for player ${playerIndex} in the current state.` };\n        }\n    }\n    /**\n     * Выполнение хода игрока\n     */\n    makeMove(playerId, action, cardIndex) {\n        // Проверка, что игра в процессе\n        if (this.state.gameStatus !== types_1.GameStatus.IN_PROGRESS) {\n            console.error(\"Error: Game is not in progress.\");\n            return false;\n        }\n        const playerIndex = this.state.players.findIndex(p => p.id === playerId);\n        if (playerIndex === -1) {\n            console.error(`Error: Player with ID ${playerId} not found.`);\n            return false;\n        }\n        // Проверка, разрешено ли действие\n        const { allowed, error } = this._isActionAllowed(playerIndex, action);\n        if (!allowed) {\n            console.error(error || `Error: Action ${action} is not allowed for player ${playerId} right now.`);\n            return false;\n        }\n        // Логика хода в зависимости от действия\n        let success = false;\n        switch (action) {\n            case types_1.PlayerAction.ATTACK:\n                success = this.handleAttack(playerIndex, cardIndex);\n                break;\n            case types_1.PlayerAction.DEFEND:\n                success = this.handleDefend(playerIndex, cardIndex);\n                break;\n            case types_1.PlayerAction.TAKE:\n                success = this.handleTake(playerIndex);\n                break;\n            case types_1.PlayerAction.PASS:\n                success = this.handlePass(playerIndex);\n                break;\n            default:\n                console.error(`Error: Unknown PlayerAction: ${action}`);\n                return false;\n        }\n        // Если ход был успешным, проверяем конец игры и обновляем активного игрока (если нужно)\n        if (success) {\n            // Отправляем событие о ходе игрока\n            this.emitEvent({\n                type: types_1.GameEvent.PLAYER_MOVED,\n                gameState: this.getState(),\n                playerId,\n                action,\n                cardIndex,\n            });\n            if (!this.checkGameEnd()) {\n                // Логика перехода хода теперь полностью внутри handleTake и handlePass\n                // (через _updateRolesAfterTake и _determineNextRoles соответственно)\n                // Поэтому вызов _moveToNextTurn здесь больше не нужен.\n                // if (action === PlayerAction.TAKE || action === PlayerAction.PASS) {\n                //     // this._moveToNextTurn(); // Удалено\n                // }\n                // this.updateActivePlayer(); // Обновление происходит внутри handle-методов или методов перехода хода\n            }\n        }\n        return success;\n    }\n    /**\n     * Проверка валидности атакующей карты\n     */\n    isValidAttack(card, tableCards, defenderHandSize) {\n        var _a;\n        // Если стол пуст (первый ход атаки), любая карта валидна.\n        if (tableCards.length === 0) {\n            return true;\n        }\n        // Если стол не пуст (подкидывание), ранг карты должен совпадать\n        // с рангом любой карты, уже лежащей на столе (атакующей или защитной).\n        const ranksOnTable = new Set(tableCards.flat().map((c) => c.rank));\n        if (!ranksOnTable.has(card.rank)) {\n            return false;\n        }\n        // Нельзя подкидывать больше карт, чем у защитника на руках (минус уже отбитые в этом раунде)\n        /* const cardsToDefendCount = tableCards.filter(\n          (pair) => pair.length === 1,\n        ).length; */ // УДАЛЕНО, ТАК КАК НЕ ИСПОЛЬЮТСЯ\n        // const maxPodkidnoy = defenderHandSize - cardsToDefendCount; // УДАЛЕНО, ТАК КАК НЕ ИСПОЛЬЮТСЯ\n        // const currentPodkidnoy = tableCards.length - cardsToDefendCount; // Сколько уже подкинули сверх первой атаки - УДАЛЕНО, ТАК КАК НЕ ИСПОЛЬЮТСЯ\n        // Проверяем общее количество карт на столе против лимита\n        const maxCardsOnTable = (_a = this.rules.maxTableCards) !== null && _a !== void 0 ? _a : 6; // Используем правило или 6 по умолчанию\n        if (tableCards.length >= Math.min(maxCardsOnTable, defenderHandSize)) {\n            return false;\n        }\n        return true;\n    }\n    /**\n     * Обработка атаки\n     */\n    handleAttack(playerIndex, cardIndex) {\n        // Проверка наличия cardIndex\n        if (typeof cardIndex !== \"number\" || cardIndex < 0) {\n            console.error(`Error: Valid cardIndex is required for ATTACK action.`);\n            return false;\n        }\n        // Атаковать (или подкидывать) может любой игрок, кроме защищающегося\n        // Эта проверка уже сделана в _isActionAllowed\n        // if (playerIndex === this.state.defenderIndex) {\n        //   console.error(\"Error: The defender cannot attack.\");\n        //   return false;\n        // }\n        const player = this.state.players[playerIndex];\n        if (cardIndex >= player.hand.length) {\n            console.error(`Error: Invalid card index ${cardIndex} for player ${player.id}.`);\n            return false;\n        }\n        const card = player.hand[cardIndex];\n        const defender = this.state.players[this.state.defenderIndex];\n        // Проверка валидности карты для атаки/подкидывания\n        if (!this.isValidAttack(card, this.state.tableCards, defender.hand.length)) {\n            console.error(`Error: Card ${card.rank} ${card.suit} is not a valid attack/podkidnoy card.`);\n            return false;\n        }\n        // Перемещаем карту из руки на стол\n        player.hand.splice(cardIndex, 1);\n        this.state.tableCards.push([card]);\n        // Сбрасываем флаг взятия карт, так как началась новая атака\n        this.state.defenderTookCards = false;\n        // Передаем ход защитнику\n        this.state.currentPlayerIndex = this.state.defenderIndex;\n        this.updateActivePlayer();\n        console.log(`Player ${player.id} attacks with ${card.rank} ${card.suit}. Turn passes to defender ${defender.id}.`);\n        return true;\n    }\n    /**\n     * Проверка валидности защищающейся карты\n     */\n    isValidDefense(attackCard, defendCard, trumpSuit) {\n        // Карта той же масти, но старше\n        if (attackCard.suit === defendCard.suit &&\n            this.getRankValue(defendCard.rank) > this.getRankValue(attackCard.rank)) {\n            return true;\n        }\n        // Карта - козырь, а атакующая карта - нет\n        if (defendCard.suit === trumpSuit && attackCard.suit !== trumpSuit) {\n            return true;\n        }\n        // Обе карты козырные, защищающаяся карта старше\n        if (attackCard.suit === trumpSuit &&\n            defendCard.suit === trumpSuit &&\n            this.getRankValue(defendCard.rank) > this.getRankValue(attackCard.rank)) {\n            return true;\n        }\n        return false;\n    }\n    /**\n     * Обработка защиты\n     */\n    handleDefend(playerIndex, cardIndex) {\n        var _a;\n        // Проверка наличия cardIndex\n        if (typeof cardIndex !== \"number\" || cardIndex < 0) {\n            console.error(`Error: Valid cardIndex is required for DEFEND action.`);\n            return false;\n        }\n        // Защищаться может только защищающийся игрок\n        // Эта проверка уже сделана в _isActionAllowed\n        // if (playerIndex !== this.state.defenderIndex) {\n        //   console.error(\"Error: Only the defender can defend.\");\n        //   return false;\n        // }\n        const player = this.state.players[playerIndex];\n        if (cardIndex >= player.hand.length) {\n            console.error(`Error: Invalid card index ${cardIndex} for player ${player.id}.`);\n            return false;\n        }\n        const defendingCard = player.hand[cardIndex];\n        // Находим последнюю атакующую карту, которую нужно отбить\n        const lastPair = this.state.tableCards.at(-1);\n        if (!lastPair || lastPair.length !== 1) {\n            console.error(\"Error: No attacking card to defend against.\");\n            return false;\n        }\n        const attackingCard = lastPair[0];\n        // Проверяем, может ли выбранная карта отбить атакующую\n        if (!this.isValidDefense(attackingCard, defendingCard, this.state.trumpSuit)) {\n            console.error(`Error: Card ${defendingCard.rank} ${defendingCard.suit} cannot defend against ${attackingCard.rank} ${attackingCard.suit}.`);\n            return false;\n        }\n        // Перемещаем карту из руки на стол к атакующей карте\n        player.hand.splice(cardIndex, 1);\n        lastPair.push(defendingCard);\n        // Проверяем, все ли карты на столе отбиты\n        const allDefended = this.state.tableCards.every((pair) => pair.length === 2);\n        const defenderHasCards = player.hand.length > 0;\n        const canPodkidnut = this.state.tableCards.length < ((_a = this.rules.maxTableCards) !== null && _a !== void 0 ? _a : 6);\n        // Если все отбито и у защитника нет карт ИЛИ нельзя больше подкидывать, ход атакующего (сказать пас/бито)\n        if (allDefended && (!defenderHasCards || !canPodkidnut)) {\n            this.state.currentPlayerIndex = this.state.attackerIndex;\n            console.log(`Defender ${player.id} defended with ${defendingCard.rank} ${defendingCard.suit}. All cards defended. Turn passes to attacker ${this.state.players[this.state.attackerIndex].id} to pass.`);\n        }\n        // Если все отбито, но можно подкидывать и у защитника есть карты, ход остается у защитника (ожидание подкидывания или паса)\n        else if (allDefended) {\n            this.state.currentPlayerIndex = this.state.defenderIndex; // Остается у защитника, но он ждет\n            console.log(`Defender ${player.id} defended with ${defendingCard.rank} ${defendingCard.suit}. Waiting for podkidnoy or pass from attacker(s).`);\n        }\n        // Если не все отбито (это не должно произойти здесь, т.к. мы только что добавили карту)\n        // Оставляем ход у защитника для следующей защиты\n        else {\n            this.state.currentPlayerIndex = this.state.defenderIndex;\n            console.log(`Defender ${player.id} defended with ${defendingCard.rank} ${defendingCard.suit}. Turn remains with defender.`);\n        }\n        this.updateActivePlayer();\n        return true;\n    }\n    /**\n     * Вспомогательный метод: Защитник берет карты со стола\n     */\n    _defenderTakesCards(playerIndex) {\n        const player = this.state.players[playerIndex];\n        const cardsToTake = this.state.tableCards.flat();\n        player.hand.push(...cardsToTake);\n        this.state.tableCards = [];\n        this.state.defenderTookCards = true; // Устанавливаем флаг, что защитник взял карты\n        console.log(`Player ${player.id} takes ${cardsToTake.length} cards from the table.`);\n    }\n    /**\n     * Вспомогательный метод: Обновляет роли после того, как защитник взял карты.\n     * Ход переходит к следующему игроку после взявшего.\n     * @returns {boolean} Возвращает true, если игра окончена, иначе false.\n     */\n    _updateRolesAfterTake() {\n        const numPlayers = this.state.players.length;\n        const playerWhoTookIndex = this.state.defenderIndex; // Индекс игрока, который только что взял карты\n        // Определяем следующего атакующего, пропуская выбывших\n        let nextAttackerIndex = (playerWhoTookIndex + 1) % numPlayers;\n        let loopCheck = 0;\n        while (this.state.players[nextAttackerIndex].hand.length === 0 &&\n            this.state.deck.length === 0 &&\n            loopCheck < numPlayers) {\n            if (nextAttackerIndex === playerWhoTookIndex) {\n                // Обошли круг и вернулись к тому, кто взял - он единственный оставшийся\n                console.log(\"Game ended: Only the player who took cards remains.\");\n                return this.checkGameEnd();\n            }\n            nextAttackerIndex = (nextAttackerIndex + 1) % numPlayers;\n            loopCheck++;\n        }\n        // Если после цикла nextAttackerIndex совпадает с playerWhoTookIndex, игра окончена\n        if (nextAttackerIndex === playerWhoTookIndex &&\n            loopCheck >= numPlayers - 1) {\n            console.log(\"Game ended: All other players are out after take.\");\n            return this.checkGameEnd();\n        }\n        // Определяем следующего защитника, пропуская выбывших\n        let nextDefenderIndex = (nextAttackerIndex + 1) % numPlayers;\n        loopCheck = 0;\n        while (this.state.players[nextDefenderIndex].hand.length === 0 &&\n            this.state.deck.length === 0 &&\n            loopCheck < numPlayers) {\n            if (nextDefenderIndex === nextAttackerIndex) {\n                // Обошли круг и вернулись к атакующему - он единственный оставшийся\n                console.log(\"Game ended: Only the next attacker remains.\");\n                return this.checkGameEnd();\n            }\n            nextDefenderIndex = (nextDefenderIndex + 1) % numPlayers;\n            loopCheck++;\n        }\n        // Если после цикла nextDefenderIndex совпадает с nextAttackerIndex, игра окончена\n        if (nextDefenderIndex === nextAttackerIndex &&\n            loopCheck >= numPlayers - 1) {\n            console.log(\"Game ended: Only attacker and defender remain, but defender cannot defend.\");\n            return this.checkGameEnd();\n        }\n        this.state.attackerIndex = nextAttackerIndex;\n        this.state.defenderIndex = nextDefenderIndex;\n        this.state.currentPlayerIndex = this.state.attackerIndex; // Ход переходит к новому атакующему\n        console.log(`Roles updated after take: Attacker=${this.state.players[this.state.attackerIndex].id}, Defender=${this.state.players[this.state.defenderIndex].id}`);\n        this.updateActivePlayer();\n        return false; // Игра не закончена этим действием\n    }\n    /**\n     * Обработка взятия карт (защитник берет)\n     */\n    handleTake(playerIndex) {\n        // Проверки перенесены в _isActionAllowed\n        // if (playerIndex !== this.state.defenderIndex) {\n        //   console.error(\"Error: Only the defender can take cards.\");\n        //   return false;\n        // }\n        // Проверяем, есть ли карты на столе для взятия\n        if (this.state.tableCards.length === 0) {\n            console.error(\"Error: No cards on the table to take.\");\n            return false;\n        }\n        // Защитник берет карты\n        this._defenderTakesCards(playerIndex);\n        // Пополняем руки (начиная с атакующего, затем защитник, потом остальные)\n        // Порядок: атакующий -> ... -> защитник\n        this.replenishHands();\n        // Проверяем окончание игры после пополнения рук\n        if (this.checkGameEnd()) {\n            return true;\n        }\n        // Обновляем роли атакующего и защитника\n        const gameEnded = this._updateRolesAfterTake();\n        if (gameEnded) {\n            return true;\n        }\n        // Сбрасываем флаг ПОСЛЕ обновления ролей и перехода хода\n        this.state.defenderTookCards = false;\n        return true;\n    }\n    /**\n     * Вспомогательный метод: Перемещает карты со стола в отбой\n     */\n    _clearTableToDiscardPile() {\n        this.state.discardPile.push(...this.state.tableCards.flat());\n        this.state.tableCards = [];\n    }\n    /**\n     * Вспомогательный метод: Определяет следующего атакующего и защитника\n     * @returns {boolean} Возвращает true, если удалось определить роли (игра продолжается), иначе false (игра окончена).\n     */\n    _determineNextRoles() {\n        const numPlayers = this.state.players.length;\n        const previousDefender = this.state.defenderIndex;\n        // Новый атакующий - это предыдущий защитник\n        this.state.attackerIndex = previousDefender;\n        // Определяем нового защитника, пропуская выбывших игроков\n        let nextDefenderIndex = (this.state.attackerIndex + 1) % numPlayers;\n        let loopCheck = 0; // Предотвращение бесконечного цикла, если что-то пойдет не так\n        while (this.state.players[nextDefenderIndex].hand.length === 0 &&\n            this.state.deck.length === 0 &&\n            loopCheck < numPlayers // Проверяем не больше, чем количество игроков\n        ) {\n            if (nextDefenderIndex === this.state.attackerIndex) {\n                // Если обошли круг и вернулись к атакующему, значит, все остальные выбыли\n                console.log(\"Game potentially ended: Only attacker remains with cards or deck is empty.\");\n                return false; // Сигнализируем, что роли определить не удалось (игра окончена)\n            }\n            nextDefenderIndex = (nextDefenderIndex + 1) % numPlayers;\n            loopCheck++;\n        }\n        // Если после цикла nextDefenderIndex совпадает с attackerIndex, игра окончена\n        if (nextDefenderIndex === this.state.attackerIndex &&\n            loopCheck >= numPlayers - 1) {\n            console.log(\"Game ended: All other players are out.\");\n            return false;\n        }\n        this.state.defenderIndex = nextDefenderIndex;\n        this.state.currentPlayerIndex = this.state.attackerIndex; // Ход переходит к новому атакующему\n        return true; // Роли успешно определены\n    }\n    /**\n     * Обработка паса (бито) - атакующий завершает раунд после успешной защиты\n     */\n    handlePass(playerIndex) {\n        // Проверяем, что это атакующий игрок\n        if (playerIndex !== this.state.attackerIndex) {\n            console.error(\"Error: Only the attacker can pass (finish the round).\");\n            return false;\n        }\n        // Проверяем, есть ли карты на столе и все ли они отбиты\n        if (this.state.tableCards.length === 0) {\n            console.error(\"Error: Cannot pass, no cards on the table.\");\n            return false;\n        }\n        const allDefended = this.state.tableCards.every((pair) => pair.length === 2);\n        if (!allDefended) {\n            console.error(\"Error: Cannot pass, not all cards are defended.\");\n            return false;\n        }\n        // Перемещаем карты со стола в отбой\n        this._clearTableToDiscardPile();\n        // Пополняем руки игроков\n        this.replenishHands();\n        // Сбрасываем флаг перед проверкой конца игры и определением ролей\n        this.state.defenderTookCards = false;\n        // Проверяем окончание игры после пополнения рук\n        if (this.checkGameEnd()) {\n            return true; // Игра завершена\n        }\n        // Определяем следующие роли и передаем ход\n        if (!this._determineNextRoles()) {\n            // Если определить роли не удалось (например, остался 1 игрок), проверяем конец игры еще раз\n            return this.checkGameEnd();\n        }\n        // Обновляем статус активного игрока\n        this.updateActivePlayer();\n        console.log(`Round finished (Pass). New attacker: ${this.state.players[this.state.attackerIndex].id}, New defender: ${this.state.players[this.state.defenderIndex].id}`);\n        return true;\n    }\n    /**\n     * Обработка завершения хода (атакующий говорит \"бито\" или \"пас\")\n     */\n    handleDone(playerIndex) {\n        // Проверяем, что это действительно атакующий игрок завершает ход\n        if (playerIndex !== this.state.attackerIndex) {\n            console.error(\"Error: Only the attacker can finish the turn with 'Done'.\");\n            return false;\n        }\n        // Проверяем, есть ли карты на столе (был ли хотя бы один ход атаки)\n        if (this.state.tableCards.length > 0) {\n            // Перемещаем карты со стола в отбой\n            this._clearTableToDiscardPile();\n        }\n        else {\n            // Если стол пуст, значит атакующий спасовал сразу\n            console.log(`Player ${this.state.players[playerIndex].id} passed the turn immediately.`);\n            // Ничего не делаем с картами, просто передаем ход\n        }\n        // Пополняем руки игроков\n        this.replenishHands();\n        // Проверяем окончание игры после пополнения рук\n        if (this.checkGameEnd()) {\n            return true; // Игра завершена\n        }\n        // Определяем следующие роли и передаем ход\n        // Логика такая же, как при 'Pass', т.к. защитник успешно отбился (или атаки не было)\n        if (!this._determineNextRoles()) {\n            // Если определить роли не удалось (например, остался 1 игрок), проверяем конец игры еще раз\n            return this.checkGameEnd();\n        }\n        // Обновляем статус активного игрока\n        this.updateActivePlayer();\n        console.log(`Round finished (Done/Pass). New attacker: ${this.state.players[this.state.attackerIndex].id}, New defender: ${this.state.players[this.state.defenderIndex].id}`);\n        return true;\n    }\n    /**\n     * Пополнение рук игроков из колоды до нужного количества\n     */\n    replenishHands() {\n        const cardsNeeded = this.rules.initialHandSize;\n        const numPlayers = this.state.players.length;\n        let currentPlayerToCheck = this.state.attackerIndex; // Начинаем с атакующего\n        for (let i = 0; i < numPlayers; i++) {\n            const player = this.state.players[currentPlayerToCheck];\n            // Пополняем руку, только если игрок еще в игре (есть карты или есть колода)\n            if (player.hand.length > 0 || this.state.deck.length > 0) {\n                while (player.hand.length < cardsNeeded && this.state.deck.length > 0) {\n                    const card = this.state.deck.shift();\n                    if (card) {\n                        player.hand.push(card);\n                    }\n                }\n            }\n            // Переходим к следующему игроку по кругу\n            currentPlayerToCheck = (currentPlayerToCheck + 1) % numPlayers;\n        }\n        // Если колода закончилась и козырь был под ней, добавляем его в state\n        if (this.state.deck.length === 0 &&\n            this.state.trumpCard &&\n            !this.state.players.some((p) => p.hand.includes(this.state.trumpCard))) {\n            // Козырь забирает игрок, который последним пополнил руку (если ему нужно)\n            // В нашей логике пополнения это будет игрок перед атакующим, если круг полный\n            // Но проще отдать его текущему атакующему, если у него меньше 6 карт.\n            // Или просто оставить его видимым, но не в игре? Правила разнятся.\n            // Пока оставим его видимым в state.trumpCard, но не в руках.\n            // Убираем TODO, т.к. конкретная реализация зависит от выбранных правил.\n            // this.state.trumpCard = undefined;\n        }\n    }\n    /**\n     * Проверка окончания игры\n     */\n    checkGameEnd() {\n        // Проверка условий окончания игры\n        const playersWithCards = this.state.players.filter((p) => p.hand.length > 0);\n        const playersWithoutCards = this.state.players.filter((p) => p.hand.length === 0);\n        // Игра заканчивается, если колода пуста и не более одного игрока с картами\n        if (this.state.deck.length === 0 && playersWithCards.length <= 1) {\n            this.state.gameStatus = types_1.GameStatus.FINISHED;\n            let message = \"\";\n            if (playersWithCards.length === 1) {\n                // Проигравший - тот, у кого остались карты\n                this.state.loser = playersWithCards[0];\n                console.log(`Game finished. Loser: ${this.state.loser.id}`);\n                // Победитель - первый игрок, который избавился от всех карт\n                // В классическом дураке первый вышедший считается победителем\n                this.state.winner = playersWithoutCards.length > 0 ? playersWithoutCards[0] : undefined;\n                if (this.state.winner) {\n                    console.log(`Winner: ${this.state.winner.id}`);\n                    message = `Game finished. Winner: ${this.state.winner.id}, Loser: ${this.state.loser.id}`;\n                }\n                else {\n                    message = `Game finished. Loser: ${this.state.loser.id}`;\n                }\n            }\n            else {\n                // Ничья (все сбросили карты одновременно) - очень редкий случай\n                console.log(\"Game finished. Draw!\");\n                this.state.winner = undefined;\n                this.state.loser = undefined;\n                message = \"Game finished. Draw!\";\n            }\n            // Отправляем событие окончания игры\n            this.emitEvent({\n                type: types_1.GameEvent.GAME_ENDED,\n                gameState: this.getState(),\n                message,\n            });\n            // Обнуляем активного игрока, т.к. игра завершена\n            this.state.currentPlayerIndex = -1;\n            this.updateActivePlayer();\n            return true;\n        }\n        return false;\n    }\n}\nexports.DurakGame = DurakGame;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/core/dist/durak/index.js\n");

/***/ }),

/***/ "../../packages/core/dist/index.js":
/*!*****************************************!*\
  !*** ../../packages/core/dist/index.js ***!
  \*****************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\n/**\n * Игровое ядро \"Козырь Мастер\"\n *\n * Этот файл экспортирует основные классы и интерфейсы игрового ядра\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BotDifficulty = exports.BotFactory = exports.DurakBot = void 0;\n// Экспорт общих типов\n__exportStar(__webpack_require__(/*! ./types */ \"../../packages/core/dist/types.js\"), exports);\n// Экспорт основной логики игры Дурак\n__exportStar(__webpack_require__(/*! ./durak */ \"../../packages/core/dist/durak/index.js\"), exports);\n// Экспорт системы ботов\nvar bot_1 = __webpack_require__(/*! ./durak/bot */ \"../../packages/core/dist/durak/bot.js\");\nObject.defineProperty(exports, \"DurakBot\", ({ enumerable: true, get: function () { return bot_1.DurakBot; } }));\nObject.defineProperty(exports, \"BotFactory\", ({ enumerable: true, get: function () { return bot_1.BotFactory; } }));\nObject.defineProperty(exports, \"BotDifficulty\", ({ enumerable: true, get: function () { return bot_1.BotDifficulty; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/core/dist/index.js\n");

/***/ }),

/***/ "../../packages/core/dist/types.js":
/*!*****************************************!*\
  !*** ../../packages/core/dist/types.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n/**\n * Общие типы и интерфейсы для игрового ядра \"Козырь Мастер\"\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GameEvent = exports.PlayerAction = exports.GameStatus = exports.DurakVariant = exports.CardRank = exports.CardSuit = void 0;\n// Типы карт\nvar CardSuit;\n(function (CardSuit) {\n    CardSuit[\"HEARTS\"] = \"hearts\";\n    CardSuit[\"DIAMONDS\"] = \"diamonds\";\n    CardSuit[\"CLUBS\"] = \"clubs\";\n    CardSuit[\"SPADES\"] = \"spades\";\n})(CardSuit || (exports.CardSuit = CardSuit = {}));\nvar CardRank;\n(function (CardRank) {\n    CardRank[\"SIX\"] = \"6\";\n    CardRank[\"SEVEN\"] = \"7\";\n    CardRank[\"EIGHT\"] = \"8\";\n    CardRank[\"NINE\"] = \"9\";\n    CardRank[\"TEN\"] = \"10\";\n    CardRank[\"JACK\"] = \"jack\";\n    CardRank[\"QUEEN\"] = \"queen\";\n    CardRank[\"KING\"] = \"king\";\n    CardRank[\"ACE\"] = \"ace\";\n})(CardRank || (exports.CardRank = CardRank = {}));\n// Варианты игры Дурак\nvar DurakVariant;\n(function (DurakVariant) {\n    DurakVariant[\"CLASSIC\"] = \"classic\";\n    DurakVariant[\"THROWING\"] = \"throwing\";\n    DurakVariant[\"TRANSFERABLE\"] = \"transferable\";\n    DurakVariant[\"TEAM\"] = \"team\";\n})(DurakVariant || (exports.DurakVariant = DurakVariant = {}));\n// Статус игры\nvar GameStatus;\n(function (GameStatus) {\n    GameStatus[\"NOT_STARTED\"] = \"not_started\";\n    GameStatus[\"IN_PROGRESS\"] = \"in_progress\";\n    GameStatus[\"FINISHED\"] = \"finished\";\n})(GameStatus || (exports.GameStatus = GameStatus = {}));\n// Действия игрока\nvar PlayerAction;\n(function (PlayerAction) {\n    PlayerAction[\"ATTACK\"] = \"attack\";\n    PlayerAction[\"DEFEND\"] = \"defend\";\n    PlayerAction[\"TAKE\"] = \"take\";\n    PlayerAction[\"PASS\"] = \"pass\";\n})(PlayerAction || (exports.PlayerAction = PlayerAction = {}));\n// События игры\nvar GameEvent;\n(function (GameEvent) {\n    GameEvent[\"GAME_STARTED\"] = \"game_started\";\n    GameEvent[\"GAME_ENDED\"] = \"game_ended\";\n    GameEvent[\"PLAYER_MOVED\"] = \"player_moved\";\n    GameEvent[\"TURN_CHANGED\"] = \"turn_changed\";\n    GameEvent[\"CARDS_DEALT\"] = \"cards_dealt\";\n    GameEvent[\"ROUND_ENDED\"] = \"round_ended\";\n})(GameEvent || (exports.GameEvent = GameEvent = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvY29yZS9kaXN0L3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGlCQUFpQixHQUFHLG9CQUFvQixHQUFHLGtCQUFrQixHQUFHLG9CQUFvQixHQUFHLGdCQUFnQixHQUFHLGdCQUFnQjtBQUMxSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsZUFBZSxnQkFBZ0IsZ0JBQWdCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLGVBQWUsZ0JBQWdCLGdCQUFnQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsbUJBQW1CLG9CQUFvQixvQkFBb0I7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxpQkFBaUIsa0JBQWtCLGtCQUFrQjtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsbUJBQW1CLG9CQUFvQixvQkFBb0I7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxnQkFBZ0IsaUJBQWlCLGlCQUFpQjtBQUNuRCIsInNvdXJjZXMiOlsid2VicGFjazovL2tvenlyLW1hc3Rlci13ZWIvLi4vLi4vcGFja2FnZXMvY29yZS9kaXN0L3R5cGVzLmpzPzc5ZmEiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKipcbiAqINCe0LHRidC40LUg0YLQuNC/0Ysg0Lgg0LjQvdGC0LXRgNGE0LXQudGB0Ysg0LTQu9GPINC40LPRgNC+0LLQvtCz0L4g0Y/QtNGA0LAgXCLQmtC+0LfRi9GA0Ywg0JzQsNGB0YLQtdGAXCJcbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5HYW1lRXZlbnQgPSBleHBvcnRzLlBsYXllckFjdGlvbiA9IGV4cG9ydHMuR2FtZVN0YXR1cyA9IGV4cG9ydHMuRHVyYWtWYXJpYW50ID0gZXhwb3J0cy5DYXJkUmFuayA9IGV4cG9ydHMuQ2FyZFN1aXQgPSB2b2lkIDA7XG4vLyDQotC40L/RiyDQutCw0YDRglxudmFyIENhcmRTdWl0O1xuKGZ1bmN0aW9uIChDYXJkU3VpdCkge1xuICAgIENhcmRTdWl0W1wiSEVBUlRTXCJdID0gXCJoZWFydHNcIjtcbiAgICBDYXJkU3VpdFtcIkRJQU1PTkRTXCJdID0gXCJkaWFtb25kc1wiO1xuICAgIENhcmRTdWl0W1wiQ0xVQlNcIl0gPSBcImNsdWJzXCI7XG4gICAgQ2FyZFN1aXRbXCJTUEFERVNcIl0gPSBcInNwYWRlc1wiO1xufSkoQ2FyZFN1aXQgfHwgKGV4cG9ydHMuQ2FyZFN1aXQgPSBDYXJkU3VpdCA9IHt9KSk7XG52YXIgQ2FyZFJhbms7XG4oZnVuY3Rpb24gKENhcmRSYW5rKSB7XG4gICAgQ2FyZFJhbmtbXCJTSVhcIl0gPSBcIjZcIjtcbiAgICBDYXJkUmFua1tcIlNFVkVOXCJdID0gXCI3XCI7XG4gICAgQ2FyZFJhbmtbXCJFSUdIVFwiXSA9IFwiOFwiO1xuICAgIENhcmRSYW5rW1wiTklORVwiXSA9IFwiOVwiO1xuICAgIENhcmRSYW5rW1wiVEVOXCJdID0gXCIxMFwiO1xuICAgIENhcmRSYW5rW1wiSkFDS1wiXSA9IFwiamFja1wiO1xuICAgIENhcmRSYW5rW1wiUVVFRU5cIl0gPSBcInF1ZWVuXCI7XG4gICAgQ2FyZFJhbmtbXCJLSU5HXCJdID0gXCJraW5nXCI7XG4gICAgQ2FyZFJhbmtbXCJBQ0VcIl0gPSBcImFjZVwiO1xufSkoQ2FyZFJhbmsgfHwgKGV4cG9ydHMuQ2FyZFJhbmsgPSBDYXJkUmFuayA9IHt9KSk7XG4vLyDQktCw0YDQuNCw0L3RgtGLINC40LPRgNGLINCU0YPRgNCw0LpcbnZhciBEdXJha1ZhcmlhbnQ7XG4oZnVuY3Rpb24gKER1cmFrVmFyaWFudCkge1xuICAgIER1cmFrVmFyaWFudFtcIkNMQVNTSUNcIl0gPSBcImNsYXNzaWNcIjtcbiAgICBEdXJha1ZhcmlhbnRbXCJUSFJPV0lOR1wiXSA9IFwidGhyb3dpbmdcIjtcbiAgICBEdXJha1ZhcmlhbnRbXCJUUkFOU0ZFUkFCTEVcIl0gPSBcInRyYW5zZmVyYWJsZVwiO1xuICAgIER1cmFrVmFyaWFudFtcIlRFQU1cIl0gPSBcInRlYW1cIjtcbn0pKER1cmFrVmFyaWFudCB8fCAoZXhwb3J0cy5EdXJha1ZhcmlhbnQgPSBEdXJha1ZhcmlhbnQgPSB7fSkpO1xuLy8g0KHRgtCw0YLRg9GBINC40LPRgNGLXG52YXIgR2FtZVN0YXR1cztcbihmdW5jdGlvbiAoR2FtZVN0YXR1cykge1xuICAgIEdhbWVTdGF0dXNbXCJOT1RfU1RBUlRFRFwiXSA9IFwibm90X3N0YXJ0ZWRcIjtcbiAgICBHYW1lU3RhdHVzW1wiSU5fUFJPR1JFU1NcIl0gPSBcImluX3Byb2dyZXNzXCI7XG4gICAgR2FtZVN0YXR1c1tcIkZJTklTSEVEXCJdID0gXCJmaW5pc2hlZFwiO1xufSkoR2FtZVN0YXR1cyB8fCAoZXhwb3J0cy5HYW1lU3RhdHVzID0gR2FtZVN0YXR1cyA9IHt9KSk7XG4vLyDQlNC10LnRgdGC0LLQuNGPINC40LPRgNC+0LrQsFxudmFyIFBsYXllckFjdGlvbjtcbihmdW5jdGlvbiAoUGxheWVyQWN0aW9uKSB7XG4gICAgUGxheWVyQWN0aW9uW1wiQVRUQUNLXCJdID0gXCJhdHRhY2tcIjtcbiAgICBQbGF5ZXJBY3Rpb25bXCJERUZFTkRcIl0gPSBcImRlZmVuZFwiO1xuICAgIFBsYXllckFjdGlvbltcIlRBS0VcIl0gPSBcInRha2VcIjtcbiAgICBQbGF5ZXJBY3Rpb25bXCJQQVNTXCJdID0gXCJwYXNzXCI7XG59KShQbGF5ZXJBY3Rpb24gfHwgKGV4cG9ydHMuUGxheWVyQWN0aW9uID0gUGxheWVyQWN0aW9uID0ge30pKTtcbi8vINCh0L7QsdGL0YLQuNGPINC40LPRgNGLXG52YXIgR2FtZUV2ZW50O1xuKGZ1bmN0aW9uIChHYW1lRXZlbnQpIHtcbiAgICBHYW1lRXZlbnRbXCJHQU1FX1NUQVJURURcIl0gPSBcImdhbWVfc3RhcnRlZFwiO1xuICAgIEdhbWVFdmVudFtcIkdBTUVfRU5ERURcIl0gPSBcImdhbWVfZW5kZWRcIjtcbiAgICBHYW1lRXZlbnRbXCJQTEFZRVJfTU9WRURcIl0gPSBcInBsYXllcl9tb3ZlZFwiO1xuICAgIEdhbWVFdmVudFtcIlRVUk5fQ0hBTkdFRFwiXSA9IFwidHVybl9jaGFuZ2VkXCI7XG4gICAgR2FtZUV2ZW50W1wiQ0FSRFNfREVBTFRcIl0gPSBcImNhcmRzX2RlYWx0XCI7XG4gICAgR2FtZUV2ZW50W1wiUk9VTkRfRU5ERURcIl0gPSBcInJvdW5kX2VuZGVkXCI7XG59KShHYW1lRXZlbnQgfHwgKGV4cG9ydHMuR2FtZUV2ZW50ID0gR2FtZUV2ZW50ID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/core/dist/types.js\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "styled-components":
/*!************************************!*\
  !*** external "styled-components" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("styled-components");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "@reduxjs/toolkit":
/*!***********************************!*\
  !*** external "@reduxjs/toolkit" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = import("@reduxjs/toolkit");;

/***/ }),

/***/ "react-redux":
/*!******************************!*\
  !*** external "react-redux" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-redux");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();