"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_MetaversePreview-simple_tsx";
exports.ids = ["src_components_MetaversePreview-simple_tsx"];
exports.modules = {

/***/ "./src/components/MetaversePreview-simple.tsx":
/*!****************************************************!*\
  !*** ./src/components/MetaversePreview-simple.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst worlds = [\n    {\n        id: \"casino\",\n        name: \"Королевское казино\",\n        description: \"Роскошное казино с золотыми столами и кристальными люстрами\",\n        theme: \"Классика\",\n        color: \"#ffd700\",\n        environment: \"sunset\",\n        features: [\n            \"Покерные столы\",\n            \"VIP зоны\",\n            \"Живая музыка\",\n            \"Бар\"\n        ],\n        playerCount: 1247,\n        preview: \"\\uD83C\\uDFF0\"\n    },\n    {\n        id: \"medieval\",\n        name: \"Средневековая таверна\",\n        description: \"Уютная таверна с каменными стенами и горящим камином\",\n        theme: \"Фантазия\",\n        color: \"#8b4513\",\n        environment: \"forest\",\n        features: [\n            \"Деревянные столы\",\n            \"Камин\",\n            \"Бард\",\n            \"Эль\"\n        ],\n        playerCount: 892,\n        preview: \"\\uD83C\\uDFDB️\"\n    },\n    {\n        id: \"futuristic\",\n        name: \"Киберпространство\",\n        description: \"Футуристическая станция с голографическими интерфейсами\",\n        theme: \"Sci-Fi\",\n        color: \"#00ffff\",\n        environment: \"city\",\n        features: [\n            \"Голограммы\",\n            \"Неон\",\n            \"ИИ дилеры\",\n            \"Антигравитация\"\n        ],\n        playerCount: 2156,\n        preview: \"\\uD83D\\uDE80\"\n    }\n];\n// Стилизованные компоненты\nconst MetaverseContainer = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__MetaverseContainer\",\n    componentId: \"sc-449e83ba-0\"\n})([\n    \"min-height:100vh;background:linear-gradient(135deg,#0f0f23 0%,#1a1a2e 50%,#16213e 100%);padding:4rem 2rem;display:flex;align-items:center;justify-content:center;\"\n]);\nconst ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__ContentWrapper\",\n    componentId: \"sc-449e83ba-1\"\n})([\n    \"max-width:1400px;width:100%;\"\n]);\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h2.withConfig({\n    displayName: \"MetaversePreview-simple__SectionTitle\",\n    componentId: \"sc-449e83ba-2\"\n})([\n    \"font-size:3.5rem;font-weight:900;text-align:center;margin-bottom:1rem;background:linear-gradient(45deg,#4a90e2,#7b68ee,#9370db);-webkit-background-clip:text;-webkit-text-fill-color:transparent;@media (max-width:768px){font-size:2.5rem;}\"\n]);\nconst SectionSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"MetaversePreview-simple__SectionSubtitle\",\n    componentId: \"sc-449e83ba-3\"\n})([\n    \"font-size:1.3rem;color:rgba(255,255,255,0.7);text-align:center;margin-bottom:4rem;max-width:800px;margin-left:auto;margin-right:auto;\"\n]);\nconst MainContent = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__MainContent\",\n    componentId: \"sc-449e83ba-4\"\n})([\n    \"display:grid;grid-template-columns:1fr 1fr;gap:4rem;margin-bottom:4rem;@media (max-width:1024px){grid-template-columns:1fr;gap:2rem;}\"\n]);\nconst PreviewContainer = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__PreviewContainer\",\n    componentId: \"sc-449e83ba-5\"\n})([\n    \"position:relative;height:500px;border-radius:20px;overflow:hidden;border:2px solid rgba(255,255,255,0.1);background:linear-gradient(135deg,#1a1a2e,#16213e);display:flex;align-items:center;justify-content:center;\"\n]);\nconst PreviewPlaceholder = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__PreviewPlaceholder\",\n    componentId: \"sc-449e83ba-6\"\n})([\n    \"font-size:8rem;filter:drop-shadow(0 0 30px currentColor);\"\n]);\nconst WorldInfo = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__WorldInfo\",\n    componentId: \"sc-449e83ba-7\"\n})([\n    \"display:flex;flex-direction:column;justify-content:center;\"\n]);\nconst WorldHeader = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__WorldHeader\",\n    componentId: \"sc-449e83ba-8\"\n})([\n    \"display:flex;align-items:center;gap:1.5rem;margin-bottom:2rem;\"\n]);\nconst WorldIcon = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__WorldIcon\",\n    componentId: \"sc-449e83ba-9\"\n})([\n    \"font-size:4rem;filter:drop-shadow(0 0 20px currentColor);\"\n]);\nconst WorldName = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h3.withConfig({\n    displayName: \"MetaversePreview-simple__WorldName\",\n    componentId: \"sc-449e83ba-10\"\n})([\n    \"font-size:2.5rem;font-weight:700;color:white;margin-bottom:0.5rem;\"\n]);\nconst WorldTheme = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"MetaversePreview-simple__WorldTheme\",\n    componentId: \"sc-449e83ba-11\"\n})([\n    \"font-size:1.2rem;color:rgba(255,255,255,0.7);\"\n]);\nconst WorldDescription = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"MetaversePreview-simple__WorldDescription\",\n    componentId: \"sc-449e83ba-12\"\n})([\n    \"font-size:1.1rem;color:rgba(255,255,255,0.8);line-height:1.6;margin-bottom:2rem;\"\n]);\nconst WorldStats = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__WorldStats\",\n    componentId: \"sc-449e83ba-13\"\n})([\n    \"display:grid;grid-template-columns:repeat(3,1fr);gap:1rem;margin-bottom:2rem;\"\n]);\nconst StatItem = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__StatItem\",\n    componentId: \"sc-449e83ba-14\"\n})([\n    \"text-align:center;background:rgba(255,255,255,0.05);border-radius:10px;padding:1rem;\"\n]);\nconst StatValue = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__StatValue\",\n    componentId: \"sc-449e83ba-15\"\n})([\n    \"font-size:2rem;font-weight:700;color:#4a90e2;\"\n]);\nconst StatLabel = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__StatLabel\",\n    componentId: \"sc-449e83ba-16\"\n})([\n    \"font-size:0.9rem;color:rgba(255,255,255,0.6);\"\n]);\nconst FeaturesList = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__FeaturesList\",\n    componentId: \"sc-449e83ba-17\"\n})([\n    \"margin-bottom:2rem;\"\n]);\nconst FeaturesTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h4.withConfig({\n    displayName: \"MetaversePreview-simple__FeaturesTitle\",\n    componentId: \"sc-449e83ba-18\"\n})([\n    \"font-size:1.2rem;font-weight:600;color:white;margin-bottom:1rem;\"\n]);\nconst FeatureItem = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"MetaversePreview-simple__FeatureItem\",\n    componentId: \"sc-449e83ba-19\"\n})([\n    \"color:rgba(255,255,255,0.8);font-size:1rem;margin-bottom:0.5rem;\"\n]);\nconst ActionButtons = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__ActionButtons\",\n    componentId: \"sc-449e83ba-20\"\n})([\n    \"display:flex;gap:1rem;\"\n]);\nconst PrimaryButton = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"MetaversePreview-simple__PrimaryButton\",\n    componentId: \"sc-449e83ba-21\"\n})([\n    \"background:linear-gradient(135deg,#4a90e2,#7b68ee);color:white;border:none;border-radius:10px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;\"\n]);\nconst SecondaryButton = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"MetaversePreview-simple__SecondaryButton\",\n    componentId: \"sc-449e83ba-22\"\n})([\n    \"background:transparent;color:white;border:2px solid rgba(255,255,255,0.3);border-radius:10px;padding:1rem 2rem;font-size:1.1rem;font-weight:600;cursor:pointer;\"\n]);\nconst WorldSelector = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__WorldSelector\",\n    componentId: \"sc-449e83ba-23\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1rem;margin-bottom:3rem;\"\n]);\nconst WorldCard = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"MetaversePreview-simple__WorldCard\",\n    componentId: \"sc-449e83ba-24\"\n})([\n    \"background:\",\n    \";border:2px solid \",\n    \";border-radius:15px;padding:1.5rem;text-align:center;cursor:pointer;transition:all 0.3s ease;\"\n], (props)=>props.active ? `${props.color}20` : \"rgba(255, 255, 255, 0.05)\", (props)=>props.active ? props.color : \"rgba(255, 255, 255, 0.1)\");\nconst WorldCardIcon = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__WorldCardIcon\",\n    componentId: \"sc-449e83ba-25\"\n})([\n    \"font-size:2rem;margin-bottom:0.5rem;\"\n]);\nconst WorldCardName = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__WorldCardName\",\n    componentId: \"sc-449e83ba-26\"\n})([\n    \"font-size:1rem;font-weight:600;color:white;margin-bottom:0.5rem;\"\n]);\nconst WorldCardPlayers = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"MetaversePreview-simple__WorldCardPlayers\",\n    componentId: \"sc-449e83ba-27\"\n})([\n    \"font-size:0.8rem;color:rgba(255,255,255,0.6);\"\n]);\nconst MetaversePreview = ()=>{\n    const [selectedWorld, setSelectedWorld] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetaverseContainer, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentWrapper, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionTitle, {\n                            children: \"Метавселенная карточных игр\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSubtitle, {\n                            children: \"Исследуйте уникальные 3D миры и играйте в иммерсивной среде\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MainContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PreviewContainer, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PreviewPlaceholder, {\n                                children: worlds[selectedWorld].preview\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldInfo, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                                mode: \"wait\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 50\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        x: -50\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldIcon, {\n                                                    children: worlds[selectedWorld].preview\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldName, {\n                                                            children: worlds[selectedWorld].name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldTheme, {\n                                                            children: worlds[selectedWorld].theme\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldDescription, {\n                                            children: worlds[selectedWorld].description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldStats, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                            children: worlds[selectedWorld].playerCount\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                            children: \"Игроков онлайн\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                            children: worlds[selectedWorld].features.length\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                            children: \"Уникальных функций\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                            children: \"4.9\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                            children: \"Рейтинг\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesList, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesTitle, {\n                                                    children: \"Особенности мира:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                worlds[selectedWorld].features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureItem, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            x: -20\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            x: 0\n                                                        },\n                                                        transition: {\n                                                            delay: index * 0.1\n                                                        },\n                                                        children: [\n                                                            \"✨ \",\n                                                            feature\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButtons, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PrimaryButton, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    children: \"Войти в мир\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SecondaryButton, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    children: \"Виртуальный тур\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, selectedWorld, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldSelector, {\n                    children: worlds.map((world, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldCard, {\n                            active: index === selectedWorld,\n                            color: world.color,\n                            onClick: ()=>setSelectedWorld(index),\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldCardIcon, {\n                                    children: world.preview\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldCardName, {\n                                    children: world.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorldCardPlayers, {\n                                    children: [\n                                        world.playerCount,\n                                        \" игроков\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, world.id, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n            lineNumber: 266,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/MetaversePreview-simple.tsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MetaversePreview);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/MetaversePreview-simple.tsx\n");

/***/ })

};
;