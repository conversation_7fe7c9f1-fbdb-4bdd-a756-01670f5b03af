"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_LoadingScreen_tsx";
exports.ids = ["src_components_LoadingScreen_tsx"];
exports.modules = {

/***/ "./src/components/LoadingScreen.tsx":
/*!******************************************!*\
  !*** ./src/components/LoadingScreen.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingScreen: () => (/* binding */ LoadingScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst LoadingScreen = ({ message = \"Инициализация квантовых систем...\" })=>{\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentMessage, setCurrentMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(message);\n    const loadingMessages = [\n        \"Подключение к квантовым источникам...\",\n        \"Инициализация эмоционального ИИ...\",\n        \"Загрузка 3D метавселенной...\",\n        \"Настройка блокчейн соединения...\",\n        \"Активация предиктивной аналитики...\",\n        \"Запуск систем безопасности...\",\n        \"Финализация загрузки...\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            setProgress((prev)=>{\n                const newProgress = prev + Math.random() * 15;\n                if (newProgress >= 100) {\n                    clearInterval(interval);\n                    return 100;\n                }\n                // Обновляем сообщение в зависимости от прогресса\n                const messageIndex = Math.floor(newProgress / 100 * loadingMessages.length);\n                if (messageIndex < loadingMessages.length) {\n                    setCurrentMessage(loadingMessages[messageIndex]);\n                }\n                return newProgress;\n            });\n        }, 200);\n        return ()=>clearInterval(interval);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingContainer, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ParticlesBackground, {\n                children: Array.from({\n                    length: 50\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Particle, {\n                        style: {\n                            left: `${Math.random() * 100}%`,\n                            top: `${Math.random() * 100}%`,\n                            animationDelay: `${Math.random() * 3}s`,\n                            animationDuration: `${3 + Math.random() * 4}s`\n                        }\n                    }, i, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentWrapper, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogoContainer, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                animate: {\n                                    rotate: 360,\n                                    scale: [\n                                        1,\n                                        1.1,\n                                        1\n                                    ]\n                                },\n                                transition: {\n                                    rotate: {\n                                        duration: 4,\n                                        repeat: Infinity,\n                                        ease: \"linear\"\n                                    },\n                                    scale: {\n                                        duration: 2,\n                                        repeat: Infinity\n                                    }\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Logo, {\n                                    children: \"\\uD83C\\uDFAE\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h1, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.5\n                                },\n                                children: \"Козырь Мастер 4.0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuantumIndicator, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuantumRing, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    animate: {\n                                        rotate: 360\n                                    },\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity,\n                                        ease: \"linear\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuantumOrb, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuantumText, {\n                                children: \"Квантовая инициализация\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgressContainer, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgressBar, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        width: 0\n                                    },\n                                    animate: {\n                                        width: `${progress}%`\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    style: {\n                                        height: \"100%\",\n                                        background: \"linear-gradient(90deg, #4a90e2, #7b68ee, #9370db)\",\n                                        borderRadius: \"10px\",\n                                        position: \"relative\",\n                                        overflow: \"hidden\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgressGlow, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgressText, {\n                                children: [\n                                    Math.round(progress),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingMessage, {\n                            children: currentMessage\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined)\n                    }, currentMessage, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SystemIndicators, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SystemIndicator, {\n                                active: progress > 20,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IndicatorDot, {\n                                        active: progress > 20\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Квантовый движок\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SystemIndicator, {\n                                active: progress > 40,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IndicatorDot, {\n                                        active: progress > 40\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Эмоциональный ИИ\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SystemIndicator, {\n                                active: progress > 60,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IndicatorDot, {\n                                        active: progress > 60\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Метавселенная\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SystemIndicator, {\n                                active: progress > 80,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IndicatorDot, {\n                                        active: progress > 80\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Web3 экосистема\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechDetails, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechItem, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechLabel, {\n                                        children: \"Энтропия:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechValue, {\n                                        children: (progress / 100 * 0.999).toFixed(3)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechItem, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechLabel, {\n                                        children: \"Квантовые источники:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechValue, {\n                                        children: [\n                                            Math.min(5, Math.floor(progress / 20)),\n                                            \"/5\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechItem, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechLabel, {\n                                        children: \"ИИ модели:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechValue, {\n                                        children: [\n                                            Math.min(6, Math.floor(progress / 16)),\n                                            \"/6\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/LoadingScreen.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n// Стилизованные компоненты\nconst LoadingContainer = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__LoadingContainer\",\n    componentId: \"sc-52bb7eb-0\"\n})([\n    \"position:fixed;top:0;left:0;width:100vw;height:100vh;background:linear-gradient(135deg,#0c0c0c 0%,#1a1a2e 50%,#16213e 100%);display:flex;align-items:center;justify-content:center;z-index:9999;overflow:hidden;\"\n]);\nconst ParticlesBackground = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__ParticlesBackground\",\n    componentId: \"sc-52bb7eb-1\"\n})([\n    \"position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none;\"\n]);\nconst Particle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__Particle\",\n    componentId: \"sc-52bb7eb-2\"\n})([\n    \"position:absolute;width:2px;height:2px;background:rgba(74,144,226,0.6);border-radius:50%;animation:float linear infinite;@keyframes float{0%{transform:translateY(100vh) scale(0);opacity:0;}10%{opacity:1;}90%{opacity:1;}100%{transform:translateY(-100px) scale(1);opacity:0;}}\"\n]);\nconst ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__ContentWrapper\",\n    componentId: \"sc-52bb7eb-3\"\n})([\n    \"text-align:center;color:white;z-index:1;\"\n]);\nconst LogoContainer = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__LogoContainer\",\n    componentId: \"sc-52bb7eb-4\"\n})([\n    \"margin-bottom:3rem;h1{font-size:2.5rem;font-weight:700;background:linear-gradient(45deg,#4a90e2,#7b68ee,#9370db);-webkit-background-clip:text;-webkit-text-fill-color:transparent;margin-top:1rem;}\"\n]);\nconst Logo = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__Logo\",\n    componentId: \"sc-52bb7eb-5\"\n})([\n    \"font-size:4rem;margin-bottom:1rem;filter:drop-shadow(0 0 20px rgba(74,144,226,0.5));\"\n]);\nconst QuantumIndicator = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__QuantumIndicator\",\n    componentId: \"sc-52bb7eb-6\"\n})([\n    \"margin-bottom:3rem;display:flex;flex-direction:column;align-items:center;\"\n]);\nconst QuantumRing = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__QuantumRing\",\n    componentId: \"sc-52bb7eb-7\"\n})([\n    \"width:80px;height:80px;border:2px solid rgba(74,144,226,0.3);border-radius:50%;display:flex;align-items:center;justify-content:center;position:relative;margin-bottom:1rem;&::before{content:'';position:absolute;top:-2px;left:-2px;right:-2px;bottom:-2px;border-radius:50%;background:conic-gradient(from 0deg,transparent,#4a90e2,transparent);animation:spin 2s linear infinite;}@keyframes spin{to{transform:rotate(360deg);}}\"\n]);\nconst QuantumOrb = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__QuantumOrb\",\n    componentId: \"sc-52bb7eb-8\"\n})([\n    \"width:20px;height:20px;background:radial-gradient(circle,#4a90e2,#7b68ee);border-radius:50%;box-shadow:0 0 20px rgba(74,144,226,0.8);z-index:1;\"\n]);\nconst QuantumText = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__QuantumText\",\n    componentId: \"sc-52bb7eb-9\"\n})([\n    \"font-size:0.9rem;color:rgba(255,255,255,0.7);\"\n]);\nconst ProgressContainer = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__ProgressContainer\",\n    componentId: \"sc-52bb7eb-10\"\n})([\n    \"width:400px;margin-bottom:2rem;@media (max-width:480px){width:300px;}\"\n]);\nconst ProgressBar = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__ProgressBar\",\n    componentId: \"sc-52bb7eb-11\"\n})([\n    \"width:100%;height:8px;background:rgba(255,255,255,0.1);border-radius:10px;overflow:hidden;margin-bottom:0.5rem;position:relative;\"\n]);\nconst ProgressGlow = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__ProgressGlow\",\n    componentId: \"sc-52bb7eb-12\"\n})([\n    \"position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.3),transparent);animation:shimmer 2s infinite;@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}\"\n]);\nconst ProgressText = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__ProgressText\",\n    componentId: \"sc-52bb7eb-13\"\n})([\n    \"font-size:1.2rem;font-weight:600;color:#4a90e2;\"\n]);\nconst LoadingMessage = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__LoadingMessage\",\n    componentId: \"sc-52bb7eb-14\"\n})([\n    \"font-size:1.1rem;color:rgba(255,255,255,0.8);margin-bottom:2rem;min-height:1.5rem;\"\n]);\nconst SystemIndicators = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__SystemIndicators\",\n    componentId: \"sc-52bb7eb-15\"\n})([\n    \"display:grid;grid-template-columns:repeat(2,1fr);gap:1rem;margin-bottom:2rem;max-width:400px;@media (max-width:480px){grid-template-columns:1fr;}\"\n]);\nconst SystemIndicator = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__SystemIndicator\",\n    componentId: \"sc-52bb7eb-16\"\n})([\n    \"display:flex;align-items:center;gap:0.5rem;font-size:0.9rem;color:\",\n    \";transition:color 0.3s ease;\"\n], (props)=>props.active ? \"#4a90e2\" : \"rgba(255, 255, 255, 0.5)\");\nconst IndicatorDot = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__IndicatorDot\",\n    componentId: \"sc-52bb7eb-17\"\n})([\n    \"width:8px;height:8px;border-radius:50%;background:\",\n    \";box-shadow:\",\n    \";transition:all 0.3s ease;\"\n], (props)=>props.active ? \"#4a90e2\" : \"rgba(255, 255, 255, 0.3)\", (props)=>props.active ? \"0 0 10px rgba(74, 144, 226, 0.5)\" : \"none\");\nconst TechDetails = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__TechDetails\",\n    componentId: \"sc-52bb7eb-18\"\n})([\n    \"display:flex;justify-content:center;gap:2rem;font-size:0.8rem;@media (max-width:480px){flex-direction:column;gap:0.5rem;}\"\n]);\nconst TechItem = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"LoadingScreen__TechItem\",\n    componentId: \"sc-52bb7eb-19\"\n})([\n    \"display:flex;gap:0.5rem;\"\n]);\nconst TechLabel = styled_components__WEBPACK_IMPORTED_MODULE_3___default().span.withConfig({\n    displayName: \"LoadingScreen__TechLabel\",\n    componentId: \"sc-52bb7eb-20\"\n})([\n    \"color:rgba(255,255,255,0.6);\"\n]);\nconst TechValue = styled_components__WEBPACK_IMPORTED_MODULE_3___default().span.withConfig({\n    displayName: \"LoadingScreen__TechValue\",\n    componentId: \"sc-52bb7eb-21\"\n})([\n    \"color:#4a90e2;font-weight:600;\"\n]);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/LoadingScreen.tsx\n");

/***/ })

};
;