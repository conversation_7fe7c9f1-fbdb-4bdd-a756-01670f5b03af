/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next";
exports.ids = ["vendor-chunks/next"];
exports.modules = {

/***/ "../../node_modules/next/dist/pages/_document.js":
/*!*******************************************************!*\
  !*** ../../node_modules/next/dist/pages/_document.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  Head: function () {\n    return Head;\n  },\n  Html: function () {\n    return Html;\n  },\n  Main: function () {\n    return Main;\n  },\n  NextScript: function () {\n    return NextScript;\n  },\n  /**\n  * `Document` component handles the initial `document` markup and renders only on the server side.\n  * Commonly used for implementing server side rendering for `css-in-js` libraries.\n  */\n  default: function () {\n    return Document;\n  }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\nconst _react = /*#__PURE__*/_interop_require_wildcard(__webpack_require__(/*! react */ \"react\"));\nconst _constants = __webpack_require__(/*! ../shared/lib/constants */ \"../../node_modules/next/dist/shared/lib/constants.js\");\nconst _getpagefiles = __webpack_require__(/*! ../server/get-page-files */ \"../../node_modules/next/dist/server/get-page-files.js\");\nconst _htmlescape = __webpack_require__(/*! ../server/htmlescape */ \"../../node_modules/next/dist/server/htmlescape.js\");\nconst _iserror = /*#__PURE__*/_interop_require_default(__webpack_require__(/*! ../lib/is-error */ \"../../node_modules/next/dist/lib/is-error.js\"));\nconst _htmlcontextsharedruntime = __webpack_require__(/*! ../shared/lib/html-context.shared-runtime */ \"../../node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/html-context.js\");\nconst _encodeuripath = __webpack_require__(/*! ../shared/lib/encode-uri-path */ \"../../node_modules/next/dist/shared/lib/encode-uri-path.js\");\nfunction _interop_require_default(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function (nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n    return {\n      default: obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {\n    __proto__: null\n  };\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj.default = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\n/** Set of pages that have triggered a large data warning on production mode. */\nconst largePageDataWarnings = new Set();\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n  const sharedFiles = (0, _getpagefiles.getPageFiles)(buildManifest, \"/_app\");\n  const pageFiles =  true && inAmpMode ? [] : (0, _getpagefiles.getPageFiles)(buildManifest, pathname);\n  return {\n    sharedFiles,\n    pageFiles,\n    allFiles: [...new Set([...sharedFiles, ...pageFiles])]\n  };\n}\nfunction getPolyfillScripts(context, props) {\n  // polyfills.js has to be rendered as nomodule without async\n  // It also has to be the first script to load\n  const {\n    assetPrefix,\n    buildManifest,\n    assetQueryString,\n    disableOptimizedLoading,\n    crossOrigin\n  } = context;\n  return buildManifest.polyfillFiles.filter(polyfill => polyfill.endsWith(\".js\") && !polyfill.endsWith(\".module.js\")).map(polyfill => /*#__PURE__*/(0, _jsxruntime.jsx)(\"script\", {\n    defer: !disableOptimizedLoading,\n    nonce: props.nonce,\n    crossOrigin: props.crossOrigin || crossOrigin,\n    noModule: true,\n    src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(polyfill)}${assetQueryString}`\n  }, polyfill));\n}\nfunction hasComponentProps(child) {\n  return !!child && !!child.props;\n}\nfunction AmpStyles({\n  styles\n}) {\n  if (!styles) return null;\n  // try to parse styles from fragment for backwards compat\n  const curStyles = Array.isArray(styles) ? styles : [];\n  if (\n  // @ts-ignore Property 'props' does not exist on type ReactElement\n  styles.props &&\n  // @ts-ignore Property 'props' does not exist on type ReactElement\n  Array.isArray(styles.props.children)) {\n    const hasStyles = el => {\n      var _el_props_dangerouslySetInnerHTML, _el_props;\n      return el == null ? void 0 : (_el_props = el.props) == null ? void 0 : (_el_props_dangerouslySetInnerHTML = _el_props.dangerouslySetInnerHTML) == null ? void 0 : _el_props_dangerouslySetInnerHTML.__html;\n    };\n    // @ts-ignore Property 'props' does not exist on type ReactElement\n    styles.props.children.forEach(child => {\n      if (Array.isArray(child)) {\n        child.forEach(el => hasStyles(el) && curStyles.push(el));\n      } else if (hasStyles(child)) {\n        curStyles.push(child);\n      }\n    });\n  }\n  /* Add custom styles before AMP styles to prevent accidental overrides */\n  return /*#__PURE__*/(0, _jsxruntime.jsx)(\"style\", {\n    \"amp-custom\": \"\",\n    dangerouslySetInnerHTML: {\n      __html: curStyles.map(style => style.props.dangerouslySetInnerHTML.__html).join(\"\").replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, \"\").replace(/\\/\\*@ sourceURL=.*?\\*\\//g, \"\")\n    }\n  });\n}\nfunction getDynamicChunks(context, props, files) {\n  const {\n    dynamicImports,\n    assetPrefix,\n    isDevelopment,\n    assetQueryString,\n    disableOptimizedLoading,\n    crossOrigin\n  } = context;\n  return dynamicImports.map(file => {\n    if (!file.endsWith(\".js\") || files.allFiles.includes(file)) return null;\n    return /*#__PURE__*/(0, _jsxruntime.jsx)(\"script\", {\n      async: !isDevelopment && disableOptimizedLoading,\n      defer: !disableOptimizedLoading,\n      src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n      nonce: props.nonce,\n      crossOrigin: props.crossOrigin || crossOrigin\n    }, file);\n  });\n}\nfunction getScripts(context, props, files) {\n  var _buildManifest_lowPriorityFiles;\n  const {\n    assetPrefix,\n    buildManifest,\n    isDevelopment,\n    assetQueryString,\n    disableOptimizedLoading,\n    crossOrigin\n  } = context;\n  const normalScripts = files.allFiles.filter(file => file.endsWith(\".js\"));\n  const lowPriorityScripts = (_buildManifest_lowPriorityFiles = buildManifest.lowPriorityFiles) == null ? void 0 : _buildManifest_lowPriorityFiles.filter(file => file.endsWith(\".js\"));\n  return [...normalScripts, ...lowPriorityScripts].map(file => {\n    return /*#__PURE__*/(0, _jsxruntime.jsx)(\"script\", {\n      src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n      nonce: props.nonce,\n      async: !isDevelopment && disableOptimizedLoading,\n      defer: !disableOptimizedLoading,\n      crossOrigin: props.crossOrigin || crossOrigin\n    }, file);\n  });\n}\nfunction getPreNextWorkerScripts(context, props) {\n  const {\n    assetPrefix,\n    scriptLoader,\n    crossOrigin,\n    nextScriptWorkers\n  } = context;\n  // disable `nextScriptWorkers` in edge runtime\n  if (!nextScriptWorkers || \"nodejs\" === \"edge\") return null;\n  try {\n    let {\n      partytownSnippet\n    } = require(\"@builder.io/partytown/integration\");\n    const children = Array.isArray(props.children) ? props.children : [props.children];\n    // Check to see if the user has defined their own Partytown configuration\n    const userDefinedConfig = children.find(child => {\n      var _child_props_dangerouslySetInnerHTML, _child_props;\n      return hasComponentProps(child) && (child == null ? void 0 : (_child_props = child.props) == null ? void 0 : (_child_props_dangerouslySetInnerHTML = _child_props.dangerouslySetInnerHTML) == null ? void 0 : _child_props_dangerouslySetInnerHTML.__html.length) && \"data-partytown-config\" in child.props;\n    });\n    return /*#__PURE__*/(0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n      children: [!userDefinedConfig && /*#__PURE__*/(0, _jsxruntime.jsx)(\"script\", {\n        \"data-partytown-config\": \"\",\n        dangerouslySetInnerHTML: {\n          __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `\n        }\n      }), /*#__PURE__*/(0, _jsxruntime.jsx)(\"script\", {\n        \"data-partytown\": \"\",\n        dangerouslySetInnerHTML: {\n          __html: partytownSnippet()\n        }\n      }), (scriptLoader.worker || []).map((file, index) => {\n        const {\n          strategy,\n          src,\n          children: scriptChildren,\n          dangerouslySetInnerHTML,\n          ...scriptProps\n        } = file;\n        let srcProps = {};\n        if (src) {\n          // Use external src if provided\n          srcProps.src = src;\n        } else if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n          // Embed inline script if provided with dangerouslySetInnerHTML\n          srcProps.dangerouslySetInnerHTML = {\n            __html: dangerouslySetInnerHTML.__html\n          };\n        } else if (scriptChildren) {\n          // Embed inline script if provided with children\n          srcProps.dangerouslySetInnerHTML = {\n            __html: typeof scriptChildren === \"string\" ? scriptChildren : Array.isArray(scriptChildren) ? scriptChildren.join(\"\") : \"\"\n          };\n        } else {\n          throw new Error(\"Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script\");\n        }\n        return /*#__PURE__*/(0, _react.createElement)(\"script\", {\n          ...srcProps,\n          ...scriptProps,\n          type: \"text/partytown\",\n          key: src || index,\n          nonce: props.nonce,\n          \"data-nscript\": \"worker\",\n          crossOrigin: props.crossOrigin || crossOrigin\n        });\n      })]\n    });\n  } catch (err) {\n    if ((0, _iserror.default)(err) && err.code !== \"MODULE_NOT_FOUND\") {\n      console.warn(`Warning: ${err.message}`);\n    }\n    return null;\n  }\n}\nfunction getPreNextScripts(context, props) {\n  const {\n    scriptLoader,\n    disableOptimizedLoading,\n    crossOrigin\n  } = context;\n  const webWorkerScripts = getPreNextWorkerScripts(context, props);\n  const beforeInteractiveScripts = (scriptLoader.beforeInteractive || []).filter(script => script.src).map((file, index) => {\n    const {\n      strategy,\n      ...scriptProps\n    } = file;\n    return /*#__PURE__*/(0, _react.createElement)(\"script\", {\n      ...scriptProps,\n      key: scriptProps.src || index,\n      defer: scriptProps.defer ?? !disableOptimizedLoading,\n      nonce: props.nonce,\n      \"data-nscript\": \"beforeInteractive\",\n      crossOrigin: props.crossOrigin || crossOrigin\n    });\n  });\n  return /*#__PURE__*/(0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n    children: [webWorkerScripts, beforeInteractiveScripts]\n  });\n}\nfunction getHeadHTMLProps(props) {\n  const {\n    crossOrigin,\n    nonce,\n    ...restProps\n  } = props;\n  // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n  const headProps = restProps;\n  return headProps;\n}\nfunction getAmpPath(ampPath, asPath) {\n  return ampPath || `${asPath}${asPath.includes(\"?\") ? \"&\" : \"?\"}amp=1`;\n}\nfunction getNextFontLinkTags(nextFontManifest, dangerousAsPath, assetPrefix = \"\") {\n  if (!nextFontManifest) {\n    return {\n      preconnect: null,\n      preload: null\n    };\n  }\n  const appFontsEntry = nextFontManifest.pages[\"/_app\"];\n  const pageFontsEntry = nextFontManifest.pages[dangerousAsPath];\n  const preloadedFontFiles = Array.from(new Set([...(appFontsEntry ?? []), ...(pageFontsEntry ?? [])]));\n  // If no font files should preload but there's an entry for the path, add a preconnect tag.\n  const preconnectToSelf = !!(preloadedFontFiles.length === 0 && (appFontsEntry || pageFontsEntry));\n  return {\n    preconnect: preconnectToSelf ? /*#__PURE__*/(0, _jsxruntime.jsx)(\"link\", {\n      \"data-next-font\": nextFontManifest.pagesUsingSizeAdjust ? \"size-adjust\" : \"\",\n      rel: \"preconnect\",\n      href: \"/\",\n      crossOrigin: \"anonymous\"\n    }) : null,\n    preload: preloadedFontFiles ? preloadedFontFiles.map(fontFile => {\n      const ext = /\\.(woff|woff2|eot|ttf|otf)$/.exec(fontFile)[1];\n      return /*#__PURE__*/(0, _jsxruntime.jsx)(\"link\", {\n        rel: \"preload\",\n        href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(fontFile)}`,\n        as: \"font\",\n        type: `font/${ext}`,\n        crossOrigin: \"anonymous\",\n        \"data-next-font\": fontFile.includes(\"-s\") ? \"size-adjust\" : \"\"\n      }, fontFile);\n    }) : null\n  };\n}\nclass Head extends _react.default.Component {\n  static #_ = this.contextType = _htmlcontextsharedruntime.HtmlContext;\n  getCssLinks(files) {\n    const {\n      assetPrefix,\n      assetQueryString,\n      dynamicImports,\n      crossOrigin,\n      optimizeCss,\n      optimizeFonts\n    } = this.context;\n    const cssFiles = files.allFiles.filter(f => f.endsWith(\".css\"));\n    const sharedFiles = new Set(files.sharedFiles);\n    // Unmanaged files are CSS files that will be handled directly by the\n    // webpack runtime (`mini-css-extract-plugin`).\n    let unmangedFiles = new Set([]);\n    let dynamicCssFiles = Array.from(new Set(dynamicImports.filter(file => file.endsWith(\".css\"))));\n    if (dynamicCssFiles.length) {\n      const existing = new Set(cssFiles);\n      dynamicCssFiles = dynamicCssFiles.filter(f => !(existing.has(f) || sharedFiles.has(f)));\n      unmangedFiles = new Set(dynamicCssFiles);\n      cssFiles.push(...dynamicCssFiles);\n    }\n    let cssLinkElements = [];\n    cssFiles.forEach(file => {\n      const isSharedFile = sharedFiles.has(file);\n      if (!optimizeCss) {\n        cssLinkElements.push( /*#__PURE__*/(0, _jsxruntime.jsx)(\"link\", {\n          nonce: this.props.nonce,\n          rel: \"preload\",\n          href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n          as: \"style\",\n          crossOrigin: this.props.crossOrigin || crossOrigin\n        }, `${file}-preload`));\n      }\n      const isUnmanagedFile = unmangedFiles.has(file);\n      cssLinkElements.push( /*#__PURE__*/(0, _jsxruntime.jsx)(\"link\", {\n        nonce: this.props.nonce,\n        rel: \"stylesheet\",\n        href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n        crossOrigin: this.props.crossOrigin || crossOrigin,\n        \"data-n-g\": isUnmanagedFile ? undefined : isSharedFile ? \"\" : undefined,\n        \"data-n-p\": isUnmanagedFile ? undefined : isSharedFile ? undefined : \"\"\n      }, file));\n    });\n    if (false) {}\n    return cssLinkElements.length === 0 ? null : cssLinkElements;\n  }\n  getPreloadDynamicChunks() {\n    const {\n      dynamicImports,\n      assetPrefix,\n      assetQueryString,\n      crossOrigin\n    } = this.context;\n    return dynamicImports.map(file => {\n      if (!file.endsWith(\".js\")) {\n        return null;\n      }\n      return /*#__PURE__*/(0, _jsxruntime.jsx)(\"link\", {\n        rel: \"preload\",\n        href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n        as: \"script\",\n        nonce: this.props.nonce,\n        crossOrigin: this.props.crossOrigin || crossOrigin\n      }, file);\n    }) // Filter out nulled scripts\n    .filter(Boolean);\n  }\n  getPreloadMainLinks(files) {\n    const {\n      assetPrefix,\n      assetQueryString,\n      scriptLoader,\n      crossOrigin\n    } = this.context;\n    const preloadFiles = files.allFiles.filter(file => {\n      return file.endsWith(\".js\");\n    });\n    return [...(scriptLoader.beforeInteractive || []).map(file => /*#__PURE__*/(0, _jsxruntime.jsx)(\"link\", {\n      nonce: this.props.nonce,\n      rel: \"preload\",\n      href: file.src,\n      as: \"script\",\n      crossOrigin: this.props.crossOrigin || crossOrigin\n    }, file.src)), ...preloadFiles.map(file => /*#__PURE__*/(0, _jsxruntime.jsx)(\"link\", {\n      nonce: this.props.nonce,\n      rel: \"preload\",\n      href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n      as: \"script\",\n      crossOrigin: this.props.crossOrigin || crossOrigin\n    }, file))];\n  }\n  getBeforeInteractiveInlineScripts() {\n    const {\n      scriptLoader\n    } = this.context;\n    const {\n      nonce,\n      crossOrigin\n    } = this.props;\n    return (scriptLoader.beforeInteractive || []).filter(script => !script.src && (script.dangerouslySetInnerHTML || script.children)).map((file, index) => {\n      const {\n        strategy,\n        children,\n        dangerouslySetInnerHTML,\n        src,\n        ...scriptProps\n      } = file;\n      let html = \"\";\n      if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n        html = dangerouslySetInnerHTML.__html;\n      } else if (children) {\n        html = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n      }\n      return /*#__PURE__*/(0, _react.createElement)(\"script\", {\n        ...scriptProps,\n        dangerouslySetInnerHTML: {\n          __html: html\n        },\n        key: scriptProps.id || index,\n        nonce: nonce,\n        \"data-nscript\": \"beforeInteractive\",\n        crossOrigin: crossOrigin || undefined\n      });\n    });\n  }\n  getDynamicChunks(files) {\n    return getDynamicChunks(this.context, this.props, files);\n  }\n  getPreNextScripts() {\n    return getPreNextScripts(this.context, this.props);\n  }\n  getScripts(files) {\n    return getScripts(this.context, this.props, files);\n  }\n  getPolyfillScripts() {\n    return getPolyfillScripts(this.context, this.props);\n  }\n  makeStylesheetInert(node) {\n    return _react.default.Children.map(node, c => {\n      var _c_props, _c_props1;\n      if ((c == null ? void 0 : c.type) === \"link\" && (c == null ? void 0 : (_c_props = c.props) == null ? void 0 : _c_props.href) && _constants.OPTIMIZED_FONT_PROVIDERS.some(({\n        url\n      }) => {\n        var _c_props_href, _c_props;\n        return c == null ? void 0 : (_c_props = c.props) == null ? void 0 : (_c_props_href = _c_props.href) == null ? void 0 : _c_props_href.startsWith(url);\n      })) {\n        const newProps = {\n          ...(c.props || {}),\n          \"data-href\": c.props.href,\n          href: undefined\n        };\n        return /*#__PURE__*/_react.default.cloneElement(c, newProps);\n      } else if (c == null ? void 0 : (_c_props1 = c.props) == null ? void 0 : _c_props1.children) {\n        const newProps = {\n          ...(c.props || {}),\n          children: this.makeStylesheetInert(c.props.children)\n        };\n        return /*#__PURE__*/_react.default.cloneElement(c, newProps);\n      }\n      return c;\n      // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n    }).filter(Boolean);\n  }\n  render() {\n    const {\n      styles,\n      ampPath,\n      inAmpMode,\n      hybridAmp,\n      canonicalBase,\n      __NEXT_DATA__,\n      dangerousAsPath,\n      headTags,\n      unstable_runtimeJS,\n      unstable_JsPreload,\n      disableOptimizedLoading,\n      optimizeCss,\n      optimizeFonts,\n      assetPrefix,\n      nextFontManifest\n    } = this.context;\n    const disableRuntimeJS = unstable_runtimeJS === false;\n    const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n    this.context.docComponentsRendered.Head = true;\n    let {\n      head\n    } = this.context;\n    let cssPreloads = [];\n    let otherHeadElements = [];\n    if (head) {\n      head.forEach(c => {\n        let metaTag;\n        if (this.context.strictNextHead) {\n          metaTag = /*#__PURE__*/_react.default.createElement(\"meta\", {\n            name: \"next-head\",\n            content: \"1\"\n          });\n        }\n        if (c && c.type === \"link\" && c.props[\"rel\"] === \"preload\" && c.props[\"as\"] === \"style\") {\n          metaTag && cssPreloads.push(metaTag);\n          cssPreloads.push(c);\n        } else {\n          if (c) {\n            if (metaTag && (c.type !== \"meta\" || !c.props[\"charSet\"])) {\n              otherHeadElements.push(metaTag);\n            }\n            otherHeadElements.push(c);\n          }\n        }\n      });\n      head = cssPreloads.concat(otherHeadElements);\n    }\n    let children = _react.default.Children.toArray(this.props.children).filter(Boolean);\n    // show a warning if Head contains <title> (only in development)\n    if (true) {\n      children = _react.default.Children.map(children, child => {\n        var _child_props;\n        const isReactHelmet = child == null ? void 0 : (_child_props = child.props) == null ? void 0 : _child_props[\"data-react-helmet\"];\n        if (!isReactHelmet) {\n          var _child_props1;\n          if ((child == null ? void 0 : child.type) === \"title\") {\n            console.warn(\"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\");\n          } else if ((child == null ? void 0 : child.type) === \"meta\" && (child == null ? void 0 : (_child_props1 = child.props) == null ? void 0 : _child_props1.name) === \"viewport\") {\n            console.warn(\"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\");\n          }\n        }\n        return child;\n        // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n      });\n\n      if (this.props.crossOrigin) console.warn(\"Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n    }\n    if (false) {}\n    let hasAmphtmlRel = false;\n    let hasCanonicalRel = false;\n    // show warning and remove conflicting amp head tags\n    head = _react.default.Children.map(head || [], child => {\n      if (!child) return child;\n      const {\n        type,\n        props\n      } = child;\n      if ( true && inAmpMode) {\n        let badProp = \"\";\n        if (type === \"meta\" && props.name === \"viewport\") {\n          badProp = 'name=\"viewport\"';\n        } else if (type === \"link\" && props.rel === \"canonical\") {\n          hasCanonicalRel = true;\n        } else if (type === \"script\") {\n          // only block if\n          // 1. it has a src and isn't pointing to ampproject's CDN\n          // 2. it is using dangerouslySetInnerHTML without a type or\n          // a type of text/javascript\n          if (props.src && props.src.indexOf(\"ampproject\") < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === \"text/javascript\")) {\n            badProp = \"<script\";\n            Object.keys(props).forEach(prop => {\n              badProp += ` ${prop}=\"${props[prop]}\"`;\n            });\n            badProp += \"/>\";\n          }\n        }\n        if (badProp) {\n          console.warn(`Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n          return null;\n        }\n      } else {\n        // non-amp mode\n        if (type === \"link\" && props.rel === \"amphtml\") {\n          hasAmphtmlRel = true;\n        }\n      }\n      return child;\n      // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n    });\n\n    const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n    const nextFontLinkTags = getNextFontLinkTags(nextFontManifest, dangerousAsPath, assetPrefix);\n    return /*#__PURE__*/(0, _jsxruntime.jsxs)(\"head\", {\n      ...getHeadHTMLProps(this.props),\n      children: [this.context.isDevelopment && /*#__PURE__*/(0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [/*#__PURE__*/(0, _jsxruntime.jsx)(\"style\", {\n          \"data-next-hide-fouc\": true,\n          \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined,\n          dangerouslySetInnerHTML: {\n            __html: `body{display:none}`\n          }\n        }), /*#__PURE__*/(0, _jsxruntime.jsx)(\"noscript\", {\n          \"data-next-hide-fouc\": true,\n          \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined,\n          children: /*#__PURE__*/(0, _jsxruntime.jsx)(\"style\", {\n            dangerouslySetInnerHTML: {\n              __html: `body{display:block}`\n            }\n          })\n        })]\n      }), head, this.context.strictNextHead ? null : /*#__PURE__*/(0, _jsxruntime.jsx)(\"meta\", {\n        name: \"next-head-count\",\n        content: _react.default.Children.count(head || []).toString()\n      }), children, optimizeFonts && /*#__PURE__*/(0, _jsxruntime.jsx)(\"meta\", {\n        name: \"next-font-preconnect\"\n      }), nextFontLinkTags.preconnect, nextFontLinkTags.preload,  true && inAmpMode && /*#__PURE__*/(0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [/*#__PURE__*/(0, _jsxruntime.jsx)(\"meta\", {\n          name: \"viewport\",\n          content: \"width=device-width,minimum-scale=1,initial-scale=1\"\n        }), !hasCanonicalRel && /*#__PURE__*/(0, _jsxruntime.jsx)(\"link\", {\n          rel: \"canonical\",\n          href: canonicalBase + (__webpack_require__(/*! ../server/utils */ \"../../node_modules/next/dist/server/utils.js\").cleanAmpPath)(dangerousAsPath)\n        }), /*#__PURE__*/(0, _jsxruntime.jsx)(\"link\", {\n          rel: \"preload\",\n          as: \"script\",\n          href: \"https://cdn.ampproject.org/v0.js\"\n        }), /*#__PURE__*/(0, _jsxruntime.jsx)(AmpStyles, {\n          styles: styles\n        }), /*#__PURE__*/(0, _jsxruntime.jsx)(\"style\", {\n          \"amp-boilerplate\": \"\",\n          dangerouslySetInnerHTML: {\n            __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n          }\n        }), /*#__PURE__*/(0, _jsxruntime.jsx)(\"noscript\", {\n          children: /*#__PURE__*/(0, _jsxruntime.jsx)(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n              __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n            }\n          })\n        }), /*#__PURE__*/(0, _jsxruntime.jsx)(\"script\", {\n          async: true,\n          src: \"https://cdn.ampproject.org/v0.js\"\n        })]\n      }), !( true && inAmpMode) && /*#__PURE__*/(0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [!hasAmphtmlRel && hybridAmp && /*#__PURE__*/(0, _jsxruntime.jsx)(\"link\", {\n          rel: \"amphtml\",\n          href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n        }), this.getBeforeInteractiveInlineScripts(), !optimizeCss && this.getCssLinks(files), !optimizeCss && /*#__PURE__*/(0, _jsxruntime.jsx)(\"noscript\", {\n          \"data-n-css\": this.props.nonce ?? \"\"\n        }), !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(), !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files), optimizeCss && this.getCssLinks(files), optimizeCss && /*#__PURE__*/(0, _jsxruntime.jsx)(\"noscript\", {\n          \"data-n-css\": this.props.nonce ?? \"\"\n        }), this.context.isDevelopment &&\n        // this element is used to mount development styles so the\n        // ordering matches production\n        // (by default, style-loader injects at the bottom of <head />)\n        /*#__PURE__*/\n        (0, _jsxruntime.jsx)(\"noscript\", {\n          id: \"__next_css__DO_NOT_USE__\"\n        }), styles || null]\n      }), /*#__PURE__*/_react.default.createElement(_react.default.Fragment, {}, ...(headTags || []))]\n    });\n  }\n}\nfunction handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props) {\n  var _children_find_props, _children_find, _children_find_props1, _children_find1;\n  if (!props.children) return;\n  const scriptLoaderItems = [];\n  const children = Array.isArray(props.children) ? props.children : [props.children];\n  const headChildren = (_children_find = children.find(child => child.type === Head)) == null ? void 0 : (_children_find_props = _children_find.props) == null ? void 0 : _children_find_props.children;\n  const bodyChildren = (_children_find1 = children.find(child => child.type === \"body\")) == null ? void 0 : (_children_find_props1 = _children_find1.props) == null ? void 0 : _children_find_props1.children;\n  // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n  const combinedChildren = [...(Array.isArray(headChildren) ? headChildren : [headChildren]), ...(Array.isArray(bodyChildren) ? bodyChildren : [bodyChildren])];\n  _react.default.Children.forEach(combinedChildren, child => {\n    var _child_type;\n    if (!child) return;\n    // When using the `next/script` component, register it in script loader.\n    if ((_child_type = child.type) == null ? void 0 : _child_type.__nextScript) {\n      if (child.props.strategy === \"beforeInteractive\") {\n        scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([{\n          ...child.props\n        }]);\n        return;\n      } else if ([\"lazyOnload\", \"afterInteractive\", \"worker\"].includes(child.props.strategy)) {\n        scriptLoaderItems.push(child.props);\n        return;\n      }\n    }\n  });\n  __NEXT_DATA__.scriptLoader = scriptLoaderItems;\n}\nclass NextScript extends _react.default.Component {\n  static #_ = this.contextType = _htmlcontextsharedruntime.HtmlContext;\n  getDynamicChunks(files) {\n    return getDynamicChunks(this.context, this.props, files);\n  }\n  getPreNextScripts() {\n    return getPreNextScripts(this.context, this.props);\n  }\n  getScripts(files) {\n    return getScripts(this.context, this.props, files);\n  }\n  getPolyfillScripts() {\n    return getPolyfillScripts(this.context, this.props);\n  }\n  static getInlineScriptSource(context) {\n    const {\n      __NEXT_DATA__,\n      largePageDataBytes\n    } = context;\n    try {\n      const data = JSON.stringify(__NEXT_DATA__);\n      if (largePageDataWarnings.has(__NEXT_DATA__.page)) {\n        return (0, _htmlescape.htmlEscapeJsonString)(data);\n      }\n      const bytes =  false ? 0 : Buffer.from(data).byteLength;\n      const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ \"../../node_modules/next/dist/lib/pretty-bytes.js\")[\"default\"]);\n      if (largePageDataBytes && bytes > largePageDataBytes) {\n        if (false) {}\n        console.warn(`Warning: data for page \"${__NEXT_DATA__.page}\"${__NEXT_DATA__.page === context.dangerousAsPath ? \"\" : ` (path \"${context.dangerousAsPath}\")`} is ${prettyBytes(bytes)} which exceeds the threshold of ${prettyBytes(largePageDataBytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n      }\n      return (0, _htmlescape.htmlEscapeJsonString)(data);\n    } catch (err) {\n      if ((0, _iserror.default)(err) && err.message.indexOf(\"circular structure\") !== -1) {\n        throw new Error(`Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`);\n      }\n      throw err;\n    }\n  }\n  render() {\n    const {\n      assetPrefix,\n      inAmpMode,\n      buildManifest,\n      unstable_runtimeJS,\n      docComponentsRendered,\n      assetQueryString,\n      disableOptimizedLoading,\n      crossOrigin\n    } = this.context;\n    const disableRuntimeJS = unstable_runtimeJS === false;\n    docComponentsRendered.NextScript = true;\n    if ( true && inAmpMode) {\n      if (false) {}\n      const ampDevFiles = [...buildManifest.devFiles, ...buildManifest.polyfillFiles, ...buildManifest.ampDevFiles];\n      return /*#__PURE__*/(0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [disableRuntimeJS ? null : /*#__PURE__*/(0, _jsxruntime.jsx)(\"script\", {\n          id: \"__NEXT_DATA__\",\n          type: \"application/json\",\n          nonce: this.props.nonce,\n          crossOrigin: this.props.crossOrigin || crossOrigin,\n          dangerouslySetInnerHTML: {\n            __html: NextScript.getInlineScriptSource(this.context)\n          },\n          \"data-ampdevmode\": true\n        }), ampDevFiles.map(file => /*#__PURE__*/(0, _jsxruntime.jsx)(\"script\", {\n          src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n          nonce: this.props.nonce,\n          crossOrigin: this.props.crossOrigin || crossOrigin,\n          \"data-ampdevmode\": true\n        }, file))]\n      });\n    }\n    if (true) {\n      if (this.props.crossOrigin) console.warn(\"Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n    }\n    const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n    return /*#__PURE__*/(0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n      children: [!disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map(file => /*#__PURE__*/(0, _jsxruntime.jsx)(\"script\", {\n        src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n        nonce: this.props.nonce,\n        crossOrigin: this.props.crossOrigin || crossOrigin\n      }, file)) : null, disableRuntimeJS ? null : /*#__PURE__*/(0, _jsxruntime.jsx)(\"script\", {\n        id: \"__NEXT_DATA__\",\n        type: \"application/json\",\n        nonce: this.props.nonce,\n        crossOrigin: this.props.crossOrigin || crossOrigin,\n        dangerouslySetInnerHTML: {\n          __html: NextScript.getInlineScriptSource(this.context)\n        }\n      }), disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files)]\n    });\n  }\n}\nfunction Html(props) {\n  const {\n    inAmpMode,\n    docComponentsRendered,\n    locale,\n    scriptLoader,\n    __NEXT_DATA__\n  } = (0, _htmlcontextsharedruntime.useHtmlContext)();\n  docComponentsRendered.Html = true;\n  handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props);\n  return /*#__PURE__*/(0, _jsxruntime.jsx)(\"html\", {\n    ...props,\n    lang: props.lang || locale || undefined,\n    amp:  true && inAmpMode ? \"\" : undefined,\n    \"data-ampdevmode\":  true && inAmpMode && true ? \"\" : undefined\n  });\n}\nfunction Main() {\n  const {\n    docComponentsRendered\n  } = (0, _htmlcontextsharedruntime.useHtmlContext)();\n  docComponentsRendered.Main = true;\n  // @ts-ignore\n  return /*#__PURE__*/(0, _jsxruntime.jsx)(\"next-js-internal-body-render-target\", {});\n}\nclass Document extends _react.default.Component {\n  /**\n  * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n  * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n  */\n  static getInitialProps(ctx) {\n    return ctx.defaultGetInitialProps(ctx);\n  }\n  render() {\n    return /*#__PURE__*/(0, _jsxruntime.jsxs)(Html, {\n      children: [/*#__PURE__*/(0, _jsxruntime.jsx)(Head, {}), /*#__PURE__*/(0, _jsxruntime.jsxs)(\"body\", {\n        children: [/*#__PURE__*/(0, _jsxruntime.jsx)(Main, {}), /*#__PURE__*/(0, _jsxruntime.jsx)(NextScript, {})]\n      })]\n    });\n  }\n}\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument = function InternalFunctionDocument() {\n  return /*#__PURE__*/(0, _jsxruntime.jsxs)(Html, {\n    children: [/*#__PURE__*/(0, _jsxruntime.jsx)(Head, {}), /*#__PURE__*/(0, _jsxruntime.jsxs)(\"body\", {\n      children: [/*#__PURE__*/(0, _jsxruntime.jsx)(Main, {}), /*#__PURE__*/(0, _jsxruntime.jsx)(NextScript, {})]\n    })]\n  });\n};\nDocument[_constants.NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/pages/_document.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/pages/_error.js":
/*!****************************************************!*\
  !*** ../../node_modules/next/dist/pages/_error.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n  enumerable: true,\n  get: function () {\n    return Error;\n  }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"../../node_modules/@swc/helpers/cjs/_interop_require_default.cjs\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\nconst _react = /*#__PURE__*/_interop_require_default._(__webpack_require__(/*! react */ \"react\"));\nconst _head = /*#__PURE__*/_interop_require_default._(__webpack_require__(/*! ../shared/lib/head */ \"../../node_modules/next/dist/shared/lib/head.js\"));\nconst statusCodes = {\n  400: \"Bad Request\",\n  404: \"This page could not be found\",\n  405: \"Method Not Allowed\",\n  500: \"Internal Server Error\"\n};\nfunction _getInitialProps(param) {\n  let {\n    res,\n    err\n  } = param;\n  const statusCode = res && res.statusCode ? res.statusCode : err ? err.statusCode : 404;\n  return {\n    statusCode\n  };\n}\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily: 'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: \"100vh\",\n    textAlign: \"center\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\"\n  },\n  desc: {\n    lineHeight: \"48px\"\n  },\n  h1: {\n    display: \"inline-block\",\n    margin: \"0 20px 0 0\",\n    paddingRight: 23,\n    fontSize: 24,\n    fontWeight: 500,\n    verticalAlign: \"top\"\n  },\n  h2: {\n    fontSize: 14,\n    fontWeight: 400,\n    lineHeight: \"28px\"\n  },\n  wrap: {\n    display: \"inline-block\"\n  }\n};\nclass Error extends _react.default.Component {\n  render() {\n    const {\n      statusCode,\n      withDarkMode = true\n    } = this.props;\n    const title = this.props.title || statusCodes[statusCode] || \"An unexpected error has occurred\";\n    return /*#__PURE__*/(0, _jsxruntime.jsxs)(\"div\", {\n      style: styles.error,\n      children: [/*#__PURE__*/(0, _jsxruntime.jsx)(_head.default, {\n        children: /*#__PURE__*/(0, _jsxruntime.jsx)(\"title\", {\n          children: statusCode ? statusCode + \": \" + title : \"Application error: a client-side exception has occurred\"\n        })\n      }), /*#__PURE__*/(0, _jsxruntime.jsxs)(\"div\", {\n        style: styles.desc,\n        children: [/*#__PURE__*/(0, _jsxruntime.jsx)(\"style\", {\n          dangerouslySetInnerHTML: {\n            /* CSS minified from\n            body { margin: 0; color: #000; background: #fff; }\n            .next-error-h1 {\n            border-right: 1px solid rgba(0, 0, 0, .3);\n            }\n            ${\n            withDarkMode\n            ? `@media (prefers-color-scheme: dark) {\n            body { color: #fff; background: #000; }\n            .next-error-h1 {\n            border-right: 1px solid rgba(255, 255, 255, .3);\n            }\n            }`\n            : ''\n            }\n            */\n            __html: \"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}\" + (withDarkMode ? \"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\" : \"\")\n          }\n        }), statusCode ? /*#__PURE__*/(0, _jsxruntime.jsx)(\"h1\", {\n          className: \"next-error-h1\",\n          style: styles.h1,\n          children: statusCode\n        }) : null, /*#__PURE__*/(0, _jsxruntime.jsx)(\"div\", {\n          style: styles.wrap,\n          children: /*#__PURE__*/(0, _jsxruntime.jsxs)(\"h2\", {\n            style: styles.h2,\n            children: [this.props.title || statusCode ? title : /*#__PURE__*/(0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n              children: \"Application error: a client-side exception has occurred (see the browser console for more information)\"\n            }), \".\"]\n          })\n        })]\n      })]\n    });\n  }\n}\nError.displayName = \"ErrorPage\";\nError.getInitialProps = _getInitialProps;\nError.origGetInitialProps = _getInitialProps;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n  Object.defineProperty(exports.default, '__esModule', {\n    value: true\n  });\n  Object.assign(exports.default, exports);\n  module.exports = exports.default;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/pages/_error.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/amp-mode.js":
/*!***********************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/amp-mode.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"isInAmpMode\", ({\n  enumerable: true,\n  get: function () {\n    return isInAmpMode;\n  }\n}));\nfunction isInAmpMode(param) {\n  let {\n    ampFirst = false,\n    hybrid = false,\n    hasQuery = false\n  } = param === void 0 ? {} : param;\n  return ampFirst || hybrid && hasQuery;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL2FtcC1tb2RlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUNiQSw4Q0FBNkM7RUFDekNHLEtBQUssRUFBRTtBQUNYLENBQUMsRUFBQztBQUNGSCwrQ0FBOEM7RUFDMUNJLFVBQVUsRUFBRSxJQUFJO0VBQ2hCQyxHQUFHLEVBQUUsU0FBQUEsQ0FBQSxFQUFXO0lBQ1osT0FBT0MsV0FBVztFQUN0QjtBQUNKLENBQUMsRUFBQztBQUNGLFNBQVNBLFdBQVdBLENBQUNDLEtBQUssRUFBRTtFQUN4QixJQUFJO0lBQUVDLFFBQVEsR0FBRyxLQUFLO0lBQUVDLE1BQU0sR0FBRyxLQUFLO0lBQUVDLFFBQVEsR0FBRztFQUFNLENBQUMsR0FBR0gsS0FBSyxLQUFLLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHQSxLQUFLO0VBQzFGLE9BQU9DLFFBQVEsSUFBSUMsTUFBTSxJQUFJQyxRQUFRO0FBQ3pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va296eXItbWFzdGVyLXdlYi8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvYW1wLW1vZGUuanM/MjEzNSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImlzSW5BbXBNb2RlXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBpc0luQW1wTW9kZTtcbiAgICB9XG59KTtcbmZ1bmN0aW9uIGlzSW5BbXBNb2RlKHBhcmFtKSB7XG4gICAgbGV0IHsgYW1wRmlyc3QgPSBmYWxzZSwgaHlicmlkID0gZmFsc2UsIGhhc1F1ZXJ5ID0gZmFsc2UgfSA9IHBhcmFtID09PSB2b2lkIDAgPyB7fSA6IHBhcmFtO1xuICAgIHJldHVybiBhbXBGaXJzdCB8fCBoeWJyaWQgJiYgaGFzUXVlcnk7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFtcC1tb2RlLmpzLm1hcCJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsImVudW1lcmFibGUiLCJnZXQiLCJpc0luQW1wTW9kZSIsInBhcmFtIiwiYW1wRmlyc3QiLCJoeWJyaWQiLCJoYXNRdWVyeSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/amp-mode.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/constants.js":
/*!************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/constants.js ***!
  \************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  APP_BUILD_MANIFEST: function () {\n    return APP_BUILD_MANIFEST;\n  },\n  APP_CLIENT_INTERNALS: function () {\n    return APP_CLIENT_INTERNALS;\n  },\n  APP_PATHS_MANIFEST: function () {\n    return APP_PATHS_MANIFEST;\n  },\n  APP_PATH_ROUTES_MANIFEST: function () {\n    return APP_PATH_ROUTES_MANIFEST;\n  },\n  AUTOMATIC_FONT_OPTIMIZATION_MANIFEST: function () {\n    return AUTOMATIC_FONT_OPTIMIZATION_MANIFEST;\n  },\n  BARREL_OPTIMIZATION_PREFIX: function () {\n    return BARREL_OPTIMIZATION_PREFIX;\n  },\n  BLOCKED_PAGES: function () {\n    return BLOCKED_PAGES;\n  },\n  BUILD_ID_FILE: function () {\n    return BUILD_ID_FILE;\n  },\n  BUILD_MANIFEST: function () {\n    return BUILD_MANIFEST;\n  },\n  CLIENT_PUBLIC_FILES_PATH: function () {\n    return CLIENT_PUBLIC_FILES_PATH;\n  },\n  CLIENT_REFERENCE_MANIFEST: function () {\n    return CLIENT_REFERENCE_MANIFEST;\n  },\n  CLIENT_STATIC_FILES_PATH: function () {\n    return CLIENT_STATIC_FILES_PATH;\n  },\n  CLIENT_STATIC_FILES_RUNTIME_AMP: function () {\n    return CLIENT_STATIC_FILES_RUNTIME_AMP;\n  },\n  CLIENT_STATIC_FILES_RUNTIME_MAIN: function () {\n    return CLIENT_STATIC_FILES_RUNTIME_MAIN;\n  },\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP: function () {\n    return CLIENT_STATIC_FILES_RUNTIME_MAIN_APP;\n  },\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS: function () {\n    return CLIENT_STATIC_FILES_RUNTIME_POLYFILLS;\n  },\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL: function () {\n    return CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL;\n  },\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH: function () {\n    return CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH;\n  },\n  CLIENT_STATIC_FILES_RUNTIME_WEBPACK: function () {\n    return CLIENT_STATIC_FILES_RUNTIME_WEBPACK;\n  },\n  COMPILER_INDEXES: function () {\n    return COMPILER_INDEXES;\n  },\n  COMPILER_NAMES: function () {\n    return COMPILER_NAMES;\n  },\n  CONFIG_FILES: function () {\n    return CONFIG_FILES;\n  },\n  DEFAULT_RUNTIME_WEBPACK: function () {\n    return DEFAULT_RUNTIME_WEBPACK;\n  },\n  DEFAULT_SANS_SERIF_FONT: function () {\n    return DEFAULT_SANS_SERIF_FONT;\n  },\n  DEFAULT_SERIF_FONT: function () {\n    return DEFAULT_SERIF_FONT;\n  },\n  DEV_CLIENT_PAGES_MANIFEST: function () {\n    return DEV_CLIENT_PAGES_MANIFEST;\n  },\n  DEV_MIDDLEWARE_MANIFEST: function () {\n    return DEV_MIDDLEWARE_MANIFEST;\n  },\n  EDGE_RUNTIME_WEBPACK: function () {\n    return EDGE_RUNTIME_WEBPACK;\n  },\n  EDGE_UNSUPPORTED_NODE_APIS: function () {\n    return EDGE_UNSUPPORTED_NODE_APIS;\n  },\n  EXPORT_DETAIL: function () {\n    return EXPORT_DETAIL;\n  },\n  EXPORT_MARKER: function () {\n    return EXPORT_MARKER;\n  },\n  FUNCTIONS_CONFIG_MANIFEST: function () {\n    return FUNCTIONS_CONFIG_MANIFEST;\n  },\n  GOOGLE_FONT_PROVIDER: function () {\n    return GOOGLE_FONT_PROVIDER;\n  },\n  IMAGES_MANIFEST: function () {\n    return IMAGES_MANIFEST;\n  },\n  INTERCEPTION_ROUTE_REWRITE_MANIFEST: function () {\n    return INTERCEPTION_ROUTE_REWRITE_MANIFEST;\n  },\n  MIDDLEWARE_BUILD_MANIFEST: function () {\n    return MIDDLEWARE_BUILD_MANIFEST;\n  },\n  MIDDLEWARE_MANIFEST: function () {\n    return MIDDLEWARE_MANIFEST;\n  },\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST: function () {\n    return MIDDLEWARE_REACT_LOADABLE_MANIFEST;\n  },\n  MODERN_BROWSERSLIST_TARGET: function () {\n    return _modernbrowserslisttarget.default;\n  },\n  NEXT_BUILTIN_DOCUMENT: function () {\n    return NEXT_BUILTIN_DOCUMENT;\n  },\n  NEXT_FONT_MANIFEST: function () {\n    return NEXT_FONT_MANIFEST;\n  },\n  OPTIMIZED_FONT_PROVIDERS: function () {\n    return OPTIMIZED_FONT_PROVIDERS;\n  },\n  PAGES_MANIFEST: function () {\n    return PAGES_MANIFEST;\n  },\n  PHASE_DEVELOPMENT_SERVER: function () {\n    return PHASE_DEVELOPMENT_SERVER;\n  },\n  PHASE_EXPORT: function () {\n    return PHASE_EXPORT;\n  },\n  PHASE_INFO: function () {\n    return PHASE_INFO;\n  },\n  PHASE_PRODUCTION_BUILD: function () {\n    return PHASE_PRODUCTION_BUILD;\n  },\n  PHASE_PRODUCTION_SERVER: function () {\n    return PHASE_PRODUCTION_SERVER;\n  },\n  PHASE_TEST: function () {\n    return PHASE_TEST;\n  },\n  PRERENDER_MANIFEST: function () {\n    return PRERENDER_MANIFEST;\n  },\n  REACT_LOADABLE_MANIFEST: function () {\n    return REACT_LOADABLE_MANIFEST;\n  },\n  ROUTES_MANIFEST: function () {\n    return ROUTES_MANIFEST;\n  },\n  RSC_MODULE_TYPES: function () {\n    return RSC_MODULE_TYPES;\n  },\n  SERVER_DIRECTORY: function () {\n    return SERVER_DIRECTORY;\n  },\n  SERVER_FILES_MANIFEST: function () {\n    return SERVER_FILES_MANIFEST;\n  },\n  SERVER_PROPS_ID: function () {\n    return SERVER_PROPS_ID;\n  },\n  SERVER_REFERENCE_MANIFEST: function () {\n    return SERVER_REFERENCE_MANIFEST;\n  },\n  STATIC_PROPS_ID: function () {\n    return STATIC_PROPS_ID;\n  },\n  STATIC_STATUS_PAGES: function () {\n    return STATIC_STATUS_PAGES;\n  },\n  STRING_LITERAL_DROP_BUNDLE: function () {\n    return STRING_LITERAL_DROP_BUNDLE;\n  },\n  SUBRESOURCE_INTEGRITY_MANIFEST: function () {\n    return SUBRESOURCE_INTEGRITY_MANIFEST;\n  },\n  SYSTEM_ENTRYPOINTS: function () {\n    return SYSTEM_ENTRYPOINTS;\n  },\n  TRACE_OUTPUT_VERSION: function () {\n    return TRACE_OUTPUT_VERSION;\n  },\n  TURBO_TRACE_DEFAULT_MEMORY_LIMIT: function () {\n    return TURBO_TRACE_DEFAULT_MEMORY_LIMIT;\n  },\n  UNDERSCORE_NOT_FOUND_ROUTE: function () {\n    return UNDERSCORE_NOT_FOUND_ROUTE;\n  },\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY: function () {\n    return UNDERSCORE_NOT_FOUND_ROUTE_ENTRY;\n  }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"../../node_modules/@swc/helpers/cjs/_interop_require_default.cjs\");\nconst _modernbrowserslisttarget = /*#__PURE__*/_interop_require_default._(__webpack_require__(/*! ./modern-browserslist-target */ \"../../node_modules/next/dist/shared/lib/modern-browserslist-target.js\"));\nconst COMPILER_NAMES = {\n  client: \"client\",\n  server: \"server\",\n  edgeServer: \"edge-server\"\n};\nconst COMPILER_INDEXES = {\n  [COMPILER_NAMES.client]: 0,\n  [COMPILER_NAMES.server]: 1,\n  [COMPILER_NAMES.edgeServer]: 2\n};\nconst UNDERSCORE_NOT_FOUND_ROUTE = \"/_not-found\";\nconst UNDERSCORE_NOT_FOUND_ROUTE_ENTRY = \"\" + UNDERSCORE_NOT_FOUND_ROUTE + \"/page\";\nconst PHASE_EXPORT = \"phase-export\";\nconst PHASE_PRODUCTION_BUILD = \"phase-production-build\";\nconst PHASE_PRODUCTION_SERVER = \"phase-production-server\";\nconst PHASE_DEVELOPMENT_SERVER = \"phase-development-server\";\nconst PHASE_TEST = \"phase-test\";\nconst PHASE_INFO = \"phase-info\";\nconst PAGES_MANIFEST = \"pages-manifest.json\";\nconst APP_PATHS_MANIFEST = \"app-paths-manifest.json\";\nconst APP_PATH_ROUTES_MANIFEST = \"app-path-routes-manifest.json\";\nconst BUILD_MANIFEST = \"build-manifest.json\";\nconst APP_BUILD_MANIFEST = \"app-build-manifest.json\";\nconst FUNCTIONS_CONFIG_MANIFEST = \"functions-config-manifest.json\";\nconst SUBRESOURCE_INTEGRITY_MANIFEST = \"subresource-integrity-manifest\";\nconst NEXT_FONT_MANIFEST = \"next-font-manifest\";\nconst EXPORT_MARKER = \"export-marker.json\";\nconst EXPORT_DETAIL = \"export-detail.json\";\nconst PRERENDER_MANIFEST = \"prerender-manifest.json\";\nconst ROUTES_MANIFEST = \"routes-manifest.json\";\nconst IMAGES_MANIFEST = \"images-manifest.json\";\nconst SERVER_FILES_MANIFEST = \"required-server-files.json\";\nconst DEV_CLIENT_PAGES_MANIFEST = \"_devPagesManifest.json\";\nconst MIDDLEWARE_MANIFEST = \"middleware-manifest.json\";\nconst DEV_MIDDLEWARE_MANIFEST = \"_devMiddlewareManifest.json\";\nconst REACT_LOADABLE_MANIFEST = \"react-loadable-manifest.json\";\nconst AUTOMATIC_FONT_OPTIMIZATION_MANIFEST = \"font-manifest.json\";\nconst SERVER_DIRECTORY = \"server\";\nconst CONFIG_FILES = [\"next.config.js\", \"next.config.mjs\"];\nconst BUILD_ID_FILE = \"BUILD_ID\";\nconst BLOCKED_PAGES = [\"/_document\", \"/_app\", \"/_error\"];\nconst CLIENT_PUBLIC_FILES_PATH = \"public\";\nconst CLIENT_STATIC_FILES_PATH = \"static\";\nconst STRING_LITERAL_DROP_BUNDLE = \"__NEXT_DROP_CLIENT_FILE__\";\nconst NEXT_BUILTIN_DOCUMENT = \"__NEXT_BUILTIN_DOCUMENT__\";\nconst BARREL_OPTIMIZATION_PREFIX = \"__barrel_optimize__\";\nconst CLIENT_REFERENCE_MANIFEST = \"client-reference-manifest\";\nconst SERVER_REFERENCE_MANIFEST = \"server-reference-manifest\";\nconst MIDDLEWARE_BUILD_MANIFEST = \"middleware-build-manifest\";\nconst MIDDLEWARE_REACT_LOADABLE_MANIFEST = \"middleware-react-loadable-manifest\";\nconst INTERCEPTION_ROUTE_REWRITE_MANIFEST = \"interception-route-rewrite-manifest\";\nconst CLIENT_STATIC_FILES_RUNTIME_MAIN = \"main\";\nconst CLIENT_STATIC_FILES_RUNTIME_MAIN_APP = \"\" + CLIENT_STATIC_FILES_RUNTIME_MAIN + \"-app\";\nconst APP_CLIENT_INTERNALS = \"app-pages-internals\";\nconst CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH = \"react-refresh\";\nconst CLIENT_STATIC_FILES_RUNTIME_AMP = \"amp\";\nconst CLIENT_STATIC_FILES_RUNTIME_WEBPACK = \"webpack\";\nconst CLIENT_STATIC_FILES_RUNTIME_POLYFILLS = \"polyfills\";\nconst CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL = Symbol(CLIENT_STATIC_FILES_RUNTIME_POLYFILLS);\nconst DEFAULT_RUNTIME_WEBPACK = \"webpack-runtime\";\nconst EDGE_RUNTIME_WEBPACK = \"edge-runtime-webpack\";\nconst STATIC_PROPS_ID = \"__N_SSG\";\nconst SERVER_PROPS_ID = \"__N_SSP\";\nconst GOOGLE_FONT_PROVIDER = \"https://fonts.googleapis.com/\";\nconst OPTIMIZED_FONT_PROVIDERS = [{\n  url: GOOGLE_FONT_PROVIDER,\n  preconnect: \"https://fonts.gstatic.com\"\n}, {\n  url: \"https://use.typekit.net\",\n  preconnect: \"https://use.typekit.net\"\n}];\nconst DEFAULT_SERIF_FONT = {\n  name: \"Times New Roman\",\n  xAvgCharWidth: 821,\n  azAvgWidth: 854.3953488372093,\n  unitsPerEm: 2048\n};\nconst DEFAULT_SANS_SERIF_FONT = {\n  name: \"Arial\",\n  xAvgCharWidth: 904,\n  azAvgWidth: 934.5116279069767,\n  unitsPerEm: 2048\n};\nconst STATIC_STATUS_PAGES = [\"/500\"];\nconst TRACE_OUTPUT_VERSION = 1;\nconst TURBO_TRACE_DEFAULT_MEMORY_LIMIT = 6000;\nconst RSC_MODULE_TYPES = {\n  client: \"client\",\n  server: \"server\"\n};\nconst EDGE_UNSUPPORTED_NODE_APIS = [\"clearImmediate\", \"setImmediate\", \"BroadcastChannel\", \"ByteLengthQueuingStrategy\", \"CompressionStream\", \"CountQueuingStrategy\", \"DecompressionStream\", \"DomException\", \"MessageChannel\", \"MessageEvent\", \"MessagePort\", \"ReadableByteStreamController\", \"ReadableStreamBYOBRequest\", \"ReadableStreamDefaultController\", \"TransformStreamDefaultController\", \"WritableStreamDefaultController\"];\nconst SYSTEM_ENTRYPOINTS = new Set([CLIENT_STATIC_FILES_RUNTIME_MAIN, CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH, CLIENT_STATIC_FILES_RUNTIME_AMP, CLIENT_STATIC_FILES_RUNTIME_MAIN_APP]);\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n  Object.defineProperty(exports.default, '__esModule', {\n    value: true\n  });\n  Object.assign(exports.default, exports);\n  module.exports = exports.default;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/constants.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/encode-uri-path.js":
/*!******************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/encode-uri-path.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"encodeURIPath\", ({\n  enumerable: true,\n  get: function () {\n    return encodeURIPath;\n  }\n}));\nfunction encodeURIPath(file) {\n  return file.split(\"/\").map(p => encodeURIComponent(p)).join(\"/\");\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL2VuY29kZS11cmktcGF0aC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFDYkEsOENBQTZDO0VBQ3pDRyxLQUFLLEVBQUU7QUFDWCxDQUFDLEVBQUM7QUFDRkgsaURBQWdEO0VBQzVDSSxVQUFVLEVBQUUsSUFBSTtFQUNoQkMsR0FBRyxFQUFFLFNBQUFBLENBQUEsRUFBVztJQUNaLE9BQU9DLGFBQWE7RUFDeEI7QUFDSixDQUFDLEVBQUM7QUFDRixTQUFTQSxhQUFhQSxDQUFDQyxJQUFJLEVBQUU7RUFDekIsT0FBT0EsSUFBSSxDQUFDQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUNDLEdBQUcsQ0FBRUMsQ0FBQyxJQUFHQyxrQkFBa0IsQ0FBQ0QsQ0FBQyxDQUFDLENBQUMsQ0FBQ0UsSUFBSSxDQUFDLEdBQUcsQ0FBQztBQUNwRSIsInNvdXJjZXMiOlsid2VicGFjazovL2tvenlyLW1hc3Rlci13ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL2VuY29kZS11cmktcGF0aC5qcz9iZGJkIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiZW5jb2RlVVJJUGF0aFwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gZW5jb2RlVVJJUGF0aDtcbiAgICB9XG59KTtcbmZ1bmN0aW9uIGVuY29kZVVSSVBhdGgoZmlsZSkge1xuICAgIHJldHVybiBmaWxlLnNwbGl0KFwiL1wiKS5tYXAoKHApPT5lbmNvZGVVUklDb21wb25lbnQocCkpLmpvaW4oXCIvXCIpO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1lbmNvZGUtdXJpLXBhdGguanMubWFwIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiZW51bWVyYWJsZSIsImdldCIsImVuY29kZVVSSVBhdGgiLCJmaWxlIiwic3BsaXQiLCJtYXAiLCJwIiwiZW5jb2RlVVJJQ29tcG9uZW50Iiwiam9pbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/encode-uri-path.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/head.js":
/*!*******************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/head.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\"use client\";\n\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  default: function () {\n    return _default;\n  },\n  defaultHead: function () {\n    return defaultHead;\n  }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"../../node_modules/@swc/helpers/cjs/_interop_require_default.cjs\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"../../node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\nconst _react = /*#__PURE__*/_interop_require_wildcard._(__webpack_require__(/*! react */ \"react\"));\nconst _sideeffect = /*#__PURE__*/_interop_require_default._(__webpack_require__(/*! ./side-effect */ \"../../node_modules/next/dist/shared/lib/side-effect.js\"));\nconst _ampcontextsharedruntime = __webpack_require__(/*! ./amp-context.shared-runtime */ \"../../node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/amp-context.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ./head-manager-context.shared-runtime */ \"../../node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/head-manager-context.js\");\nconst _ampmode = __webpack_require__(/*! ./amp-mode */ \"../../node_modules/next/dist/shared/lib/amp-mode.js\");\nconst _warnonce = __webpack_require__(/*! ./utils/warn-once */ \"../../node_modules/next/dist/shared/lib/utils/warn-once.js\");\nfunction defaultHead(inAmpMode) {\n  if (inAmpMode === void 0) inAmpMode = false;\n  const head = [/*#__PURE__*/(0, _jsxruntime.jsx)(\"meta\", {\n    charSet: \"utf-8\"\n  })];\n  if (!inAmpMode) {\n    head.push( /*#__PURE__*/(0, _jsxruntime.jsx)(\"meta\", {\n      name: \"viewport\",\n      content: \"width=device-width\"\n    }));\n  }\n  return head;\n}\nfunction onlyReactElement(list, child) {\n  // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n  if (typeof child === \"string\" || typeof child === \"number\") {\n    return list;\n  }\n  // Adds support for React.Fragment\n  if (child.type === _react.default.Fragment) {\n    return list.concat(\n    // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n    _react.default.Children.toArray(child.props.children).reduce(\n    // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n    (fragmentList, fragmentChild) => {\n      if (typeof fragmentChild === \"string\" || typeof fragmentChild === \"number\") {\n        return fragmentList;\n      }\n      return fragmentList.concat(fragmentChild);\n    }, []));\n  }\n  return list.concat(child);\n}\nconst METATYPES = [\"name\", \"httpEquiv\", \"charSet\", \"itemProp\"];\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/\nfunction unique() {\n  const keys = new Set();\n  const tags = new Set();\n  const metaTypes = new Set();\n  const metaCategories = {};\n  return h => {\n    let isUnique = true;\n    let hasKey = false;\n    if (h.key && typeof h.key !== \"number\" && h.key.indexOf(\"$\") > 0) {\n      hasKey = true;\n      const key = h.key.slice(h.key.indexOf(\"$\") + 1);\n      if (keys.has(key)) {\n        isUnique = false;\n      } else {\n        keys.add(key);\n      }\n    }\n    // eslint-disable-next-line default-case\n    switch (h.type) {\n      case \"title\":\n      case \"base\":\n        if (tags.has(h.type)) {\n          isUnique = false;\n        } else {\n          tags.add(h.type);\n        }\n        break;\n      case \"meta\":\n        for (let i = 0, len = METATYPES.length; i < len; i++) {\n          const metatype = METATYPES[i];\n          if (!h.props.hasOwnProperty(metatype)) continue;\n          if (metatype === \"charSet\") {\n            if (metaTypes.has(metatype)) {\n              isUnique = false;\n            } else {\n              metaTypes.add(metatype);\n            }\n          } else {\n            const category = h.props[metatype];\n            const categories = metaCategories[metatype] || new Set();\n            if ((metatype !== \"name\" || !hasKey) && categories.has(category)) {\n              isUnique = false;\n            } else {\n              categories.add(category);\n              metaCategories[metatype] = categories;\n            }\n          }\n        }\n        break;\n    }\n    return isUnique;\n  };\n}\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */\nfunction reduceComponents(headChildrenElements, props) {\n  const {\n    inAmpMode\n  } = props;\n  return headChildrenElements.reduce(onlyReactElement, []).reverse().concat(defaultHead(inAmpMode).reverse()).filter(unique()).reverse().map((c, i) => {\n    const key = c.key || i;\n    if (false) {}\n    if (true) {\n      // omit JSON-LD structured data snippets from the warning\n      if (c.type === \"script\" && c.props[\"type\"] !== \"application/ld+json\") {\n        const srcMessage = c.props[\"src\"] ? '<script> tag with src=\"' + c.props[\"src\"] + '\"' : \"inline <script>\";\n        (0, _warnonce.warnOnce)(\"Do not add <script> tags using next/head (see \" + srcMessage + \"). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component\");\n      } else if (c.type === \"link\" && c.props[\"rel\"] === \"stylesheet\") {\n        (0, _warnonce.warnOnce)('Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"' + c.props[\"href\"] + '\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component');\n      }\n    }\n    return /*#__PURE__*/_react.default.cloneElement(c, {\n      key\n    });\n  });\n}\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */\nfunction Head(param) {\n  let {\n    children\n  } = param;\n  const ampState = (0, _react.useContext)(_ampcontextsharedruntime.AmpStateContext);\n  const headManager = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);\n  return /*#__PURE__*/(0, _jsxruntime.jsx)(_sideeffect.default, {\n    reduceComponentsToState: reduceComponents,\n    headManager: headManager,\n    inAmpMode: (0, _ampmode.isInAmpMode)(ampState),\n    children: children\n  });\n}\nconst _default = Head;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n  Object.defineProperty(exports.default, '__esModule', {\n    value: true\n  });\n  Object.assign(exports.default, exports);\n  module.exports = exports.default;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/head.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/is-plain-object.js":
/*!******************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/is-plain-object.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  getObjectClassLabel: function () {\n    return getObjectClassLabel;\n  },\n  isPlainObject: function () {\n    return isPlainObject;\n  }\n});\nfunction getObjectClassLabel(value) {\n  return Object.prototype.toString.call(value);\n}\nfunction isPlainObject(value) {\n  if (getObjectClassLabel(value) !== \"[object Object]\") {\n    return false;\n  }\n  const prototype = Object.getPrototypeOf(value);\n  /**\n  * this used to be previously:\n  *\n  * `return prototype === null || prototype === Object.prototype`\n  *\n  * but Edge Runtime expose Object from vm, being that kind of type-checking wrongly fail.\n  *\n  * It was changed to the current implementation since it's resilient to serialization.\n  */\n  return prototype === null || prototype.hasOwnProperty(\"isPrototypeOf\");\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/is-plain-object.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/modern-browserslist-target.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/modern-browserslist-target.js ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
eval("// Note: This file is JS because it's used by the taskfile-swc.js file, which is JS.\n// Keep file changes in sync with the corresponding `.d.ts` files.\n/**\n * These are the browser versions that support all of the following:\n * static import: https://caniuse.com/es6-module\n * dynamic import: https://caniuse.com/es6-module-dynamic-import\n * import.meta: https://caniuse.com/mdn-javascript_operators_import_meta\n */\n\nconst MODERN_BROWSERSLIST_TARGET = [\"chrome 64\", \"edge 79\", \"firefox 67\", \"opera 51\", \"safari 12\"];\nmodule.exports = MODERN_BROWSERSLIST_TARGET;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL21vZGVybi1icm93c2Vyc2xpc3QtdGFyZ2V0LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBaUI7O0FBQ2pCLE1BQU1BLDBCQUEwQixHQUFHLENBQy9CLFdBQVcsRUFDWCxTQUFTLEVBQ1QsWUFBWSxFQUNaLFVBQVUsRUFDVixXQUFXLENBQ2Q7QUFDREMsTUFBTSxDQUFDQyxPQUFPLEdBQUdGLDBCQUEwQiIsInNvdXJjZXMiOlsid2VicGFjazovL2tvenlyLW1hc3Rlci13ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL21vZGVybi1icm93c2Vyc2xpc3QtdGFyZ2V0LmpzP2UwYWUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gTm90ZTogVGhpcyBmaWxlIGlzIEpTIGJlY2F1c2UgaXQncyB1c2VkIGJ5IHRoZSB0YXNrZmlsZS1zd2MuanMgZmlsZSwgd2hpY2ggaXMgSlMuXG4vLyBLZWVwIGZpbGUgY2hhbmdlcyBpbiBzeW5jIHdpdGggdGhlIGNvcnJlc3BvbmRpbmcgYC5kLnRzYCBmaWxlcy5cbi8qKlxuICogVGhlc2UgYXJlIHRoZSBicm93c2VyIHZlcnNpb25zIHRoYXQgc3VwcG9ydCBhbGwgb2YgdGhlIGZvbGxvd2luZzpcbiAqIHN0YXRpYyBpbXBvcnQ6IGh0dHBzOi8vY2FuaXVzZS5jb20vZXM2LW1vZHVsZVxuICogZHluYW1pYyBpbXBvcnQ6IGh0dHBzOi8vY2FuaXVzZS5jb20vZXM2LW1vZHVsZS1keW5hbWljLWltcG9ydFxuICogaW1wb3J0Lm1ldGE6IGh0dHBzOi8vY2FuaXVzZS5jb20vbWRuLWphdmFzY3JpcHRfb3BlcmF0b3JzX2ltcG9ydF9tZXRhXG4gKi8gXCJ1c2Ugc3RyaWN0XCI7XG5jb25zdCBNT0RFUk5fQlJPV1NFUlNMSVNUX1RBUkdFVCA9IFtcbiAgICBcImNocm9tZSA2NFwiLFxuICAgIFwiZWRnZSA3OVwiLFxuICAgIFwiZmlyZWZveCA2N1wiLFxuICAgIFwib3BlcmEgNTFcIixcbiAgICBcInNhZmFyaSAxMlwiXG5dO1xubW9kdWxlLmV4cG9ydHMgPSBNT0RFUk5fQlJPV1NFUlNMSVNUX1RBUkdFVDtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bW9kZXJuLWJyb3dzZXJzbGlzdC10YXJnZXQuanMubWFwIl0sIm5hbWVzIjpbIk1PREVSTl9CUk9XU0VSU0xJU1RfVEFSR0VUIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/modern-browserslist-target.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"denormalizePagePath\", ({\n  enumerable: true,\n  get: function () {\n    return denormalizePagePath;\n  }\n}));\nconst _utils = __webpack_require__(/*! ../router/utils */ \"../../node_modules/next/dist/shared/lib/router/utils/index.js\");\nconst _normalizepathsep = __webpack_require__(/*! ./normalize-path-sep */ \"../../node_modules/next/dist/shared/lib/page-path/normalize-path-sep.js\");\nfunction denormalizePagePath(page) {\n  let _page = (0, _normalizepathsep.normalizePathSep)(page);\n  return _page.startsWith(\"/index/\") && !(0, _utils.isDynamicRoute)(_page) ? _page.slice(6) : _page !== \"/index\" ? _page : \"/\";\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js":
/*!*********************************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"ensureLeadingSlash\", ({\n  enumerable: true,\n  get: function () {\n    return ensureLeadingSlash;\n  }\n}));\nfunction ensureLeadingSlash(path) {\n  return path.startsWith(\"/\") ? path : \"/\" + path;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL3BhZ2UtcGF0aC9lbnN1cmUtbGVhZGluZy1zbGFzaC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSxHQUFpQjs7QUFDakJBLDhDQUE2QztFQUN6Q0csS0FBSyxFQUFFO0FBQ1gsQ0FBQyxFQUFDO0FBQ0ZILHNEQUFxRDtFQUNqREksVUFBVSxFQUFFLElBQUk7RUFDaEJDLEdBQUcsRUFBRSxTQUFBQSxDQUFBLEVBQVc7SUFDWixPQUFPQyxrQkFBa0I7RUFDN0I7QUFDSixDQUFDLEVBQUM7QUFDRixTQUFTQSxrQkFBa0JBLENBQUNDLElBQUksRUFBRTtFQUM5QixPQUFPQSxJQUFJLENBQUNDLFVBQVUsQ0FBQyxHQUFHLENBQUMsR0FBR0QsSUFBSSxHQUFHLEdBQUcsR0FBR0EsSUFBSTtBQUNuRCIsInNvdXJjZXMiOlsid2VicGFjazovL2tvenlyLW1hc3Rlci13ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL3BhZ2UtcGF0aC9lbnN1cmUtbGVhZGluZy1zbGFzaC5qcz9mNTA4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRm9yIGEgZ2l2ZW4gcGFnZSBwYXRoLCB0aGlzIGZ1bmN0aW9uIGVuc3VyZXMgdGhhdCB0aGVyZSBpcyBhIGxlYWRpbmcgc2xhc2guXG4gKiBJZiB0aGVyZSBpcyBub3QgYSBsZWFkaW5nIHNsYXNoLCBvbmUgaXMgYWRkZWQsIG90aGVyd2lzZSBpdCBpcyBub29wLlxuICovIFwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiZW5zdXJlTGVhZGluZ1NsYXNoXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBlbnN1cmVMZWFkaW5nU2xhc2g7XG4gICAgfVxufSk7XG5mdW5jdGlvbiBlbnN1cmVMZWFkaW5nU2xhc2gocGF0aCkge1xuICAgIHJldHVybiBwYXRoLnN0YXJ0c1dpdGgoXCIvXCIpID8gcGF0aCA6IFwiL1wiICsgcGF0aDtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZW5zdXJlLWxlYWRpbmctc2xhc2guanMubWFwIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiZW51bWVyYWJsZSIsImdldCIsImVuc3VyZUxlYWRpbmdTbGFzaCIsInBhdGgiLCJzdGFydHNXaXRoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/page-path/normalize-page-path.js":
/*!********************************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/page-path/normalize-page-path.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"normalizePagePath\", ({\n  enumerable: true,\n  get: function () {\n    return normalizePagePath;\n  }\n}));\nconst _ensureleadingslash = __webpack_require__(/*! ./ensure-leading-slash */ \"../../node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js\");\nconst _utils = __webpack_require__(/*! ../router/utils */ \"../../node_modules/next/dist/shared/lib/router/utils/index.js\");\nconst _utils1 = __webpack_require__(/*! ../utils */ \"../../node_modules/next/dist/shared/lib/utils.js\");\nfunction normalizePagePath(page) {\n  const normalized = /^\\/index(\\/|$)/.test(page) && !(0, _utils.isDynamicRoute)(page) ? \"/index\" + page : page === \"/\" ? \"/index\" : (0, _ensureleadingslash.ensureLeadingSlash)(page);\n  if (true) {\n    const {\n      posix\n    } = __webpack_require__(/*! path */ \"path\");\n    const resolvedPage = posix.normalize(normalized);\n    if (resolvedPage !== normalized) {\n      throw new _utils1.NormalizeError(\"Requested and resolved page mismatch: \" + normalized + \" \" + resolvedPage);\n    }\n  }\n  return normalized;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/page-path/normalize-page-path.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/page-path/normalize-path-sep.js":
/*!*******************************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/page-path/normalize-path-sep.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("/**\n * For a given page path, this function ensures that there is no backslash\n * escaping slashes in the path. Example:\n *  - `foo\\/bar\\/baz` -> `foo/bar/baz`\n */\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"normalizePathSep\", ({\n  enumerable: true,\n  get: function () {\n    return normalizePathSep;\n  }\n}));\nfunction normalizePathSep(path) {\n  return path.replace(/\\\\/g, \"/\");\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL3BhZ2UtcGF0aC9ub3JtYWxpemUtcGF0aC1zZXAuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFpQjs7QUFDakJBLDhDQUE2QztFQUN6Q0csS0FBSyxFQUFFO0FBQ1gsQ0FBQyxFQUFDO0FBQ0ZILG9EQUFtRDtFQUMvQ0ksVUFBVSxFQUFFLElBQUk7RUFDaEJDLEdBQUcsRUFBRSxTQUFBQSxDQUFBLEVBQVc7SUFDWixPQUFPQyxnQkFBZ0I7RUFDM0I7QUFDSixDQUFDLEVBQUM7QUFDRixTQUFTQSxnQkFBZ0JBLENBQUNDLElBQUksRUFBRTtFQUM1QixPQUFPQSxJQUFJLENBQUNDLE9BQU8sQ0FBQyxLQUFLLEVBQUUsR0FBRyxDQUFDO0FBQ25DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va296eXItbWFzdGVyLXdlYi8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcGFnZS1wYXRoL25vcm1hbGl6ZS1wYXRoLXNlcC5qcz85MGJkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRm9yIGEgZ2l2ZW4gcGFnZSBwYXRoLCB0aGlzIGZ1bmN0aW9uIGVuc3VyZXMgdGhhdCB0aGVyZSBpcyBubyBiYWNrc2xhc2hcbiAqIGVzY2FwaW5nIHNsYXNoZXMgaW4gdGhlIHBhdGguIEV4YW1wbGU6XG4gKiAgLSBgZm9vXFwvYmFyXFwvYmF6YCAtPiBgZm9vL2Jhci9iYXpgXG4gKi8gXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJub3JtYWxpemVQYXRoU2VwXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBub3JtYWxpemVQYXRoU2VwO1xuICAgIH1cbn0pO1xuZnVuY3Rpb24gbm9ybWFsaXplUGF0aFNlcChwYXRoKSB7XG4gICAgcmV0dXJuIHBhdGgucmVwbGFjZSgvXFxcXC9nLCBcIi9cIik7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW5vcm1hbGl6ZS1wYXRoLXNlcC5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJlbnVtZXJhYmxlIiwiZ2V0Iiwibm9ybWFsaXplUGF0aFNlcCIsInBhdGgiLCJyZXBsYWNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/page-path/normalize-path-sep.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/router/utils/app-paths.js":
/*!*************************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/router/utils/app-paths.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  normalizeAppPath: function () {\n    return normalizeAppPath;\n  },\n  normalizeRscURL: function () {\n    return normalizeRscURL;\n  }\n});\nconst _ensureleadingslash = __webpack_require__(/*! ../../page-path/ensure-leading-slash */ \"../../node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js\");\nconst _segment = __webpack_require__(/*! ../../segment */ \"../../node_modules/next/dist/shared/lib/segment.js\");\nfunction normalizeAppPath(route) {\n  return (0, _ensureleadingslash.ensureLeadingSlash)(route.split(\"/\").reduce((pathname, segment, index, segments) => {\n    // Empty segments are ignored.\n    if (!segment) {\n      return pathname;\n    }\n    // Groups are ignored.\n    if ((0, _segment.isGroupSegment)(segment)) {\n      return pathname;\n    }\n    // Parallel segments are ignored.\n    if (segment[0] === \"@\") {\n      return pathname;\n    }\n    // The last segment (if it's a leaf) should be ignored.\n    if ((segment === \"page\" || segment === \"route\") && index === segments.length - 1) {\n      return pathname;\n    }\n    return pathname + \"/\" + segment;\n  }, \"\"));\n}\nfunction normalizeRscURL(url) {\n  return url.replace(/\\.rsc($|\\?)/,\n  // $1 ensures `?` is preserved\n  \"$1\");\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/router/utils/app-paths.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/router/utils/index.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/router/utils/index.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  getSortedRoutes: function () {\n    return _sortedroutes.getSortedRoutes;\n  },\n  isDynamicRoute: function () {\n    return _isdynamic.isDynamicRoute;\n  }\n});\nconst _sortedroutes = __webpack_require__(/*! ./sorted-routes */ \"../../node_modules/next/dist/shared/lib/router/utils/sorted-routes.js\");\nconst _isdynamic = __webpack_require__(/*! ./is-dynamic */ \"../../node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL3JvdXRlci91dGlscy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFDYkEsOENBQTZDO0VBQ3pDRyxLQUFLLEVBQUU7QUFDWCxDQUFDLEVBQUM7QUFDRixDQUFDLEtBQUtDLENBR0wsQ0FBQztBQUNGLFNBQVNHLE9BQU9BLENBQUNDLE1BQU0sRUFBRUMsR0FBRyxFQUFFO0VBQzFCLEtBQUksSUFBSUMsSUFBSSxJQUFJRCxHQUFHLEVBQUNULE1BQU0sQ0FBQ0MsY0FBYyxDQUFDTyxNQUFNLEVBQUVFLElBQUksRUFBRTtJQUNwREMsVUFBVSxFQUFFLElBQUk7SUFDaEJDLEdBQUcsRUFBRUgsR0FBRyxDQUFDQyxJQUFJO0VBQ2pCLENBQUMsQ0FBQztBQUNOO0FBQ0FILE9BQU8sQ0FBQ0wsT0FBTyxFQUFFO0VBQ2JHLGVBQWUsRUFBRSxTQUFBQSxDQUFBLEVBQVc7SUFDeEIsT0FBT1EsYUFBYSxDQUFDUixlQUFlO0VBQ3hDLENBQUM7RUFDREMsY0FBYyxFQUFFLFNBQUFBLENBQUEsRUFBVztJQUN2QixPQUFPUSxVQUFVLENBQUNSLGNBQWM7RUFDcEM7QUFDSixDQUFDLENBQUM7QUFDRixNQUFNTyxhQUFhLEdBQUdFLG1CQUFPLENBQUMsOEZBQWlCLENBQUM7QUFDaEQsTUFBTUQsVUFBVSxHQUFHQyxtQkFBTyxDQUFDLHdGQUFjLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb3p5ci1tYXN0ZXItd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaW5kZXguanM/NDNiNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbjAgJiYgKG1vZHVsZS5leHBvcnRzID0ge1xuICAgIGdldFNvcnRlZFJvdXRlczogbnVsbCxcbiAgICBpc0R5bmFtaWNSb3V0ZTogbnVsbFxufSk7XG5mdW5jdGlvbiBfZXhwb3J0KHRhcmdldCwgYWxsKSB7XG4gICAgZm9yKHZhciBuYW1lIGluIGFsbClPYmplY3QuZGVmaW5lUHJvcGVydHkodGFyZ2V0LCBuYW1lLCB7XG4gICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgIGdldDogYWxsW25hbWVdXG4gICAgfSk7XG59XG5fZXhwb3J0KGV4cG9ydHMsIHtcbiAgICBnZXRTb3J0ZWRSb3V0ZXM6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gX3NvcnRlZHJvdXRlcy5nZXRTb3J0ZWRSb3V0ZXM7XG4gICAgfSxcbiAgICBpc0R5bmFtaWNSb3V0ZTogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBfaXNkeW5hbWljLmlzRHluYW1pY1JvdXRlO1xuICAgIH1cbn0pO1xuY29uc3QgX3NvcnRlZHJvdXRlcyA9IHJlcXVpcmUoXCIuL3NvcnRlZC1yb3V0ZXNcIik7XG5jb25zdCBfaXNkeW5hbWljID0gcmVxdWlyZShcIi4vaXMtZHluYW1pY1wiKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwibW9kdWxlIiwiZ2V0U29ydGVkUm91dGVzIiwiaXNEeW5hbWljUm91dGUiLCJfZXhwb3J0IiwidGFyZ2V0IiwiYWxsIiwibmFtZSIsImVudW1lcmFibGUiLCJnZXQiLCJfc29ydGVkcm91dGVzIiwiX2lzZHluYW1pYyIsInJlcXVpcmUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/router/utils/index.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/router/utils/is-dynamic.js":
/*!**************************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/router/utils/is-dynamic.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"isDynamicRoute\", ({\n  enumerable: true,\n  get: function () {\n    return isDynamicRoute;\n  }\n}));\nconst _interceptionroutes = __webpack_require__(/*! ../../../../server/future/helpers/interception-routes */ \"../../node_modules/next/dist/server/future/helpers/interception-routes.js\");\n// Identify /[param]/ in route string\nconst TEST_ROUTE = /\\/\\[[^/]+?\\](?=\\/|$)/;\nfunction isDynamicRoute(route) {\n  if ((0, _interceptionroutes.isInterceptionRouteAppPath)(route)) {\n    route = (0, _interceptionroutes.extractInterceptionRouteInformation)(route).interceptedRoute;\n  }\n  return TEST_ROUTE.test(route);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/router/utils/sorted-routes.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/router/utils/sorted-routes.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"getSortedRoutes\", ({\n  enumerable: true,\n  get: function () {\n    return getSortedRoutes;\n  }\n}));\nclass UrlNode {\n  insert(urlPath) {\n    this._insert(urlPath.split(\"/\").filter(Boolean), [], false);\n  }\n  smoosh() {\n    return this._smoosh();\n  }\n  _smoosh(prefix) {\n    if (prefix === void 0) prefix = \"/\";\n    const childrenPaths = [...this.children.keys()].sort();\n    if (this.slugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf(\"[]\"), 1);\n    }\n    if (this.restSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf(\"[...]\"), 1);\n    }\n    if (this.optionalRestSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf(\"[[...]]\"), 1);\n    }\n    const routes = childrenPaths.map(c => this.children.get(c)._smoosh(\"\" + prefix + c + \"/\")).reduce((prev, curr) => [...prev, ...curr], []);\n    if (this.slugName !== null) {\n      routes.push(...this.children.get(\"[]\")._smoosh(prefix + \"[\" + this.slugName + \"]/\"));\n    }\n    if (!this.placeholder) {\n      const r = prefix === \"/\" ? \"/\" : prefix.slice(0, -1);\n      if (this.optionalRestSlugName != null) {\n        throw new Error('You cannot define a route with the same specificity as a optional catch-all route (\"' + r + '\" and \"' + r + \"[[...\" + this.optionalRestSlugName + ']]\").');\n      }\n      routes.unshift(r);\n    }\n    if (this.restSlugName !== null) {\n      routes.push(...this.children.get(\"[...]\")._smoosh(prefix + \"[...\" + this.restSlugName + \"]/\"));\n    }\n    if (this.optionalRestSlugName !== null) {\n      routes.push(...this.children.get(\"[[...]]\")._smoosh(prefix + \"[[...\" + this.optionalRestSlugName + \"]]/\"));\n    }\n    return routes;\n  }\n  _insert(urlPaths, slugNames, isCatchAll) {\n    if (urlPaths.length === 0) {\n      this.placeholder = false;\n      return;\n    }\n    if (isCatchAll) {\n      throw new Error(\"Catch-all must be the last part of the URL.\");\n    }\n    // The next segment in the urlPaths list\n    let nextSegment = urlPaths[0];\n    // Check if the segment matches `[something]`\n    if (nextSegment.startsWith(\"[\") && nextSegment.endsWith(\"]\")) {\n      // Strip `[` and `]`, leaving only `something`\n      let segmentName = nextSegment.slice(1, -1);\n      let isOptional = false;\n      if (segmentName.startsWith(\"[\") && segmentName.endsWith(\"]\")) {\n        // Strip optional `[` and `]`, leaving only `something`\n        segmentName = segmentName.slice(1, -1);\n        isOptional = true;\n      }\n      if (segmentName.startsWith(\"...\")) {\n        // Strip `...`, leaving only `something`\n        segmentName = segmentName.substring(3);\n        isCatchAll = true;\n      }\n      if (segmentName.startsWith(\"[\") || segmentName.endsWith(\"]\")) {\n        throw new Error(\"Segment names may not start or end with extra brackets ('\" + segmentName + \"').\");\n      }\n      if (segmentName.startsWith(\".\")) {\n        throw new Error(\"Segment names may not start with erroneous periods ('\" + segmentName + \"').\");\n      }\n      function handleSlug(previousSlug, nextSlug) {\n        if (previousSlug !== null) {\n          // If the specific segment already has a slug but the slug is not `something`\n          // This prevents collisions like:\n          // pages/[post]/index.js\n          // pages/[id]/index.js\n          // Because currently multiple dynamic params on the same segment level are not supported\n          if (previousSlug !== nextSlug) {\n            // TODO: This error seems to be confusing for users, needs an error link, the description can be based on above comment.\n            throw new Error(\"You cannot use different slug names for the same dynamic path ('\" + previousSlug + \"' !== '\" + nextSlug + \"').\");\n          }\n        }\n        slugNames.forEach(slug => {\n          if (slug === nextSlug) {\n            throw new Error('You cannot have the same slug name \"' + nextSlug + '\" repeat within a single dynamic path');\n          }\n          if (slug.replace(/\\W/g, \"\") === nextSegment.replace(/\\W/g, \"\")) {\n            throw new Error('You cannot have the slug names \"' + slug + '\" and \"' + nextSlug + '\" differ only by non-word symbols within a single dynamic path');\n          }\n        });\n        slugNames.push(nextSlug);\n      }\n      if (isCatchAll) {\n        if (isOptional) {\n          if (this.restSlugName != null) {\n            throw new Error('You cannot use both an required and optional catch-all route at the same level (\"[...' + this.restSlugName + ']\" and \"' + urlPaths[0] + '\" ).');\n          }\n          handleSlug(this.optionalRestSlugName, segmentName);\n          // slugName is kept as it can only be one particular slugName\n          this.optionalRestSlugName = segmentName;\n          // nextSegment is overwritten to [[...]] so that it can later be sorted specifically\n          nextSegment = \"[[...]]\";\n        } else {\n          if (this.optionalRestSlugName != null) {\n            throw new Error('You cannot use both an optional and required catch-all route at the same level (\"[[...' + this.optionalRestSlugName + ']]\" and \"' + urlPaths[0] + '\").');\n          }\n          handleSlug(this.restSlugName, segmentName);\n          // slugName is kept as it can only be one particular slugName\n          this.restSlugName = segmentName;\n          // nextSegment is overwritten to [...] so that it can later be sorted specifically\n          nextSegment = \"[...]\";\n        }\n      } else {\n        if (isOptional) {\n          throw new Error('Optional route parameters are not yet supported (\"' + urlPaths[0] + '\").');\n        }\n        handleSlug(this.slugName, segmentName);\n        // slugName is kept as it can only be one particular slugName\n        this.slugName = segmentName;\n        // nextSegment is overwritten to [] so that it can later be sorted specifically\n        nextSegment = \"[]\";\n      }\n    }\n    // If this UrlNode doesn't have the nextSegment yet we create a new child UrlNode\n    if (!this.children.has(nextSegment)) {\n      this.children.set(nextSegment, new UrlNode());\n    }\n    this.children.get(nextSegment)._insert(urlPaths.slice(1), slugNames, isCatchAll);\n  }\n  constructor() {\n    this.placeholder = true;\n    this.children = new Map();\n    this.slugName = null;\n    this.restSlugName = null;\n    this.optionalRestSlugName = null;\n  }\n}\nfunction getSortedRoutes(normalizedPages) {\n  // First the UrlNode is created, and every UrlNode can have only 1 dynamic segment\n  // Eg you can't have pages/[post]/abc.js and pages/[hello]/something-else.js\n  // Only 1 dynamic segment per nesting level\n  // So in the case that is test/integration/dynamic-routing it'll be this:\n  // pages/[post]/comments.js\n  // pages/blog/[post]/comment/[id].js\n  // Both are fine because `pages/[post]` and `pages/blog` are on the same level\n  // So in this case `UrlNode` created here has `this.slugName === 'post'`\n  // And since your PR passed through `slugName` as an array basically it'd including it in too many possibilities\n  // Instead what has to be passed through is the upwards path's dynamic names\n  const root = new UrlNode();\n  // Here the `root` gets injected multiple paths, and insert will break them up into sublevels\n  normalizedPages.forEach(pagePath => root.insert(pagePath));\n  // Smoosh will then sort those sublevels up to the point where you get the correct route definition priority\n  return root.smoosh();\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/router/utils/sorted-routes.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/segment.js":
/*!**********************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/segment.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  DEFAULT_SEGMENT_KEY: function () {\n    return DEFAULT_SEGMENT_KEY;\n  },\n  PAGE_SEGMENT_KEY: function () {\n    return PAGE_SEGMENT_KEY;\n  },\n  isGroupSegment: function () {\n    return isGroupSegment;\n  }\n});\nfunction isGroupSegment(segment) {\n  // Use array[0] for performant purpose\n  return segment[0] === \"(\" && segment.endsWith(\")\");\n}\nconst PAGE_SEGMENT_KEY = \"__PAGE__\";\nconst DEFAULT_SEGMENT_KEY = \"__DEFAULT__\";//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/segment.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/side-effect.js":
/*!**************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/side-effect.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n  enumerable: true,\n  get: function () {\n    return SideEffect;\n  }\n}));\nconst _react = __webpack_require__(/*! react */ \"react\");\nconst isServer = true;\nconst useClientOnlyLayoutEffect = isServer ? () => {} : _react.useLayoutEffect;\nconst useClientOnlyEffect = isServer ? () => {} : _react.useEffect;\nfunction SideEffect(props) {\n  const {\n    headManager,\n    reduceComponentsToState\n  } = props;\n  function emitChange() {\n    if (headManager && headManager.mountedInstances) {\n      const headElements = _react.Children.toArray(Array.from(headManager.mountedInstances).filter(Boolean));\n      headManager.updateHead(reduceComponentsToState(headElements, props));\n    }\n  }\n  if (isServer) {\n    var _headManager_mountedInstances;\n    headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n    emitChange();\n  }\n  useClientOnlyLayoutEffect(() => {\n    var _headManager_mountedInstances;\n    headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n    return () => {\n      var _headManager_mountedInstances;\n      headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.delete(props.children);\n    };\n  });\n  // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n  // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n  // being rendered, we only trigger the method from the last one.\n  // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n  // singleton in the layout effect pass, and actually trigger it in the effect pass.\n  useClientOnlyLayoutEffect(() => {\n    if (headManager) {\n      headManager._pendingUpdate = emitChange;\n    }\n    return () => {\n      if (headManager) {\n        headManager._pendingUpdate = emitChange;\n      }\n    };\n  });\n  useClientOnlyEffect(() => {\n    if (headManager && headManager._pendingUpdate) {\n      headManager._pendingUpdate();\n      headManager._pendingUpdate = null;\n    }\n    return () => {\n      if (headManager && headManager._pendingUpdate) {\n        headManager._pendingUpdate();\n        headManager._pendingUpdate = null;\n      }\n    };\n  });\n  return null;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/side-effect.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/utils.js":
/*!********************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/utils.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  DecodeError: function () {\n    return DecodeError;\n  },\n  MiddlewareNotFoundError: function () {\n    return MiddlewareNotFoundError;\n  },\n  MissingStaticPage: function () {\n    return MissingStaticPage;\n  },\n  NormalizeError: function () {\n    return NormalizeError;\n  },\n  PageNotFoundError: function () {\n    return PageNotFoundError;\n  },\n  SP: function () {\n    return SP;\n  },\n  ST: function () {\n    return ST;\n  },\n  WEB_VITALS: function () {\n    return WEB_VITALS;\n  },\n  execOnce: function () {\n    return execOnce;\n  },\n  getDisplayName: function () {\n    return getDisplayName;\n  },\n  getLocationOrigin: function () {\n    return getLocationOrigin;\n  },\n  getURL: function () {\n    return getURL;\n  },\n  isAbsoluteUrl: function () {\n    return isAbsoluteUrl;\n  },\n  isResSent: function () {\n    return isResSent;\n  },\n  loadGetInitialProps: function () {\n    return loadGetInitialProps;\n  },\n  normalizeRepeatedSlashes: function () {\n    return normalizeRepeatedSlashes;\n  },\n  stringifyError: function () {\n    return stringifyError;\n  }\n});\nconst WEB_VITALS = [\"CLS\", \"FCP\", \"FID\", \"INP\", \"LCP\", \"TTFB\"];\nfunction execOnce(fn) {\n  let used = false;\n  let result;\n  return function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (!used) {\n      used = true;\n      result = fn(...args);\n    }\n    return result;\n  };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = url => ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n  const {\n    protocol,\n    hostname,\n    port\n  } = window.location;\n  return protocol + \"//\" + hostname + (port ? \":\" + port : \"\");\n}\nfunction getURL() {\n  const {\n    href\n  } = window.location;\n  const origin = getLocationOrigin();\n  return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n  return typeof Component === \"string\" ? Component : Component.displayName || Component.name || \"Unknown\";\n}\nfunction isResSent(res) {\n  return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n  const urlParts = url.split(\"?\");\n  const urlNoQuery = urlParts[0];\n  return urlNoQuery // first we replace any non-encoded backslashes with forward\n  // then normalize repeated forward slashes\n  .replace(/\\\\/g, \"/\").replace(/\\/\\/+/g, \"/\") + (urlParts[1] ? \"?\" + urlParts.slice(1).join(\"?\") : \"\");\n}\nasync function loadGetInitialProps(App, ctx) {\n  if (true) {\n    var _App_prototype;\n    if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n      const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n      throw new Error(message);\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || ctx.ctx && ctx.ctx.res;\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n      };\n    }\n    return {};\n  }\n  const props = await App.getInitialProps(ctx);\n  if (res && isResSent(res)) {\n    return props;\n  }\n  if (!props) {\n    const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n    throw new Error(message);\n  }\n  if (true) {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n    }\n  }\n  return props;\n}\nconst SP = typeof performance !== \"undefined\";\nconst ST = SP && [\"mark\", \"measure\", \"getEntriesByName\"].every(method => typeof performance[method] === \"function\");\nclass DecodeError extends Error {}\nclass NormalizeError extends Error {}\nclass PageNotFoundError extends Error {\n  constructor(page) {\n    super();\n    this.code = \"ENOENT\";\n    this.name = \"PageNotFoundError\";\n    this.message = \"Cannot find module for page: \" + page;\n  }\n}\nclass MissingStaticPage extends Error {\n  constructor(page, message) {\n    super();\n    this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n  }\n}\nclass MiddlewareNotFoundError extends Error {\n  constructor() {\n    super();\n    this.code = \"ENOENT\";\n    this.message = \"Cannot find the middleware module\";\n  }\n}\nfunction stringifyError(error) {\n  return JSON.stringify({\n    message: error.message,\n    stack: error.stack\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL3V0aWxzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUNiQSw4Q0FBNkM7RUFDekNHLEtBQUssRUFBRTtBQUNYLENBQUMsRUFBQztBQUNGLENBQUMsS0FBS0MsQ0FrQkwsQ0FBQztBQUNGLFNBQVNrQixPQUFPQSxDQUFDQyxNQUFNLEVBQUVDLEdBQUcsRUFBRTtFQUMxQixLQUFJLElBQUlDLElBQUksSUFBSUQsR0FBRyxFQUFDeEIsTUFBTSxDQUFDQyxjQUFjLENBQUNzQixNQUFNLEVBQUVFLElBQUksRUFBRTtJQUNwREMsVUFBVSxFQUFFLElBQUk7SUFDaEJDLEdBQUcsRUFBRUgsR0FBRyxDQUFDQyxJQUFJO0VBQ2pCLENBQUMsQ0FBQztBQUNOO0FBQ0FILE9BQU8sQ0FBQ3BCLE9BQU8sRUFBRTtFQUNiRyxXQUFXLEVBQUUsU0FBQUEsQ0FBQSxFQUFXO0lBQ3BCLE9BQU9BLFdBQVc7RUFDdEIsQ0FBQztFQUNEQyx1QkFBdUIsRUFBRSxTQUFBQSxDQUFBLEVBQVc7SUFDaEMsT0FBT0EsdUJBQXVCO0VBQ2xDLENBQUM7RUFDREMsaUJBQWlCLEVBQUUsU0FBQUEsQ0FBQSxFQUFXO0lBQzFCLE9BQU9BLGlCQUFpQjtFQUM1QixDQUFDO0VBQ0RDLGNBQWMsRUFBRSxTQUFBQSxDQUFBLEVBQVc7SUFDdkIsT0FBT0EsY0FBYztFQUN6QixDQUFDO0VBQ0RDLGlCQUFpQixFQUFFLFNBQUFBLENBQUEsRUFBVztJQUMxQixPQUFPQSxpQkFBaUI7RUFDNUIsQ0FBQztFQUNEQyxFQUFFLEVBQUUsU0FBQUEsQ0FBQSxFQUFXO0lBQ1gsT0FBT0EsRUFBRTtFQUNiLENBQUM7RUFDREMsRUFBRSxFQUFFLFNBQUFBLENBQUEsRUFBVztJQUNYLE9BQU9BLEVBQUU7RUFDYixDQUFDO0VBQ0RDLFVBQVUsRUFBRSxTQUFBQSxDQUFBLEVBQVc7SUFDbkIsT0FBT0EsVUFBVTtFQUNyQixDQUFDO0VBQ0RDLFFBQVEsRUFBRSxTQUFBQSxDQUFBLEVBQVc7SUFDakIsT0FBT0EsUUFBUTtFQUNuQixDQUFDO0VBQ0RDLGNBQWMsRUFBRSxTQUFBQSxDQUFBLEVBQVc7SUFDdkIsT0FBT0EsY0FBYztFQUN6QixDQUFDO0VBQ0RDLGlCQUFpQixFQUFFLFNBQUFBLENBQUEsRUFBVztJQUMxQixPQUFPQSxpQkFBaUI7RUFDNUIsQ0FBQztFQUNEQyxNQUFNLEVBQUUsU0FBQUEsQ0FBQSxFQUFXO0lBQ2YsT0FBT0EsTUFBTTtFQUNqQixDQUFDO0VBQ0RDLGFBQWEsRUFBRSxTQUFBQSxDQUFBLEVBQVc7SUFDdEIsT0FBT0EsYUFBYTtFQUN4QixDQUFDO0VBQ0RDLFNBQVMsRUFBRSxTQUFBQSxDQUFBLEVBQVc7SUFDbEIsT0FBT0EsU0FBUztFQUNwQixDQUFDO0VBQ0RDLG1CQUFtQixFQUFFLFNBQUFBLENBQUEsRUFBVztJQUM1QixPQUFPQSxtQkFBbUI7RUFDOUIsQ0FBQztFQUNEQyx3QkFBd0IsRUFBRSxTQUFBQSxDQUFBLEVBQVc7SUFDakMsT0FBT0Esd0JBQXdCO0VBQ25DLENBQUM7RUFDREMsY0FBYyxFQUFFLFNBQUFBLENBQUEsRUFBVztJQUN2QixPQUFPQSxjQUFjO0VBQ3pCO0FBQ0osQ0FBQyxDQUFDO0FBQ0YsTUFBTVQsVUFBVSxHQUFHLENBQ2YsS0FBSyxFQUNMLEtBQUssRUFDTCxLQUFLLEVBQ0wsS0FBSyxFQUNMLEtBQUssRUFDTCxNQUFNLENBQ1Q7QUFDRCxTQUFTQyxRQUFRQSxDQUFDZSxFQUFFLEVBQUU7RUFDbEIsSUFBSUMsSUFBSSxHQUFHLEtBQUs7RUFDaEIsSUFBSUMsTUFBTTtFQUNWLE9BQU8sWUFBVztJQUNkLEtBQUksSUFBSUMsSUFBSSxHQUFHQyxTQUFTLENBQUNDLE1BQU0sRUFBRUMsSUFBSSxHQUFHLElBQUlDLEtBQUssQ0FBQ0osSUFBSSxDQUFDLEVBQUVLLElBQUksR0FBRyxDQUFDLEVBQUVBLElBQUksR0FBR0wsSUFBSSxFQUFFSyxJQUFJLEVBQUUsRUFBQztNQUNuRkYsSUFBSSxDQUFDRSxJQUFJLENBQUMsR0FBR0osU0FBUyxDQUFDSSxJQUFJLENBQUM7SUFDaEM7SUFDQSxJQUFJLENBQUNQLElBQUksRUFBRTtNQUNQQSxJQUFJLEdBQUcsSUFBSTtNQUNYQyxNQUFNLEdBQUdGLEVBQUUsQ0FBQyxHQUFHTSxJQUFJLENBQUM7SUFDeEI7SUFDQSxPQUFPSixNQUFNO0VBQ2pCLENBQUM7QUFDTDtBQUNBO0FBQ0E7QUFDQSxNQUFNTyxrQkFBa0IsR0FBRyw0QkFBNEI7QUFDdkQsTUFBTXBCLGFBQWEsR0FBSXFCLEdBQUcsSUFBR0Qsa0JBQWtCLENBQUNFLElBQUksQ0FBQ0QsR0FBRyxDQUFDO0FBQ3pELFNBQVN2QixpQkFBaUJBLENBQUEsRUFBRztFQUN6QixNQUFNO0lBQUV5QixRQUFRO0lBQUVDLFFBQVE7SUFBRUM7RUFBSyxDQUFDLEdBQUdDLE1BQU0sQ0FBQ0MsUUFBUTtFQUNwRCxPQUFPSixRQUFRLEdBQUcsSUFBSSxHQUFHQyxRQUFRLElBQUlDLElBQUksR0FBRyxHQUFHLEdBQUdBLElBQUksR0FBRyxFQUFFLENBQUM7QUFDaEU7QUFDQSxTQUFTMUIsTUFBTUEsQ0FBQSxFQUFHO0VBQ2QsTUFBTTtJQUFFNkI7RUFBSyxDQUFDLEdBQUdGLE1BQU0sQ0FBQ0MsUUFBUTtFQUNoQyxNQUFNRSxNQUFNLEdBQUcvQixpQkFBaUIsQ0FBQyxDQUFDO0VBQ2xDLE9BQU84QixJQUFJLENBQUNFLFNBQVMsQ0FBQ0QsTUFBTSxDQUFDYixNQUFNLENBQUM7QUFDeEM7QUFDQSxTQUFTbkIsY0FBY0EsQ0FBQ2tDLFNBQVMsRUFBRTtFQUMvQixPQUFPLE9BQU9BLFNBQVMsS0FBSyxRQUFRLEdBQUdBLFNBQVMsR0FBR0EsU0FBUyxDQUFDQyxXQUFXLElBQUlELFNBQVMsQ0FBQ3ZCLElBQUksSUFBSSxTQUFTO0FBQzNHO0FBQ0EsU0FBU1AsU0FBU0EsQ0FBQ2dDLEdBQUcsRUFBRTtFQUNwQixPQUFPQSxHQUFHLENBQUNDLFFBQVEsSUFBSUQsR0FBRyxDQUFDRSxXQUFXO0FBQzFDO0FBQ0EsU0FBU2hDLHdCQUF3QkEsQ0FBQ2tCLEdBQUcsRUFBRTtFQUNuQyxNQUFNZSxRQUFRLEdBQUdmLEdBQUcsQ0FBQ2dCLEtBQUssQ0FBQyxHQUFHLENBQUM7RUFDL0IsTUFBTUMsVUFBVSxHQUFHRixRQUFRLENBQUMsQ0FBQyxDQUFDO0VBQzlCLE9BQU9FLFVBQVU7RUFDakI7RUFBQSxDQUNDQyxPQUFPLENBQUMsS0FBSyxFQUFFLEdBQUcsQ0FBQyxDQUFDQSxPQUFPLENBQUMsUUFBUSxFQUFFLEdBQUcsQ0FBQyxJQUFJSCxRQUFRLENBQUMsQ0FBQyxDQUFDLEdBQUcsR0FBRyxHQUFHQSxRQUFRLENBQUNJLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsSUFBSSxDQUFDLEdBQUcsQ0FBQyxHQUFHLEVBQUUsQ0FBQztBQUN4RztBQUNBLGVBQWV2QyxtQkFBbUJBLENBQUN3QyxHQUFHLEVBQUVDLEdBQUcsRUFBRTtFQUN6QyxJQUFJLE1BQXVDO0lBQ3ZDLElBQUlDLGNBQWM7SUFDbEIsSUFBSSxDQUFDQSxjQUFjLEdBQUdGLEdBQUcsQ0FBQ0csU0FBUyxLQUFLLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR0QsY0FBYyxDQUFDRSxlQUFlLEVBQUU7TUFDcEYsTUFBTUMsT0FBTyxHQUFHLEdBQUcsR0FBR2xELGNBQWMsQ0FBQzZDLEdBQUcsQ0FBQyxHQUFHLDZKQUE2SjtNQUN6TSxNQUFNLElBQUlNLEtBQUssQ0FBQ0QsT0FBTyxDQUFDO0lBQzVCO0VBQ0o7RUFDQTtFQUNBLE1BQU1kLEdBQUcsR0FBR1UsR0FBRyxDQUFDVixHQUFHLElBQUlVLEdBQUcsQ0FBQ0EsR0FBRyxJQUFJQSxHQUFHLENBQUNBLEdBQUcsQ0FBQ1YsR0FBRztFQUM3QyxJQUFJLENBQUNTLEdBQUcsQ0FBQ0ksZUFBZSxFQUFFO0lBQ3RCLElBQUlILEdBQUcsQ0FBQ0EsR0FBRyxJQUFJQSxHQUFHLENBQUNaLFNBQVMsRUFBRTtNQUMxQjtNQUNBLE9BQU87UUFDSGtCLFNBQVMsRUFBRSxNQUFNL0MsbUJBQW1CLENBQUN5QyxHQUFHLENBQUNaLFNBQVMsRUFBRVksR0FBRyxDQUFDQSxHQUFHO01BQy9ELENBQUM7SUFDTDtJQUNBLE9BQU8sQ0FBQyxDQUFDO0VBQ2I7RUFDQSxNQUFNTyxLQUFLLEdBQUcsTUFBTVIsR0FBRyxDQUFDSSxlQUFlLENBQUNILEdBQUcsQ0FBQztFQUM1QyxJQUFJVixHQUFHLElBQUloQyxTQUFTLENBQUNnQyxHQUFHLENBQUMsRUFBRTtJQUN2QixPQUFPaUIsS0FBSztFQUNoQjtFQUNBLElBQUksQ0FBQ0EsS0FBSyxFQUFFO0lBQ1IsTUFBTUgsT0FBTyxHQUFHLEdBQUcsR0FBR2xELGNBQWMsQ0FBQzZDLEdBQUcsQ0FBQyxHQUFHLDhEQUE4RCxHQUFHUSxLQUFLLEdBQUcsWUFBWTtJQUNqSSxNQUFNLElBQUlGLEtBQUssQ0FBQ0QsT0FBTyxDQUFDO0VBQzVCO0VBQ0EsSUFBSSxNQUF1QztJQUN2QyxJQUFJaEUsTUFBTSxDQUFDb0UsSUFBSSxDQUFDRCxLQUFLLENBQUMsQ0FBQ2xDLE1BQU0sS0FBSyxDQUFDLElBQUksQ0FBQzJCLEdBQUcsQ0FBQ0EsR0FBRyxFQUFFO01BQzdDUyxPQUFPLENBQUNDLElBQUksQ0FBQyxFQUFFLEdBQUd4RCxjQUFjLENBQUM2QyxHQUFHLENBQUMsR0FBRywrS0FBK0ssQ0FBQztJQUM1TjtFQUNKO0VBQ0EsT0FBT1EsS0FBSztBQUNoQjtBQUNBLE1BQU16RCxFQUFFLEdBQUcsT0FBTzZELFdBQVcsS0FBSyxXQUFXO0FBQzdDLE1BQU01RCxFQUFFLEdBQUdELEVBQUUsSUFBSSxDQUNiLE1BQU0sRUFDTixTQUFTLEVBQ1Qsa0JBQWtCLENBQ3JCLENBQUM4RCxLQUFLLENBQUVDLE1BQU0sSUFBRyxPQUFPRixXQUFXLENBQUNFLE1BQU0sQ0FBQyxLQUFLLFVBQVUsQ0FBQztBQUM1RCxNQUFNcEUsV0FBVyxTQUFTNEQsS0FBSyxDQUFDO0FBRWhDLE1BQU16RCxjQUFjLFNBQVN5RCxLQUFLLENBQUM7QUFFbkMsTUFBTXhELGlCQUFpQixTQUFTd0QsS0FBSyxDQUFDO0VBQ2xDUyxXQUFXQSxDQUFDQyxJQUFJLEVBQUM7SUFDYixLQUFLLENBQUMsQ0FBQztJQUNQLElBQUksQ0FBQ0MsSUFBSSxHQUFHLFFBQVE7SUFDcEIsSUFBSSxDQUFDbkQsSUFBSSxHQUFHLG1CQUFtQjtJQUMvQixJQUFJLENBQUN1QyxPQUFPLEdBQUcsK0JBQStCLEdBQUdXLElBQUk7RUFDekQ7QUFDSjtBQUNBLE1BQU1wRSxpQkFBaUIsU0FBUzBELEtBQUssQ0FBQztFQUNsQ1MsV0FBV0EsQ0FBQ0MsSUFBSSxFQUFFWCxPQUFPLEVBQUM7SUFDdEIsS0FBSyxDQUFDLENBQUM7SUFDUCxJQUFJLENBQUNBLE9BQU8sR0FBRyx1Q0FBdUMsR0FBR1csSUFBSSxHQUFHLEdBQUcsR0FBR1gsT0FBTztFQUNqRjtBQUNKO0FBQ0EsTUFBTTFELHVCQUF1QixTQUFTMkQsS0FBSyxDQUFDO0VBQ3hDUyxXQUFXQSxDQUFBLEVBQUU7SUFDVCxLQUFLLENBQUMsQ0FBQztJQUNQLElBQUksQ0FBQ0UsSUFBSSxHQUFHLFFBQVE7SUFDcEIsSUFBSSxDQUFDWixPQUFPLEdBQUcsbUNBQW1DO0VBQ3REO0FBQ0o7QUFDQSxTQUFTM0MsY0FBY0EsQ0FBQ3dELEtBQUssRUFBRTtFQUMzQixPQUFPQyxJQUFJLENBQUNDLFNBQVMsQ0FBQztJQUNsQmYsT0FBTyxFQUFFYSxLQUFLLENBQUNiLE9BQU87SUFDdEJnQixLQUFLLEVBQUVILEtBQUssQ0FBQ0c7RUFDakIsQ0FBQyxDQUFDO0FBQ04iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb3p5ci1tYXN0ZXItd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy5qcz9kZDUyIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuMCAmJiAobW9kdWxlLmV4cG9ydHMgPSB7XG4gICAgRGVjb2RlRXJyb3I6IG51bGwsXG4gICAgTWlkZGxld2FyZU5vdEZvdW5kRXJyb3I6IG51bGwsXG4gICAgTWlzc2luZ1N0YXRpY1BhZ2U6IG51bGwsXG4gICAgTm9ybWFsaXplRXJyb3I6IG51bGwsXG4gICAgUGFnZU5vdEZvdW5kRXJyb3I6IG51bGwsXG4gICAgU1A6IG51bGwsXG4gICAgU1Q6IG51bGwsXG4gICAgV0VCX1ZJVEFMUzogbnVsbCxcbiAgICBleGVjT25jZTogbnVsbCxcbiAgICBnZXREaXNwbGF5TmFtZTogbnVsbCxcbiAgICBnZXRMb2NhdGlvbk9yaWdpbjogbnVsbCxcbiAgICBnZXRVUkw6IG51bGwsXG4gICAgaXNBYnNvbHV0ZVVybDogbnVsbCxcbiAgICBpc1Jlc1NlbnQ6IG51bGwsXG4gICAgbG9hZEdldEluaXRpYWxQcm9wczogbnVsbCxcbiAgICBub3JtYWxpemVSZXBlYXRlZFNsYXNoZXM6IG51bGwsXG4gICAgc3RyaW5naWZ5RXJyb3I6IG51bGxcbn0pO1xuZnVuY3Rpb24gX2V4cG9ydCh0YXJnZXQsIGFsbCkge1xuICAgIGZvcih2YXIgbmFtZSBpbiBhbGwpT2JqZWN0LmRlZmluZVByb3BlcnR5KHRhcmdldCwgbmFtZSwge1xuICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICBnZXQ6IGFsbFtuYW1lXVxuICAgIH0pO1xufVxuX2V4cG9ydChleHBvcnRzLCB7XG4gICAgRGVjb2RlRXJyb3I6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gRGVjb2RlRXJyb3I7XG4gICAgfSxcbiAgICBNaWRkbGV3YXJlTm90Rm91bmRFcnJvcjogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBNaWRkbGV3YXJlTm90Rm91bmRFcnJvcjtcbiAgICB9LFxuICAgIE1pc3NpbmdTdGF0aWNQYWdlOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIE1pc3NpbmdTdGF0aWNQYWdlO1xuICAgIH0sXG4gICAgTm9ybWFsaXplRXJyb3I6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gTm9ybWFsaXplRXJyb3I7XG4gICAgfSxcbiAgICBQYWdlTm90Rm91bmRFcnJvcjogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBQYWdlTm90Rm91bmRFcnJvcjtcbiAgICB9LFxuICAgIFNQOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIFNQO1xuICAgIH0sXG4gICAgU1Q6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gU1Q7XG4gICAgfSxcbiAgICBXRUJfVklUQUxTOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIFdFQl9WSVRBTFM7XG4gICAgfSxcbiAgICBleGVjT25jZTogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBleGVjT25jZTtcbiAgICB9LFxuICAgIGdldERpc3BsYXlOYW1lOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIGdldERpc3BsYXlOYW1lO1xuICAgIH0sXG4gICAgZ2V0TG9jYXRpb25PcmlnaW46IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gZ2V0TG9jYXRpb25PcmlnaW47XG4gICAgfSxcbiAgICBnZXRVUkw6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gZ2V0VVJMO1xuICAgIH0sXG4gICAgaXNBYnNvbHV0ZVVybDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBpc0Fic29sdXRlVXJsO1xuICAgIH0sXG4gICAgaXNSZXNTZW50OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIGlzUmVzU2VudDtcbiAgICB9LFxuICAgIGxvYWRHZXRJbml0aWFsUHJvcHM6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gbG9hZEdldEluaXRpYWxQcm9wcztcbiAgICB9LFxuICAgIG5vcm1hbGl6ZVJlcGVhdGVkU2xhc2hlczogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBub3JtYWxpemVSZXBlYXRlZFNsYXNoZXM7XG4gICAgfSxcbiAgICBzdHJpbmdpZnlFcnJvcjogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBzdHJpbmdpZnlFcnJvcjtcbiAgICB9XG59KTtcbmNvbnN0IFdFQl9WSVRBTFMgPSBbXG4gICAgXCJDTFNcIixcbiAgICBcIkZDUFwiLFxuICAgIFwiRklEXCIsXG4gICAgXCJJTlBcIixcbiAgICBcIkxDUFwiLFxuICAgIFwiVFRGQlwiXG5dO1xuZnVuY3Rpb24gZXhlY09uY2UoZm4pIHtcbiAgICBsZXQgdXNlZCA9IGZhbHNlO1xuICAgIGxldCByZXN1bHQ7XG4gICAgcmV0dXJuIGZ1bmN0aW9uKCkge1xuICAgICAgICBmb3IodmFyIF9sZW4gPSBhcmd1bWVudHMubGVuZ3RoLCBhcmdzID0gbmV3IEFycmF5KF9sZW4pLCBfa2V5ID0gMDsgX2tleSA8IF9sZW47IF9rZXkrKyl7XG4gICAgICAgICAgICBhcmdzW19rZXldID0gYXJndW1lbnRzW19rZXldO1xuICAgICAgICB9XG4gICAgICAgIGlmICghdXNlZCkge1xuICAgICAgICAgICAgdXNlZCA9IHRydWU7XG4gICAgICAgICAgICByZXN1bHQgPSBmbiguLi5hcmdzKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH07XG59XG4vLyBTY2hlbWU6IGh0dHBzOi8vdG9vbHMuaWV0Zi5vcmcvaHRtbC9yZmMzOTg2I3NlY3Rpb24tMy4xXG4vLyBBYnNvbHV0ZSBVUkw6IGh0dHBzOi8vdG9vbHMuaWV0Zi5vcmcvaHRtbC9yZmMzOTg2I3NlY3Rpb24tNC4zXG5jb25zdCBBQlNPTFVURV9VUkxfUkVHRVggPSAvXlthLXpBLVpdW2EtekEtWlxcZCtcXC0uXSo/Oi87XG5jb25zdCBpc0Fic29sdXRlVXJsID0gKHVybCk9PkFCU09MVVRFX1VSTF9SRUdFWC50ZXN0KHVybCk7XG5mdW5jdGlvbiBnZXRMb2NhdGlvbk9yaWdpbigpIHtcbiAgICBjb25zdCB7IHByb3RvY29sLCBob3N0bmFtZSwgcG9ydCB9ID0gd2luZG93LmxvY2F0aW9uO1xuICAgIHJldHVybiBwcm90b2NvbCArIFwiLy9cIiArIGhvc3RuYW1lICsgKHBvcnQgPyBcIjpcIiArIHBvcnQgOiBcIlwiKTtcbn1cbmZ1bmN0aW9uIGdldFVSTCgpIHtcbiAgICBjb25zdCB7IGhyZWYgfSA9IHdpbmRvdy5sb2NhdGlvbjtcbiAgICBjb25zdCBvcmlnaW4gPSBnZXRMb2NhdGlvbk9yaWdpbigpO1xuICAgIHJldHVybiBocmVmLnN1YnN0cmluZyhvcmlnaW4ubGVuZ3RoKTtcbn1cbmZ1bmN0aW9uIGdldERpc3BsYXlOYW1lKENvbXBvbmVudCkge1xuICAgIHJldHVybiB0eXBlb2YgQ29tcG9uZW50ID09PSBcInN0cmluZ1wiID8gQ29tcG9uZW50IDogQ29tcG9uZW50LmRpc3BsYXlOYW1lIHx8IENvbXBvbmVudC5uYW1lIHx8IFwiVW5rbm93blwiO1xufVxuZnVuY3Rpb24gaXNSZXNTZW50KHJlcykge1xuICAgIHJldHVybiByZXMuZmluaXNoZWQgfHwgcmVzLmhlYWRlcnNTZW50O1xufVxuZnVuY3Rpb24gbm9ybWFsaXplUmVwZWF0ZWRTbGFzaGVzKHVybCkge1xuICAgIGNvbnN0IHVybFBhcnRzID0gdXJsLnNwbGl0KFwiP1wiKTtcbiAgICBjb25zdCB1cmxOb1F1ZXJ5ID0gdXJsUGFydHNbMF07XG4gICAgcmV0dXJuIHVybE5vUXVlcnkvLyBmaXJzdCB3ZSByZXBsYWNlIGFueSBub24tZW5jb2RlZCBiYWNrc2xhc2hlcyB3aXRoIGZvcndhcmRcbiAgICAvLyB0aGVuIG5vcm1hbGl6ZSByZXBlYXRlZCBmb3J3YXJkIHNsYXNoZXNcbiAgICAucmVwbGFjZSgvXFxcXC9nLCBcIi9cIikucmVwbGFjZSgvXFwvXFwvKy9nLCBcIi9cIikgKyAodXJsUGFydHNbMV0gPyBcIj9cIiArIHVybFBhcnRzLnNsaWNlKDEpLmpvaW4oXCI/XCIpIDogXCJcIik7XG59XG5hc3luYyBmdW5jdGlvbiBsb2FkR2V0SW5pdGlhbFByb3BzKEFwcCwgY3R4KSB7XG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAgICAgICB2YXIgX0FwcF9wcm90b3R5cGU7XG4gICAgICAgIGlmICgoX0FwcF9wcm90b3R5cGUgPSBBcHAucHJvdG90eXBlKSA9PSBudWxsID8gdm9pZCAwIDogX0FwcF9wcm90b3R5cGUuZ2V0SW5pdGlhbFByb3BzKSB7XG4gICAgICAgICAgICBjb25zdCBtZXNzYWdlID0gJ1wiJyArIGdldERpc3BsYXlOYW1lKEFwcCkgKyAnLmdldEluaXRpYWxQcm9wcygpXCIgaXMgZGVmaW5lZCBhcyBhbiBpbnN0YW5jZSBtZXRob2QgLSB2aXNpdCBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9nZXQtaW5pdGlhbC1wcm9wcy1hcy1hbi1pbnN0YW5jZS1tZXRob2QgZm9yIG1vcmUgaW5mb3JtYXRpb24uJztcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihtZXNzYWdlKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvLyB3aGVuIGNhbGxlZCBmcm9tIF9hcHAgYGN0eGAgaXMgbmVzdGVkIGluIGBjdHhgXG4gICAgY29uc3QgcmVzID0gY3R4LnJlcyB8fCBjdHguY3R4ICYmIGN0eC5jdHgucmVzO1xuICAgIGlmICghQXBwLmdldEluaXRpYWxQcm9wcykge1xuICAgICAgICBpZiAoY3R4LmN0eCAmJiBjdHguQ29tcG9uZW50KSB7XG4gICAgICAgICAgICAvLyBAdHMtaWdub3JlIHBhZ2VQcm9wcyBkZWZhdWx0XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHBhZ2VQcm9wczogYXdhaXQgbG9hZEdldEluaXRpYWxQcm9wcyhjdHguQ29tcG9uZW50LCBjdHguY3R4KVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4ge307XG4gICAgfVxuICAgIGNvbnN0IHByb3BzID0gYXdhaXQgQXBwLmdldEluaXRpYWxQcm9wcyhjdHgpO1xuICAgIGlmIChyZXMgJiYgaXNSZXNTZW50KHJlcykpIHtcbiAgICAgICAgcmV0dXJuIHByb3BzO1xuICAgIH1cbiAgICBpZiAoIXByb3BzKSB7XG4gICAgICAgIGNvbnN0IG1lc3NhZ2UgPSAnXCInICsgZ2V0RGlzcGxheU5hbWUoQXBwKSArICcuZ2V0SW5pdGlhbFByb3BzKClcIiBzaG91bGQgcmVzb2x2ZSB0byBhbiBvYmplY3QuIEJ1dCBmb3VuZCBcIicgKyBwcm9wcyArICdcIiBpbnN0ZWFkLic7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihtZXNzYWdlKTtcbiAgICB9XG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAgICAgICBpZiAoT2JqZWN0LmtleXMocHJvcHMpLmxlbmd0aCA9PT0gMCAmJiAhY3R4LmN0eCkge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKFwiXCIgKyBnZXREaXNwbGF5TmFtZShBcHApICsgXCIgcmV0dXJuZWQgYW4gZW1wdHkgb2JqZWN0IGZyb20gYGdldEluaXRpYWxQcm9wc2AuIFRoaXMgZGUtb3B0aW1pemVzIGFuZCBwcmV2ZW50cyBhdXRvbWF0aWMgc3RhdGljIG9wdGltaXphdGlvbi4gaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvZW1wdHktb2JqZWN0LWdldEluaXRpYWxQcm9wc1wiKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gcHJvcHM7XG59XG5jb25zdCBTUCA9IHR5cGVvZiBwZXJmb3JtYW5jZSAhPT0gXCJ1bmRlZmluZWRcIjtcbmNvbnN0IFNUID0gU1AgJiYgW1xuICAgIFwibWFya1wiLFxuICAgIFwibWVhc3VyZVwiLFxuICAgIFwiZ2V0RW50cmllc0J5TmFtZVwiXG5dLmV2ZXJ5KChtZXRob2QpPT50eXBlb2YgcGVyZm9ybWFuY2VbbWV0aG9kXSA9PT0gXCJmdW5jdGlvblwiKTtcbmNsYXNzIERlY29kZUVycm9yIGV4dGVuZHMgRXJyb3Ige1xufVxuY2xhc3MgTm9ybWFsaXplRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG59XG5jbGFzcyBQYWdlTm90Rm91bmRFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcihwYWdlKXtcbiAgICAgICAgc3VwZXIoKTtcbiAgICAgICAgdGhpcy5jb2RlID0gXCJFTk9FTlRcIjtcbiAgICAgICAgdGhpcy5uYW1lID0gXCJQYWdlTm90Rm91bmRFcnJvclwiO1xuICAgICAgICB0aGlzLm1lc3NhZ2UgPSBcIkNhbm5vdCBmaW5kIG1vZHVsZSBmb3IgcGFnZTogXCIgKyBwYWdlO1xuICAgIH1cbn1cbmNsYXNzIE1pc3NpbmdTdGF0aWNQYWdlIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKHBhZ2UsIG1lc3NhZ2Upe1xuICAgICAgICBzdXBlcigpO1xuICAgICAgICB0aGlzLm1lc3NhZ2UgPSBcIkZhaWxlZCB0byBsb2FkIHN0YXRpYyBmaWxlIGZvciBwYWdlOiBcIiArIHBhZ2UgKyBcIiBcIiArIG1lc3NhZ2U7XG4gICAgfVxufVxuY2xhc3MgTWlkZGxld2FyZU5vdEZvdW5kRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gICAgY29uc3RydWN0b3IoKXtcbiAgICAgICAgc3VwZXIoKTtcbiAgICAgICAgdGhpcy5jb2RlID0gXCJFTk9FTlRcIjtcbiAgICAgICAgdGhpcy5tZXNzYWdlID0gXCJDYW5ub3QgZmluZCB0aGUgbWlkZGxld2FyZSBtb2R1bGVcIjtcbiAgICB9XG59XG5mdW5jdGlvbiBzdHJpbmdpZnlFcnJvcihlcnJvcikge1xuICAgIHJldHVybiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsXG4gICAgICAgIHN0YWNrOiBlcnJvci5zdGFja1xuICAgIH0pO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlscy5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJtb2R1bGUiLCJEZWNvZGVFcnJvciIsIk1pZGRsZXdhcmVOb3RGb3VuZEVycm9yIiwiTWlzc2luZ1N0YXRpY1BhZ2UiLCJOb3JtYWxpemVFcnJvciIsIlBhZ2VOb3RGb3VuZEVycm9yIiwiU1AiLCJTVCIsIldFQl9WSVRBTFMiLCJleGVjT25jZSIsImdldERpc3BsYXlOYW1lIiwiZ2V0TG9jYXRpb25PcmlnaW4iLCJnZXRVUkwiLCJpc0Fic29sdXRlVXJsIiwiaXNSZXNTZW50IiwibG9hZEdldEluaXRpYWxQcm9wcyIsIm5vcm1hbGl6ZVJlcGVhdGVkU2xhc2hlcyIsInN0cmluZ2lmeUVycm9yIiwiX2V4cG9ydCIsInRhcmdldCIsImFsbCIsIm5hbWUiLCJlbnVtZXJhYmxlIiwiZ2V0IiwiZm4iLCJ1c2VkIiwicmVzdWx0IiwiX2xlbiIsImFyZ3VtZW50cyIsImxlbmd0aCIsImFyZ3MiLCJBcnJheSIsIl9rZXkiLCJBQlNPTFVURV9VUkxfUkVHRVgiLCJ1cmwiLCJ0ZXN0IiwicHJvdG9jb2wiLCJob3N0bmFtZSIsInBvcnQiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJvcmlnaW4iLCJzdWJzdHJpbmciLCJDb21wb25lbnQiLCJkaXNwbGF5TmFtZSIsInJlcyIsImZpbmlzaGVkIiwiaGVhZGVyc1NlbnQiLCJ1cmxQYXJ0cyIsInNwbGl0IiwidXJsTm9RdWVyeSIsInJlcGxhY2UiLCJzbGljZSIsImpvaW4iLCJBcHAiLCJjdHgiLCJfQXBwX3Byb3RvdHlwZSIsInByb3RvdHlwZSIsImdldEluaXRpYWxQcm9wcyIsIm1lc3NhZ2UiLCJFcnJvciIsInBhZ2VQcm9wcyIsInByb3BzIiwia2V5cyIsImNvbnNvbGUiLCJ3YXJuIiwicGVyZm9ybWFuY2UiLCJldmVyeSIsIm1ldGhvZCIsImNvbnN0cnVjdG9yIiwicGFnZSIsImNvZGUiLCJlcnJvciIsIkpTT04iLCJzdHJpbmdpZnkiLCJzdGFjayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/utils.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/shared/lib/utils/warn-once.js":
/*!******************************************************************!*\
  !*** ../../node_modules/next/dist/shared/lib/utils/warn-once.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"warnOnce\", ({\n  enumerable: true,\n  get: function () {\n    return warnOnce;\n  }\n}));\nlet warnOnce = _ => {};\nif (true) {\n  const warnings = new Set();\n  warnOnce = msg => {\n    if (!warnings.has(msg)) {\n      console.warn(msg);\n    }\n    warnings.add(msg);\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL3V0aWxzL3dhcm4tb25jZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFDYkEsOENBQTZDO0VBQ3pDRyxLQUFLLEVBQUU7QUFDWCxDQUFDLEVBQUM7QUFDRkgsNENBQTJDO0VBQ3ZDSSxVQUFVLEVBQUUsSUFBSTtFQUNoQkMsR0FBRyxFQUFFLFNBQUFBLENBQUEsRUFBVztJQUNaLE9BQU9DLFFBQVE7RUFDbkI7QUFDSixDQUFDLEVBQUM7QUFDRixJQUFJQSxRQUFRLEdBQUlDLENBQUMsSUFBRyxDQUFDLENBQUM7QUFDdEIsSUFBSSxNQUF1QztFQUN2QyxNQUFNQyxRQUFRLEdBQUcsSUFBSUMsR0FBRyxDQUFDLENBQUM7RUFDMUJILFFBQVEsR0FBSUksR0FBRyxJQUFHO0lBQ2QsSUFBSSxDQUFDRixRQUFRLENBQUNHLEdBQUcsQ0FBQ0QsR0FBRyxDQUFDLEVBQUU7TUFDcEJFLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDSCxHQUFHLENBQUM7SUFDckI7SUFDQUYsUUFBUSxDQUFDTSxHQUFHLENBQUNKLEdBQUcsQ0FBQztFQUNyQixDQUFDO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb3p5ci1tYXN0ZXItd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy93YXJuLW9uY2UuanM/Njk3MyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIndhcm5PbmNlXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB3YXJuT25jZTtcbiAgICB9XG59KTtcbmxldCB3YXJuT25jZSA9IChfKT0+e307XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiKSB7XG4gICAgY29uc3Qgd2FybmluZ3MgPSBuZXcgU2V0KCk7XG4gICAgd2Fybk9uY2UgPSAobXNnKT0+e1xuICAgICAgICBpZiAoIXdhcm5pbmdzLmhhcyhtc2cpKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4obXNnKTtcbiAgICAgICAgfVxuICAgICAgICB3YXJuaW5ncy5hZGQobXNnKTtcbiAgICB9O1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD13YXJuLW9uY2UuanMubWFwIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiZW51bWVyYWJsZSIsImdldCIsIndhcm5PbmNlIiwiXyIsIndhcm5pbmdzIiwiU2V0IiwibXNnIiwiaGFzIiwiY29uc29sZSIsIndhcm4iLCJhZGQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/shared/lib/utils/warn-once.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/build/templates/helpers.js":
/*!***************************************************************!*\
  !*** ../../node_modules/next/dist/build/templates/helpers.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("/**\n * Hoists a name from a module or promised module.\n *\n * @param module the module to hoist the name from\n * @param name the name to hoist\n * @returns the value on the module (or promised module)\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hoist\", ({\n    enumerable: true,\n    get: function() {\n        return hoist;\n    }\n}));\nfunction hoist(module, name) {\n    // If the name is available in the module, return it.\n    if (name in module) {\n        return module[name];\n    }\n    // If a property called `then` exists, assume it's a promise and\n    // return a promise that resolves to the name.\n    if (\"then\" in module && typeof module.then === \"function\") {\n        return module.then((mod)=>hoist(mod, name));\n    }\n    // If we're trying to hoise the default export, and the module is a function,\n    // return the module itself.\n    if (typeof module === \"function\" && name === \"default\") {\n        return module;\n    }\n    // Otherwise, return undefined.\n    return undefined;\n}\n\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/templates/helpers.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/lib/is-error.js":
/*!****************************************************!*\
  !*** ../../node_modules/next/dist/lib/is-error.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return isError;\n    },\n    getProperError: function() {\n        return getProperError;\n    }\n});\nconst _isplainobject = __webpack_require__(/*! ../shared/lib/is-plain-object */ \"../../node_modules/next/dist/shared/lib/is-plain-object.js\");\nfunction isError(err) {\n    return typeof err === \"object\" && err !== null && \"name\" in err && \"message\" in err;\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (true) {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === \"undefined\") {\n            return new Error(\"An undefined error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n        if (err === null) {\n            return new Error(\"A null error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n    }\n    return new Error((0, _isplainobject.isPlainObject)(err) ? JSON.stringify(err) : err + \"\");\n}\n\n//# sourceMappingURL=is-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/lib/is-error.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/lib/pretty-bytes.js":
/*!********************************************************!*\
  !*** ../../node_modules/next/dist/lib/pretty-bytes.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return prettyBytes;\n    }\n}));\nconst UNITS = [\n    \"B\",\n    \"kB\",\n    \"MB\",\n    \"GB\",\n    \"TB\",\n    \"PB\",\n    \"EB\",\n    \"ZB\",\n    \"YB\"\n];\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/ const toLocaleString = (number, locale)=>{\n    let result = number;\n    if (typeof locale === \"string\") {\n        result = number.toLocaleString(locale);\n    } else if (locale === true) {\n        result = number.toLocaleString();\n    }\n    return result;\n};\nfunction prettyBytes(number, options) {\n    if (!Number.isFinite(number)) {\n        throw new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);\n    }\n    options = Object.assign({}, options);\n    if (options.signed && number === 0) {\n        return \" 0 B\";\n    }\n    const isNegative = number < 0;\n    const prefix = isNegative ? \"-\" : options.signed ? \"+\" : \"\";\n    if (isNegative) {\n        number = -number;\n    }\n    if (number < 1) {\n        const numberString = toLocaleString(number, options.locale);\n        return prefix + numberString + \" B\";\n    }\n    const exponent = Math.min(Math.floor(Math.log10(number) / 3), UNITS.length - 1);\n    number = Number((number / Math.pow(1000, exponent)).toPrecision(3));\n    const numberString = toLocaleString(number, options.locale);\n    const unit = UNITS[exponent];\n    return prefix + numberString + \" \" + unit;\n}\n\n//# sourceMappingURL=pretty-bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/lib/pretty-bytes.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/server/future/helpers/interception-routes.js":
/*!*********************************************************************************!*\
  !*** ../../node_modules/next/dist/server/future/helpers/interception-routes.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    INTERCEPTION_ROUTE_MARKERS: function() {\n        return INTERCEPTION_ROUTE_MARKERS;\n    },\n    extractInterceptionRouteInformation: function() {\n        return extractInterceptionRouteInformation;\n    },\n    isInterceptionRouteAppPath: function() {\n        return isInterceptionRouteAppPath;\n    }\n});\nconst _apppaths = __webpack_require__(/*! ../../../shared/lib/router/utils/app-paths */ \"../../node_modules/next/dist/shared/lib/router/utils/app-paths.js\");\nconst INTERCEPTION_ROUTE_MARKERS = [\n    \"(..)(..)\",\n    \"(.)\",\n    \"(..)\",\n    \"(...)\"\n];\nfunction isInterceptionRouteAppPath(path) {\n    // TODO-APP: add more serious validation\n    return path.split(\"/\").find((segment)=>INTERCEPTION_ROUTE_MARKERS.find((m)=>segment.startsWith(m))) !== undefined;\n}\nfunction extractInterceptionRouteInformation(path) {\n    let interceptingRoute, marker, interceptedRoute;\n    for (const segment of path.split(\"/\")){\n        marker = INTERCEPTION_ROUTE_MARKERS.find((m)=>segment.startsWith(m));\n        if (marker) {\n            [interceptingRoute, interceptedRoute] = path.split(marker, 2);\n            break;\n        }\n    }\n    if (!interceptingRoute || !marker || !interceptedRoute) {\n        throw new Error(`Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);\n    }\n    interceptingRoute = (0, _apppaths.normalizeAppPath)(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n    ;\n    switch(marker){\n        case \"(.)\":\n            // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n            if (interceptingRoute === \"/\") {\n                interceptedRoute = `/${interceptedRoute}`;\n            } else {\n                interceptedRoute = interceptingRoute + \"/\" + interceptedRoute;\n            }\n            break;\n        case \"(..)\":\n            // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n            if (interceptingRoute === \"/\") {\n                throw new Error(`Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`);\n            }\n            interceptedRoute = interceptingRoute.split(\"/\").slice(0, -1).concat(interceptedRoute).join(\"/\");\n            break;\n        case \"(...)\":\n            // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n            interceptedRoute = \"/\" + interceptedRoute;\n            break;\n        case \"(..)(..)\":\n            // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n            const splitInterceptingRoute = interceptingRoute.split(\"/\");\n            if (splitInterceptingRoute.length <= 2) {\n                throw new Error(`Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`);\n            }\n            interceptedRoute = splitInterceptingRoute.slice(0, -2).concat(interceptedRoute).join(\"/\");\n            break;\n        default:\n            throw new Error(\"Invariant: unexpected marker\");\n    }\n    return {\n        interceptingRoute,\n        interceptedRoute\n    };\n}\n\n//# sourceMappingURL=interception-routes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/server/future/helpers/interception-routes.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/server/future/route-kind.js":
/*!****************************************************************!*\
  !*** ../../node_modules/next/dist/server/future/route-kind.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"RouteKind\", ({\n    enumerable: true,\n    get: function() {\n        return RouteKind;\n    }\n}));\nvar RouteKind;\n(function(RouteKind) {\n    /**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */ RouteKind[\"PAGES\"] = \"PAGES\";\n    /**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */ RouteKind[\"PAGES_API\"] = \"PAGES_API\";\n    /**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */ RouteKind[\"APP_PAGE\"] = \"APP_PAGE\";\n    /**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */ RouteKind[\"APP_ROUTE\"] = \"APP_ROUTE\";\n})(RouteKind || (RouteKind = {}));\n\n//# sourceMappingURL=route-kind.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRiw2Q0FBNEM7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixJQUFJLEVBQUUsR0FBRztBQUNqQztBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsSUFBSSxFQUFFLEdBQUc7QUFDbEM7QUFDQSxDQUFDLDhCQUE4Qjs7QUFFL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb3p5ci1tYXN0ZXItd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kLmpzP2NmMDUiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJSb3V0ZUtpbmRcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIFJvdXRlS2luZDtcbiAgICB9XG59KTtcbnZhciBSb3V0ZUtpbmQ7XG4oZnVuY3Rpb24oUm91dGVLaW5kKSB7XG4gICAgLyoqXG4gICAqIGBQQUdFU2AgcmVwcmVzZW50cyBhbGwgdGhlIFJlYWN0IHBhZ2VzIHRoYXQgYXJlIHVuZGVyIGBwYWdlcy9gLlxuICAgKi8gUm91dGVLaW5kW1wiUEFHRVNcIl0gPSBcIlBBR0VTXCI7XG4gICAgLyoqXG4gICAqIGBQQUdFU19BUElgIHJlcHJlc2VudHMgYWxsIHRoZSBBUEkgcm91dGVzIHVuZGVyIGBwYWdlcy9hcGkvYC5cbiAgICovIFJvdXRlS2luZFtcIlBBR0VTX0FQSVwiXSA9IFwiUEFHRVNfQVBJXCI7XG4gICAgLyoqXG4gICAqIGBBUFBfUEFHRWAgcmVwcmVzZW50cyBhbGwgdGhlIFJlYWN0IHBhZ2VzIHRoYXQgYXJlIHVuZGVyIGBhcHAvYCB3aXRoIHRoZVxuICAgKiBmaWxlbmFtZSBvZiBgcGFnZS57aix0fXN7LHh9YC5cbiAgICovIFJvdXRlS2luZFtcIkFQUF9QQUdFXCJdID0gXCJBUFBfUEFHRVwiO1xuICAgIC8qKlxuICAgKiBgQVBQX1JPVVRFYCByZXByZXNlbnRzIGFsbCB0aGUgQVBJIHJvdXRlcyBhbmQgbWV0YWRhdGEgcm91dGVzIHRoYXQgYXJlIHVuZGVyIGBhcHAvYCB3aXRoIHRoZVxuICAgKiBmaWxlbmFtZSBvZiBgcm91dGUue2osdH1zeyx4fWAuXG4gICAqLyBSb3V0ZUtpbmRbXCJBUFBfUk9VVEVcIl0gPSBcIkFQUF9ST1VURVwiO1xufSkoUm91dGVLaW5kIHx8IChSb3V0ZUtpbmQgPSB7fSkpO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1yb3V0ZS1raW5kLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/server/future/route-kind.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js":
/*!*****************************************************************************************!*\
  !*** ../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js ***!
  \*****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nif (false) {} else {\n    if (true) {\n        module.exports = __webpack_require__(/*! next/dist/compiled/next-server/pages.runtime.dev.js */ \"next/dist/compiled/next-server/pages.runtime.dev.js\");\n    } else {}\n}\n\n//# sourceMappingURL=module.compiled.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMvbW9kdWxlLmNvbXBpbGVkLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsSUFBSSxLQUFtQyxFQUFFLEVBRXhDLENBQUM7QUFDRixRQUFRLElBQXNDO0FBQzlDLFFBQVEsc0pBQStFO0FBQ3ZGLE1BQU0sS0FBSyxFQUlOO0FBQ0w7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb3p5ci1tYXN0ZXItd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL3BhZ2VzL21vZHVsZS5jb21waWxlZC5qcz9hMTY0Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuaWYgKHByb2Nlc3MuZW52Lk5FWFRfUlVOVElNRSA9PT0gXCJlZGdlXCIpIHtcbiAgICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL3BhZ2VzL21vZHVsZS5qc1wiKTtcbn0gZWxzZSB7XG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcImRldmVsb3BtZW50XCIpIHtcbiAgICAgICAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKFwibmV4dC9kaXN0L2NvbXBpbGVkL25leHQtc2VydmVyL3BhZ2VzLnJ1bnRpbWUuZGV2LmpzXCIpO1xuICAgIH0gZWxzZSBpZiAocHJvY2Vzcy5lbnYuVFVSQk9QQUNLKSB7XG4gICAgICAgIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZShcIm5leHQvZGlzdC9jb21waWxlZC9uZXh0LXNlcnZlci9wYWdlcy10dXJiby5ydW50aW1lLnByb2QuanNcIik7XG4gICAgfSBlbHNlIHtcbiAgICAgICAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKFwibmV4dC9kaXN0L2NvbXBpbGVkL25leHQtc2VydmVyL3BhZ2VzLnJ1bnRpbWUucHJvZC5qc1wiKTtcbiAgICB9XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1vZHVsZS5jb21waWxlZC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/amp-context.js":
/*!*******************************************************************************************************!*\
  !*** ../../node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/amp-context.js ***!
  \*******************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nmodule.exports = __webpack_require__(/*! ../../module.compiled */ \"../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\").vendored.contexts.AmpContext;\n\n//# sourceMappingURL=amp-context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMvdmVuZG9yZWQvY29udGV4dHMvYW1wLWNvbnRleHQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYixtTEFBaUY7O0FBRWpGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va296eXItbWFzdGVyLXdlYi8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy92ZW5kb3JlZC9jb250ZXh0cy9hbXAtY29udGV4dC5qcz9mODYzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xubW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKFwiLi4vLi4vbW9kdWxlLmNvbXBpbGVkXCIpLnZlbmRvcmVkW1wiY29udGV4dHNcIl0uQW1wQ29udGV4dDtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YW1wLWNvbnRleHQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/amp-context.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/head-manager-context.js":
/*!****************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/head-manager-context.js ***!
  \****************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nmodule.exports = __webpack_require__(/*! ../../module.compiled */ \"../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\").vendored.contexts.HeadManagerContext;\n\n//# sourceMappingURL=head-manager-context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMvdmVuZG9yZWQvY29udGV4dHMvaGVhZC1tYW5hZ2VyLWNvbnRleHQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiwyTEFBeUY7O0FBRXpGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va296eXItbWFzdGVyLXdlYi8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy92ZW5kb3JlZC9jb250ZXh0cy9oZWFkLW1hbmFnZXItY29udGV4dC5qcz9mOTQ2Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xubW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKFwiLi4vLi4vbW9kdWxlLmNvbXBpbGVkXCIpLnZlbmRvcmVkW1wiY29udGV4dHNcIl0uSGVhZE1hbmFnZXJDb250ZXh0O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1oZWFkLW1hbmFnZXItY29udGV4dC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/head-manager-context.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/html-context.js":
/*!********************************************************************************************************!*\
  !*** ../../node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/html-context.js ***!
  \********************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nmodule.exports = __webpack_require__(/*! ../../module.compiled */ \"../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\").vendored.contexts.HtmlContext;\n\n//# sourceMappingURL=html-context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMvdmVuZG9yZWQvY29udGV4dHMvaHRtbC1jb250ZXh0LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2Isb0xBQWtGOztBQUVsRiIsInNvdXJjZXMiOlsid2VicGFjazovL2tvenlyLW1hc3Rlci13ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMvdmVuZG9yZWQvY29udGV4dHMvaHRtbC1jb250ZXh0LmpzP2NhZTMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5tb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoXCIuLi8uLi9tb2R1bGUuY29tcGlsZWRcIikudmVuZG9yZWRbXCJjb250ZXh0c1wiXS5IdG1sQ29udGV4dDtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aHRtbC1jb250ZXh0LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/html-context.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/server/get-page-files.js":
/*!*************************************************************!*\
  !*** ../../node_modules/next/dist/server/get-page-files.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getPageFiles\", ({\n    enumerable: true,\n    get: function() {\n        return getPageFiles;\n    }\n}));\nconst _denormalizepagepath = __webpack_require__(/*! ../shared/lib/page-path/denormalize-page-path */ \"../../node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js\");\nconst _normalizepagepath = __webpack_require__(/*! ../shared/lib/page-path/normalize-page-path */ \"../../node_modules/next/dist/shared/lib/page-path/normalize-page-path.js\");\nfunction getPageFiles(buildManifest, page) {\n    const normalizedPage = (0, _denormalizepagepath.denormalizePagePath)((0, _normalizepagepath.normalizePagePath)(page));\n    let files = buildManifest.pages[normalizedPage];\n    if (!files) {\n        console.warn(`Could not find files for ${normalizedPage} in .next/build-manifest.json`);\n        return [];\n    }\n    return files;\n}\n\n//# sourceMappingURL=get-page-files.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvZ2V0LXBhZ2UtZmlsZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixnREFBK0M7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRiw2QkFBNkIsbUJBQU8sQ0FBQyxpSUFBK0M7QUFDcEYsMkJBQTJCLG1CQUFPLENBQUMsNkhBQTZDO0FBQ2hGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQWlELGdCQUFnQjtBQUNqRTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL2tvenlyLW1hc3Rlci13ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvZ2V0LXBhZ2UtZmlsZXMuanM/YjBjYiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImdldFBhZ2VGaWxlc1wiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gZ2V0UGFnZUZpbGVzO1xuICAgIH1cbn0pO1xuY29uc3QgX2Rlbm9ybWFsaXplcGFnZXBhdGggPSByZXF1aXJlKFwiLi4vc2hhcmVkL2xpYi9wYWdlLXBhdGgvZGVub3JtYWxpemUtcGFnZS1wYXRoXCIpO1xuY29uc3QgX25vcm1hbGl6ZXBhZ2VwYXRoID0gcmVxdWlyZShcIi4uL3NoYXJlZC9saWIvcGFnZS1wYXRoL25vcm1hbGl6ZS1wYWdlLXBhdGhcIik7XG5mdW5jdGlvbiBnZXRQYWdlRmlsZXMoYnVpbGRNYW5pZmVzdCwgcGFnZSkge1xuICAgIGNvbnN0IG5vcm1hbGl6ZWRQYWdlID0gKDAsIF9kZW5vcm1hbGl6ZXBhZ2VwYXRoLmRlbm9ybWFsaXplUGFnZVBhdGgpKCgwLCBfbm9ybWFsaXplcGFnZXBhdGgubm9ybWFsaXplUGFnZVBhdGgpKHBhZ2UpKTtcbiAgICBsZXQgZmlsZXMgPSBidWlsZE1hbmlmZXN0LnBhZ2VzW25vcm1hbGl6ZWRQYWdlXTtcbiAgICBpZiAoIWZpbGVzKSB7XG4gICAgICAgIGNvbnNvbGUud2FybihgQ291bGQgbm90IGZpbmQgZmlsZXMgZm9yICR7bm9ybWFsaXplZFBhZ2V9IGluIC5uZXh0L2J1aWxkLW1hbmlmZXN0Lmpzb25gKTtcbiAgICAgICAgcmV0dXJuIFtdO1xuICAgIH1cbiAgICByZXR1cm4gZmlsZXM7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldC1wYWdlLWZpbGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/server/get-page-files.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/server/htmlescape.js":
/*!*********************************************************!*\
  !*** ../../node_modules/next/dist/server/htmlescape.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("// This utility is based on https://github.com/zertosh/htmlescape\n// License: https://github.com/zertosh/htmlescape/blob/0527ca7156a524d256101bb310a9f970f63078ad/LICENSE\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ESCAPE_REGEX: function() {\n        return ESCAPE_REGEX;\n    },\n    htmlEscapeJsonString: function() {\n        return htmlEscapeJsonString;\n    }\n});\nconst ESCAPE_LOOKUP = {\n    \"&\": \"\\\\u0026\",\n    \">\": \"\\\\u003e\",\n    \"<\": \"\\\\u003c\",\n    \"\\u2028\": \"\\\\u2028\",\n    \"\\u2029\": \"\\\\u2029\"\n};\nconst ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction htmlEscapeJsonString(str) {\n    return str.replace(ESCAPE_REGEX, (match)=>ESCAPE_LOOKUP[match]);\n}\n\n//# sourceMappingURL=htmlescape.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/server/htmlescape.js\n");

/***/ }),

/***/ "../../node_modules/next/dist/server/utils.js":
/*!****************************************************!*\
  !*** ../../node_modules/next/dist/server/utils.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    cleanAmpPath: function() {\n        return cleanAmpPath;\n    },\n    debounce: function() {\n        return debounce;\n    },\n    isBlockedPage: function() {\n        return isBlockedPage;\n    }\n});\nconst _constants = __webpack_require__(/*! ../shared/lib/constants */ \"../../node_modules/next/dist/shared/lib/constants.js\");\nfunction isBlockedPage(page) {\n    return _constants.BLOCKED_PAGES.includes(page);\n}\nfunction cleanAmpPath(pathname) {\n    if (pathname.match(/\\?amp=(y|yes|true|1)/)) {\n        pathname = pathname.replace(/\\?amp=(y|yes|true|1)&?/, \"?\");\n    }\n    if (pathname.match(/&amp=(y|yes|true|1)/)) {\n        pathname = pathname.replace(/&amp=(y|yes|true|1)/, \"\");\n    }\n    pathname = pathname.replace(/\\?$/, \"\");\n    return pathname;\n}\nfunction debounce(fn, ms, maxWait = Infinity) {\n    let timeoutId;\n    // The time the debouncing function was first called during this debounce queue.\n    let startTime = 0;\n    // The time the debouncing function was last called.\n    let lastCall = 0;\n    // The arguments and this context of the last call to the debouncing function.\n    let args, context;\n    // A helper used to that either invokes the debounced function, or\n    // reschedules the timer if a more recent call was made.\n    function run() {\n        const now = Date.now();\n        const diff = lastCall + ms - now;\n        // If the diff is non-positive, then we've waited at least `ms`\n        // milliseconds since the last call. Or if we've waited for longer than the\n        // max wait time, we must call the debounced function.\n        if (diff <= 0 || startTime + maxWait >= now) {\n            // It's important to clear the timeout id before invoking the debounced\n            // function, in case the function calls the debouncing function again.\n            timeoutId = undefined;\n            fn.apply(context, args);\n        } else {\n            // Else, a new call was made after the original timer was scheduled. We\n            // didn't clear the timeout (doing so is very slow), so now we need to\n            // reschedule the timer for the time difference.\n            timeoutId = setTimeout(run, diff);\n        }\n    }\n    return function(...passedArgs) {\n        // The arguments and this context of the most recent call are saved so the\n        // debounced function can be invoked with them later.\n        args = passedArgs;\n        context = this;\n        // Instead of constantly clearing and scheduling a timer, we record the\n        // time of the last call. If a second call comes in before the timer fires,\n        // then we'll reschedule in the run function. Doing this is considerably\n        // faster.\n        lastCall = Date.now();\n        // Only schedule a new timer if we're not currently waiting.\n        if (timeoutId === undefined) {\n            startTime = lastCall;\n            timeoutId = setTimeout(run, ms);\n        }\n    };\n}\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/server/utils.js\n");

/***/ })

};
;