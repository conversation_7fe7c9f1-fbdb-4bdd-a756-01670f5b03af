"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_StreamingPlatform_tsx";
exports.ids = ["src_components_StreamingPlatform_tsx"];
exports.modules = {

/***/ "./src/components/StreamingPlatform.tsx":
/*!**********************************************!*\
  !*** ./src/components/StreamingPlatform.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst StreamingPlatform = ({ onStartStreaming })=>{\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"browse\");\n    const [featuredStreams, setFeaturedStreams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamStats, setStreamStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        viewers: 0,\n        followers: 1247,\n        donations: 156.50,\n        chatMessages: 0\n    });\n    const [streamSettings, setStreamSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"Козырь Мастер - Эпические игры!\",\n        quality: \"1080p\",\n        enableChat: true,\n        enableDonations: true,\n        enableAI: true\n    });\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Симуляция популярных стримов\n        setFeaturedStreams([\n            {\n                id: \"1\",\n                title: \"Турнир по Дураку - Финал!\",\n                streamer: \"ProGamer2024\",\n                viewers: 2847,\n                game: \"Дурак\",\n                thumbnail: \"\\uD83C\\uDCCF\",\n                isLive: true,\n                quality: \"1080p\",\n                language: \"RU\",\n                tags: [\n                    \"Турнир\",\n                    \"Финал\",\n                    \"Профи\"\n                ]\n            },\n            {\n                id: \"2\",\n                title: \"Покер с ИИ - Кто умнее?\",\n                streamer: \"AIChallenger\",\n                viewers: 1523,\n                game: \"Покер\",\n                thumbnail: \"♠️\",\n                isLive: true,\n                quality: \"4K\",\n                language: \"EN\",\n                tags: [\n                    \"ИИ\",\n                    \"Покер\",\n                    \"Вызов\"\n                ]\n            },\n            {\n                id: \"3\",\n                title: \"Обучение новичков\",\n                streamer: \"CardMaster\",\n                viewers: 892,\n                game: \"Дурак\",\n                thumbnail: \"\\uD83C\\uDF93\",\n                isLive: true,\n                quality: \"720p\",\n                language: \"RU\",\n                tags: [\n                    \"Обучение\",\n                    \"Новички\"\n                ]\n            }\n        ]);\n        // Симуляция обновления статистики стрима\n        if (isStreaming) {\n            const interval = setInterval(()=>{\n                setStreamStats((prev)=>({\n                        ...prev,\n                        viewers: prev.viewers + Math.floor(Math.random() * 10 - 3),\n                        chatMessages: prev.chatMessages + Math.floor(Math.random() * 5),\n                        donations: prev.donations + (Math.random() > 0.9 ? Math.random() * 20 : 0)\n                    }));\n            }, 3000);\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        isStreaming\n    ]);\n    const startStream = async ()=>{\n        try {\n            // Запрос доступа к камере и микрофону\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: true,\n                audio: true\n            });\n            if (videoRef.current) {\n                videoRef.current.srcObject = stream;\n            }\n            setIsStreaming(true);\n            setStreamStats((prev)=>({\n                    ...prev,\n                    viewers: 1\n                }));\n        } catch (error) {\n            console.error(\"Ошибка доступа к медиа:\", error);\n            // Симуляция стрима без реального видео\n            setIsStreaming(true);\n            setStreamStats((prev)=>({\n                    ...prev,\n                    viewers: 1\n                }));\n        }\n    };\n    const stopStream = ()=>{\n        if (videoRef.current?.srcObject) {\n            const stream = videoRef.current.srcObject;\n            stream.getTracks().forEach((track)=>track.stop());\n        }\n        setIsStreaming(false);\n        setStreamStats((prev)=>({\n                ...prev,\n                viewers: 0\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamingContainer, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentWrapper, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionTitle, {\n                            children: \"Стриминг платформа\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSubtitle, {\n                            children: \"Профессиональные инструменты для создателей контента\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabNavigation, {\n                    children: [\n                        {\n                            id: \"browse\",\n                            label: \"Обзор стримов\",\n                            icon: \"\\uD83D\\uDCFA\"\n                        },\n                        {\n                            id: \"stream\",\n                            label: \"Мой стрим\",\n                            icon: \"\\uD83C\\uDFA5\"\n                        },\n                        {\n                            id: \"analytics\",\n                            label: \"Аналитика\",\n                            icon: \"\\uD83D\\uDCCA\"\n                        }\n                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabButton, {\n                            active: activeTab === tab.id,\n                            onClick: ()=>setActiveTab(tab.id),\n                            whileHover: {\n                                scale: 1.02\n                            },\n                            whileTap: {\n                                scale: 0.98\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabIcon, {\n                                    children: tab.icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabLabel, {\n                                    children: tab.label\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                        mode: \"wait\",\n                        children: [\n                            activeTab === \"browse\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BrowseSection, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSubtitle, {\n                                                    children: \"Популярные стримы\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LiveIndicator, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LiveDot, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        featuredStreams.length,\n                                                        \" стримов в эфире\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamsGrid, {\n                                            children: featuredStreams.map((stream)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamCard, {\n                                                    whileHover: {\n                                                        scale: 1.02\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.98\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamThumbnail, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThumbnailIcon, {\n                                                                    children: stream.thumbnail\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamOverlay, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QualityBadge, {\n                                                                            children: stream.quality\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ViewerCount, {\n                                                                            children: [\n                                                                                \"\\uD83D\\uDC41️ \",\n                                                                                stream.viewers.toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                            lineNumber: 194,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                stream.isLive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LiveBadge, {\n                                                                    children: \"LIVE\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 196,\n                                                                    columnNumber: 45\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamInfo, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamTitle, {\n                                                                    children: stream.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamMeta, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamerName, {\n                                                                            children: stream.streamer\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                            lineNumber: 202,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamGame, {\n                                                                            children: stream.game\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                            lineNumber: 203,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 201,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamTags, {\n                                                                    children: stream.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamTag, {\n                                                                            children: tag\n                                                                        }, tag, false, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                            lineNumber: 207,\n                                                                            columnNumber: 31\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, stream.id, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, \"browse\", false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 15\n                            }, undefined),\n                            activeTab === \"stream\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamSection, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamContainer, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamPreview, {\n                                                    children: [\n                                                        isStreaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VideoPreview, {\n                                                            ref: videoRef,\n                                                            autoPlay: true,\n                                                            muted: true,\n                                                            playsInline: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 25\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PreviewPlaceholder, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlaceholderIcon, {\n                                                                    children: \"\\uD83C\\uDFA5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlaceholderText, {\n                                                                    children: \"Предпросмотр стрима\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 239,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamOverlays, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamStatus, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusDot, {}, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        \"В ЭФИРЕ\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ViewerCounter, {\n                                                                    children: [\n                                                                        \"\\uD83D\\uDC41️ \",\n                                                                        streamStats.viewers\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamControls, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ControlsRow, {\n                                                            children: [\n                                                                !isStreaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StartStreamButton, {\n                                                                    onClick: startStream,\n                                                                    whileHover: {\n                                                                        scale: 1.05\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.95\n                                                                    },\n                                                                    children: \"\\uD83D\\uDE80 Начать стрим\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 27\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StopStreamButton, {\n                                                                    onClick: stopStream,\n                                                                    whileHover: {\n                                                                        scale: 1.05\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.95\n                                                                    },\n                                                                    children: \"⏹️ Остановить стрим\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ControlButton, {\n                                                                    whileHover: {\n                                                                        scale: 1.05\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.95\n                                                                    },\n                                                                    children: \"⚙️ Настройки\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamSettings, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingGroup, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingLabel, {\n                                                                            children: \"Название стрима:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                            lineNumber: 286,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingInput, {\n                                                                            value: streamSettings.title,\n                                                                            onChange: (e)=>setStreamSettings((prev)=>({\n                                                                                        ...prev,\n                                                                                        title: e.target.value\n                                                                                    }))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingGroup, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingLabel, {\n                                                                            children: \"Качество:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                            lineNumber: 297,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingSelect, {\n                                                                            value: streamSettings.quality,\n                                                                            onChange: (e)=>setStreamSettings((prev)=>({\n                                                                                        ...prev,\n                                                                                        quality: e.target.value\n                                                                                    })),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"720p\",\n                                                                                    children: \"720p\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                                    lineNumber: 305,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"1080p\",\n                                                                                    children: \"1080p\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                                    lineNumber: 306,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"4K\",\n                                                                                    children: \"4K\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                                    lineNumber: 307,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                            lineNumber: 298,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingToggles, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToggleOption, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToggleInput, {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: streamSettings.enableChat,\n                                                                                    onChange: (e)=>setStreamSettings((prev)=>({\n                                                                                                ...prev,\n                                                                                                enableChat: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                                    lineNumber: 313,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToggleLabel, {\n                                                                                    children: \"Включить чат\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                                    lineNumber: 321,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                            lineNumber: 312,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToggleOption, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToggleInput, {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: streamSettings.enableDonations,\n                                                                                    onChange: (e)=>setStreamSettings((prev)=>({\n                                                                                                ...prev,\n                                                                                                enableDonations: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                                    lineNumber: 325,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToggleLabel, {\n                                                                                    children: \"Принимать донаты\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                                    lineNumber: 333,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                            lineNumber: 324,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToggleOption, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToggleInput, {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: streamSettings.enableAI,\n                                                                                    onChange: (e)=>setStreamSettings((prev)=>({\n                                                                                                ...prev,\n                                                                                                enableAI: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                                    lineNumber: 337,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToggleLabel, {\n                                                                                    children: \"ИИ-анализ\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                                    lineNumber: 345,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                            lineNumber: 336,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LiveStats, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatIcon, {\n                                                            children: \"\\uD83D\\uDC41️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                            children: streamStats.viewers\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                            children: \"Зрителей\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatIcon, {\n                                                            children: \"\\uD83D\\uDC65\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                            children: streamStats.followers\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                            children: \"Подписчиков\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatIcon, {\n                                                            children: \"\\uD83D\\uDCB0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                            children: [\n                                                                \"$\",\n                                                                streamStats.donations.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                            children: \"Донатов\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatIcon, {\n                                                            children: \"\\uD83D\\uDCAC\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                            children: streamStats.chatMessages\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                            children: \"Сообщений\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, \"stream\", false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 15\n                            }, undefined),\n                            activeTab === \"analytics\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsSection, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsGrid, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsCard, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                        children: \"Статистика за месяц\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricsList, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricItem, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricLabel, {\n                                                                        children: \"Общее время стримов:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                        lineNumber: 397,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricValue, {\n                                                                        children: \"47ч 23м\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricItem, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricLabel, {\n                                                                        children: \"Средние зрители:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricValue, {\n                                                                        children: \"234\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricItem, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricLabel, {\n                                                                        children: \"Пиковые зрители:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                        lineNumber: 405,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricValue, {\n                                                                        children: \"1,847\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricItem, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricLabel, {\n                                                                        children: \"Новых подписчиков:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricValue, {\n                                                                        children: \"+156\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                        lineNumber: 410,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsCard, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                        children: \"Доходы\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RevenueChart, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartBar, {\n                                                                height: 60,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartValue, {\n                                                                    children: \"$245\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartBar, {\n                                                                height: 80,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartValue, {\n                                                                    children: \"$320\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartBar, {\n                                                                height: 45,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartValue, {\n                                                                    children: \"$180\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartBar, {\n                                                                height: 95,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartValue, {\n                                                                    children: \"$425\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsCard, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                        children: \"ИИ Инсайты\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIInsights, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InsightItem, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InsightIcon, {\n                                                                        children: \"\\uD83C\\uDFAF\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                        lineNumber: 437,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InsightText, {\n                                                                        children: \"Лучшее время для стрима: 19:00-22:00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                        lineNumber: 438,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InsightItem, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InsightIcon, {\n                                                                        children: \"\\uD83D\\uDCC8\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InsightText, {\n                                                                        children: \"Турнирные стримы привлекают +40% зрителей\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InsightItem, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InsightIcon, {\n                                                                        children: \"\\uD83D\\uDCA1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InsightText, {\n                                                                        children: \"Рекомендуем добавить больше интерактива\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, \"analytics\", false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/StreamingPlatform.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, undefined);\n};\n// Стилизованные компоненты\nconst StreamingContainer = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StreamingContainer\",\n    componentId: \"sc-a51f6eb7-0\"\n})([\n    \"min-height:100vh;background:linear-gradient(135deg,#0f0f23 0%,#1a1a2e 50%,#16213e 100%);padding:4rem 2rem;display:flex;align-items:center;justify-content:center;\"\n]);\nconst ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__ContentWrapper\",\n    componentId: \"sc-a51f6eb7-1\"\n})([\n    \"max-width:1400px;width:100%;\"\n]);\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h2.withConfig({\n    displayName: \"StreamingPlatform__SectionTitle\",\n    componentId: \"sc-a51f6eb7-2\"\n})([\n    \"font-size:3.5rem;font-weight:900;text-align:center;margin-bottom:1rem;background:linear-gradient(45deg,#4a90e2,#7b68ee,#9370db);-webkit-background-clip:text;-webkit-text-fill-color:transparent;@media (max-width:768px){font-size:2.5rem;}\"\n]);\nconst SectionSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"StreamingPlatform__SectionSubtitle\",\n    componentId: \"sc-a51f6eb7-3\"\n})([\n    \"font-size:1.3rem;color:rgba(255,255,255,0.7);text-align:center;margin-bottom:4rem;max-width:800px;margin-left:auto;margin-right:auto;\"\n]);\nconst TabNavigation = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__TabNavigation\",\n    componentId: \"sc-a51f6eb7-4\"\n})([\n    \"display:flex;gap:0.5rem;margin-bottom:2rem;background:rgba(255,255,255,0.05);border-radius:15px;padding:0.5rem;\"\n]);\nconst TabButton = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"StreamingPlatform__TabButton\",\n    componentId: \"sc-a51f6eb7-5\"\n})([\n    \"flex:1;display:flex;align-items:center;justify-content:center;gap:0.5rem;padding:1rem;border:none;background:\",\n    \";color:\",\n    \";border-radius:10px;cursor:pointer;transition:all 0.3s ease;font-weight:600;&:hover{background:rgba(74,144,226,0.1);color:#4a90e2;}\"\n], (props)=>props.active ? \"rgba(74, 144, 226, 0.3)\" : \"transparent\", (props)=>props.active ? \"#4a90e2\" : \"rgba(255, 255, 255, 0.7)\");\nconst TabIcon = styled_components__WEBPACK_IMPORTED_MODULE_3___default().span.withConfig({\n    displayName: \"StreamingPlatform__TabIcon\",\n    componentId: \"sc-a51f6eb7-6\"\n})([\n    \"font-size:1.2rem;\"\n]);\nconst TabLabel = styled_components__WEBPACK_IMPORTED_MODULE_3___default().span.withConfig({\n    displayName: \"StreamingPlatform__TabLabel\",\n    componentId: \"sc-a51f6eb7-7\"\n})([\n    \"font-size:0.9rem;\"\n]);\nconst TabContent = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__TabContent\",\n    componentId: \"sc-a51f6eb7-8\"\n})([\n    \"min-height:500px;\"\n]);\nconst BrowseSection = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__BrowseSection\",\n    componentId: \"sc-a51f6eb7-9\"\n})([\n    \"\"\n]);\nconst SectionHeader = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__SectionHeader\",\n    componentId: \"sc-a51f6eb7-10\"\n})([\n    \"display:flex;justify-content:space-between;align-items:center;margin-bottom:2rem;\"\n]);\nconst LiveIndicator = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__LiveIndicator\",\n    componentId: \"sc-a51f6eb7-11\"\n})([\n    \"display:flex;align-items:center;gap:0.5rem;color:rgba(255,255,255,0.8);font-size:0.9rem;\"\n]);\nconst LiveDot = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__LiveDot\",\n    componentId: \"sc-a51f6eb7-12\"\n})([\n    \"width:8px;height:8px;background:#ef4444;border-radius:50%;animation:pulse 2s infinite;\"\n]);\nconst StreamsGrid = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StreamsGrid\",\n    componentId: \"sc-a51f6eb7-13\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:1.5rem;\"\n]);\nconst StreamCard = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"StreamingPlatform__StreamCard\",\n    componentId: \"sc-a51f6eb7-14\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;overflow:hidden;border:1px solid rgba(255,255,255,0.1);cursor:pointer;transition:all 0.3s ease;&:hover{background:rgba(255,255,255,0.1);}\"\n]);\nconst StreamThumbnail = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StreamThumbnail\",\n    componentId: \"sc-a51f6eb7-15\"\n})([\n    \"position:relative;height:180px;background:linear-gradient(135deg,#1a1a2e,#16213e);display:flex;align-items:center;justify-content:center;\"\n]);\nconst ThumbnailIcon = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__ThumbnailIcon\",\n    componentId: \"sc-a51f6eb7-16\"\n})([\n    \"font-size:4rem;filter:drop-shadow(0 0 20px currentColor);\"\n]);\nconst StreamOverlay = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StreamOverlay\",\n    componentId: \"sc-a51f6eb7-17\"\n})([\n    \"position:absolute;bottom:0.5rem;left:0.5rem;right:0.5rem;display:flex;justify-content:space-between;align-items:center;\"\n]);\nconst QualityBadge = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__QualityBadge\",\n    componentId: \"sc-a51f6eb7-18\"\n})([\n    \"background:rgba(0,0,0,0.7);color:white;padding:0.25rem 0.5rem;border-radius:5px;font-size:0.7rem;font-weight:600;\"\n]);\nconst ViewerCount = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__ViewerCount\",\n    componentId: \"sc-a51f6eb7-19\"\n})([\n    \"background:rgba(0,0,0,0.7);color:white;padding:0.25rem 0.5rem;border-radius:5px;font-size:0.7rem;font-weight:600;\"\n]);\nconst LiveBadge = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__LiveBadge\",\n    componentId: \"sc-a51f6eb7-20\"\n})([\n    \"position:absolute;top:0.5rem;right:0.5rem;background:#ef4444;color:white;padding:0.25rem 0.5rem;border-radius:5px;font-size:0.7rem;font-weight:700;animation:pulse 2s infinite;\"\n]);\nconst StreamInfo = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StreamInfo\",\n    componentId: \"sc-a51f6eb7-21\"\n})([\n    \"padding:1rem;\"\n]);\nconst StreamTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h4.withConfig({\n    displayName: \"StreamingPlatform__StreamTitle\",\n    componentId: \"sc-a51f6eb7-22\"\n})([\n    \"color:white;font-size:1rem;font-weight:600;margin-bottom:0.5rem;line-height:1.3;\"\n]);\nconst StreamMeta = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StreamMeta\",\n    componentId: \"sc-a51f6eb7-23\"\n})([\n    \"display:flex;justify-content:space-between;align-items:center;margin-bottom:0.75rem;\"\n]);\nconst StreamerName = styled_components__WEBPACK_IMPORTED_MODULE_3___default().span.withConfig({\n    displayName: \"StreamingPlatform__StreamerName\",\n    componentId: \"sc-a51f6eb7-24\"\n})([\n    \"color:#4a90e2;font-weight:600;font-size:0.9rem;\"\n]);\nconst StreamGame = styled_components__WEBPACK_IMPORTED_MODULE_3___default().span.withConfig({\n    displayName: \"StreamingPlatform__StreamGame\",\n    componentId: \"sc-a51f6eb7-25\"\n})([\n    \"color:rgba(255,255,255,0.7);font-size:0.8rem;\"\n]);\nconst StreamTags = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StreamTags\",\n    componentId: \"sc-a51f6eb7-26\"\n})([\n    \"display:flex;flex-wrap:wrap;gap:0.25rem;\"\n]);\nconst StreamTag = styled_components__WEBPACK_IMPORTED_MODULE_3___default().span.withConfig({\n    displayName: \"StreamingPlatform__StreamTag\",\n    componentId: \"sc-a51f6eb7-27\"\n})([\n    \"background:rgba(74,144,226,0.2);color:#4a90e2;padding:0.2rem 0.5rem;border-radius:10px;font-size:0.7rem;font-weight:500;\"\n]);\nconst StreamSection = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StreamSection\",\n    componentId: \"sc-a51f6eb7-28\"\n})([\n    \"\"\n]);\nconst StreamContainer = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StreamContainer\",\n    componentId: \"sc-a51f6eb7-29\"\n})([\n    \"display:grid;grid-template-columns:2fr 1fr;gap:2rem;margin-bottom:2rem;@media (max-width:1024px){grid-template-columns:1fr;}\"\n]);\nconst StreamPreview = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StreamPreview\",\n    componentId: \"sc-a51f6eb7-30\"\n})([\n    \"position:relative;background:rgba(255,255,255,0.05);border-radius:15px;overflow:hidden;aspect-ratio:16/9;border:1px solid rgba(255,255,255,0.1);\"\n]);\nconst VideoPreview = styled_components__WEBPACK_IMPORTED_MODULE_3___default().video.withConfig({\n    displayName: \"StreamingPlatform__VideoPreview\",\n    componentId: \"sc-a51f6eb7-31\"\n})([\n    \"width:100%;height:100%;object-fit:cover;\"\n]);\nconst PreviewPlaceholder = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__PreviewPlaceholder\",\n    componentId: \"sc-a51f6eb7-32\"\n})([\n    \"width:100%;height:100%;display:flex;flex-direction:column;align-items:center;justify-content:center;background:linear-gradient(135deg,#1a1a2e,#16213e);\"\n]);\nconst PlaceholderIcon = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__PlaceholderIcon\",\n    componentId: \"sc-a51f6eb7-33\"\n})([\n    \"font-size:4rem;margin-bottom:1rem;opacity:0.5;\"\n]);\nconst PlaceholderText = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__PlaceholderText\",\n    componentId: \"sc-a51f6eb7-34\"\n})([\n    \"color:rgba(255,255,255,0.5);font-size:1.1rem;\"\n]);\nconst StreamOverlays = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StreamOverlays\",\n    componentId: \"sc-a51f6eb7-35\"\n})([\n    \"position:absolute;top:1rem;left:1rem;right:1rem;display:flex;justify-content:space-between;align-items:center;\"\n]);\nconst StreamStatus = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StreamStatus\",\n    componentId: \"sc-a51f6eb7-36\"\n})([\n    \"display:flex;align-items:center;gap:0.5rem;background:rgba(239,68,68,0.9);color:white;padding:0.5rem 1rem;border-radius:20px;font-weight:700;font-size:0.9rem;\"\n]);\nconst StatusDot = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StatusDot\",\n    componentId: \"sc-a51f6eb7-37\"\n})([\n    \"width:8px;height:8px;background:white;border-radius:50%;animation:pulse 2s infinite;\"\n]);\nconst ViewerCounter = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__ViewerCounter\",\n    componentId: \"sc-a51f6eb7-38\"\n})([\n    \"background:rgba(0,0,0,0.7);color:white;padding:0.5rem 1rem;border-radius:20px;font-weight:600;\"\n]);\nconst StreamControls = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StreamControls\",\n    componentId: \"sc-a51f6eb7-39\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;border:1px solid rgba(255,255,255,0.1);\"\n]);\nconst ControlsRow = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__ControlsRow\",\n    componentId: \"sc-a51f6eb7-40\"\n})([\n    \"display:flex;gap:1rem;margin-bottom:1.5rem;\"\n]);\nconst StartStreamButton = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"StreamingPlatform__StartStreamButton\",\n    componentId: \"sc-a51f6eb7-41\"\n})([\n    \"flex:1;background:linear-gradient(135deg,#4ade80,#22c55e);color:white;border:none;border-radius:10px;padding:1rem;font-size:1rem;font-weight:600;cursor:pointer;\"\n]);\nconst StopStreamButton = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"StreamingPlatform__StopStreamButton\",\n    componentId: \"sc-a51f6eb7-42\"\n})([\n    \"flex:1;background:linear-gradient(135deg,#ef4444,#dc2626);color:white;border:none;border-radius:10px;padding:1rem;font-size:1rem;font-weight:600;cursor:pointer;\"\n]);\nconst ControlButton = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"StreamingPlatform__ControlButton\",\n    componentId: \"sc-a51f6eb7-43\"\n})([\n    \"background:rgba(255,255,255,0.1);color:white;border:1px solid rgba(255,255,255,0.2);border-radius:10px;padding:1rem;cursor:pointer;font-weight:600;\"\n]);\nconst StreamSettings = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StreamSettings\",\n    componentId: \"sc-a51f6eb7-44\"\n})([\n    \"display:flex;flex-direction:column;gap:1rem;\"\n]);\nconst SettingGroup = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__SettingGroup\",\n    componentId: \"sc-a51f6eb7-45\"\n})([\n    \"\"\n]);\nconst SettingLabel = styled_components__WEBPACK_IMPORTED_MODULE_3___default().label.withConfig({\n    displayName: \"StreamingPlatform__SettingLabel\",\n    componentId: \"sc-a51f6eb7-46\"\n})([\n    \"display:block;color:white;font-weight:600;margin-bottom:0.5rem;font-size:0.9rem;\"\n]);\nconst SettingInput = styled_components__WEBPACK_IMPORTED_MODULE_3___default().input.withConfig({\n    displayName: \"StreamingPlatform__SettingInput\",\n    componentId: \"sc-a51f6eb7-47\"\n})([\n    \"width:100%;background:rgba(255,255,255,0.1);border:1px solid rgba(255,255,255,0.2);border-radius:8px;padding:0.75rem;color:white;font-size:0.9rem;&:focus{outline:none;border-color:#4a90e2;}\"\n]);\nconst SettingSelect = styled_components__WEBPACK_IMPORTED_MODULE_3___default().select.withConfig({\n    displayName: \"StreamingPlatform__SettingSelect\",\n    componentId: \"sc-a51f6eb7-48\"\n})([\n    \"width:100%;background:rgba(255,255,255,0.1);border:1px solid rgba(255,255,255,0.2);border-radius:8px;padding:0.75rem;color:white;font-size:0.9rem;&:focus{outline:none;border-color:#4a90e2;}\"\n]);\nconst SettingToggles = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__SettingToggles\",\n    componentId: \"sc-a51f6eb7-49\"\n})([\n    \"display:flex;flex-direction:column;gap:0.75rem;\"\n]);\nconst ToggleOption = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__ToggleOption\",\n    componentId: \"sc-a51f6eb7-50\"\n})([\n    \"display:flex;align-items:center;gap:0.5rem;\"\n]);\nconst ToggleInput = styled_components__WEBPACK_IMPORTED_MODULE_3___default().input.withConfig({\n    displayName: \"StreamingPlatform__ToggleInput\",\n    componentId: \"sc-a51f6eb7-51\"\n})([\n    \"width:18px;height:18px;accent-color:#4a90e2;\"\n]);\nconst ToggleLabel = styled_components__WEBPACK_IMPORTED_MODULE_3___default().label.withConfig({\n    displayName: \"StreamingPlatform__ToggleLabel\",\n    componentId: \"sc-a51f6eb7-52\"\n})([\n    \"color:rgba(255,255,255,0.8);font-size:0.9rem;cursor:pointer;\"\n]);\nconst LiveStats = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__LiveStats\",\n    componentId: \"sc-a51f6eb7-53\"\n})([\n    \"display:grid;grid-template-columns:repeat(4,1fr);gap:1rem;@media (max-width:768px){grid-template-columns:repeat(2,1fr);}\"\n]);\nconst StatCard = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StatCard\",\n    componentId: \"sc-a51f6eb7-54\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;text-align:center;border:1px solid rgba(255,255,255,0.1);\"\n]);\nconst StatIcon = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StatIcon\",\n    componentId: \"sc-a51f6eb7-55\"\n})([\n    \"font-size:2rem;margin-bottom:0.5rem;\"\n]);\nconst StatValue = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StatValue\",\n    componentId: \"sc-a51f6eb7-56\"\n})([\n    \"color:#4a90e2;font-size:1.5rem;font-weight:700;margin-bottom:0.25rem;\"\n]);\nconst StatLabel = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__StatLabel\",\n    componentId: \"sc-a51f6eb7-57\"\n})([\n    \"color:rgba(255,255,255,0.7);font-size:0.8rem;\"\n]);\nconst AnalyticsSection = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__AnalyticsSection\",\n    componentId: \"sc-a51f6eb7-58\"\n})([\n    \"\"\n]);\nconst AnalyticsGrid = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__AnalyticsGrid\",\n    componentId: \"sc-a51f6eb7-59\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:1.5rem;\"\n]);\nconst AnalyticsCard = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__AnalyticsCard\",\n    componentId: \"sc-a51f6eb7-60\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;border:1px solid rgba(255,255,255,0.1);\"\n]);\nconst CardTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h4.withConfig({\n    displayName: \"StreamingPlatform__CardTitle\",\n    componentId: \"sc-a51f6eb7-61\"\n})([\n    \"color:white;font-size:1.2rem;font-weight:700;margin-bottom:1rem;\"\n]);\nconst MetricsList = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__MetricsList\",\n    componentId: \"sc-a51f6eb7-62\"\n})([\n    \"display:flex;flex-direction:column;gap:0.75rem;\"\n]);\nconst MetricItem = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__MetricItem\",\n    componentId: \"sc-a51f6eb7-63\"\n})([\n    \"display:flex;justify-content:space-between;align-items:center;\"\n]);\nconst MetricLabel = styled_components__WEBPACK_IMPORTED_MODULE_3___default().span.withConfig({\n    displayName: \"StreamingPlatform__MetricLabel\",\n    componentId: \"sc-a51f6eb7-64\"\n})([\n    \"color:rgba(255,255,255,0.7);font-size:0.9rem;\"\n]);\nconst MetricValue = styled_components__WEBPACK_IMPORTED_MODULE_3___default().span.withConfig({\n    displayName: \"StreamingPlatform__MetricValue\",\n    componentId: \"sc-a51f6eb7-65\"\n})([\n    \"color:#4a90e2;font-weight:600;\"\n]);\nconst RevenueChart = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__RevenueChart\",\n    componentId: \"sc-a51f6eb7-66\"\n})([\n    \"display:flex;align-items:end;gap:1rem;height:120px;\"\n]);\nconst ChartBar = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__ChartBar\",\n    componentId: \"sc-a51f6eb7-67\"\n})([\n    \"flex:1;height:\",\n    \"%;background:linear-gradient(to top,#4a90e2,#7b68ee);border-radius:5px 5px 0 0;position:relative;display:flex;align-items:end;justify-content:center;padding-bottom:0.5rem;\"\n], (props)=>props.height);\nconst ChartValue = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__ChartValue\",\n    componentId: \"sc-a51f6eb7-68\"\n})([\n    \"color:white;font-size:0.8rem;font-weight:600;\"\n]);\nconst AIInsights = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__AIInsights\",\n    componentId: \"sc-a51f6eb7-69\"\n})([\n    \"display:flex;flex-direction:column;gap:1rem;\"\n]);\nconst InsightItem = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__InsightItem\",\n    componentId: \"sc-a51f6eb7-70\"\n})([\n    \"display:flex;align-items:center;gap:1rem;\"\n]);\nconst InsightIcon = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__InsightIcon\",\n    componentId: \"sc-a51f6eb7-71\"\n})([\n    \"font-size:1.5rem;\"\n]);\nconst InsightText = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"StreamingPlatform__InsightText\",\n    componentId: \"sc-a51f6eb7-72\"\n})([\n    \"color:rgba(255,255,255,0.8);font-size:0.9rem;line-height:1.4;\"\n]);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StreamingPlatform);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/StreamingPlatform.tsx\n");

/***/ })

};
;