"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_GameDemo_tsx";
exports.ids = ["src_components_GameDemo_tsx"];
exports.modules = {

/***/ "./src/components/GameDemo.tsx":
/*!*************************************!*\
  !*** ./src/components/GameDemo.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/fiber */ \"@react-three/fiber\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/drei */ \"@react-three/drei\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_react_three_drei__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n// 3D карта компонент\nconst Card3D = ({ card, onClick, isHovered })=>{\n    const meshRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.useFrame)((state)=>{\n        if (meshRef.current) {\n            meshRef.current.rotation.y = isHovered ? Math.sin(state.clock.elapsedTime * 2) * 0.1 : 0;\n            meshRef.current.position.y = isHovered ? card.position.y + Math.sin(state.clock.elapsedTime * 4) * 0.05 : card.position.y;\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_4__.Float, {\n        speed: 2,\n        rotationIntensity: 0.5,\n        floatIntensity: 0.3,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n            ref: meshRef,\n            position: [\n                card.position.x,\n                card.position.y,\n                card.position.z\n            ],\n            onClick: onClick,\n            onPointerOver: (e)=>e.stopPropagation(),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                    args: [\n                        0.8,\n                        1.2,\n                        0.05\n                    ]\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                    color: card.isRevealed ? \"white\" : \"#1a4480\",\n                    metalness: 0.3,\n                    roughness: 0.4\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, undefined),\n                card.isRevealed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_4__.Text3D, {\n                    font: \"/fonts/helvetiker_regular.typeface.json\",\n                    size: 0.2,\n                    height: 0.01,\n                    position: [\n                        -0.2,\n                        0,\n                        0.026\n                    ],\n                    children: [\n                        card.value,\n                        \" \",\n                        card.suit,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                            color: card.color === \"red\" ? \"#dc2626\" : \"#1f2937\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n// Игровой стол 3D\nconst GameTable3D = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"group\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                position: [\n                    0,\n                    -0.5,\n                    0\n                ],\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"cylinderGeometry\", {\n                        args: [\n                            3,\n                            3,\n                            0.1,\n                            32\n                        ]\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                        color: \"#0f4c3a\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                position: [\n                    0,\n                    -0.44,\n                    0\n                ],\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"cylinderGeometry\", {\n                        args: [\n                            2.9,\n                            2.9,\n                            0.02,\n                            32\n                        ]\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                        color: \"#1a5d4a\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, undefined);\n};\nconst GameDemo = ({ onStartGame })=>{\n    const [cards, setCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"waiting\");\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        player: 0,\n        ai: 0\n    });\n    const [hoveredCard, setHoveredCard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [aiThinking, setAiThinking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [gameStats, setGameStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        cardsPlayed: 0,\n        winStreak: 0,\n        totalGames: 0,\n        aiPredictionAccuracy: 0.85\n    });\n    // Создание колоды карт\n    const createDeck = ()=>{\n        const suits = [\n            \"♠️\",\n            \"♥️\",\n            \"♦️\",\n            \"♣️\"\n        ];\n        const values = [\n            \"6\",\n            \"7\",\n            \"8\",\n            \"9\",\n            \"10\",\n            \"J\",\n            \"Q\",\n            \"K\",\n            \"A\"\n        ];\n        const deck = [];\n        suits.forEach((suit)=>{\n            values.forEach((value)=>{\n                deck.push({\n                    id: `${suit}-${value}`,\n                    suit,\n                    value,\n                    color: suit === \"♥️\" || suit === \"♦️\" ? \"red\" : \"black\",\n                    isRevealed: false,\n                    position: {\n                        x: 0,\n                        y: 0,\n                        z: 0\n                    }\n                });\n            });\n        });\n        return deck.sort(()=>Math.random() - 0.5);\n    };\n    // Раздача карт\n    const dealCards = async ()=>{\n        setGameState(\"dealing\");\n        const deck = createDeck();\n        const playerCards = deck.slice(0, 6);\n        const aiCards = deck.slice(6, 12);\n        // Позиционирование карт игрока\n        playerCards.forEach((card, index)=>{\n            card.position = {\n                x: (index - 2.5) * 0.9,\n                y: -1.5,\n                z: 0\n            };\n            card.isRevealed = true;\n        });\n        // Позиционирование карт ИИ\n        aiCards.forEach((card, index)=>{\n            card.position = {\n                x: (index - 2.5) * 0.9,\n                y: 1.5,\n                z: 0\n            };\n        });\n        setCards([\n            ...playerCards,\n            ...aiCards\n        ]);\n        // Анимация раздачи\n        for(let i = 0; i < 12; i++){\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            setCards((prev)=>{\n                const newCards = [\n                    ...prev\n                ];\n                if (newCards[i]) {\n                    newCards[i].isRevealed = i < 6; // Только карты игрока открыты\n                }\n                return newCards;\n            });\n        }\n        setGameState(\"playing\");\n    };\n    // Ход игрока\n    const playCard = async (cardId)=>{\n        if (gameState !== \"playing\" || aiThinking) return;\n        setCards((prev)=>prev.map((card)=>card.id === cardId ? {\n                    ...card,\n                    position: {\n                        ...card.position,\n                        y: 0,\n                        z: 0.5\n                    }\n                } : card));\n        setGameStats((prev)=>({\n                ...prev,\n                cardsPlayed: prev.cardsPlayed + 1\n            }));\n        // ИИ думает\n        setAiThinking(true);\n        await new Promise((resolve)=>setTimeout(resolve, 1500));\n        // ИИ играет карту\n        const aiCards = cards.filter((card)=>card.position.y > 0 && !card.isRevealed);\n        if (aiCards.length > 0) {\n            const aiCard = aiCards[Math.floor(Math.random() * aiCards.length)];\n            setCards((prev)=>prev.map((card)=>card.id === aiCard.id ? {\n                        ...card,\n                        position: {\n                            ...card.position,\n                            y: 0,\n                            z: -0.5\n                        },\n                        isRevealed: true\n                    } : card));\n        }\n        setAiThinking(false);\n        // Определение победителя хода (упрощенная логика)\n        const winner = Math.random() > 0.5 ? \"player\" : \"ai\";\n        setScore((prev)=>({\n                ...prev,\n                [winner]: prev[winner] + 1\n            }));\n        // Проверка окончания игры\n        if (score.player + score.ai >= 5) {\n            setGameState(\"finished\");\n            setGameStats((prev)=>({\n                    ...prev,\n                    totalGames: prev.totalGames + 1,\n                    winStreak: winner === \"player\" ? prev.winStreak + 1 : 0\n                }));\n        }\n    };\n    // Новая игра\n    const startNewGame = ()=>{\n        setCards([]);\n        setGameState(\"waiting\");\n        setScore({\n            player: 0,\n            ai: 0\n        });\n        setHoveredCard(null);\n        setAiThinking(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameDemoContainer, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentWrapper, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionTitle, {\n                            children: \"Интерактивная игровая демонстрация\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSubtitle, {\n                            children: \"Попробуйте революционный игровой движок в действии\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameContainer, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameCanvas, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.Canvas, {\n                                    camera: {\n                                        position: [\n                                            0,\n                                            2,\n                                            8\n                                        ],\n                                        fov: 60\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ambientLight\", {\n                                            intensity: 0.4\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pointLight\", {\n                                            position: [\n                                                5,\n                                                5,\n                                                5\n                                            ],\n                                            intensity: 1\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pointLight\", {\n                                            position: [\n                                                -5,\n                                                -5,\n                                                -5\n                                            ],\n                                            intensity: 0.5,\n                                            color: \"#4a90e2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameTable3D, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        cards.map((card)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card3D, {\n                                                card: card,\n                                                isHovered: hoveredCard === card.id,\n                                                onClick: ()=>{\n                                                    if (card.position.y < 0 && card.isRevealed) {\n                                                        playCard(card.id);\n                                                    }\n                                                }\n                                            }, card.id, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_4__.OrbitControls, {\n                                            enableZoom: false,\n                                            enablePan: false\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameOverlay, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScoreBoard, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScoreItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScoreLabel, {\n                                                            children: \"Игрок\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScoreValue, {\n                                                            children: score.player\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScoreItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScoreLabel, {\n                                                            children: \"ИИ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScoreValue, {\n                                                            children: score.ai\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        aiThinking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIStatus, {\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.8\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                scale: 1\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                scale: 0.8\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIIcon, {\n                                                    children: \"\\uD83E\\uDDE0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIText, {\n                                                    children: \"ИИ анализирует ход...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIProgress, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameStatus, {\n                                            children: [\n                                                gameState === \"waiting\" && \"Готов к игре\",\n                                                gameState === \"dealing\" && \"Раздача карт...\",\n                                                gameState === \"playing\" && \"Ваш ход\",\n                                                gameState === \"finished\" && `Игра окончена! ${score.player > score.ai ? \"Вы победили!\" : \"ИИ победил!\"}`\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ControlPanel, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GameControls, {\n                                    children: [\n                                        gameState === \"waiting\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ControlButton, {\n                                            primary: true,\n                                            onClick: dealCards,\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            children: \"\\uD83C\\uDFB4 Начать игру\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        gameState === \"finished\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ControlButton, {\n                                            primary: true,\n                                            onClick: startNewGame,\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            children: \"\\uD83D\\uDD04 Новая игра\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ControlButton, {\n                                            onClick: onStartGame,\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            children: \"\\uD83D\\uDE80 Полная версия\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsPanel, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsTitle, {\n                                            children: \"Статистика сессии\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsList, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                            children: \"Карт сыграно:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                            children: gameStats.cardsPlayed\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                            children: \"Серия побед:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                            children: gameStats.winStreak\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                            children: \"Всего игр:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                            children: gameStats.totalGames\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                            children: \"Точность ИИ:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                            children: [\n                                                                (gameStats.aiPredictionAccuracy * 100).toFixed(1),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechFeatures, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureItem, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureIcon, {\n                                    children: \"⚛️\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureText, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureTitle, {\n                                            children: \"Квантовая случайность\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureDescription, {\n                                            children: \"Истинно случайная раздача карт\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureItem, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureIcon, {\n                                    children: \"\\uD83E\\uDDE0\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureText, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureTitle, {\n                                            children: \"ИИ противник\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureDescription, {\n                                            children: \"Адаптивный искусственный интеллект\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureItem, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureIcon, {\n                                    children: \"\\uD83C\\uDFAE\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureText, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureTitle, {\n                                            children: \"3D графика\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureDescription, {\n                                            children: \"Иммерсивный игровой опыт\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureItem, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureIcon, {\n                                    children: \"\\uD83D\\uDCCA\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureText, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureTitle, {\n                                            children: \"Реальная аналитика\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureDescription, {\n                                            children: \"Детальная статистика игры\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n            lineNumber: 224,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/GameDemo.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, undefined);\n};\n// Стилизованные компоненты\nconst GameDemoContainer = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__GameDemoContainer\",\n    componentId: \"sc-e4ea546e-0\"\n})([\n    \"min-height:100vh;background:linear-gradient(135deg,#0f0f23 0%,#1a1a2e 50%,#16213e 100%);padding:4rem 2rem;display:flex;align-items:center;justify-content:center;\"\n]);\nconst ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__ContentWrapper\",\n    componentId: \"sc-e4ea546e-1\"\n})([\n    \"max-width:1400px;width:100%;\"\n]);\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_5___default().h2.withConfig({\n    displayName: \"GameDemo__SectionTitle\",\n    componentId: \"sc-e4ea546e-2\"\n})([\n    \"font-size:3.5rem;font-weight:900;text-align:center;margin-bottom:1rem;background:linear-gradient(45deg,#4a90e2,#7b68ee,#9370db);-webkit-background-clip:text;-webkit-text-fill-color:transparent;@media (max-width:768px){font-size:2.5rem;}\"\n]);\nconst SectionSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_5___default().p.withConfig({\n    displayName: \"GameDemo__SectionSubtitle\",\n    componentId: \"sc-e4ea546e-3\"\n})([\n    \"font-size:1.3rem;color:rgba(255,255,255,0.7);text-align:center;margin-bottom:4rem;max-width:800px;margin-left:auto;margin-right:auto;\"\n]);\nconst GameContainer = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__GameContainer\",\n    componentId: \"sc-e4ea546e-4\"\n})([\n    \"display:grid;grid-template-columns:2fr 1fr;gap:2rem;margin-bottom:4rem;@media (max-width:1024px){grid-template-columns:1fr;}\"\n]);\nconst GameCanvas = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__GameCanvas\",\n    componentId: \"sc-e4ea546e-5\"\n})([\n    \"position:relative;height:600px;background:rgba(255,255,255,0.05);border-radius:20px;overflow:hidden;border:1px solid rgba(255,255,255,0.1);\"\n]);\nconst GameOverlay = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__GameOverlay\",\n    componentId: \"sc-e4ea546e-6\"\n})([\n    \"position:absolute;top:0;left:0;right:0;bottom:0;pointer-events:none;z-index:10;\"\n]);\nconst ScoreBoard = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__ScoreBoard\",\n    componentId: \"sc-e4ea546e-7\"\n})([\n    \"position:absolute;top:1rem;left:1rem;display:flex;gap:1rem;\"\n]);\nconst ScoreItem = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__ScoreItem\",\n    componentId: \"sc-e4ea546e-8\"\n})([\n    \"background:rgba(0,0,0,0.5);backdrop-filter:blur(10px);border-radius:10px;padding:0.75rem 1rem;text-align:center;\"\n]);\nconst ScoreLabel = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__ScoreLabel\",\n    componentId: \"sc-e4ea546e-9\"\n})([\n    \"font-size:0.8rem;color:rgba(255,255,255,0.7);margin-bottom:0.25rem;\"\n]);\nconst ScoreValue = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__ScoreValue\",\n    componentId: \"sc-e4ea546e-10\"\n})([\n    \"font-size:1.5rem;font-weight:700;color:#4a90e2;\"\n]);\nconst AIStatus = styled_components__WEBPACK_IMPORTED_MODULE_5___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"GameDemo__AIStatus\",\n    componentId: \"sc-e4ea546e-11\"\n})([\n    \"position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:rgba(0,0,0,0.8);backdrop-filter:blur(20px);border-radius:15px;padding:1.5rem;text-align:center;border:1px solid rgba(74,144,226,0.3);\"\n]);\nconst AIIcon = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__AIIcon\",\n    componentId: \"sc-e4ea546e-12\"\n})([\n    \"font-size:2rem;margin-bottom:0.5rem;\"\n]);\nconst AIText = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__AIText\",\n    componentId: \"sc-e4ea546e-13\"\n})([\n    \"color:white;font-weight:600;margin-bottom:1rem;\"\n]);\nconst AIProgress = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__AIProgress\",\n    componentId: \"sc-e4ea546e-14\"\n})([\n    \"width:100px;height:4px;background:rgba(255,255,255,0.2);border-radius:2px;overflow:hidden;&::after{content:'';display:block;width:30%;height:100%;background:linear-gradient(90deg,#4a90e2,#7b68ee);animation:progress 1.5s infinite;}@keyframes progress{0%{transform:translateX(-100%);}100%{transform:translateX(300%);}}\"\n]);\nconst GameStatus = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__GameStatus\",\n    componentId: \"sc-e4ea546e-15\"\n})([\n    \"position:absolute;bottom:1rem;left:50%;transform:translateX(-50%);background:rgba(0,0,0,0.5);backdrop-filter:blur(10px);border-radius:10px;padding:0.75rem 1.5rem;color:white;font-weight:600;\"\n]);\nconst ControlPanel = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__ControlPanel\",\n    componentId: \"sc-e4ea546e-16\"\n})([\n    \"display:flex;flex-direction:column;gap:2rem;\"\n]);\nconst GameControls = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__GameControls\",\n    componentId: \"sc-e4ea546e-17\"\n})([\n    \"display:flex;flex-direction:column;gap:1rem;\"\n]);\nconst ControlButton = styled_components__WEBPACK_IMPORTED_MODULE_5___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"GameDemo__ControlButton\",\n    componentId: \"sc-e4ea546e-18\"\n})([\n    \"background:\",\n    \";color:white;border:\",\n    \";border-radius:10px;padding:1rem 1.5rem;font-size:1rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;&:hover{background:\",\n    \";}\"\n], (props)=>props.primary ? \"linear-gradient(135deg, #4a90e2, #7b68ee)\" : \"rgba(255, 255, 255, 0.1)\", (props)=>props.primary ? \"none\" : \"1px solid rgba(255, 255, 255, 0.2)\", (props)=>props.primary ? \"linear-gradient(135deg, #5ba0f2, #8b78fe)\" : \"rgba(255, 255, 255, 0.2)\");\nconst StatsPanel = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__StatsPanel\",\n    componentId: \"sc-e4ea546e-19\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;border:1px solid rgba(255,255,255,0.1);\"\n]);\nconst StatsTitle = styled_components__WEBPACK_IMPORTED_MODULE_5___default().h4.withConfig({\n    displayName: \"GameDemo__StatsTitle\",\n    componentId: \"sc-e4ea546e-20\"\n})([\n    \"color:white;font-size:1.1rem;font-weight:600;margin-bottom:1rem;\"\n]);\nconst StatsList = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__StatsList\",\n    componentId: \"sc-e4ea546e-21\"\n})([\n    \"display:flex;flex-direction:column;gap:0.75rem;\"\n]);\nconst StatItem = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__StatItem\",\n    componentId: \"sc-e4ea546e-22\"\n})([\n    \"display:flex;justify-content:space-between;align-items:center;\"\n]);\nconst StatLabel = styled_components__WEBPACK_IMPORTED_MODULE_5___default().span.withConfig({\n    displayName: \"GameDemo__StatLabel\",\n    componentId: \"sc-e4ea546e-23\"\n})([\n    \"color:rgba(255,255,255,0.7);font-size:0.9rem;\"\n]);\nconst StatValue = styled_components__WEBPACK_IMPORTED_MODULE_5___default().span.withConfig({\n    displayName: \"GameDemo__StatValue\",\n    componentId: \"sc-e4ea546e-24\"\n})([\n    \"color:#4a90e2;font-weight:600;\"\n]);\nconst TechFeatures = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__TechFeatures\",\n    componentId: \"sc-e4ea546e-25\"\n})([\n    \"display:grid;grid-template-columns:repeat(4,1fr);gap:1.5rem;@media (max-width:768px){grid-template-columns:repeat(2,1fr);}@media (max-width:480px){grid-template-columns:1fr;}\"\n]);\nconst FeatureItem = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__FeatureItem\",\n    componentId: \"sc-e4ea546e-26\"\n})([\n    \"display:flex;align-items:center;gap:1rem;background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;border:1px solid rgba(255,255,255,0.1);\"\n]);\nconst FeatureIcon = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__FeatureIcon\",\n    componentId: \"sc-e4ea546e-27\"\n})([\n    \"font-size:2rem;\"\n]);\nconst FeatureText = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__FeatureText\",\n    componentId: \"sc-e4ea546e-28\"\n})([\n    \"\"\n]);\nconst FeatureTitle = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__FeatureTitle\",\n    componentId: \"sc-e4ea546e-29\"\n})([\n    \"color:white;font-weight:600;margin-bottom:0.25rem;\"\n]);\nconst FeatureDescription = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"GameDemo__FeatureDescription\",\n    componentId: \"sc-e4ea546e-30\"\n})([\n    \"color:rgba(255,255,255,0.7);font-size:0.8rem;\"\n]);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GameDemo);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/GameDemo.tsx\n");

/***/ })

};
;