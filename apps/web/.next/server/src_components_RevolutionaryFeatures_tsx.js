"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_RevolutionaryFeatures_tsx";
exports.ids = ["src_components_RevolutionaryFeatures_tsx"];
exports.modules = {

/***/ "./src/components/RevolutionaryFeatures.tsx":
/*!**************************************************!*\
  !*** ./src/components/RevolutionaryFeatures.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst features = [\n    {\n        id: \"quantum\",\n        title: \"Квантовый игровой движок\",\n        subtitle: \"Истинная случайность из квантовых источников\",\n        description: \"Первая в мире игровая платформа, использующая реальные квантовые источники случайности для абсолютной честности игр.\",\n        icon: \"⚛️\",\n        color: \"#4a90e2\",\n        stats: [\n            {\n                label: \"Энтропия\",\n                value: \"99.99%\"\n            },\n            {\n                label: \"Квантовых источников\",\n                value: \"5\"\n            },\n            {\n                label: \"Тестов качества\",\n                value: \"15+\"\n            }\n        ],\n        technologies: [\n            \"ANU Quantum RNG\",\n            \"ID Quantique\",\n            \"PicoQuant\",\n            \"Quantum Dice\",\n            \"NIST Tests\"\n        ],\n        benefits: [\n            \"Абсолютная честность\",\n            \"Невозможность предсказания\",\n            \"Научная валидация\"\n        ]\n    },\n    {\n        id: \"ai\",\n        title: \"Эмоциональный ИИ\",\n        subtitle: \"Глубокое понимание каждого игрока\",\n        description: \"Революционная система искусственного интеллекта, анализирующая эмоциональное состояние и персонализирующая игровой опыт.\",\n        icon: \"\\uD83E\\uDDE0\",\n        color: \"#7b68ee\",\n        stats: [\n            {\n                label: \"Эмоциональных состояний\",\n                value: \"8\"\n            },\n            {\n                label: \"Точность анализа\",\n                value: \"95%\"\n            },\n            {\n                label: \"ML моделей\",\n                value: \"6\"\n            }\n        ],\n        technologies: [\n            \"TensorFlow\",\n            \"OpenAI GPT-4\",\n            \"Computer Vision\",\n            \"NLP\",\n            \"Behavioral Analysis\"\n        ],\n        benefits: [\n            \"Персонализация\",\n            \"Предотвращение тильта\",\n            \"Адаптивное обучение\"\n        ]\n    },\n    {\n        id: \"metaverse\",\n        title: \"3D Метавселенная\",\n        subtitle: \"Иммерсивные игровые миры\",\n        description: \"Полноценная метавселенная с 3D мирами, VR/AR поддержкой и социальными пространствами для революционного игрового опыта.\",\n        icon: \"\\uD83C\\uDF0D\",\n        color: \"#9370db\",\n        stats: [\n            {\n                label: \"Тематических миров\",\n                value: \"6\"\n            },\n            {\n                label: \"Одновременных игроков\",\n                value: \"10K+\"\n            },\n            {\n                label: \"VR/AR поддержка\",\n                value: \"100%\"\n            }\n        ],\n        technologies: [\n            \"Three.js\",\n            \"WebXR\",\n            \"WebGL\",\n            \"Physics Engine\",\n            \"Spatial Audio\"\n        ],\n        benefits: [\n            \"Иммерсивность\",\n            \"Социальное взаимодействие\",\n            \"Новый опыт\"\n        ]\n    },\n    {\n        id: \"security\",\n        title: \"Квантовая безопасность\",\n        subtitle: \"Непробиваемая защита\",\n        description: \"Передовые системы безопасности с квантовым шифрованием, биометрией и ИИ-детекцией угроз.\",\n        icon: \"\\uD83D\\uDEE1️\",\n        color: \"#ff6b6b\",\n        stats: [\n            {\n                label: \"Точность детекции\",\n                value: \"99.9%\"\n            },\n            {\n                label: \"Типов угроз\",\n                value: \"6\"\n            },\n            {\n                label: \"Время реакции\",\n                value: \"<1с\"\n            }\n        ],\n        technologies: [\n            \"Post-Quantum Crypto\",\n            \"Zero-Knowledge Proofs\",\n            \"Biometrics\",\n            \"AI Detection\"\n        ],\n        benefits: [\n            \"Абсолютная защита\",\n            \"Приватность\",\n            \"Доверие\"\n        ]\n    },\n    {\n        id: \"analytics\",\n        title: \"Предиктивная аналитика\",\n        subtitle: \"ИИ предсказывает будущее\",\n        description: \"Мощная система машинного обучения, предсказывающая игровые события и персонализирующая опыт.\",\n        icon: \"\\uD83D\\uDCCA\",\n        color: \"#4ecdc4\",\n        stats: [\n            {\n                label: \"Точность предсказаний\",\n                value: \"85%\"\n            },\n            {\n                label: \"Анализируемых метрик\",\n                value: \"100+\"\n            },\n            {\n                label: \"Обновлений в секунду\",\n                value: \"1000+\"\n            }\n        ],\n        technologies: [\n            \"Machine Learning\",\n            \"Real-time Analytics\",\n            \"Predictive Models\",\n            \"Big Data\"\n        ],\n        benefits: [\n            \"Персонализация\",\n            \"Оптимизация\",\n            \"Инсайты\"\n        ]\n    },\n    {\n        id: \"web3\",\n        title: \"Web3 экосистема\",\n        subtitle: \"Децентрализованное будущее\",\n        description: \"Полная интеграция с блокчейном, NFT картами, DeFi протоколами и DAO управлением.\",\n        icon: \"⛓️\",\n        color: \"#feca57\",\n        stats: [\n            {\n                label: \"Смарт-контрактов\",\n                value: \"6\"\n            },\n            {\n                label: \"Поддерживаемых сетей\",\n                value: \"5+\"\n            },\n            {\n                label: \"NFT коллекций\",\n                value: \"10+\"\n            }\n        ],\n        technologies: [\n            \"Ethereum\",\n            \"Polygon\",\n            \"IPFS\",\n            \"Smart Contracts\",\n            \"DeFi\"\n        ],\n        benefits: [\n            \"Владение активами\",\n            \"Децентрализация\",\n            \"Новая экономика\"\n        ]\n    }\n];\nconst RevolutionaryFeatures = ({ currentSection })=>{\n    const [activeFeature, setActiveFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hoveredFeature, setHoveredFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            setActiveFeature((prev)=>(prev + 1) % features.length);\n        }, 5000);\n        return ()=>clearInterval(interval);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesContainer, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentWrapper, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionTitle, {\n                            children: \"Революционные технологии\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSubtitle, {\n                            children: \"Мы объединили передовые технологии будущего в одной платформе\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesGrid, {\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {\n                            active: index === activeFeature,\n                            hovered: hoveredFeature === feature.id,\n                            color: feature.color,\n                            onMouseEnter: ()=>setHoveredFeature(feature.id),\n                            onMouseLeave: ()=>setHoveredFeature(null),\n                            onClick: ()=>setActiveFeature(index),\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureIcon, {\n                                    active: index === activeFeature,\n                                    children: feature.icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureTitle, {\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureSubtitle, {\n                                    children: feature.subtitle\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                                    children: (index === activeFeature || hoveredFeature === feature.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureDetails, {\n                                        initial: {\n                                            opacity: 0,\n                                            height: 0\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            height: \"auto\"\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            height: 0\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureDescription, {\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsGrid, {\n                                                children: feature.stats.map((stat, statIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatItem, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatValue, {\n                                                                children: stat.value\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatLabel, {\n                                                                children: stat.label\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, statIndex, true, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, feature.id, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedView, {\n                        initial: {\n                            opacity: 0,\n                            x: 50\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            x: -50\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedIcon, {\n                                            color: features[activeFeature].color,\n                                            children: features[activeFeature].icon\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedTitle, {\n                                                    children: features[activeFeature].title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedSubtitle, {\n                                                    children: features[activeFeature].subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedDescription, {\n                                    children: features[activeFeature].description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechnologiesSection, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionLabel, {\n                                            children: \"Технологии:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechnologiesList, {\n                                            children: features[activeFeature].technologies.map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechTag, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        scale: 0\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        scale: 1\n                                                    },\n                                                    transition: {\n                                                        delay: index * 0.1\n                                                    },\n                                                    children: tech\n                                                }, index, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BenefitsSection, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionLabel, {\n                                            children: \"Преимущества:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BenefitsList, {\n                                            children: features[activeFeature].benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BenefitItem, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: index * 0.1\n                                                    },\n                                                    children: [\n                                                        \"✨ \",\n                                                        benefit\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, undefined)\n                    }, activeFeature, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgressIndicators, {\n                    children: features.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgressDot, {\n                            active: index === activeFeature,\n                            onClick: ()=>setActiveFeature(index),\n                            whileHover: {\n                                scale: 1.2\n                            },\n                            whileTap: {\n                                scale: 0.9\n                            }\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n            lineNumber: 128,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/RevolutionaryFeatures.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, undefined);\n};\n// Стилизованные компоненты\nconst FeaturesContainer = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"RevolutionaryFeatures__FeaturesContainer\",\n    componentId: \"sc-146f8b4-0\"\n})([\n    \"min-height:100vh;background:linear-gradient(135deg,#0f0f23 0%,#1a1a2e 50%,#16213e 100%);padding:4rem 2rem;display:flex;align-items:center;justify-content:center;\"\n]);\nconst ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"RevolutionaryFeatures__ContentWrapper\",\n    componentId: \"sc-146f8b4-1\"\n})([\n    \"max-width:1400px;width:100%;\"\n]);\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h2.withConfig({\n    displayName: \"RevolutionaryFeatures__SectionTitle\",\n    componentId: \"sc-146f8b4-2\"\n})([\n    \"font-size:3.5rem;font-weight:900;text-align:center;margin-bottom:1rem;background:linear-gradient(45deg,#4a90e2,#7b68ee,#9370db);-webkit-background-clip:text;-webkit-text-fill-color:transparent;@media (max-width:768px){font-size:2.5rem;}\"\n]);\nconst SectionSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"RevolutionaryFeatures__SectionSubtitle\",\n    componentId: \"sc-146f8b4-3\"\n})([\n    \"font-size:1.3rem;color:rgba(255,255,255,0.7);text-align:center;margin-bottom:4rem;max-width:800px;margin-left:auto;margin-right:auto;\"\n]);\nconst FeaturesGrid = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"RevolutionaryFeatures__FeaturesGrid\",\n    componentId: \"sc-146f8b4-4\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:2rem;margin-bottom:4rem;\"\n]);\nconst FeatureCard = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"RevolutionaryFeatures__FeatureCard\",\n    componentId: \"sc-146f8b4-5\"\n})([\n    \"background:\",\n    \";border:2px solid \",\n    \";border-radius:20px;padding:2rem;cursor:pointer;transition:all 0.3s ease;backdrop-filter:blur(10px);overflow:hidden;position:relative;&::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:\",\n    \";opacity:\",\n    \";transition:opacity 0.3s ease;}\"\n], (props)=>props.active || props.hovered ? `linear-gradient(135deg, ${props.color}20, ${props.color}10)` : \"rgba(255, 255, 255, 0.05)\", (props)=>props.active || props.hovered ? props.color : \"rgba(255, 255, 255, 0.1)\", (props)=>`linear-gradient(135deg, ${props.color}10, transparent)`, (props)=>props.active || props.hovered ? 1 : 0);\nconst FeatureIcon = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"RevolutionaryFeatures__FeatureIcon\",\n    componentId: \"sc-146f8b4-6\"\n})([\n    \"font-size:3rem;margin-bottom:1rem;text-align:center;filter:\",\n    \";transition:all 0.3s ease;\"\n], (props)=>props.active ? \"drop-shadow(0 0 20px currentColor)\" : \"none\");\nconst FeatureTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h3.withConfig({\n    displayName: \"RevolutionaryFeatures__FeatureTitle\",\n    componentId: \"sc-146f8b4-7\"\n})([\n    \"font-size:1.5rem;font-weight:700;color:white;margin-bottom:0.5rem;text-align:center;\"\n]);\nconst FeatureSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"RevolutionaryFeatures__FeatureSubtitle\",\n    componentId: \"sc-146f8b4-8\"\n})([\n    \"font-size:1rem;color:rgba(255,255,255,0.7);text-align:center;margin-bottom:1rem;\"\n]);\nconst FeatureDetails = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"RevolutionaryFeatures__FeatureDetails\",\n    componentId: \"sc-146f8b4-9\"\n})([\n    \"position:relative;z-index:1;\"\n]);\nconst FeatureDescription = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"RevolutionaryFeatures__FeatureDescription\",\n    componentId: \"sc-146f8b4-10\"\n})([\n    \"font-size:0.9rem;color:rgba(255,255,255,0.8);line-height:1.6;margin-bottom:1.5rem;\"\n]);\nconst StatsGrid = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"RevolutionaryFeatures__StatsGrid\",\n    componentId: \"sc-146f8b4-11\"\n})([\n    \"display:grid;grid-template-columns:repeat(3,1fr);gap:1rem;\"\n]);\nconst StatItem = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"RevolutionaryFeatures__StatItem\",\n    componentId: \"sc-146f8b4-12\"\n})([\n    \"text-align:center;\"\n]);\nconst StatValue = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"RevolutionaryFeatures__StatValue\",\n    componentId: \"sc-146f8b4-13\"\n})([\n    \"font-size:1.5rem;font-weight:700;color:white;\"\n]);\nconst StatLabel = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"RevolutionaryFeatures__StatLabel\",\n    componentId: \"sc-146f8b4-14\"\n})([\n    \"font-size:0.8rem;color:rgba(255,255,255,0.6);\"\n]);\nconst DetailedView = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"RevolutionaryFeatures__DetailedView\",\n    componentId: \"sc-146f8b4-15\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:20px;padding:3rem;backdrop-filter:blur(20px);border:1px solid rgba(255,255,255,0.1);margin-bottom:3rem;\"\n]);\nconst DetailedContent = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"RevolutionaryFeatures__DetailedContent\",\n    componentId: \"sc-146f8b4-16\"\n})([\n    \"\"\n]);\nconst DetailedHeader = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"RevolutionaryFeatures__DetailedHeader\",\n    componentId: \"sc-146f8b4-17\"\n})([\n    \"display:flex;align-items:center;gap:1.5rem;margin-bottom:2rem;\"\n]);\nconst DetailedIcon = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"RevolutionaryFeatures__DetailedIcon\",\n    componentId: \"sc-146f8b4-18\"\n})([\n    \"font-size:4rem;color:\",\n    \";filter:drop-shadow(0 0 20px \",\n    \"50);\"\n], (props)=>props.color, (props)=>props.color);\nconst DetailedTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h3.withConfig({\n    displayName: \"RevolutionaryFeatures__DetailedTitle\",\n    componentId: \"sc-146f8b4-19\"\n})([\n    \"font-size:2.5rem;font-weight:700;color:white;margin-bottom:0.5rem;\"\n]);\nconst DetailedSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"RevolutionaryFeatures__DetailedSubtitle\",\n    componentId: \"sc-146f8b4-20\"\n})([\n    \"font-size:1.2rem;color:rgba(255,255,255,0.7);\"\n]);\nconst DetailedDescription = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"RevolutionaryFeatures__DetailedDescription\",\n    componentId: \"sc-146f8b4-21\"\n})([\n    \"font-size:1.1rem;color:rgba(255,255,255,0.8);line-height:1.8;margin-bottom:2rem;\"\n]);\nconst TechnologiesSection = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"RevolutionaryFeatures__TechnologiesSection\",\n    componentId: \"sc-146f8b4-22\"\n})([\n    \"margin-bottom:2rem;\"\n]);\nconst BenefitsSection = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"RevolutionaryFeatures__BenefitsSection\",\n    componentId: \"sc-146f8b4-23\"\n})([\n    \"\"\n]);\nconst SectionLabel = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h4.withConfig({\n    displayName: \"RevolutionaryFeatures__SectionLabel\",\n    componentId: \"sc-146f8b4-24\"\n})([\n    \"font-size:1.2rem;font-weight:600;color:white;margin-bottom:1rem;\"\n]);\nconst TechnologiesList = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"RevolutionaryFeatures__TechnologiesList\",\n    componentId: \"sc-146f8b4-25\"\n})([\n    \"display:flex;flex-wrap:wrap;gap:0.5rem;\"\n]);\nconst TechTag = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span).withConfig({\n    displayName: \"RevolutionaryFeatures__TechTag\",\n    componentId: \"sc-146f8b4-26\"\n})([\n    \"background:rgba(74,144,226,0.2);color:#4a90e2;padding:0.5rem 1rem;border-radius:20px;font-size:0.9rem;font-weight:500;border:1px solid rgba(74,144,226,0.3);\"\n]);\nconst BenefitsList = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"RevolutionaryFeatures__BenefitsList\",\n    componentId: \"sc-146f8b4-27\"\n})([\n    \"display:flex;flex-direction:column;gap:0.5rem;\"\n]);\nconst BenefitItem = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"RevolutionaryFeatures__BenefitItem\",\n    componentId: \"sc-146f8b4-28\"\n})([\n    \"color:rgba(255,255,255,0.8);font-size:1rem;\"\n]);\nconst ProgressIndicators = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"RevolutionaryFeatures__ProgressIndicators\",\n    componentId: \"sc-146f8b4-29\"\n})([\n    \"display:flex;justify-content:center;gap:1rem;\"\n]);\nconst ProgressDot = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"RevolutionaryFeatures__ProgressDot\",\n    componentId: \"sc-146f8b4-30\"\n})([\n    \"width:12px;height:12px;border-radius:50%;background:\",\n    \";cursor:pointer;transition:all 0.3s ease;\"\n], (props)=>props.active ? \"#4a90e2\" : \"rgba(255, 255, 255, 0.3)\");\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RevolutionaryFeatures);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/RevolutionaryFeatures.tsx\n");

/***/ })

};
;