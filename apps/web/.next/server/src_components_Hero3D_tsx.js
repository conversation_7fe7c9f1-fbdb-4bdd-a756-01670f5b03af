"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_Hero3D_tsx";
exports.ids = ["src_components_Hero3D_tsx"];
exports.modules = {

/***/ "./src/components/Hero3D.tsx":
/*!***********************************!*\
  !*** ./src/components/Hero3D.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"@react-three/fiber\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/drei */ \"@react-three/drei\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_4__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n// 3D компонент квантовой сферы\nconst QuantumSphere = ({ quantumStatus })=>{\n    const meshRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [hovered, setHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.useFrame)((state)=>{\n        if (meshRef.current) {\n            meshRef.current.rotation.x = state.clock.elapsedTime * 0.2;\n            meshRef.current.rotation.y = state.clock.elapsedTime * 0.3;\n            // Пульсация в зависимости от квантового статуса\n            const scale = quantumStatus?.isQuantumAvailable ? 1 + Math.sin(state.clock.elapsedTime * 2) * 0.1 : 1 + Math.sin(state.clock.elapsedTime) * 0.05;\n            meshRef.current.scale.setScalar(scale);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Float, {\n        speed: 2,\n        rotationIntensity: 1,\n        floatIntensity: 2,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Sphere, {\n            ref: meshRef,\n            args: [\n                1,\n                64,\n                64\n            ],\n            onPointerOver: ()=>setHovered(true),\n            onPointerOut: ()=>setHovered(false),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.MeshDistortMaterial, {\n                color: quantumStatus?.isQuantumAvailable ? \"#4a90e2\" : \"#7b68ee\",\n                attach: \"material\",\n                distort: 0.4,\n                speed: 2,\n                roughness: 0.1,\n                metalness: 0.8,\n                emissive: quantumStatus?.isQuantumAvailable ? \"#1a4480\" : \"#3d3470\",\n                emissiveIntensity: hovered ? 0.5 : 0.2\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n// 3D текст с анимацией\nconst AnimatedText = ({ text, position })=>{\n    const textRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.useFrame)((state)=>{\n        if (textRef.current) {\n            textRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime) * 0.1;\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Text, {\n        ref: textRef,\n        position: position,\n        fontSize: 0.5,\n        color: \"#ffffff\",\n        anchorX: \"center\",\n        anchorY: \"middle\",\n        font: \"/fonts/Inter-Bold.woff\",\n        children: text\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n// Частицы для фона\nconst Particles = ()=>{\n    const particlesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const particleCount = 1000;\n    const positions = new Float32Array(particleCount * 3);\n    const colors = new Float32Array(particleCount * 3);\n    for(let i = 0; i < particleCount; i++){\n        positions[i * 3] = (Math.random() - 0.5) * 20;\n        positions[i * 3 + 1] = (Math.random() - 0.5) * 20;\n        positions[i * 3 + 2] = (Math.random() - 0.5) * 20;\n        colors[i * 3] = Math.random();\n        colors[i * 3 + 1] = Math.random() * 0.5 + 0.5;\n        colors[i * 3 + 2] = 1;\n    }\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.useFrame)((state)=>{\n        if (particlesRef.current) {\n            particlesRef.current.rotation.y = state.clock.elapsedTime * 0.05;\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"points\", {\n        ref: particlesRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"bufferGeometry\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"bufferAttribute\", {\n                        attach: \"attributes-position\",\n                        count: particleCount,\n                        array: positions,\n                        itemSize: 3\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"bufferAttribute\", {\n                        attach: \"attributes-color\",\n                        count: particleCount,\n                        array: colors,\n                        itemSize: 3\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pointsMaterial\", {\n                size: 0.02,\n                vertexColors: true,\n                transparent: true,\n                opacity: 0.6\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, undefined);\n};\nconst Hero3D = ({ quantumStatus, emotionalState, onStartJourney })=>{\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleMouseMove = (event)=>{\n            setMousePosition({\n                x: event.clientX / window.innerWidth * 2 - 1,\n                y: -(event.clientY / window.innerHeight) * 2 + 1\n            });\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>window.removeEventListener(\"mousemove\", handleMouseMove);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroContainer, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CanvasContainer, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.Canvas, {\n                    camera: {\n                        position: [\n                            0,\n                            0,\n                            5\n                        ],\n                        fov: 75\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ambientLight\", {\n                            intensity: 0.3\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pointLight\", {\n                            position: [\n                                10,\n                                10,\n                                10\n                            ],\n                            intensity: 1\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pointLight\", {\n                            position: [\n                                -10,\n                                -10,\n                                -10\n                            ],\n                            intensity: 0.5,\n                            color: \"#7b68ee\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Stars, {\n                            radius: 100,\n                            depth: 50,\n                            count: 5000,\n                            factor: 4,\n                            saturation: 0,\n                            fade: true\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Particles, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuantumSphere, {\n                            quantumStatus: quantumStatus\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedText, {\n                            text: \"КОЗЫРЬ МАСТЕР 4.0\",\n                            position: [\n                                0,\n                                2,\n                                0\n                            ]\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedText, {\n                            text: \"Революция карточных игр\",\n                            position: [\n                                0,\n                                -2,\n                                0\n                            ]\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.OrbitControls, {\n                            enableZoom: false,\n                            enablePan: false\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentOverlay, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 1,\n                            delay: 0.5\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MainTitle, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                    animate: {\n                                        backgroundPosition: [\n                                            \"0% 50%\",\n                                            \"100% 50%\",\n                                            \"0% 50%\"\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 3,\n                                        repeat: Infinity\n                                    },\n                                    style: {\n                                        background: \"linear-gradient(90deg, #4a90e2, #7b68ee, #9370db, #4a90e2)\",\n                                        backgroundSize: \"200% 100%\",\n                                        WebkitBackgroundClip: \"text\",\n                                        WebkitTextFillColor: \"transparent\"\n                                    },\n                                    children: \"КОЗЫРЬ МАСТЕР 4.0\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Subtitle, {\n                                children: \"Где квантовая случайность встречается с эмоциональным интеллектом\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesList, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureItem, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: 1\n                                        },\n                                        children: \"\\uD83D\\uDD2C Квантовая честность\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureItem, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: 1.2\n                                        },\n                                        children: \"\\uD83E\\uDDE0 Эмоциональный ИИ\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureItem, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: 1.4\n                                        },\n                                        children: \"\\uD83C\\uDF0D 3D Метавселенная\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureItem, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: 1.6\n                                        },\n                                        children: \"⛓️ Web3 экосистема\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ButtonContainer, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StartButton, {\n                                            onClick: onStartJourney,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ButtonGlow, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Начать путешествие\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SecondaryButton, {\n                                            children: \"Смотреть демо\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusPanels, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusPanel, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                        children: \"⚛️\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusText, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusLabel, {\n                                                children: \"Квантовый статус\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusValue, {\n                                                active: quantumStatus?.isQuantumAvailable,\n                                                children: quantumStatus?.isQuantumAvailable ? \"Активен\" : \"Инициализация\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusPanel, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                        children: \"\\uD83E\\uDDE0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusText, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusLabel, {\n                                                children: \"ИИ анализ\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusValue, {\n                                                active: true,\n                                                children: [\n                                                    \"Настроение: \",\n                                                    emotionalState?.happiness > 0.7 ? \"Отличное\" : emotionalState?.happiness > 0.4 ? \"Хорошее\" : \"Нормальное\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusPanel, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                        children: \"\\uD83C\\uDF10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusText, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusLabel, {\n                                                children: \"Игроков онлайн\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusValue, {\n                                                active: true,\n                                                children: (Math.random() * 50000 + 10000).toFixed(0)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InteractiveCursor, {\n                style: {\n                    left: `${(mousePosition.x + 1) * 50}%`,\n                    top: `${(-mousePosition.y + 1) * 50}%`\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Hero3D.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, undefined);\n};\n// Стилизованные компоненты\nconst HeroContainer = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"Hero3D__HeroContainer\",\n    componentId: \"sc-5267bdc5-0\"\n})([\n    \"position:relative;width:100vw;height:100vh;overflow:hidden;background:radial-gradient(ellipse at center,#1a1a2e 0%,#16213e 50%,#0f0f23 100%);\"\n]);\nconst CanvasContainer = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"Hero3D__CanvasContainer\",\n    componentId: \"sc-5267bdc5-1\"\n})([\n    \"position:absolute;top:0;left:0;width:100%;height:100%;\"\n]);\nconst ContentOverlay = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"Hero3D__ContentOverlay\",\n    componentId: \"sc-5267bdc5-2\"\n})([\n    \"position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:column;align-items:center;justify-content:center;z-index:10;pointer-events:none;> *{pointer-events:auto;}\"\n]);\nconst MainTitle = styled_components__WEBPACK_IMPORTED_MODULE_5___default().h1.withConfig({\n    displayName: \"Hero3D__MainTitle\",\n    componentId: \"sc-5267bdc5-3\"\n})([\n    \"font-size:4rem;font-weight:900;text-align:center;margin-bottom:1rem;text-shadow:0 0 30px rgba(74,144,226,0.5);@media (max-width:768px){font-size:2.5rem;}\"\n]);\nconst Subtitle = styled_components__WEBPACK_IMPORTED_MODULE_5___default().p.withConfig({\n    displayName: \"Hero3D__Subtitle\",\n    componentId: \"sc-5267bdc5-4\"\n})([\n    \"font-size:1.5rem;color:rgba(255,255,255,0.8);text-align:center;margin-bottom:3rem;max-width:600px;@media (max-width:768px){font-size:1.2rem;}\"\n]);\nconst FeaturesList = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"Hero3D__FeaturesList\",\n    componentId: \"sc-5267bdc5-5\"\n})([\n    \"display:grid;grid-template-columns:repeat(2,1fr);gap:1rem;margin-bottom:3rem;@media (max-width:768px){grid-template-columns:1fr;}\"\n]);\nconst FeatureItem = styled_components__WEBPACK_IMPORTED_MODULE_5___default()(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div).withConfig({\n    displayName: \"Hero3D__FeatureItem\",\n    componentId: \"sc-5267bdc5-6\"\n})([\n    \"color:rgba(255,255,255,0.9);font-size:1.1rem;padding:0.5rem;text-align:center;\"\n]);\nconst ButtonContainer = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"Hero3D__ButtonContainer\",\n    componentId: \"sc-5267bdc5-7\"\n})([\n    \"display:flex;gap:1.5rem;margin-bottom:4rem;@media (max-width:768px){flex-direction:column;align-items:center;}\"\n]);\nconst StartButton = styled_components__WEBPACK_IMPORTED_MODULE_5___default().button.withConfig({\n    displayName: \"Hero3D__StartButton\",\n    componentId: \"sc-5267bdc5-8\"\n})([\n    \"position:relative;background:linear-gradient(135deg,#4a90e2,#7b68ee);color:white;border:none;border-radius:50px;padding:1rem 2.5rem;font-size:1.2rem;font-weight:600;cursor:pointer;overflow:hidden;transition:all 0.3s ease;box-shadow:0 10px 30px rgba(74,144,226,0.3);&:hover{box-shadow:0 15px 40px rgba(74,144,226,0.5);transform:translateY(-2px);}\"\n]);\nconst ButtonGlow = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"Hero3D__ButtonGlow\",\n    componentId: \"sc-5267bdc5-9\"\n})([\n    \"position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.3),transparent);animation:shimmer 3s infinite;@keyframes shimmer{0%{left:-100%;}100%{left:100%;}}\"\n]);\nconst SecondaryButton = styled_components__WEBPACK_IMPORTED_MODULE_5___default().button.withConfig({\n    displayName: \"Hero3D__SecondaryButton\",\n    componentId: \"sc-5267bdc5-10\"\n})([\n    \"background:transparent;color:white;border:2px solid rgba(255,255,255,0.3);border-radius:50px;padding:1rem 2.5rem;font-size:1.2rem;font-weight:600;cursor:pointer;transition:all 0.3s ease;backdrop-filter:blur(10px);&:hover{border-color:#4a90e2;background:rgba(74,144,226,0.1);transform:translateY(-2px);}\"\n]);\nconst StatusPanels = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"Hero3D__StatusPanels\",\n    componentId: \"sc-5267bdc5-11\"\n})([\n    \"position:absolute;bottom:2rem;left:50%;transform:translateX(-50%);display:flex;gap:1rem;@media (max-width:768px){flex-direction:column;align-items:center;}\"\n]);\nconst StatusPanel = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"Hero3D__StatusPanel\",\n    componentId: \"sc-5267bdc5-12\"\n})([\n    \"display:flex;align-items:center;gap:0.5rem;background:rgba(0,0,0,0.3);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,0.1);border-radius:10px;padding:0.75rem 1rem;\"\n]);\nconst StatusIcon = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"Hero3D__StatusIcon\",\n    componentId: \"sc-5267bdc5-13\"\n})([\n    \"font-size:1.2rem;\"\n]);\nconst StatusText = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"Hero3D__StatusText\",\n    componentId: \"sc-5267bdc5-14\"\n})([\n    \"display:flex;flex-direction:column;\"\n]);\nconst StatusLabel = styled_components__WEBPACK_IMPORTED_MODULE_5___default().span.withConfig({\n    displayName: \"Hero3D__StatusLabel\",\n    componentId: \"sc-5267bdc5-15\"\n})([\n    \"font-size:0.8rem;color:rgba(255,255,255,0.6);\"\n]);\nconst StatusValue = styled_components__WEBPACK_IMPORTED_MODULE_5___default().span.withConfig({\n    displayName: \"Hero3D__StatusValue\",\n    componentId: \"sc-5267bdc5-16\"\n})([\n    \"font-size:0.9rem;font-weight:600;color:\",\n    \";\"\n], (props)=>props.active ? \"#4a90e2\" : \"rgba(255, 255, 255, 0.8)\");\nconst InteractiveCursor = styled_components__WEBPACK_IMPORTED_MODULE_5___default().div.withConfig({\n    displayName: \"Hero3D__InteractiveCursor\",\n    componentId: \"sc-5267bdc5-17\"\n})([\n    \"position:absolute;width:20px;height:20px;border-radius:50%;background:radial-gradient(circle,rgba(74,144,226,0.8),transparent);pointer-events:none;transition:all 0.1s ease;z-index:5;\"\n]);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Hero3D);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Hero3D.tsx\n");

/***/ })

};
;