"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_Web3Dashboard_tsx";
exports.ids = ["src_components_Web3Dashboard_tsx"];
exports.modules = {

/***/ "./src/components/Web3Dashboard.tsx":
/*!******************************************!*\
  !*** ./src/components/Web3Dashboard.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst Web3Dashboard = ({ web3Status, onConnectWallet })=>{\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"wallet\");\n    const [walletConnected, setWalletConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userAddress, setUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tokenBalances, setTokenBalances] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [nftCollection, setNftCollection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [defiPools, setDefiPools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [totalPortfolioValue, setTotalPortfolioValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Симуляция данных Web3\n        if (walletConnected) {\n            setUserAddress(\"******************************************\");\n            setTokenBalances([\n                {\n                    symbol: \"KOZYR\",\n                    balance: 15420.5,\n                    value: 7710.25,\n                    change24h: 12.5\n                },\n                {\n                    symbol: \"ETH\",\n                    balance: 2.45,\n                    value: 4900,\n                    change24h: -3.2\n                },\n                {\n                    symbol: \"USDC\",\n                    balance: 1250,\n                    value: 1250,\n                    change24h: 0.1\n                }\n            ]);\n            setNftCollection([\n                {\n                    id: \"1\",\n                    name: \"Legendary King\",\n                    image: \"\\uD83D\\uDC51\",\n                    rarity: \"legendary\",\n                    power: 95,\n                    price: 2.5,\n                    isStaked: true\n                },\n                {\n                    id: \"2\",\n                    name: \"Epic Queen\",\n                    image: \"\\uD83D\\uDC78\",\n                    rarity: \"epic\",\n                    power: 85,\n                    price: 1.2,\n                    isStaked: false\n                },\n                {\n                    id: \"3\",\n                    name: \"Rare Ace\",\n                    image: \"\\uD83C\\uDCCF\",\n                    rarity: \"rare\",\n                    power: 75,\n                    price: 0.8,\n                    isStaked: true\n                }\n            ]);\n            setDefiPools([\n                {\n                    id: \"1\",\n                    name: \"KOZYR/ETH\",\n                    apr: 145.2,\n                    tvl: 2500000,\n                    userStaked: 1500,\n                    rewards: 25.4\n                },\n                {\n                    id: \"2\",\n                    name: \"KOZYR/USDC\",\n                    apr: 89.7,\n                    tvl: 1800000,\n                    userStaked: 800,\n                    rewards: 12.1\n                }\n            ]);\n            setTotalPortfolioValue(13860.25);\n        }\n    }, [\n        walletConnected\n    ]);\n    const connectWallet = async ()=>{\n        // Симуляция подключения кошелька\n        await new Promise((resolve)=>setTimeout(resolve, 1500));\n        setWalletConnected(true);\n        onConnectWallet();\n    };\n    const getRarityColor = (rarity)=>{\n        switch(rarity){\n            case \"legendary\":\n                return \"#ffd700\";\n            case \"epic\":\n                return \"#9370db\";\n            case \"rare\":\n                return \"#4a90e2\";\n            default:\n                return \"#6b7280\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Web3Container, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentWrapper, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionTitle, {\n                            children: \"Web3 Экосистема\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSubtitle, {\n                            children: \"Децентрализованное будущее карточных игр\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, undefined),\n                !walletConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletConnection, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConnectionCard, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.8\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConnectionIcon, {\n                                children: \"\\uD83D\\uDD17\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConnectionTitle, {\n                                children: \"Подключите кошелёк\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConnectionDescription, {\n                                children: \"Подключите Web3 кошелёк для доступа к NFT, DeFi и DAO функциям\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletOptions, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletOption, {\n                                        onClick: connectWallet,\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletIcon, {\n                                                children: \"\\uD83E\\uDD8A\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletName, {\n                                                children: \"MetaMask\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletOption, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletIcon, {\n                                                children: \"\\uD83C\\uDF08\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletName, {\n                                                children: \"Rainbow\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletOption, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletIcon, {\n                                                children: \"\\uD83D\\uDC99\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletName, {\n                                                children: \"Coinbase\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserInfo, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserAvatar, {\n                                            children: \"\\uD83D\\uDC64\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserDetails, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserAddress, {\n                                                    children: [\n                                                        userAddress.slice(0, 6),\n                                                        \"...\",\n                                                        userAddress.slice(-4)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PortfolioValue, {\n                                                    children: [\n                                                        \"$\",\n                                                        totalPortfolioValue.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NetworkInfo, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NetworkIndicator, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NetworkName, {\n                                            children: \"Ethereum Mainnet\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabNavigation, {\n                            children: [\n                                {\n                                    id: \"wallet\",\n                                    label: \"Кошелёк\",\n                                    icon: \"\\uD83D\\uDCB0\"\n                                },\n                                {\n                                    id: \"nft\",\n                                    label: \"NFT\",\n                                    icon: \"\\uD83C\\uDFB4\"\n                                },\n                                {\n                                    id: \"defi\",\n                                    label: \"DeFi\",\n                                    icon: \"\\uD83C\\uDFE6\"\n                                },\n                                {\n                                    id: \"dao\",\n                                    label: \"DAO\",\n                                    icon: \"\\uD83D\\uDDF3️\"\n                                }\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabButton, {\n                                    active: activeTab === tab.id,\n                                    onClick: ()=>setActiveTab(tab.id),\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabIcon, {\n                                            children: tab.icon\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabLabel, {\n                                            children: tab.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, tab.id, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                                mode: \"wait\",\n                                children: [\n                                    activeTab === \"wallet\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TokenGrid, {\n                                            children: tokenBalances.map((token)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TokenCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TokenHeader, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TokenSymbol, {\n                                                                    children: token.symbol\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TokenChange, {\n                                                                    positive: token.change24h > 0,\n                                                                    children: [\n                                                                        token.change24h > 0 ? \"↗\" : \"↘\",\n                                                                        \" \",\n                                                                        Math.abs(token.change24h),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TokenBalance, {\n                                                            children: token.balance.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TokenValue, {\n                                                            children: [\n                                                                \"$\",\n                                                                token.value.toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, token.symbol, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, \"wallet\", false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    activeTab === \"nft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTGrid, {\n                                            children: nftCollection.map((nft)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTCard, {\n                                                    rarity: nft.rarity,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTImage, {\n                                                            children: nft.image\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTInfo, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTName, {\n                                                                    children: nft.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTRarity, {\n                                                                    rarity: nft.rarity,\n                                                                    children: nft.rarity.toUpperCase()\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTPower, {\n                                                                    children: [\n                                                                        \"⚡ \",\n                                                                        nft.power\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTPrice, {\n                                                                    children: [\n                                                                        nft.price,\n                                                                        \" ETH\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        nft.isStaked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StakedBadge, {\n                                                            children: \"STAKED\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 44\n                                                        }, undefined)\n                                                    ]\n                                                }, nft.id, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, \"nft\", false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    activeTab === \"defi\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeFiGrid, {\n                                            children: defiPools.map((pool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolCard, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolHeader, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolName, {\n                                                                    children: pool.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolAPR, {\n                                                                    children: [\n                                                                        pool.apr,\n                                                                        \"% APR\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStats, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStat, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStatLabel, {\n                                                                            children: \"TVL\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                            lineNumber: 290,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStatValue, {\n                                                                            children: [\n                                                                                \"$\",\n                                                                                (pool.tvl / 1000000).toFixed(1),\n                                                                                \"M\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                            lineNumber: 291,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStat, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStatLabel, {\n                                                                            children: \"Ваш стейк\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                            lineNumber: 294,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStatValue, {\n                                                                            children: [\n                                                                                \"$\",\n                                                                                pool.userStaked\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                            lineNumber: 295,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 293,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStat, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStatLabel, {\n                                                                            children: \"Награды\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                            lineNumber: 298,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolStatValue, {\n                                                                            children: [\n                                                                                pool.rewards,\n                                                                                \" KOZYR\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolActions, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolButton, {\n                                                                    whileHover: {\n                                                                        scale: 1.02\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.98\n                                                                    },\n                                                                    children: \"Добавить\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PoolButton, {\n                                                                    secondary: true,\n                                                                    whileHover: {\n                                                                        scale: 1.02\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.98\n                                                                    },\n                                                                    children: \"Забрать\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, pool.id, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, \"defi\", false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    activeTab === \"dao\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOSection, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStats, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStat, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStatValue, {\n                                                                    children: \"15,420\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStatLabel, {\n                                                                    children: \"Ваша voting power\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStat, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStatValue, {\n                                                                    children: \"7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStatLabel, {\n                                                                    children: \"Активных предложений\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStat, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStatValue, {\n                                                                    children: \"89%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DAOStatLabel, {\n                                                                    children: \"Участие в голосовании\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProposalsList, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProposalCard, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProposalTitle, {\n                                                                children: \"Добавить новую игру: Блэкджек\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProposalDescription, {\n                                                                children: \"Предложение о добавлении блэкджека в платформу\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProposalVotes, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoteOption, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoteLabel, {\n                                                                                children: \"За: 85%\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                                lineNumber: 355,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoteBar, {\n                                                                                width: 85\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                                lineNumber: 356,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoteOption, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoteLabel, {\n                                                                                children: \"Против: 15%\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                                lineNumber: 359,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoteBar, {\n                                                                                width: 15\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                                lineNumber: 360,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProposalActions, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoteButton, {\n                                                                        whileHover: {\n                                                                            scale: 1.02\n                                                                        },\n                                                                        whileTap: {\n                                                                            scale: 0.98\n                                                                        },\n                                                                        children: \"Голосовать ЗА\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoteButton, {\n                                                                        secondary: true,\n                                                                        whileHover: {\n                                                                            scale: 1.02\n                                                                        },\n                                                                        whileTap: {\n                                                                            scale: 0.98\n                                                                        },\n                                                                        children: \"Голосовать ПРОТИВ\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, \"dao\", false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/Web3Dashboard.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, undefined);\n};\n// Стилизованные компоненты (первая часть)\nconst Web3Container = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__Web3Container\",\n    componentId: \"sc-d33395ac-0\"\n})([\n    \"min-height:100vh;background:linear-gradient(135deg,#16213e 0%,#0f0f23 50%,#1a1a2e 100%);padding:4rem 2rem;display:flex;align-items:center;justify-content:center;\"\n]);\nconst ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__ContentWrapper\",\n    componentId: \"sc-d33395ac-1\"\n})([\n    \"max-width:1400px;width:100%;\"\n]);\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h2.withConfig({\n    displayName: \"Web3Dashboard__SectionTitle\",\n    componentId: \"sc-d33395ac-2\"\n})([\n    \"font-size:3.5rem;font-weight:900;text-align:center;margin-bottom:1rem;background:linear-gradient(45deg,#4a90e2,#7b68ee,#9370db);-webkit-background-clip:text;-webkit-text-fill-color:transparent;@media (max-width:768px){font-size:2.5rem;}\"\n]);\nconst SectionSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"Web3Dashboard__SectionSubtitle\",\n    componentId: \"sc-d33395ac-3\"\n})([\n    \"font-size:1.3rem;color:rgba(255,255,255,0.7);text-align:center;margin-bottom:4rem;max-width:800px;margin-left:auto;margin-right:auto;\"\n]);\nconst WalletConnection = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__WalletConnection\",\n    componentId: \"sc-d33395ac-4\"\n})([\n    \"display:flex;justify-content:center;align-items:center;min-height:400px;\"\n]);\nconst ConnectionCard = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"Web3Dashboard__ConnectionCard\",\n    componentId: \"sc-d33395ac-5\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:20px;padding:3rem;text-align:center;backdrop-filter:blur(20px);border:1px solid rgba(255,255,255,0.1);max-width:500px;width:100%;\"\n]);\nconst ConnectionIcon = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__ConnectionIcon\",\n    componentId: \"sc-d33395ac-6\"\n})([\n    \"font-size:4rem;margin-bottom:1.5rem;\"\n]);\nconst ConnectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h3.withConfig({\n    displayName: \"Web3Dashboard__ConnectionTitle\",\n    componentId: \"sc-d33395ac-7\"\n})([\n    \"font-size:2rem;font-weight:700;color:white;margin-bottom:1rem;\"\n]);\nconst ConnectionDescription = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"Web3Dashboard__ConnectionDescription\",\n    componentId: \"sc-d33395ac-8\"\n})([\n    \"color:rgba(255,255,255,0.7);margin-bottom:2rem;line-height:1.6;\"\n]);\nconst WalletOptions = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__WalletOptions\",\n    componentId: \"sc-d33395ac-9\"\n})([\n    \"display:grid;grid-template-columns:repeat(3,1fr);gap:1rem;@media (max-width:640px){grid-template-columns:1fr;}\"\n]);\nconst WalletOption = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"Web3Dashboard__WalletOption\",\n    componentId: \"sc-d33395ac-10\"\n})([\n    \"background:rgba(255,255,255,0.05);border:1px solid rgba(255,255,255,0.1);border-radius:15px;padding:1.5rem 1rem;cursor:pointer;transition:all 0.3s ease;&:hover{background:rgba(255,255,255,0.1);border-color:#4a90e2;}\"\n]);\nconst WalletIcon = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__WalletIcon\",\n    componentId: \"sc-d33395ac-11\"\n})([\n    \"font-size:2rem;margin-bottom:0.5rem;\"\n]);\nconst WalletName = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__WalletName\",\n    componentId: \"sc-d33395ac-12\"\n})([\n    \"color:white;font-weight:600;font-size:0.9rem;\"\n]);\n// Стилизованные компоненты (продолжение)\nconst DashboardContent = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__DashboardContent\",\n    componentId: \"sc-d33395ac-13\"\n})([\n    \"\"\n]);\nconst DashboardHeader = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__DashboardHeader\",\n    componentId: \"sc-d33395ac-14\"\n})([\n    \"display:flex;justify-content:space-between;align-items:center;margin-bottom:2rem;padding:1.5rem;background:rgba(255,255,255,0.05);border-radius:15px;backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,0.1);\"\n]);\nconst UserInfo = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__UserInfo\",\n    componentId: \"sc-d33395ac-15\"\n})([\n    \"display:flex;align-items:center;gap:1rem;\"\n]);\nconst UserAvatar = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__UserAvatar\",\n    componentId: \"sc-d33395ac-16\"\n})([\n    \"width:50px;height:50px;background:linear-gradient(135deg,#4a90e2,#7b68ee);border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:1.5rem;\"\n]);\nconst UserDetails = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__UserDetails\",\n    componentId: \"sc-d33395ac-17\"\n})([\n    \"\"\n]);\nconst UserAddress = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__UserAddress\",\n    componentId: \"sc-d33395ac-18\"\n})([\n    \"color:white;font-weight:600;font-size:1.1rem;\"\n]);\nconst PortfolioValue = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__PortfolioValue\",\n    componentId: \"sc-d33395ac-19\"\n})([\n    \"color:#4ade80;font-weight:700;font-size:1.3rem;\"\n]);\nconst NetworkInfo = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__NetworkInfo\",\n    componentId: \"sc-d33395ac-20\"\n})([\n    \"display:flex;align-items:center;gap:0.5rem;\"\n]);\nconst NetworkIndicator = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__NetworkIndicator\",\n    componentId: \"sc-d33395ac-21\"\n})([\n    \"width:12px;height:12px;background:#4ade80;border-radius:50%;animation:pulse 2s infinite;\"\n]);\nconst NetworkName = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__NetworkName\",\n    componentId: \"sc-d33395ac-22\"\n})([\n    \"color:rgba(255,255,255,0.8);font-size:0.9rem;\"\n]);\nconst TabNavigation = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__TabNavigation\",\n    componentId: \"sc-d33395ac-23\"\n})([\n    \"display:flex;gap:0.5rem;margin-bottom:2rem;background:rgba(255,255,255,0.05);border-radius:15px;padding:0.5rem;\"\n]);\nconst TabButton = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"Web3Dashboard__TabButton\",\n    componentId: \"sc-d33395ac-24\"\n})([\n    \"flex:1;display:flex;align-items:center;justify-content:center;gap:0.5rem;padding:1rem;border:none;background:\",\n    \";color:\",\n    \";border-radius:10px;cursor:pointer;transition:all 0.3s ease;font-weight:600;&:hover{background:rgba(74,144,226,0.1);color:#4a90e2;}\"\n], (props)=>props.active ? \"rgba(74, 144, 226, 0.3)\" : \"transparent\", (props)=>props.active ? \"#4a90e2\" : \"rgba(255, 255, 255, 0.7)\");\nconst TabIcon = styled_components__WEBPACK_IMPORTED_MODULE_3___default().span.withConfig({\n    displayName: \"Web3Dashboard__TabIcon\",\n    componentId: \"sc-d33395ac-25\"\n})([\n    \"font-size:1.2rem;\"\n]);\nconst TabLabel = styled_components__WEBPACK_IMPORTED_MODULE_3___default().span.withConfig({\n    displayName: \"Web3Dashboard__TabLabel\",\n    componentId: \"sc-d33395ac-26\"\n})([\n    \"font-size:0.9rem;\"\n]);\nconst TabContent = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__TabContent\",\n    componentId: \"sc-d33395ac-27\"\n})([\n    \"min-height:400px;\"\n]);\nconst TokenGrid = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__TokenGrid\",\n    componentId: \"sc-d33395ac-28\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:1.5rem;\"\n]);\nconst TokenCard = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__TokenCard\",\n    componentId: \"sc-d33395ac-29\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;border:1px solid rgba(255,255,255,0.1);\"\n]);\nconst TokenHeader = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__TokenHeader\",\n    componentId: \"sc-d33395ac-30\"\n})([\n    \"display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem;\"\n]);\nconst TokenSymbol = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__TokenSymbol\",\n    componentId: \"sc-d33395ac-31\"\n})([\n    \"color:white;font-weight:700;font-size:1.2rem;\"\n]);\nconst TokenChange = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__TokenChange\",\n    componentId: \"sc-d33395ac-32\"\n})([\n    \"color:\",\n    \";font-weight:600;font-size:0.9rem;\"\n], (props)=>props.positive ? \"#4ade80\" : \"#ef4444\");\nconst TokenBalance = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__TokenBalance\",\n    componentId: \"sc-d33395ac-33\"\n})([\n    \"color:white;font-size:1.5rem;font-weight:700;margin-bottom:0.5rem;\"\n]);\nconst TokenValue = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__TokenValue\",\n    componentId: \"sc-d33395ac-34\"\n})([\n    \"color:rgba(255,255,255,0.7);font-size:1rem;\"\n]);\nconst NFTGrid = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__NFTGrid\",\n    componentId: \"sc-d33395ac-35\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1.5rem;\"\n]);\nconst NFTCard = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__NFTCard\",\n    componentId: \"sc-d33395ac-36\"\n})([\n    \"position:relative;background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;border:2px solid \",\n    \";overflow:hidden;&::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,\",\n    \"20,transparent);pointer-events:none;}\"\n], (props)=>getRarityColor(props.rarity), (props)=>getRarityColor(props.rarity));\nconst NFTImage = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__NFTImage\",\n    componentId: \"sc-d33395ac-37\"\n})([\n    \"font-size:4rem;text-align:center;margin-bottom:1rem;filter:drop-shadow(0 0 20px currentColor);\"\n]);\nconst NFTInfo = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__NFTInfo\",\n    componentId: \"sc-d33395ac-38\"\n})([\n    \"position:relative;z-index:1;\"\n]);\nconst NFTName = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__NFTName\",\n    componentId: \"sc-d33395ac-39\"\n})([\n    \"color:white;font-weight:700;font-size:1.1rem;margin-bottom:0.5rem;\"\n]);\nconst NFTRarity = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__NFTRarity\",\n    componentId: \"sc-d33395ac-40\"\n})([\n    \"color:\",\n    \";font-weight:600;font-size:0.8rem;text-transform:uppercase;margin-bottom:0.5rem;\"\n], (props)=>getRarityColor(props.rarity));\nconst NFTPower = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__NFTPower\",\n    componentId: \"sc-d33395ac-41\"\n})([\n    \"color:#fbbf24;font-weight:600;margin-bottom:0.5rem;\"\n]);\nconst NFTPrice = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__NFTPrice\",\n    componentId: \"sc-d33395ac-42\"\n})([\n    \"color:rgba(255,255,255,0.8);font-size:0.9rem;\"\n]);\nconst StakedBadge = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__StakedBadge\",\n    componentId: \"sc-d33395ac-43\"\n})([\n    \"position:absolute;top:0.5rem;right:0.5rem;background:#4ade80;color:black;padding:0.25rem 0.5rem;border-radius:5px;font-size:0.7rem;font-weight:700;\"\n]);\nconst DeFiGrid = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__DeFiGrid\",\n    componentId: \"sc-d33395ac-44\"\n})([\n    \"display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:1.5rem;\"\n]);\nconst PoolCard = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__PoolCard\",\n    componentId: \"sc-d33395ac-45\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;border:1px solid rgba(255,255,255,0.1);\"\n]);\nconst PoolHeader = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__PoolHeader\",\n    componentId: \"sc-d33395ac-46\"\n})([\n    \"display:flex;justify-content:space-between;align-items:center;margin-bottom:1.5rem;\"\n]);\nconst PoolName = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__PoolName\",\n    componentId: \"sc-d33395ac-47\"\n})([\n    \"color:white;font-weight:700;font-size:1.2rem;\"\n]);\nconst PoolAPR = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__PoolAPR\",\n    componentId: \"sc-d33395ac-48\"\n})([\n    \"color:#4ade80;font-weight:700;font-size:1.1rem;\"\n]);\nconst PoolStats = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__PoolStats\",\n    componentId: \"sc-d33395ac-49\"\n})([\n    \"display:grid;grid-template-columns:repeat(3,1fr);gap:1rem;margin-bottom:1.5rem;\"\n]);\nconst PoolStat = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__PoolStat\",\n    componentId: \"sc-d33395ac-50\"\n})([\n    \"text-align:center;\"\n]);\nconst PoolStatLabel = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__PoolStatLabel\",\n    componentId: \"sc-d33395ac-51\"\n})([\n    \"color:rgba(255,255,255,0.6);font-size:0.8rem;margin-bottom:0.25rem;\"\n]);\nconst PoolStatValue = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__PoolStatValue\",\n    componentId: \"sc-d33395ac-52\"\n})([\n    \"color:white;font-weight:600;\"\n]);\nconst PoolActions = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__PoolActions\",\n    componentId: \"sc-d33395ac-53\"\n})([\n    \"display:flex;gap:1rem;\"\n]);\nconst PoolButton = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"Web3Dashboard__PoolButton\",\n    componentId: \"sc-d33395ac-54\"\n})([\n    \"flex:1;background:\",\n    \";color:white;border:\",\n    \";border-radius:8px;padding:0.75rem;font-weight:600;cursor:pointer;\"\n], (props)=>props.secondary ? \"transparent\" : \"linear-gradient(135deg, #4a90e2, #7b68ee)\", (props)=>props.secondary ? \"1px solid rgba(255, 255, 255, 0.3)\" : \"none\");\nconst DAOSection = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__DAOSection\",\n    componentId: \"sc-d33395ac-55\"\n})([\n    \"\"\n]);\nconst DAOStats = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__DAOStats\",\n    componentId: \"sc-d33395ac-56\"\n})([\n    \"display:grid;grid-template-columns:repeat(3,1fr);gap:1.5rem;margin-bottom:2rem;\"\n]);\nconst DAOStat = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__DAOStat\",\n    componentId: \"sc-d33395ac-57\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;text-align:center;border:1px solid rgba(255,255,255,0.1);\"\n]);\nconst DAOStatValue = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__DAOStatValue\",\n    componentId: \"sc-d33395ac-58\"\n})([\n    \"color:#4a90e2;font-size:2rem;font-weight:700;margin-bottom:0.5rem;\"\n]);\nconst DAOStatLabel = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__DAOStatLabel\",\n    componentId: \"sc-d33395ac-59\"\n})([\n    \"color:rgba(255,255,255,0.7);font-size:0.9rem;\"\n]);\nconst ProposalsList = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__ProposalsList\",\n    componentId: \"sc-d33395ac-60\"\n})([\n    \"\"\n]);\nconst ProposalCard = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__ProposalCard\",\n    componentId: \"sc-d33395ac-61\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;border:1px solid rgba(255,255,255,0.1);\"\n]);\nconst ProposalTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h4.withConfig({\n    displayName: \"Web3Dashboard__ProposalTitle\",\n    componentId: \"sc-d33395ac-62\"\n})([\n    \"color:white;font-size:1.2rem;font-weight:700;margin-bottom:0.5rem;\"\n]);\nconst ProposalDescription = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"Web3Dashboard__ProposalDescription\",\n    componentId: \"sc-d33395ac-63\"\n})([\n    \"color:rgba(255,255,255,0.7);margin-bottom:1.5rem;line-height:1.5;\"\n]);\nconst ProposalVotes = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__ProposalVotes\",\n    componentId: \"sc-d33395ac-64\"\n})([\n    \"margin-bottom:1.5rem;\"\n]);\nconst VoteOption = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__VoteOption\",\n    componentId: \"sc-d33395ac-65\"\n})([\n    \"margin-bottom:1rem;\"\n]);\nconst VoteLabel = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__VoteLabel\",\n    componentId: \"sc-d33395ac-66\"\n})([\n    \"color:white;font-size:0.9rem;margin-bottom:0.5rem;\"\n]);\nconst VoteBar = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__VoteBar\",\n    componentId: \"sc-d33395ac-67\"\n})([\n    \"width:100%;height:8px;background:rgba(255,255,255,0.1);border-radius:4px;overflow:hidden;&::after{content:'';display:block;width:\",\n    \"%;height:100%;background:linear-gradient(90deg,#4a90e2,#7b68ee);transition:width 0.8s ease;}\"\n], (props)=>props.width);\nconst ProposalActions = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"Web3Dashboard__ProposalActions\",\n    componentId: \"sc-d33395ac-68\"\n})([\n    \"display:flex;gap:1rem;\"\n]);\nconst VoteButton = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"Web3Dashboard__VoteButton\",\n    componentId: \"sc-d33395ac-69\"\n})([\n    \"flex:1;background:\",\n    \";color:white;border:\",\n    \";border-radius:8px;padding:0.75rem;font-weight:600;cursor:pointer;\"\n], (props)=>props.secondary ? \"transparent\" : \"linear-gradient(135deg, #4a90e2, #7b68ee)\", (props)=>props.secondary ? \"1px solid rgba(255, 255, 255, 0.3)\" : \"none\");\n// Вспомогательная функция\nconst getRarityColor = (rarity)=>{\n    switch(rarity){\n        case \"legendary\":\n            return \"#ffd700\";\n        case \"epic\":\n            return \"#9370db\";\n        case \"rare\":\n            return \"#4a90e2\";\n        default:\n            return \"#6b7280\";\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Web3Dashboard);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Web3Dashboard.tsx\n");

/***/ })

};
;