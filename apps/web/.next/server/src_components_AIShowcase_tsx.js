"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_AIShowcase_tsx";
exports.ids = ["src_components_AIShowcase_tsx"];
exports.modules = {

/***/ "./src/components/AIShowcase.tsx":
/*!***************************************!*\
  !*** ./src/components/AIShowcase.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst emotionalMetrics = [\n    {\n        name: \"Счастье\",\n        value: 0.85,\n        color: \"#4ade80\",\n        icon: \"\\uD83D\\uDE0A\",\n        description: \"Уровень позитивных эмоций игрока\"\n    },\n    {\n        name: \"Фокус\",\n        value: 0.92,\n        color: \"#3b82f6\",\n        icon: \"\\uD83C\\uDFAF\",\n        description: \"Концентрация и внимание к игре\"\n    },\n    {\n        name: \"Уверенность\",\n        value: 0.78,\n        color: \"#8b5cf6\",\n        icon: \"\\uD83D\\uDCAA\",\n        description: \"Уверенность в принятии решений\"\n    },\n    {\n        name: \"Стресс\",\n        value: 0.23,\n        color: \"#ef4444\",\n        icon: \"\\uD83D\\uDE30\",\n        description: \"Уровень стресса и напряжения\"\n    },\n    {\n        name: \"Мотивация\",\n        value: 0.89,\n        color: \"#f59e0b\",\n        icon: \"\\uD83D\\uDD25\",\n        description: \"Желание продолжать игру\"\n    },\n    {\n        name: \"Усталость\",\n        value: 0.31,\n        color: \"#6b7280\",\n        icon: \"\\uD83D\\uDE34\",\n        description: \"Уровень усталости игрока\"\n    }\n];\nconst aiFeatures = [\n    {\n        title: \"Анализ эмоций в реальном времени\",\n        description: \"ИИ анализирует микровыражения, тон голоса и поведенческие паттерны\",\n        icon: \"\\uD83E\\uDDE0\",\n        metrics: [\n            \"Точность: 95%\",\n            \"Задержка: <100мс\",\n            \"Источников данных: 8\"\n        ]\n    },\n    {\n        title: \"Предиктивная аналитика\",\n        description: \"Предсказание следующих ходов и вероятности победы\",\n        icon: \"\\uD83D\\uDD2E\",\n        metrics: [\n            \"Точность предсказаний: 85%\",\n            \"Анализируемых факторов: 50+\",\n            \"Обновлений/сек: 1000+\"\n        ]\n    },\n    {\n        title: \"Персонализированное обучение\",\n        description: \"Адаптивные уроки и рекомендации на основе стиля игры\",\n        icon: \"\\uD83D\\uDCDA\",\n        metrics: [\n            \"Персональных путей: ∞\",\n            \"Адаптация: в реальном времени\",\n            \"Эффективность: +300%\"\n        ]\n    },\n    {\n        title: \"Детекция тильта и выгорания\",\n        description: \"Раннее обнаружение негативных состояний и превентивные меры\",\n        icon: \"\\uD83D\\uDEE1️\",\n        metrics: [\n            \"Точность детекции: 99%\",\n            \"Время реакции: <1сек\",\n            \"Предотвращённых тильтов: 10K+\"\n        ]\n    }\n];\nconst AIShowcase = ({ emotionalState })=>{\n    const [activeFeature, setActiveFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentMetrics, setCurrentMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(emotionalMetrics);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Симуляция обновления метрик в реальном времени\n        const interval = setInterval(()=>{\n            setCurrentMetrics((prev)=>prev.map((metric)=>({\n                        ...metric,\n                        value: Math.max(0, Math.min(1, metric.value + (Math.random() - 0.5) * 0.1))\n                    })));\n        }, 2000);\n        return ()=>clearInterval(interval);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Автоматическое переключение функций\n        const interval = setInterval(()=>{\n            setActiveFeature((prev)=>(prev + 1) % aiFeatures.length);\n        }, 4000);\n        return ()=>clearInterval(interval);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIContainer, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentWrapper, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionTitle, {\n                            children: \"Эмоциональный ИИ\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSubtitle, {\n                            children: \"Революционная система понимания и анализа игроков\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MainContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricsPanel, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PanelHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PanelTitle, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalysisIndicator, {\n                                                    active: isAnalyzing\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Анализ в реальном времени\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PanelSubtitle, {\n                                            children: \"ИИ анализирует 8 эмоциональных состояний одновременно\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricsGrid, {\n                                    children: currentMetrics.map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.8\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                scale: 1\n                                            },\n                                            transition: {\n                                                delay: index * 0.1\n                                            },\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricIcon, {\n                                                            children: metric.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricName, {\n                                                            children: metric.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricValue, {\n                                                    color: metric.color,\n                                                    children: [\n                                                        (metric.value * 100).toFixed(0),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricBar, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricProgress, {\n                                                        color: metric.color,\n                                                        width: metric.value * 100,\n                                                        initial: {\n                                                            width: 0\n                                                        },\n                                                        animate: {\n                                                            width: `${metric.value * 100}%`\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricDescription, {\n                                                    children: metric.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, metric.name, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesPanel, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureSelector, {\n                                    children: aiFeatures.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureTab, {\n                                            active: index === activeFeature,\n                                            onClick: ()=>setActiveFeature(index),\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureTabIcon, {\n                                                    children: feature.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureTabTitle, {\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 50\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -50\n                                            },\n                                            transition: {\n                                                duration: 0.5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureIcon, {\n                                                            children: aiFeatures[activeFeature].icon\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureTitle, {\n                                                                    children: aiFeatures[activeFeature].title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureDescription, {\n                                                                    children: aiFeatures[activeFeature].description\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureMetrics, {\n                                                    children: aiFeatures[activeFeature].metrics.map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureMetric, {\n                                                            initial: {\n                                                                opacity: 0,\n                                                                y: 20\n                                                            },\n                                                            animate: {\n                                                                opacity: 1,\n                                                                y: 0\n                                                            },\n                                                            transition: {\n                                                                delay: index * 0.1\n                                                            },\n                                                            children: [\n                                                                \"✨ \",\n                                                                metric\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, activeFeature, true, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoSection, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoTitle, {\n                            children: \"Попробуйте ИИ в действии\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoGrid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCard, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoIcon, {\n                                            children: \"\\uD83C\\uDFAE\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCardTitle, {\n                                            children: \"Игровая сессия\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCardDescription, {\n                                            children: \"Начните игру и наблюдайте за анализом ИИ\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCard, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoIcon, {\n                                            children: \"\\uD83D\\uDCCA\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCardTitle, {\n                                            children: \"Аналитика\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCardDescription, {\n                                            children: \"Изучите детальную аналитику своей игры\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCard, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoIcon, {\n                                            children: \"\\uD83C\\uDFAF\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCardTitle, {\n                                            children: \"Персонализация\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DemoCardDescription, {\n                                            children: \"Получите персональные рекомендации\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/S/a/A1-K/apps/web/src/components/AIShowcase.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, undefined);\n};\n// Стилизованные компоненты\nconst AIContainer = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__AIContainer\",\n    componentId: \"sc-ce6ca944-0\"\n})([\n    \"min-height:100vh;background:linear-gradient(135deg,#1a1a2e 0%,#16213e 50%,#0f0f23 100%);padding:4rem 2rem;display:flex;align-items:center;justify-content:center;\"\n]);\nconst ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__ContentWrapper\",\n    componentId: \"sc-ce6ca944-1\"\n})([\n    \"max-width:1400px;width:100%;\"\n]);\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h2.withConfig({\n    displayName: \"AIShowcase__SectionTitle\",\n    componentId: \"sc-ce6ca944-2\"\n})([\n    \"font-size:3.5rem;font-weight:900;text-align:center;margin-bottom:1rem;background:linear-gradient(45deg,#4a90e2,#7b68ee,#9370db);-webkit-background-clip:text;-webkit-text-fill-color:transparent;@media (max-width:768px){font-size:2.5rem;}\"\n]);\nconst SectionSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"AIShowcase__SectionSubtitle\",\n    componentId: \"sc-ce6ca944-3\"\n})([\n    \"font-size:1.3rem;color:rgba(255,255,255,0.7);text-align:center;margin-bottom:4rem;max-width:800px;margin-left:auto;margin-right:auto;\"\n]);\nconst MainContent = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__MainContent\",\n    componentId: \"sc-ce6ca944-4\"\n})([\n    \"display:grid;grid-template-columns:1fr 1fr;gap:3rem;margin-bottom:4rem;@media (max-width:1024px){grid-template-columns:1fr;gap:2rem;}\"\n]);\nconst MetricsPanel = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__MetricsPanel\",\n    componentId: \"sc-ce6ca944-5\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:20px;padding:2rem;backdrop-filter:blur(20px);border:1px solid rgba(255,255,255,0.1);\"\n]);\nconst PanelHeader = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__PanelHeader\",\n    componentId: \"sc-ce6ca944-6\"\n})([\n    \"margin-bottom:2rem;\"\n]);\nconst PanelTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h3.withConfig({\n    displayName: \"AIShowcase__PanelTitle\",\n    componentId: \"sc-ce6ca944-7\"\n})([\n    \"font-size:1.5rem;font-weight:700;color:white;margin-bottom:0.5rem;display:flex;align-items:center;gap:0.5rem;\"\n]);\nconst PanelSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"AIShowcase__PanelSubtitle\",\n    componentId: \"sc-ce6ca944-8\"\n})([\n    \"color:rgba(255,255,255,0.7);font-size:0.9rem;\"\n]);\nconst AnalysisIndicator = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__AnalysisIndicator\",\n    componentId: \"sc-ce6ca944-9\"\n})([\n    \"width:12px;height:12px;border-radius:50%;background:\",\n    \";animation:\",\n    \";\"\n], (props)=>props.active ? \"#4ade80\" : \"#6b7280\", (props)=>props.active ? \"pulse 2s infinite\" : \"none\");\nconst MetricsGrid = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__MetricsGrid\",\n    componentId: \"sc-ce6ca944-10\"\n})([\n    \"display:grid;grid-template-columns:repeat(2,1fr);gap:1rem;@media (max-width:640px){grid-template-columns:1fr;}\"\n]);\nconst MetricCard = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"AIShowcase__MetricCard\",\n    componentId: \"sc-ce6ca944-11\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;padding:1.5rem;border:1px solid rgba(255,255,255,0.1);\"\n]);\nconst MetricHeader = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__MetricHeader\",\n    componentId: \"sc-ce6ca944-12\"\n})([\n    \"display:flex;align-items:center;gap:0.5rem;margin-bottom:1rem;\"\n]);\nconst MetricIcon = styled_components__WEBPACK_IMPORTED_MODULE_3___default().span.withConfig({\n    displayName: \"AIShowcase__MetricIcon\",\n    componentId: \"sc-ce6ca944-13\"\n})([\n    \"font-size:1.5rem;\"\n]);\nconst MetricName = styled_components__WEBPACK_IMPORTED_MODULE_3___default().span.withConfig({\n    displayName: \"AIShowcase__MetricName\",\n    componentId: \"sc-ce6ca944-14\"\n})([\n    \"font-weight:600;color:white;\"\n]);\nconst MetricValue = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__MetricValue\",\n    componentId: \"sc-ce6ca944-15\"\n})([\n    \"font-size:2rem;font-weight:700;color:\",\n    \";margin-bottom:0.5rem;\"\n], (props)=>props.color);\nconst MetricBar = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__MetricBar\",\n    componentId: \"sc-ce6ca944-16\"\n})([\n    \"width:100%;height:6px;background:rgba(255,255,255,0.1);border-radius:3px;overflow:hidden;margin-bottom:0.5rem;\"\n]);\nconst MetricProgress = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"AIShowcase__MetricProgress\",\n    componentId: \"sc-ce6ca944-17\"\n})([\n    \"height:100%;background:\",\n    \";border-radius:3px;\"\n], (props)=>props.color);\nconst MetricDescription = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"AIShowcase__MetricDescription\",\n    componentId: \"sc-ce6ca944-18\"\n})([\n    \"font-size:0.8rem;color:rgba(255,255,255,0.6);line-height:1.4;\"\n]);\nconst FeaturesPanel = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__FeaturesPanel\",\n    componentId: \"sc-ce6ca944-19\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:20px;padding:2rem;backdrop-filter:blur(20px);border:1px solid rgba(255,255,255,0.1);\"\n]);\nconst FeatureSelector = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__FeatureSelector\",\n    componentId: \"sc-ce6ca944-20\"\n})([\n    \"display:grid;grid-template-columns:repeat(2,1fr);gap:0.5rem;margin-bottom:2rem;@media (max-width:640px){grid-template-columns:1fr;}\"\n]);\nconst FeatureTab = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button).withConfig({\n    displayName: \"AIShowcase__FeatureTab\",\n    componentId: \"sc-ce6ca944-21\"\n})([\n    \"background:\",\n    \";border:1px solid \",\n    \";border-radius:10px;padding:1rem;color:\",\n    \";cursor:pointer;transition:all 0.3s ease;text-align:left;\"\n], (props)=>props.active ? \"rgba(74, 144, 226, 0.2)\" : \"transparent\", (props)=>props.active ? \"#4a90e2\" : \"rgba(255, 255, 255, 0.1)\", (props)=>props.active ? \"#4a90e2\" : \"rgba(255, 255, 255, 0.8)\");\nconst FeatureTabIcon = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__FeatureTabIcon\",\n    componentId: \"sc-ce6ca944-22\"\n})([\n    \"font-size:1.5rem;margin-bottom:0.5rem;\"\n]);\nconst FeatureTabTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__FeatureTabTitle\",\n    componentId: \"sc-ce6ca944-23\"\n})([\n    \"font-size:0.9rem;font-weight:600;\"\n]);\nconst FeatureContent = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__FeatureContent\",\n    componentId: \"sc-ce6ca944-24\"\n})([\n    \"min-height:200px;\"\n]);\nconst FeatureHeader = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__FeatureHeader\",\n    componentId: \"sc-ce6ca944-25\"\n})([\n    \"display:flex;align-items:flex-start;gap:1rem;margin-bottom:1.5rem;\"\n]);\nconst FeatureIcon = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__FeatureIcon\",\n    componentId: \"sc-ce6ca944-26\"\n})([\n    \"font-size:3rem;color:#4a90e2;\"\n]);\nconst FeatureTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h4.withConfig({\n    displayName: \"AIShowcase__FeatureTitle\",\n    componentId: \"sc-ce6ca944-27\"\n})([\n    \"font-size:1.5rem;font-weight:700;color:white;margin-bottom:0.5rem;\"\n]);\nconst FeatureDescription = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"AIShowcase__FeatureDescription\",\n    componentId: \"sc-ce6ca944-28\"\n})([\n    \"color:rgba(255,255,255,0.7);line-height:1.6;\"\n]);\nconst FeatureMetrics = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__FeatureMetrics\",\n    componentId: \"sc-ce6ca944-29\"\n})([\n    \"display:flex;flex-direction:column;gap:0.5rem;\"\n]);\nconst FeatureMetric = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"AIShowcase__FeatureMetric\",\n    componentId: \"sc-ce6ca944-30\"\n})([\n    \"color:rgba(255,255,255,0.8);font-size:0.9rem;\"\n]);\nconst DemoSection = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__DemoSection\",\n    componentId: \"sc-ce6ca944-31\"\n})([\n    \"text-align:center;\"\n]);\nconst DemoTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h3.withConfig({\n    displayName: \"AIShowcase__DemoTitle\",\n    componentId: \"sc-ce6ca944-32\"\n})([\n    \"font-size:2rem;font-weight:700;color:white;margin-bottom:2rem;\"\n]);\nconst DemoGrid = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__DemoGrid\",\n    componentId: \"sc-ce6ca944-33\"\n})([\n    \"display:grid;grid-template-columns:repeat(3,1fr);gap:2rem;@media (max-width:768px){grid-template-columns:1fr;}\"\n]);\nconst DemoCard = styled_components__WEBPACK_IMPORTED_MODULE_3___default()(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div).withConfig({\n    displayName: \"AIShowcase__DemoCard\",\n    componentId: \"sc-ce6ca944-34\"\n})([\n    \"background:rgba(255,255,255,0.05);border-radius:15px;padding:2rem;text-align:center;cursor:pointer;border:1px solid rgba(255,255,255,0.1);transition:all 0.3s ease;&:hover{background:rgba(255,255,255,0.1);}\"\n]);\nconst DemoIcon = styled_components__WEBPACK_IMPORTED_MODULE_3___default().div.withConfig({\n    displayName: \"AIShowcase__DemoIcon\",\n    componentId: \"sc-ce6ca944-35\"\n})([\n    \"font-size:3rem;margin-bottom:1rem;\"\n]);\nconst DemoCardTitle = styled_components__WEBPACK_IMPORTED_MODULE_3___default().h4.withConfig({\n    displayName: \"AIShowcase__DemoCardTitle\",\n    componentId: \"sc-ce6ca944-36\"\n})([\n    \"font-size:1.2rem;font-weight:600;color:white;margin-bottom:0.5rem;\"\n]);\nconst DemoCardDescription = styled_components__WEBPACK_IMPORTED_MODULE_3___default().p.withConfig({\n    displayName: \"AIShowcase__DemoCardDescription\",\n    componentId: \"sc-ce6ca944-37\"\n})([\n    \"color:rgba(255,255,255,0.7);font-size:0.9rem;line-height:1.5;\"\n]);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AIShowcase);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/AIShowcase.tsx\n");

/***/ })

};
;