import '@testing-library/jest-dom';

// Мокируем локальное хранилище
class LocalStorageMock {
  constructor() {
    this.store = {};
  }

  clear() {
    this.store = {};
  }

  getItem(key) {
    return this.store[key] || null;
  }

  setItem(key, value) {
    this.store[key] = String(value);
  }

  removeItem(key) {
    delete this.store[key];
  }
}

// Устанавливаем моки перед всеми тестами
global.localStorage = new LocalStorageMock();

// Подавляем предупреждения консоли в тестах
global.console = {
  ...console,
  // Можно раскомментировать для отладки тестов
  // log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
};

// Мокируем IntersectionObserver, который не доступен в jsdom
global.IntersectionObserver = class IntersectionObserver {
  constructor(callback) {
    this.callback = callback;
  }

  observe() {
    return null;
  }

  unobserve() {
    return null;
  }

  disconnect() {
    return null;
  }
};

// Мокируем matchMedia, который не доступен в jsdom
global.matchMedia = jest.fn().mockImplementation(query => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: jest.fn(),
  removeListener: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
}));