import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/router';
import HomePage from '../../pages/index';

// Мокаем Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Мокаем динамические импорты
jest.mock('next/dynamic', () => (fn: any) => {
  const Component = fn();
  return Component;
});

// Мокаем framer-motion
jest.mock('framer-motion', () => {
  const React = require('react');
  return {
    motion: {
      div: ({ children, ...props }: any) => React.createElement('div', props, children),
      section: ({ children, ...props }: any) => React.createElement('section', props, children),
      button: ({ children, ...props }: any) => React.createElement('button', props, children),
      nav: ({ children, ...props }: any) => React.createElement('nav', props, children),
    },
    AnimatePresence: ({ children }: { children: React.ReactNode }) => React.createElement('div', {}, children),
  };
});

// Мокаем styled-components
jest.mock('styled-components', () => ({
  __esModule: true,
  default: (tag: any) => (styles: any) => tag,
}));

// Мокаем Three.js компоненты
jest.mock('@react-three/fiber', () => ({
  Canvas: ({ children }: { children: React.ReactNode }) => <div data-testid="canvas">{children}</div>,
}));

jest.mock('@react-three/drei', () => ({
  Text3D: () => <div data-testid="text3d" />,
  Float: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  OrbitControls: () => <div data-testid="orbit-controls" />,
}));

// Мокаем компоненты
jest.mock('../../components/LoadingScreen', () => {
  return function LoadingScreen({ message }: { message: string }) {
    return <div data-testid="loading-screen">{message}</div>;
  };
});

jest.mock('../../components/Hero3D', () => {
  return function Hero3D({ onStartJourney }: { onStartJourney: () => void }) {
    return (
      <div data-testid="hero3d">
        <button onClick={onStartJourney}>Начать путешествие</button>
      </div>
    );
  };
});

jest.mock('../../components/RevolutionaryFeatures', () => {
  return function RevolutionaryFeatures({ currentSection }: { currentSection: number }) {
    return <div data-testid="revolutionary-features">Section: {currentSection}</div>;
  };
});

jest.mock('../../components/MetaversePreview-simple', () => {
  return function MetaversePreview() {
    return <div data-testid="metaverse-preview">Метавселенная</div>;
  };
});

jest.mock('../../components/AIShowcase', () => {
  return function AIShowcase({ emotionalState }: { emotionalState: any }) {
    return <div data-testid="ai-showcase">ИИ: {emotionalState.happiness}</div>;
  };
});

jest.mock('../../components/Web3Dashboard', () => {
  return function Web3Dashboard({ web3Status, onConnectWallet }: any) {
    return (
      <div data-testid="web3-dashboard">
        <button onClick={onConnectWallet}>Подключить кошелёк</button>
        <span>Статус: {web3Status.connected ? 'подключен' : 'отключен'}</span>
      </div>
    );
  };
});

jest.mock('../../components/StreamingPlatform', () => {
  return function StreamingPlatform() {
    return <div data-testid="streaming-platform">Стриминг платформа</div>;
  };
});

jest.mock('../../components/GameDemo', () => {
  return function GameDemo({ onStartGame }: { onStartGame: () => void }) {
    return (
      <div data-testid="game-demo">
        <button onClick={onStartGame}>Начать игру</button>
      </div>
    );
  };
});

jest.mock('../../components/Footer', () => {
  return function Footer({ onSubscribe }: { onSubscribe: (email: string) => void }) {
    return (
      <div data-testid="footer">
        <button onClick={() => onSubscribe('<EMAIL>')}>Подписаться</button>
      </div>
    );
  };
});

const mockRouter = {
  pathname: '/',
  push: jest.fn(),
};

// Мокаем navigator.mediaDevices для тестов стриминга
Object.defineProperty(navigator, 'mediaDevices', {
  value: {
    getUserMedia: jest.fn().mockResolvedValue({
      getTracks: () => [{ stop: jest.fn() }],
    }),
  },
  writable: true,
});

describe('HomePage Integration', () => {
  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('должен рендерить все основные секции', async () => {
    render(<HomePage />);

    // Ждём загрузки компонентов
    await waitFor(() => {
      expect(screen.getByTestId('hero3d')).toBeInTheDocument();
    });

    expect(screen.getByTestId('revolutionary-features')).toBeInTheDocument();
    expect(screen.getByTestId('metaverse-preview')).toBeInTheDocument();
    expect(screen.getByTestId('ai-showcase')).toBeInTheDocument();
    expect(screen.getByTestId('web3-dashboard')).toBeInTheDocument();
    expect(screen.getByTestId('streaming-platform')).toBeInTheDocument();
    expect(screen.getByTestId('game-demo')).toBeInTheDocument();
    expect(screen.getByTestId('footer')).toBeInTheDocument();
  });

  it('должен показывать экран загрузки в начале', () => {
    render(<HomePage />);

    expect(screen.getByTestId('loading-screen')).toBeInTheDocument();
    expect(screen.getByText('Инициализация квантовых систем...')).toBeInTheDocument();
  });

  it('должен инициализировать квантовые системы', async () => {
    render(<HomePage />);

    // Ускоряем время для завершения инициализации
    jest.advanceTimersByTime(3000);

    await waitFor(() => {
      expect(screen.queryByTestId('loading-screen')).not.toBeInTheDocument();
    });
  });

  it('должен обрабатывать навигацию между секциями', async () => {
    render(<HomePage />);

    // Ждём загрузки
    jest.advanceTimersByTime(3000);

    await waitFor(() => {
      expect(screen.getByTestId('hero3d')).toBeInTheDocument();
    });

    // Кликаем "Начать путешествие"
    fireEvent.click(screen.getByText('Начать путешествие'));

    // Проверяем, что секция изменилась
    await waitFor(() => {
      expect(screen.getByText('Section: 1')).toBeInTheDocument();
    });
  });

  it('должен обрабатывать подключение Web3 кошелька', async () => {
    render(<HomePage />);

    jest.advanceTimersByTime(3000);

    await waitFor(() => {
      expect(screen.getByText('Статус: отключен')).toBeInTheDocument();
    });

    // Подключаем кошелёк
    fireEvent.click(screen.getByText('Подключить кошелёк'));

    // Ускоряем время для завершения подключения
    jest.advanceTimersByTime(1500);

    await waitFor(() => {
      expect(screen.getByText('Статус: подключен')).toBeInTheDocument();
    });
  });

  it('должен обрабатывать запуск игры', async () => {
    render(<HomePage />);

    jest.advanceTimersByTime(3000);

    await waitFor(() => {
      expect(screen.getByTestId('game-demo')).toBeInTheDocument();
    });

    // Кликаем "Начать игру"
    fireEvent.click(screen.getByText('Начать игру'));

    expect(mockRouter.push).toHaveBeenCalledWith('/games');
  });

  it('должен обрабатывать подписку на новости', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

    render(<HomePage />);

    jest.advanceTimersByTime(3000);

    await waitFor(() => {
      expect(screen.getByTestId('footer')).toBeInTheDocument();
    });

    // Подписываемся на новости
    fireEvent.click(screen.getByText('Подписаться'));

    expect(consoleSpy).toHaveBeenCalledWith('Подписка:', '<EMAIL>');

    consoleSpy.mockRestore();
  });

  it('должен отображать навигационные точки', async () => {
    render(<HomePage />);

    jest.advanceTimersByTime(3000);

    await waitFor(() => {
      // Проверяем наличие навигационных точек (7 секций)
      const navDots = screen.getAllByRole('button');
      expect(navDots.length).toBeGreaterThanOrEqual(7);
    });
  });

  it('должен обрабатывать скролл для переключения секций', async () => {
    render(<HomePage />);

    jest.advanceTimersByTime(3000);

    await waitFor(() => {
      expect(screen.getByTestId('hero3d')).toBeInTheDocument();
    });

    // Симулируем скролл
    Object.defineProperty(window, 'scrollY', { value: window.innerHeight, writable: true });
    fireEvent.scroll(window);

    // Проверяем, что секция изменилась
    await waitFor(() => {
      expect(screen.getByText('Section: 1')).toBeInTheDocument();
    });
  });

  it('должен отображать статус квантовых систем', async () => {
    render(<HomePage />);

    jest.advanceTimersByTime(3000);

    await waitFor(() => {
      expect(screen.getByText('Квантовая связь активна')).toBeInTheDocument();
    });
  });

  it('должен отображать статус эмоционального ИИ', async () => {
    render(<HomePage />);

    jest.advanceTimersByTime(3000);

    await waitFor(() => {
      expect(screen.getByText('ИИ анализ активен')).toBeInTheDocument();
    });
  });

  it('должен обрабатывать ошибки инициализации', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    // Мокаем ошибку
    const originalSetTimeout = global.setTimeout;
    global.setTimeout = jest.fn(() => {
      throw new Error('Initialization failed');
    }) as any;

    render(<HomePage />);

    jest.advanceTimersByTime(3000);

    await waitFor(() => {
      expect(screen.queryByTestId('loading-screen')).not.toBeInTheDocument();
    });

    // Восстанавливаем
    global.setTimeout = originalSetTimeout;
    consoleSpy.mockRestore();
  });

  it('должен корректно работать на мобильных устройствах', async () => {
    // Симулируем мобильное устройство
    Object.defineProperty(window, 'innerWidth', { value: 375, writable: true });
    Object.defineProperty(window, 'innerHeight', { value: 667, writable: true });

    render(<HomePage />);

    jest.advanceTimersByTime(3000);

    await waitFor(() => {
      expect(screen.getByTestId('hero3d')).toBeInTheDocument();
    });

    // Проверяем, что все компоненты рендерятся на мобильном
    expect(screen.getByTestId('revolutionary-features')).toBeInTheDocument();
    expect(screen.getByTestId('metaverse-preview')).toBeInTheDocument();
  });
});
