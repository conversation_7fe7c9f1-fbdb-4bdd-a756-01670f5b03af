import { Card, Player, GameState, GameAction, TableCard } from './types';
import { canDefend, canAddCard } from './gameLogic';

export interface AIPersonality {
  aggression: number;    // 0-1: склонность к атаке
  caution: number;       // 0-1: осторожность в защите
  bluffing: number;      // 0-1: склонность к блефу
  adaptability: number;  // 0-1: способность адаптироваться
}

export interface AIAnalysis {
  playerStress: number;      // Уровень стресса игрока (0-1)
  playerConfidence: number;  // Уверенность игрока (0-1)
  gamePhase: 'early' | 'mid' | 'late';
  riskLevel: number;         // Текущий уровень риска (0-1)
  recommendedStrategy: 'aggressive' | 'defensive' | 'balanced';
}

export class EmotionalAI {
  private personality: AIPersonality;
  private gameHistory: GameAction[] = [];
  private playerBehaviorPattern: Map<string, number[]> = new Map();

  constructor(personality?: Partial<AIPersonality>) {
    this.personality = {
      aggression: 0.6,
      caution: 0.7,
      bluffing: 0.3,
      adaptability: 0.8,
      ...personality
    };
  }

  // Анализ текущего состояния игры и игрока
  analyzeGameState(gameState: GameState, targetPlayerId: string): AIAnalysis {
    const targetPlayer = gameState.players.find(p => p.id === targetPlayerId);
    const gameProgress = this.calculateGameProgress(gameState);
    
    // Анализ поведения игрока
    const playerActions = this.gameHistory.filter(action => action.playerId === targetPlayerId);
    const playerStress = this.calculatePlayerStress(playerActions, gameState);
    const playerConfidence = this.calculatePlayerConfidence(targetPlayer, gameState);
    
    // Определение фазы игры
    const gamePhase = gameProgress < 0.3 ? 'early' : gameProgress < 0.7 ? 'mid' : 'late';
    
    // Расчет уровня риска
    const riskLevel = this.calculateRiskLevel(gameState, targetPlayer);
    
    // Рекомендуемая стратегия
    const recommendedStrategy = this.determineStrategy(playerStress, playerConfidence, riskLevel);

    return {
      playerStress,
      playerConfidence,
      gamePhase,
      riskLevel,
      recommendedStrategy
    };
  }

  // Принятие решения ИИ
  makeDecision(gameState: GameState, botPlayer: Player): GameAction | null {
    const analysis = this.analyzeGameState(gameState, botPlayer.id);
    const availableActions = this.getAvailableActions(gameState, botPlayer);
    
    if (availableActions.length === 0) return null;

    // Адаптация личности на основе анализа
    this.adaptPersonality(analysis);

    // Выбор действия на основе стратегии
    const selectedAction = this.selectAction(availableActions, analysis, gameState, botPlayer);
    
    // Сохранение действия в историю
    if (selectedAction) {
      this.gameHistory.push(selectedAction);
    }

    return selectedAction;
  }

  private calculateGameProgress(gameState: GameState): number {
    const totalCards = gameState.players.reduce((sum, player) => sum + player.cards.length, 0);
    const initialCards = gameState.players.length * 6;
    return 1 - (totalCards / initialCards);
  }

  private calculatePlayerStress(playerActions: GameAction[], gameState: GameState): number {
    if (playerActions.length === 0) return 0.5;

    let stressFactors = 0;
    const recentActions = playerActions.slice(-5); // Последние 5 действий

    // Анализ паттернов стресса
    const takeCardActions = recentActions.filter(a => a.type === 'take_cards').length;
    const quickDecisions = recentActions.filter((_, index, arr) => {
      if (index === 0) return false;
      // Симуляция времени принятия решения (в реальной игре было бы время)
      return Math.random() < 0.3; // 30% "быстрых" решений
    }).length;

    stressFactors += takeCardActions * 0.3; // Взятие карт увеличивает стресс
    stressFactors += quickDecisions * 0.2;  // Быстрые решения могут указывать на стресс

    return Math.min(stressFactors, 1);
  }

  private calculatePlayerConfidence(player: Player | undefined, gameState: GameState): number {
    if (!player) return 0.5;

    let confidence = 0.5;

    // Количество карт (меньше карт = больше уверенности)
    const cardRatio = player.cards.length / 6;
    confidence += (1 - cardRatio) * 0.3;

    // Качество карт (наличие козырей и старших карт)
    const trumpCards = player.cards.filter(card => card.suit === gameState.trumpSuit).length;
    const highCards = player.cards.filter(card => card.value >= 11).length;
    
    confidence += (trumpCards / player.cards.length) * 0.2;
    confidence += (highCards / player.cards.length) * 0.2;

    return Math.min(Math.max(confidence, 0), 1);
  }

  private calculateRiskLevel(gameState: GameState, player: Player | undefined): number {
    if (!player) return 0.5;

    let risk = 0;

    // Количество карт у игрока
    risk += (player.cards.length / 10) * 0.4;

    // Количество карт на столе
    risk += (gameState.table.length / 6) * 0.3;

    // Фаза игры
    const gameProgress = this.calculateGameProgress(gameState);
    risk += gameProgress * 0.3;

    return Math.min(risk, 1);
  }

  private determineStrategy(stress: number, confidence: number, risk: number): 'aggressive' | 'defensive' | 'balanced' {
    const aggressionScore = confidence - stress - risk + this.personality.aggression;
    const defensiveScore = stress + risk - confidence + this.personality.caution;

    if (aggressionScore > 0.6) return 'aggressive';
    if (defensiveScore > 0.6) return 'defensive';
    return 'balanced';
  }

  private adaptPersonality(analysis: AIAnalysis): void {
    const adaptationRate = this.personality.adaptability * 0.1;

    // Адаптация на основе успешности стратегии
    if (analysis.recommendedStrategy === 'aggressive' && analysis.playerStress > 0.7) {
      this.personality.aggression = Math.max(this.personality.aggression - adaptationRate, 0);
      this.personality.caution = Math.min(this.personality.caution + adaptationRate, 1);
    } else if (analysis.recommendedStrategy === 'defensive' && analysis.playerConfidence > 0.7) {
      this.personality.aggression = Math.min(this.personality.aggression + adaptationRate, 1);
      this.personality.caution = Math.max(this.personality.caution - adaptationRate, 0);
    }
  }

  private getAvailableActions(gameState: GameState, botPlayer: Player): GameAction[] {
    const actions: GameAction[] = [];
    const botIndex = gameState.players.findIndex(p => p.id === botPlayer.id);

    switch (gameState.phase) {
      case 'attacking':
        if (botIndex === gameState.currentPlayerIndex) {
          // Можем атаковать
          botPlayer.cards.forEach(card => {
            if (gameState.table.length === 0 || canAddCard(card, gameState.table)) {
              actions.push({
                type: 'attack',
                playerId: botPlayer.id,
                card
              });
            }
          });

          // Можем пропустить ход
          actions.push({
            type: 'pass_turn',
            playerId: botPlayer.id
          });
        }
        break;

      case 'defending':
        if (botIndex === gameState.defendingPlayerIndex) {
          // Можем защищаться
          gameState.table.forEach((tableCard, position) => {
            if (!tableCard.defendCard) {
              botPlayer.cards.forEach(card => {
                if (canDefend(tableCard.attackCard, card, gameState.trumpSuit)) {
                  actions.push({
                    type: 'defend',
                    playerId: botPlayer.id,
                    card,
                    targetPosition: position
                  });
                }
              });
            }
          });

          // Можем взять карты
          actions.push({
            type: 'take_cards',
            playerId: botPlayer.id
          });
        }
        break;

      case 'adding':
        if (botIndex !== gameState.defendingPlayerIndex) {
          // Можем подкидывать
          botPlayer.cards.forEach(card => {
            if (canAddCard(card, gameState.table)) {
              actions.push({
                type: 'add_card',
                playerId: botPlayer.id,
                card
              });
            }
          });
        }

        // Можем завершить ход
        actions.push({
          type: 'finish_turn',
          playerId: botPlayer.id
        });
        break;
    }

    return actions;
  }

  private selectAction(
    actions: GameAction[], 
    analysis: AIAnalysis, 
    gameState: GameState, 
    botPlayer: Player
  ): GameAction {
    if (actions.length === 1) return actions[0];

    // Фильтруем действия по стратегии
    const strategicActions = this.filterActionsByStrategy(actions, analysis, gameState, botPlayer);
    
    if (strategicActions.length === 0) {
      // Если нет стратегических действий, выбираем случайно
      return actions[Math.floor(Math.random() * actions.length)];
    }

    // Оцениваем каждое действие
    const scoredActions = strategicActions.map(action => ({
      action,
      score: this.scoreAction(action, analysis, gameState, botPlayer)
    }));

    // Сортируем по убыванию оценки
    scoredActions.sort((a, b) => b.score - a.score);

    // Добавляем элемент случайности (но предпочитаем лучшие действия)
    const randomFactor = Math.random();
    if (randomFactor < 0.7) {
      // 70% - выбираем лучшее действие
      return scoredActions[0].action;
    } else if (randomFactor < 0.9 && scoredActions.length > 1) {
      // 20% - выбираем второе лучшее
      return scoredActions[1].action;
    } else {
      // 10% - случайное действие
      return scoredActions[Math.floor(Math.random() * scoredActions.length)].action;
    }
  }

  private filterActionsByStrategy(
    actions: GameAction[], 
    analysis: AIAnalysis, 
    gameState: GameState, 
    botPlayer: Player
  ): GameAction[] {
    switch (analysis.recommendedStrategy) {
      case 'aggressive':
        return actions.filter(action => 
          action.type === 'attack' || 
          action.type === 'add_card' ||
          (action.type === 'defend' && this.isStrongDefense(action, gameState))
        );

      case 'defensive':
        return actions.filter(action => 
          action.type === 'defend' || 
          action.type === 'take_cards' ||
          action.type === 'pass_turn' ||
          action.type === 'finish_turn'
        );

      case 'balanced':
      default:
        return actions; // Все действия доступны
    }
  }

  private scoreAction(
    action: GameAction, 
    analysis: AIAnalysis, 
    gameState: GameState, 
    botPlayer: Player
  ): number {
    let score = 0;

    switch (action.type) {
      case 'attack':
        if (action.card) {
          // Предпочитаем атаковать слабыми картами
          score += (15 - action.card.value) * 0.1;
          // Бонус за агрессивную стратегию
          if (analysis.recommendedStrategy === 'aggressive') score += 0.5;
        }
        break;

      case 'defend':
        if (action.card) {
          // Предпочитаем защищаться минимально возможными картами
          const tableCard = gameState.table[action.targetPosition || 0];
          if (tableCard) {
            const valueDiff = action.card.value - tableCard.attackCard.value;
            score += Math.max(0, 5 - valueDiff) * 0.1;
          }
        }
        break;

      case 'take_cards':
        // Штраф за взятие карт, но иногда это лучший выбор
        score -= 0.8;
        if (analysis.riskLevel > 0.8) score += 0.4; // Меньший штраф в критической ситуации
        break;

      case 'add_card':
        if (action.card) {
          // Предпочитаем подкидывать слабые карты
          score += (15 - action.card.value) * 0.1;
        }
        break;

      case 'finish_turn':
        // Бонус за завершение хода в правильный момент
        score += 0.3;
        if (analysis.gamePhase === 'late') score += 0.2;
        break;

      case 'pass_turn':
        // Нейтральное действие
        score += 0.1;
        break;
    }

    // Модификаторы на основе личности
    score *= (1 + this.personality.adaptability * 0.2);

    return score;
  }

  private isStrongDefense(action: GameAction, gameState: GameState): boolean {
    if (action.type !== 'defend' || !action.card || action.targetPosition === undefined) {
      return false;
    }

    const tableCard = gameState.table[action.targetPosition];
    if (!tableCard) return false;

    // Считаем защиту сильной, если используем козырь или карту значительно старше
    const isTrump = action.card.suit === gameState.trumpSuit;
    const valueDiff = action.card.value - tableCard.attackCard.value;

    return isTrump || valueDiff >= 3;
  }

  // Получение эмоционального состояния бота для отображения
  getEmotionalState(): { confidence: number; aggression: number; patience: number } {
    return {
      confidence: (this.personality.aggression + (1 - this.personality.caution)) / 2,
      aggression: this.personality.aggression,
      patience: this.personality.caution
    };
  }

  // Сброс истории для новой игры
  resetHistory(): void {
    this.gameHistory = [];
    this.playerBehaviorPattern.clear();
  }
}
