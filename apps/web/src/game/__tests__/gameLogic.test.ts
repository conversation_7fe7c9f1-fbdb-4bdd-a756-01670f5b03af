import { 
  createDeck, 
  createPlayer, 
  createGame, 
  addPlayerToGame, 
  dealCards, 
  canDefend, 
  canAddCard,
  executeAction 
} from '../gameLogic';
import { GameSettings, Card } from '../types';

describe('Game Logic', () => {
  const defaultSettings: GameSettings = {
    maxPlayers: 2,
    deckSize: 36,
    allowAddCards: true,
    maxAttackCards: 6
  };

  describe('createDeck', () => {
    it('должен создать колоду из 36 карт', () => {
      const deck = createDeck(36);
      expect(deck).toHaveLength(36);
      
      // Проверяем, что все карты уникальны
      const cardIds = deck.map(card => card.id);
      const uniqueIds = new Set(cardIds);
      expect(uniqueIds.size).toBe(36);
    });

    it('должен создать колоду из 52 карт', () => {
      const deck = createDeck(52);
      expect(deck).toHaveLength(52);
    });

    it('карты должны иметь правильную структуру', () => {
      const deck = createDeck(36);
      const card = deck[0];
      
      expect(card).toHaveProperty('suit');
      expect(card).toHaveProperty('rank');
      expect(card).toHaveProperty('id');
      expect(card).toHaveProperty('value');
      expect(typeof card.value).toBe('number');
    });
  });

  describe('createPlayer', () => {
    it('должен создать игрока с правильными свойствами', () => {
      const player = createPlayer('player1', 'Тестовый игрок', false);
      
      expect(player.id).toBe('player1');
      expect(player.name).toBe('Тестовый игрок');
      expect(player.isBot).toBe(false);
      expect(player.cards).toEqual([]);
      expect(player.emotionalState).toBeUndefined();
    });

    it('должен создать бота с эмоциональным состоянием', () => {
      const bot = createPlayer('bot1', 'Бот', true);
      
      expect(bot.isBot).toBe(true);
      expect(bot.emotionalState).toBeDefined();
      expect(bot.emotionalState?.confidence).toBe(0.5);
      expect(bot.emotionalState?.aggression).toBe(0.5);
      expect(bot.emotionalState?.patience).toBe(0.5);
    });
  });

  describe('createGame', () => {
    it('должен создать игру с правильными настройками', () => {
      const game = createGame(defaultSettings);
      
      expect(game.players).toEqual([]);
      expect(game.phase).toBe('waiting');
      expect(game.deck.length).toBeGreaterThan(0);
      expect(game.trumpCard).toBeDefined();
      expect(game.trumpSuit).toBe(game.trumpCard.suit);
      expect(game.table).toEqual([]);
      expect(game.discardPile).toEqual([]);
      expect(game.winner).toBe(null);
    });
  });

  describe('addPlayerToGame', () => {
    it('должен добавить игрока в игру', () => {
      const game = createGame(defaultSettings);
      const player = createPlayer('player1', 'Игрок 1', false);
      
      const updatedGame = addPlayerToGame(game, player);
      
      expect(updatedGame.players).toHaveLength(1);
      expect(updatedGame.players[0]).toEqual(player);
    });

    it('должен выбросить ошибку при превышении лимита игроков', () => {
      let game = createGame(defaultSettings);
      const player1 = createPlayer('player1', 'Игрок 1', false);
      const player2 = createPlayer('player2', 'Игрок 2', false);
      const player3 = createPlayer('player3', 'Игрок 3', false);
      
      game = addPlayerToGame(game, player1);
      game = addPlayerToGame(game, player2);
      
      expect(() => addPlayerToGame(game, player3)).toThrow('Игра уже заполнена');
    });
  });

  describe('dealCards', () => {
    it('должен раздать карты игрокам', () => {
      let game = createGame(defaultSettings);
      const player1 = createPlayer('player1', 'Игрок 1', false);
      const player2 = createPlayer('player2', 'Игрок 2', false);
      
      game = addPlayerToGame(game, player1);
      game = addPlayerToGame(game, player2);
      
      const updatedGame = dealCards(game);
      
      expect(updatedGame.players[0].cards).toHaveLength(6);
      expect(updatedGame.players[1].cards).toHaveLength(6);
      expect(updatedGame.phase).toBe('attacking');
    });

    it('должен выбросить ошибку при недостатке игроков', () => {
      const game = createGame(defaultSettings);
      
      expect(() => dealCards(game)).toThrow('Недостаточно игроков для начала игры');
    });
  });

  describe('canDefend', () => {
    const trumpSuit = 'hearts';
    
    it('должен позволить защиту старшей картой той же масти', () => {
      const attackCard: Card = { suit: 'spades', rank: '7', id: 'spades-7', value: 7 };
      const defendCard: Card = { suit: 'spades', rank: '10', id: 'spades-10', value: 10 };
      
      expect(canDefend(attackCard, defendCard, trumpSuit)).toBe(true);
    });

    it('не должен позволить защиту младшей картой той же масти', () => {
      const attackCard: Card = { suit: 'spades', rank: '10', id: 'spades-10', value: 10 };
      const defendCard: Card = { suit: 'spades', rank: '7', id: 'spades-7', value: 7 };
      
      expect(canDefend(attackCard, defendCard, trumpSuit)).toBe(false);
    });

    it('должен позволить защиту козырем против некозырной карты', () => {
      const attackCard: Card = { suit: 'spades', rank: '10', id: 'spades-10', value: 10 };
      const defendCard: Card = { suit: 'hearts', rank: '6', id: 'hearts-6', value: 6 };
      
      expect(canDefend(attackCard, defendCard, trumpSuit)).toBe(true);
    });

    it('не должен позволить защиту некозырем против козыря', () => {
      const attackCard: Card = { suit: 'hearts', rank: '6', id: 'hearts-6', value: 6 };
      const defendCard: Card = { suit: 'spades', rank: 'A', id: 'spades-A', value: 14 };
      
      expect(canDefend(attackCard, defendCard, trumpSuit)).toBe(false);
    });
  });

  describe('canAddCard', () => {
    it('должен позволить подкинуть карту того же ранга', () => {
      const tableCards = [
        {
          attackCard: { suit: 'spades', rank: '7', id: 'spades-7', value: 7 },
          defendCard: { suit: 'hearts', rank: '10', id: 'hearts-10', value: 10 },
          position: 0
        }
      ];
      const addCard: Card = { suit: 'clubs', rank: '7', id: 'clubs-7', value: 7 };
      
      expect(canAddCard(addCard, tableCards)).toBe(true);
    });

    it('не должен позволить подкинуть карту другого ранга', () => {
      const tableCards = [
        {
          attackCard: { suit: 'spades', rank: '7', id: 'spades-7', value: 7 },
          position: 0
        }
      ];
      const addCard: Card = { suit: 'clubs', rank: '8', id: 'clubs-8', value: 8 };
      
      expect(canAddCard(addCard, tableCards)).toBe(false);
    });

    it('не должен позволить подкинуть карту на пустой стол', () => {
      const tableCards: any[] = [];
      const addCard: Card = { suit: 'clubs', rank: '7', id: 'clubs-7', value: 7 };
      
      expect(canAddCard(addCard, tableCards)).toBe(false);
    });
  });

  describe('executeAction', () => {
    let game: any;
    
    beforeEach(() => {
      game = createGame(defaultSettings);
      const player1 = createPlayer('player1', 'Игрок 1', false);
      const player2 = createPlayer('player2', 'Игрок 2', false);
      
      game = addPlayerToGame(game, player1);
      game = addPlayerToGame(game, player2);
      game = dealCards(game);
    });

    it('должен выполнить атаку', () => {
      const attackCard = game.players[0].cards[0];
      const action = {
        type: 'attack' as const,
        playerId: 'player1',
        card: attackCard
      };
      
      const updatedGame = executeAction(game, action);
      
      expect(updatedGame.table).toHaveLength(1);
      expect(updatedGame.table[0].attackCard).toEqual(attackCard);
      expect(updatedGame.phase).toBe('defending');
      expect(updatedGame.players[0].cards).toHaveLength(5);
    });

    it('должен выбросить ошибку при неизвестном действии', () => {
      const action = {
        type: 'unknown' as any,
        playerId: 'player1'
      };
      
      expect(() => executeAction(game, action)).toThrow('Неизвестное действие: unknown');
    });
  });
});
