import { 
  Card, 
  Suit, 
  Rank, 
  GameState, 
  Player, 
  TableCard, 
  GameAction,
  GameSettings,
  RANK_VALUES,
  SUIT_SYMBOLS 
} from './types';

// Создание колоды карт
export function createDeck(deckSize: 36 | 52 = 36, quantumSeed?: number): Card[] {
  const suits: Suit[] = ['hearts', 'diamonds', 'clubs', 'spades'];
  const ranks: Rank[] = deckSize === 36
    ? ['6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A']
    : ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'] as Rank[];

  const deck: Card[] = [];

  suits.forEach(suit => {
    ranks.forEach(rank => {
      deck.push({
        suit,
        rank,
        id: `${suit}-${rank}`,
        value: RANK_VALUES[rank]
      });
    });
  });

  return shuffleDeck(deck, quantumSeed);
}

// Перемешивание колоды
export function shuffleDeck(deck: Card[], quantumSeed?: number): Card[] {
  const shuffled = [...deck];

  // Используем квантовое семя если доступно, иначе обычный Math.random
  const getRandom = () => {
    if (quantumSeed) {
      // Простой PRNG на основе квантового семени
      const seed = (quantumSeed * 9301 + 49297) % 233280;
      return (seed / 233280);
    }
    return Math.random();
  };

  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(getRandom() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// Создание нового игрока
export function createPlayer(id: string, name: string, isBot: boolean = false): Player {
  return {
    id,
    name,
    cards: [],
    isBot,
    emotionalState: isBot ? {
      confidence: 0.5,
      aggression: 0.5,
      patience: 0.5
    } : undefined
  };
}

// Создание новой игры
export function createGame(settings: GameSettings): GameState {
  const deck = createDeck(settings.deckSize);
  const trumpCard = deck[deck.length - 1]; // Последняя карта - козырь
  
  return {
    id: `game-${Date.now()}`,
    players: [],
    currentPlayerIndex: 0,
    attackingPlayerIndex: 0,
    defendingPlayerIndex: 1,
    trumpSuit: trumpCard.suit,
    trumpCard,
    deck,
    table: [],
    discardPile: [],
    phase: 'waiting',
    winner: null,
    gameSettings: settings
  };
}

// Добавление игрока в игру
export function addPlayerToGame(gameState: GameState, player: Player): GameState {
  if (gameState.players.length >= gameState.gameSettings.maxPlayers) {
    throw new Error('Игра уже заполнена');
  }

  return {
    ...gameState,
    players: [...gameState.players, player]
  };
}

// Раздача карт
export function dealCards(gameState: GameState): GameState {
  if (gameState.players.length < 2) {
    throw new Error('Недостаточно игроков для начала игры');
  }

  const newGameState = { ...gameState };
  const cardsPerPlayer = 6;
  let deckIndex = 0;

  // Раздаем по 6 карт каждому игроку
  newGameState.players = newGameState.players.map(player => ({
    ...player,
    cards: newGameState.deck.slice(deckIndex, deckIndex + cardsPerPlayer)
  }));

  deckIndex += newGameState.players.length * cardsPerPlayer;

  // Оставшиеся карты в колоде (кроме козырной)
  newGameState.deck = newGameState.deck.slice(deckIndex, -1);

  newGameState.phase = 'attacking';
  
  return newGameState;
}

// Проверка, может ли карта бить другую карту
export function canDefend(attackCard: Card, defendCard: Card, trumpSuit: Suit): boolean {
  // Если обе карты одной масти, защищающая должна быть старше
  if (attackCard.suit === defendCard.suit) {
    return defendCard.value > attackCard.value;
  }
  
  // Если атакующая карта не козырь, а защищающая козырь - можно бить
  if (attackCard.suit !== trumpSuit && defendCard.suit === trumpSuit) {
    return true;
  }
  
  // В остальных случаях нельзя
  return false;
}

// Проверка, можно ли подкинуть карту
export function canAddCard(card: Card, tableCards: TableCard[]): boolean {
  if (tableCards.length === 0) return false;
  
  // Можно подкидывать карты того же ранга, что уже есть на столе
  const existingRanks = new Set<Rank>();
  
  tableCards.forEach(tableCard => {
    existingRanks.add(tableCard.attackCard.rank);
    if (tableCard.defendCard) {
      existingRanks.add(tableCard.defendCard.rank);
    }
  });
  
  return existingRanks.has(card.rank);
}

// Выполнение игрового действия
export function executeAction(gameState: GameState, action: GameAction): GameState {
  const newGameState = { ...gameState };
  
  switch (action.type) {
    case 'attack':
      return executeAttack(newGameState, action);
    case 'defend':
      return executeDefend(newGameState, action);
    case 'add_card':
      return executeAddCard(newGameState, action);
    case 'take_cards':
      return executeTakeCards(newGameState, action);
    case 'pass_turn':
      return executePassTurn(newGameState, action);
    case 'finish_turn':
      return executeFinishTurn(newGameState, action);
    default:
      throw new Error(`Неизвестное действие: ${action.type}`);
  }
}

// Выполнение атаки
function executeAttack(gameState: GameState, action: GameAction): GameState {
  if (!action.card) throw new Error('Карта не указана');
  
  const player = gameState.players[gameState.currentPlayerIndex];
  const cardIndex = player.cards.findIndex(c => c.id === action.card!.id);
  
  if (cardIndex === -1) throw new Error('Карта не найдена у игрока');
  
  // Проверяем, можно ли атаковать этой картой
  if (gameState.table.length > 0 && !canAddCard(action.card, gameState.table)) {
    throw new Error('Нельзя атаковать этой картой');
  }
  
  // Убираем карту у игрока
  const newPlayer = {
    ...player,
    cards: player.cards.filter((_, index) => index !== cardIndex)
  };
  
  // Добавляем карту на стол
  const newTableCard: TableCard = {
    attackCard: action.card,
    position: gameState.table.length
  };
  
  return {
    ...gameState,
    players: gameState.players.map((p, index) => 
      index === gameState.currentPlayerIndex ? newPlayer : p
    ),
    table: [...gameState.table, newTableCard],
    phase: 'defending'
  };
}

// Выполнение защиты
function executeDefend(gameState: GameState, action: GameAction): GameState {
  if (!action.card || action.targetPosition === undefined) {
    throw new Error('Карта или позиция не указаны');
  }
  
  const player = gameState.players[gameState.defendingPlayerIndex];
  const cardIndex = player.cards.findIndex(c => c.id === action.card!.id);
  
  if (cardIndex === -1) throw new Error('Карта не найдена у игрока');
  
  const tableCard = gameState.table[action.targetPosition];
  if (!tableCard || tableCard.defendCard) {
    throw new Error('Неверная позиция для защиты');
  }
  
  // Проверяем, можно ли защититься этой картой
  if (!canDefend(tableCard.attackCard, action.card, gameState.trumpSuit)) {
    throw new Error('Нельзя защититься этой картой');
  }
  
  // Убираем карту у игрока
  const newPlayer = {
    ...player,
    cards: player.cards.filter((_, index) => index !== cardIndex)
  };
  
  // Добавляем карту защиты на стол
  const newTable = gameState.table.map((tc, index) => 
    index === action.targetPosition 
      ? { ...tc, defendCard: action.card }
      : tc
  );
  
  // Проверяем, все ли карты защищены
  const allDefended = newTable.every(tc => tc.defendCard);
  
  return {
    ...gameState,
    players: gameState.players.map((p, index) => 
      index === gameState.defendingPlayerIndex ? newPlayer : p
    ),
    table: newTable,
    phase: allDefended ? 'adding' : 'defending'
  };
}

// Выполнение подкидывания карты
function executeAddCard(gameState: GameState, action: GameAction): GameState {
  if (!action.card) throw new Error('Карта не указана');
  
  const player = gameState.players[gameState.currentPlayerIndex];
  const cardIndex = player.cards.findIndex(c => c.id === action.card!.id);
  
  if (cardIndex === -1) throw new Error('Карта не найдена у игрока');
  
  // Проверяем, можно ли подкинуть эту карту
  if (!canAddCard(action.card, gameState.table)) {
    throw new Error('Нельзя подкинуть эту карту');
  }
  
  // Проверяем лимит карт на столе
  if (gameState.table.length >= gameState.gameSettings.maxAttackCards) {
    throw new Error('Достигнут лимит карт в атаке');
  }
  
  // Убираем карту у игрока
  const newPlayer = {
    ...player,
    cards: player.cards.filter((_, index) => index !== cardIndex)
  };
  
  // Добавляем карту на стол
  const newTableCard: TableCard = {
    attackCard: action.card,
    position: gameState.table.length
  };
  
  return {
    ...gameState,
    players: gameState.players.map((p, index) => 
      index === gameState.currentPlayerIndex ? newPlayer : p
    ),
    table: [...gameState.table, newTableCard],
    phase: 'defending'
  };
}

// Выполнение взятия карт
function executeTakeCards(gameState: GameState, action: GameAction): GameState {
  const defendingPlayer = gameState.players[gameState.defendingPlayerIndex];
  
  // Собираем все карты со стола
  const cardsFromTable: Card[] = [];
  gameState.table.forEach(tableCard => {
    cardsFromTable.push(tableCard.attackCard);
    if (tableCard.defendCard) {
      cardsFromTable.push(tableCard.defendCard);
    }
  });
  
  // Добавляем карты защищающемуся игроку
  const newDefendingPlayer = {
    ...defendingPlayer,
    cards: [...defendingPlayer.cards, ...cardsFromTable]
  };
  
  return {
    ...gameState,
    players: gameState.players.map((p, index) => 
      index === gameState.defendingPlayerIndex ? newDefendingPlayer : p
    ),
    table: [],
    phase: 'attacking',
    currentPlayerIndex: getNextPlayerIndex(gameState, gameState.attackingPlayerIndex),
    attackingPlayerIndex: getNextPlayerIndex(gameState, gameState.attackingPlayerIndex),
    defendingPlayerIndex: getNextPlayerIndex(gameState, getNextPlayerIndex(gameState, gameState.attackingPlayerIndex))
  };
}

// Выполнение завершения хода
function executeFinishTurn(gameState: GameState, action: GameAction): GameState {
  // Все карты со стола идут в отбой
  const cardsFromTable: Card[] = [];
  gameState.table.forEach(tableCard => {
    cardsFromTable.push(tableCard.attackCard);
    if (tableCard.defendCard) {
      cardsFromTable.push(tableCard.defendCard);
    }
  });
  
  return {
    ...gameState,
    table: [],
    discardPile: [...gameState.discardPile, ...cardsFromTable],
    phase: 'attacking',
    currentPlayerIndex: gameState.defendingPlayerIndex,
    attackingPlayerIndex: gameState.defendingPlayerIndex,
    defendingPlayerIndex: getNextPlayerIndex(gameState, gameState.defendingPlayerIndex)
  };
}

// Выполнение пропуска хода
function executePassTurn(gameState: GameState, action: GameAction): GameState {
  const nextPlayerIndex = getNextPlayerIndex(gameState, gameState.currentPlayerIndex);
  
  return {
    ...gameState,
    currentPlayerIndex: nextPlayerIndex
  };
}

// Получение индекса следующего игрока
function getNextPlayerIndex(gameState: GameState, currentIndex: number): number {
  return (currentIndex + 1) % gameState.players.length;
}

// Проверка окончания игры
export function checkGameEnd(gameState: GameState): GameState {
  // Игра заканчивается, когда у игрока закончились карты
  const playersWithCards = gameState.players.filter(p => p.cards.length > 0);
  
  if (playersWithCards.length <= 1) {
    const winner = playersWithCards.length === 1 ? playersWithCards[0] : null;
    
    return {
      ...gameState,
      phase: 'finished',
      winner: winner?.id || null
    };
  }
  
  return gameState;
}

// Добор карт из колоды
export function drawCards(gameState: GameState): GameState {
  if (gameState.deck.length === 0) return gameState;
  
  const newGameState = { ...gameState };
  const targetHandSize = 6;
  
  // Добираем карты всем игрокам до 6 карт (или пока есть карты в колоде)
  newGameState.players = newGameState.players.map(player => {
    const cardsNeeded = Math.max(0, targetHandSize - player.cards.length);
    const cardsToTake = Math.min(cardsNeeded, newGameState.deck.length);
    
    if (cardsToTake > 0) {
      const newCards = newGameState.deck.splice(0, cardsToTake);
      return {
        ...player,
        cards: [...player.cards, ...newCards]
      };
    }
    
    return player;
  });
  
  return newGameState;
}
