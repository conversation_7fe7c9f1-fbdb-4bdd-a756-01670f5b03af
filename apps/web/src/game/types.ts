// Типы для игры в дурака

export type Suit = 'hearts' | 'diamonds' | 'clubs' | 'spades';
export type Rank = '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | 'J' | 'Q' | 'K' | 'A';

export interface Card {
  suit: Suit;
  rank: Rank;
  id: string;
  value: number; // Для сравнения карт (6=6, 7=7, ..., A=14)
}

export interface Player {
  id: string;
  name: string;
  cards: Card[];
  isBot: boolean;
  emotionalState?: {
    confidence: number;
    aggression: number;
    patience: number;
  };
}

export interface GameState {
  id: string;
  players: Player[];
  currentPlayerIndex: number;
  attackingPlayerIndex: number;
  defendingPlayerIndex: number;
  trumpSuit: Suit;
  trumpCard: Card;
  deck: Card[];
  table: TableCard[];
  discardPile: Card[];
  phase: GamePhase;
  winner: string | null;
  gameSettings: GameSettings;
}

export interface TableCard {
  attackCard: Card;
  defendCard?: Card;
  position: number;
}

export type GamePhase = 
  | 'waiting'     // Ожидание игроков
  | 'dealing'     // Раздача карт
  | 'attacking'   // Фаза атаки
  | 'defending'   // Фаза защиты
  | 'adding'      // Подкидывание карт
  | 'taking'      // Взятие карт
  | 'finished';   // Игра окончена

export interface GameSettings {
  maxPlayers: number;
  deckSize: 36 | 52; // 36 карт (6-A) или 52 карты (2-A)
  allowAddCards: boolean; // Можно ли подкидывать
  maxAttackCards: number; // Максимум карт в атаке
}

export interface GameAction {
  type: GameActionType;
  playerId: string;
  card?: Card;
  cards?: Card[];
  targetPosition?: number;
}

export type GameActionType =
  | 'attack'      // Атака картой
  | 'defend'      // Защита картой
  | 'add_card'    // Подкидывание карты
  | 'take_cards'  // Взятие карт
  | 'pass_turn'   // Пропуск хода
  | 'finish_turn'; // Завершение хода

export interface GameEvent {
  type: GameEventType;
  playerId?: string;
  message: string;
  data?: any;
  timestamp: number;
}

export type GameEventType =
  | 'game_started'
  | 'cards_dealt'
  | 'turn_started'
  | 'card_played'
  | 'cards_taken'
  | 'turn_finished'
  | 'game_finished'
  | 'player_joined'
  | 'player_left'
  | 'error';

// Константы для игры
export const SUIT_SYMBOLS: Record<Suit, string> = {
  hearts: '♥️',
  diamonds: '♦️',
  clubs: '♣️',
  spades: '♠️'
};

export const SUIT_COLORS: Record<Suit, 'red' | 'black'> = {
  hearts: 'red',
  diamonds: 'red',
  clubs: 'black',
  spades: 'black'
};

export const RANK_VALUES: Record<Rank, number> = {
  '2': 2,
  '3': 3,
  '4': 4,
  '5': 5,
  '6': 6,
  '7': 7,
  '8': 8,
  '9': 9,
  '10': 10,
  'J': 11,
  'Q': 12,
  'K': 13,
  'A': 14
};

export const RANK_NAMES: Record<Rank, string> = {
  '2': '2',
  '3': '3',
  '4': '4',
  '5': '5',
  '6': '6',
  '7': '7',
  '8': '8',
  '9': '9',
  '10': '10',
  'J': 'Валет',
  'Q': 'Дама',
  'K': 'Король',
  'A': 'Туз'
};

export const SUIT_NAMES: Record<Suit, string> = {
  hearts: 'Червы',
  diamonds: 'Бубны',
  clubs: 'Трефы',
  spades: 'Пики'
};
