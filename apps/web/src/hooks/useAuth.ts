import { useState, useEffect, useCallback } from 'react';

interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  level: number;
  experience: number;
  coins: number;
  isVip: boolean;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null
  });

  const login = useCallback(async (email: string, password: string) => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Симуляция API запроса
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockUser: User = {
        id: '1',
        username: 'TestUser',
        email,
        avatar: '👤',
        level: 15,
        experience: 2450,
        coins: 1500,
        isVip: false
      };

      setAuthState({
        user: mockUser,
        isAuthenticated: true,
        isLoading: false,
        error: null
      });

      // Сохраняем в localStorage
      localStorage.setItem('auth_token', 'mock_token_123');
      localStorage.setItem('user', JSON.stringify(mockUser));

    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Ошибка входа'
      }));
    }
  }, []);

  const logout = useCallback(() => {
    setAuthState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null
    });

    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
  }, []);

  const register = useCallback(async (username: string, email: string, password: string) => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Симуляция API запроса
      await new Promise(resolve => setTimeout(resolve, 1500));

      const mockUser: User = {
        id: '1',
        username,
        email,
        avatar: '👤',
        level: 1,
        experience: 0,
        coins: 100,
        isVip: false
      };

      setAuthState({
        user: mockUser,
        isAuthenticated: true,
        isLoading: false,
        error: null
      });

      localStorage.setItem('auth_token', 'mock_token_123');
      localStorage.setItem('user', JSON.stringify(mockUser));

    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Ошибка регистрации'
      }));
    }
  }, []);

  // Проверка токена при загрузке
  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    const userData = localStorage.getItem('user');

    if (token && userData) {
      try {
        const user = JSON.parse(userData);
        setAuthState({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null
        });
      } catch (error) {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user');
        setAuthState(prev => ({ ...prev, isLoading: false }));
      }
    } else {
      setAuthState(prev => ({ ...prev, isLoading: false }));
    }
  }, []);

  return {
    ...authState,
    login,
    logout,
    register
  };
};
