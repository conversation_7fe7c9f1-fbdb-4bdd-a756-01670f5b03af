import { useState, useEffect, useCallback } from 'react';

interface EmotionalMetrics {
  happiness: number;
  focus: number;
  confidence: number;
  stress: number;
  motivation: number;
  fatigue: number;
}

interface EmotionalState {
  metrics: EmotionalMetrics;
  overallMood: 'excellent' | 'good' | 'neutral' | 'poor' | 'critical';
  recommendations: string[];
  lastAnalysis: Date;
  isAnalyzing: boolean;
}

interface GameBehavior {
  decisionTime: number;
  riskTolerance: number;
  aggressiveness: number;
  consistency: number;
}

export const useEmotionalAI = () => {
  const [emotionalState, setEmotionalState] = useState<EmotionalState>({
    metrics: {
      happiness: 0.5,
      focus: 0.5,
      confidence: 0.5,
      stress: 0.5,
      motivation: 0.5,
      fatigue: 0.5
    },
    overallMood: 'neutral',
    recommendations: [],
    lastAnalysis: new Date(),
    isAnalyzing: false
  });

  const [gameBehavior, setGameBehavior] = useState<GameBehavior>({
    decisionTime: 5000,
    riskTolerance: 0.5,
    aggressiveness: 0.5,
    consistency: 0.5
  });

  const analyzeUser = useCallback(async (inputData?: any) => {
    setEmotionalState(prev => ({ ...prev, isAnalyzing: true }));

    try {
      // Симуляция анализа эмоций
      await new Promise(resolve => setTimeout(resolve, 2000));

      const newMetrics: EmotionalMetrics = {
        happiness: Math.max(0, Math.min(1, 0.7 + (Math.random() - 0.5) * 0.4)),
        focus: Math.max(0, Math.min(1, 0.8 + (Math.random() - 0.5) * 0.3)),
        confidence: Math.max(0, Math.min(1, 0.6 + (Math.random() - 0.5) * 0.5)),
        stress: Math.max(0, Math.min(1, 0.3 + (Math.random() - 0.5) * 0.4)),
        motivation: Math.max(0, Math.min(1, 0.75 + (Math.random() - 0.5) * 0.3)),
        fatigue: Math.max(0, Math.min(1, 0.4 + (Math.random() - 0.5) * 0.4))
      };

      // Определяем общее настроение
      const averagePositive = (newMetrics.happiness + newMetrics.focus + newMetrics.confidence + newMetrics.motivation) / 4;
      const averageNegative = (newMetrics.stress + newMetrics.fatigue) / 2;
      const overallScore = (averagePositive - averageNegative + 1) / 2; // Нормализация от 0 до 1

      let overallMood: EmotionalState['overallMood'];
      if (overallScore > 0.8) overallMood = 'excellent';
      else if (overallScore > 0.6) overallMood = 'good';
      else if (overallScore > 0.4) overallMood = 'neutral';
      else if (overallScore > 0.2) overallMood = 'poor';
      else overallMood = 'critical';

      // Генерируем рекомендации
      const recommendations = generateRecommendations(newMetrics, overallMood);

      // Обновляем игровое поведение
      setGameBehavior({
        decisionTime: 3000 + (1 - newMetrics.focus) * 5000,
        riskTolerance: newMetrics.confidence * 0.8 + newMetrics.motivation * 0.2,
        aggressiveness: newMetrics.confidence * 0.6 + (1 - newMetrics.stress) * 0.4,
        consistency: newMetrics.focus * 0.7 + (1 - newMetrics.fatigue) * 0.3
      });

      setEmotionalState({
        metrics: newMetrics,
        overallMood,
        recommendations,
        lastAnalysis: new Date(),
        isAnalyzing: false
      });

    } catch (error) {
      console.error('Emotional analysis error:', error);
      setEmotionalState(prev => ({ ...prev, isAnalyzing: false }));
    }
  }, []);

  const generateRecommendations = (metrics: EmotionalMetrics, mood: EmotionalState['overallMood']): string[] => {
    const recommendations: string[] = [];

    if (metrics.stress > 0.7) {
      recommendations.push('Рекомендуем сделать перерыв для снижения стресса');
    }

    if (metrics.fatigue > 0.8) {
      recommendations.push('Высокий уровень усталости - время отдохнуть');
    }

    if (metrics.focus < 0.4) {
      recommendations.push('Попробуйте упражнения на концентрацию');
    }

    if (metrics.confidence < 0.3) {
      recommendations.push('Начните с более простых игр для повышения уверенности');
    }

    if (metrics.motivation < 0.4) {
      recommendations.push('Установите небольшие достижимые цели');
    }

    if (mood === 'excellent') {
      recommendations.push('Отличное состояние! Время для сложных вызовов');
    }

    return recommendations;
  };

  // Автоматическое обновление метрик
  useEffect(() => {
    const interval = setInterval(() => {
      setEmotionalState(prev => {
        if (prev.isAnalyzing) return prev;

        const updatedMetrics = { ...prev.metrics };
        
        // Небольшие случайные изменения
        Object.keys(updatedMetrics).forEach(key => {
          const currentValue = updatedMetrics[key as keyof EmotionalMetrics];
          const change = (Math.random() - 0.5) * 0.05;
          updatedMetrics[key as keyof EmotionalMetrics] = Math.max(0, Math.min(1, currentValue + change));
        });

        return {
          ...prev,
          metrics: updatedMetrics
        };
      });
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  const getEmotionalInsight = () => {
    const { metrics, overallMood } = emotionalState;
    
    return {
      primaryEmotion: Object.entries(metrics).reduce((a, b) => metrics[a[0] as keyof EmotionalMetrics] > metrics[b[0] as keyof EmotionalMetrics] ? a : b)[0],
      moodDescription: getMoodDescription(overallMood),
      playabilityScore: (metrics.focus + metrics.motivation + (1 - metrics.fatigue) + (1 - metrics.stress)) / 4,
      suggestedGameType: getSuggestedGameType(metrics)
    };
  };

  const getMoodDescription = (mood: EmotionalState['overallMood']): string => {
    switch (mood) {
      case 'excellent': return 'Превосходное настроение! Готовы к любым вызовам';
      case 'good': return 'Хорошее настроение, отличное время для игр';
      case 'neutral': return 'Нейтральное состояние, можно играть в спокойном темпе';
      case 'poor': return 'Не лучшее настроение, рекомендуем легкие игры';
      case 'critical': return 'Критическое состояние, лучше отдохнуть';
      default: return 'Анализ настроения...';
    }
  };

  const getSuggestedGameType = (metrics: EmotionalMetrics): string => {
    if (metrics.focus > 0.8 && metrics.confidence > 0.7) {
      return 'Турнирные игры высокого уровня';
    } else if (metrics.stress < 0.3 && metrics.motivation > 0.6) {
      return 'Соревновательные игры';
    } else if (metrics.fatigue > 0.7) {
      return 'Казуальные игры';
    } else {
      return 'Обучающие игры';
    }
  };

  return {
    emotionalState,
    gameBehavior,
    analyzeUser,
    getEmotionalInsight
  };
};
