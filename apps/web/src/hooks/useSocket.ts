import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';

export interface Player {
  id: string;
  name: string;
  isOnline: boolean;
  joinedAt: Date;
}

export interface Room {
  id: string;
  name: string;
  ownerName: string;
  playerCount: number;
  maxPlayers: number;
  status: 'waiting' | 'playing' | 'finished';
  createdAt: Date;
  isPrivate: boolean;
}

export interface ChatMessage {
  id: string;
  roomId: string;
  playerId: string;
  playerName: string;
  message: string;
  timestamp: Date;
  type: 'message' | 'system' | 'game';
}

export interface GameState {
  id: string;
  roomId: string;
  players: any[];
  gameState: any;
  status: 'waiting' | 'playing' | 'finished';
  startedAt: Date;
  winner?: any;
}

export interface SocketState {
  connected: boolean;
  player: Player | null;
  currentRoom: Room | null;
  rooms: Room[];
  chatMessages: ChatMessage[];
  gameState: GameState | null;
  error: string | null;
}

export const useSocket = (serverUrl: string = 'http://localhost:3002') => {
  const socketRef = useRef<Socket | null>(null);
  const [state, setState] = useState<SocketState>({
    connected: false,
    player: null,
    currentRoom: null,
    rooms: [],
    chatMessages: [],
    gameState: null,
    error: null
  });

  // Подключение к серверу
  const connect = () => {
    if (socketRef.current?.connected) return;

    socketRef.current = io(serverUrl, {
      transports: ['websocket', 'polling']
    });

    const socket = socketRef.current;

    // Обработчики подключения
    socket.on('connect', () => {
      setState(prev => ({ ...prev, connected: true, error: null }));
    });

    socket.on('disconnect', () => {
      setState(prev => ({ ...prev, connected: false }));
    });

    socket.on('error', (error: { message: string }) => {
      setState(prev => ({ ...prev, error: error.message }));
    });

    // Обработчики игрока
    socket.on('player_registered', (data: { playerId: string; name: string }) => {
      setState(prev => ({
        ...prev,
        player: {
          id: data.playerId,
          name: data.name,
          isOnline: true,
          joinedAt: new Date()
        }
      }));
    });

    // Обработчики комнат
    socket.on('rooms_list', (rooms: Room[]) => {
      setState(prev => ({ ...prev, rooms }));
    });

    socket.on('room_created', (data: { roomId: string; room: Room }) => {
      setState(prev => ({
        ...prev,
        currentRoom: data.room,
        rooms: [...prev.rooms, data.room]
      }));
    });

    socket.on('room_joined', (data: { roomId: string; room: Room }) => {
      setState(prev => ({ ...prev, currentRoom: data.room }));
    });

    socket.on('room_left', () => {
      setState(prev => ({ ...prev, currentRoom: null, chatMessages: [] }));
    });

    socket.on('room_added', (room: Room) => {
      setState(prev => ({
        ...prev,
        rooms: [...prev.rooms.filter(r => r.id !== room.id), room]
      }));
    });

    socket.on('player_joined', (data: { player: Player; room: Room }) => {
      setState(prev => ({ ...prev, currentRoom: data.room }));
    });

    socket.on('player_left', (data: { player: Player; room: Room }) => {
      setState(prev => ({ ...prev, currentRoom: data.room }));
    });

    // Обработчики игры
    socket.on('game_started', (data: { gameId: string; gameState: GameState }) => {
      setState(prev => ({ ...prev, gameState: data.gameState }));
    });

    socket.on('game_updated', (data: { gameState: GameState }) => {
      setState(prev => ({ ...prev, gameState: data.gameState }));
    });

    // Обработчики чата
    socket.on('chat_message', (message: ChatMessage) => {
      setState(prev => ({
        ...prev,
        chatMessages: [...prev.chatMessages.slice(-49), message] // Ограничиваем 50 сообщениями
      }));
    });
  };

  // Отключение от сервера
  const disconnect = () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }
    setState({
      connected: false,
      player: null,
      currentRoom: null,
      rooms: [],
      chatMessages: [],
      gameState: null,
      error: null
    });
  };

  // Регистрация игрока
  const registerPlayer = (name: string) => {
    if (!socketRef.current) return;
    socketRef.current.emit('register_player', { name });
  };

  // Создание комнаты
  const createRoom = (name: string, maxPlayers: number = 2) => {
    if (!socketRef.current) return;
    socketRef.current.emit('create_room', { name, maxPlayers });
  };

  // Присоединение к комнате
  const joinRoom = (roomId: string) => {
    if (!socketRef.current) return;
    socketRef.current.emit('join_room', { roomId });
  };

  // Покидание комнаты
  const leaveRoom = (roomId: string) => {
    if (!socketRef.current) return;
    socketRef.current.emit('leave_room', { roomId });
  };

  // Запуск игры
  const startGame = (roomId: string) => {
    if (!socketRef.current) return;
    socketRef.current.emit('start_game', { roomId });
  };

  // Ход в игре
  const makeMove = (roomId: string, action: string, cardIndex?: number) => {
    if (!socketRef.current) return;
    socketRef.current.emit('game_move', { roomId, action, cardIndex });
  };

  // Отправка сообщения в чат
  const sendChatMessage = (roomId: string, message: string) => {
    if (!socketRef.current) return;
    socketRef.current.emit('chat_message', { roomId, message });
  };

  // Получение списка комнат
  const getRooms = () => {
    if (!socketRef.current) return;
    socketRef.current.emit('get_rooms');
  };

  // Очистка ошибки
  const clearError = () => {
    setState(prev => ({ ...prev, error: null }));
  };

  // Автоматическое подключение при монтировании
  useEffect(() => {
    connect();
    return () => disconnect();
  }, [serverUrl]);

  return {
    // Состояние
    ...state,
    
    // Методы
    connect,
    disconnect,
    registerPlayer,
    createRoom,
    joinRoom,
    leaveRoom,
    startGame,
    makeMove,
    sendChatMessage,
    getRooms,
    clearError,
    
    // Утилиты
    isConnected: state.connected,
    isInRoom: !!state.currentRoom,
    isGameActive: state.gameState?.status === 'playing'
  };
};
