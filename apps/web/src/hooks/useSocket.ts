import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';

export interface Player {
  id: string;
  name: string;
  isOnline: boolean;
  joinedAt: Date;
}

export interface Room {
  id: string;
  name: string;
  ownerName: string;
  playerCount: number;
  maxPlayers: number;
  status: 'waiting' | 'playing' | 'finished';
  createdAt: Date;
  isPrivate: boolean;
}

export interface ChatMessage {
  id: string;
  roomId: string;
  playerId: string;
  playerName: string;
  message: string;
  timestamp: Date;
  type: 'message' | 'system' | 'game';
}

export interface GameState {
  id: string;
  roomId: string;
  players: any[];
  gameState: any;
  status: 'waiting' | 'playing' | 'finished';
  startedAt: Date;
  winner?: any;
}

export interface PlayerRating {
  playerId: string;
  playerName: string;
  rating: number;
  gamesPlayed: number;
  wins: number;
  losses: number;
  winRate: number;
  highestRating: number;
  currentStreak: number;
  longestWinStreak: number;
  averageGameDuration: number;
  lastGameAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface RatingCategory {
  name: string;
  color: string;
  minRating: number;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'games' | 'wins' | 'rating' | 'streaks' | 'special';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

export interface PlayerAchievements {
  unlocked: Array<Achievement & { unlockedAt: Date }>;
  locked: Achievement[];
  progress: { total: number; unlocked: number; percentage: number };
}

export interface Tournament {
  id: string;
  name: string;
  description: string;
  type: 'single_elimination' | 'double_elimination' | 'round_robin';
  status: 'registration' | 'starting' | 'in_progress' | 'finished';
  maxParticipants: number;
  minParticipants: number;
  entryFee: number;
  prizePool: {
    first: number;
    second: number;
    third: number;
  };
  participants: TournamentParticipant[];
  bracket: TournamentBracket;
  currentRound: number;
  totalRounds: number;
  createdAt: Date;
  startedAt?: Date;
  finishedAt?: Date;
  createdBy: string;
  settings: TournamentSettings;
}

export interface TournamentParticipant {
  playerId: string;
  playerName: string;
  rating: number;
  seed: number;
  registeredAt: Date;
  status: 'registered' | 'active' | 'eliminated' | 'winner';
  currentMatch?: string;
  wins: number;
  losses: number;
}

export interface TournamentMatch {
  id: string;
  tournamentId: string;
  round: number;
  position: number;
  player1?: TournamentParticipant;
  player2?: TournamentParticipant;
  winner?: TournamentParticipant;
  gameId?: string;
  status: 'pending' | 'ready' | 'in_progress' | 'finished';
  startedAt?: Date;
  finishedAt?: Date;
  nextMatchId?: string;
}

export interface TournamentBracket {
  matches: Map<string, TournamentMatch>;
  rounds: Map<number, string[]>;
}

export interface TournamentSettings {
  autoStart: boolean;
  allowSpectators: boolean;
  timeLimit: number;
  ratingRestriction?: {
    min: number;
    max: number;
  };
}

export interface SpectatorGame {
  gameId: string;
  roomId: string;
  roomName: string;
  players: Array<{
    id: string;
    name: string;
    rating?: number;
  }>;
  spectators: Spectator[];
  gameState: any;
  status: 'waiting' | 'playing' | 'finished';
  startedAt?: Date;
  isPublic: boolean;
  allowSpectators: boolean;
  maxSpectators: number;
  tournamentId?: string;
}

export interface Spectator {
  id: string;
  playerId: string;
  playerName: string;
  joinedAt: Date;
  isActive: boolean;
}

export interface SpectatorChatMessage {
  id: string;
  gameId: string;
  spectatorId: string;
  spectatorName: string;
  message: string;
  timestamp: Date;
  type: 'spectator' | 'system';
}

export interface SocketState {
  connected: boolean;
  player: Player | null;
  currentRoom: Room | null;
  rooms: Room[];
  chatMessages: ChatMessage[];
  gameState: GameState | null;
  error: string | null;
  playerRating: PlayerRating | null;
  ratingCategory: RatingCategory | null;
  playerAchievements: PlayerAchievements | null;
  leaderboard: PlayerRating[];
  topPlayers: {
    byRating: PlayerRating[];
    byWins: PlayerRating[];
    byWinRate: PlayerRating[];
    byStreak: PlayerRating[];
  } | null;
  tournaments: Tournament[];
  currentTournament: Tournament | null;
  spectatorGames: SpectatorGame[];
  currentSpectatorGame: SpectatorGame | null;
  spectatorChatMessages: SpectatorChatMessage[];
  isSpectating: boolean;
}

export const useSocket = (serverUrl: string = 'http://localhost:3002') => {
  const socketRef = useRef<Socket | null>(null);
  const [state, setState] = useState<SocketState>({
    connected: false,
    player: null,
    currentRoom: null,
    rooms: [],
    chatMessages: [],
    gameState: null,
    error: null,
    playerRating: null,
    ratingCategory: null,
    playerAchievements: null,
    leaderboard: [],
    topPlayers: null,
    tournaments: [],
    currentTournament: null,
    spectatorGames: [],
    currentSpectatorGame: null,
    spectatorChatMessages: [],
    isSpectating: false
  });

  // Подключение к серверу
  const connect = () => {
    if (socketRef.current?.connected) return;

    socketRef.current = io(serverUrl, {
      transports: ['websocket', 'polling']
    });

    const socket = socketRef.current;

    // Обработчики подключения
    socket.on('connect', () => {
      setState(prev => ({ ...prev, connected: true, error: null }));
    });

    socket.on('disconnect', () => {
      setState(prev => ({ ...prev, connected: false }));
    });

    socket.on('error', (error: { message: string }) => {
      setState(prev => ({ ...prev, error: error.message }));
    });

    // Обработчики игрока
    socket.on('player_registered', (data: { playerId: string; name: string }) => {
      setState(prev => ({
        ...prev,
        player: {
          id: data.playerId,
          name: data.name,
          isOnline: true,
          joinedAt: new Date()
        }
      }));
    });

    // Обработчики комнат
    socket.on('rooms_list', (rooms: Room[]) => {
      setState(prev => ({ ...prev, rooms }));
    });

    socket.on('room_created', (data: { roomId: string; room: Room }) => {
      setState(prev => ({
        ...prev,
        currentRoom: data.room,
        rooms: [...prev.rooms, data.room]
      }));
    });

    socket.on('room_joined', (data: { roomId: string; room: Room }) => {
      setState(prev => ({ ...prev, currentRoom: data.room }));
    });

    socket.on('room_left', () => {
      setState(prev => ({ ...prev, currentRoom: null, chatMessages: [] }));
    });

    socket.on('room_added', (room: Room) => {
      setState(prev => ({
        ...prev,
        rooms: [...prev.rooms.filter(r => r.id !== room.id), room]
      }));
    });

    socket.on('player_joined', (data: { player: Player; room: Room }) => {
      setState(prev => ({ ...prev, currentRoom: data.room }));
    });

    socket.on('player_left', (data: { player: Player; room: Room }) => {
      setState(prev => ({ ...prev, currentRoom: data.room }));
    });

    // Обработчики игры
    socket.on('game_started', (data: { gameId: string; gameState: GameState }) => {
      setState(prev => ({ ...prev, gameState: data.gameState }));
    });

    socket.on('game_updated', (data: { gameState: GameState }) => {
      setState(prev => ({ ...prev, gameState: data.gameState }));
    });

    // Обработчики чата
    socket.on('chat_message', (message: ChatMessage) => {
      setState(prev => ({
        ...prev,
        chatMessages: [...prev.chatMessages.slice(-49), message] // Ограничиваем 50 сообщениями
      }));
    });

    // Обработчики рейтингов и достижений
    socket.on('player_rating', (data: { rating: PlayerRating; category: RatingCategory }) => {
      setState(prev => ({
        ...prev,
        playerRating: data.rating,
        ratingCategory: data.category
      }));
    });

    socket.on('player_achievements', (achievements: PlayerAchievements) => {
      setState(prev => ({
        ...prev,
        playerAchievements: achievements
      }));
    });

    socket.on('leaderboard', (leaderboard: PlayerRating[]) => {
      setState(prev => ({
        ...prev,
        leaderboard
      }));
    });

    socket.on('top_players', (topPlayers: any) => {
      setState(prev => ({
        ...prev,
        topPlayers
      }));
    });

    socket.on('game_finished', (data: any) => {
      // Обновляем рейтинг игрока если это его игра
      if (data.winner.player.id === state.player?.id) {
        setState(prev => ({
          ...prev,
          playerRating: data.winner.rating
        }));
      } else if (data.loser.player.id === state.player?.id) {
        setState(prev => ({
          ...prev,
          playerRating: data.loser.rating
        }));
      }
    });

    // Обработчики турниров
    socket.on('tournament_created', (tournament: Tournament) => {
      setState(prev => ({
        ...prev,
        tournaments: [...prev.tournaments, tournament]
      }));
    });

    socket.on('tournament_added', (tournament: Tournament) => {
      setState(prev => ({
        ...prev,
        tournaments: [...prev.tournaments, tournament]
      }));
    });

    socket.on('tournament_updated', (tournament: Tournament) => {
      setState(prev => ({
        ...prev,
        tournaments: prev.tournaments.map(t =>
          t.id === tournament.id ? tournament : t
        ),
        currentTournament: prev.currentTournament?.id === tournament.id ?
          tournament : prev.currentTournament
      }));
    });

    socket.on('tournament_registered', (data: { tournament: Tournament; participant: any }) => {
      setState(prev => ({
        ...prev,
        tournaments: prev.tournaments.map(t =>
          t.id === data.tournament.id ? data.tournament : t
        ),
        currentTournament: data.tournament
      }));
    });

    socket.on('tournaments_list', (tournaments: Tournament[]) => {
      setState(prev => ({
        ...prev,
        tournaments
      }));
    });

    socket.on('tournament_details', (tournament: Tournament) => {
      setState(prev => ({
        ...prev,
        currentTournament: tournament
      }));
    });

    // Обработчики спектаторов
    socket.on('spectator_games_list', (games: SpectatorGame[]) => {
      setState(prev => ({
        ...prev,
        spectatorGames: games
      }));
    });

    socket.on('spectator_game_added', (gameInfo: any) => {
      setState(prev => ({
        ...prev,
        spectatorGames: [...prev.spectatorGames, gameInfo]
      }));
    });

    socket.on('spectator_game_updated', (data: { gameId: string; gameState: any }) => {
      setState(prev => ({
        ...prev,
        spectatorGames: prev.spectatorGames.map(game =>
          game.gameId === data.gameId ? { ...game, gameState: data.gameState } : game
        ),
        currentSpectatorGame: prev.currentSpectatorGame?.gameId === data.gameId ?
          { ...prev.currentSpectatorGame, gameState: data.gameState } : prev.currentSpectatorGame
      }));
    });

    socket.on('spectator_joined', (data: { gameId: string; spectator: Spectator; game: SpectatorGame }) => {
      setState(prev => ({
        ...prev,
        currentSpectatorGame: data.game,
        isSpectating: true,
        spectatorChatMessages: []
      }));
    });

    socket.on('spectator_left', (data: { gameId: string }) => {
      setState(prev => ({
        ...prev,
        currentSpectatorGame: null,
        isSpectating: false,
        spectatorChatMessages: []
      }));
    });

    socket.on('spectator_added', (data: { gameId: string; spectator: Spectator }) => {
      setState(prev => ({
        ...prev,
        currentSpectatorGame: prev.currentSpectatorGame ? {
          ...prev.currentSpectatorGame,
          spectators: [...prev.currentSpectatorGame.spectators, data.spectator]
        } : prev.currentSpectatorGame
      }));
    });

    socket.on('spectator_removed', (data: { gameId: string; playerId: string }) => {
      setState(prev => ({
        ...prev,
        currentSpectatorGame: prev.currentSpectatorGame ? {
          ...prev.currentSpectatorGame,
          spectators: prev.currentSpectatorGame.spectators.filter(s => s.playerId !== data.playerId)
        } : prev.currentSpectatorGame
      }));
    });

    socket.on('spectator_chat_message', (message: SpectatorChatMessage) => {
      setState(prev => ({
        ...prev,
        spectatorChatMessages: [...prev.spectatorChatMessages.slice(-49), message]
      }));
    });
  };

  // Отключение от сервера
  const disconnect = () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }
    setState({
      connected: false,
      player: null,
      currentRoom: null,
      rooms: [],
      chatMessages: [],
      gameState: null,
      error: null,
      playerRating: null,
      ratingCategory: null,
      playerAchievements: null,
      leaderboard: [],
      topPlayers: null,
      tournaments: [],
      currentTournament: null,
      spectatorGames: [],
      currentSpectatorGame: null,
      spectatorChatMessages: [],
      isSpectating: false
    });
  };

  // Регистрация игрока
  const registerPlayer = (name: string) => {
    if (!socketRef.current) return;
    socketRef.current.emit('register_player', { name });
  };

  // Создание комнаты
  const createRoom = (name: string, maxPlayers: number = 2) => {
    if (!socketRef.current) return;
    socketRef.current.emit('create_room', { name, maxPlayers });
  };

  // Присоединение к комнате
  const joinRoom = (roomId: string) => {
    if (!socketRef.current) return;
    socketRef.current.emit('join_room', { roomId });
  };

  // Покидание комнаты
  const leaveRoom = (roomId: string) => {
    if (!socketRef.current) return;
    socketRef.current.emit('leave_room', { roomId });
  };

  // Запуск игры
  const startGame = (roomId: string) => {
    if (!socketRef.current) return;
    socketRef.current.emit('start_game', { roomId });
  };

  // Ход в игре
  const makeMove = (roomId: string, action: string, cardIndex?: number) => {
    if (!socketRef.current) return;
    socketRef.current.emit('game_move', { roomId, action, cardIndex });
  };

  // Отправка сообщения в чат
  const sendChatMessage = (roomId: string, message: string) => {
    if (!socketRef.current) return;
    socketRef.current.emit('chat_message', { roomId, message });
  };

  // Получение списка комнат
  const getRooms = () => {
    if (!socketRef.current) return;
    socketRef.current.emit('get_rooms');
  };

  // Получение рейтинга игрока
  const getPlayerRating = () => {
    if (!socketRef.current) return;
    socketRef.current.emit('get_player_rating');
  };

  // Получение достижений игрока
  const getPlayerAchievements = () => {
    if (!socketRef.current) return;
    socketRef.current.emit('get_player_achievements');
  };

  // Получение таблицы лидеров
  const getLeaderboard = (limit?: number) => {
    if (!socketRef.current) return;
    socketRef.current.emit('get_leaderboard', { limit });
  };

  // Получение топ игроков
  const getTopPlayers = () => {
    if (!socketRef.current) return;
    socketRef.current.emit('get_top_players');
  };

  // Создание турнира
  const createTournament = (
    name: string,
    description: string,
    type: Tournament['type'],
    maxParticipants: number,
    settings?: Partial<TournamentSettings>
  ) => {
    if (!socketRef.current) return;
    socketRef.current.emit('create_tournament', {
      name,
      description,
      type,
      maxParticipants,
      settings
    });
  };

  // Регистрация на турнир
  const registerForTournament = (tournamentId: string) => {
    if (!socketRef.current) return;
    socketRef.current.emit('register_for_tournament', { tournamentId });
  };

  // Получение списка турниров
  const getTournaments = (status?: string) => {
    if (!socketRef.current) return;
    socketRef.current.emit('get_tournaments', { status });
  };

  // Получение деталей турнира
  const getTournament = (tournamentId: string) => {
    if (!socketRef.current) return;
    socketRef.current.emit('get_tournament', { tournamentId });
  };

  // Получение списка игр для просмотра
  const getSpectatorGames = () => {
    if (!socketRef.current) return;
    socketRef.current.emit('get_spectator_games');
  };

  // Присоединение как зритель
  const joinSpectator = (gameId: string) => {
    if (!socketRef.current) return;
    socketRef.current.emit('join_spectator', { gameId });
  };

  // Выход из режима зрителя
  const leaveSpectator = (gameId: string) => {
    if (!socketRef.current) return;
    socketRef.current.emit('leave_spectator', { gameId });
  };

  // Отправка сообщения в чат зрителей
  const sendSpectatorChatMessage = (gameId: string, message: string) => {
    if (!socketRef.current) return;
    socketRef.current.emit('spectator_chat_message', { gameId, message });
  };

  // Очистка ошибки
  const clearError = () => {
    setState(prev => ({ ...prev, error: null }));
  };

  // Автоматическое подключение при монтировании
  useEffect(() => {
    connect();
    return () => disconnect();
  }, [serverUrl]);

  return {
    // Состояние
    ...state,
    
    // Методы
    connect,
    disconnect,
    registerPlayer,
    createRoom,
    joinRoom,
    leaveRoom,
    startGame,
    makeMove,
    sendChatMessage,
    getRooms,
    getPlayerRating,
    getPlayerAchievements,
    getLeaderboard,
    getTopPlayers,
    createTournament,
    registerForTournament,
    getTournaments,
    getTournament,
    getSpectatorGames,
    joinSpectator,
    leaveSpectator,
    sendSpectatorChatMessage,
    clearError,

    // Утилиты
    isConnected: state.connected,
    isInRoom: !!state.currentRoom,
    isGameActive: state.gameState?.status === 'playing'
  };
};
