import { useState, useCallback, useEffect } from 'react';
import {
  GameState,
  GameAction,
  Player,
  Card,
  GameEvent,
  GameSettings
} from '../game/types';
import {
  createGame,
  createPlayer,
  addPlayerToGame,
  dealCards,
  executeAction,
  checkGameEnd,
  drawCards
} from '../game/gameLogic';

interface UseGameStateReturn {
  gameState: GameState | null;
  currentPlayer: Player | null;
  isMyTurn: boolean;
  gameEvents: GameEvent[];

  // Действия
  createNewGame: (settings: GameSettings) => void;
  joinGame: (playerName: string) => void;
  startGame: () => void;
  playAction: (action: GameAction) => void;

  // Утилиты
  canPlayCard: (card: Card) => boolean;
  getValidActions: () => GameAction[];
  resetGame: () => void;
}

export const useGameState = (playerId?: string): UseGameStateReturn => {
  const [gameState, setGameState] = useState<GameState | null>(null);
  const [gameEvents, setGameEvents] = useState<GameEvent[]>([]);
  const [currentPlayerId, setCurrentPlayerId] = useState<string>(
    playerId || `player-${Date.now()}`
  );

  // Вычисляемые значения (объявляем рано, чтобы использовать в других функциях)
  const currentPlayer = gameState?.players.find(p => p.id === currentPlayerId) || null;
  const isMyTurn = gameState ?
    gameState.players[gameState.currentPlayerIndex]?.id === currentPlayerId : false;

  // Добавление события в лог
  const addEvent = useCallback((event: Omit<GameEvent, 'timestamp'>) => {
    const newEvent: GameEvent = {
      ...event,
      timestamp: Date.now()
    };
    setGameEvents(prev => [...prev, newEvent]);
  }, []);

  // Создание новой игры
  const createNewGame = useCallback((settings: GameSettings) => {
    const newGame = createGame(settings);
    setGameState(newGame);
    setGameEvents([]);

    addEvent({
      type: 'game_started',
      message: 'Новая игра создана'
    });
  }, [addEvent]);

  // Присоединение к игре
  const joinGame = useCallback((playerName: string) => {
    if (!gameState) {
      throw new Error('Игра не создана');
    }

    const player = createPlayer(currentPlayerId, playerName, false);
    
    try {
      const updatedGame = addPlayerToGame(gameState, player);
      setGameState(updatedGame);
      
      addEvent({
        type: 'player_joined',
        playerId: currentPlayerId,
        message: `${playerName} присоединился к игре`
      });
    } catch (error) {
      addEvent({
        type: 'error',
        message: error instanceof Error ? error.message : 'Ошибка присоединения'
      });
    }
  }, [gameState, currentPlayerId, addEvent]);

  // Добавление бота
  const addBot = useCallback((botName: string) => {
    if (!gameState) return;

    const bot = createPlayer(`bot-${Date.now()}`, botName, true);

    try {
      const updatedGame = addPlayerToGame(gameState, bot);
      setGameState(updatedGame);

      addEvent({
        type: 'player_joined',
        playerId: bot.id,
        message: `Бот ${botName} добавлен в игру`
      });
    } catch (error) {
      addEvent({
        type: 'error',
        message: error instanceof Error ? error.message : 'Ошибка добавления бота'
      });
    }
  }, [gameState, addEvent]);

  // Начало игры
  const startGame = useCallback(() => {
    if (!gameState) {
      throw new Error('Игра не создана');
    }

    if (gameState.players.length < 2) {
      // Добавляем бота если недостаточно игроков
      addBot('ИИ Противник');
      return;
    }

    try {
      let updatedGame = dealCards(gameState);
      updatedGame = drawCards(updatedGame);
      setGameState(updatedGame);
      
      addEvent({
        type: 'cards_dealt',
        message: 'Карты розданы, игра началась!'
      });
    } catch (error) {
      addEvent({
        type: 'error',
        message: error instanceof Error ? error.message : 'Ошибка начала игры'
      });
    }
  }, [gameState, addBot, addEvent]);

  // Выполнение игрового действия
  const playAction = useCallback((action: GameAction) => {
    if (!gameState) {
      throw new Error('Игра не создана');
    }

    try {
      let updatedGame = executeAction(gameState, action);
      
      // Добираем карты после хода
      if (action.type === 'finish_turn' || action.type === 'take_cards') {
        updatedGame = drawCards(updatedGame);
      }
      
      // Проверяем окончание игры
      updatedGame = checkGameEnd(updatedGame);
      
      setGameState(updatedGame);
      
      addEvent({
        type: 'card_played',
        playerId: action.playerId,
        message: `Игрок выполнил действие: ${action.type}`,
        data: action
      });

      if (updatedGame.phase === 'finished') {
        addEvent({
          type: 'game_finished',
          message: updatedGame.winner 
            ? `Игра окончена! Победитель: ${updatedGame.players.find(p => p.id === updatedGame.winner)?.name}`
            : 'Игра окончена ничьей'
        });
      }
    } catch (error) {
      addEvent({
        type: 'error',
        playerId: action.playerId,
        message: error instanceof Error ? error.message : 'Ошибка выполнения действия'
      });
    }
  }, [gameState, addEvent]);

  // Проверка, можно ли сыграть карту
  const canPlayCard = useCallback((card: Card): boolean => {
    if (!gameState || !currentPlayer) return false;
    
    // Проверяем, есть ли карта у игрока
    const hasCard = currentPlayer.cards.some(c => c.id === card.id);
    if (!hasCard) return false;
    
    // Проверяем, наш ли ход
    if (!isMyTurn) return false;
    
    // Дополнительные проверки в зависимости от фазы игры
    switch (gameState.phase) {
      case 'attacking':
        return gameState.currentPlayerIndex === gameState.players.findIndex(p => p.id === currentPlayerId);
      case 'defending':
        return gameState.defendingPlayerIndex === gameState.players.findIndex(p => p.id === currentPlayerId);
      case 'adding':
        return gameState.currentPlayerIndex !== gameState.defendingPlayerIndex;
      default:
        return false;
    }
  }, [gameState, currentPlayerId]);

  // Получение доступных действий
  const getValidActions = useCallback((): GameAction[] => {
    if (!gameState) return [];

    const player = gameState.players.find(p => p.id === currentPlayerId);
    if (!player) return [];

    const playerIndex = gameState.players.findIndex(p => p.id === currentPlayerId);
    const isPlayerTurn = gameState.players[gameState.currentPlayerIndex]?.id === currentPlayerId;

    if (!isPlayerTurn) return [];

    const actions: GameAction[] = [];

    switch (gameState.phase) {
      case 'attacking':
        // Можем атаковать любой картой (если стол пуст) или подходящей картой
        player.cards.forEach(card => {
          actions.push({
            type: 'attack',
            playerId: currentPlayerId,
            card
          });
        });

        // Можем пропустить ход
        actions.push({
          type: 'pass_turn',
          playerId: currentPlayerId
        });
        break;

      case 'defending':
        if (playerIndex === gameState.defendingPlayerIndex) {
          // Можем защищаться подходящими картами
          gameState.table.forEach((tableCard, position) => {
            if (!tableCard.defendCard) {
              player.cards.forEach(card => {
                actions.push({
                  type: 'defend',
                  playerId: currentPlayerId,
                  card,
                  targetPosition: position
                });
              });
            }
          });

          // Можем взять карты
          actions.push({
            type: 'take_cards',
            playerId: currentPlayerId
          });
        }
        break;

      case 'adding':
        if (playerIndex !== gameState.defendingPlayerIndex) {
          // Можем подкидывать подходящие карты
          player.cards.forEach(card => {
            actions.push({
              type: 'add_card',
              playerId: currentPlayerId,
              card
            });
          });
        }

        // Можем завершить ход
        actions.push({
          type: 'finish_turn',
          playerId: currentPlayerId
        });
        break;
    }

    return actions;
  }, [gameState, currentPlayerId]);

  // Сброс игры
  const resetGame = useCallback(() => {
    setGameState(null);
    setGameEvents([]);
  }, []);

  // Вычисляемые значения уже объявлены выше

  // Автоматические действия ботов
  useEffect(() => {
    if (!gameState || gameState.phase === 'finished') return;

    const currentGamePlayer = gameState.players[gameState.currentPlayerIndex];
    if (!currentGamePlayer?.isBot) return;

    // Простая логика бота - случайное действие через небольшую задержку
    const timer = setTimeout(() => {
      const validActions = getValidActions();
      if (validActions.length > 0) {
        const randomAction = validActions[Math.floor(Math.random() * validActions.length)];
        playAction(randomAction);
      }
    }, 1000 + Math.random() * 2000); // 1-3 секунды задержка

    return () => clearTimeout(timer);
  }, [gameState, getValidActions, playAction]);

  return {
    gameState,
    currentPlayer,
    isMyTurn,
    gameEvents,

    createNewGame,
    joinGame,
    startGame,
    playAction,

    canPlayCard,
    getValidActions,
    resetGame
  };
};
