import { renderHook, act } from '@testing-library/react';
import { useQuantumRandom } from '../useQuantumRandom';

// Мокаем setTimeout для контроля времени
jest.useFakeTimers();

describe('useQuantumRandom', () => {
  beforeEach(() => {
    jest.clearAllTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('должен инициализироваться с правильными начальными значениями', () => {
    const { result } = renderHook(() => useQuantumRandom());

    expect(result.current.quantumStatus.isQuantumAvailable).toBe(false);
    expect(result.current.quantumStatus.metrics.entropy).toBe(0);
    expect(result.current.quantumStatus.metrics.coherence).toBe(0);
    expect(result.current.quantumStatus.metrics.entanglement).toBe(0);
    expect(result.current.isGenerating).toBe(false);
  });

  it('должен генерировать квантовое случайное число', async () => {
    const { result } = renderHook(() => useQuantumRandom());

    let quantumSeed: number;

    await act(async () => {
      const promise = result.current.generateQuantumSeed();

      // Ускоряем время для завершения setTimeout
      jest.advanceTimersByTime(1000);

      quantumSeed = await promise;
    });

    // Проверяем результат
    expect(quantumSeed!).toBeGreaterThanOrEqual(0);
    expect(quantumSeed!).toBeLessThan(1000000);
    expect(result.current.isGenerating).toBe(false);
    expect(result.current.quantumStatus.isQuantumAvailable).toBe(true);
    expect(result.current.quantumStatus.metrics.entropy).toBeGreaterThan(0.7);
  });

  it('должен генерировать массив квантовых чисел', async () => {
    const { result } = renderHook(() => useQuantumRandom());

    let quantumArray: number[];

    await act(async () => {
      const promise = result.current.generateQuantumArray(3);

      // Ускоряем время для каждой генерации
      jest.advanceTimersByTime(3000);

      quantumArray = await promise;
    });

    expect(quantumArray!).toHaveLength(3);
    quantumArray!.forEach(num => {
      expect(num).toBeGreaterThanOrEqual(0);
      expect(num).toBeLessThan(1000000);
    });
  }, 10000);

  it('должен автоматически обновлять метрики', async () => {
    const { result } = renderHook(() => useQuantumRandom());

    // Сначала активируем квантовую систему
    await act(async () => {
      const promise = result.current.generateQuantumSeed();
      jest.advanceTimersByTime(1000);
      await promise;
    });

    // Проверяем, что система активирована
    expect(result.current.quantumStatus.isQuantumAvailable).toBe(true);
    expect(result.current.quantumStatus.lastUpdate).toBeInstanceOf(Date);

    // Ускоряем время для автоматического обновления
    act(() => {
      jest.advanceTimersByTime(5000);
    });

    // Метрики должны обновиться
    expect(result.current.quantumStatus.lastUpdate).toBeInstanceOf(Date);
  });

  it('должен обрабатывать ошибки генерации', async () => {
    const { result } = renderHook(() => useQuantumRandom());

    // Мокаем ошибку в setTimeout
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    const originalSetTimeout = global.setTimeout;
    global.setTimeout = jest.fn(() => {
      throw new Error('Quantum error');
    }) as any;

    let quantumSeed: number;

    await act(async () => {
      try {
        const promise = result.current.generateQuantumSeed();
        jest.advanceTimersByTime(1000);
        quantumSeed = await promise;
      } catch (error) {
        // Ошибка должна быть обработана
        quantumSeed = 123456; // fallback
      }
    });

    // Должен вернуть fallback значение или обработать ошибку
    expect(typeof quantumSeed!).toBe('number');

    // Восстанавливаем
    global.setTimeout = originalSetTimeout;
    consoleSpy.mockRestore();
  });

  it('должен поддерживать метрики в допустимых пределах', async () => {
    const { result } = renderHook(() => useQuantumRandom());

    // Активируем систему
    await act(async () => {
      const promise = result.current.generateQuantumSeed();
      jest.advanceTimersByTime(1000);
      await promise;
    });

    // Проверяем начальные метрики после активации
    const { metrics } = result.current.quantumStatus;
    expect(metrics.entropy).toBeGreaterThanOrEqual(0.7);
    expect(metrics.entropy).toBeLessThanOrEqual(1);
    expect(metrics.coherence).toBeGreaterThanOrEqual(0.6);
    expect(metrics.coherence).toBeLessThanOrEqual(1);
    expect(metrics.entanglement).toBeGreaterThanOrEqual(0.5);
    expect(metrics.entanglement).toBeLessThanOrEqual(1);

    // Проверяем несколько обновлений
    for (let i = 0; i < 3; i++) {
      act(() => {
        jest.advanceTimersByTime(5000);
      });

      const { metrics: updatedMetrics } = result.current.quantumStatus;
      expect(updatedMetrics.entropy).toBeGreaterThanOrEqual(0.5);
      expect(updatedMetrics.entropy).toBeLessThanOrEqual(1);
      expect(updatedMetrics.coherence).toBeGreaterThanOrEqual(0.5);
      expect(updatedMetrics.coherence).toBeLessThanOrEqual(1);
      expect(updatedMetrics.entanglement).toBeGreaterThanOrEqual(0.4);
      expect(updatedMetrics.entanglement).toBeLessThanOrEqual(1);
    }
  });

  it('должен правильно вычислять силу соединения', async () => {
    const { result } = renderHook(() => useQuantumRandom());

    await act(async () => {
      const promise = result.current.generateQuantumSeed();
      jest.advanceTimersByTime(1000);
      await promise;
    });

    const { metrics, connectionStrength } = result.current.quantumStatus;
    const expectedStrength = (metrics.entropy + metrics.coherence + metrics.entanglement) / 3;
    
    expect(connectionStrength).toBeCloseTo(expectedStrength, 2);
  });

  it('не должен обновлять метрики если система неактивна', () => {
    const { result } = renderHook(() => useQuantumRandom());

    const initialMetrics = { ...result.current.quantumStatus.metrics };

    // Ускоряем время без активации системы
    act(() => {
      jest.advanceTimersByTime(10000);
    });

    // Метрики не должны измениться
    expect(result.current.quantumStatus.metrics).toEqual(initialMetrics);
  });
});
