import { renderHook, act } from '@testing-library/react';
import { useEmotionalAI } from '../useEmotionalAI';

jest.useFakeTimers();

describe('useEmotionalAI', () => {
  beforeEach(() => {
    jest.clearAllTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('должен инициализироваться с правильными начальными значениями', () => {
    const { result } = renderHook(() => useEmotionalAI());

    expect(result.current.emotionalState.metrics.happiness).toBe(0.5);
    expect(result.current.emotionalState.metrics.focus).toBe(0.5);
    expect(result.current.emotionalState.metrics.confidence).toBe(0.5);
    expect(result.current.emotionalState.metrics.stress).toBe(0.5);
    expect(result.current.emotionalState.metrics.motivation).toBe(0.5);
    expect(result.current.emotionalState.metrics.fatigue).toBe(0.5);
    expect(result.current.emotionalState.overallMood).toBe('neutral');
    expect(result.current.emotionalState.isAnalyzing).toBe(false);
    expect(result.current.emotionalState.recommendations).toEqual([]);
  });

  it('должен анализировать пользователя и обновлять метрики', async () => {
    const { result } = renderHook(() => useEmotionalAI());

    await act(async () => {
      const promise = result.current.analyzeUser();
      
      // Проверяем, что анализ начался
      expect(result.current.emotionalState.isAnalyzing).toBe(true);
      
      // Ускоряем время
      jest.advanceTimersByTime(2000);
      
      await promise;
    });

    // Проверяем результат
    expect(result.current.emotionalState.isAnalyzing).toBe(false);
    expect(result.current.emotionalState.lastAnalysis).toBeInstanceOf(Date);
    
    // Метрики должны быть в допустимых пределах
    const { metrics } = result.current.emotionalState;
    Object.values(metrics).forEach(value => {
      expect(value).toBeGreaterThanOrEqual(0);
      expect(value).toBeLessThanOrEqual(1);
    });
  });

  it('должен правильно определять настроение', async () => {
    const { result } = renderHook(() => useEmotionalAI());

    // Мокаем Math.random для получения высоких позитивных метрик
    const originalRandom = Math.random;
    let callCount = 0;
    Math.random = jest.fn(() => {
      callCount++;
      // Возвращаем значения для создания отличного настроения
      if (callCount <= 6) {
        return 0.5; // Это даст нам высокие позитивные и низкие негативные метрики
      }
      return originalRandom();
    });

    await act(async () => {
      const promise = result.current.analyzeUser();
      jest.advanceTimersByTime(2000);
      await promise;
    });

    // Проверяем, что настроение определилось правильно
    expect(['excellent', 'good', 'neutral']).toContain(result.current.emotionalState.overallMood);

    Math.random = originalRandom;
  });

  it('должен генерировать рекомендации на основе метрик', async () => {
    const { result } = renderHook(() => useEmotionalAI());

    await act(async () => {
      const promise = result.current.analyzeUser();
      jest.advanceTimersByTime(2000);
      await promise;
    });

    // Проверяем, что рекомендации генерируются
    expect(Array.isArray(result.current.emotionalState.recommendations)).toBe(true);
    // Рекомендации могут быть разными в зависимости от случайных метрик
    expect(result.current.emotionalState.recommendations.length).toBeGreaterThanOrEqual(0);
  });

  it('должен обновлять игровое поведение на основе эмоций', async () => {
    const { result } = renderHook(() => useEmotionalAI());

    const initialBehavior = { ...result.current.gameBehavior };

    await act(async () => {
      const promise = result.current.analyzeUser();
      jest.advanceTimersByTime(2000);
      await promise;
    });

    // Поведение должно обновиться
    expect(result.current.gameBehavior).not.toEqual(initialBehavior);
    expect(typeof result.current.gameBehavior.decisionTime).toBe('number');
    expect(result.current.gameBehavior.decisionTime).toBeGreaterThan(0);
    expect(result.current.gameBehavior.riskTolerance).toBeGreaterThanOrEqual(0);
    expect(result.current.gameBehavior.riskTolerance).toBeLessThanOrEqual(1);
    expect(result.current.gameBehavior.aggressiveness).toBeGreaterThanOrEqual(0);
    expect(result.current.gameBehavior.aggressiveness).toBeLessThanOrEqual(1);
    expect(result.current.gameBehavior.consistency).toBeGreaterThanOrEqual(0);
    expect(result.current.gameBehavior.consistency).toBeLessThanOrEqual(1);
  });

  it('должен автоматически обновлять метрики', () => {
    const { result } = renderHook(() => useEmotionalAI());

    const initialMetrics = { ...result.current.emotionalState.metrics };

    // Ускоряем время для автоматического обновления (несколько циклов)
    act(() => {
      jest.advanceTimersByTime(10000);
    });

    act(() => {
      jest.advanceTimersByTime(10000);
    });

    // Метрики должны немного измениться
    const updatedMetrics = result.current.emotionalState.metrics;
    const hasChanged = Object.keys(initialMetrics).some(key =>
      Math.abs(initialMetrics[key as keyof typeof initialMetrics] -
               updatedMetrics[key as keyof typeof updatedMetrics]) > 0.001
    );

    // Проверяем, что метрики остаются в допустимых пределах
    Object.values(updatedMetrics).forEach(value => {
      expect(value).toBeGreaterThanOrEqual(0);
      expect(value).toBeLessThanOrEqual(1);
    });

    expect(hasChanged).toBe(true);
  });

  it('должен предоставлять эмоциональные инсайты', async () => {
    const { result } = renderHook(() => useEmotionalAI());

    await act(async () => {
      const promise = result.current.analyzeUser();
      jest.advanceTimersByTime(2000);
      await promise;
    });

    const insight = result.current.getEmotionalInsight();

    expect(insight).toHaveProperty('primaryEmotion');
    expect(insight).toHaveProperty('moodDescription');
    expect(insight).toHaveProperty('playabilityScore');
    expect(insight).toHaveProperty('suggestedGameType');

    expect(typeof insight.primaryEmotion).toBe('string');
    expect(typeof insight.moodDescription).toBe('string');
    expect(typeof insight.playabilityScore).toBe('number');
    expect(insight.playabilityScore).toBeGreaterThanOrEqual(0);
    expect(insight.playabilityScore).toBeLessThanOrEqual(1);
    expect(typeof insight.suggestedGameType).toBe('string');
  });

  it('должен правильно рассчитывать время принятия решений', async () => {
    const { result } = renderHook(() => useEmotionalAI());

    // Мокаем высокий фокус
    const originalRandom = Math.random;
    Math.random = jest.fn()
      .mockReturnValueOnce(0.5) // happiness
      .mockReturnValueOnce(0.9) // focus - высокий
      .mockReturnValueOnce(0.5) // confidence
      .mockReturnValueOnce(0.5) // stress
      .mockReturnValueOnce(0.5) // motivation
      .mockReturnValueOnce(0.5); // fatigue

    await act(async () => {
      const promise = result.current.analyzeUser();
      jest.advanceTimersByTime(2000);
      await promise;
    });

    // При высоком фокусе время решения должно быть меньше
    expect(result.current.gameBehavior.decisionTime).toBeLessThan(5000);

    Math.random = originalRandom;
  });

  it('должен обрабатывать ошибки анализа', async () => {
    const { result } = renderHook(() => useEmotionalAI());

    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    // Мокаем ошибку
    const originalSetTimeout = global.setTimeout;
    global.setTimeout = jest.fn(() => {
      throw new Error('Analysis error');
    }) as any;

    await act(async () => {
      try {
        const promise = result.current.analyzeUser();
        jest.advanceTimersByTime(2000);
        await promise;
      } catch (error) {
        // Ошибка должна быть обработана
      }
    });

    expect(result.current.emotionalState.isAnalyzing).toBe(false);
    expect(consoleSpy).toHaveBeenCalledWith('Emotional analysis error:', expect.any(Error));

    // Восстанавливаем
    global.setTimeout = originalSetTimeout;
    consoleSpy.mockRestore();
  });

  it('не должен обновлять метрики во время анализа', async () => {
    const { result } = renderHook(() => useEmotionalAI());

    // Начинаем анализ
    act(() => {
      result.current.analyzeUser();
    });

    const metricsBeforeUpdate = { ...result.current.emotionalState.metrics };

    // Пытаемся обновить метрики во время анализа
    act(() => {
      jest.advanceTimersByTime(10000);
    });

    // Метрики не должны измениться во время анализа
    expect(result.current.emotionalState.metrics).toEqual(metricsBeforeUpdate);

    // Завершаем анализ
    act(() => {
      jest.advanceTimersByTime(2000);
    });
  });
});
