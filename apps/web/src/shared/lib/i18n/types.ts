/**
 * Типы для строго типизированных переводов
 */

// Общие переводы
export interface CommonTranslations {
  welcome: string;
  play_now: string;
  join_game: string;
  tournaments: string;
  tournaments_desc: string;
  learning: string;
  learning_desc: string;
  copyright: string;
  language: {
    ru: string;
    en: string;
  };
}

// Переводы для игры
export interface GameTranslations {
  durak: {
    rules: {
      title: string;
      description: string;
    };
    game: {
      your_turn: string;
      opponent_turn: string;
      take_cards: string;
      pass: string;
      game_over: string;
      you_win: string;
      you_lose: string;
    };
  };
}

// Переводы для авторизации
export interface AuthTranslations {
  login: string;
  register: string;
  email: string;
  password: string;
  confirm_password: string;
  forgot_password: string;
  submit: string;
  logout: string;
}

// Переводы для профиля
export interface ProfileTranslations {
  profile: string;
  edit_profile: string;
  save: string;
  cancel: string;
  username: string;
  email: string;
  avatar: string;
  change_password: string;
  statistics: string;
  games_played: string;
  games_won: string;
  win_rate: string;
}

// Переводы для турниров
export interface TournamentTranslations {
  tournaments: string;
  upcoming: string;
  ongoing: string;
  completed: string;
  join: string;
  leave: string;
  participants: string;
  prize_pool: string;
  start_date: string;
  end_date: string;
}

// Объединение всех переводов
export interface Resources {
  common: CommonTranslations;
  game: GameTranslations;
  auth: AuthTranslations;
  profile: ProfileTranslations;
  tournament: TournamentTranslations;
}

// Тип для пространств имен
export type Namespace = keyof Resources;