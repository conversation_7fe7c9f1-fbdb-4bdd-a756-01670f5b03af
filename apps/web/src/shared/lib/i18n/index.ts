import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import Backend from "i18next-http-backend";

// Инициализация i18n с автоматическим определением языка и загрузкой переводов
i18n
  .use(Backend) // загрузка переводов с сервера (в /public/locales/)
  .use(LanguageDetector) // автоматическое определение языка
  .use(initReactI18next) // интеграция с React
  .init({
    fallbackLng: "ru",
    supportedLngs: ["ru", "en"],
    debug: process.env.NODE_ENV === "development",
    
    // Настройки для загрузки переводов
    backend: {
      loadPath: "/locales/{{lng}}/{{ns}}.json",
    },
    
    // Настройки для определения языка
    detection: {
      order: ["localStorage", "cookie", "navigator"],
      caches: ["localStorage", "cookie"],
    },
    
    // Настройки для интерполяции
    interpolation: {
      escapeValue: false,
    },
    
    // Разделение переводов на пространства имен
    ns: ["common", "game", "auth", "profile", "tournament"],
    defaultNS: "common",
  });

export default i18n;

// Хук для удобного использования переводов
export { useTranslation } from "react-i18next";

// Типы для строго типизированных переводов
export type TFunction = ReturnType<typeof useTranslation>["t"];