/**
 * Стили для компонента кнопки
 */

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 0.25rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  outline: none;
  border: 1px solid transparent;
  font-family: "Roboto", "Helvetica", "Arial", sans-serif;
  overflow: hidden;
  user-select: none;
}

/* Варианты кнопок */
.variant-primary {
  background-color: var(--primary-500, #1890ff);
  color: white;
  border-color: var(--primary-500, #1890ff);
}

.variant-primary:hover:not(.disabled) {
  background-color: var(--primary-600, #096dd9);
  border-color: var(--primary-600, #096dd9);
}

.variant-primary:active:not(.disabled) {
  background-color: var(--primary-700, #0050b3);
  border-color: var(--primary-700, #0050b3);
}

.variant-secondary {
  background-color: transparent;
  color: var(--primary-500, #1890ff);
  border-color: var(--primary-500, #1890ff);
}

.variant-secondary:hover:not(.disabled) {
  background-color: var(--primary-50, #e6f7ff);
  border-color: var(--primary-600, #096dd9);
  color: var(--primary-600, #096dd9);
}

.variant-secondary:active:not(.disabled) {
  background-color: var(--primary-100, #bae7ff);
  border-color: var(--primary-700, #0050b3);
  color: var(--primary-700, #0050b3);
}

.variant-tertiary {
  background-color: transparent;
  color: var(--primary-500, #1890ff);
  border-color: transparent;
}

.variant-tertiary:hover:not(.disabled) {
  background-color: var(--primary-50, #e6f7ff);
  color: var(--primary-600, #096dd9);
}

.variant-tertiary:active:not(.disabled) {
  background-color: var(--primary-100, #bae7ff);
  color: var(--primary-700, #0050b3);
}

.variant-danger {
  background-color: var(--error, #f5222d);
  color: white;
  border-color: var(--error, #f5222d);
}

.variant-danger:hover:not(.disabled) {
  background-color: var(--red-dark, #a8071a);
  border-color: var(--red-dark, #a8071a);
}

.variant-danger:active:not(.disabled) {
  background-color: var(--red-dark, #a8071a);
  border-color: var(--red-dark, #a8071a);
  filter: brightness(0.9);
}

.variant-success {
  background-color: var(--success, #52c41a);
  color: white;
  border-color: var(--success, #52c41a);
}

.variant-success:hover:not(.disabled) {
  background-color: var(--green-dark, #237804);
  border-color: var(--green-dark, #237804);
}

.variant-success:active:not(.disabled) {
  background-color: var(--green-dark, #237804);
  border-color: var(--green-dark, #237804);
  filter: brightness(0.9);
}

/* Размеры кнопок */
.size-sm {
  height: 1.5rem;
  padding: 0 0.75rem;
  font-size: 0.75rem;
}

.size-md {
  height: 2rem;
  padding: 0 1rem;
  font-size: 0.875rem;
}

.size-lg {
  height: 2.5rem;
  padding: 0 1.25rem;
  font-size: 1rem;
}

/* Полная ширина */
.fullWidth {
  width: 100%;
}

/* Состояние отключено */
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Состояние загрузки */
.loading {
  cursor: wait;
}

.loadingIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
}

.loadingDot {
  width: 0.25rem;
  height: 0.25rem;
  border-radius: 50%;
  background-color: currentColor;
  margin: 0 0.125rem;
  animation: loadingAnimation 1.4s infinite ease-in-out both;
}

.loadingDot:nth-child(1) {
  animation-delay: -0.32s;
}

.loadingDot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loadingAnimation {
  0%, 80%, 100% {
    transform: scale(0);
  } 
  40% {
    transform: scale(1);
  }
}

/* Иконки */
.startIcon {
  margin-right: 0.5rem;
  display: flex;
  align-items: center;
}

.endIcon {
  margin-left: 0.5rem;
  display: flex;
  align-items: center;
}

.content {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Адаптивность для мобильных устройств */
@media (max-width: 576px) {
  .button {
    font-size: 0.875rem;
  }
  
  .size-sm {
    height: 1.75rem;
    padding: 0 0.875rem;
  }
  
  .size-md {
    height: 2.25rem;
    padding: 0 1.125rem;
  }
  
  .size-lg {
    height: 2.75rem;
    padding: 0 1.375rem;
  }
}