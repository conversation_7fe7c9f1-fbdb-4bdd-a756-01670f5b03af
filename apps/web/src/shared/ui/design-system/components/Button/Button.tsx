/**
 * Компонент кнопки для дизайн-системы "Козырь Мастер"
 * 
 * Универсальный компонент кнопки с поддержкой различных вариантов,
 * размеров и состояний. Использует токены дизайн-системы и поддерживает
 * светлую и темную темы.
 */

import React, { ButtonHTMLAttributes, ReactNode } from 'react';
import { useTheme } from '../../ThemeProvider';
import styles from './Button.module.css';

// Типы вариантов кнопки
export type ButtonVariant = 'primary' | 'secondary' | 'tertiary' | 'danger' | 'success';

// Типы размеров кнопки
export type ButtonSize = 'sm' | 'md' | 'lg';

// Интерфейс пропсов кнопки
export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  /** Вариант кнопки */
  variant?: ButtonVariant;
  /** Размер кнопки */
  size?: ButtonSize;
  /** Содержимое кнопки */
  children: ReactNode;
  /** Полная ширина */
  fullWidth?: boolean;
  /** Иконка слева */
  startIcon?: ReactNode;
  /** Иконка справа */
  endIcon?: ReactNode;
  /** Состояние загрузки */
  loading?: boolean;
}

/**
 * Компонент кнопки
 * 
 * @param {ButtonProps} props - Пропсы компонента
 * @returns {JSX.Element} Компонент кнопки
 */
export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  children,
  fullWidth = false,
  startIcon,
  endIcon,
  loading = false,
  disabled = false,
  className = '',
  ...restProps
}) => {
  const { theme } = useTheme();
  
  // Формирование классов
  const buttonClasses = [
    styles.button,
    styles[`variant-${variant}`],
    styles[`size-${size}`],
    fullWidth ? styles.fullWidth : '',
    loading ? styles.loading : '',
    disabled ? styles.disabled : '',
    className,
  ].filter(Boolean).join(' ');
  
  return (
    <button
      className={buttonClasses}
      disabled={disabled || loading}
      {...restProps}
    >
      {loading && (
        <span className={styles.loadingIndicator}>
          <span className={styles.loadingDot} />
          <span className={styles.loadingDot} />
          <span className={styles.loadingDot} />
        </span>
      )}
      
      {!loading && startIcon && (
        <span className={styles.startIcon}>{startIcon}</span>
      )}
      
      <span className={styles.content}>{children}</span>
      
      {!loading && endIcon && (
        <span className={styles.endIcon}>{endIcon}</span>
      )}
    </button>
  );
};

export default Button;