/**
 * Компонент карточки для дизайн-системы "Козырь Мастер"
 * 
 * Универсальный компонент для отображения контента в виде карточки.
 * Может использоваться как для игральных карт, так и для информационных блоков.
 */

import React, { ReactNode } from 'react';
import { useTheme } from '../../ThemeProvider';
import styles from './Card.module.css';

// Типы вариантов карточки
export type CardVariant = 'default' | 'outlined' | 'elevated' | 'playing';

// Интерфейс пропсов карточки
export interface CardProps {
  /** Вариант карточки */
  variant?: CardVariant;
  /** Содержимое карточки */
  children: ReactNode;
  /** Дополнительные классы */
  className?: string;
  /** Обработчик клика */
  onClick?: () => void;
  /** Заголовок карточки */
  title?: ReactNode;
  /** Нижний колонтитул карточки */
  footer?: ReactNode;
  /** Масть (для игральных карт) */
  suit?: 'hearts' | 'diamonds' | 'clubs' | 'spades';
  /** Ранг (для игральных карт) */
  rank?: string;
  /** Ширина */
  width?: string | number;
  /** Высота */
  height?: string | number;
}

/**
 * Компонент карточки
 * 
 * @param {CardProps} props - Пропсы компонента
 * @returns {JSX.Element} Компонент карточки
 */
export const Card: React.FC<CardProps> = ({
  variant = 'default',
  children,
  className = '',
  onClick,
  title,
  footer,
  suit,
  rank,
  width,
  height,
}) => {
  const { theme } = useTheme();
  
  // Формирование классов
  const cardClasses = [
    styles.card,
    styles[`variant-${variant}`],
    suit ? styles[`suit-${suit}`] : '',
    onClick ? styles.clickable : '',
    className,
  ].filter(Boolean).join(' ');
  
  // Стили для размеров
  const cardStyle = {
    width: width ? (typeof width === 'number' ? `${width}px` : width) : undefined,
    height: height ? (typeof height === 'number' ? `${height}px` : height) : undefined,
  };
  
  // Рендер игральной карты
  if (variant === 'playing' && suit && rank) {
    return (
      <div 
        className={cardClasses} 
        style={cardStyle}
        onClick={onClick}
      >
        <div className={styles.cardCorner}>
          <div className={styles.cardRank}>{rank}</div>
          <div className={styles.cardSuit}>
            {suit === 'hearts' && '♥'}
            {suit === 'diamonds' && '♦'}
            {suit === 'clubs' && '♣'}
            {suit === 'spades' && '♠'}
          </div>
        </div>
        
        <div className={styles.cardCenter}>
          {children || (
            <div className={styles.cardSuitLarge}>
              {suit === 'hearts' && '♥'}
              {suit === 'diamonds' && '♦'}
              {suit === 'clubs' && '♣'}
              {suit === 'spades' && '♠'}
            </div>
          )}
        </div>
        
        <div className={`${styles.cardCorner} ${styles.cardCornerBottom}`}>
          <div className={styles.cardRank}>{rank}</div>
          <div className={styles.cardSuit}>
            {suit === 'hearts' && '♥'}
            {suit === 'diamonds' && '♦'}
            {suit === 'clubs' && '♣'}
            {suit === 'spades' && '♠'}
          </div>
        </div>
      </div>
    );
  }
  
  // Рендер обычной карточки
  return (
    <div 
      className={cardClasses} 
      style={cardStyle}
      onClick={onClick}
    >
      {title && <div className={styles.cardHeader}>{title}</div>}
      <div className={styles.cardContent}>{children}</div>
      {footer && <div className={styles.cardFooter}>{footer}</div>}
    </div>
  );
};

export default Card;