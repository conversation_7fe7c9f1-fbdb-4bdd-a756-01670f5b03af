/**
 * Стили для компонента карточки
 */

.card {
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.2s ease-in-out;
  box-sizing: border-box;
}

/* Варианты карточек */
.variant-default {
  background-color: var(--background-card, white);
  border: 1px solid var(--border-primary, #d9d9d9);
}

.variant-outlined {
  background-color: transparent;
  border: 1px solid var(--border-primary, #d9d9d9);
}

.variant-elevated {
  background-color: var(--background-card, white);
  border: none;
  box-shadow: var(--shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06));
}

.variant-playing {
  background-color: white;
  border: 1px solid #d9d9d9;
  border-radius: 0.5rem;
  width: 2.5rem;
  height: 3.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0.25rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.variant-playing-large {
  background-color: white;
  border: 1px solid #d9d9d9;
  border-radius: 0.75rem;
  width: 4rem;
  height: 5.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0.35rem;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.variant-playing-small {
  background-color: white;
  border: 1px solid #d9d9d9;
  border-radius: 0.4rem;
  width: 2rem;
  height: 2.8rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0.2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.variant-playing-facedown {
  background-color: var(--card-back-color, #1976d2);
  background-image: var(--card-back-pattern, repeating-linear-gradient(45deg, #1565c0 0, #1565c0 5px, #1976d2 5px, #1976d2 10px));
  border: 1px solid #d9d9d9;
  border-radius: 0.5rem;
  width: 2.5rem;
  height: 3.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Масти карт */
.suit-hearts .cardSuit,
.suit-hearts .cardSuitLarge,
.suit-diamonds .cardSuit,
.suit-diamonds .cardSuitLarge {
  color: var(--suits-hearts, #e53935);
}

.suit-clubs .cardSuit,
.suit-clubs .cardSuitLarge,
.suit-spades .cardSuit,
.suit-spades .cardSuitLarge {
  color: var(--suits-clubs, #2e2e2e);
}

/* Высококонтрастный режим */
.highContrast .suit-hearts .cardSuit,
.highContrast .suit-hearts .cardSuitLarge,
.highContrast .suit-diamonds .cardSuit,
.highContrast .suit-diamonds .cardSuitLarge {
  color: var(--high-contrast-red, #ff0000);
  font-weight: bold;
}

.highContrast .suit-clubs .cardSuit,
.highContrast .suit-clubs .cardSuitLarge,
.highContrast .suit-spades .cardSuit,
.highContrast .suit-spades .cardSuitLarge {
  color: var(--high-contrast-black, #000000);
  font-weight: bold;
}

.highContrast.variant-playing {
  border: 2px solid #000000;
}

/* Кликабельная карточка */
.clickable {
  cursor: pointer;
}

.clickable:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06));
}

.clickable:active {
  transform: translateY(0);
}

/* Структура обычной карточки */
.cardHeader {
  padding: 1rem;
  border-bottom: 1px solid var(--border-secondary, #f0f0f0);
  font-weight: 500;
}

.cardContent {
  padding: 1rem;
}

.cardFooter {
  padding: 1rem;
  border-top: 1px solid var(--border-secondary, #f0f0f0);
  background-color: var(--background-secondary, #f5f5f5);
}

/* Структура игральной карты */
.cardCorner {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.cardCornerBottom {
  transform: rotate(180deg);
  align-self: flex-end;
}

.cardRank {
  font-size: 0.75rem;
  font-weight: bold;
  line-height: 1;
}

.cardSuit {
  font-size: 0.75rem;
  line-height: 1;
}

.cardCenter {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cardSuitLarge {
  font-size: 1.5rem;
}

/* Адаптивность для мобильных устройств */
@media (max-width: 576px) {
  .cardHeader,
  .cardContent,
  .cardFooter {
    padding: 0.75rem;
  }
  
  .variant-playing {
    width: 2rem;
    height: 2.8rem;
    padding: 0.2rem;
  }
  
  .variant-playing-large {
    width: 3rem;
    height: 4.2rem;
    padding: 0.25rem;
  }
  
  .variant-playing-small {
    width: 1.5rem;
    height: 2.1rem;
    padding: 0.15rem;
  }
  
  .variant-playing-facedown {
    width: 2rem;
    height: 2.8rem;
  }
  
  .cardRank {
    font-size: 0.6rem;
  }
  
  .cardSuit {
    font-size: 0.6rem;
  }
  
  .cardSuitLarge {
    font-size: 1.2rem;
  }
  
  .cardSelected {
    animation: cardSelected 0.3s ease-out forwards;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  }
}

/* Адаптивность для планшетов */
@media (min-width: 577px) and (max-width: 992px) {
  .cardHeader,
  .cardContent,
  .cardFooter {
    padding: 0.85rem;
  }
  
  .variant-playing-large {
    width: 3.5rem;
    height: 4.8rem;
    padding: 0.3rem;
  }
}

/* Анимации */
@keyframes cardFlip {
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(180deg);
  }
}

@keyframes cardAppear {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes cardDisappear {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

@keyframes cardSelected {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(-5px);
  }
}

@keyframes cardShake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

.cardFlip {
  animation: cardFlip 0.5s ease-in-out;
}

.cardAppear {
  animation: cardAppear 0.3s ease-out forwards;
}

.cardDisappear {
  animation: cardDisappear 0.3s ease-in forwards;
}

.cardSelected {
  animation: cardSelected 0.3s ease-out forwards;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.cardShake {
  animation: cardShake 0.5s ease-in-out;
}