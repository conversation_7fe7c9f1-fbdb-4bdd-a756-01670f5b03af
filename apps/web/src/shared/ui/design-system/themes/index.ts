/**
 * Темы дизайн-системы "Козырь Мастер"
 * 
 * Экспортирует светлую и темную темы, а также функциональность
 * для работы с темами (переключение, получение текущей темы).
 */

import lightTheme from './light';
import darkTheme from './dark';

// Тип для тем
export type ThemeName = 'light' | 'dark';

// Интерфейс для темы
export interface Theme {
  name: ThemeName;
  [key: string]: any;
}

// Объект со всеми темами
export const themes: Record<ThemeName, Theme> = {
  light: lightTheme,
  dark: darkTheme,
};

// Получение темы по имени
export const getTheme = (themeName: ThemeName): Theme => {
  return themes[themeName] || lightTheme;
};

// Получение противоположной темы
export const getOppositeTheme = (themeName: ThemeName): Theme => {
  return themeName === 'light' ? darkTheme : lightTheme;
};

// Экспорт отдельных тем
export {
  lightTheme,
  darkTheme,
};

export default themes;