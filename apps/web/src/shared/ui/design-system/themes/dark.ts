/**
 * Темная тема дизайн-системы "Козырь Мастер"
 * 
 * Определяет цветовую схему и другие параметры для темной темы приложения.
 * Использует токены дизайн-системы для обеспечения единообразия.
 */

import { colors } from '../tokens/colors';

export const darkTheme = {
  name: 'dark',
  
  // Цвета фона
  background: {
    primary: colors.neutral.gray[900],
    secondary: colors.neutral.gray[800],
    tertiary: colors.neutral.gray[700],
    accent: colors.primary[900],
    card: colors.neutral.gray[800],
    input: colors.neutral.gray[700],
    button: {
      primary: colors.primary[500],
      secondary: colors.neutral.gray[700],
      tertiary: 'transparent',
      danger: colors.accent.red.base,
      success: colors.accent.green.base,
    },
  },
  
  // Цвета текста
  text: {
    primary: colors.neutral.gray[100],
    secondary: colors.neutral.gray[300],
    tertiary: colors.neutral.gray[400],
    disabled: colors.neutral.gray[600],
    inverse: colors.neutral.gray[900],
    accent: colors.primary[300],
    error: colors.accent.red.light,
    button: {
      primary: colors.neutral.white,
      secondary: colors.primary[300],
      tertiary: colors.primary[300],
      danger: colors.neutral.white,
      success: colors.neutral.white,
    },
  },
  
  // Цвета границ
  border: {
    primary: colors.neutral.gray[600],
    secondary: colors.neutral.gray[700],
    tertiary: colors.primary[700],
    focus: colors.primary[400],
    error: colors.accent.red.light,
    input: colors.neutral.gray[600],
    button: {
      primary: colors.primary[500],
      secondary: colors.primary[700],
      tertiary: 'transparent',
      danger: colors.accent.red.base,
      success: colors.accent.green.base,
    },
  },
  
  // Цвета иконок
  icon: {
    primary: colors.neutral.gray[100],
    secondary: colors.neutral.gray[300],
    tertiary: colors.neutral.gray[400],
    accent: colors.primary[300],
    disabled: colors.neutral.gray[600],
  },
  
  // Цвета состояний
  state: {
    hover: {
      background: {
        primary: colors.primary[600],
        secondary: colors.neutral.gray[600],
        tertiary: colors.neutral.gray[800],
      },
      border: {
        primary: colors.primary[400],
        secondary: colors.primary[400],
        tertiary: colors.primary[600],
      },
    },
    active: {
      background: {
        primary: colors.primary[700],
        secondary: colors.neutral.gray[500],
        tertiary: colors.neutral.gray[700],
      },
      border: {
        primary: colors.primary[300],
        secondary: colors.primary[300],
        tertiary: colors.primary[500],
      },
    },
    disabled: {
      background: {
        primary: colors.neutral.gray[800],
        secondary: colors.neutral.gray[900],
        tertiary: 'transparent',
      },
      text: colors.neutral.gray[600],
      border: colors.neutral.gray[700],
    },
  },
  
  // Цвета для карточных мастей
  suits: {
    hearts: colors.accent.red.light,
    diamonds: colors.accent.red.light,
    clubs: colors.neutral.gray[300],
    spades: colors.neutral.gray[300],
  },
  
  // Тени
  shadows: {
    sm: '0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2)',
    md: '0 4px 6px rgba(0, 0, 0, 0.25), 0 2px 4px rgba(0, 0, 0, 0.15)',
    lg: '0 10px 15px rgba(0, 0, 0, 0.2), 0 4px 6px rgba(0, 0, 0, 0.15)',
    xl: '0 20px 25px rgba(0, 0, 0, 0.15), 0 10px 10px rgba(0, 0, 0, 0.1)',
  },
};

export default darkTheme;