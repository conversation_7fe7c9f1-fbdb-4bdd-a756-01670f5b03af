/**
 * Светлая тема дизайн-системы "Козырь Мастер"
 * 
 * Определяет цветовую схему и другие параметры для светлой темы приложения.
 * Использует токены дизайн-системы для обеспечения единообразия.
 */

import { colors } from '../tokens/colors';

export const lightTheme = {
  name: 'light',
  
  // Цвета фона
  background: {
    primary: colors.background.primary,
    secondary: colors.background.secondary,
    tertiary: colors.background.tertiary,
    accent: colors.primary[100],
    card: colors.neutral.white,
    input: colors.neutral.white,
    button: {
      primary: colors.primary[500],
      secondary: colors.neutral.white,
      tertiary: 'transparent',
      danger: colors.accent.red.base,
      success: colors.accent.green.base,
    },
  },
  
  // Цвета текста
  text: {
    primary: colors.text.primary,
    secondary: colors.text.secondary,
    tertiary: colors.neutral.gray[500],
    disabled: colors.text.disabled,
    inverse: colors.text.inverse,
    accent: colors.primary[500],
    error: colors.semantic.error,
    button: {
      primary: colors.neutral.white,
      secondary: colors.primary[500],
      tertiary: colors.primary[500],
      danger: colors.neutral.white,
      success: colors.neutral.white,
    },
  },
  
  // Цвета границ
  border: {
    primary: colors.border.base,
    secondary: colors.border.light,
    tertiary: colors.primary[200],
    focus: colors.primary[500],
    error: colors.semantic.error,
    input: colors.border.base,
    button: {
      primary: colors.primary[500],
      secondary: colors.primary[500],
      tertiary: 'transparent',
      danger: colors.accent.red.base,
      success: colors.accent.green.base,
    },
  },
  
  // Цвета иконок
  icon: {
    primary: colors.text.primary,
    secondary: colors.text.secondary,
    tertiary: colors.neutral.gray[500],
    accent: colors.primary[500],
    disabled: colors.text.disabled,
  },
  
  // Цвета состояний
  state: {
    hover: {
      background: {
        primary: colors.primary[600],
        secondary: colors.primary[50],
        tertiary: colors.primary[50],
      },
      border: {
        primary: colors.primary[600],
        secondary: colors.primary[600],
        tertiary: colors.primary[200],
      },
    },
    active: {
      background: {
        primary: colors.primary[700],
        secondary: colors.primary[100],
        tertiary: colors.primary[100],
      },
      border: {
        primary: colors.primary[700],
        secondary: colors.primary[700],
        tertiary: colors.primary[300],
      },
    },
    disabled: {
      background: {
        primary: colors.neutral.gray[200],
        secondary: colors.neutral.gray[100],
        tertiary: 'transparent',
      },
      text: colors.text.disabled,
      border: colors.neutral.gray[300],
    },
  },
  
  // Цвета для карточных мастей
  suits: {
    hearts: colors.suits.hearts,
    diamonds: colors.suits.diamonds,
    clubs: colors.suits.clubs,
    spades: colors.suits.spades,
  },
  
  // Тени
  shadows: {
    sm: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.08)',
    md: '0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px rgba(0, 0, 0, 0.07), 0 4px 6px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px rgba(0, 0, 0, 0.05), 0 10px 10px rgba(0, 0, 0, 0.04)',
  },
};

export default lightTheme;