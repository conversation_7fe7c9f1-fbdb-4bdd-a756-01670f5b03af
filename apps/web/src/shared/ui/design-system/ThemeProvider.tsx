/**
 * Провайдер темы для дизайн-системы "Козырь Мастер"
 * 
 * Предоставляет контекст темы для всего приложения и функциональность
 * для переключения между светлой и темной темами.
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { lightTheme, darkTheme, Theme, ThemeName } from './themes';

// Интерфейс контекста темы
interface ThemeContextType {
  theme: Theme;
  themeName: ThemeName;
  toggleTheme: () => void;
  setTheme: (themeName: ThemeName) => void;
}

// Создание контекста темы с значениями по умолчанию
const ThemeContext = createContext<ThemeContextType>({
  theme: lightTheme,
  themeName: 'light',
  toggleTheme: () => {},
  setTheme: () => {},
});

// Ключ для хранения темы в localStorage
const THEME_STORAGE_KEY = 'kozyr-master-theme';

interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: ThemeName;
}

/**
 * Провайдер темы для приложения
 * 
 * @param {ReactNode} children - Дочерние компоненты
 * @param {ThemeName} defaultTheme - Тема по умолчанию ('light' или 'dark')
 */
export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultTheme = 'light',
}) => {
  // Инициализация состояния темы
  const [themeName, setThemeName] = useState<ThemeName>(defaultTheme);
  const [theme, setThemeObject] = useState<Theme>(themeName === 'light' ? lightTheme : darkTheme);
  
  // Эффект для загрузки сохраненной темы при монтировании компонента
  useEffect(() => {
    const savedTheme = localStorage.getItem(THEME_STORAGE_KEY) as ThemeName | null;
    if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
      setThemeName(savedTheme);
      setThemeObject(savedTheme === 'light' ? lightTheme : darkTheme);
      applyThemeToDOM(savedTheme);
    } else {
      // Если тема не сохранена, используем предпочтения системы
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      const systemTheme: ThemeName = prefersDark ? 'dark' : 'light';
      setThemeName(systemTheme);
      setThemeObject(systemTheme === 'light' ? lightTheme : darkTheme);
      applyThemeToDOM(systemTheme);
    }
  }, []);
  
  // Функция для применения темы к DOM
  const applyThemeToDOM = (themeName: ThemeName) => {
    // Добавляем класс темы к body
    document.body.classList.remove('theme-light', 'theme-dark');
    document.body.classList.add(`theme-${themeName}`);
    
    // Устанавливаем мета-тег для цвета темы в мобильных браузерах
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute(
        'content',
        themeName === 'light' ? lightTheme.background.primary : darkTheme.background.primary
      );
    }
  };
  
  // Функция для установки темы
  const setTheme = (newThemeName: ThemeName) => {
    setThemeName(newThemeName);
    setThemeObject(newThemeName === 'light' ? lightTheme : darkTheme);
    localStorage.setItem(THEME_STORAGE_KEY, newThemeName);
    applyThemeToDOM(newThemeName);
  };
  
  // Функция для переключения темы
  const toggleTheme = () => {
    const newThemeName: ThemeName = themeName === 'light' ? 'dark' : 'light';
    setTheme(newThemeName);
  };
  
  // Значение контекста
  const contextValue: ThemeContextType = {
    theme,
    themeName,
    toggleTheme,
    setTheme,
  };
  
  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Хук для использования темы в компонентах
 * 
 * @returns {ThemeContextType} Объект с текущей темой и функциями для управления темой
 */
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme должен использоваться внутри ThemeProvider');
  }
  return context;
};

export default ThemeProvider;