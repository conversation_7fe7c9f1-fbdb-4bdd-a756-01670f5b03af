/**
 * Дизайн-система "Козырь Мастер"
 * 
 * Централизованный экспорт всех компонентов дизайн-системы,
 * токенов и тем для обеспечения единообразия интерфейса.
 */

// Экспорт токенов дизайн-системы
import tokens, {
  colors,
  typography,
  spacing,
  breakpoints,
  getColor,
  getTypography,
  getSpacing,
  getBreakpoint,
  createMediaQuery,
} from './tokens';

// Экспорт тем
import themes, {
  lightTheme,
  darkTheme,
  getTheme,
  getOppositeTheme,
  type Theme,
  type ThemeName,
} from './themes';

// Экспорт контекста темы
import ThemeProvider, { useTheme } from './ThemeProvider';

// Экспорт всех компонентов дизайн-системы
import components, { Button } from './components';
import type { ButtonProps, ButtonVariant, ButtonSize } from './components';

// Экспорт токенов
export {
  tokens,
  colors,
  typography,
  spacing,
  breakpoints,
  getColor,
  getTypography,
  getSpacing,
  getBreakpoint,
  createMediaQuery,
};

// Экспорт тем
export {
  themes,
  lightTheme,
  darkTheme,
  getTheme,
  getOppositeTheme,
};

// Экспорт типов
export type {
  Theme,
  ThemeName,
};

// Экспорт контекста темы
export { ThemeProvider, useTheme };

// Экспорт компонентов
export {
  Button,
  components,
};

// Экспорт типов компонентов
export type {
  ButtonProps,
  ButtonVariant,
  ButtonSize,
};

// Экспорт всего как объекта по умолчанию
const designSystem = {
  tokens,
  themes,
  ThemeProvider,
  useTheme,
  components,
};

export default designSystem;