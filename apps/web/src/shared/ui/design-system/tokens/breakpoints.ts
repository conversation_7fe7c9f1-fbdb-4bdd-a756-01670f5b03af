/**
 * Точки перелома для адаптивного дизайна "Козырь Мастер"
 * 
 * Определяет стандартные размеры экрана для создания
 * адаптивного интерфейса, который корректно отображается
 * на различных устройствах.
 */

export const breakpoints = {
  // Базовые точки перелома (в пикселях)
  values: {
    xs: 0,       // Мобильные устройства (портретная ориентация)
    sm: 576,     // Мобильные устройства (альбомная ориентация)
    md: 768,     // Планшеты
    lg: 992,     // Десктопы малого размера
    xl: 1200,    // Десктопы
    xxl: 1600,   // Большие десктопы
  },
  
  // Медиа-запросы для использования в CSS-in-JS
  up: {
    xs: '@media (min-width: 0px)',
    sm: '@media (min-width: 576px)',
    md: '@media (min-width: 768px)',
    lg: '@media (min-width: 992px)',
    xl: '@media (min-width: 1200px)',
    xxl: '@media (min-width: 1600px)',
  },
  
  down: {
    xs: '@media (max-width: 575.98px)',
    sm: '@media (max-width: 767.98px)',
    md: '@media (max-width: 991.98px)',
    lg: '@media (max-width: 1199.98px)',
    xl: '@media (max-width: 1599.98px)',
  },
  
  between: {
    xs_sm: '@media (min-width: 0px) and (max-width: 575.98px)',
    sm_md: '@media (min-width: 576px) and (max-width: 767.98px)',
    md_lg: '@media (min-width: 768px) and (max-width: 991.98px)',
    lg_xl: '@media (min-width: 992px) and (max-width: 1199.98px)',
    xl_xxl: '@media (min-width: 1200px) and (max-width: 1599.98px)',
  },
};

// Функция для получения значения точки перелома
export const getBreakpoint = (path: string): any => {
  const keys = path.split('.');
  let result: any = breakpoints;
  
  for (const key of keys) {
    if (result[key] === undefined) {
      console.warn(`Значение точки перелома по пути ${path} не найдено`);
      return undefined;
    }
    result = result[key];
  }
  
  return result;
};

// Хелпер для создания медиа-запросов
export const createMediaQuery = (minWidth?: number, maxWidth?: number): string => {
  if (minWidth !== undefined && maxWidth !== undefined) {
    return `@media (min-width: ${minWidth}px) and (max-width: ${maxWidth - 0.02}px)`;
  } else if (minWidth !== undefined) {
    return `@media (min-width: ${minWidth}px)`;
  } else if (maxWidth !== undefined) {
    return `@media (max-width: ${maxWidth - 0.02}px)`;
  }
  return '';
};

export default breakpoints;