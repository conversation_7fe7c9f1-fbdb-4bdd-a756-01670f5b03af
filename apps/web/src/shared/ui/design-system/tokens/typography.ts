/**
 * Типографика дизайн-системы "Козырь Мастер"
 * 
 * Определяет шрифты, размеры, веса и другие параметры текста
 * для обеспечения единообразия во всем приложении.
 */

export const typography = {
  // Семейства шрифтов
  fontFamily: {
    primary: '"Roboto", "Helvetica", "Arial", sans-serif',
    secondary: '"Playfair Display", serif',
    monospace: '"Roboto Mono", monospace',
  },
  
  // Размеры шрифтов
  fontSize: {
    xs: '0.75rem',      // 12px
    sm: '0.875rem',     // 14px
    base: '1rem',       // 16px
    lg: '1.125rem',     // 18px
    xl: '1.25rem',      // 20px
    '2xl': '1.5rem',    // 24px
    '3xl': '1.875rem',  // 30px
    '4xl': '2.25rem',   // 36px
    '5xl': '3rem',      // 48px
    '6xl': '3.75rem',   // 60px
  },
  
  // Веса шрифтов
  fontWeight: {
    thin: 100,
    extralight: 200,
    light: 300,
    regular: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900,
  },
  
  // Высота строки
  lineHeight: {
    none: 1,
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 2,
  },
  
  // Межбуквенное расстояние
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em',
  },
  
  // Предустановленные стили текста
  textStyles: {
    h1: {
      fontSize: '3rem',
      fontWeight: 700,
      lineHeight: 1.25,
      fontFamily: '"Playfair Display", serif',
    },
    h2: {
      fontSize: '2.25rem',
      fontWeight: 700,
      lineHeight: 1.25,
      fontFamily: '"Playfair Display", serif',
    },
    h3: {
      fontSize: '1.875rem',
      fontWeight: 600,
      lineHeight: 1.25,
      fontFamily: '"Playfair Display", serif',
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: 1.25,
      fontFamily: '"Playfair Display", serif',
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 600,
      lineHeight: 1.25,
      fontFamily: '"Playfair Display", serif',
    },
    h6: {
      fontSize: '1.125rem',
      fontWeight: 600,
      lineHeight: 1.25,
      fontFamily: '"Playfair Display", serif',
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.5,
      fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    },
    body2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: 1.5,
      fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    },
    caption: {
      fontSize: '0.75rem',
      fontWeight: 400,
      lineHeight: 1.5,
      fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    },
    button: {
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.75,
      fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
      textTransform: 'uppercase',
    },
  },
};

// Функция для получения стиля типографики по пути
export const getTypography = (path: string): any => {
  const keys = path.split('.');
  let result: any = typography;
  
  for (const key of keys) {
    if (result[key] === undefined) {
      console.warn(`Типографика по пути ${path} не найдена`);
      return undefined;
    }
    result = result[key];
  }
  
  return result;
};

export default typography;