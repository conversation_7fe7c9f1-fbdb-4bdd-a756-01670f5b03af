/**
 * Цветовая палитра дизайн-системы "Козырь Мастер"
 * 
 * Содержит основные, акцентные и нейтральные цвета для использования
 * во всех компонентах приложения. Обеспечивает единообразие
 * и соответствие брендингу.
 */

export const colors = {
  // Основные цвета бренда
  primary: {
    50: '#e6f7ff',
    100: '#bae7ff',
    200: '#91d5ff',
    300: '#69c0ff',
    400: '#40a9ff',
    500: '#1890ff', // Основной цвет
    600: '#096dd9',
    700: '#0050b3',
    800: '#003a8c',
    900: '#002766',
  },
  
  // Акцентные цвета
  accent: {
    red: {
      light: '#ff7875',
      base: '#f5222d',
      dark: '#a8071a',
    },
    green: {
      light: '#95de64',
      base: '#52c41a',
      dark: '#237804',
    },
    yellow: {
      light: '#fff566',
      base: '#fadb14',
      dark: '#ad8b00',
    },
    blue: {
      light: '#69c0ff',
      base: '#1890ff',
      dark: '#0050b3',
    },
  },
  
  // Нейтральные цвета
  neutral: {
    white: '#ffffff',
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
    },
    black: '#000000',
  },
  
  // Семантические цвета
  semantic: {
    success: '#52c41a',
    warning: '#faad14',
    error: '#f5222d',
    info: '#1890ff',
  },
  
  // Цвета карточных мастей
  suits: {
    hearts: '#e53935',    // Червы
    diamonds: '#d81b60',   // Бубны
    clubs: '#2e2e2e',      // Трефы
    spades: '#212121',     // Пики
  },
  
  // Цвета для фонов
  background: {
    primary: '#ffffff',
    secondary: '#f5f5f5',
    tertiary: '#e6f7ff',
    dark: '#001529',
  },
  
  // Цвета для текста
  text: {
    primary: '#262626',
    secondary: '#595959',
    disabled: '#bfbfbf',
    inverse: '#ffffff',
  },
  
  // Цвета для границ
  border: {
    light: '#f0f0f0',
    base: '#d9d9d9',
    dark: '#8c8c8c',
  },
};

// Функция для получения цвета по пути
export const getColor = (path: string): string => {
  const keys = path.split('.');
  let result: any = colors;
  
  for (const key of keys) {
    if (result[key] === undefined) {
      console.warn(`Цвет по пути ${path} не найден`);
      return '';
    }
    result = result[key];
  }
  
  return typeof result === 'string' ? result : '';
};

export default colors;