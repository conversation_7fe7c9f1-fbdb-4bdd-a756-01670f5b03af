/**
 * Отступы и размеры дизайн-системы "Козырь Мастер"
 * 
 * Определяет стандартные значения отступов, размеров и радиусов
 * для обеспечения единообразия пространственных отношений
 * между элементами интерфейса.
 */

export const spacing = {
  // Базовые отступы (в пикселях)
  space: {
    0: '0',
    1: '0.25rem',      // 4px
    2: '0.5rem',       // 8px
    3: '0.75rem',      // 12px
    4: '1rem',         // 16px
    5: '1.25rem',      // 20px
    6: '1.5rem',       // 24px
    8: '2rem',         // 32px
    10: '2.5rem',      // 40px
    12: '3rem',        // 48px
    16: '4rem',        // 64px
    20: '5rem',        // 80px
    24: '6rem',        // 96px
    32: '8rem',        // 128px
    40: '10rem',       // 160px
    48: '12rem',       // 192px
    56: '14rem',       // 224px
    64: '16rem',       // 256px
  },
  
  // Размеры компонентов
  sizes: {
    // Высоты компонентов
    heights: {
      xs: '1.5rem',      // 24px
      sm: '2rem',        // 32px
      md: '2.5rem',      // 40px
      lg: '3rem',        // 48px
      xl: '3.5rem',      // 56px
    },
    
    // Ширины компонентов
    widths: {
      xs: '4rem',        // 64px
      sm: '8rem',        // 128px
      md: '12rem',       // 192px
      lg: '16rem',       // 256px
      xl: '20rem',       // 320px
      '2xl': '24rem',    // 384px
      '3xl': '28rem',    // 448px
      '4xl': '32rem',    // 512px
      '5xl': '36rem',    // 576px
      '6xl': '42rem',    // 672px
      '7xl': '48rem',    // 768px
      full: '100%',
      auto: 'auto',
    },
    
    // Максимальные ширины
    maxWidths: {
      none: 'none',
      xs: '20rem',       // 320px
      sm: '24rem',       // 384px
      md: '28rem',       // 448px
      lg: '32rem',       // 512px
      xl: '36rem',       // 576px
      '2xl': '42rem',    // 672px
      '3xl': '48rem',    // 768px
      '4xl': '56rem',    // 896px
      '5xl': '64rem',    // 1024px
      '6xl': '72rem',    // 1152px
      '7xl': '80rem',    // 1280px
      full: '100%',
    },
  },
  
  // Радиусы скругления
  borderRadius: {
    none: '0',
    sm: '0.125rem',    // 2px
    base: '0.25rem',    // 4px
    md: '0.375rem',     // 6px
    lg: '0.5rem',       // 8px
    xl: '0.75rem',      // 12px
    '2xl': '1rem',      // 16px
    '3xl': '1.5rem',    // 24px
    full: '9999px',
  },
  
  // Толщина границ
  borderWidth: {
    0: '0',
    1: '1px',
    2: '2px',
    4: '4px',
    8: '8px',
  },
  
  // Тени
  shadows: {
    none: 'none',
    xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    outline: '0 0 0 3px rgba(24, 144, 255, 0.5)',
  },
  
  // Z-индексы
  zIndices: {
    0: 0,
    10: 10,
    20: 20,
    30: 30,
    40: 40,
    50: 50,
    auto: 'auto',
    dropdown: 1000,
    sticky: 1100,
    fixed: 1200,
    overlay: 1300,
    modal: 1400,
    popover: 1500,
    tooltip: 1600,
  },
};

// Функция для получения значения отступа по пути
export const getSpacing = (path: string): any => {
  const keys = path.split('.');
  let result: any = spacing;
  
  for (const key of keys) {
    if (result[key] === undefined) {
      console.warn(`Значение отступа по пути ${path} не найдено`);
      return undefined;
    }
    result = result[key];
  }
  
  return result;
};

export default spacing;