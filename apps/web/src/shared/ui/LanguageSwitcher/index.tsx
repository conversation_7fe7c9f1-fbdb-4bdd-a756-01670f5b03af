import React from 'react';
import { useTranslation } from 'react-i18next';
import styles from './LanguageSwitcher.module.css';

/**
 * Компонент для переключения языка приложения
 */
export const LanguageSwitcher: React.FC = () => {
  const { i18n, t } = useTranslation('common');
  
  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
    // Сохраняем выбор пользователя
    localStorage.setItem('i18nextLng', lng);
  };

  return (
    <div className={styles.languageSwitcher}>
      <button 
        className={`${styles.languageButton} ${i18n.language === 'ru' ? styles.active : ''}`}
        onClick={() => changeLanguage('ru')}
        aria-label={t('language.ru')}
      >
        RU
      </button>
      <button 
        className={`${styles.languageButton} ${i18n.language === 'en' ? styles.active : ''}`}
        onClick={() => changeLanguage('en')}
        aria-label={t('language.en')}
      >
        EN
      </button>
    </div>
  );
};