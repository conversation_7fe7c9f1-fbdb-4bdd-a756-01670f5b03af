.languageSwitcher {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 16px;
}

.languageButton {
  background: transparent;
  border: 1px solid var(--color-border, #ccc);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.languageButton:hover {
  background-color: var(--color-bg-hover, #f0f0f0);
}

.active {
  background-color: var(--color-primary, #4a76a8);
  color: white;
  border-color: var(--color-primary, #4a76a8);
}

.active:hover {
  background-color: var(--color-primary-dark, #3a5a78);
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
  .languageSwitcher {
    margin: 0 8px;
  }
  
  .languageButton {
    padding: 2px 6px;
    font-size: 12px;
  }
}