import React from 'react';
import styled, { css } from 'styled-components';
import { colors, darken, ThemeType, mixins } from './colors';
import { pulse, shake, notificationAnimation } from './animations';

// Общие компоненты для использования во всех частях приложения

// Контейнер для игры
export const GameWrapper = styled.div<{ disabled?: boolean; theme?: ThemeType }>`
  padding: 20px;
  font-family: sans-serif;
  background-color: ${props => props.theme === 'dark' ? colors.backgrounds.dark : colors.backgrounds.light};
  min-height: 100vh;
  position: relative;
  color: ${props => props.theme === 'dark' ? colors.text.dark : colors.text.light};
  
  ${props => props.disabled && css`
    pointer-events: none;
    opacity: 0.8;
  `}
`;

// Сообщение об ошибке
export const ErrorMessage = styled.p`
  color: ${colors.danger};
  font-weight: bold;
  animation: ${shake} 0.5s ease-in-out;
  padding: 10px;
  background-color: rgba(255, 0, 0, 0.1);
  border-radius: 4px;
  border-left: 4px solid ${colors.danger};
`;

// Сообщение о статусе
export const StatusMessage = styled.p`
  font-size: 1.1em;
  margin-bottom: 15px;
  padding: 8px 12px;
  background-color: ${colors.backgrounds.light};
  border-radius: 4px;
  ${mixins.uiShadow}
`;

// Кнопка действия
export const ActionButton = styled.button<{ color?: string; disabled?: boolean }>`
  ${props => mixins.buttonStyle(props.color)}
  
  ${props => props.disabled && css`
    pointer-events: none;
    opacity: 0.7;
  `}
`;

// Область игры
export const GameArea = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

// Информация об игре
export const GameInfo = styled.div<{ theme?: ThemeType }>`
  background-color: ${props => props.theme === 'dark' ? colors.backgrounds.dark : 'white'};
  padding: 15px;
  border-radius: 5px;
  ${mixins.uiShadow}
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  position: relative;
  color: ${props => props.theme === 'dark' ? colors.text.dark : colors.text.light};
`;

// Область стола
export const TableArea = styled.div<{ theme?: ThemeType }>`
  background-color: ${props => props.theme === 'dark' ? darken(0.1, colors.backgrounds.dark) : '#e9ecef'};
  padding: 15px;
  border-radius: 5px;
  min-height: 150px;
  position: relative;
  transition: all 0.3s ease;
  ${mixins.uiShadow}
  border: 1px solid ${props => props.theme === 'dark' ? darken(0.2, colors.backgrounds.dark) : '#dee2e6'};
`;

// Контейнер для карт на столе
export const TableCardsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
`;

// Пара карт (атака/защита)
export const CardPair = styled.div<{ isNew?: boolean; isDefending?: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  padding: 5px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 3px;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  }
`;

// Обертка для карты с анимацией
export const CardWrapper = styled.div<{ isAnimating?: boolean; animationType?: 'attack' | 'defend' | 'take' }>`
  position: relative;
  transition: all 0.3s ease;
`;

// Область игроков
export const PlayersArea = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;
`;

// Информация об игроке
export const PlayerInfo = styled.div<{ isActive: boolean; isCurrentPlayer?: boolean; theme?: ThemeType }>`
  border: 2px solid ${props => {
    if (props.isActive) return colors.success;
    if (props.isCurrentPlayer) return colors.primary;
    return props.theme === 'dark' ? darken(0.1, colors.backgrounds.dark) : '#ccc';
  }};
  background-color: ${props => {
    if (props.theme === 'dark') return colors.backgrounds.dark;
    if (props.isCurrentPlayer) return 'rgba(0, 123, 255, 0.05)';
    return 'white';
  }};
  margin-bottom: 15px;
  padding: 15px;
  border-radius: 5px;
  ${mixins.uiShadow}
  opacity: ${props => props.isActive ? 1 : 0.8};
  transition: all 0.3s ease;
  position: relative;
  color: ${props => props.theme === 'dark' ? colors.text.dark : colors.text.light};
  
  h3 {
    margin-top: 0;
    color: ${props => {
      if (props.isActive) return colors.success;
      if (props.isCurrentPlayer) return colors.primary;
      return props.theme === 'dark' ? colors.text.dark : 'black';
    }};
  }
`;

// Контейнер для карт в руке
export const HandContainer = styled.div<{ isActive?: boolean; theme?: ThemeType }>`
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 10px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
  padding: 10px;
  border-radius: 5px;
  
  ${props => props.isActive && css`
    background-color: ${props.theme === 'dark' ? 'rgba(40, 167, 69, 0.2)' : 'rgba(40, 167, 69, 0.1)'};
    box-shadow: inset 0 0 5px rgba(40, 167, 69, 0.2);
  `}
`;

// Контейнер для кнопок действий
export const ActionButtonsContainer = styled.div`
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
`;

// Индикатор синхронизации
export const SyncIndicator = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 5px 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 4px;
  font-size: 0.9em;
  display: flex;
  align-items: center;
  gap: 5px;
  z-index: 100;
  animation: ${pulse} 1.5s infinite ease-in-out;
`;

// Индикатор хода
export const TurnIndicator = styled.div<{ isYourTurn: boolean }>`
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 5px 10px;
  background-color: ${props => props.isYourTurn ? 'rgba(40, 167, 69, 0.9)' : 'rgba(108, 117, 125, 0.9)'};
  color: white;
  border-radius: 4px;
  font-size: 0.9em;
  font-weight: bold;
  z-index: 100;
`;

// Компонент таймера хода
export const TurnTimer = styled.div<{ timeLeft: number }>`
  position: absolute;
  top: 50px;
  left: 10px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: conic-gradient(
    ${props => props.timeLeft <= 5 ? colors.danger : props.timeLeft <= 15 ? colors.warning : colors.success} 
    ${props => (props.timeLeft / 30) * 360}deg,
    #e9ecef ${props => (props.timeLeft / 30) * 360}deg 360deg
  );
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  z-index: 100;
  
  &::before {
    content: '${props => props.timeLeft}';
    width: 40px;
    height: 40px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  ${props => props.timeLeft <= 5 && css`
    animation: ${pulse} 0.5s infinite;
  `}
`;

// Компонент уведомления о действиях
export const ActionNotification = styled.div<{ type: 'attack' | 'defend' | 'take' | 'pass' | 'error' | 'sync' }>`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 24px;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  box-shadow: 0 8px 16px rgba(0,0,0,0.3);
  z-index: 1000;
  animation: ${notificationAnimation} 3s ease-in-out forwards;
  display: flex;
  align-items: center;
  gap: 10px;
  
  &:before {
    content: '';
    width: 20px;
    height: 20px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
  }
  
  ${props => props.type === 'attack' && css`
    background-color: ${colors.danger};
    &:before {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M13 14h-2v-4h2v4zm0 6h-2v-2h2v2zM1 21h22L12 2 1 21z'/%3E%3C/svg%3E");
    }
  `}
  ${props => props.type === 'defend' && css`
    background-color: ${colors.success};
    &:before {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm-2 16l-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9l-8 8z'/%3E%3C/svg%3E");
    }
  `}
  ${props => props.type === 'take' && css`
    background-color: ${colors.warning}; 
    color: #212529;
    &:before {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23212529'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm-2 14l-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9l-8 8z'/%3E%3C/svg%3E");
    }
  `}
  ${props => props.type === 'pass' && css`
    background-color: ${colors.info};
    &:before {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/%3E%3C/svg%3E");
    }
  `}
  ${props => props.type === 'error' && css`
    background-color: ${colors.danger};
    &:before {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/%3E%3C/svg%3E");
    }
  `}
  ${props => props.type === 'sync' && css`
    background-color: ${colors.secondary};
    &:before {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z'/%3E%3C/svg%3E");
    }
  `}
`;

// Компонент для отображения победителя
export const WinnerDisplay = styled.div`
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(40, 167, 69, 0.9);
  color: white;
  padding: 30px 50px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  z-index: 1100;
  text-align: center;
  
  h2 {
    font-size: 2rem;
    margin-bottom: 10px;
  }
  
  p {
    font-size: 1.2rem;
    margin-bottom: 20px;
  }
  
  button {
    padding: 10px 20px;
    background-color: white;
    color: ${colors.success};
    border: none;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background-color: #f8f9fa;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
  }
`;