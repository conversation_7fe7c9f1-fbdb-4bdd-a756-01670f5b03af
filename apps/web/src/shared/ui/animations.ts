import { keyframes } from 'styled-components';

// Базовые анимации для карт и элементов интерфейса
// Эти анимации могут быть переиспользованы в разных компонентах

// Анимация пульсации
export const pulse = keyframes`
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
`;

// Анимация встряхивания (для ошибок)
export const shake = keyframes`
  0% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  50% { transform: translateX(5px); }
  75% { transform: translateX(-5px); }
  100% { transform: translateX(0); }
`;

// Анимации для карт
export const slideIn = keyframes`
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
`;

// Анимация для карт противника
export const opponentCardMove = keyframes`
  0% { transform: translate(-50px, -30px) rotate(-5deg); opacity: 0; }
  70% { transform: translate(5px, 3px) rotate(2deg); opacity: 1; }
  100% { transform: translate(0, 0) rotate(0); opacity: 1; }
`;

// Анимация для взятия карт
export const takeCards = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.1); box-shadow: 0 0 15px rgba(255, 193, 7, 0.8); }
  100% { transform: scale(1); }
`;

// Анимация для победы
export const winAnimation = keyframes`
  0% { transform: scale(1); }
  10% { transform: scale(1.1) rotate(3deg); }
  20% { transform: scale(1.1) rotate(-3deg); }
  30% { transform: scale(1.1) rotate(3deg); }
  40% { transform: scale(1.1) rotate(-3deg); }
  50% { transform: scale(1.1) rotate(3deg); }
  60% { transform: scale(1.1) rotate(-3deg); }
  70% { transform: scale(1.1) rotate(3deg); }
  80% { transform: scale(1.1) rotate(-3deg); }
  90% { transform: scale(1.1) rotate(3deg); }
  100% { transform: scale(1); }
`;

// Анимация для новой карты
export const newCardAnimation = keyframes`
  0% { transform: translateY(50px) scale(0.8); opacity: 0; }
  70% { transform: translateY(-10px) scale(1.05); opacity: 1; }
  85% { transform: translateY(5px) scale(0.95); opacity: 1; }
  100% { transform: translateY(0) scale(1); opacity: 1; }
`;

// Анимация для карты, которой можно сходить
export const playableCardAnimation = keyframes`
  0% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.3); }
  50% { box-shadow: 0 0 15px rgba(255, 215, 0, 0.8); }
  100% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.3); }
`;

// Анимация для перетаскивания карты
export const dragCardAnimation = keyframes`
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1) rotate(3deg); opacity: 0.9; box-shadow: 0 15px 25px rgba(255, 215, 0, 0.5); }
  100% { transform: scale(1.1) rotate(-3deg); opacity: 0.8; box-shadow: 0 15px 25px rgba(255, 215, 0, 0.5); }
`;

// Анимация для уведомлений
export const notificationAnimation = keyframes`
  0% { transform: translateX(-50%) translateY(-20px); opacity: 0; }
  10% { transform: translateX(-50%) translateY(0); opacity: 1; }
  90% { transform: translateX(-50%) translateY(0); opacity: 1; }
  100% { transform: translateX(-50%) translateY(-20px); opacity: 0; }
`;

// Анимация для раздачи карт
export const dealCardAnimation = keyframes`
  0% {
    transform: translate(-100px, -100px) rotate(-10deg);
    opacity: 0;
  }
  100% {
    transform: translate(0, 0) rotate(0);
    opacity: 1;
  }
`;

// Анимация для защиты картой
export const defendCardAnimation = keyframes`
  0% {
    transform: translate(50px, -50px) rotate(10deg);
    opacity: 0;
  }
  100% {
    transform: translate(0, 0) rotate(0);
    opacity: 1;
  }
`;

// Анимация для подсветки активного игрока
export const pulseAnimation = keyframes`
  0% { box-shadow: 0 0 10px rgba(255, 255, 0, 0.3); }
  50% { box-shadow: 0 0 20px rgba(255, 255, 0, 0.6); }
  100% { box-shadow: 0 0 10px rgba(255, 255, 0, 0.3); }
`;

// Анимация для выбранной карты
export const selectedCardAnimation = keyframes`
  0% { transform: translateY(-20px) rotate(0deg); box-shadow: 0 10px 20px rgba(255, 215, 0, 0.4); }
  50% { transform: translateY(-30px) rotate(2deg); box-shadow: 0 15px 30px rgba(255, 215, 0, 0.8), 0 0 15px gold; }
  100% { transform: translateY(-20px) rotate(0deg); box-shadow: 0 10px 20px rgba(255, 215, 0, 0.4); }
`;

// Анимация для подсветки
export const highlightAnimation = keyframes`
  0% { box-shadow: 0 0 0 rgba(255, 215, 0, 0); }
  50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.7); }
  100% { box-shadow: 0 0 0 rgba(255, 215, 0, 0); }
`;