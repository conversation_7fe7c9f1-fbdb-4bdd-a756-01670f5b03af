import { keyframes } from 'styled-components';

// Анимация для победы
export const winAnimation = keyframes`
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2) rotate(5deg); opacity: 1; box-shadow: 0 0 30px gold; }
  100% { transform: scale(1); opacity: 1; }
`;

// Анимация для взятия карт
export const takeCards = keyframes`
  0% { transform: translateY(0); opacity: 1; }
  50% { transform: translateY(-20px); opacity: 0.7; }
  100% { transform: translateY(-50px); opacity: 0; }
`;

// Анимация для появления элементов
export const slideIn = keyframes`
  0% { transform: translateY(20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
`;

// Анимация для хода карты соперника
export const opponentCardMove = keyframes`
  0% { transform: translateX(-50px) rotate(-10deg); opacity: 0; }
  100% { transform: translateX(0) rotate(0); opacity: 1; }
`;

// Анимация для атаки
export const attackAnimation = keyframes`
  0% { transform: translateY(0) rotate(0); }
  50% { transform: translateY(-30px) rotate(-5deg); }
  100% { transform: translateY(0) rotate(0); }
`;

// Анимация для защиты
export const defendAnimation = keyframes`
  0% { transform: translateY(0) rotate(0); }
  50% { transform: translateY(-20px) rotate(5deg); }
  100% { transform: translateY(0) rotate(0); }
`;

// Анимация для раздачи карт
export const dealCardAnimation = keyframes`
  0% { transform: translate(-100px, -100px) scale(0.5) rotate(-10deg); opacity: 0; }
  100% { transform: translate(0, 0) scale(1) rotate(0); opacity: 1; }
`;

// Анимация для пульсации
export const pulseAnimation = keyframes`
  0% { transform: scale(1); box-shadow: 0 0 0 rgba(233, 69, 96, 0); }
  50% { transform: scale(1.05); box-shadow: 0 0 10px rgba(233, 69, 96, 0.5); }
  100% { transform: scale(1); box-shadow: 0 0 0 rgba(233, 69, 96, 0); }
`;

// Анимация для подсветки активного элемента
export const highlightAnimation = keyframes`
  0% { box-shadow: 0 0 0 rgba(255, 215, 0, 0); }
  50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.7); }
  100% { box-shadow: 0 0 0 rgba(255, 215, 0, 0); }
`;

// Анимация для переворота карты
export const flipCardAnimation = keyframes`
  0% { transform: rotateY(0deg); }
  100% { transform: rotateY(180deg); }
`;

// Анимация для исчезновения
export const fadeOut = keyframes`
  0% { opacity: 1; }
  100% { opacity: 0; }
`;

// Анимация для появления
export const fadeIn = keyframes`
  0% { opacity: 0; }
  100% { opacity: 1; }
`;