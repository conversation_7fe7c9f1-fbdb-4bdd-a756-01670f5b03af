import { css } from 'styled-components';

// Типы для тем
export type ThemeType = 'light' | 'dark' | 'colorful' | 'monochrome';
export type CardThemeType = 'classic' | 'modern' | 'minimal' | 'fantasy';

// Основные цвета приложения
export const colors = {
  // Основная палитра
  primary: '#007bff',
  secondary: '#6c757d',
  success: '#28a745',
  danger: '#dc3545',
  warning: '#ffc107',
  info: '#17a2b8',
  light: '#f8f9fa',
  dark: '#343a40',
  
  // Дополнительные цвета
  gold: 'rgba(255, 215, 0, 0.8)',
  silver: 'rgba(192, 192, 192, 0.8)',
  bronze: 'rgba(205, 127, 50, 0.8)',
  
  // Цвета для карточных мастей
  hearts: '#e44145',
  diamonds: '#e44145',
  clubs: '#2d2d2d',
  spades: '#2d2d2d',
  
  // Цвета фона для разных тем
  backgrounds: {
    light: '#f8f9fa',
    dark: '#1e2a38',
    colorful: '#4ca1af',
    monochrome: '#2c3e50',
    gameTable: '#277714' // Цвет игрового стола по умолчанию
  },
  
  // Цвета текста для разных тем
  text: {
    light: '#333333',
    dark: '#ffffff',
    colorful: '#ffffff',
    monochrome: '#f0f0f0'
  }
};

// Функция для затемнения цвета (аналог darken из библиотеки polished)
export const darken = (amount: number, color: string): string => {
  try {
    let usePound = false;
    if (color[0] === "#") {
      color = color.slice(1);
      usePound = true;
    }
    const num = parseInt(color, 16);
    let r = (num >> 16) * (1 - amount);
    let g = ((num >> 8) & 0x00FF) * (1 - amount);
    let b = (num & 0x0000FF) * (1 - amount);
    r = Math.max(0, Math.min(255, Math.round(r)));
    g = Math.max(0, Math.min(255, Math.round(g)));
    b = Math.max(0, Math.min(255, Math.round(b)));
    return (usePound ? "#" : "") + (b | (g << 8) | (r << 16)).toString(16).padStart(6, '0');
  } catch (e) {
    return color; // Возвращаем исходный цвет в случае ошибки
  }
};

// Функция для осветления цвета
export const lighten = (amount: number, color: string): string => {
  try {
    let usePound = false;
    if (color[0] === "#") {
      color = color.slice(1);
      usePound = true;
    }
    const num = parseInt(color, 16);
    let r = (num >> 16) + (255 - (num >> 16)) * amount;
    let g = ((num >> 8) & 0x00FF) + (255 - ((num >> 8) & 0x00FF)) * amount;
    let b = (num & 0x0000FF) + (255 - (num & 0x0000FF)) * amount;
    r = Math.max(0, Math.min(255, Math.round(r)));
    g = Math.max(0, Math.min(255, Math.round(g)));
    b = Math.max(0, Math.min(255, Math.round(b)));
    return (usePound ? "#" : "") + (b | (g << 8) | (r << 16)).toString(16).padStart(6, '0');
  } catch (e) {
    return color; // Возвращаем исходный цвет в случае ошибки
  }
};

// Функция для создания прозрачного цвета
export const transparentize = (amount: number, color: string): string => {
  // Преобразуем hex в rgba
  try {
    if (color.startsWith('#')) {
      const hex = color.slice(1);
      const r = parseInt(hex.slice(0, 2), 16);
      const g = parseInt(hex.slice(2, 4), 16);
      const b = parseInt(hex.slice(4, 6), 16);
      return `rgba(${r}, ${g}, ${b}, ${1 - amount})`;
    } else if (color.startsWith('rgb')) {
      // Если цвет уже в формате rgb или rgba
      if (color.startsWith('rgba')) {
        const parts = color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
        if (parts) {
          const alpha = Math.max(0, Math.min(1, parseFloat(parts[4]) * (1 - amount)));
          return `rgba(${parts[1]}, ${parts[2]}, ${parts[3]}, ${alpha})`;
        }
      } else {
        const parts = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
        if (parts) {
          return `rgba(${parts[1]}, ${parts[2]}, ${parts[3]}, ${1 - amount})`;
        }
      }
    }
    return color; // Возвращаем исходный цвет, если не удалось преобразовать
  } catch (e) {
    return color;
  }
};

// Миксины для часто используемых стилей
export const mixins = {
  // Тень для карт
  cardShadow: css`
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  `,
  
  // Тень для элементов интерфейса
  uiShadow: css`
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  `,
  
  // Стиль для кнопок
  buttonStyle: (color: string = colors.primary) => css`
    padding: 8px 15px;
    margin: 5px;
    font-size: 1em;
    cursor: pointer;
    border: none;
    border-radius: 4px;
    background-color: ${color};
    color: white;
    transition: all 0.2s;
    position: relative;
    overflow: hidden;

    &:hover {
      background-color: ${darken(0.1, color)};
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &:disabled {
      background-color: ${colors.secondary};
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
      opacity: 0.7;
    }
  `,
  
  // Стиль для контейнеров
  containerStyle: (theme: ThemeType = 'light') => css`
    background-color: ${colors.backgrounds[theme]};
    color: ${colors.text[theme]};
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  `,
  
  // Стиль для игрового стола
  gameTableStyle: (theme: ThemeType = 'light') => css`
    background-color: ${theme === 'light' ? '#277714' : colors.backgrounds[theme]};
    color: ${colors.text[theme]};
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
  `
};