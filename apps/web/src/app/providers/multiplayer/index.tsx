/**
 * Провайдер для многопользовательского режима
 */
import React, { ReactNode } from 'react';
import { MultiplayerProvider } from '@/features/multiplayer';

interface MultiplayerProviderWrapperProps {
  children: ReactNode;
}

/**
 * Компонент-обертка для провайдера многопользовательского режима
 */
export const MultiplayerProviderWrapper: React.FC<MultiplayerProviderWrapperProps> = ({ children }) => {
  return (
    <MultiplayerProvider>
      {children}
    </MultiplayerProvider>
  );
};

/**
 * Экспорт провайдера для использования в приложении
 */
export { useMultiplayer, useGameConnection, useGameChat } from '@/features/multiplayer';