import i18n from "i18next";
import { initReactI18next } from "react-i18next";

// Импорт ресурсов для локализации
const resources = {
  ru: {
    translation: {
      welcome: "Добро пожаловать в Козырь Мастер",
      play_now: "Играть сейчас",
      join_game: "Присоединяйтесь к игре или создайте свою",
      tournaments: "Турниры",
      tournaments_desc: "Участвуйте в турнирах и выигрывайте призы",
      learning: "Обучение",
      learning_desc: "Изучите правила игр и стратегии",
      copyright: "© 2023 Козырь Мастер. Все права защищены.",
    },
  },
  en: {
    translation: {
      welcome: "Welcome to Kozyr Master",
      play_now: "Play Now",
      join_game: "Join a game or create your own",
      tournaments: "Tournaments",
      tournaments_desc: "Participate in tournaments and win prizes",
      learning: "Learning",
      learning_desc: "Learn game rules and strategies",
      copyright: "© 2023 Kozyr Master. All rights reserved.",
    },
  },
};

i18n.use(initReactI18next).init({
  resources,
  lng: "ru",
  fallbackLng: "ru",
  interpolation: {
    escapeValue: false,
  },
});

export default i18n;
