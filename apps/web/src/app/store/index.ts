import { configureStore } from "@reduxjs/toolkit";

// Импорт редьюсеров
// import { authReducer } from '@features/auth/model/slice';
// import { gameReducer } from "@entities/game/model/slice"; // Removed old game slice
import durakReducer from '@entities/game/model/durakSlice'; // Import the new Durak slice

export const store = configureStore({
  reducer: {
    // auth: authReducer,
    // game: gameReducer, // Removed old game slice
    durak: durakReducer, // Add the Durak game slice
    // Здесь будут добавлены другие редьюсеры
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
