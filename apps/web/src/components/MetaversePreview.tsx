import React, { useState, useRef, Suspense } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Environment, Float, Text3D, MeshReflectorMaterial } from '@react-three/drei';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';
import * as THREE from 'three';

interface World {
  id: string;
  name: string;
  description: string;
  theme: string;
  color: string;
  environment: string;
  features: string[];
  playerCount: number;
  preview: string;
}

const worlds: World[] = [
  {
    id: 'casino',
    name: 'Королевское казино',
    description: 'Роскошное казино с золотыми столами и кристальными люстрами',
    theme: 'Классика',
    color: '#ffd700',
    environment: 'sunset',
    features: ['Покерные столы', 'VIP зоны', 'Живая музыка', 'Бар'],
    playerCount: 1247,
    preview: '🏰'
  },
  {
    id: 'medieval',
    name: 'Средневековая таверна',
    description: 'Уютная таверна с каменными стенами и горящим камином',
    theme: 'Фантазия',
    color: '#8b4513',
    environment: 'forest',
    features: ['Деревянные столы', 'Камин', 'Бард', 'Эль'],
    playerCount: 892,
    preview: '🏛️'
  },
  {
    id: 'futuristic',
    name: 'Киберпространство',
    description: 'Футуристическая станция с голографическими интерфейсами',
    theme: 'Sci-Fi',
    color: '#00ffff',
    environment: 'city',
    features: ['Голограммы', 'Неон', 'ИИ дилеры', 'Антигравитация'],
    playerCount: 2156,
    preview: '🚀'
  },
  {
    id: 'nature',
    name: 'Лесная поляна',
    description: 'Магическая поляна среди древних деревьев',
    theme: 'Природа',
    color: '#228b22',
    environment: 'forest',
    features: ['Живые деревья', 'Светлячки', 'Ручей', 'Магия'],
    playerCount: 634,
    preview: '🌲'
  },
  {
    id: 'space',
    name: 'Космическая станция',
    description: 'Орбитальная станция с видом на звёзды',
    theme: 'Космос',
    color: '#4b0082',
    environment: 'night',
    features: ['Невесомость', 'Звёзды', 'Планеты', 'Астероиды'],
    playerCount: 1523,
    preview: '🌌'
  },
  {
    id: 'underwater',
    name: 'Подводный дворец',
    description: 'Кристальный дворец на дне океана',
    theme: 'Океан',
    color: '#008b8b',
    environment: 'ocean',
    features: ['Кораллы', 'Рыбы', 'Пузыри', 'Сокровища'],
    playerCount: 756,
    preview: '🌊'
  }
];

// 3D компонент мира
const World3D: React.FC<{ world: World; isActive: boolean }> = ({ world, isActive }) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.5;
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime) * 0.1;
    }
  });

  return (
    <Float speed={2} rotationIntensity={0.5} floatIntensity={0.5}>
      <mesh ref={meshRef} scale={isActive ? 1.2 : 1}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshStandardMaterial
          color={world.color}
          metalness={0.7}
          roughness={0.3}
          emissive={world.color}
          emissiveIntensity={isActive ? 0.3 : 0.1}
        />
      </mesh>
    </Float>
  );
};

// Компонент игрового стола
const GameTable3D: React.FC = () => {
  const tableRef = useRef<THREE.Group>(null);

  useFrame((state) => {
    if (tableRef.current) {
      tableRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
    }
  });

  return (
    <group ref={tableRef} position={[0, -1, 0]}>
      {/* Стол */}
      <mesh position={[0, 0, 0]}>
        <cylinderGeometry args={[2, 2, 0.2, 32]} />
        <meshStandardMaterial color="#8b4513" />
      </mesh>
      
      {/* Поверхность стола */}
      <mesh position={[0, 0.11, 0]}>
        <cylinderGeometry args={[1.9, 1.9, 0.02, 32]} />
        <meshStandardMaterial color="#228b22" />
      </mesh>

      {/* Карты */}
      {Array.from({ length: 8 }).map((_, i) => (
        <mesh
          key={i}
          position={[
            Math.cos((i / 8) * Math.PI * 2) * 1.5,
            0.15,
            Math.sin((i / 8) * Math.PI * 2) * 1.5
          ]}
          rotation={[0, (i / 8) * Math.PI * 2, 0]}
        >
          <boxGeometry args={[0.1, 0.15, 0.01]} />
          <meshStandardMaterial color="white" />
        </mesh>
      ))}
    </group>
  );
};

const MetaversePreview: React.FC = () => {
  const [selectedWorld, setSelectedWorld] = useState(0);
  const [isVRMode, setIsVRMode] = useState(false);

  return (
    <MetaverseContainer>
      <ContentWrapper>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <SectionTitle>Метавселенная карточных игр</SectionTitle>
          <SectionSubtitle>
            Исследуйте уникальные 3D миры и играйте в иммерсивной среде
          </SectionSubtitle>
        </motion.div>

        <MainContent>
          {/* 3D Превью */}
          <PreviewContainer>
            <Canvas camera={{ position: [0, 2, 8], fov: 60 }}>
              <ambientLight intensity={0.4} />
              <pointLight position={[10, 10, 10]} intensity={1} />
              <pointLight position={[-10, -10, -10]} intensity={0.5} color={worlds[selectedWorld].color} />
              
              <Suspense fallback={null}>
                <Environment preset={worlds[selectedWorld].environment as any} />
                <World3D world={worlds[selectedWorld]} isActive={true} />
                <GameTable3D />
                
                {/* Отражающий пол */}
                <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]}>
                  <planeGeometry args={[20, 20]} />
                  <MeshReflectorMaterial
                    blur={[300, 100]}
                    resolution={2048}
                    mixBlur={1}
                    mixStrength={40}
                    roughness={1}
                    depthScale={1.2}
                    minDepthThreshold={0.4}
                    maxDepthThreshold={1.4}
                    color="#050505"
                    metalness={0.5}
                  />
                </mesh>
              </Suspense>
              
              <OrbitControls enableZoom={false} enablePan={false} />
            </Canvas>

            {/* VR/AR переключатель */}
            <VRToggle>
              <VRButton
                active={isVRMode}
                onClick={() => setIsVRMode(!isVRMode)}
              >
                {isVRMode ? '🥽 VR Режим' : '🖥️ Обычный режим'}
              </VRButton>
            </VRToggle>
          </PreviewContainer>

          {/* Информация о мире */}
          <WorldInfo>
            <AnimatePresence mode="wait">
              <motion.div
                key={selectedWorld}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.5 }}
              >
                <WorldHeader>
                  <WorldIcon>{worlds[selectedWorld].preview}</WorldIcon>
                  <div>
                    <WorldName>{worlds[selectedWorld].name}</WorldName>
                    <WorldTheme>{worlds[selectedWorld].theme}</WorldTheme>
                  </div>
                </WorldHeader>

                <WorldDescription>
                  {worlds[selectedWorld].description}
                </WorldDescription>

                <WorldStats>
                  <StatItem>
                    <StatValue>{worlds[selectedWorld].playerCount}</StatValue>
                    <StatLabel>Игроков онлайн</StatLabel>
                  </StatItem>
                  <StatItem>
                    <StatValue>{worlds[selectedWorld].features.length}</StatValue>
                    <StatLabel>Уникальных функций</StatLabel>
                  </StatItem>
                  <StatItem>
                    <StatValue>4.9</StatValue>
                    <StatLabel>Рейтинг</StatLabel>
                  </StatItem>
                </WorldStats>

                <FeaturesList>
                  <FeaturesTitle>Особенности мира:</FeaturesTitle>
                  {worlds[selectedWorld].features.map((feature, index) => (
                    <FeatureItem
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      ✨ {feature}
                    </FeatureItem>
                  ))}
                </FeaturesList>

                <ActionButtons>
                  <PrimaryButton
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Войти в мир
                  </PrimaryButton>
                  <SecondaryButton
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Виртуальный тур
                  </SecondaryButton>
                </ActionButtons>
              </motion.div>
            </AnimatePresence>
          </WorldInfo>
        </MainContent>

        {/* Селектор миров */}
        <WorldSelector>
          {worlds.map((world, index) => (
            <WorldCard
              key={world.id}
              active={index === selectedWorld}
              color={world.color}
              onClick={() => setSelectedWorld(index)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <WorldCardIcon>{world.preview}</WorldCardIcon>
              <WorldCardName>{world.name}</WorldCardName>
              <WorldCardPlayers>{world.playerCount} игроков</WorldCardPlayers>
            </WorldCard>
          ))}
        </WorldSelector>

        {/* Технические характеристики */}
        <TechSpecs>
          <SpecItem>
            <SpecIcon>🎮</SpecIcon>
            <SpecText>
              <SpecLabel>Поддержка VR/AR</SpecLabel>
              <SpecValue>WebXR, Oculus, HTC Vive</SpecValue>
            </SpecText>
          </SpecItem>
          <SpecItem>
            <SpecIcon>👥</SpecIcon>
            <SpecText>
              <SpecLabel>Одновременных игроков</SpecLabel>
              <SpecValue>До 10,000 в одном мире</SpecValue>
            </SpecText>
          </SpecItem>
          <SpecItem>
            <SpecIcon>🌐</SpecIcon>
            <SpecText>
              <SpecLabel>Кроссплатформенность</SpecLabel>
              <SpecValue>PC, Mobile, VR, AR</SpecValue>
            </SpecText>
          </SpecItem>
          <SpecItem>
            <SpecIcon>⚡</SpecIcon>
            <SpecText>
              <SpecLabel>Производительность</SpecLabel>
              <SpecValue>60+ FPS, < 50ms задержка</SpecValue>
            </SpecText>
          </SpecItem>
        </TechSpecs>
      </ContentWrapper>
    </MetaverseContainer>
  );
};

// Стилизованные компоненты
const MetaverseContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  padding: 4rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ContentWrapper = styled.div`
  max-width: 1400px;
  width: 100%;
`;

const SectionTitle = styled.h2`
  font-size: 3.5rem;
  font-weight: 900;
  text-align: center;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  
  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const SectionSubtitle = styled.p`
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin-bottom: 4rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
`;

const MainContent = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 4rem;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
`;

const PreviewContainer = styled.div`
  position: relative;
  height: 500px;
  border-radius: 20px;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.1);
`;

const VRToggle = styled.div`
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
`;

const VRButton = styled.button<{ active: boolean }>`
  background: ${props => props.active ? 'linear-gradient(135deg, #4a90e2, #7b68ee)' : 'rgba(0, 0, 0, 0.5)'};
  color: white;
  border: none;
  border-radius: 10px;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  cursor: pointer;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
`;

const WorldInfo = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

const WorldHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const WorldIcon = styled.div`
  font-size: 4rem;
  filter: drop-shadow(0 0 20px currentColor);
`;

const WorldName = styled.h3`
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
`;

const WorldTheme = styled.p`
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
`;

const WorldDescription = styled.p`
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2rem;
`;

const WorldStats = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
`;

const StatItem = styled.div`
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 1rem;
`;

const StatValue = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: #4a90e2;
`;

const StatLabel = styled.div`
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
`;

const FeaturesList = styled.div`
  margin-bottom: 2rem;
`;

const FeaturesTitle = styled.h4`
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
`;

const FeatureItem = styled(motion.div)`
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  margin-bottom: 0.5rem;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 1rem;
`;

const PrimaryButton = styled(motion.button)`
  background: linear-gradient(135deg, #4a90e2, #7b68ee);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
`;

const SecondaryButton = styled(motion.button)`
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
`;

const WorldSelector = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 3rem;
`;

const WorldCard = styled(motion.div)<{ active: boolean; color: string }>`
  background: ${props => props.active ? `${props.color}20` : 'rgba(255, 255, 255, 0.05)'};
  border: 2px solid ${props => props.active ? props.color : 'rgba(255, 255, 255, 0.1)'};
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
`;

const WorldCardIcon = styled.div`
  font-size: 2rem;
  margin-bottom: 0.5rem;
`;

const WorldCardName = styled.div`
  font-size: 1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.5rem;
`;

const WorldCardPlayers = styled.div`
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
`;

const TechSpecs = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const SpecItem = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
`;

const SpecIcon = styled.div`
  font-size: 2rem;
`;

const SpecText = styled.div``;

const SpecLabel = styled.div`
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.25rem;
`;

const SpecValue = styled.div`
  font-size: 1rem;
  font-weight: 600;
  color: white;
`;

export default MetaversePreview;
