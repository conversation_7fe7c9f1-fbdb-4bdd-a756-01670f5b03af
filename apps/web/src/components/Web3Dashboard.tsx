import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';

interface Web3DashboardProps {
  web3Status: any;
  onConnectWallet: () => void;
}

interface NFTCard {
  id: string;
  name: string;
  image: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  power: number;
  price: number;
  isStaked: boolean;
}

interface DeFiPool {
  id: string;
  name: string;
  apr: number;
  tvl: number;
  userStaked: number;
  rewards: number;
}

interface TokenBalance {
  symbol: string;
  balance: number;
  value: number;
  change24h: number;
}

const Web3Dashboard: React.FC<Web3DashboardProps> = ({ web3Status, onConnectWallet }) => {
  const [activeTab, setActiveTab] = useState<'wallet' | 'nft' | 'defi' | 'dao'>('wallet');
  const [walletConnected, setWalletConnected] = useState(false);
  const [userAddress, setUserAddress] = useState('');
  const [tokenBalances, setTokenBalances] = useState<TokenBalance[]>([]);
  const [nftCollection, setNftCollection] = useState<NFTCard[]>([]);
  const [defiPools, setDefiPools] = useState<DeFiPool[]>([]);
  const [totalPortfolioValue, setTotalPortfolioValue] = useState(0);

  useEffect(() => {
    // Симуляция данных Web3
    if (walletConnected) {
      setUserAddress('******************************************');
      setTokenBalances([
        { symbol: 'KOZYR', balance: 15420.5, value: 7710.25, change24h: 12.5 },
        { symbol: 'ETH', balance: 2.45, value: 4900, change24h: -3.2 },
        { symbol: 'USDC', balance: 1250, value: 1250, change24h: 0.1 }
      ]);
      
      setNftCollection([
        {
          id: '1',
          name: 'Legendary King',
          image: '👑',
          rarity: 'legendary',
          power: 95,
          price: 2.5,
          isStaked: true
        },
        {
          id: '2',
          name: 'Epic Queen',
          image: '👸',
          rarity: 'epic',
          power: 85,
          price: 1.2,
          isStaked: false
        },
        {
          id: '3',
          name: 'Rare Ace',
          image: '🃏',
          rarity: 'rare',
          power: 75,
          price: 0.8,
          isStaked: true
        }
      ]);

      setDefiPools([
        {
          id: '1',
          name: 'KOZYR/ETH',
          apr: 145.2,
          tvl: 2500000,
          userStaked: 1500,
          rewards: 25.4
        },
        {
          id: '2',
          name: 'KOZYR/USDC',
          apr: 89.7,
          tvl: 1800000,
          userStaked: 800,
          rewards: 12.1
        }
      ]);

      setTotalPortfolioValue(13860.25);
    }
  }, [walletConnected]);

  const connectWallet = async () => {
    // Симуляция подключения кошелька
    await new Promise(resolve => setTimeout(resolve, 1500));
    setWalletConnected(true);
    onConnectWallet();
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return '#ffd700';
      case 'epic': return '#9370db';
      case 'rare': return '#4a90e2';
      default: return '#6b7280';
    }
  };

  return (
    <Web3Container>
      <ContentWrapper>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <SectionTitle>Web3 Экосистема</SectionTitle>
          <SectionSubtitle>
            Децентрализованное будущее карточных игр
          </SectionSubtitle>
        </motion.div>

        {!walletConnected ? (
          <WalletConnection>
            <ConnectionCard
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
            >
              <ConnectionIcon>🔗</ConnectionIcon>
              <ConnectionTitle>Подключите кошелёк</ConnectionTitle>
              <ConnectionDescription>
                Подключите Web3 кошелёк для доступа к NFT, DeFi и DAO функциям
              </ConnectionDescription>
              
              <WalletOptions>
                <WalletOption
                  onClick={connectWallet}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <WalletIcon>🦊</WalletIcon>
                  <WalletName>MetaMask</WalletName>
                </WalletOption>
                
                <WalletOption
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <WalletIcon>🌈</WalletIcon>
                  <WalletName>Rainbow</WalletName>
                </WalletOption>
                
                <WalletOption
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <WalletIcon>💙</WalletIcon>
                  <WalletName>Coinbase</WalletName>
                </WalletOption>
              </WalletOptions>
            </ConnectionCard>
          </WalletConnection>
        ) : (
          <DashboardContent>
            {/* Заголовок с адресом */}
            <DashboardHeader>
              <UserInfo>
                <UserAvatar>👤</UserAvatar>
                <UserDetails>
                  <UserAddress>{userAddress.slice(0, 6)}...{userAddress.slice(-4)}</UserAddress>
                  <PortfolioValue>${totalPortfolioValue.toLocaleString()}</PortfolioValue>
                </UserDetails>
              </UserInfo>
              
              <NetworkInfo>
                <NetworkIndicator />
                <NetworkName>Ethereum Mainnet</NetworkName>
              </NetworkInfo>
            </DashboardHeader>

            {/* Навигация по табам */}
            <TabNavigation>
              {[
                { id: 'wallet', label: 'Кошелёк', icon: '💰' },
                { id: 'nft', label: 'NFT', icon: '🎴' },
                { id: 'defi', label: 'DeFi', icon: '🏦' },
                { id: 'dao', label: 'DAO', icon: '🗳️' }
              ].map(tab => (
                <TabButton
                  key={tab.id}
                  active={activeTab === tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <TabIcon>{tab.icon}</TabIcon>
                  <TabLabel>{tab.label}</TabLabel>
                </TabButton>
              ))}
            </TabNavigation>

            {/* Контент табов */}
            <TabContent>
              <AnimatePresence mode="wait">
                {activeTab === 'wallet' && (
                  <motion.div
                    key="wallet"
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -50 }}
                    transition={{ duration: 0.3 }}
                  >
                    <TokenGrid>
                      {tokenBalances.map(token => (
                        <TokenCard key={token.symbol}>
                          <TokenHeader>
                            <TokenSymbol>{token.symbol}</TokenSymbol>
                            <TokenChange positive={token.change24h > 0}>
                              {token.change24h > 0 ? '↗' : '↘'} {Math.abs(token.change24h)}%
                            </TokenChange>
                          </TokenHeader>
                          <TokenBalance>{token.balance.toLocaleString()}</TokenBalance>
                          <TokenValue>${token.value.toLocaleString()}</TokenValue>
                        </TokenCard>
                      ))}
                    </TokenGrid>
                  </motion.div>
                )}

                {activeTab === 'nft' && (
                  <motion.div
                    key="nft"
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -50 }}
                    transition={{ duration: 0.3 }}
                  >
                    <NFTGrid>
                      {nftCollection.map(nft => (
                        <NFTCard key={nft.id} rarity={nft.rarity}>
                          <NFTImage>{nft.image}</NFTImage>
                          <NFTInfo>
                            <NFTName>{nft.name}</NFTName>
                            <NFTRarity rarity={nft.rarity}>
                              {nft.rarity.toUpperCase()}
                            </NFTRarity>
                            <NFTPower>⚡ {nft.power}</NFTPower>
                            <NFTPrice>{nft.price} ETH</NFTPrice>
                          </NFTInfo>
                          {nft.isStaked && <StakedBadge>STAKED</StakedBadge>}
                        </NFTCard>
                      ))}
                    </NFTGrid>
                  </motion.div>
                )}

                {activeTab === 'defi' && (
                  <motion.div
                    key="defi"
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -50 }}
                    transition={{ duration: 0.3 }}
                  >
                    <DeFiGrid>
                      {defiPools.map(pool => (
                        <PoolCard key={pool.id}>
                          <PoolHeader>
                            <PoolName>{pool.name}</PoolName>
                            <PoolAPR>{pool.apr}% APR</PoolAPR>
                          </PoolHeader>
                          <PoolStats>
                            <PoolStat>
                              <PoolStatLabel>TVL</PoolStatLabel>
                              <PoolStatValue>${(pool.tvl / 1000000).toFixed(1)}M</PoolStatValue>
                            </PoolStat>
                            <PoolStat>
                              <PoolStatLabel>Ваш стейк</PoolStatLabel>
                              <PoolStatValue>${pool.userStaked}</PoolStatValue>
                            </PoolStat>
                            <PoolStat>
                              <PoolStatLabel>Награды</PoolStatLabel>
                              <PoolStatValue>{pool.rewards} KOZYR</PoolStatValue>
                            </PoolStat>
                          </PoolStats>
                          <PoolActions>
                            <PoolButton
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                            >
                              Добавить
                            </PoolButton>
                            <PoolButton
                              secondary
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                            >
                              Забрать
                            </PoolButton>
                          </PoolActions>
                        </PoolCard>
                      ))}
                    </DeFiGrid>
                  </motion.div>
                )}

                {activeTab === 'dao' && (
                  <motion.div
                    key="dao"
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -50 }}
                    transition={{ duration: 0.3 }}
                  >
                    <DAOSection>
                      <DAOStats>
                        <DAOStat>
                          <DAOStatValue>15,420</DAOStatValue>
                          <DAOStatLabel>Ваша voting power</DAOStatLabel>
                        </DAOStat>
                        <DAOStat>
                          <DAOStatValue>7</DAOStatValue>
                          <DAOStatLabel>Активных предложений</DAOStatLabel>
                        </DAOStat>
                        <DAOStat>
                          <DAOStatValue>89%</DAOStatValue>
                          <DAOStatLabel>Участие в голосовании</DAOStatLabel>
                        </DAOStat>
                      </DAOStats>
                      
                      <ProposalsList>
                        <ProposalCard>
                          <ProposalTitle>Добавить новую игру: Блэкджек</ProposalTitle>
                          <ProposalDescription>
                            Предложение о добавлении блэкджека в платформу
                          </ProposalDescription>
                          <ProposalVotes>
                            <VoteOption>
                              <VoteLabel>За: 85%</VoteLabel>
                              <VoteBar width={85} />
                            </VoteOption>
                            <VoteOption>
                              <VoteLabel>Против: 15%</VoteLabel>
                              <VoteBar width={15} />
                            </VoteOption>
                          </ProposalVotes>
                          <ProposalActions>
                            <VoteButton
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                            >
                              Голосовать ЗА
                            </VoteButton>
                            <VoteButton
                              secondary
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                            >
                              Голосовать ПРОТИВ
                            </VoteButton>
                          </ProposalActions>
                        </ProposalCard>
                      </ProposalsList>
                    </DAOSection>
                  </motion.div>
                )}
              </AnimatePresence>
            </TabContent>
          </DashboardContent>
        )}
      </ContentWrapper>
    </Web3Container>
  );
};

// Стилизованные компоненты (первая часть)
const Web3Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #16213e 0%, #0f0f23 50%, #1a1a2e 100%);
  padding: 4rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ContentWrapper = styled.div`
  max-width: 1400px;
  width: 100%;
`;

const SectionTitle = styled.h2`
  font-size: 3.5rem;
  font-weight: 900;
  text-align: center;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  
  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const SectionSubtitle = styled.p`
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin-bottom: 4rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
`;

const WalletConnection = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
`;

const ConnectionCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-width: 500px;
  width: 100%;
`;

const ConnectionIcon = styled.div`
  font-size: 4rem;
  margin-bottom: 1.5rem;
`;

const ConnectionTitle = styled.h3`
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
`;

const ConnectionDescription = styled.p`
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2rem;
  line-height: 1.6;
`;

const WalletOptions = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  
  @media (max-width: 640px) {
    grid-template-columns: 1fr;
  }
`;

const WalletOption = styled(motion.button)`
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 1.5rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: #4a90e2;
  }
`;

const WalletIcon = styled.div`
  font-size: 2rem;
  margin-bottom: 0.5rem;
`;

const WalletName = styled.div`
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
`;

// Стилизованные компоненты (продолжение)
const DashboardContent = styled.div``;

const DashboardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const UserAvatar = styled.div`
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #4a90e2, #7b68ee);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
`;

const UserDetails = styled.div``;

const UserAddress = styled.div`
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
`;

const PortfolioValue = styled.div`
  color: #4ade80;
  font-weight: 700;
  font-size: 1.3rem;
`;

const NetworkInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const NetworkIndicator = styled.div`
  width: 12px;
  height: 12px;
  background: #4ade80;
  border-radius: 50%;
  animation: pulse 2s infinite;
`;

const NetworkName = styled.div`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
`;

const TabNavigation = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 0.5rem;
`;

const TabButton = styled(motion.button)<{ active: boolean }>`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  border: none;
  background: ${props => props.active ? 'rgba(74, 144, 226, 0.3)' : 'transparent'};
  color: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.7)'};
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;

  &:hover {
    background: rgba(74, 144, 226, 0.1);
    color: #4a90e2;
  }
`;

const TabIcon = styled.span`
  font-size: 1.2rem;
`;

const TabLabel = styled.span`
  font-size: 0.9rem;
`;

const TabContent = styled.div`
  min-height: 400px;
`;

const TokenGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
`;

const TokenCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const TokenHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

const TokenSymbol = styled.div`
  color: white;
  font-weight: 700;
  font-size: 1.2rem;
`;

const TokenChange = styled.div<{ positive: boolean }>`
  color: ${props => props.positive ? '#4ade80' : '#ef4444'};
  font-weight: 600;
  font-size: 0.9rem;
`;

const TokenBalance = styled.div`
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
`;

const TokenValue = styled.div`
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
`;

const NFTGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
`;

const NFTCard = styled.div<{ rarity: string }>`
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
  border: 2px solid ${props => getRarityColor(props.rarity)};
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, ${props => getRarityColor(props.rarity)}20, transparent);
    pointer-events: none;
  }
`;

const NFTImage = styled.div`
  font-size: 4rem;
  text-align: center;
  margin-bottom: 1rem;
  filter: drop-shadow(0 0 20px currentColor);
`;

const NFTInfo = styled.div`
  position: relative;
  z-index: 1;
`;

const NFTName = styled.div`
  color: white;
  font-weight: 700;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
`;

const NFTRarity = styled.div<{ rarity: string }>`
  color: ${props => getRarityColor(props.rarity)};
  font-weight: 600;
  font-size: 0.8rem;
  text-transform: uppercase;
  margin-bottom: 0.5rem;
`;

const NFTPower = styled.div`
  color: #fbbf24;
  font-weight: 600;
  margin-bottom: 0.5rem;
`;

const NFTPrice = styled.div`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
`;

const StakedBadge = styled.div`
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #4ade80;
  color: black;
  padding: 0.25rem 0.5rem;
  border-radius: 5px;
  font-size: 0.7rem;
  font-weight: 700;
`;

const DeFiGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
`;

const PoolCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const PoolHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const PoolName = styled.div`
  color: white;
  font-weight: 700;
  font-size: 1.2rem;
`;

const PoolAPR = styled.div`
  color: #4ade80;
  font-weight: 700;
  font-size: 1.1rem;
`;

const PoolStats = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const PoolStat = styled.div`
  text-align: center;
`;

const PoolStatLabel = styled.div`
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
  margin-bottom: 0.25rem;
`;

const PoolStatValue = styled.div`
  color: white;
  font-weight: 600;
`;

const PoolActions = styled.div`
  display: flex;
  gap: 1rem;
`;

const PoolButton = styled(motion.button)<{ secondary?: boolean }>`
  flex: 1;
  background: ${props => props.secondary
    ? 'transparent'
    : 'linear-gradient(135deg, #4a90e2, #7b68ee)'};
  color: white;
  border: ${props => props.secondary ? '1px solid rgba(255, 255, 255, 0.3)' : 'none'};
  border-radius: 8px;
  padding: 0.75rem;
  font-weight: 600;
  cursor: pointer;
`;

const DAOSection = styled.div``;

const DAOStats = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const DAOStat = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const DAOStatValue = styled.div`
  color: #4a90e2;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
`;

const DAOStatLabel = styled.div`
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
`;

const ProposalsList = styled.div``;

const ProposalCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const ProposalTitle = styled.h4`
  color: white;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
`;

const ProposalDescription = styled.p`
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1.5rem;
  line-height: 1.5;
`;

const ProposalVotes = styled.div`
  margin-bottom: 1.5rem;
`;

const VoteOption = styled.div`
  margin-bottom: 1rem;
`;

const VoteLabel = styled.div`
  color: white;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
`;

const VoteBar = styled.div<{ width: number }>`
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;

  &::after {
    content: '';
    display: block;
    width: ${props => props.width}%;
    height: 100%;
    background: linear-gradient(90deg, #4a90e2, #7b68ee);
    transition: width 0.8s ease;
  }
`;

const ProposalActions = styled.div`
  display: flex;
  gap: 1rem;
`;

const VoteButton = styled(motion.button)<{ secondary?: boolean }>`
  flex: 1;
  background: ${props => props.secondary
    ? 'transparent'
    : 'linear-gradient(135deg, #4a90e2, #7b68ee)'};
  color: white;
  border: ${props => props.secondary ? '1px solid rgba(255, 255, 255, 0.3)' : 'none'};
  border-radius: 8px;
  padding: 0.75rem;
  font-weight: 600;
  cursor: pointer;
`;

// Вспомогательная функция
const getRarityColor = (rarity: string) => {
  switch (rarity) {
    case 'legendary': return '#ffd700';
    case 'epic': return '#9370db';
    case 'rare': return '#4a90e2';
    default: return '#6b7280';
  }
};

export default Web3Dashboard;
