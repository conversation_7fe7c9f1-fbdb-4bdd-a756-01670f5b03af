import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Line, Doughnut, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement
);

interface PlayerStats {
  rating: number;
  rank: string;
  gamesPlayed: number;
  gamesWon: number;
  winRate: number;
  currentStreak: number;
  longestStreak: number;
  totalPlaytime: number;
  favoriteGame: string;
  ratingHistory: Array<{ date: string; rating: number; game: string }>;
  gameStats: Record<string, {
    played: number;
    won: number;
    winRate: number;
    averageGameTime: number;
    bestStreak: number;
  }>;
  achievements: Array<{
    id: string;
    name: string;
    description: string;
    icon: string;
    rarity: string;
    unlockedAt: Date;
  }>;
  recentGames: Array<{
    id: string;
    game: string;
    result: 'win' | 'loss' | 'draw';
    opponent: string;
    duration: number;
    ratingChange: number;
    playedAt: Date;
  }>;
}

const StatsContainer = styled.div`
  padding: 20px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 15px;
  color: white;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const StatCard = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const StatTitle = styled.h3`
  margin: 0 0 15px 0;
  font-size: 18px;
  color: #4a90e2;
`;

const StatValue = styled.div`
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 5px;
`;

const StatLabel = styled.div`
  font-size: 14px;
  opacity: 0.7;
`;

const RatingCard = styled(StatCard)`
  background: linear-gradient(135deg, #ffd700 0%, #ffb300 100%);
  color: #333;
`;

const RankBadge = styled.div`
  display: inline-block;
  background: rgba(0, 0, 0, 0.2);
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
  margin-top: 10px;
`;

const ChartContainer = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const TabContainer = styled.div`
  display: flex;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 5px;
`;

const Tab = styled.button<{ active: boolean }>`
  flex: 1;
  padding: 10px 15px;
  border: none;
  background: ${props => props.active ? 'rgba(74, 144, 226, 0.3)' : 'transparent'};
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background: rgba(74, 144, 226, 0.2);
  }
`;

const GameStatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
`;

const GameStatCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const GameName = styled.h4`
  margin: 0 0 10px 0;
  color: #4a90e2;
`;

const GameStatRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
`;

const RecentGamesContainer = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const GameRow = styled.div<{ result: 'win' | 'loss' | 'draw' }>`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 5px;
  background: ${props => {
    switch (props.result) {
      case 'win': return 'rgba(76, 175, 80, 0.1)';
      case 'loss': return 'rgba(244, 67, 54, 0.1)';
      case 'draw': return 'rgba(255, 193, 7, 0.1)';
    }
  }};
  border-left: 3px solid ${props => {
    switch (props.result) {
      case 'win': return '#4caf50';
      case 'loss': return '#f44336';
      case 'draw': return '#ffc107';
    }
  }};
`;

const RatingChange = styled.span<{ change: number }>`
  color: ${props => props.change > 0 ? '#4caf50' : props.change < 0 ? '#f44336' : '#ffc107'};
  font-weight: bold;
`;

interface PlayerStatsProps {
  playerId: string;
}

export const PlayerStats: React.FC<PlayerStatsProps> = ({ playerId }) => {
  const [stats, setStats] = useState<PlayerStats | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'games' | 'history'>('overview');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPlayerStats();
  }, [playerId]);

  const fetchPlayerStats = async () => {
    try {
      // Здесь будет запрос к API
      // const response = await fetch(`/api/players/${playerId}/stats`);
      // const data = await response.json();
      
      // Пока используем моковые данные
      const mockStats: PlayerStats = {
        rating: 1247,
        rank: 'Silver III',
        gamesPlayed: 156,
        gamesWon: 89,
        winRate: 57.1,
        currentStreak: 3,
        longestStreak: 12,
        totalPlaytime: 45600, // в секундах
        favoriteGame: 'Durak',
        ratingHistory: [
          { date: '2024-01-01', rating: 1000, game: 'Durak' },
          { date: '2024-01-15', rating: 1050, game: 'Poker' },
          { date: '2024-02-01', rating: 1120, game: 'Durak' },
          { date: '2024-02-15', rating: 1180, game: 'Poker' },
          { date: '2024-03-01', rating: 1247, game: 'Durak' },
        ],
        gameStats: {
          'Durak': {
            played: 89,
            won: 52,
            winRate: 58.4,
            averageGameTime: 420,
            bestStreak: 8
          },
          'Poker': {
            played: 45,
            won: 23,
            winRate: 51.1,
            averageGameTime: 1200,
            bestStreak: 5
          },
          'Preferans': {
            played: 22,
            won: 14,
            winRate: 63.6,
            averageGameTime: 1800,
            bestStreak: 4
          }
        },
        achievements: [],
        recentGames: [
          {
            id: '1',
            game: 'Durak',
            result: 'win',
            opponent: 'Player123',
            duration: 380,
            ratingChange: 15,
            playedAt: new Date('2024-03-01T10:30:00')
          },
          {
            id: '2',
            game: 'Poker',
            result: 'loss',
            opponent: 'PokerPro',
            duration: 1450,
            ratingChange: -12,
            playedAt: new Date('2024-02-28T15:20:00')
          },
          {
            id: '3',
            game: 'Durak',
            result: 'win',
            opponent: 'CardMaster',
            duration: 290,
            ratingChange: 18,
            playedAt: new Date('2024-02-28T14:10:00')
          }
        ]
      };
      
      setStats(mockStats);
      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch player stats:', error);
      setLoading(false);
    }
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return <StatsContainer>Loading stats...</StatsContainer>;
  }

  if (!stats) {
    return <StatsContainer>Failed to load stats</StatsContainer>;
  }

  const ratingChartData = {
    labels: stats.ratingHistory.map(h => new Date(h.date).toLocaleDateString()),
    datasets: [
      {
        label: 'Rating',
        data: stats.ratingHistory.map(h => h.rating),
        borderColor: '#4a90e2',
        backgroundColor: 'rgba(74, 144, 226, 0.1)',
        tension: 0.4,
        fill: true,
      },
    ],
  };

  const gameDistributionData = {
    labels: Object.keys(stats.gameStats),
    datasets: [
      {
        data: Object.values(stats.gameStats).map(g => g.played),
        backgroundColor: ['#4a90e2', '#e74c3c', '#f39c12', '#2ecc71'],
        borderWidth: 2,
        borderColor: '#fff',
      },
    ],
  };

  const winRateData = {
    labels: Object.keys(stats.gameStats),
    datasets: [
      {
        label: 'Win Rate (%)',
        data: Object.values(stats.gameStats).map(g => g.winRate),
        backgroundColor: 'rgba(74, 144, 226, 0.6)',
        borderColor: '#4a90e2',
        borderWidth: 2,
      },
    ],
  };

  return (
    <StatsContainer>
      <TabContainer>
        <Tab active={activeTab === 'overview'} onClick={() => setActiveTab('overview')}>
          Overview
        </Tab>
        <Tab active={activeTab === 'games'} onClick={() => setActiveTab('games')}>
          Games
        </Tab>
        <Tab active={activeTab === 'history'} onClick={() => setActiveTab('history')}>
          History
        </Tab>
      </TabContainer>

      {activeTab === 'overview' && (
        <>
          <StatsGrid>
            <RatingCard>
              <StatTitle>Current Rating</StatTitle>
              <StatValue>{stats.rating}</StatValue>
              <RankBadge>{stats.rank}</RankBadge>
            </RatingCard>

            <StatCard>
              <StatTitle>Games Played</StatTitle>
              <StatValue>{stats.gamesPlayed}</StatValue>
              <StatLabel>Total matches</StatLabel>
            </StatCard>

            <StatCard>
              <StatTitle>Win Rate</StatTitle>
              <StatValue>{stats.winRate}%</StatValue>
              <StatLabel>{stats.gamesWon} wins out of {stats.gamesPlayed}</StatLabel>
            </StatCard>

            <StatCard>
              <StatTitle>Current Streak</StatTitle>
              <StatValue>{stats.currentStreak}</StatValue>
              <StatLabel>Best: {stats.longestStreak} games</StatLabel>
            </StatCard>

            <StatCard>
              <StatTitle>Total Playtime</StatTitle>
              <StatValue>{formatTime(stats.totalPlaytime)}</StatValue>
              <StatLabel>Time spent playing</StatLabel>
            </StatCard>

            <StatCard>
              <StatTitle>Favorite Game</StatTitle>
              <StatValue>{stats.favoriteGame}</StatValue>
              <StatLabel>Most played</StatLabel>
            </StatCard>
          </StatsGrid>

          <ChartContainer>
            <StatTitle>Rating Progress</StatTitle>
            <Line data={ratingChartData} options={{
              responsive: true,
              plugins: {
                legend: { display: false },
              },
              scales: {
                y: { beginAtZero: false },
              },
            }} />
          </ChartContainer>
        </>
      )}

      {activeTab === 'games' && (
        <>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>
            <ChartContainer>
              <StatTitle>Games Distribution</StatTitle>
              <Doughnut data={gameDistributionData} options={{
                responsive: true,
                plugins: {
                  legend: { position: 'bottom' },
                },
              }} />
            </ChartContainer>

            <ChartContainer>
              <StatTitle>Win Rate by Game</StatTitle>
              <Bar data={winRateData} options={{
                responsive: true,
                plugins: {
                  legend: { display: false },
                },
                scales: {
                  y: { beginAtZero: true, max: 100 },
                },
              }} />
            </ChartContainer>
          </div>

          <GameStatsGrid>
            {Object.entries(stats.gameStats).map(([game, gameStats]) => (
              <GameStatCard key={game}>
                <GameName>{game}</GameName>
                <GameStatRow>
                  <span>Games Played:</span>
                  <span>{gameStats.played}</span>
                </GameStatRow>
                <GameStatRow>
                  <span>Win Rate:</span>
                  <span>{gameStats.winRate}%</span>
                </GameStatRow>
                <GameStatRow>
                  <span>Avg. Game Time:</span>
                  <span>{formatDuration(gameStats.averageGameTime)}</span>
                </GameStatRow>
                <GameStatRow>
                  <span>Best Streak:</span>
                  <span>{gameStats.bestStreak}</span>
                </GameStatRow>
              </GameStatCard>
            ))}
          </GameStatsGrid>
        </>
      )}

      {activeTab === 'history' && (
        <RecentGamesContainer>
          <StatTitle>Recent Games</StatTitle>
          <div style={{ marginBottom: '10px', fontWeight: 'bold', display: 'grid', gridTemplateColumns: '1fr 1fr 1fr 1fr 1fr', gap: '15px' }}>
            <span>Game</span>
            <span>Opponent</span>
            <span>Result</span>
            <span>Duration</span>
            <span>Rating Change</span>
          </div>
          {stats.recentGames.map(game => (
            <GameRow key={game.id} result={game.result}>
              <span>{game.game}</span>
              <span>{game.opponent}</span>
              <span style={{ textTransform: 'capitalize' }}>{game.result}</span>
              <span>{formatDuration(game.duration)}</span>
              <RatingChange change={game.ratingChange}>
                {game.ratingChange > 0 ? '+' : ''}{game.ratingChange}
              </RatingChange>
            </GameRow>
          ))}
        </RecentGamesContainer>
      )}
    </StatsContainer>
  );
};
