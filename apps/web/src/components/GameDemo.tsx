import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Canvas, useFrame } from '@react-three/fiber';
import { Text3D, Float, OrbitControls } from '@react-three/drei';
import styled from 'styled-components';
import * as THREE from 'three';

interface Card {
  id: string;
  suit: '♠️' | '♥️' | '♦️' | '♣️';
  value: string;
  color: 'red' | 'black';
  isRevealed: boolean;
  position: { x: number; y: number; z: number };
}

interface GameDemoProps {
  onStartGame?: () => void;
}

// 3D карта компонент
const Card3D: React.FC<{ card: Card; onClick: () => void; isHovered: boolean }> = ({ 
  card, onClick, isHovered 
}) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = isHovered 
        ? Math.sin(state.clock.elapsedTime * 2) * 0.1 
        : 0;
      meshRef.current.position.y = isHovered 
        ? card.position.y + Math.sin(state.clock.elapsedTime * 4) * 0.05 
        : card.position.y;
    }
  });

  return (
    <Float speed={2} rotationIntensity={0.5} floatIntensity={0.3}>
      <mesh
        ref={meshRef}
        position={[card.position.x, card.position.y, card.position.z]}
        onClick={onClick}
        onPointerOver={(e) => e.stopPropagation()}
      >
        <boxGeometry args={[0.8, 1.2, 0.05]} />
        <meshStandardMaterial
          color={card.isRevealed ? 'white' : '#1a4480'}
          metalness={0.3}
          roughness={0.4}
        />
        
        {card.isRevealed && (
          <Text3D
            font="/fonts/helvetiker_regular.typeface.json"
            size={0.2}
            height={0.01}
            position={[-0.2, 0, 0.026]}
          >
            {card.value} {card.suit}
            <meshStandardMaterial color={card.color === 'red' ? '#dc2626' : '#1f2937'} />
          </Text3D>
        )}
      </mesh>
    </Float>
  );
};

// Игровой стол 3D
const GameTable3D: React.FC = () => {
  return (
    <group>
      {/* Стол */}
      <mesh position={[0, -0.5, 0]}>
        <cylinderGeometry args={[3, 3, 0.1, 32]} />
        <meshStandardMaterial color="#0f4c3a" />
      </mesh>
      
      {/* Фетр */}
      <mesh position={[0, -0.44, 0]}>
        <cylinderGeometry args={[2.9, 2.9, 0.02, 32]} />
        <meshStandardMaterial color="#1a5d4a" />
      </mesh>
    </group>
  );
};

const GameDemo: React.FC<GameDemoProps> = ({ onStartGame }) => {
  const [cards, setCards] = useState<Card[]>([]);
  const [gameState, setGameState] = useState<'waiting' | 'dealing' | 'playing' | 'finished'>('waiting');
  const [score, setScore] = useState({ player: 0, ai: 0 });
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const [aiThinking, setAiThinking] = useState(false);
  const [gameStats, setGameStats] = useState({
    cardsPlayed: 0,
    winStreak: 0,
    totalGames: 0,
    aiPredictionAccuracy: 0.85
  });

  // Создание колоды карт
  const createDeck = (): Card[] => {
    const suits: Array<'♠️' | '♥️' | '♦️' | '♣️'> = ['♠️', '♥️', '♦️', '♣️'];
    const values = ['6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    const deck: Card[] = [];

    suits.forEach(suit => {
      values.forEach(value => {
        deck.push({
          id: `${suit}-${value}`,
          suit,
          value,
          color: suit === '♥️' || suit === '♦️' ? 'red' : 'black',
          isRevealed: false,
          position: { x: 0, y: 0, z: 0 }
        });
      });
    });

    return deck.sort(() => Math.random() - 0.5);
  };

  // Раздача карт
  const dealCards = async () => {
    setGameState('dealing');
    const deck = createDeck();
    const playerCards = deck.slice(0, 6);
    const aiCards = deck.slice(6, 12);

    // Позиционирование карт игрока
    playerCards.forEach((card, index) => {
      card.position = {
        x: (index - 2.5) * 0.9,
        y: -1.5,
        z: 0
      };
      card.isRevealed = true;
    });

    // Позиционирование карт ИИ
    aiCards.forEach((card, index) => {
      card.position = {
        x: (index - 2.5) * 0.9,
        y: 1.5,
        z: 0
      };
    });

    setCards([...playerCards, ...aiCards]);
    
    // Анимация раздачи
    for (let i = 0; i < 12; i++) {
      await new Promise(resolve => setTimeout(resolve, 200));
      setCards(prev => {
        const newCards = [...prev];
        if (newCards[i]) {
          newCards[i].isRevealed = i < 6; // Только карты игрока открыты
        }
        return newCards;
      });
    }

    setGameState('playing');
  };

  // Ход игрока
  const playCard = async (cardId: string) => {
    if (gameState !== 'playing' || aiThinking) return;

    setCards(prev => prev.map(card => 
      card.id === cardId 
        ? { ...card, position: { ...card.position, y: 0, z: 0.5 } }
        : card
    ));

    setGameStats(prev => ({ ...prev, cardsPlayed: prev.cardsPlayed + 1 }));
    
    // ИИ думает
    setAiThinking(true);
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // ИИ играет карту
    const aiCards = cards.filter(card => card.position.y > 0 && !card.isRevealed);
    if (aiCards.length > 0) {
      const aiCard = aiCards[Math.floor(Math.random() * aiCards.length)];
      setCards(prev => prev.map(card => 
        card.id === aiCard.id 
          ? { ...card, position: { ...card.position, y: 0, z: -0.5 }, isRevealed: true }
          : card
      ));
    }
    
    setAiThinking(false);
    
    // Определение победителя хода (упрощенная логика)
    const winner = Math.random() > 0.5 ? 'player' : 'ai';
    setScore(prev => ({
      ...prev,
      [winner]: prev[winner] + 1
    }));

    // Проверка окончания игры
    if (score.player + score.ai >= 5) {
      setGameState('finished');
      setGameStats(prev => ({
        ...prev,
        totalGames: prev.totalGames + 1,
        winStreak: winner === 'player' ? prev.winStreak + 1 : 0
      }));
    }
  };

  // Новая игра
  const startNewGame = () => {
    setCards([]);
    setGameState('waiting');
    setScore({ player: 0, ai: 0 });
    setHoveredCard(null);
    setAiThinking(false);
  };

  return (
    <GameDemoContainer>
      <ContentWrapper>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <SectionTitle>Интерактивная игровая демонстрация</SectionTitle>
          <SectionSubtitle>
            Попробуйте революционный игровой движок в действии
          </SectionSubtitle>
        </motion.div>

        <GameContainer>
          {/* 3D игровое поле */}
          <GameCanvas>
            <Canvas camera={{ position: [0, 2, 8], fov: 60 }}>
              <ambientLight intensity={0.4} />
              <pointLight position={[5, 5, 5]} intensity={1} />
              <pointLight position={[-5, -5, -5]} intensity={0.5} color="#4a90e2" />
              
              <GameTable3D />
              
              {cards.map(card => (
                <Card3D
                  key={card.id}
                  card={card}
                  isHovered={hoveredCard === card.id}
                  onClick={() => {
                    if (card.position.y < 0 && card.isRevealed) {
                      playCard(card.id);
                    }
                  }}
                />
              ))}
              
              <OrbitControls enableZoom={false} enablePan={false} />
            </Canvas>

            {/* Overlay элементы */}
            <GameOverlay>
              {/* Счет */}
              <ScoreBoard>
                <ScoreItem>
                  <ScoreLabel>Игрок</ScoreLabel>
                  <ScoreValue>{score.player}</ScoreValue>
                </ScoreItem>
                <ScoreItem>
                  <ScoreLabel>ИИ</ScoreLabel>
                  <ScoreValue>{score.ai}</ScoreValue>
                </ScoreItem>
              </ScoreBoard>

              {/* Статус ИИ */}
              {aiThinking && (
                <AIStatus
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                >
                  <AIIcon>🧠</AIIcon>
                  <AIText>ИИ анализирует ход...</AIText>
                  <AIProgress />
                </AIStatus>
              )}

              {/* Состояние игры */}
              <GameStatus>
                {gameState === 'waiting' && 'Готов к игре'}
                {gameState === 'dealing' && 'Раздача карт...'}
                {gameState === 'playing' && 'Ваш ход'}
                {gameState === 'finished' && 
                  `Игра окончена! ${score.player > score.ai ? 'Вы победили!' : 'ИИ победил!'}`
                }
              </GameStatus>
            </GameOverlay>
          </GameCanvas>

          {/* Панель управления */}
          <ControlPanel>
            <GameControls>
              {gameState === 'waiting' && (
                <ControlButton
                  primary
                  onClick={dealCards}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  🎴 Начать игру
                </ControlButton>
              )}
              
              {gameState === 'finished' && (
                <ControlButton
                  primary
                  onClick={startNewGame}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  🔄 Новая игра
                </ControlButton>
              )}

              <ControlButton
                onClick={onStartGame}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                🚀 Полная версия
              </ControlButton>
            </GameControls>

            {/* Статистика */}
            <StatsPanel>
              <StatsTitle>Статистика сессии</StatsTitle>
              <StatsList>
                <StatItem>
                  <StatLabel>Карт сыграно:</StatLabel>
                  <StatValue>{gameStats.cardsPlayed}</StatValue>
                </StatItem>
                <StatItem>
                  <StatLabel>Серия побед:</StatLabel>
                  <StatValue>{gameStats.winStreak}</StatValue>
                </StatItem>
                <StatItem>
                  <StatLabel>Всего игр:</StatLabel>
                  <StatValue>{gameStats.totalGames}</StatValue>
                </StatItem>
                <StatItem>
                  <StatLabel>Точность ИИ:</StatLabel>
                  <StatValue>{(gameStats.aiPredictionAccuracy * 100).toFixed(1)}%</StatValue>
                </StatItem>
              </StatsList>
            </StatsPanel>
          </ControlPanel>
        </GameContainer>

        {/* Технические особенности */}
        <TechFeatures>
          <FeatureItem>
            <FeatureIcon>⚛️</FeatureIcon>
            <FeatureText>
              <FeatureTitle>Квантовая случайность</FeatureTitle>
              <FeatureDescription>Истинно случайная раздача карт</FeatureDescription>
            </FeatureText>
          </FeatureItem>
          
          <FeatureItem>
            <FeatureIcon>🧠</FeatureIcon>
            <FeatureText>
              <FeatureTitle>ИИ противник</FeatureTitle>
              <FeatureDescription>Адаптивный искусственный интеллект</FeatureDescription>
            </FeatureText>
          </FeatureItem>
          
          <FeatureItem>
            <FeatureIcon>🎮</FeatureIcon>
            <FeatureText>
              <FeatureTitle>3D графика</FeatureTitle>
              <FeatureDescription>Иммерсивный игровой опыт</FeatureDescription>
            </FeatureText>
          </FeatureItem>
          
          <FeatureItem>
            <FeatureIcon>📊</FeatureIcon>
            <FeatureText>
              <FeatureTitle>Реальная аналитика</FeatureTitle>
              <FeatureDescription>Детальная статистика игры</FeatureDescription>
            </FeatureText>
          </FeatureItem>
        </TechFeatures>
      </ContentWrapper>
    </GameDemoContainer>
  );
};

// Стилизованные компоненты
const GameDemoContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  padding: 4rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ContentWrapper = styled.div`
  max-width: 1400px;
  width: 100%;
`;

const SectionTitle = styled.h2`
  font-size: 3.5rem;
  font-weight: 900;
  text-align: center;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  
  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const SectionSubtitle = styled.p`
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin-bottom: 4rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
`;

const GameContainer = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 4rem;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
`;

const GameCanvas = styled.div`
  position: relative;
  height: 600px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const GameOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
`;

const ScoreBoard = styled.div`
  position: absolute;
  top: 1rem;
  left: 1rem;
  display: flex;
  gap: 1rem;
`;

const ScoreItem = styled.div`
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 0.75rem 1rem;
  text-align: center;
`;

const ScoreLabel = styled.div`
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.25rem;
`;

const ScoreValue = styled.div`
  font-size: 1.5rem;
  font-weight: 700;
  color: #4a90e2;
`;

const AIStatus = styled(motion.div)`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  border: 1px solid rgba(74, 144, 226, 0.3);
`;

const AIIcon = styled.div`
  font-size: 2rem;
  margin-bottom: 0.5rem;
`;

const AIText = styled.div`
  color: white;
  font-weight: 600;
  margin-bottom: 1rem;
`;

const AIProgress = styled.div`
  width: 100px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
  
  &::after {
    content: '';
    display: block;
    width: 30%;
    height: 100%;
    background: linear-gradient(90deg, #4a90e2, #7b68ee);
    animation: progress 1.5s infinite;
  }
  
  @keyframes progress {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(300%); }
  }
`;

const GameStatus = styled.div`
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 0.75rem 1.5rem;
  color: white;
  font-weight: 600;
`;

const ControlPanel = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const GameControls = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const ControlButton = styled(motion.button)<{ primary?: boolean }>`
  background: ${props => props.primary 
    ? 'linear-gradient(135deg, #4a90e2, #7b68ee)' 
    : 'rgba(255, 255, 255, 0.1)'};
  color: white;
  border: ${props => props.primary ? 'none' : '1px solid rgba(255, 255, 255, 0.2)'};
  border-radius: 10px;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: ${props => props.primary 
      ? 'linear-gradient(135deg, #5ba0f2, #8b78fe)' 
      : 'rgba(255, 255, 255, 0.2)'};
  }
`;

const StatsPanel = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const StatsTitle = styled.h4`
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
`;

const StatsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const StatItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const StatLabel = styled.span`
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
`;

const StatValue = styled.span`
  color: #4a90e2;
  font-weight: 600;
`;

const TechFeatures = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  
  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
`;

const FeatureItem = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const FeatureIcon = styled.div`
  font-size: 2rem;
`;

const FeatureText = styled.div``;

const FeatureTitle = styled.div`
  color: white;
  font-weight: 600;
  margin-bottom: 0.25rem;
`;

const FeatureDescription = styled.div`
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
`;

export default GameDemo;
