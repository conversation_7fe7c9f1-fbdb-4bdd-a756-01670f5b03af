import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/router';
import { Navigation } from '../Navigation';

// Мокаем Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Мокаем framer-motion для упрощения тестов
jest.mock('framer-motion', () => {
  const React = require('react');
  return {
    motion: {
      nav: ({ children, ...props }: any) => React.createElement('nav', props, children),
      div: ({ children, ...props }: any) => React.createElement('div', props, children),
      button: ({ children, ...props }: any) => React.createElement('button', props, children),
    },
    AnimatePresence: ({ children }: { children: React.ReactNode }) => React.createElement('div', {}, children),
  };
});

const mockRouter = {
  pathname: '/',
  push: jest.fn(),
};

describe('Navigation', () => {
  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    jest.clearAllMocks();
  });

  it('должен рендериться с логотипом', () => {
    render(<Navigation />);
    
    expect(screen.getByText('Козырь Мастер')).toBeInTheDocument();
    expect(screen.getByText('4.0')).toBeInTheDocument();
  });

  it('должен отображать навигационные элементы', () => {
    render(<Navigation />);
    
    expect(screen.getByText('Главная')).toBeInTheDocument();
    expect(screen.getByText('Игры')).toBeInTheDocument();
    expect(screen.getByText('Метавселенная')).toBeInTheDocument();
    expect(screen.getByText('Турниры')).toBeInTheDocument();
    expect(screen.getByText('Стриминг')).toBeInTheDocument();
    expect(screen.getByText('NFT')).toBeInTheDocument();
    expect(screen.getByText('Профиль')).toBeInTheDocument();
  });

  it('должен отображать кнопки действий', () => {
    render(<Navigation />);
    
    expect(screen.getByText('Кошелёк')).toBeInTheDocument();
    expect(screen.getByText('Играть')).toBeInTheDocument();
  });

  it('должен выделять активный элемент навигации', () => {
    mockRouter.pathname = '/games';
    render(<Navigation />);
    
    // Проверяем, что элемент "Игры" активен
    // В реальном тесте здесь была бы проверка CSS классов или data-testid
    expect(screen.getByText('Игры')).toBeInTheDocument();
  });

  it('должен обрабатывать клики по навигационным элементам', () => {
    render(<Navigation />);
    
    fireEvent.click(screen.getByText('Игры'));
    expect(mockRouter.push).toHaveBeenCalledWith('/games');
    
    fireEvent.click(screen.getByText('Метавселенная'));
    expect(mockRouter.push).toHaveBeenCalledWith('/metaverse');
  });

  it('должен обрабатывать клики по кнопкам действий', () => {
    render(<Navigation />);
    
    fireEvent.click(screen.getByText('Кошелёк'));
    expect(mockRouter.push).toHaveBeenCalledWith('/wallet');
    
    fireEvent.click(screen.getByText('Играть'));
    expect(mockRouter.push).toHaveBeenCalledWith('/play');
  });

  it('должен открывать мобильное меню', () => {
    render(<Navigation />);
    
    // Ищем кнопку мобильного меню (обычно это гамбургер)
    const menuButton = screen.getByRole('button', { name: /menu/i });
    fireEvent.click(menuButton);
    
    // Проверяем, что мобильное меню открылось
    // В реальном компоненте здесь была бы проверка видимости мобильного меню
  });

  it('должен закрывать мобильное меню при клике на элемент', async () => {
    render(<Navigation />);
    
    // Открываем мобильное меню
    const menuButton = screen.getByRole('button', { name: /menu/i });
    fireEvent.click(menuButton);
    
    // Кликаем на элемент навигации в мобильном меню
    fireEvent.click(screen.getByText('Игры'));
    
    // Проверяем, что произошла навигация
    expect(mockRouter.push).toHaveBeenCalledWith('/games');
  });

  it('должен обрабатывать скролл для изменения стиля', () => {
    render(<Navigation />);
    
    // Симулируем скролл
    Object.defineProperty(window, 'scrollY', { value: 100, writable: true });
    fireEvent.scroll(window);
    
    // В реальном тесте здесь была бы проверка изменения стилей навигации
  });

  it('должен корректно обновлять активный элемент при изменении роута', () => {
    const { rerender } = render(<Navigation />);
    
    // Изменяем роут
    mockRouter.pathname = '/tournaments';
    rerender(<Navigation />);
    
    // Проверяем, что активный элемент обновился
    expect(screen.getByText('Турниры')).toBeInTheDocument();
  });

  it('должен отображать иконки для каждого элемента навигации', () => {
    render(<Navigation />);
    
    // Проверяем наличие эмодзи-иконок
    expect(screen.getByText('🏠')).toBeInTheDocument(); // Главная
    expect(screen.getByText('🎮')).toBeInTheDocument(); // Игры
    expect(screen.getByText('🌍')).toBeInTheDocument(); // Метавселенная
    expect(screen.getByText('🏆')).toBeInTheDocument(); // Турниры
    expect(screen.getByText('📺')).toBeInTheDocument(); // Стриминг
    expect(screen.getByText('💎')).toBeInTheDocument(); // NFT
    expect(screen.getByText('👤')).toBeInTheDocument(); // Профиль
  });

  it('должен быть доступным для клавиатурной навигации', () => {
    render(<Navigation />);
    
    const homeLink = screen.getByText('Главная');
    
    // Проверяем, что элемент может получить фокус
    homeLink.focus();
    expect(homeLink).toHaveFocus();
    
    // Симулируем нажатие Enter
    fireEvent.keyDown(homeLink, { key: 'Enter', code: 'Enter' });
    expect(mockRouter.push).toHaveBeenCalledWith('/');
  });

  it('должен корректно работать с пропом currentSection', () => {
    render(<Navigation currentSection={2} />);
    
    // Проверяем, что компонент рендерится с переданной секцией
    expect(screen.getByText('Козырь Мастер')).toBeInTheDocument();
  });

  it('должен обрабатывать ошибки навигации', () => {
    mockRouter.push.mockRejectedValue(new Error('Navigation failed'));
    
    render(<Navigation />);
    
    // Кликаем на элемент навигации
    fireEvent.click(screen.getByText('Игры'));
    
    // Проверяем, что ошибка не ломает компонент
    expect(screen.getByText('Игры')).toBeInTheDocument();
  });
});
