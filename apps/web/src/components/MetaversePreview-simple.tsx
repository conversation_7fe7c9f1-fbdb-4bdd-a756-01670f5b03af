import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';

interface World {
  id: string;
  name: string;
  description: string;
  theme: string;
  color: string;
  environment: string;
  features: string[];
  playerCount: number;
  preview: string;
}

const worlds: World[] = [
  {
    id: 'casino',
    name: 'Королевское казино',
    description: 'Роскошное казино с золотыми столами и кристальными люстрами',
    theme: 'Классика',
    color: '#ffd700',
    environment: 'sunset',
    features: ['Покерные столы', 'VIP зоны', 'Живая музыка', 'Бар'],
    playerCount: 1247,
    preview: '🏰'
  },
  {
    id: 'medieval',
    name: 'Средневековая таверна',
    description: 'Уютная таверна с каменными стенами и горящим камином',
    theme: 'Фантазия',
    color: '#8b4513',
    environment: 'forest',
    features: ['Деревянные столы', 'Камин', 'Бард', 'Эль'],
    playerCount: 892,
    preview: '🏛️'
  },
  {
    id: 'futuristic',
    name: 'Киберпространство',
    description: 'Футуристическая станция с голографическими интерфейсами',
    theme: 'Sci-Fi',
    color: '#00ffff',
    environment: 'city',
    features: ['Голограммы', 'Неон', 'ИИ дилеры', 'Антигравитация'],
    playerCount: 2156,
    preview: '🚀'
  }
];

// Стилизованные компоненты
const MetaverseContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  padding: 4rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ContentWrapper = styled.div`
  max-width: 1400px;
  width: 100%;
`;

const SectionTitle = styled.h2`
  font-size: 3.5rem;
  font-weight: 900;
  text-align: center;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  
  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const SectionSubtitle = styled.p`
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin-bottom: 4rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
`;

const MainContent = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 4rem;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
`;

const PreviewContainer = styled.div`
  position: relative;
  height: 500px;
  border-radius: 20px;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, #1a1a2e, #16213e);
  display: flex;
  align-items: center;
  justify-content: center;
`;

const PreviewPlaceholder = styled.div`
  font-size: 8rem;
  filter: drop-shadow(0 0 30px currentColor);
`;

const WorldInfo = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

const WorldHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const WorldIcon = styled.div`
  font-size: 4rem;
  filter: drop-shadow(0 0 20px currentColor);
`;

const WorldName = styled.h3`
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
`;

const WorldTheme = styled.p`
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
`;

const WorldDescription = styled.p`
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2rem;
`;

const WorldStats = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
`;

const StatItem = styled.div`
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 1rem;
`;

const StatValue = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: #4a90e2;
`;

const StatLabel = styled.div`
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
`;

const FeaturesList = styled.div`
  margin-bottom: 2rem;
`;

const FeaturesTitle = styled.h4`
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
`;

const FeatureItem = styled(motion.div)`
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  margin-bottom: 0.5rem;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 1rem;
`;

const PrimaryButton = styled(motion.button)`
  background: linear-gradient(135deg, #4a90e2, #7b68ee);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
`;

const SecondaryButton = styled(motion.button)`
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
`;

const WorldSelector = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 3rem;
`;

const WorldCard = styled(motion.div)<{ active: boolean; color: string }>`
  background: ${props => props.active ? `${props.color}20` : 'rgba(255, 255, 255, 0.05)'};
  border: 2px solid ${props => props.active ? props.color : 'rgba(255, 255, 255, 0.1)'};
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
`;

const WorldCardIcon = styled.div`
  font-size: 2rem;
  margin-bottom: 0.5rem;
`;

const WorldCardName = styled.div`
  font-size: 1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.5rem;
`;

const WorldCardPlayers = styled.div`
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
`;

const MetaversePreview: React.FC = () => {
  const [selectedWorld, setSelectedWorld] = useState(0);

  return (
    <MetaverseContainer>
      <ContentWrapper>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <SectionTitle>Метавселенная карточных игр</SectionTitle>
          <SectionSubtitle>
            Исследуйте уникальные 3D миры и играйте в иммерсивной среде
          </SectionSubtitle>
        </motion.div>

        <MainContent>
          {/* Превью */}
          <PreviewContainer>
            <PreviewPlaceholder>
              {worlds[selectedWorld].preview}
            </PreviewPlaceholder>
          </PreviewContainer>

          {/* Информация о мире */}
          <WorldInfo>
            <AnimatePresence mode="wait">
              <motion.div
                key={selectedWorld}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.5 }}
              >
                <WorldHeader>
                  <WorldIcon>{worlds[selectedWorld].preview}</WorldIcon>
                  <div>
                    <WorldName>{worlds[selectedWorld].name}</WorldName>
                    <WorldTheme>{worlds[selectedWorld].theme}</WorldTheme>
                  </div>
                </WorldHeader>

                <WorldDescription>
                  {worlds[selectedWorld].description}
                </WorldDescription>

                <WorldStats>
                  <StatItem>
                    <StatValue>{worlds[selectedWorld].playerCount}</StatValue>
                    <StatLabel>Игроков онлайн</StatLabel>
                  </StatItem>
                  <StatItem>
                    <StatValue>{worlds[selectedWorld].features.length}</StatValue>
                    <StatLabel>Уникальных функций</StatLabel>
                  </StatItem>
                  <StatItem>
                    <StatValue>4.9</StatValue>
                    <StatLabel>Рейтинг</StatLabel>
                  </StatItem>
                </WorldStats>

                <FeaturesList>
                  <FeaturesTitle>Особенности мира:</FeaturesTitle>
                  {worlds[selectedWorld].features.map((feature, index) => (
                    <FeatureItem
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      ✨ {feature}
                    </FeatureItem>
                  ))}
                </FeaturesList>

                <ActionButtons>
                  <PrimaryButton
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Войти в мир
                  </PrimaryButton>
                  <SecondaryButton
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Виртуальный тур
                  </SecondaryButton>
                </ActionButtons>
              </motion.div>
            </AnimatePresence>
          </WorldInfo>
        </MainContent>

        {/* Селектор миров */}
        <WorldSelector>
          {worlds.map((world, index) => (
            <WorldCard
              key={world.id}
              active={index === selectedWorld}
              color={world.color}
              onClick={() => setSelectedWorld(index)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <WorldCardIcon>{world.preview}</WorldCardIcon>
              <WorldCardName>{world.name}</WorldCardName>
              <WorldCardPlayers>{world.playerCount} игроков</WorldCardPlayers>
            </WorldCard>
          ))}
        </WorldSelector>
      </ContentWrapper>
    </MetaverseContainer>
  );
};

export default MetaversePreview;
