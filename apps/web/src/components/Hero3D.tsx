import React, { useRef, useEffect, useState } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { Text, OrbitControls, Stars, Float, Sphere, MeshDistortMaterial } from '@react-three/drei';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import * as THREE from 'three';

interface Hero3DProps {
  quantumStatus: any;
  emotionalState: any;
  onStartJourney: () => void;
}

// 3D компонент квантовой сферы
const QuantumSphere: React.FC<{ quantumStatus: any }> = ({ quantumStatus }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = state.clock.elapsedTime * 0.2;
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.3;
      
      // Пульсация в зависимости от квантового статуса
      const scale = quantumStatus?.isQuantumAvailable 
        ? 1 + Math.sin(state.clock.elapsedTime * 2) * 0.1
        : 1 + Math.sin(state.clock.elapsedTime) * 0.05;
      meshRef.current.scale.setScalar(scale);
    }
  });

  return (
    <Float speed={2} rotationIntensity={1} floatIntensity={2}>
      <Sphere
        ref={meshRef}
        args={[1, 64, 64]}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        <MeshDistortMaterial
          color={quantumStatus?.isQuantumAvailable ? "#4a90e2" : "#7b68ee"}
          attach="material"
          distort={0.4}
          speed={2}
          roughness={0.1}
          metalness={0.8}
          emissive={quantumStatus?.isQuantumAvailable ? "#1a4480" : "#3d3470"}
          emissiveIntensity={hovered ? 0.5 : 0.2}
        />
      </Sphere>
    </Float>
  );
};

// 3D текст с анимацией
const AnimatedText: React.FC<{ text: string; position: [number, number, number] }> = ({ text, position }) => {
  const textRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (textRef.current) {
      textRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime) * 0.1;
    }
  });

  return (
    <Text
      ref={textRef}
      position={position}
      fontSize={0.5}
      color="#ffffff"
      anchorX="center"
      anchorY="middle"
      font="/fonts/Inter-Bold.woff"
    >
      {text}
    </Text>
  );
};

// Частицы для фона
const Particles: React.FC = () => {
  const particlesRef = useRef<THREE.Points>(null);
  const particleCount = 1000;

  const positions = new Float32Array(particleCount * 3);
  const colors = new Float32Array(particleCount * 3);

  for (let i = 0; i < particleCount; i++) {
    positions[i * 3] = (Math.random() - 0.5) * 20;
    positions[i * 3 + 1] = (Math.random() - 0.5) * 20;
    positions[i * 3 + 2] = (Math.random() - 0.5) * 20;

    colors[i * 3] = Math.random();
    colors[i * 3 + 1] = Math.random() * 0.5 + 0.5;
    colors[i * 3 + 2] = 1;
  }

  useFrame((state) => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.05;
    }
  });

  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={positions}
          itemSize={3}
        />
        <bufferAttribute
          attach="attributes-color"
          count={particleCount}
          array={colors}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial size={0.02} vertexColors transparent opacity={0.6} />
    </points>
  );
};

const Hero3D: React.FC<Hero3DProps> = ({ quantumStatus, emotionalState, onStartJourney }) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      setMousePosition({
        x: (event.clientX / window.innerWidth) * 2 - 1,
        y: -(event.clientY / window.innerHeight) * 2 + 1
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <HeroContainer>
      {/* 3D Canvas */}
      <CanvasContainer>
        <Canvas camera={{ position: [0, 0, 5], fov: 75 }}>
          <ambientLight intensity={0.3} />
          <pointLight position={[10, 10, 10]} intensity={1} />
          <pointLight position={[-10, -10, -10]} intensity={0.5} color="#7b68ee" />
          
          <Stars radius={100} depth={50} count={5000} factor={4} saturation={0} fade />
          <Particles />
          
          <QuantumSphere quantumStatus={quantumStatus} />
          
          <AnimatedText text="КОЗЫРЬ МАСТЕР 4.0" position={[0, 2, 0]} />
          <AnimatedText text="Революция карточных игр" position={[0, -2, 0]} />
          
          <OrbitControls enableZoom={false} enablePan={false} />
        </Canvas>
      </CanvasContainer>

      {/* Overlay контент */}
      <ContentOverlay>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.5 }}
        >
          <MainTitle>
            <motion.span
              animate={{ 
                backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
              }}
              transition={{ duration: 3, repeat: Infinity }}
              style={{
                background: 'linear-gradient(90deg, #4a90e2, #7b68ee, #9370db, #4a90e2)',
                backgroundSize: '200% 100%',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              КОЗЫРЬ МАСТЕР 4.0
            </motion.span>
          </MainTitle>
          
          <Subtitle>
            Где квантовая случайность встречается с эмоциональным интеллектом
          </Subtitle>

          <FeaturesList>
            <FeatureItem
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1 }}
            >
              🔬 Квантовая честность
            </FeatureItem>
            <FeatureItem
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1.2 }}
            >
              🧠 Эмоциональный ИИ
            </FeatureItem>
            <FeatureItem
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1.4 }}
            >
              🌍 3D Метавселенная
            </FeatureItem>
            <FeatureItem
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1.6 }}
            >
              ⛓️ Web3 экосистема
            </FeatureItem>
          </FeaturesList>

          <ButtonContainer>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <StartButton onClick={onStartJourney}>
                <ButtonGlow />
                <span>Начать путешествие</span>
              </StartButton>
            </motion.div>
            
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <SecondaryButton>
                Смотреть демо
              </SecondaryButton>
            </motion.div>
          </ButtonContainer>
        </motion.div>

        {/* Статус панели */}
        <StatusPanels>
          <StatusPanel>
            <StatusIcon>⚛️</StatusIcon>
            <StatusText>
              <StatusLabel>Квантовый статус</StatusLabel>
              <StatusValue active={quantumStatus?.isQuantumAvailable}>
                {quantumStatus?.isQuantumAvailable ? 'Активен' : 'Инициализация'}
              </StatusValue>
            </StatusText>
          </StatusPanel>

          <StatusPanel>
            <StatusIcon>🧠</StatusIcon>
            <StatusText>
              <StatusLabel>ИИ анализ</StatusLabel>
              <StatusValue active={true}>
                Настроение: {emotionalState?.happiness > 0.7 ? 'Отличное' : 
                           emotionalState?.happiness > 0.4 ? 'Хорошее' : 'Нормальное'}
              </StatusValue>
            </StatusText>
          </StatusPanel>

          <StatusPanel>
            <StatusIcon>🌐</StatusIcon>
            <StatusText>
              <StatusLabel>Игроков онлайн</StatusLabel>
              <StatusValue active={true}>
                {(Math.random() * 50000 + 10000).toFixed(0)}
              </StatusValue>
            </StatusText>
          </StatusPanel>
        </StatusPanels>
      </ContentOverlay>

      {/* Интерактивный курсор */}
      <InteractiveCursor
        style={{
          left: `${(mousePosition.x + 1) * 50}%`,
          top: `${(-mousePosition.y + 1) * 50}%`,
        }}
      />
    </HeroContainer>
  );
};

// Стилизованные компоненты
const HeroContainer = styled.div`
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
`;

const CanvasContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
`;

const ContentOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  pointer-events: none;
  
  > * {
    pointer-events: auto;
  }
`;

const MainTitle = styled.h1`
  font-size: 4rem;
  font-weight: 900;
  text-align: center;
  margin-bottom: 1rem;
  text-shadow: 0 0 30px rgba(74, 144, 226, 0.5);
  
  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 3rem;
  max-width: 600px;
  
  @media (max-width: 768px) {
    font-size: 1.2rem;
  }
`;

const FeaturesList = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 3rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const FeatureItem = styled(motion.div)`
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  padding: 0.5rem;
  text-align: center;
`;

const ButtonContainer = styled.div`
  display: flex;
  gap: 1.5rem;
  margin-bottom: 4rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
`;

const StartButton = styled.button`
  position: relative;
  background: linear-gradient(135deg, #4a90e2, #7b68ee);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 1rem 2.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(74, 144, 226, 0.3);
  
  &:hover {
    box-shadow: 0 15px 40px rgba(74, 144, 226, 0.5);
    transform: translateY(-2px);
  }
`;

const ButtonGlow = styled.div`
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 3s infinite;
  
  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }
`;

const SecondaryButton = styled.button`
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  padding: 1rem 2.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  
  &:hover {
    border-color: #4a90e2;
    background: rgba(74, 144, 226, 0.1);
    transform: translateY(-2px);
  }
`;

const StatusPanels = styled.div`
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
`;

const StatusPanel = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 0.75rem 1rem;
`;

const StatusIcon = styled.div`
  font-size: 1.2rem;
`;

const StatusText = styled.div`
  display: flex;
  flex-direction: column;
`;

const StatusLabel = styled.span`
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
`;

const StatusValue = styled.span<{ active: boolean }>`
  font-size: 0.9rem;
  font-weight: 600;
  color: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.8)'};
`;

const InteractiveCursor = styled.div`
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(74, 144, 226, 0.8), transparent);
  pointer-events: none;
  transition: all 0.1s ease;
  z-index: 5;
`;

export default Hero3D;
