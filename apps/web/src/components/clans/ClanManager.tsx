import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useSocket } from '../../hooks/useSocket';
import { useAuth } from '../../hooks/useAuth';
import { Button } from '../common/Button';
import { Modal } from '../common/Modal';
import { useNotifications } from '../notifications/NotificationSystem';

interface Clan {
  id: string;
  name: string;
  tag: string;
  description: string;
  logoUrl?: string;
  bannerUrl?: string;
  isPublic: boolean;
  maxMembers: number;
  totalMembers: number;
  averageRating: number;
  clanRating: number;
  leader: {
    id: string;
    name: string;
  };
  myRole?: 'leader' | 'officer' | 'veteran' | 'member' | 'recruit';
  createdAt: Date;
}

interface ClanMember {
  id: string;
  user: {
    id: string;
    username: string;
    displayName: string;
    rating: number;
    rank: string;
  };
  role: 'leader' | 'officer' | 'veteran' | 'member' | 'recruit';
  joinedAt: Date;
  contributionPoints: number;
  gamesPlayedForClan: number;
  gamesWonForClan: number;
  isActive: boolean;
  lastActivity: Date;
}

const ClanContainer = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
`;

const ClanHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.2) 0%, rgba(53, 122, 189, 0.2) 100%);
  border-radius: 10px;
`;

const ClanLogo = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: bold;
  margin-right: 20px;
`;

const ClanInfo = styled.div`
  flex: 1;
`;

const ClanName = styled.h2`
  margin: 0 0 5px 0;
  font-size: 28px;
  color: #4a90e2;
`;

const ClanTag = styled.span`
  background: rgba(74, 144, 226, 0.3);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: bold;
  margin-left: 10px;
`;

const ClanDescription = styled.p`
  margin: 10px 0;
  opacity: 0.8;
  line-height: 1.4;
`;

const ClanStats = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
`;

const StatCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const StatValue = styled.div`
  font-size: 24px;
  font-weight: bold;
  color: #4a90e2;
  margin-bottom: 5px;
`;

const StatLabel = styled.div`
  font-size: 12px;
  opacity: 0.7;
`;

const TabContainer = styled.div`
  display: flex;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 5px;
`;

const Tab = styled.button<{ active: boolean }>`
  flex: 1;
  padding: 10px 15px;
  border: none;
  background: ${props => props.active ? 'rgba(74, 144, 226, 0.3)' : 'transparent'};
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background: rgba(74, 144, 226, 0.2);
  }
`;

const MembersGrid = styled.div`
  display: grid;
  gap: 15px;
`;

const MemberCard = styled.div<{ role: ClanMember['role'] }>`
  display: flex;
  align-items: center;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  border-left: 4px solid ${props => {
    switch (props.role) {
      case 'leader': return '#ffd700';
      case 'officer': return '#ff6b6b';
      case 'veteran': return '#4ecdc4';
      case 'member': return '#45b7d1';
      case 'recruit': return '#96ceb4';
      default: return '#666';
    }
  }};
  transition: all 0.2s;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }
`;

const MemberAvatar = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 15px;
`;

const MemberInfo = styled.div`
  flex: 1;
`;

const MemberName = styled.div`
  font-weight: bold;
  margin-bottom: 5px;
`;

const MemberDetails = styled.div`
  font-size: 12px;
  opacity: 0.7;
  display: flex;
  gap: 15px;
`;

const RoleBadge = styled.div<{ role: ClanMember['role'] }>`
  background: ${props => {
    switch (props.role) {
      case 'leader': return 'linear-gradient(135deg, #ffd700 0%, #ffb300 100%)';
      case 'officer': return 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)';
      case 'veteran': return 'linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%)';
      case 'member': return 'linear-gradient(135deg, #45b7d1 0%, #3498db 100%)';
      case 'recruit': return 'linear-gradient(135deg, #96ceb4 0%, #85c1a3 100%)';
      default: return 'linear-gradient(135deg, #666 0%, #444 100%)';
    }
  }};
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
`;

const MemberActions = styled.div`
  display: flex;
  gap: 8px;
`;

const ActionButton = styled.button`
  background: rgba(74, 144, 226, 0.2);
  border: 1px solid rgba(74, 144, 226, 0.3);
  color: white;
  padding: 6px 12px;
  border-radius: 5px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background: rgba(74, 144, 226, 0.3);
  }
`;

const CreateClanForm = styled.form`
  display: grid;
  gap: 15px;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 5px;
`;

const Label = styled.label`
  font-weight: bold;
  color: white;
`;

const Input = styled.input`
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
  
  &:focus {
    outline: none;
    border-color: #4a90e2;
    background: rgba(255, 255, 255, 0.15);
  }
`;

const TextArea = styled.textarea`
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  resize: vertical;
  min-height: 80px;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
  
  &:focus {
    outline: none;
    border-color: #4a90e2;
    background: rgba(255, 255, 255, 0.15);
  }
`;

const NoClanMessage = styled.div`
  text-align: center;
  padding: 40px;
  color: white;
`;

export const ClanManager: React.FC = () => {
  const [clan, setClan] = useState<Clan | null>(null);
  const [members, setMembers] = useState<ClanMember[]>([]);
  const [activeTab, setActiveTab] = useState<'overview' | 'members' | 'events' | 'wars'>('overview');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [loading, setLoading] = useState(true);
  
  const { socket } = useSocket();
  const { user } = useAuth();
  const { showSuccess, showError } = useNotifications();

  useEffect(() => {
    if (!socket || !user) return;

    // Загружаем информацию о клане пользователя
    socket.emit('clan:get_my_clan');

    socket.on('clan:my_clan', (data: { clan: Clan | null; members?: ClanMember[] }) => {
      setClan(data.clan);
      if (data.members) {
        setMembers(data.members);
      }
      setLoading(false);
    });

    socket.on('clan:created', (newClan: Clan) => {
      setClan(newClan);
      showSuccess('Clan Created', 'Your clan has been created successfully!');
      setShowCreateModal(false);
    });

    socket.on('clan:members', (clanMembers: ClanMember[]) => {
      setMembers(clanMembers);
    });

    socket.on('clan:error', (data: { message: string }) => {
      showError('Clan Error', data.message);
    });

    return () => {
      socket.off('clan:my_clan');
      socket.off('clan:created');
      socket.off('clan:members');
      socket.off('clan:error');
    };
  }, [socket, user]);

  const handleCreateClan = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!socket) return;

    const formData = new FormData(e.currentTarget);
    const clanData = {
      name: formData.get('name'),
      tag: formData.get('tag'),
      description: formData.get('description'),
      isPublic: formData.get('isPublic') === 'on',
      maxMembers: parseInt(formData.get('maxMembers') as string) || 50,
    };

    socket.emit('clan:create', clanData);
  };

  const handlePromoteMember = (memberId: string, newRole: string) => {
    if (!socket) return;
    socket.emit('clan:promote_member', { memberId, newRole });
  };

  const handleKickMember = (memberId: string) => {
    if (!socket) return;
    if (confirm('Are you sure you want to kick this member?')) {
      socket.emit('clan:kick_member', { memberId });
    }
  };

  const handleLeaveClan = () => {
    if (!socket) return;
    if (confirm('Are you sure you want to leave the clan?')) {
      socket.emit('clan:leave');
      setClan(null);
      setMembers([]);
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  const canManageMembers = () => {
    return clan?.myRole === 'leader' || clan?.myRole === 'officer';
  };

  if (loading) {
    return (
      <ClanContainer>
        <div style={{ textAlign: 'center', padding: '40px' }}>Loading clan information...</div>
      </ClanContainer>
    );
  }

  if (!clan) {
    return (
      <ClanContainer>
        <NoClanMessage>
          <h3>You're not in a clan</h3>
          <p>Join a clan to compete with other players and participate in clan wars!</p>
          <div style={{ marginTop: '20px', display: 'flex', gap: '15px', justifyContent: 'center' }}>
            <Button onClick={() => setShowCreateModal(true)}>
              Create Clan
            </Button>
            <Button variant="secondary">
              Browse Clans
            </Button>
          </div>
        </NoClanMessage>

        {showCreateModal && (
          <Modal onClose={() => setShowCreateModal(false)} title="Create Clan">
            <CreateClanForm onSubmit={handleCreateClan}>
              <FormGroup>
                <Label>Clan Name</Label>
                <Input name="name" type="text" required placeholder="Enter clan name" maxLength={50} />
              </FormGroup>

              <FormGroup>
                <Label>Clan Tag</Label>
                <Input name="tag" type="text" required placeholder="TAG" maxLength={10} style={{ textTransform: 'uppercase' }} />
              </FormGroup>

              <FormGroup>
                <Label>Description</Label>
                <TextArea name="description" placeholder="Describe your clan" maxLength={500} />
              </FormGroup>

              <FormGroup>
                <Label>Max Members</Label>
                <Input name="maxMembers" type="number" min="10" max="100" defaultValue="50" />
              </FormGroup>

              <FormGroup>
                <Label>
                  <input type="checkbox" name="isPublic" defaultChecked style={{ marginRight: '8px' }} />
                  Public clan (anyone can apply)
                </Label>
              </FormGroup>

              <div style={{ display: 'flex', gap: '10px', marginTop: '20px' }}>
                <Button type="submit" variant="success">
                  Create Clan
                </Button>
                <Button type="button" variant="secondary" onClick={() => setShowCreateModal(false)}>
                  Cancel
                </Button>
              </div>
            </CreateClanForm>
          </Modal>
        )}
      </ClanContainer>
    );
  }

  return (
    <ClanContainer>
      <ClanHeader>
        <ClanLogo>
          {clan.logoUrl ? (
            <img src={clan.logoUrl} alt={clan.name} style={{ width: '100%', height: '100%', borderRadius: '50%' }} />
          ) : (
            getInitials(clan.name)
          )}
        </ClanLogo>
        
        <ClanInfo>
          <div>
            <ClanName>{clan.name}</ClanName>
            <ClanTag>[{clan.tag}]</ClanTag>
          </div>
          <ClanDescription>{clan.description}</ClanDescription>
          <div style={{ fontSize: '14px', opacity: '0.8' }}>
            Leader: {clan.leader.name} • Created: {formatDate(clan.createdAt)}
          </div>
        </ClanInfo>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
          {clan.myRole === 'leader' && (
            <Button size="small">Manage Clan</Button>
          )}
          <Button variant="secondary" size="small" onClick={handleLeaveClan}>
            Leave Clan
          </Button>
        </div>
      </ClanHeader>

      <ClanStats>
        <StatCard>
          <StatValue>{clan.totalMembers}/{clan.maxMembers}</StatValue>
          <StatLabel>Members</StatLabel>
        </StatCard>
        <StatCard>
          <StatValue>{Math.round(clan.averageRating)}</StatValue>
          <StatLabel>Avg Rating</StatLabel>
        </StatCard>
        <StatCard>
          <StatValue>{clan.clanRating}</StatValue>
          <StatLabel>Clan Rating</StatLabel>
        </StatCard>
        <StatCard>
          <StatValue>{clan.isPublic ? 'Public' : 'Private'}</StatValue>
          <StatLabel>Type</StatLabel>
        </StatCard>
      </ClanStats>

      <TabContainer>
        <Tab active={activeTab === 'overview'} onClick={() => setActiveTab('overview')}>
          Overview
        </Tab>
        <Tab active={activeTab === 'members'} onClick={() => setActiveTab('members')}>
          Members ({members.length})
        </Tab>
        <Tab active={activeTab === 'events'} onClick={() => setActiveTab('events')}>
          Events
        </Tab>
        <Tab active={activeTab === 'wars'} onClick={() => setActiveTab('wars')}>
          Wars
        </Tab>
      </TabContainer>

      {activeTab === 'members' && (
        <MembersGrid>
          {members.map(member => (
            <MemberCard key={member.id} role={member.role}>
              <MemberAvatar>
                {getInitials(member.user.displayName)}
              </MemberAvatar>
              
              <MemberInfo>
                <MemberName>{member.user.displayName}</MemberName>
                <MemberDetails>
                  <span>Rating: {member.user.rating}</span>
                  <span>Rank: {member.user.rank}</span>
                  <span>Joined: {formatDate(member.joinedAt)}</span>
                  <span>Games: {member.gamesPlayedForClan}</span>
                </MemberDetails>
              </MemberInfo>
              
              <RoleBadge role={member.role}>{member.role}</RoleBadge>
              
              {canManageMembers() && member.user.id !== user?.id && (
                <MemberActions>
                  {member.role !== 'officer' && clan.myRole === 'leader' && (
                    <ActionButton onClick={() => handlePromoteMember(member.id, 'officer')}>
                      Promote
                    </ActionButton>
                  )}
                  <ActionButton onClick={() => handleKickMember(member.id)}>
                    Kick
                  </ActionButton>
                </MemberActions>
              )}
            </MemberCard>
          ))}
        </MembersGrid>
      )}

      {activeTab === 'overview' && (
        <div>
          <h3>Clan Overview</h3>
          <p>Recent activity, achievements, and clan statistics will be displayed here.</p>
        </div>
      )}

      {activeTab === 'events' && (
        <div>
          <h3>Clan Events</h3>
          <p>Upcoming tournaments, meetings, and training sessions.</p>
        </div>
      )}

      {activeTab === 'wars' && (
        <div>
          <h3>Clan Wars</h3>
          <p>Current and past clan wars, rankings, and war statistics.</p>
        </div>
      )}
    </ClanContainer>
  );
};
