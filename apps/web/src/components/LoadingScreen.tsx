import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components';

interface LoadingScreenProps {
  message?: string;
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({ 
  message = "Инициализация квантовых систем..." 
}) => {
  const [progress, setProgress] = useState(0);
  const [currentMessage, setCurrentMessage] = useState(message);

  const loadingMessages = [
    "Подключение к квантовым источникам...",
    "Инициализация эмоционального ИИ...",
    "Загрузка 3D метавселенной...",
    "Настройка блокчейн соединения...",
    "Активация предиктивной аналитики...",
    "Запуск систем безопасности...",
    "Финализация загрузки..."
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + Math.random() * 15;
        if (newProgress >= 100) {
          clearInterval(interval);
          return 100;
        }
        
        // Обновляем сообщение в зависимости от прогресса
        const messageIndex = Math.floor((newProgress / 100) * loadingMessages.length);
        if (messageIndex < loadingMessages.length) {
          setCurrentMessage(loadingMessages[messageIndex]);
        }
        
        return newProgress;
      });
    }, 200);

    return () => clearInterval(interval);
  }, []);

  return (
    <LoadingContainer>
      {/* Фоновые частицы */}
      <ParticlesBackground>
        {Array.from({ length: 50 }).map((_, i) => (
          <Particle
            key={i}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 4}s`
            }}
          />
        ))}
      </ParticlesBackground>

      {/* Основной контент */}
      <ContentWrapper>
        {/* Логотип с анимацией */}
        <LogoContainer>
          <motion.div
            animate={{ 
              rotate: 360,
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              rotate: { duration: 4, repeat: Infinity, ease: "linear" },
              scale: { duration: 2, repeat: Infinity }
            }}
          >
            <Logo>🎮</Logo>
          </motion.div>
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            Козырь Мастер 4.0
          </motion.h1>
        </LogoContainer>

        {/* Квантовый индикатор */}
        <QuantumIndicator>
          <QuantumRing>
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <QuantumOrb />
            </motion.div>
          </QuantumRing>
          <QuantumText>Квантовая инициализация</QuantumText>
        </QuantumIndicator>

        {/* Прогресс бар */}
        <ProgressContainer>
          <ProgressBar>
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.3 }}
              style={{
                height: '100%',
                background: 'linear-gradient(90deg, #4a90e2, #7b68ee, #9370db)',
                borderRadius: '10px',
                position: 'relative',
                overflow: 'hidden'
              }}
            >
              <ProgressGlow />
            </motion.div>
          </ProgressBar>
          <ProgressText>{Math.round(progress)}%</ProgressText>
        </ProgressContainer>

        {/* Сообщение о загрузке */}
        <motion.div
          key={currentMessage}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.5 }}
        >
          <LoadingMessage>{currentMessage}</LoadingMessage>
        </motion.div>

        {/* Системные индикаторы */}
        <SystemIndicators>
          <SystemIndicator active={progress > 20}>
            <IndicatorDot active={progress > 20} />
            <span>Квантовый движок</span>
          </SystemIndicator>
          <SystemIndicator active={progress > 40}>
            <IndicatorDot active={progress > 40} />
            <span>Эмоциональный ИИ</span>
          </SystemIndicator>
          <SystemIndicator active={progress > 60}>
            <IndicatorDot active={progress > 60} />
            <span>Метавселенная</span>
          </SystemIndicator>
          <SystemIndicator active={progress > 80}>
            <IndicatorDot active={progress > 80} />
            <span>Web3 экосистема</span>
          </SystemIndicator>
        </SystemIndicators>

        {/* Технические детали */}
        <TechDetails>
          <TechItem>
            <TechLabel>Энтропия:</TechLabel>
            <TechValue>{(progress / 100 * 0.999).toFixed(3)}</TechValue>
          </TechItem>
          <TechItem>
            <TechLabel>Квантовые источники:</TechLabel>
            <TechValue>{Math.min(5, Math.floor(progress / 20))}/5</TechValue>
          </TechItem>
          <TechItem>
            <TechLabel>ИИ модели:</TechLabel>
            <TechValue>{Math.min(6, Math.floor(progress / 16))}/6</TechValue>
          </TechItem>
        </TechDetails>
      </ContentWrapper>
    </LoadingContainer>
  );
};

// Стилизованные компоненты
const LoadingContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  overflow: hidden;
`;

const ParticlesBackground = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
`;

const Particle = styled.div`
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(74, 144, 226, 0.6);
  border-radius: 50%;
  animation: float linear infinite;

  @keyframes float {
    0% {
      transform: translateY(100vh) scale(0);
      opacity: 0;
    }
    10% {
      opacity: 1;
    }
    90% {
      opacity: 1;
    }
    100% {
      transform: translateY(-100px) scale(1);
      opacity: 0;
    }
  }
`;

const ContentWrapper = styled.div`
  text-align: center;
  color: white;
  z-index: 1;
`;

const LogoContainer = styled.div`
  margin-bottom: 3rem;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-top: 1rem;
  }
`;

const Logo = styled.div`
  font-size: 4rem;
  margin-bottom: 1rem;
  filter: drop-shadow(0 0 20px rgba(74, 144, 226, 0.5));
`;

const QuantumIndicator = styled.div`
  margin-bottom: 3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const QuantumRing = styled.div`
  width: 80px;
  height: 80px;
  border: 2px solid rgba(74, 144, 226, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 1rem;

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: conic-gradient(from 0deg, transparent, #4a90e2, transparent);
    animation: spin 2s linear infinite;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }
`;

const QuantumOrb = styled.div`
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, #4a90e2, #7b68ee);
  border-radius: 50%;
  box-shadow: 0 0 20px rgba(74, 144, 226, 0.8);
  z-index: 1;
`;

const QuantumText = styled.div`
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
`;

const ProgressContainer = styled.div`
  width: 400px;
  margin-bottom: 2rem;
  
  @media (max-width: 480px) {
    width: 300px;
  }
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 0.5rem;
  position: relative;
`;

const ProgressGlow = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;

  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }
`;

const ProgressText = styled.div`
  font-size: 1.2rem;
  font-weight: 600;
  color: #4a90e2;
`;

const LoadingMessage = styled.div`
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
  min-height: 1.5rem;
`;

const SystemIndicators = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
  max-width: 400px;
  
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
`;

const SystemIndicator = styled.div<{ active: boolean }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.5)'};
  transition: color 0.3s ease;
`;

const IndicatorDot = styled.div<{ active: boolean }>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.3)'};
  box-shadow: ${props => props.active ? '0 0 10px rgba(74, 144, 226, 0.5)' : 'none'};
  transition: all 0.3s ease;
`;

const TechDetails = styled.div`
  display: flex;
  justify-content: center;
  gap: 2rem;
  font-size: 0.8rem;
  
  @media (max-width: 480px) {
    flex-direction: column;
    gap: 0.5rem;
  }
`;

const TechItem = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const TechLabel = styled.span`
  color: rgba(255, 255, 255, 0.6);
`;

const TechValue = styled.span`
  color: #4a90e2;
  font-weight: 600;
`;
