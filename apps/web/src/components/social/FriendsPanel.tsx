import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useSocket } from '../../hooks/useSocket';
import { useAuth } from '../../hooks/useAuth';
import { Button } from '../common/Button';
import { Modal } from '../common/Modal';
import { useNotifications } from '../notifications/NotificationSystem';

interface Friend {
  id: string;
  username: string;
  displayName: string;
  avatarUrl?: string;
  status: 'online' | 'offline' | 'in_game';
  currentGame?: string;
  lastSeen?: Date;
  rating: number;
  rank: string;
}

interface FriendRequest {
  id: string;
  from: {
    id: string;
    username: string;
    displayName: string;
    avatarUrl?: string;
  };
  createdAt: Date;
}

const FriendsContainer = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  height: 600px;
  display: flex;
  flex-direction: column;
`;

const FriendsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
`;

const FriendsTitle = styled.h3`
  margin: 0;
  font-size: 20px;
  color: #4a90e2;
`;

const TabContainer = styled.div`
  display: flex;
  margin-bottom: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 3px;
`;

const Tab = styled.button<{ active: boolean }>`
  flex: 1;
  padding: 8px 12px;
  border: none;
  background: ${props => props.active ? 'rgba(74, 144, 226, 0.3)' : 'transparent'};
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  
  &:hover {
    background: rgba(74, 144, 226, 0.2);
  }
`;

const FriendsList = styled.div`
  flex: 1;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
`;

const FriendItem = styled.div`
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
  }
`;

const Avatar = styled.div<{ status: Friend['status'] }>`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: ${props => {
      switch (props.status) {
        case 'online': return '#4caf50';
        case 'in_game': return '#ff9800';
        case 'offline': return '#666';
        default: return '#666';
      }
    }};
    border: 2px solid rgba(255, 255, 255, 0.2);
  }
`;

const FriendInfo = styled.div`
  flex: 1;
`;

const FriendName = styled.div`
  font-weight: bold;
  margin-bottom: 2px;
`;

const FriendStatus = styled.div`
  font-size: 12px;
  opacity: 0.7;
`;

const FriendActions = styled.div`
  display: flex;
  gap: 8px;
`;

const ActionButton = styled.button`
  background: rgba(74, 144, 226, 0.2);
  border: 1px solid rgba(74, 144, 226, 0.3);
  color: white;
  padding: 6px 12px;
  border-radius: 5px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background: rgba(74, 144, 226, 0.3);
    transform: translateY(-1px);
  }
`;

const AddFriendForm = styled.form`
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
`;

const SearchInput = styled.input`
  flex: 1;
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
  
  &:focus {
    outline: none;
    border-color: #4a90e2;
    background: rgba(255, 255, 255, 0.15);
  }
`;

const RequestItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
`;

const RequestInfo = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
`;

const RequestActions = styled.div`
  display: flex;
  gap: 8px;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 40px 20px;
  opacity: 0.6;
`;

export const FriendsPanel: React.FC = () => {
  const [friends, setFriends] = useState<Friend[]>([]);
  const [friendRequests, setFriendRequests] = useState<FriendRequest[]>([]);
  const [activeTab, setActiveTab] = useState<'friends' | 'requests'>('friends');
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddFriend, setShowAddFriend] = useState(false);
  const [loading, setLoading] = useState(true);
  
  const { socket } = useSocket();
  const { user } = useAuth();
  const { showSuccess, showError } = useNotifications();

  useEffect(() => {
    if (!socket || !user) return;

    // Загружаем список друзей
    socket.emit('friends:get_list');
    socket.emit('friends:get_requests');

    // Обработчики событий
    socket.on('friends:list', (data: Friend[]) => {
      setFriends(data);
      setLoading(false);
    });

    socket.on('friends:requests', (data: FriendRequest[]) => {
      setFriendRequests(data);
    });

    socket.on('friends:request_sent', () => {
      showSuccess('Friend Request Sent', 'Your friend request has been sent!');
      setSearchQuery('');
    });

    socket.on('friends:request_accepted', (data: { friend: Friend }) => {
      setFriends(prev => [...prev, data.friend]);
      setFriendRequests(prev => prev.filter(req => req.from.id !== data.friend.id));
      showSuccess('Friend Added', `${data.friend.displayName} is now your friend!`);
    });

    socket.on('friends:request_received', (data: FriendRequest) => {
      setFriendRequests(prev => [...prev, data]);
    });

    socket.on('friends:status_updated', (data: { friendId: string; status: Friend['status']; currentGame?: string }) => {
      setFriends(prev => prev.map(friend => 
        friend.id === data.friendId 
          ? { ...friend, status: data.status, currentGame: data.currentGame }
          : friend
      ));
    });

    socket.on('friends:error', (data: { message: string }) => {
      showError('Error', data.message);
    });

    return () => {
      socket.off('friends:list');
      socket.off('friends:requests');
      socket.off('friends:request_sent');
      socket.off('friends:request_accepted');
      socket.off('friends:request_received');
      socket.off('friends:status_updated');
      socket.off('friends:error');
    };
  }, [socket, user]);

  const handleAddFriend = (e: React.FormEvent) => {
    e.preventDefault();
    if (!socket || !searchQuery.trim()) return;

    socket.emit('friends:send_request', { username: searchQuery.trim() });
  };

  const handleAcceptRequest = (requestId: string, fromUserId: string) => {
    if (!socket) return;
    socket.emit('friends:accept_request', { fromUserId });
  };

  const handleDeclineRequest = (requestId: string, fromUserId: string) => {
    if (!socket) return;
    socket.emit('friends:decline_request', { fromUserId });
    setFriendRequests(prev => prev.filter(req => req.id !== requestId));
  };

  const handleInviteToGame = (friendId: string, gameType: string) => {
    if (!socket) return;
    socket.emit('friends:invite_to_game', { friendId, gameType });
    showSuccess('Invitation Sent', 'Game invitation sent to your friend!');
  };

  const handleRemoveFriend = (friendId: string) => {
    if (!socket) return;
    if (confirm('Are you sure you want to remove this friend?')) {
      socket.emit('friends:remove', { friendId });
      setFriends(prev => prev.filter(friend => friend.id !== friendId));
    }
  };

  const getStatusText = (friend: Friend) => {
    switch (friend.status) {
      case 'online':
        return 'Online';
      case 'in_game':
        return `Playing ${friend.currentGame}`;
      case 'offline':
        return friend.lastSeen ? `Last seen ${new Date(friend.lastSeen).toLocaleDateString()}` : 'Offline';
      default:
        return 'Unknown';
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  if (loading) {
    return (
      <FriendsContainer>
        <div style={{ textAlign: 'center', padding: '40px' }}>Loading friends...</div>
      </FriendsContainer>
    );
  }

  return (
    <FriendsContainer>
      <FriendsHeader>
        <FriendsTitle>Friends ({friends.length})</FriendsTitle>
        <Button size="small" onClick={() => setShowAddFriend(true)}>
          Add Friend
        </Button>
      </FriendsHeader>

      <TabContainer>
        <Tab active={activeTab === 'friends'} onClick={() => setActiveTab('friends')}>
          Friends ({friends.length})
        </Tab>
        <Tab active={activeTab === 'requests'} onClick={() => setActiveTab('requests')}>
          Requests ({friendRequests.length})
        </Tab>
      </TabContainer>

      {activeTab === 'friends' && (
        <FriendsList>
          {friends.length === 0 ? (
            <EmptyState>
              <div>No friends yet</div>
              <div style={{ fontSize: '14px', marginTop: '10px' }}>
                Add some friends to start playing together!
              </div>
            </EmptyState>
          ) : (
            friends.map(friend => (
              <FriendItem key={friend.id}>
                <Avatar status={friend.status}>
                  {friend.avatarUrl ? (
                    <img src={friend.avatarUrl} alt={friend.displayName} style={{ width: '100%', height: '100%', borderRadius: '50%' }} />
                  ) : (
                    getInitials(friend.displayName)
                  )}
                </Avatar>
                
                <FriendInfo>
                  <FriendName>{friend.displayName}</FriendName>
                  <FriendStatus>{getStatusText(friend)} • {friend.rank} ({friend.rating})</FriendStatus>
                </FriendInfo>
                
                <FriendActions>
                  {friend.status === 'online' && (
                    <>
                      <ActionButton onClick={() => handleInviteToGame(friend.id, 'durak')}>
                        Durak
                      </ActionButton>
                      <ActionButton onClick={() => handleInviteToGame(friend.id, 'poker')}>
                        Poker
                      </ActionButton>
                    </>
                  )}
                  <ActionButton onClick={() => handleRemoveFriend(friend.id)}>
                    Remove
                  </ActionButton>
                </FriendActions>
              </FriendItem>
            ))
          )}
        </FriendsList>
      )}

      {activeTab === 'requests' && (
        <FriendsList>
          {friendRequests.length === 0 ? (
            <EmptyState>
              <div>No friend requests</div>
            </EmptyState>
          ) : (
            friendRequests.map(request => (
              <RequestItem key={request.id}>
                <RequestInfo>
                  <Avatar status="offline">
                    {request.from.avatarUrl ? (
                      <img src={request.from.avatarUrl} alt={request.from.displayName} style={{ width: '100%', height: '100%', borderRadius: '50%' }} />
                    ) : (
                      getInitials(request.from.displayName)
                    )}
                  </Avatar>
                  <div>
                    <FriendName>{request.from.displayName}</FriendName>
                    <FriendStatus>Wants to be your friend</FriendStatus>
                  </div>
                </RequestInfo>
                
                <RequestActions>
                  <ActionButton onClick={() => handleAcceptRequest(request.id, request.from.id)}>
                    Accept
                  </ActionButton>
                  <ActionButton onClick={() => handleDeclineRequest(request.id, request.from.id)}>
                    Decline
                  </ActionButton>
                </RequestActions>
              </RequestItem>
            ))
          )}
        </FriendsList>
      )}

      {showAddFriend && (
        <Modal onClose={() => setShowAddFriend(false)} title="Add Friend">
          <AddFriendForm onSubmit={handleAddFriend}>
            <SearchInput
              type="text"
              placeholder="Enter username..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              autoFocus
            />
            <Button type="submit" disabled={!searchQuery.trim()}>
              Send Request
            </Button>
          </AddFriendForm>
        </Modal>
      )}
    </FriendsContainer>
  );
};
