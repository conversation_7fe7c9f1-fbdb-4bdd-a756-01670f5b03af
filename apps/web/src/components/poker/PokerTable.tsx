import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Card } from '../../types/Card';
import { PokerPlayer, PokerGameState, PokerAction } from '../../types/Poker';
import { PlayingCard } from '../common/PlayingCard';
import { Button } from '../common/Button';

interface PokerTableProps {
  gameState: PokerGameState;
  currentPlayerId: string;
  onAction: (action: PokerAction) => void;
}

const TableContainer = styled.div`
  position: relative;
  width: 800px;
  height: 600px;
  background: linear-gradient(135deg, #0f4c3a 0%, #1a5f4a 100%);
  border-radius: 400px / 300px;
  border: 8px solid #8b4513;
  margin: 20px auto;
  box-shadow: 
    inset 0 0 50px rgba(0, 0, 0, 0.3),
    0 10px 30px rgba(0, 0, 0, 0.5);
`;

const PlayerSeat = styled.div<{ position: number; isActive: boolean; isCurrentPlayer: boolean }>`
  position: absolute;
  width: 120px;
  height: 80px;
  background: ${props => props.isCurrentPlayer ? 
    'linear-gradient(135deg, #ffd700 0%, #ffed4e 100%)' : 
    props.isActive ? 
    'linear-gradient(135deg, #4a90e2 0%, #357abd 100%)' : 
    'linear-gradient(135deg, #666 0%, #444 100%)'
  };
  border-radius: 10px;
  border: 2px solid ${props => props.isCurrentPlayer ? '#ffa500' : props.isActive ? '#2c5aa0' : '#333'};
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  
  ${props => {
    const angle = (props.position * 360) / 9; // 9 максимальных позиций
    const radius = 280;
    const x = Math.cos((angle - 90) * Math.PI / 180) * radius;
    const y = Math.sin((angle - 90) * Math.PI / 180) * radius;
    return `
      left: ${400 + x - 60}px;
      top: ${300 + y - 40}px;
    `;
  }}
`;

const PlayerName = styled.div`
  font-size: 12px;
  margin-bottom: 4px;
`;

const PlayerChips = styled.div`
  font-size: 14px;
  font-weight: bold;
`;

const PlayerBet = styled.div`
  position: absolute;
  top: -30px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 4px 8px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
`;

const PlayerCards = styled.div`
  position: absolute;
  bottom: -40px;
  display: flex;
  gap: 4px;
`;

const CommunityCardsArea = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 10px;
  background: rgba(0, 0, 0, 0.2);
  padding: 20px;
  border-radius: 10px;
`;

const PotDisplay = styled.div`
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #333;
  padding: 10px 20px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 18px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
`;

const ActionPanel = styled.div`
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  background: rgba(0, 0, 0, 0.8);
  padding: 15px;
  border-radius: 10px;
`;

const BetInput = styled.input`
  width: 80px;
  padding: 8px;
  border: none;
  border-radius: 5px;
  text-align: center;
  font-weight: bold;
`;

const PhaseDisplay = styled.div`
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-weight: bold;
  text-transform: uppercase;
`;

const LastActionDisplay = styled.div`
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 10px;
  font-size: 10px;
  white-space: nowrap;
`;

export const PokerTable: React.FC<PokerTableProps> = ({
  gameState,
  currentPlayerId,
  onAction
}) => {
  const [betAmount, setBetAmount] = useState(0);
  const [showActions, setShowActions] = useState(false);

  const currentPlayer = gameState.players.find(p => p.id === currentPlayerId);
  const activePlayer = gameState.players[gameState.currentPlayerIndex];
  const isMyTurn = activePlayer?.id === currentPlayerId;

  useEffect(() => {
    setShowActions(isMyTurn && gameState.phase !== 'finished');
  }, [isMyTurn, gameState.phase]);

  const handleAction = (actionType: PokerAction['type'], amount?: number) => {
    const action: PokerAction = {
      type: actionType,
      amount,
      timestamp: new Date()
    };
    onAction(action);
    setBetAmount(0);
  };

  const getValidActions = () => {
    if (!currentPlayer || !isMyTurn) return [];
    
    const callAmount = gameState.currentBet - currentPlayer.currentBet;
    const actions = [];

    // Fold всегда доступен
    actions.push('fold');

    // Check если нет ставки
    if (callAmount === 0) {
      actions.push('check');
    }

    // Call если есть ставка
    if (callAmount > 0 && currentPlayer.chips >= callAmount) {
      actions.push('call');
    }

    // Bet если нет текущей ставки
    if (gameState.currentBet === 0 && currentPlayer.chips >= gameState.bigBlind) {
      actions.push('bet');
    }

    // Raise если есть текущая ставка
    if (gameState.currentBet > 0 && currentPlayer.chips >= gameState.currentBet + gameState.minRaise) {
      actions.push('raise');
    }

    // All-in всегда доступен если есть фишки
    if (currentPlayer.chips > 0) {
      actions.push('all_in');
    }

    return actions;
  };

  const formatChips = (amount: number) => {
    return amount.toLocaleString();
  };

  const getPhaseDisplay = () => {
    switch (gameState.phase) {
      case 'preflop': return 'Pre-Flop';
      case 'flop': return 'Flop';
      case 'turn': return 'Turn';
      case 'river': return 'River';
      case 'showdown': return 'Showdown';
      case 'finished': return 'Hand Finished';
      default: return gameState.phase;
    }
  };

  const getActionText = (action?: PokerAction) => {
    if (!action) return '';
    
    switch (action.type) {
      case 'fold': return 'Fold';
      case 'check': return 'Check';
      case 'call': return 'Call';
      case 'bet': return `Bet ${formatChips(action.amount || 0)}`;
      case 'raise': return `Raise ${formatChips(action.amount || 0)}`;
      case 'all_in': return 'All-In';
      default: return action.type;
    }
  };

  const validActions = getValidActions();

  return (
    <TableContainer>
      <PhaseDisplay>
        {getPhaseDisplay()} - Hand #{gameState.handNumber}
      </PhaseDisplay>

      <PotDisplay>
        Pot: ${formatChips(gameState.pot)}
      </PotDisplay>

      <CommunityCardsArea>
        {gameState.communityCards.map((card, index) => (
          <PlayingCard
            key={index}
            card={card}
            size="medium"
            faceUp={true}
          />
        ))}
        {/* Placeholder cards */}
        {Array.from({ length: 5 - gameState.communityCards.length }).map((_, index) => (
          <PlayingCard
            key={`placeholder-${index}`}
            card={{ suit: 'hearts', rank: 'A' }}
            size="medium"
            faceUp={false}
            placeholder={true}
          />
        ))}
      </CommunityCardsArea>

      {gameState.players.map((player, index) => (
        <PlayerSeat
          key={player.id}
          position={index}
          isActive={!player.isFolded && player.isActive}
          isCurrentPlayer={player.id === activePlayer?.id}
        >
          <PlayerName>{player.name}</PlayerName>
          <PlayerChips>${formatChips(player.chips)}</PlayerChips>
          
          {player.currentBet > 0 && (
            <PlayerBet>${formatChips(player.currentBet)}</PlayerBet>
          )}

          {player.lastAction && (
            <LastActionDisplay>
              {getActionText(player.lastAction)}
            </LastActionDisplay>
          )}

          <PlayerCards>
            {player.id === currentPlayerId ? (
              // Показываем свои карты
              player.holeCards.map((card, cardIndex) => (
                <PlayingCard
                  key={cardIndex}
                  card={card}
                  size="small"
                  faceUp={true}
                />
              ))
            ) : (
              // Показываем рубашки карт других игроков
              player.holeCards.map((_, cardIndex) => (
                <PlayingCard
                  key={cardIndex}
                  card={{ suit: 'hearts', rank: 'A' }}
                  size="small"
                  faceUp={false}
                />
              ))
            )}
          </PlayerCards>
        </PlayerSeat>
      ))}

      {showActions && (
        <ActionPanel>
          {validActions.includes('fold') && (
            <Button
              variant="danger"
              onClick={() => handleAction('fold')}
            >
              Fold
            </Button>
          )}

          {validActions.includes('check') && (
            <Button
              variant="secondary"
              onClick={() => handleAction('check')}
            >
              Check
            </Button>
          )}

          {validActions.includes('call') && (
            <Button
              variant="primary"
              onClick={() => handleAction('call')}
            >
              Call ${formatChips(gameState.currentBet - (currentPlayer?.currentBet || 0))}
            </Button>
          )}

          {(validActions.includes('bet') || validActions.includes('raise')) && (
            <>
              <BetInput
                type="number"
                value={betAmount || ''}
                onChange={(e) => setBetAmount(parseInt(e.target.value) || 0)}
                placeholder="Amount"
                min={gameState.bigBlind}
                max={currentPlayer?.chips || 0}
              />
              <Button
                variant="success"
                onClick={() => handleAction(validActions.includes('bet') ? 'bet' : 'raise', betAmount)}
                disabled={betAmount < gameState.bigBlind}
              >
                {validActions.includes('bet') ? 'Bet' : 'Raise'}
              </Button>
            </>
          )}

          {validActions.includes('all_in') && (
            <Button
              variant="warning"
              onClick={() => handleAction('all_in')}
            >
              All-In (${formatChips(currentPlayer?.chips || 0)})
            </Button>
          )}
        </ActionPanel>
      )}
    </TableContainer>
  );
};
