import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';

interface RevolutionaryFeaturesProps {
  currentSection: number;
}

interface Feature {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  icon: string;
  color: string;
  stats: { label: string; value: string }[];
  technologies: string[];
  benefits: string[];
}

const features: Feature[] = [
  {
    id: 'quantum',
    title: 'Квантовый игровой движок',
    subtitle: 'Истинная случайность из квантовых источников',
    description: 'Первая в мире игровая платформа, использующая реальные квантовые источники случайности для абсолютной честности игр.',
    icon: '⚛️',
    color: '#4a90e2',
    stats: [
      { label: 'Энтропия', value: '99.99%' },
      { label: 'Квантовых источников', value: '5' },
      { label: 'Тестов качества', value: '15+' }
    ],
    technologies: ['ANU Quantum RNG', 'ID Quantique', 'PicoQuant', 'Quantum Dice', 'NIST Tests'],
    benefits: ['Абсолютная честность', 'Невозможность предсказания', 'Научная валидация']
  },
  {
    id: 'ai',
    title: 'Эмоциональный ИИ',
    subtitle: 'Глубокое понимание каждого игрока',
    description: 'Революционная система искусственного интеллекта, анализирующая эмоциональное состояние и персонализирующая игровой опыт.',
    icon: '🧠',
    color: '#7b68ee',
    stats: [
      { label: 'Эмоциональных состояний', value: '8' },
      { label: 'Точность анализа', value: '95%' },
      { label: 'ML моделей', value: '6' }
    ],
    technologies: ['TensorFlow', 'OpenAI GPT-4', 'Computer Vision', 'NLP', 'Behavioral Analysis'],
    benefits: ['Персонализация', 'Предотвращение тильта', 'Адаптивное обучение']
  },
  {
    id: 'metaverse',
    title: '3D Метавселенная',
    subtitle: 'Иммерсивные игровые миры',
    description: 'Полноценная метавселенная с 3D мирами, VR/AR поддержкой и социальными пространствами для революционного игрового опыта.',
    icon: '🌍',
    color: '#9370db',
    stats: [
      { label: 'Тематических миров', value: '6' },
      { label: 'Одновременных игроков', value: '10K+' },
      { label: 'VR/AR поддержка', value: '100%' }
    ],
    technologies: ['Three.js', 'WebXR', 'WebGL', 'Physics Engine', 'Spatial Audio'],
    benefits: ['Иммерсивность', 'Социальное взаимодействие', 'Новый опыт']
  },
  {
    id: 'security',
    title: 'Квантовая безопасность',
    subtitle: 'Непробиваемая защита',
    description: 'Передовые системы безопасности с квантовым шифрованием, биометрией и ИИ-детекцией угроз.',
    icon: '🛡️',
    color: '#ff6b6b',
    stats: [
      { label: 'Точность детекции', value: '99.9%' },
      { label: 'Типов угроз', value: '6' },
      { label: 'Время реакции', value: '<1с' }
    ],
    technologies: ['Post-Quantum Crypto', 'Zero-Knowledge Proofs', 'Biometrics', 'AI Detection'],
    benefits: ['Абсолютная защита', 'Приватность', 'Доверие']
  },
  {
    id: 'analytics',
    title: 'Предиктивная аналитика',
    subtitle: 'ИИ предсказывает будущее',
    description: 'Мощная система машинного обучения, предсказывающая игровые события и персонализирующая опыт.',
    icon: '📊',
    color: '#4ecdc4',
    stats: [
      { label: 'Точность предсказаний', value: '85%' },
      { label: 'Анализируемых метрик', value: '100+' },
      { label: 'Обновлений в секунду', value: '1000+' }
    ],
    technologies: ['Machine Learning', 'Real-time Analytics', 'Predictive Models', 'Big Data'],
    benefits: ['Персонализация', 'Оптимизация', 'Инсайты']
  },
  {
    id: 'web3',
    title: 'Web3 экосистема',
    subtitle: 'Децентрализованное будущее',
    description: 'Полная интеграция с блокчейном, NFT картами, DeFi протоколами и DAO управлением.',
    icon: '⛓️',
    color: '#feca57',
    stats: [
      { label: 'Смарт-контрактов', value: '6' },
      { label: 'Поддерживаемых сетей', value: '5+' },
      { label: 'NFT коллекций', value: '10+' }
    ],
    technologies: ['Ethereum', 'Polygon', 'IPFS', 'Smart Contracts', 'DeFi'],
    benefits: ['Владение активами', 'Децентрализация', 'Новая экономика']
  }
];

const RevolutionaryFeatures: React.FC<RevolutionaryFeaturesProps> = ({ currentSection }) => {
  const [activeFeature, setActiveFeature] = useState(0);
  const [hoveredFeature, setHoveredFeature] = useState<string | null>(null);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % features.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <FeaturesContainer>
      <ContentWrapper>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <SectionTitle>Революционные технологии</SectionTitle>
          <SectionSubtitle>
            Мы объединили передовые технологии будущего в одной платформе
          </SectionSubtitle>
        </motion.div>

        <FeaturesGrid>
          {features.map((feature, index) => (
            <FeatureCard
              key={feature.id}
              active={index === activeFeature}
              hovered={hoveredFeature === feature.id}
              color={feature.color}
              onMouseEnter={() => setHoveredFeature(feature.id)}
              onMouseLeave={() => setHoveredFeature(null)}
              onClick={() => setActiveFeature(index)}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <FeatureIcon active={index === activeFeature}>
                {feature.icon}
              </FeatureIcon>
              <FeatureTitle>{feature.title}</FeatureTitle>
              <FeatureSubtitle>{feature.subtitle}</FeatureSubtitle>
              
              <AnimatePresence>
                {(index === activeFeature || hoveredFeature === feature.id) && (
                  <FeatureDetails
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <FeatureDescription>
                      {feature.description}
                    </FeatureDescription>
                    
                    <StatsGrid>
                      {feature.stats.map((stat, statIndex) => (
                        <StatItem key={statIndex}>
                          <StatValue>{stat.value}</StatValue>
                          <StatLabel>{stat.label}</StatLabel>
                        </StatItem>
                      ))}
                    </StatsGrid>
                  </FeatureDetails>
                )}
              </AnimatePresence>
            </FeatureCard>
          ))}
        </FeaturesGrid>

        {/* Детальная информация об активной функции */}
        <AnimatePresence mode="wait">
          <DetailedView
            key={activeFeature}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.5 }}
          >
            <DetailedContent>
              <DetailedHeader>
                <DetailedIcon color={features[activeFeature].color}>
                  {features[activeFeature].icon}
                </DetailedIcon>
                <div>
                  <DetailedTitle>{features[activeFeature].title}</DetailedTitle>
                  <DetailedSubtitle>{features[activeFeature].subtitle}</DetailedSubtitle>
                </div>
              </DetailedHeader>

              <DetailedDescription>
                {features[activeFeature].description}
              </DetailedDescription>

              <TechnologiesSection>
                <SectionLabel>Технологии:</SectionLabel>
                <TechnologiesList>
                  {features[activeFeature].technologies.map((tech, index) => (
                    <TechTag
                      key={index}
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      {tech}
                    </TechTag>
                  ))}
                </TechnologiesList>
              </TechnologiesSection>

              <BenefitsSection>
                <SectionLabel>Преимущества:</SectionLabel>
                <BenefitsList>
                  {features[activeFeature].benefits.map((benefit, index) => (
                    <BenefitItem
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      ✨ {benefit}
                    </BenefitItem>
                  ))}
                </BenefitsList>
              </BenefitsSection>
            </DetailedContent>
          </DetailedView>
        </AnimatePresence>

        {/* Индикаторы прогресса */}
        <ProgressIndicators>
          {features.map((_, index) => (
            <ProgressDot
              key={index}
              active={index === activeFeature}
              onClick={() => setActiveFeature(index)}
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.9 }}
            />
          ))}
        </ProgressIndicators>
      </ContentWrapper>
    </FeaturesContainer>
  );
};

// Стилизованные компоненты
const FeaturesContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  padding: 4rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ContentWrapper = styled.div`
  max-width: 1400px;
  width: 100%;
`;

const SectionTitle = styled.h2`
  font-size: 3.5rem;
  font-weight: 900;
  text-align: center;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  
  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const SectionSubtitle = styled.p`
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin-bottom: 4rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
`;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
`;

const FeatureCard = styled(motion.div)<{ active: boolean; hovered: boolean; color: string }>`
  background: ${props => props.active || props.hovered 
    ? `linear-gradient(135deg, ${props.color}20, ${props.color}10)` 
    : 'rgba(255, 255, 255, 0.05)'};
  border: 2px solid ${props => props.active || props.hovered ? props.color : 'rgba(255, 255, 255, 0.1)'};
  border-radius: 20px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  overflow: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: ${props => `linear-gradient(135deg, ${props.color}10, transparent)`};
    opacity: ${props => props.active || props.hovered ? 1 : 0};
    transition: opacity 0.3s ease;
  }
`;

const FeatureIcon = styled.div<{ active: boolean }>`
  font-size: 3rem;
  margin-bottom: 1rem;
  text-align: center;
  filter: ${props => props.active ? 'drop-shadow(0 0 20px currentColor)' : 'none'};
  transition: all 0.3s ease;
`;

const FeatureTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
  text-align: center;
`;

const FeatureSubtitle = styled.p`
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin-bottom: 1rem;
`;

const FeatureDetails = styled(motion.div)`
  position: relative;
  z-index: 1;
`;

const FeatureDescription = styled.p`
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 1.5rem;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
`;

const StatItem = styled.div`
  text-align: center;
`;

const StatValue = styled.div`
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
`;

const StatLabel = styled.div`
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
`;

const DetailedView = styled(motion.div)`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 3rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 3rem;
`;

const DetailedContent = styled.div``;

const DetailedHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const DetailedIcon = styled.div<{ color: string }>`
  font-size: 4rem;
  color: ${props => props.color};
  filter: drop-shadow(0 0 20px ${props => props.color}50);
`;

const DetailedTitle = styled.h3`
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
`;

const DetailedSubtitle = styled.p`
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
`;

const DetailedDescription = styled.p`
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.8;
  margin-bottom: 2rem;
`;

const TechnologiesSection = styled.div`
  margin-bottom: 2rem;
`;

const BenefitsSection = styled.div``;

const SectionLabel = styled.h4`
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
`;

const TechnologiesList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
`;

const TechTag = styled(motion.span)`
  background: rgba(74, 144, 226, 0.2);
  color: #4a90e2;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(74, 144, 226, 0.3);
`;

const BenefitsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const BenefitItem = styled(motion.div)`
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
`;

const ProgressIndicators = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
`;

const ProgressDot = styled(motion.div)<{ active: boolean }>`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.3)'};
  cursor: pointer;
  transition: all 0.3s ease;
`;

export default RevolutionaryFeatures;
