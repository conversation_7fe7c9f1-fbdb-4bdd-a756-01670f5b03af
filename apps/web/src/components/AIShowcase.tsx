import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';

interface AIShowcaseProps {
  emotionalState: any;
}

interface EmotionalMetric {
  name: string;
  value: number;
  color: string;
  icon: string;
  description: string;
}

const emotionalMetrics: EmotionalMetric[] = [
  {
    name: 'Счастье',
    value: 0.85,
    color: '#4ade80',
    icon: '😊',
    description: 'Уровень позитивных эмоций игрока'
  },
  {
    name: 'Фокус',
    value: 0.92,
    color: '#3b82f6',
    icon: '🎯',
    description: 'Концентрация и внимание к игре'
  },
  {
    name: 'Уверенность',
    value: 0.78,
    color: '#8b5cf6',
    icon: '💪',
    description: 'Уверенность в принятии решений'
  },
  {
    name: 'Стресс',
    value: 0.23,
    color: '#ef4444',
    icon: '😰',
    description: 'Уровень стресса и напряжения'
  },
  {
    name: 'Мотивация',
    value: 0.89,
    color: '#f59e0b',
    icon: '🔥',
    description: 'Желание продолжать игру'
  },
  {
    name: 'Усталость',
    value: 0.31,
    color: '#6b7280',
    icon: '😴',
    description: 'Уровень усталости игрока'
  }
];

const aiFeatures = [
  {
    title: 'Анализ эмоций в реальном времени',
    description: 'ИИ анализирует микровыражения, тон голоса и поведенческие паттерны',
    icon: '🧠',
    metrics: ['Точность: 95%', 'Задержка: <100мс', 'Источников данных: 8']
  },
  {
    title: 'Предиктивная аналитика',
    description: 'Предсказание следующих ходов и вероятности победы',
    icon: '🔮',
    metrics: ['Точность предсказаний: 85%', 'Анализируемых факторов: 50+', 'Обновлений/сек: 1000+']
  },
  {
    title: 'Персонализированное обучение',
    description: 'Адаптивные уроки и рекомендации на основе стиля игры',
    icon: '📚',
    metrics: ['Персональных путей: ∞', 'Адаптация: в реальном времени', 'Эффективность: +300%']
  },
  {
    title: 'Детекция тильта и выгорания',
    description: 'Раннее обнаружение негативных состояний и превентивные меры',
    icon: '🛡️',
    metrics: ['Точность детекции: 99%', 'Время реакции: <1сек', 'Предотвращённых тильтов: 10K+']
  }
];

const AIShowcase: React.FC<AIShowcaseProps> = ({ emotionalState }) => {
  const [activeFeature, setActiveFeature] = useState(0);
  const [isAnalyzing, setIsAnalyzing] = useState(true);
  const [currentMetrics, setCurrentMetrics] = useState(emotionalMetrics);

  useEffect(() => {
    // Симуляция обновления метрик в реальном времени
    const interval = setInterval(() => {
      setCurrentMetrics(prev => prev.map(metric => ({
        ...metric,
        value: Math.max(0, Math.min(1, metric.value + (Math.random() - 0.5) * 0.1))
      })));
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    // Автоматическое переключение функций
    const interval = setInterval(() => {
      setActiveFeature(prev => (prev + 1) % aiFeatures.length);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  return (
    <AIContainer>
      <ContentWrapper>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <SectionTitle>Эмоциональный ИИ</SectionTitle>
          <SectionSubtitle>
            Революционная система понимания и анализа игроков
          </SectionSubtitle>
        </motion.div>

        <MainContent>
          {/* Панель эмоциональных метрик */}
          <MetricsPanel>
            <PanelHeader>
              <PanelTitle>
                <AnalysisIndicator active={isAnalyzing} />
                Анализ в реальном времени
              </PanelTitle>
              <PanelSubtitle>
                ИИ анализирует 8 эмоциональных состояний одновременно
              </PanelSubtitle>
            </PanelHeader>

            <MetricsGrid>
              {currentMetrics.map((metric, index) => (
                <MetricCard
                  key={metric.name}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                >
                  <MetricHeader>
                    <MetricIcon>{metric.icon}</MetricIcon>
                    <MetricName>{metric.name}</MetricName>
                  </MetricHeader>
                  
                  <MetricValue color={metric.color}>
                    {(metric.value * 100).toFixed(0)}%
                  </MetricValue>
                  
                  <MetricBar>
                    <MetricProgress
                      color={metric.color}
                      width={metric.value * 100}
                      initial={{ width: 0 }}
                      animate={{ width: `${metric.value * 100}%` }}
                      transition={{ duration: 0.8, delay: index * 0.1 }}
                    />
                  </MetricBar>
                  
                  <MetricDescription>
                    {metric.description}
                  </MetricDescription>
                </MetricCard>
              ))}
            </MetricsGrid>
          </MetricsPanel>

          {/* Панель функций ИИ */}
          <FeaturesPanel>
            <FeatureSelector>
              {aiFeatures.map((feature, index) => (
                <FeatureTab
                  key={index}
                  active={index === activeFeature}
                  onClick={() => setActiveFeature(index)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <FeatureTabIcon>{feature.icon}</FeatureTabIcon>
                  <FeatureTabTitle>{feature.title}</FeatureTabTitle>
                </FeatureTab>
              ))}
            </FeatureSelector>

            <FeatureContent>
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeFeature}
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -50 }}
                  transition={{ duration: 0.5 }}
                >
                  <FeatureHeader>
                    <FeatureIcon>{aiFeatures[activeFeature].icon}</FeatureIcon>
                    <div>
                      <FeatureTitle>{aiFeatures[activeFeature].title}</FeatureTitle>
                      <FeatureDescription>
                        {aiFeatures[activeFeature].description}
                      </FeatureDescription>
                    </div>
                  </FeatureHeader>

                  <FeatureMetrics>
                    {aiFeatures[activeFeature].metrics.map((metric, index) => (
                      <FeatureMetric
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        ✨ {metric}
                      </FeatureMetric>
                    ))}
                  </FeatureMetrics>
                </motion.div>
              </AnimatePresence>
            </FeatureContent>
          </FeaturesPanel>
        </MainContent>

        {/* Демонстрация возможностей */}
        <DemoSection>
          <DemoTitle>Попробуйте ИИ в действии</DemoTitle>
          <DemoGrid>
            <DemoCard
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <DemoIcon>🎮</DemoIcon>
              <DemoCardTitle>Игровая сессия</DemoCardTitle>
              <DemoCardDescription>
                Начните игру и наблюдайте за анализом ИИ
              </DemoCardDescription>
            </DemoCard>

            <DemoCard
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <DemoIcon>📊</DemoIcon>
              <DemoCardTitle>Аналитика</DemoCardTitle>
              <DemoCardDescription>
                Изучите детальную аналитику своей игры
              </DemoCardDescription>
            </DemoCard>

            <DemoCard
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <DemoIcon>🎯</DemoIcon>
              <DemoCardTitle>Персонализация</DemoCardTitle>
              <DemoCardDescription>
                Получите персональные рекомендации
              </DemoCardDescription>
            </DemoCard>
          </DemoGrid>
        </DemoSection>
      </ContentWrapper>
    </AIContainer>
  );
};

// Стилизованные компоненты
const AIContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
  padding: 4rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ContentWrapper = styled.div`
  max-width: 1400px;
  width: 100%;
`;

const SectionTitle = styled.h2`
  font-size: 3.5rem;
  font-weight: 900;
  text-align: center;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  
  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const SectionSubtitle = styled.p`
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin-bottom: 4rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
`;

const MainContent = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 4rem;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
`;

const MetricsPanel = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const PanelHeader = styled.div`
  margin-bottom: 2rem;
`;

const PanelTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const PanelSubtitle = styled.p`
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
`;

const AnalysisIndicator = styled.div<{ active: boolean }>`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: ${props => props.active ? '#4ade80' : '#6b7280'};
  animation: ${props => props.active ? 'pulse 2s infinite' : 'none'};
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  
  @media (max-width: 640px) {
    grid-template-columns: 1fr;
  }
`;

const MetricCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const MetricHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
`;

const MetricIcon = styled.span`
  font-size: 1.5rem;
`;

const MetricName = styled.span`
  font-weight: 600;
  color: white;
`;

const MetricValue = styled.div<{ color: string }>`
  font-size: 2rem;
  font-weight: 700;
  color: ${props => props.color};
  margin-bottom: 0.5rem;
`;

const MetricBar = styled.div`
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 0.5rem;
`;

const MetricProgress = styled(motion.div)<{ color: string; width: number }>`
  height: 100%;
  background: ${props => props.color};
  border-radius: 3px;
`;

const MetricDescription = styled.p`
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
`;

const FeaturesPanel = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const FeatureSelector = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
  margin-bottom: 2rem;
  
  @media (max-width: 640px) {
    grid-template-columns: 1fr;
  }
`;

const FeatureTab = styled(motion.button)<{ active: boolean }>`
  background: ${props => props.active ? 'rgba(74, 144, 226, 0.2)' : 'transparent'};
  border: 1px solid ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.1)'};
  border-radius: 10px;
  padding: 1rem;
  color: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.8)'};
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
`;

const FeatureTabIcon = styled.div`
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
`;

const FeatureTabTitle = styled.div`
  font-size: 0.9rem;
  font-weight: 600;
`;

const FeatureContent = styled.div`
  min-height: 200px;
`;

const FeatureHeader = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const FeatureIcon = styled.div`
  font-size: 3rem;
  color: #4a90e2;
`;

const FeatureTitle = styled.h4`
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
`;

const FeatureDescription = styled.p`
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
`;

const FeatureMetrics = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const FeatureMetric = styled(motion.div)`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
`;

const DemoSection = styled.div`
  text-align: center;
`;

const DemoTitle = styled.h3`
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 2rem;
`;

const DemoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const DemoCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const DemoIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const DemoCardTitle = styled.h4`
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.5rem;
`;

const DemoCardDescription = styled.p`
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  line-height: 1.5;
`;

export default AIShowcase;
