import React from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components';

interface FooterProps {
  onSubscribe?: (email: string) => void;
}

const Footer: React.FC<FooterProps> = ({ onSubscribe }) => {
  const [email, setEmail] = React.useState('');

  const handleSubscribe = (e: React.FormEvent) => {
    e.preventDefault();
    if (email && onSubscribe) {
      onSubscribe(email);
      setEmail('');
    }
  };

  const socialLinks = [
    { name: 'Discord', icon: '💬', url: 'https://discord.gg/kozyrmasterr', color: '#5865F2' },
    { name: 'Telegram', icon: '📱', url: 'https://t.me/kozyrmasterr', color: '#0088cc' },
    { name: 'Twitter', icon: '🐦', url: 'https://twitter.com/kozyrmasterr', color: '#1DA1F2' },
    { name: 'YouTube', icon: '📺', url: 'https://youtube.com/@kozyrmasterr', color: '#FF0000' },
    { name: 'Twitch', icon: '🎮', url: 'https://twitch.tv/kozyrmasterr', color: '#9146FF' },
    { name: 'GitHub', icon: '💻', url: 'https://github.com/kozyrmasterr', color: '#333' }
  ];

  const quickLinks = [
    { name: 'Игры', url: '/games' },
    { name: 'Турниры', url: '/tournaments' },
    { name: 'Обучение', url: '/tutorials' },
    { name: 'Рейтинг', url: '/leaderboard' },
    { name: 'Профиль', url: '/profile' },
    { name: 'Поддержка', url: '/support' }
  ];

  const legalLinks = [
    { name: 'Пользовательское соглашение', url: '/terms' },
    { name: 'Политика конфиденциальности', url: '/privacy' },
    { name: 'Правила игры', url: '/rules' },
    { name: 'FAQ', url: '/faq' }
  ];

  return (
    <FooterContainer>
      <FooterContent>
        {/* Основная информация */}
        <FooterSection>
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <BrandSection>
              <Logo>
                <LogoIcon>🎮</LogoIcon>
                <LogoText>
                  <LogoTitle>Козырь Мастер</LogoTitle>
                  <LogoVersion>4.0</LogoVersion>
                </LogoText>
              </Logo>
              
              <BrandDescription>
                Революционная платформа карточных игр с квантовой случайностью, 
                эмоциональным ИИ и 3D метавселенной. Будущее игр уже здесь!
              </BrandDescription>

              <TechBadges>
                <TechBadge>⚛️ Квантовые технологии</TechBadge>
                <TechBadge>🧠 Эмоциональный ИИ</TechBadge>
                <TechBadge>🌍 3D Метавселенная</TechBadge>
                <TechBadge>⛓️ Web3 & NFT</TechBadge>
              </TechBadges>
            </BrandSection>
          </motion.div>
        </FooterSection>

        {/* Быстрые ссылки */}
        <FooterSection>
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <SectionTitle>Быстрые ссылки</SectionTitle>
            <LinksList>
              {quickLinks.map((link, index) => (
                <LinkItem
                  key={link.name}
                  href={link.url}
                  whileHover={{ x: 5, color: '#4a90e2' }}
                  transition={{ duration: 0.2 }}
                >
                  {link.name}
                </LinkItem>
              ))}
            </LinksList>
          </motion.div>
        </FooterSection>

        {/* Сообщество */}
        <FooterSection>
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <SectionTitle>Сообщество</SectionTitle>
            <SocialLinks>
              {socialLinks.map((social, index) => (
                <SocialLink
                  key={social.name}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  color={social.color}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, scale: 0 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <SocialIcon>{social.icon}</SocialIcon>
                  <SocialName>{social.name}</SocialName>
                </SocialLink>
              ))}
            </SocialLinks>

            <CommunityStats>
              <StatItem>
                <StatValue>50K+</StatValue>
                <StatLabel>Игроков</StatLabel>
              </StatItem>
              <StatItem>
                <StatValue>15K+</StatValue>
                <StatLabel>Discord</StatLabel>
              </StatItem>
              <StatItem>
                <StatValue>25K+</StatValue>
                <StatLabel>Подписчиков</StatLabel>
              </StatItem>
            </CommunityStats>
          </motion.div>
        </FooterSection>

        {/* Подписка на новости */}
        <FooterSection>
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <SectionTitle>Новости и обновления</SectionTitle>
            <NewsletterDescription>
              Будьте в курсе последних новостей, турниров и обновлений платформы
            </NewsletterDescription>
            
            <NewsletterForm onSubmit={handleSubscribe}>
              <EmailInput
                type="email"
                placeholder="Ваш email адрес"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
              <SubscribeButton
                type="submit"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                🚀 Подписаться
              </SubscribeButton>
            </NewsletterForm>

            <NewsletterBenefits>
              <BenefitItem>✨ Эксклюзивные турниры</BenefitItem>
              <BenefitItem>🎁 Бонусы и промокоды</BenefitItem>
              <BenefitItem>📰 Первыми узнавайте новости</BenefitItem>
            </NewsletterBenefits>
          </motion.div>
        </FooterSection>
      </FooterContent>

      {/* Нижняя часть */}
      <FooterBottom>
        <BottomContent>
          <LegalLinks>
            {legalLinks.map((link, index) => (
              <LegalLink
                key={link.name}
                href={link.url}
                whileHover={{ color: '#4a90e2' }}
              >
                {link.name}
              </LegalLink>
            ))}
          </LegalLinks>

          <Copyright>
            <CopyrightText>
              © 2024 Козырь Мастер 4.0. Все права защищены.
            </CopyrightText>
            <TechInfo>
              Работает на квантовых технологиях и эмоциональном ИИ
            </TechInfo>
          </Copyright>

          <PoweredBy>
            <PoweredText>Создано с ❤️ командой Козырь Мастер</PoweredText>
            <TechStack>
              <TechItem>React</TechItem>
              <TechItem>Three.js</TechItem>
              <TechItem>Web3</TechItem>
              <TechItem>AI/ML</TechItem>
            </TechStack>
          </PoweredBy>
        </BottomContent>
      </FooterBottom>
    </FooterContainer>
  );
};

// Стилизованные компоненты
const FooterContainer = styled.footer`
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #4a90e2, transparent);
  }
`;

const FooterContent = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 4rem 2rem 2rem;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 3rem;
  
  @media (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 2rem 1rem 1rem;
  }
`;

const FooterSection = styled.div``;

const BrandSection = styled.div``;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const LogoIcon = styled.div`
  font-size: 3rem;
  filter: drop-shadow(0 0 20px rgba(74, 144, 226, 0.5));
`;

const LogoText = styled.div``;

const LogoTitle = styled.div`
  font-size: 1.8rem;
  font-weight: 900;
  background: linear-gradient(45deg, #4a90e2, #7b68ee);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1;
`;

const LogoVersion = styled.div`
  font-size: 1rem;
  color: #4a90e2;
  font-weight: 700;
`;

const BrandDescription = styled.p`
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
`;

const TechBadges = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
`;

const TechBadge = styled.span`
  background: rgba(74, 144, 226, 0.2);
  color: #4a90e2;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid rgba(74, 144, 226, 0.3);
`;

const SectionTitle = styled.h4`
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: white;
`;

const LinksList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const LinkItem = styled(motion.a)`
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  
  &:hover {
    color: #4a90e2;
  }
`;

const SocialLinks = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  margin-bottom: 1.5rem;
`;

const SocialLink = styled(motion.a)<{ color: string }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  text-decoration: none;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    background: ${props => `${props.color}20`};
    border-color: ${props => props.color};
  }
`;

const SocialIcon = styled.span`
  font-size: 1.2rem;
`;

const SocialName = styled.span`
  font-size: 0.85rem;
  font-weight: 600;
`;

const CommunityStats = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
`;

const StatItem = styled.div`
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 1rem 0.5rem;
`;

const StatValue = styled.div`
  font-size: 1.2rem;
  font-weight: 700;
  color: #4a90e2;
  margin-bottom: 0.25rem;
`;

const StatLabel = styled.div`
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
`;

const NewsletterDescription = styled.p`
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  line-height: 1.5;
`;

const NewsletterForm = styled.form`
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
`;

const EmailInput = styled.input`
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.75rem;
  color: white;
  font-size: 0.9rem;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
  
  &:focus {
    outline: none;
    border-color: #4a90e2;
    background: rgba(255, 255, 255, 0.15);
  }
`;

const SubscribeButton = styled(motion.button)`
  background: linear-gradient(135deg, #4a90e2, #7b68ee);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  white-space: nowrap;
`;

const NewsletterBenefits = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const BenefitItem = styled.div`
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
`;

const FooterBottom = styled.div`
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem 0;
`;

const BottomContent = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 2rem;
  align-items: center;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 1rem;
    padding: 0 1rem;
  }
`;

const LegalLinks = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  
  @media (max-width: 768px) {
    justify-content: center;
  }
`;

const LegalLink = styled(motion.a)`
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  font-size: 0.8rem;
  transition: color 0.3s ease;
  
  &:hover {
    color: #4a90e2;
  }
`;

const Copyright = styled.div`
  text-align: center;
`;

const CopyrightText = styled.div`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
`;

const TechInfo = styled.div`
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.75rem;
`;

const PoweredBy = styled.div`
  text-align: right;
  
  @media (max-width: 768px) {
    text-align: center;
  }
`;

const PoweredText = styled.div`
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
  margin-bottom: 0.5rem;
`;

const TechStack = styled.div`
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  
  @media (max-width: 768px) {
    justify-content: center;
  }
`;

const TechItem = styled.span`
  background: rgba(74, 144, 226, 0.1);
  color: #4a90e2;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
`;

export default Footer;
