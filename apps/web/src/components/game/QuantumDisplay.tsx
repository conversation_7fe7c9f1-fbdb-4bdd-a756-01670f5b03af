import React from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components';

interface QuantumMetrics {
  entropy: number;
  coherence: number;
  entanglement: number;
}

interface QuantumDisplayProps {
  metrics: QuantumMetrics;
  isQuantumAvailable: boolean;
  connectionStrength: number;
  lastUpdate: Date | null;
}

const QuantumContainer = styled(motion.div)`
  background: linear-gradient(135deg, rgba(123, 104, 238, 0.1), rgba(74, 144, 226, 0.1));
  border-radius: 15px;
  padding: 1rem;
  border: 2px solid rgba(123, 104, 238, 0.3);
  backdrop-filter: blur(10px);
  min-width: 200px;
  position: relative;
  overflow: hidden;
`;

const QuantumTitle = styled.div`
  font-weight: bold;
  color: #7b68ee;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
`;

const QuantumIcon = styled(motion.span)`
  font-size: 1.2rem;
`;

const StatusIndicator = styled(motion.div)<{ isActive: boolean }>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => props.isActive ? '#28a745' : '#dc3545'};
  box-shadow: 0 0 10px ${props => props.isActive ? '#28a745' : '#dc3545'};
`;

const MetricRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.4rem;
  font-size: 0.8rem;
`;

const MetricLabel = styled.span`
  color: rgba(255, 255, 255, 0.8);
`;

const MetricValue = styled(motion.span)<{ color: string }>`
  color: ${props => props.color};
  font-weight: bold;
  font-family: 'Courier New', monospace;
`;

const QuantumWave = styled(motion.div)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(123, 104, 238, 0.1) 50%,
    transparent 70%
  );
  pointer-events: none;
`;

const ConnectionBar = styled.div`
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.5rem;
`;

const ConnectionFill = styled(motion.div)<{ strength: number }>`
  height: 100%;
  background: linear-gradient(90deg, #7b68ee, #4a90e2);
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(123, 104, 238, 0.5);
`;

const LastUpdateText = styled.div`
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 0.5rem;
  text-align: center;
`;

const getMetricColor = (value: number): string => {
  if (value >= 0.8) return '#28a745';
  if (value >= 0.6) return '#ffc107';
  if (value >= 0.4) return '#fd7e14';
  return '#dc3545';
};

const formatMetricValue = (value: number): string => {
  return (value * 100).toFixed(1) + '%';
};

const getQuantumPhase = (metrics: QuantumMetrics): string => {
  const avg = (metrics.entropy + metrics.coherence + metrics.entanglement) / 3;
  if (avg >= 0.8) return '🌟 Оптимальная';
  if (avg >= 0.6) return '⚡ Стабильная';
  if (avg >= 0.4) return '🔄 Переходная';
  return '⚠️ Нестабильная';
};

const formatTimeAgo = (date: Date): string => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const seconds = Math.floor(diff / 1000);
  
  if (seconds < 60) return `${seconds}с назад`;
  const minutes = Math.floor(seconds / 60);
  if (minutes < 60) return `${minutes}м назад`;
  const hours = Math.floor(minutes / 60);
  return `${hours}ч назад`;
};

export const QuantumDisplay: React.FC<QuantumDisplayProps> = ({
  metrics,
  isQuantumAvailable,
  connectionStrength,
  lastUpdate
}) => {
  const quantumPhase = getQuantumPhase(metrics);

  return (
    <QuantumContainer
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Квантовая волна */}
      <QuantumWave
        animate={{
          x: [-100, 100],
          opacity: [0.3, 0.7, 0.3]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      <QuantumTitle>
        <QuantumIcon
          animate={{ rotate: 360 }}
          transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
        >
          ⚛️
        </QuantumIcon>
        Квантовые метрики
        <StatusIndicator
          isActive={isQuantumAvailable}
          animate={isQuantumAvailable ? { scale: [1, 1.2, 1] } : {}}
          transition={{ duration: 1, repeat: Infinity }}
        />
      </QuantumTitle>

      <MetricRow>
        <MetricLabel>Энтропия</MetricLabel>
        <MetricValue
          color={getMetricColor(metrics.entropy)}
          animate={{ opacity: [0.7, 1, 0.7] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          {formatMetricValue(metrics.entropy)}
        </MetricValue>
      </MetricRow>

      <MetricRow>
        <MetricLabel>Когерентность</MetricLabel>
        <MetricValue
          color={getMetricColor(metrics.coherence)}
          animate={{ opacity: [0.7, 1, 0.7] }}
          transition={{ duration: 2, repeat: Infinity, delay: 0.3 }}
        >
          {formatMetricValue(metrics.coherence)}
        </MetricValue>
      </MetricRow>

      <MetricRow>
        <MetricLabel>Запутанность</MetricLabel>
        <MetricValue
          color={getMetricColor(metrics.entanglement)}
          animate={{ opacity: [0.7, 1, 0.7] }}
          transition={{ duration: 2, repeat: Infinity, delay: 0.6 }}
        >
          {formatMetricValue(metrics.entanglement)}
        </MetricValue>
      </MetricRow>

      <MetricRow>
        <MetricLabel>Фаза</MetricLabel>
        <MetricValue color="#7b68ee">
          {quantumPhase}
        </MetricValue>
      </MetricRow>

      <div style={{ marginTop: '0.5rem' }}>
        <MetricLabel>Сила соединения</MetricLabel>
        <ConnectionBar>
          <ConnectionFill
            strength={connectionStrength}
            initial={{ width: 0 }}
            animate={{ width: `${connectionStrength * 100}%` }}
            transition={{ duration: 1 }}
          />
        </ConnectionBar>
      </div>

      {lastUpdate && (
        <LastUpdateText>
          Обновлено: {formatTimeAgo(lastUpdate)}
        </LastUpdateText>
      )}
    </QuantumContainer>
  );
};

// Компонент для отображения квантового статуса в игре
export const GameQuantumStatus: React.FC<{
  quantumStatus: any;
  gamePhase: string;
}> = ({ quantumStatus, gamePhase }) => {
  if (!quantumStatus.isQuantumAvailable) {
    return (
      <motion.div
        style={{
          position: 'fixed',
          bottom: '20px',
          left: '20px',
          background: 'rgba(220, 53, 69, 0.1)',
          border: '2px solid rgba(220, 53, 69, 0.3)',
          borderRadius: '10px',
          padding: '0.5rem 1rem',
          color: '#dc3545',
          fontSize: '0.8rem',
          zIndex: 1000
        }}
        initial={{ x: -200, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        ⚠️ Квантовая система недоступна
      </motion.div>
    );
  }

  return (
    <motion.div
      style={{
        position: 'fixed',
        bottom: '20px',
        left: '20px',
        zIndex: 1000
      }}
      initial={{ x: -200, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <QuantumDisplay
        metrics={quantumStatus.metrics}
        isQuantumAvailable={quantumStatus.isQuantumAvailable}
        connectionStrength={quantumStatus.connectionStrength}
        lastUpdate={quantumStatus.lastUpdate}
      />
    </motion.div>
  );
};
