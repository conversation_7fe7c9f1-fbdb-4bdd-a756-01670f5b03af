import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';
import { Card, CardStack } from './Card';
import { useGameState } from '../../hooks/useGameState';
import { Card as CardType, GameSettings } from '../../game/types';

const GameContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
`;

const GameHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  margin-bottom: 2rem;
`;

const GameTitle = styled.h1`
  font-size: 2rem;
  font-weight: bold;
  background: linear-gradient(45deg, #4a90e2, #7b68ee);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
`;

const GameInfo = styled.div`
  display: flex;
  gap: 2rem;
  align-items: center;
`;

const InfoItem = styled.div`
  text-align: center;
`;

const InfoLabel = styled.div`
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.25rem;
`;

const InfoValue = styled.div`
  font-size: 1.2rem;
  font-weight: bold;
  color: #4a90e2;
`;

const GameBoard = styled.div`
  width: 100%;
  max-width: 1200px;
  height: 600px;
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 2rem;
`;

const OpponentArea = styled.div`
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const PlayerName = styled.div`
  font-weight: bold;
  color: white;
  margin-bottom: 0.5rem;
`;

const CardCount = styled.div`
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
`;

const TableArea = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 1rem;
  align-items: center;
  min-height: 120px;
`;

const TableCard = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
`;

const DeckArea = styled.div`
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
`;

const TrumpCard = styled.div`
  transform: rotate(90deg);
  margin-top: 1rem;
`;

const PlayerArea = styled.div`
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
`;

const PlayerHand = styled.div`
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
`;

const ActionButton = styled(motion.button)<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  background: ${props => {
    switch (props.variant) {
      case 'primary': return 'linear-gradient(135deg, #4a90e2, #7b68ee)';
      case 'danger': return 'linear-gradient(135deg, #dc3545, #c82333)';
      default: return 'rgba(255, 255, 255, 0.1)';
    }
  }};
  color: white;
  border: none;
  border-radius: 10px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const GameStatus = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`;

const StatusText = styled.div`
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
`;

const PhaseText = styled.div`
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
`;

const SetupArea = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  padding: 4rem 2rem;
`;

const SetupForm = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
`;

const Input = styled.input`
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 0.75rem 1rem;
  color: white;
  font-size: 1rem;
  text-align: center;

  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }

  &:focus {
    outline: none;
    border-color: #4a90e2;
  }
`;

export const GameBoardComponent: React.FC = () => {
  const [selectedCard, setSelectedCard] = useState<CardType | null>(null);
  const [playerName, setPlayerName] = useState('');
  
  const gameState = useGameState();

  const defaultSettings: GameSettings = {
    maxPlayers: 2,
    deckSize: 36,
    allowAddCards: true,
    maxAttackCards: 6
  };

  const handleCreateGame = useCallback(() => {
    gameState.createNewGame(defaultSettings);
  }, [gameState]);

  const handleJoinGame = useCallback(() => {
    if (playerName.trim()) {
      gameState.joinGame(playerName.trim());
    }
  }, [gameState, playerName]);

  const handleCardClick = useCallback((card: CardType) => {
    if (gameState.canPlayCard(card)) {
      setSelectedCard(card);
    }
  }, [gameState]);

  const handleAttack = useCallback(() => {
    if (selectedCard && gameState.gameState) {
      gameState.playAction({
        type: 'attack',
        playerId: gameState.currentPlayer?.id || '',
        card: selectedCard
      });
      setSelectedCard(null);
    }
  }, [selectedCard, gameState]);

  const handleDefend = useCallback((position: number) => {
    if (selectedCard && gameState.gameState) {
      gameState.playAction({
        type: 'defend',
        playerId: gameState.currentPlayer?.id || '',
        card: selectedCard,
        targetPosition: position
      });
      setSelectedCard(null);
    }
  }, [selectedCard, gameState]);

  const handleTakeCards = useCallback(() => {
    if (gameState.gameState) {
      gameState.playAction({
        type: 'take_cards',
        playerId: gameState.currentPlayer?.id || ''
      });
    }
  }, [gameState]);

  const handleFinishTurn = useCallback(() => {
    if (gameState.gameState) {
      gameState.playAction({
        type: 'finish_turn',
        playerId: gameState.currentPlayer?.id || ''
      });
    }
  }, [gameState]);

  // Если игра не создана
  if (!gameState.gameState) {
    return (
      <GameContainer>
        <SetupArea>
          <GameTitle>🎮 Дурак - Козырь Мастер 4.0</GameTitle>
          <SetupForm>
            <ActionButton
              variant="primary"
              onClick={handleCreateGame}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Создать новую игру
            </ActionButton>
          </SetupForm>
        </SetupArea>
      </GameContainer>
    );
  }

  // Если игрок не присоединился
  if (!gameState.currentPlayer) {
    return (
      <GameContainer>
        <SetupArea>
          <GameTitle>Присоединиться к игре</GameTitle>
          <SetupForm>
            <Input
              type="text"
              placeholder="Введите ваше имя"
              value={playerName}
              onChange={(e) => setPlayerName(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleJoinGame()}
            />
            <ActionButton
              variant="primary"
              onClick={handleJoinGame}
              disabled={!playerName.trim()}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Присоединиться
            </ActionButton>
          </SetupForm>
        </SetupArea>
      </GameContainer>
    );
  }

  // Если игра не началась
  if (gameState.gameState.phase === 'waiting') {
    return (
      <GameContainer>
        <SetupArea>
          <GameTitle>Ожидание игроков...</GameTitle>
          <div>Игроков: {gameState.gameState.players.length}/{gameState.gameState.gameSettings.maxPlayers}</div>
          {gameState.gameState.players.length >= 2 && (
            <ActionButton
              variant="primary"
              onClick={gameState.startGame}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Начать игру
            </ActionButton>
          )}
        </SetupArea>
      </GameContainer>
    );
  }

  const currentGame = gameState.gameState;
  const opponent = currentGame.players.find(p => p.id !== gameState.currentPlayer?.id);
  const isMyTurn = gameState.isMyTurn;

  return (
    <GameContainer>
      <GameHeader>
        <GameTitle>🎮 Дурак</GameTitle>
        <GameInfo>
          <InfoItem>
            <InfoLabel>Козырь</InfoLabel>
            <InfoValue>{currentGame.trumpCard.suit} {currentGame.trumpCard.rank}</InfoValue>
          </InfoItem>
          <InfoItem>
            <InfoLabel>Фаза</InfoLabel>
            <InfoValue>{currentGame.phase}</InfoValue>
          </InfoItem>
          <InfoItem>
            <InfoLabel>Ход</InfoLabel>
            <InfoValue>{isMyTurn ? 'Ваш' : 'Противника'}</InfoValue>
          </InfoItem>
        </GameInfo>
      </GameHeader>

      <GameStatus>
        <StatusText>
          {currentGame.phase === 'finished' 
            ? `Игра окончена! ${currentGame.winner ? 'Победитель: ' + currentGame.players.find(p => p.id === currentGame.winner)?.name : 'Ничья'}`
            : isMyTurn 
              ? 'Ваш ход' 
              : `Ход игрока ${currentGame.players[currentGame.currentPlayerIndex]?.name}`
          }
        </StatusText>
        <PhaseText>
          {currentGame.phase === 'attacking' && 'Фаза атаки'}
          {currentGame.phase === 'defending' && 'Фаза защиты'}
          {currentGame.phase === 'adding' && 'Подкидывание карт'}
        </PhaseText>
      </GameStatus>

      <GameBoard>
        {/* Противник */}
        {opponent && (
          <OpponentArea>
            <div>
              <PlayerName>{opponent.name}</PlayerName>
              <CardCount>{opponent.cards.length} карт</CardCount>
            </div>
            <CardStack count={opponent.cards.length} size="small" />
          </OpponentArea>
        )}

        {/* Стол */}
        <TableArea>
          <AnimatePresence>
            {currentGame.table.map((tableCard, index) => (
              <TableCard key={index}>
                <Card 
                  card={tableCard.attackCard} 
                  size="medium"
                />
                {tableCard.defendCard && (
                  <Card 
                    card={tableCard.defendCard} 
                    size="medium"
                  />
                )}
                {!tableCard.defendCard && selectedCard && currentGame.phase === 'defending' && (
                  <ActionButton
                    variant="secondary"
                    onClick={() => handleDefend(index)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Защитить
                  </ActionButton>
                )}
              </TableCard>
            ))}
          </AnimatePresence>
        </TableArea>

        {/* Колода и козырь */}
        <DeckArea>
          <CardStack count={currentGame.deck.length} size="small" />
          <TrumpCard>
            <Card card={currentGame.trumpCard} size="small" />
          </TrumpCard>
        </DeckArea>

        {/* Игрок */}
        <PlayerArea>
          <div>
            <PlayerName>{gameState.currentPlayer.name}</PlayerName>
            <CardCount>{gameState.currentPlayer.cards.length} карт</CardCount>
          </div>
          
          <PlayerHand>
            <AnimatePresence>
              {gameState.currentPlayer.cards.map((card) => (
                <Card
                  key={card.id}
                  card={card}
                  isPlayable={gameState.canPlayCard(card)}
                  isSelected={selectedCard?.id === card.id}
                  onClick={() => handleCardClick(card)}
                  size="medium"
                />
              ))}
            </AnimatePresence>
          </PlayerHand>

          {/* Кнопки действий */}
          {isMyTurn && (
            <ActionButtons>
              {currentGame.phase === 'attacking' && selectedCard && (
                <ActionButton
                  variant="primary"
                  onClick={handleAttack}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Атаковать
                </ActionButton>
              )}
              
              {currentGame.phase === 'defending' && (
                <ActionButton
                  variant="danger"
                  onClick={handleTakeCards}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Взять карты
                </ActionButton>
              )}
              
              {currentGame.phase === 'adding' && (
                <ActionButton
                  variant="primary"
                  onClick={handleFinishTurn}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Завершить ход
                </ActionButton>
              )}
            </ActionButtons>
          )}
        </PlayerArea>
      </GameBoard>

      {/* Сброс игры */}
      <ActionButton
        variant="secondary"
        onClick={gameState.resetGame}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        Новая игра
      </ActionButton>
    </GameContainer>
  );
};
