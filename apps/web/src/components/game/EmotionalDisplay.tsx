import React from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components';

interface EmotionalState {
  confidence: number;
  aggression: number;
  patience: number;
}

interface EmotionalDisplayProps {
  playerName: string;
  emotionalState: EmotionalState;
  isBot?: boolean;
  analysis?: {
    playerStress: number;
    playerConfidence: number;
    gamePhase: 'early' | 'mid' | 'late';
    riskLevel: number;
    recommendedStrategy: 'aggressive' | 'defensive' | 'balanced';
  };
}

const EmotionalContainer = styled(motion.div)`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  min-width: 250px;
`;

const PlayerTitle = styled.div`
  font-weight: bold;
  color: white;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const BotIcon = styled.span`
  background: linear-gradient(45deg, #4a90e2, #7b68ee);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: bold;
`;

const MetricRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
`;

const MetricLabel = styled.span`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
`;

const MetricBar = styled.div`
  width: 100px;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
`;

const MetricFill = styled(motion.div)<{ color: string }>`
  height: 100%;
  background: ${props => props.color};
  border-radius: 4px;
`;

const StrategyBadge = styled(motion.div)<{ strategy: string }>`
  background: ${props => {
    switch (props.strategy) {
      case 'aggressive': return 'linear-gradient(45deg, #dc3545, #c82333)';
      case 'defensive': return 'linear-gradient(45deg, #28a745, #20c997)';
      default: return 'linear-gradient(45deg, #ffc107, #fd7e14)';
    }
  }};
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
  text-align: center;
  margin-top: 0.5rem;
`;

const PhaseIndicator = styled.div<{ phase: string }>`
  color: ${props => {
    switch (props.phase) {
      case 'early': return '#28a745';
      case 'mid': return '#ffc107';
      case 'late': return '#dc3545';
      default: return '#6c757d';
    }
  }};
  font-weight: bold;
  font-size: 0.8rem;
`;

const StressIndicator = styled(motion.div)<{ level: number }>`
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: ${props => {
    if (props.level < 0.3) return '#28a745';
    if (props.level < 0.7) return '#ffc107';
    return '#dc3545';
  }};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  color: white;
  font-weight: bold;
`;

const getEmotionEmoji = (state: EmotionalState): string => {
  const { confidence, aggression, patience } = state;
  
  if (confidence > 0.8 && aggression > 0.7) return '😤'; // Агрессивный и уверенный
  if (confidence > 0.8 && patience > 0.7) return '😌'; // Уверенный и терпеливый
  if (confidence < 0.3 && patience < 0.3) return '😰'; // Неуверенный и нетерпеливый
  if (aggression > 0.8) return '😠'; // Очень агрессивный
  if (patience > 0.8) return '🧘'; // Очень терпеливый
  if (confidence > 0.7) return '😊'; // Уверенный
  if (confidence < 0.3) return '😟'; // Неуверенный
  return '😐'; // Нейтральный
};

const getStrategyText = (strategy: string): string => {
  switch (strategy) {
    case 'aggressive': return 'Агрессивная';
    case 'defensive': return 'Защитная';
    case 'balanced': return 'Сбалансированная';
    default: return 'Неизвестная';
  }
};

const getPhaseText = (phase: string): string => {
  switch (phase) {
    case 'early': return 'Начало';
    case 'mid': return 'Середина';
    case 'late': return 'Конец';
    default: return 'Неизвестно';
  }
};

export const EmotionalDisplay: React.FC<EmotionalDisplayProps> = ({
  playerName,
  emotionalState,
  isBot = false,
  analysis
}) => {
  const emotionEmoji = getEmotionEmoji(emotionalState);

  return (
    <EmotionalContainer
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <PlayerTitle>
        <span>{emotionEmoji}</span>
        <span>{playerName}</span>
        {isBot && <BotIcon>ИИ</BotIcon>}
      </PlayerTitle>

      <MetricRow>
        <MetricLabel>Уверенность</MetricLabel>
        <MetricBar>
          <MetricFill
            color="linear-gradient(90deg, #4a90e2, #7b68ee)"
            initial={{ width: 0 }}
            animate={{ width: `${emotionalState.confidence * 100}%` }}
            transition={{ duration: 0.5 }}
          />
        </MetricBar>
      </MetricRow>

      <MetricRow>
        <MetricLabel>Агрессия</MetricLabel>
        <MetricBar>
          <MetricFill
            color="linear-gradient(90deg, #dc3545, #c82333)"
            initial={{ width: 0 }}
            animate={{ width: `${emotionalState.aggression * 100}%` }}
            transition={{ duration: 0.5 }}
          />
        </MetricBar>
      </MetricRow>

      <MetricRow>
        <MetricLabel>Терпение</MetricLabel>
        <MetricBar>
          <MetricFill
            color="linear-gradient(90deg, #28a745, #20c997)"
            initial={{ width: 0 }}
            animate={{ width: `${emotionalState.patience * 100}%` }}
            transition={{ duration: 0.5 }}
          />
        </MetricBar>
      </MetricRow>

      {analysis && (
        <>
          <MetricRow>
            <MetricLabel>Стресс</MetricLabel>
            <StressIndicator
              level={analysis.playerStress}
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              {Math.round(analysis.playerStress * 100)}
            </StressIndicator>
          </MetricRow>

          <MetricRow>
            <MetricLabel>Фаза игры</MetricLabel>
            <PhaseIndicator phase={analysis.gamePhase}>
              {getPhaseText(analysis.gamePhase)}
            </PhaseIndicator>
          </MetricRow>

          <StrategyBadge
            strategy={analysis.recommendedStrategy}
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.3 }}
          >
            {getStrategyText(analysis.recommendedStrategy)}
          </StrategyBadge>
        </>
      )}
    </EmotionalContainer>
  );
};

// Компонент для отображения эмоционального анализа в реальном времени
export const EmotionalAnalysisPanel: React.FC<{
  botState: EmotionalState | null;
  playerAnalysis: any;
  gamePhase: string;
}> = ({ botState, playerAnalysis, gamePhase }) => {
  if (!botState && !playerAnalysis) return null;

  return (
    <motion.div
      style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        zIndex: 1000,
        display: 'flex',
        flexDirection: 'column',
        gap: '1rem'
      }}
      initial={{ x: 300, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {botState && (
        <EmotionalDisplay
          playerName="ИИ Противник"
          emotionalState={botState}
          isBot={true}
        />
      )}
      
      {playerAnalysis && (
        <EmotionalDisplay
          playerName="Ваш анализ"
          emotionalState={{
            confidence: playerAnalysis.playerConfidence,
            aggression: 1 - playerAnalysis.playerStress, // Инвертируем стресс
            patience: playerAnalysis.riskLevel < 0.5 ? 0.8 : 0.3
          }}
          analysis={playerAnalysis}
        />
      )}
    </motion.div>
  );
};
