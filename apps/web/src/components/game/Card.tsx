import React from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import { Card as CardType, SUIT_SYMBOLS, SUIT_COLORS } from '../../game/types';

interface CardProps {
  card: CardType;
  isPlayable?: boolean;
  isSelected?: boolean;
  isHidden?: boolean;
  size?: 'small' | 'medium' | 'large';
  onClick?: () => void;
  onHover?: () => void;
  style?: React.CSSProperties;
}

const CardContainer = styled(motion.div)<{
  isPlayable: boolean;
  isSelected: boolean;
  isHidden: boolean;
  size: string;
  color: 'red' | 'black';
}>`
  width: ${props => {
    switch (props.size) {
      case 'small': return '60px';
      case 'large': return '120px';
      default: return '80px';
    }
  }};
  height: ${props => {
    switch (props.size) {
      case 'small': return '84px';
      case 'large': return '168px';
      default: return '112px';
    }
  }};
  
  background: ${props => props.isHidden 
    ? 'linear-gradient(135deg, #1a1a2e, #16213e)' 
    : 'linear-gradient(135deg, #ffffff, #f8f9fa)'};
  
  border: 2px solid ${props => {
    if (props.isSelected) return '#4a90e2';
    if (props.isPlayable) return '#28a745';
    return props.isHidden ? '#444' : '#ddd';
  }};
  
  border-radius: 8px;
  cursor: ${props => props.isPlayable ? 'pointer' : 'default'};
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 4px;
  
  box-shadow: ${props => {
    if (props.isSelected) return '0 0 20px rgba(74, 144, 226, 0.5)';
    if (props.isPlayable) return '0 0 15px rgba(40, 167, 69, 0.3)';
    return '0 2px 8px rgba(0, 0, 0, 0.1)';
  }};
  
  color: ${props => props.isHidden ? '#fff' : props.color === 'red' ? '#dc3545' : '#212529'};
  
  transition: all 0.2s ease;
  
  &:hover {
    transform: ${props => props.isPlayable ? 'translateY(-5px) scale(1.05)' : 'none'};
    box-shadow: ${props => props.isPlayable 
      ? '0 8px 25px rgba(0, 0, 0, 0.2)' 
      : '0 2px 8px rgba(0, 0, 0, 0.1)'};
  }
`;

const CardRank = styled.div<{ size: string }>`
  font-size: ${props => {
    switch (props.size) {
      case 'small': return '12px';
      case 'large': return '20px';
      default: return '16px';
    }
  }};
  font-weight: bold;
  line-height: 1;
`;

const CardSuit = styled.div<{ size: string }>`
  font-size: ${props => {
    switch (props.size) {
      case 'small': return '16px';
      case 'large': return '32px';
      default: return '24px';
    }
  }};
  line-height: 1;
`;

const CardCenter = styled.div<{ size: string }>`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: ${props => {
    switch (props.size) {
      case 'small': return '20px';
      case 'large': return '40px';
      default: return '30px';
    }
  }};
  opacity: 0.3;
`;

const CardBack = styled.div<{ size: string }>`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: ${props => {
    switch (props.size) {
      case 'small': return '24px';
      case 'large': return '48px';
      default: return '36px';
    }
  }};
  color: #4a90e2;
`;

const PlayableIndicator = styled(motion.div)`
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  background: #28a745;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
`;

const SelectedIndicator = styled(motion.div)`
  position: absolute;
  top: -5px;
  left: -5px;
  width: 20px;
  height: 20px;
  background: #4a90e2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
`;

export const Card: React.FC<CardProps> = ({
  card,
  isPlayable = false,
  isSelected = false,
  isHidden = false,
  size = 'medium',
  onClick,
  onHover,
  style
}) => {
  const suitColor = SUIT_COLORS[card.suit];
  const suitSymbol = SUIT_SYMBOLS[card.suit];

  const handleClick = () => {
    if (isPlayable && onClick) {
      onClick();
    }
  };

  return (
    <CardContainer
      isPlayable={isPlayable}
      isSelected={isSelected}
      isHidden={isHidden}
      size={size}
      color={suitColor}
      onClick={handleClick}
      onMouseEnter={onHover}
      style={style}
      whileHover={isPlayable ? { scale: 1.05, y: -5 } : {}}
      whileTap={isPlayable ? { scale: 0.95 } : {}}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.2 }}
    >
      {isHidden ? (
        <CardBack size={size}>🂠</CardBack>
      ) : (
        <>
          {/* Верхний левый угол */}
          <div>
            <CardRank size={size}>{card.rank}</CardRank>
            <CardSuit size={size}>{suitSymbol}</CardSuit>
          </div>
          
          {/* Центральный символ */}
          <CardCenter size={size}>{suitSymbol}</CardCenter>
          
          {/* Нижний правый угол (перевернутый) */}
          <div style={{ 
            transform: 'rotate(180deg)', 
            alignSelf: 'flex-end' 
          }}>
            <CardRank size={size}>{card.rank}</CardRank>
            <CardSuit size={size}>{suitSymbol}</CardSuit>
          </div>
        </>
      )}
      
      {/* Индикаторы */}
      {isPlayable && !isSelected && (
        <PlayableIndicator
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.1 }}
        >
          ✓
        </PlayableIndicator>
      )}
      
      {isSelected && (
        <SelectedIndicator
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.1 }}
        >
          ★
        </SelectedIndicator>
      )}
    </CardContainer>
  );
};

// Компонент для отображения стопки карт
export const CardStack: React.FC<{
  count: number;
  size?: 'small' | 'medium' | 'large';
  topCard?: CardType;
}> = ({ count, size = 'medium', topCard }) => {
  return (
    <div style={{ position: 'relative' }}>
      {/* Стопка карт */}
      {Array.from({ length: Math.min(count, 3) }).map((_, index) => (
        <Card
          key={index}
          card={topCard || { 
            suit: 'spades', 
            rank: 'A', 
            id: 'stack', 
            value: 14 
          }}
          isHidden={!topCard}
          size={size}
          style={{
            position: index > 0 ? 'absolute' : 'relative',
            top: index * -2,
            left: index * -2,
            zIndex: 3 - index
          }}
        />
      ))}
      
      {/* Счетчик карт */}
      {count > 3 && (
        <div style={{
          position: 'absolute',
          bottom: -10,
          right: -10,
          background: '#4a90e2',
          color: 'white',
          borderRadius: '50%',
          width: 24,
          height: 24,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          fontWeight: 'bold'
        }}>
          {count}
        </div>
      )}
    </div>
  );
};
