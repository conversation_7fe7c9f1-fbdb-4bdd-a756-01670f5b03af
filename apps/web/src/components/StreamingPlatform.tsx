import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';

interface Stream {
  id: string;
  title: string;
  streamer: string;
  viewers: number;
  game: string;
  thumbnail: string;
  isLive: boolean;
  quality: '720p' | '1080p' | '4K';
  language: string;
  tags: string[];
}

interface StreamingPlatformProps {
  onStartStreaming?: () => void;
}

const StreamingPlatform: React.FC<StreamingPlatformProps> = ({ onStartStreaming }) => {
  const [activeTab, setActiveTab] = useState<'browse' | 'stream' | 'analytics'>('browse');
  const [featuredStreams, setFeaturedStreams] = useState<Stream[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamStats, setStreamStats] = useState({
    viewers: 0,
    followers: 1247,
    donations: 156.50,
    chatMessages: 0
  });
  const [streamSettings, setStreamSettings] = useState({
    title: 'Козырь Мастер - Эпические игры!',
    quality: '1080p' as const,
    enableChat: true,
    enableDonations: true,
    enableAI: true
  });

  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    // Симуляция популярных стримов
    setFeaturedStreams([
      {
        id: '1',
        title: 'Турнир по Дураку - Финал!',
        streamer: 'ProGamer2024',
        viewers: 2847,
        game: 'Дурак',
        thumbnail: '🃏',
        isLive: true,
        quality: '1080p',
        language: 'RU',
        tags: ['Турнир', 'Финал', 'Профи']
      },
      {
        id: '2',
        title: 'Покер с ИИ - Кто умнее?',
        streamer: 'AIChallenger',
        viewers: 1523,
        game: 'Покер',
        thumbnail: '♠️',
        isLive: true,
        quality: '4K',
        language: 'EN',
        tags: ['ИИ', 'Покер', 'Вызов']
      },
      {
        id: '3',
        title: 'Обучение новичков',
        streamer: 'CardMaster',
        viewers: 892,
        game: 'Дурак',
        thumbnail: '🎓',
        isLive: true,
        quality: '720p',
        language: 'RU',
        tags: ['Обучение', 'Новички']
      }
    ]);

    // Симуляция обновления статистики стрима
    if (isStreaming) {
      const interval = setInterval(() => {
        setStreamStats(prev => ({
          ...prev,
          viewers: prev.viewers + Math.floor(Math.random() * 10 - 3),
          chatMessages: prev.chatMessages + Math.floor(Math.random() * 5),
          donations: prev.donations + (Math.random() > 0.9 ? Math.random() * 20 : 0)
        }));
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [isStreaming]);

  const startStream = async () => {
    try {
      // Запрос доступа к камере и микрофону
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
      
      setIsStreaming(true);
      setStreamStats(prev => ({ ...prev, viewers: 1 }));
    } catch (error) {
      console.error('Ошибка доступа к медиа:', error);
      // Симуляция стрима без реального видео
      setIsStreaming(true);
      setStreamStats(prev => ({ ...prev, viewers: 1 }));
    }
  };

  const stopStream = () => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
    }
    setIsStreaming(false);
    setStreamStats(prev => ({ ...prev, viewers: 0 }));
  };

  return (
    <StreamingContainer>
      <ContentWrapper>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <SectionTitle>Стриминг платформа</SectionTitle>
          <SectionSubtitle>
            Профессиональные инструменты для создателей контента
          </SectionSubtitle>
        </motion.div>

        {/* Навигация */}
        <TabNavigation>
          {[
            { id: 'browse', label: 'Обзор стримов', icon: '📺' },
            { id: 'stream', label: 'Мой стрим', icon: '🎥' },
            { id: 'analytics', label: 'Аналитика', icon: '📊' }
          ].map(tab => (
            <TabButton
              key={tab.id}
              active={activeTab === tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <TabIcon>{tab.icon}</TabIcon>
              <TabLabel>{tab.label}</TabLabel>
            </TabButton>
          ))}
        </TabNavigation>

        {/* Контент табов */}
        <TabContent>
          <AnimatePresence mode="wait">
            {activeTab === 'browse' && (
              <motion.div
                key="browse"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
              >
                <BrowseSection>
                  <SectionHeader>
                    <SectionSubtitle>Популярные стримы</SectionSubtitle>
                    <LiveIndicator>
                      <LiveDot />
                      {featuredStreams.length} стримов в эфире
                    </LiveIndicator>
                  </SectionHeader>

                  <StreamsGrid>
                    {featuredStreams.map(stream => (
                      <StreamCard
                        key={stream.id}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <StreamThumbnail>
                          <ThumbnailIcon>{stream.thumbnail}</ThumbnailIcon>
                          <StreamOverlay>
                            <QualityBadge>{stream.quality}</QualityBadge>
                            <ViewerCount>👁️ {stream.viewers.toLocaleString()}</ViewerCount>
                          </StreamOverlay>
                          {stream.isLive && <LiveBadge>LIVE</LiveBadge>}
                        </StreamThumbnail>
                        
                        <StreamInfo>
                          <StreamTitle>{stream.title}</StreamTitle>
                          <StreamMeta>
                            <StreamerName>{stream.streamer}</StreamerName>
                            <StreamGame>{stream.game}</StreamGame>
                          </StreamMeta>
                          <StreamTags>
                            {stream.tags.map(tag => (
                              <StreamTag key={tag}>{tag}</StreamTag>
                            ))}
                          </StreamTags>
                        </StreamInfo>
                      </StreamCard>
                    ))}
                  </StreamsGrid>
                </BrowseSection>
              </motion.div>
            )}

            {activeTab === 'stream' && (
              <motion.div
                key="stream"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
              >
                <StreamSection>
                  <StreamContainer>
                    <StreamPreview>
                      {isStreaming ? (
                        <VideoPreview
                          ref={videoRef}
                          autoPlay
                          muted
                          playsInline
                        />
                      ) : (
                        <PreviewPlaceholder>
                          <PlaceholderIcon>🎥</PlaceholderIcon>
                          <PlaceholderText>Предпросмотр стрима</PlaceholderText>
                        </PreviewPlaceholder>
                      )}
                      
                      {isStreaming && (
                        <StreamOverlays>
                          <StreamStatus>
                            <StatusDot />
                            В ЭФИРЕ
                          </StreamStatus>
                          <ViewerCounter>
                            👁️ {streamStats.viewers}
                          </ViewerCounter>
                        </StreamOverlays>
                      )}
                    </StreamPreview>

                    <StreamControls>
                      <ControlsRow>
                        {!isStreaming ? (
                          <StartStreamButton
                            onClick={startStream}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            🚀 Начать стрим
                          </StartStreamButton>
                        ) : (
                          <StopStreamButton
                            onClick={stopStream}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            ⏹️ Остановить стрим
                          </StopStreamButton>
                        )}
                        
                        <ControlButton
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          ⚙️ Настройки
                        </ControlButton>
                      </ControlsRow>

                      <StreamSettings>
                        <SettingGroup>
                          <SettingLabel>Название стрима:</SettingLabel>
                          <SettingInput
                            value={streamSettings.title}
                            onChange={(e) => setStreamSettings(prev => ({
                              ...prev,
                              title: e.target.value
                            }))}
                          />
                        </SettingGroup>

                        <SettingGroup>
                          <SettingLabel>Качество:</SettingLabel>
                          <SettingSelect
                            value={streamSettings.quality}
                            onChange={(e) => setStreamSettings(prev => ({
                              ...prev,
                              quality: e.target.value as any
                            }))}
                          >
                            <option value="720p">720p</option>
                            <option value="1080p">1080p</option>
                            <option value="4K">4K</option>
                          </SettingSelect>
                        </SettingGroup>

                        <SettingToggles>
                          <ToggleOption>
                            <ToggleInput
                              type="checkbox"
                              checked={streamSettings.enableChat}
                              onChange={(e) => setStreamSettings(prev => ({
                                ...prev,
                                enableChat: e.target.checked
                              }))}
                            />
                            <ToggleLabel>Включить чат</ToggleLabel>
                          </ToggleOption>

                          <ToggleOption>
                            <ToggleInput
                              type="checkbox"
                              checked={streamSettings.enableDonations}
                              onChange={(e) => setStreamSettings(prev => ({
                                ...prev,
                                enableDonations: e.target.checked
                              }))}
                            />
                            <ToggleLabel>Принимать донаты</ToggleLabel>
                          </ToggleOption>

                          <ToggleOption>
                            <ToggleInput
                              type="checkbox"
                              checked={streamSettings.enableAI}
                              onChange={(e) => setStreamSettings(prev => ({
                                ...prev,
                                enableAI: e.target.checked
                              }))}
                            />
                            <ToggleLabel>ИИ-анализ</ToggleLabel>
                          </ToggleOption>
                        </SettingToggles>
                      </StreamSettings>
                    </StreamControls>
                  </StreamContainer>

                  {isStreaming && (
                    <LiveStats>
                      <StatCard>
                        <StatIcon>👁️</StatIcon>
                        <StatValue>{streamStats.viewers}</StatValue>
                        <StatLabel>Зрителей</StatLabel>
                      </StatCard>
                      
                      <StatCard>
                        <StatIcon>👥</StatIcon>
                        <StatValue>{streamStats.followers}</StatValue>
                        <StatLabel>Подписчиков</StatLabel>
                      </StatCard>
                      
                      <StatCard>
                        <StatIcon>💰</StatIcon>
                        <StatValue>${streamStats.donations.toFixed(2)}</StatValue>
                        <StatLabel>Донатов</StatLabel>
                      </StatCard>
                      
                      <StatCard>
                        <StatIcon>💬</StatIcon>
                        <StatValue>{streamStats.chatMessages}</StatValue>
                        <StatLabel>Сообщений</StatLabel>
                      </StatCard>
                    </LiveStats>
                  )}
                </StreamSection>
              </motion.div>
            )}

            {activeTab === 'analytics' && (
              <motion.div
                key="analytics"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
              >
                <AnalyticsSection>
                  <AnalyticsGrid>
                    <AnalyticsCard>
                      <CardTitle>Статистика за месяц</CardTitle>
                      <MetricsList>
                        <MetricItem>
                          <MetricLabel>Общее время стримов:</MetricLabel>
                          <MetricValue>47ч 23м</MetricValue>
                        </MetricItem>
                        <MetricItem>
                          <MetricLabel>Средние зрители:</MetricLabel>
                          <MetricValue>234</MetricValue>
                        </MetricItem>
                        <MetricItem>
                          <MetricLabel>Пиковые зрители:</MetricLabel>
                          <MetricValue>1,847</MetricValue>
                        </MetricItem>
                        <MetricItem>
                          <MetricLabel>Новых подписчиков:</MetricLabel>
                          <MetricValue>+156</MetricValue>
                        </MetricItem>
                      </MetricsList>
                    </AnalyticsCard>

                    <AnalyticsCard>
                      <CardTitle>Доходы</CardTitle>
                      <RevenueChart>
                        <ChartBar height={60}>
                          <ChartValue>$245</ChartValue>
                        </ChartBar>
                        <ChartBar height={80}>
                          <ChartValue>$320</ChartValue>
                        </ChartBar>
                        <ChartBar height={45}>
                          <ChartValue>$180</ChartValue>
                        </ChartBar>
                        <ChartBar height={95}>
                          <ChartValue>$425</ChartValue>
                        </ChartBar>
                      </RevenueChart>
                    </AnalyticsCard>

                    <AnalyticsCard>
                      <CardTitle>ИИ Инсайты</CardTitle>
                      <AIInsights>
                        <InsightItem>
                          <InsightIcon>🎯</InsightIcon>
                          <InsightText>
                            Лучшее время для стрима: 19:00-22:00
                          </InsightText>
                        </InsightItem>
                        <InsightItem>
                          <InsightIcon>📈</InsightIcon>
                          <InsightText>
                            Турнирные стримы привлекают +40% зрителей
                          </InsightText>
                        </InsightItem>
                        <InsightItem>
                          <InsightIcon>💡</InsightIcon>
                          <InsightText>
                            Рекомендуем добавить больше интерактива
                          </InsightText>
                        </InsightItem>
                      </AIInsights>
                    </AnalyticsCard>
                  </AnalyticsGrid>
                </AnalyticsSection>
              </motion.div>
            )}
          </AnimatePresence>
        </TabContent>
      </ContentWrapper>
    </StreamingContainer>
  );
};

// Стилизованные компоненты
const StreamingContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  padding: 4rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ContentWrapper = styled.div`
  max-width: 1400px;
  width: 100%;
`;

const SectionTitle = styled.h2`
  font-size: 3.5rem;
  font-weight: 900;
  text-align: center;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const SectionSubtitle = styled.p`
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin-bottom: 4rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
`;

const TabNavigation = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 0.5rem;
`;

const TabButton = styled(motion.button)<{ active: boolean }>`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  border: none;
  background: ${props => props.active ? 'rgba(74, 144, 226, 0.3)' : 'transparent'};
  color: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.7)'};
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;

  &:hover {
    background: rgba(74, 144, 226, 0.1);
    color: #4a90e2;
  }
`;

const TabIcon = styled.span`
  font-size: 1.2rem;
`;

const TabLabel = styled.span`
  font-size: 0.9rem;
`;

const TabContent = styled.div`
  min-height: 500px;
`;

const BrowseSection = styled.div``;

const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const LiveIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
`;

const LiveDot = styled.div`
  width: 8px;
  height: 8px;
  background: #ef4444;
  border-radius: 50%;
  animation: pulse 2s infinite;
`;

const StreamsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
`;

const StreamCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const StreamThumbnail = styled.div`
  position: relative;
  height: 180px;
  background: linear-gradient(135deg, #1a1a2e, #16213e);
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ThumbnailIcon = styled.div`
  font-size: 4rem;
  filter: drop-shadow(0 0 20px currentColor);
`;

const StreamOverlay = styled.div`
  position: absolute;
  bottom: 0.5rem;
  left: 0.5rem;
  right: 0.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const QualityBadge = styled.div`
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 5px;
  font-size: 0.7rem;
  font-weight: 600;
`;

const ViewerCount = styled.div`
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 5px;
  font-size: 0.7rem;
  font-weight: 600;
`;

const LiveBadge = styled.div`
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #ef4444;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 5px;
  font-size: 0.7rem;
  font-weight: 700;
  animation: pulse 2s infinite;
`;

const StreamInfo = styled.div`
  padding: 1rem;
`;

const StreamTitle = styled.h4`
  color: white;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  line-height: 1.3;
`;

const StreamMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
`;

const StreamerName = styled.span`
  color: #4a90e2;
  font-weight: 600;
  font-size: 0.9rem;
`;

const StreamGame = styled.span`
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
`;

const StreamTags = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
`;

const StreamTag = styled.span`
  background: rgba(74, 144, 226, 0.2);
  color: #4a90e2;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 500;
`;

const StreamSection = styled.div``;

const StreamContainer = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
`;

const StreamPreview = styled.div`
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  overflow: hidden;
  aspect-ratio: 16/9;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const VideoPreview = styled.video`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const PreviewPlaceholder = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1a1a2e, #16213e);
`;

const PlaceholderIcon = styled.div`
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
`;

const PlaceholderText = styled.div`
  color: rgba(255, 255, 255, 0.5);
  font-size: 1.1rem;
`;

const StreamOverlays = styled.div`
  position: absolute;
  top: 1rem;
  left: 1rem;
  right: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const StreamStatus = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(239, 68, 68, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 700;
  font-size: 0.9rem;
`;

const StatusDot = styled.div`
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
  animation: pulse 2s infinite;
`;

const ViewerCounter = styled.div`
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
`;

const StreamControls = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const ControlsRow = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const StartStreamButton = styled(motion.button)`
  flex: 1;
  background: linear-gradient(135deg, #4ade80, #22c55e);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 1rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
`;

const StopStreamButton = styled(motion.button)`
  flex: 1;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 1rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
`;

const ControlButton = styled(motion.button)`
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 1rem;
  cursor: pointer;
  font-weight: 600;
`;

const StreamSettings = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const SettingGroup = styled.div``;

const SettingLabel = styled.label`
  display: block;
  color: white;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
`;

const SettingInput = styled.input`
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.75rem;
  color: white;
  font-size: 0.9rem;

  &:focus {
    outline: none;
    border-color: #4a90e2;
  }
`;

const SettingSelect = styled.select`
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.75rem;
  color: white;
  font-size: 0.9rem;

  &:focus {
    outline: none;
    border-color: #4a90e2;
  }
`;

const SettingToggles = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const ToggleOption = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const ToggleInput = styled.input`
  width: 18px;
  height: 18px;
  accent-color: #4a90e2;
`;

const ToggleLabel = styled.label`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  cursor: pointer;
`;

const LiveStats = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
`;

const StatCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const StatIcon = styled.div`
  font-size: 2rem;
  margin-bottom: 0.5rem;
`;

const StatValue = styled.div`
  color: #4a90e2;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
`;

const StatLabel = styled.div`
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
`;

const AnalyticsSection = styled.div``;

const AnalyticsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
`;

const AnalyticsCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const CardTitle = styled.h4`
  color: white;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
`;

const MetricsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const MetricItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const MetricLabel = styled.span`
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
`;

const MetricValue = styled.span`
  color: #4a90e2;
  font-weight: 600;
`;

const RevenueChart = styled.div`
  display: flex;
  align-items: end;
  gap: 1rem;
  height: 120px;
`;

const ChartBar = styled.div<{ height: number }>`
  flex: 1;
  height: ${props => props.height}%;
  background: linear-gradient(to top, #4a90e2, #7b68ee);
  border-radius: 5px 5px 0 0;
  position: relative;
  display: flex;
  align-items: end;
  justify-content: center;
  padding-bottom: 0.5rem;
`;

const ChartValue = styled.div`
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
`;

const AIInsights = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const InsightItem = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const InsightIcon = styled.div`
  font-size: 1.5rem;
`;

const InsightText = styled.div`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  line-height: 1.4;
`;

export default StreamingPlatform;
