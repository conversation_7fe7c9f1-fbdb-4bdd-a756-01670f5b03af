import React, { useState, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';

interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  category: string;
  unlockedAt?: Date;
  progress?: number;
  maxProgress?: number;
}

interface AchievementUnlockedProps {
  achievement: Achievement;
  onClose: () => void;
}

const slideInFromTop = keyframes`
  0% {
    transform: translateY(-100%) scale(0.8);
    opacity: 0;
  }
  50% {
    transform: translateY(20px) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
`;

const glow = keyframes`
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 215, 0, 0.8);
  }
`;

const sparkle = keyframes`
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
`;

const AchievementOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(5px);
`;

const AchievementCard = styled.div<{ rarity: Achievement['rarity'] }>`
  background: ${props => {
    switch (props.rarity) {
      case 'common': return 'linear-gradient(135deg, #4a90e2 0%, #357abd 100%)';
      case 'rare': return 'linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%)';
      case 'epic': return 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)';
      case 'legendary': return 'linear-gradient(135deg, #f1c40f 0%, #f39c12 100%)';
      default: return 'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)';
    }
  }};
  color: white;
  padding: 30px;
  border-radius: 20px;
  text-align: center;
  animation: ${slideInFromTop} 0.8s ease-out;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  position: relative;
  overflow: hidden;
  min-width: 400px;
  max-width: 500px;
  
  ${props => props.rarity === 'legendary' && `
    animation: ${slideInFromTop} 0.8s ease-out, ${glow} 2s ease-in-out infinite;
  `}
  
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: ${props => {
      switch (props.rarity) {
        case 'legendary': return 'radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%)';
        case 'epic': return 'radial-gradient(circle, rgba(231, 76, 60, 0.1) 0%, transparent 70%)';
        case 'rare': return 'radial-gradient(circle, rgba(155, 89, 182, 0.1) 0%, transparent 70%)';
        default: return 'none';
      }
    }};
    animation: ${sparkle} 3s ease-in-out infinite;
    pointer-events: none;
  }
`;

const AchievementHeader = styled.div`
  margin-bottom: 20px;
`;

const AchievementTitle = styled.h2`
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
`;

const AchievementSubtitle = styled.div`
  font-size: 16px;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: 500;
`;

const AchievementIcon = styled.div`
  font-size: 80px;
  margin: 20px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  animation: ${sparkle} 2s ease-in-out infinite;
`;

const AchievementName = styled.h3`
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: bold;
`;

const AchievementDescription = styled.p`
  margin: 0 0 20px 0;
  font-size: 16px;
  line-height: 1.5;
  opacity: 0.9;
`;

const RarityBadge = styled.div<{ rarity: Achievement['rarity'] }>`
  display: inline-block;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 15px;
  background: ${props => {
    switch (props.rarity) {
      case 'common': return 'rgba(255, 255, 255, 0.2)';
      case 'rare': return 'rgba(155, 89, 182, 0.3)';
      case 'epic': return 'rgba(231, 76, 60, 0.3)';
      case 'legendary': return 'rgba(255, 215, 0, 0.3)';
      default: return 'rgba(255, 255, 255, 0.1)';
    }
  }};
  border: 1px solid rgba(255, 255, 255, 0.3);
`;

const CloseButton = styled.button`
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 20px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }
`;

const ContinueButton = styled.button`
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.5);
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
  margin-top: 10px;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  }
`;

const Sparkles = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
`;

const Sparkle = styled.div<{ delay: number; size: number; left: number; top: number }>`
  position: absolute;
  width: ${props => props.size}px;
  height: ${props => props.size}px;
  background: white;
  border-radius: 50%;
  left: ${props => props.left}%;
  top: ${props => props.top}%;
  animation: ${sparkle} 2s ease-in-out infinite;
  animation-delay: ${props => props.delay}s;
  opacity: 0.8;
`;

const CategoryBadge = styled.div`
  position: absolute;
  top: 15px;
  left: 15px;
  background: rgba(0, 0, 0, 0.3);
  color: white;
  padding: 4px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
`;

export const AchievementUnlocked: React.FC<AchievementUnlockedProps> = ({
  achievement,
  onClose
}) => {
  const [showSparkles, setShowSparkles] = useState(true);

  useEffect(() => {
    // Автоматическое закрытие через 5 секунд
    const timer = setTimeout(() => {
      onClose();
    }, 5000);

    // Убираем блестки через 3 секунды
    const sparkleTimer = setTimeout(() => {
      setShowSparkles(false);
    }, 3000);

    return () => {
      clearTimeout(timer);
      clearTimeout(sparkleTimer);
    };
  }, [onClose]);

  const getRarityText = (rarity: Achievement['rarity']) => {
    switch (rarity) {
      case 'common': return 'Common Achievement';
      case 'rare': return 'Rare Achievement';
      case 'epic': return 'Epic Achievement';
      case 'legendary': return 'Legendary Achievement';
      default: return 'Achievement';
    }
  };

  const generateSparkles = () => {
    const sparkles = [];
    for (let i = 0; i < 15; i++) {
      sparkles.push(
        <Sparkle
          key={i}
          delay={Math.random() * 2}
          size={Math.random() * 6 + 2}
          left={Math.random() * 100}
          top={Math.random() * 100}
        />
      );
    }
    return sparkles;
  };

  return (
    <AchievementOverlay onClick={onClose}>
      <AchievementCard 
        rarity={achievement.rarity}
        onClick={(e) => e.stopPropagation()}
      >
        <CloseButton onClick={onClose}>×</CloseButton>
        
        <CategoryBadge>{achievement.category}</CategoryBadge>
        
        <AchievementHeader>
          <AchievementTitle>Achievement Unlocked!</AchievementTitle>
          <AchievementSubtitle>{getRarityText(achievement.rarity)}</AchievementSubtitle>
        </AchievementHeader>
        
        <RarityBadge rarity={achievement.rarity}>
          {achievement.rarity}
        </RarityBadge>
        
        <AchievementIcon>{achievement.icon}</AchievementIcon>
        
        <AchievementName>{achievement.name}</AchievementName>
        <AchievementDescription>{achievement.description}</AchievementDescription>
        
        <ContinueButton onClick={onClose}>
          Continue Playing
        </ContinueButton>
        
        {showSparkles && achievement.rarity !== 'common' && (
          <Sparkles>
            {generateSparkles()}
          </Sparkles>
        )}
      </AchievementCard>
    </AchievementOverlay>
  );
};
