import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { useSocket } from '../../hooks/useSocket';
import { useAuth } from '../../hooks/useAuth';

interface ChatMessage {
  id: string;
  sender: {
    id: string;
    username: string;
    displayName: string;
    rank: string;
    avatarUrl?: string;
  };
  content: string;
  timestamp: Date;
  type: 'message' | 'system' | 'achievement' | 'join' | 'leave';
  mentions?: string[];
  isEdited?: boolean;
}

interface ChatUser {
  id: string;
  username: string;
  displayName: string;
  rank: string;
  status: 'online' | 'away' | 'in_game';
  currentGame?: string;
}

const ChatContainer = styled.div`
  display: flex;
  height: 500px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
`;

const UsersList = styled.div`
  width: 200px;
  background: rgba(0, 0, 0, 0.2);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
`;

const UsersHeader = styled.div`
  padding: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: bold;
  background: rgba(74, 144, 226, 0.2);
`;

const UsersListContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
`;

const UserItem = styled.div<{ status: ChatUser['status'] }>`
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  margin-bottom: 4px;
  cursor: pointer;
  transition: all 0.2s;
  color: white;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const UserAvatar = styled.div<{ status: ChatUser['status'] }>`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  margin-right: 8px;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    right: -1px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: ${props => {
      switch (props.status) {
        case 'online': return '#4caf50';
        case 'in_game': return '#ff9800';
        case 'away': return '#ffc107';
        default: return '#666';
      }
    }};
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
`;

const UserInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const UserName = styled.div`
  font-size: 12px;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const UserRank = styled.div`
  font-size: 10px;
  opacity: 0.7;
`;

const ChatArea = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const ChatHeader = styled.div`
  padding: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: bold;
  background: rgba(74, 144, 226, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const ChatMessages = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
`;

const MessageItem = styled.div<{ type: ChatMessage['type']; isOwn: boolean }>`
  margin-bottom: 12px;
  padding: ${props => props.type === 'system' ? '8px 12px' : '0'};
  border-radius: ${props => props.type === 'system' ? '8px' : '0'};
  background: ${props => {
    if (props.type === 'system') return 'rgba(255, 193, 7, 0.1)';
    if (props.type === 'achievement') return 'rgba(76, 175, 80, 0.1)';
    if (props.type === 'join') return 'rgba(33, 150, 243, 0.1)';
    if (props.type === 'leave') return 'rgba(244, 67, 54, 0.1)';
    return 'transparent';
  }};
  text-align: ${props => props.type === 'system' ? 'center' : 'left'};
`;

const MessageHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  gap: 8px;
`;

const MessageAvatar = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: white;
`;

const MessageSender = styled.span`
  font-weight: bold;
  color: #4a90e2;
  cursor: pointer;
  
  &:hover {
    text-decoration: underline;
  }
`;

const MessageRank = styled.span`
  font-size: 11px;
  background: rgba(74, 144, 226, 0.2);
  color: #4a90e2;
  padding: 2px 6px;
  border-radius: 10px;
`;

const MessageTime = styled.span`
  font-size: 11px;
  opacity: 0.6;
  color: white;
`;

const MessageContent = styled.div<{ isOwn: boolean }>`
  color: white;
  line-height: 1.4;
  margin-left: 40px;
  word-wrap: break-word;
  
  ${props => props.isOwn && `
    background: rgba(74, 144, 226, 0.2);
    padding: 8px 12px;
    border-radius: 12px;
    margin-left: 0;
    margin-right: 40px;
  `}
`;

const ChatInput = styled.div`
  padding: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(0, 0, 0, 0.2);
`;

const InputContainer = styled.form`
  display: flex;
  gap: 10px;
`;

const MessageInput = styled.input`
  flex: 1;
  padding: 10px 15px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
  
  &:focus {
    outline: none;
    border-color: #4a90e2;
    background: rgba(255, 255, 255, 0.15);
  }
`;

const SendButton = styled.button`
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.2s;
  
  &:hover {
    background: linear-gradient(135deg, #357abd 0%, #2c5aa0 100%);
    transform: translateY(-1px);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const OnlineCount = styled.div`
  font-size: 12px;
  opacity: 0.7;
`;

export const GlobalChat: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [users, setUsers] = useState<ChatUser[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { socket } = useSocket();
  const { user } = useAuth();

  useEffect(() => {
    if (!socket || !user) return;

    // Присоединяемся к глобальному чату
    socket.emit('chat:join_global');

    socket.on('chat:connected', () => {
      setIsConnected(true);
    });

    socket.on('chat:message', (message: ChatMessage) => {
      setMessages(prev => [...prev, message]);
    });

    socket.on('chat:users_list', (usersList: ChatUser[]) => {
      setUsers(usersList);
    });

    socket.on('chat:user_joined', (userData: ChatUser) => {
      setUsers(prev => [...prev, userData]);
      setMessages(prev => [...prev, {
        id: Date.now().toString(),
        sender: userData,
        content: `${userData.displayName} joined the chat`,
        timestamp: new Date(),
        type: 'join'
      }]);
    });

    socket.on('chat:user_left', (userData: ChatUser) => {
      setUsers(prev => prev.filter(u => u.id !== userData.id));
      setMessages(prev => [...prev, {
        id: Date.now().toString(),
        sender: userData,
        content: `${userData.displayName} left the chat`,
        timestamp: new Date(),
        type: 'leave'
      }]);
    });

    socket.on('chat:user_status_changed', (data: { userId: string; status: ChatUser['status']; currentGame?: string }) => {
      setUsers(prev => prev.map(u => 
        u.id === data.userId 
          ? { ...u, status: data.status, currentGame: data.currentGame }
          : u
      ));
    });

    return () => {
      socket.off('chat:connected');
      socket.off('chat:message');
      socket.off('chat:users_list');
      socket.off('chat:user_joined');
      socket.off('chat:user_left');
      socket.off('chat:user_status_changed');
    };
  }, [socket, user]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!socket || !inputValue.trim()) return;

    socket.emit('chat:send_message', {
      content: inputValue.trim(),
      type: 'message'
    });

    setInputValue('');
  };

  const handleUserClick = (clickedUser: ChatUser) => {
    // Добавляем упоминание пользователя
    setInputValue(prev => `${prev}@${clickedUser.username} `);
  };

  const formatTime = (timestamp: Date) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getStatusText = (user: ChatUser) => {
    switch (user.status) {
      case 'online': return 'Online';
      case 'in_game': return `Playing ${user.currentGame}`;
      case 'away': return 'Away';
      default: return 'Offline';
    }
  };

  if (!user) {
    return (
      <ChatContainer>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center', 
          width: '100%', 
          color: 'white' 
        }}>
          Please log in to use the chat
        </div>
      </ChatContainer>
    );
  }

  return (
    <ChatContainer>
      <UsersList>
        <UsersHeader>
          Online Users ({users.length})
        </UsersHeader>
        <UsersListContainer>
          {users.map(chatUser => (
            <UserItem 
              key={chatUser.id} 
              status={chatUser.status}
              onClick={() => handleUserClick(chatUser)}
              title={getStatusText(chatUser)}
            >
              <UserAvatar status={chatUser.status}>
                {getInitials(chatUser.displayName)}
              </UserAvatar>
              <UserInfo>
                <UserName>{chatUser.displayName}</UserName>
                <UserRank>{chatUser.rank}</UserRank>
              </UserInfo>
            </UserItem>
          ))}
        </UsersListContainer>
      </UsersList>

      <ChatArea>
        <ChatHeader>
          <div>Global Chat</div>
          <OnlineCount>
            {isConnected ? `${users.length} online` : 'Connecting...'}
          </OnlineCount>
        </ChatHeader>

        <ChatMessages>
          {messages.map(message => (
            <MessageItem 
              key={message.id} 
              type={message.type}
              isOwn={message.sender.id === user.id}
            >
              {message.type === 'message' && (
                <>
                  <MessageHeader>
                    <MessageAvatar>
                      {getInitials(message.sender.displayName)}
                    </MessageAvatar>
                    <MessageSender onClick={() => handleUserClick(message.sender)}>
                      {message.sender.displayName}
                    </MessageSender>
                    <MessageRank>{message.sender.rank}</MessageRank>
                    <MessageTime>{formatTime(message.timestamp)}</MessageTime>
                  </MessageHeader>
                  <MessageContent isOwn={message.sender.id === user.id}>
                    {message.content}
                  </MessageContent>
                </>
              )}
              
              {message.type !== 'message' && (
                <div style={{ 
                  color: 'white', 
                  fontSize: '14px', 
                  fontStyle: 'italic',
                  opacity: 0.8 
                }}>
                  {message.content}
                </div>
              )}
            </MessageItem>
          ))}
          <div ref={messagesEndRef} />
        </ChatMessages>

        <ChatInput>
          <InputContainer onSubmit={handleSendMessage}>
            <MessageInput
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Type a message..."
              maxLength={500}
              disabled={!isConnected}
            />
            <SendButton 
              type="submit" 
              disabled={!inputValue.trim() || !isConnected}
            >
              Send
            </SendButton>
          </InputContainer>
        </ChatInput>
      </ChatArea>
    </ChatContainer>
  );
};
