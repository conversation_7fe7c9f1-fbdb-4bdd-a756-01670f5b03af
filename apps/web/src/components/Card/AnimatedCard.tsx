import React from 'react';
import styled, { keyframes } from 'styled-components';

interface AnimatedCardProps {
  rank: string;
  suit: string;
  isSelected?: boolean;
  isFlipped?: boolean;
  onClick?: () => void;
  animationType?: 'none' | 'flip' | 'slide' | 'bounce' | 'glow';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
}

const AnimatedCard: React.FC<AnimatedCardProps> = ({
  rank,
  suit,
  isSelected = false,
  isFlipped = false,
  onClick,
  animationType = 'none',
  size = 'medium',
  disabled = false,
}) => {
  const getSuitColor = (suit: string) => {
    switch (suit.toLowerCase()) {
      case 'hearts':
      case 'червы':
      case '♥':
        return '#e53e3e';
      case 'diamonds':
      case 'бубны':
      case '♦':
        return '#e53e3e';
      case 'clubs':
      case 'трефы':
      case '♣':
        return '#2d3748';
      case 'spades':
      case 'пики':
      case '♠':
        return '#2d3748';
      default:
        return '#2d3748';
    }
  };

  const getSuitSymbol = (suit: string) => {
    switch (suit.toLowerCase()) {
      case 'hearts':
      case 'червы':
        return '♥';
      case 'diamonds':
      case 'бубны':
        return '♦';
      case 'clubs':
      case 'трефы':
        return '♣';
      case 'spades':
      case 'пики':
        return '♠';
      default:
        return suit;
    }
  };

  const getRankDisplay = (rank: string) => {
    switch (rank.toLowerCase()) {
      case 'jack':
        return 'J';
      case 'queen':
        return 'Q';
      case 'king':
        return 'K';
      case 'ace':
        return 'A';
      default:
        return rank;
    }
  };

  return (
    <CardContainer
      $size={size}
      $isSelected={isSelected}
      $isFlipped={isFlipped}
      $animationType={animationType}
      onClick={!disabled ? onClick : undefined}
      $disabled={disabled}
      $suitColor={getSuitColor(suit)}
    >
      <CardInner $isFlipped={isFlipped}>
        <CardFace className="front">
          <CardFront $suitColor={getSuitColor(suit)}>
            <TopCorner>
              <RankText color={getSuitColor(suit)}>{getRankDisplay(rank)}</RankText>
              <SuitSymbol color={getSuitColor(suit)}>{getSuitSymbol(suit)}</SuitSymbol>
            </TopCorner>

            <CenterSuit color={getSuitColor(suit)}>
              {getSuitSymbol(suit)}
            </CenterSuit>

            <BottomCorner>
              <RankText color={getSuitColor(suit)}>{getRankDisplay(rank)}</RankText>
              <SuitSymbol color={getSuitColor(suit)}>{getSuitSymbol(suit)}</SuitSymbol>
            </BottomCorner>
          </CardFront>
        </CardFace>
        
        <CardFace className="back">
          <CardBack>
            <BackPattern>🂠</BackPattern>
          </CardBack>
        </CardFace>
      </CardInner>
    </CardContainer>
  );
};

// Анимации
const flipAnimation = keyframes`
  0% { transform: rotateY(0deg); }
  100% { transform: rotateY(180deg); }
`;

const slideAnimation = keyframes`
  0% { transform: translateX(-100px); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
`;

const bounceAnimation = keyframes`
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
  40%, 43% { transform: translate3d(0, -10px, 0); }
  70% { transform: translate3d(0, -5px, 0); }
  90% { transform: translate3d(0, -2px, 0); }
`;

const glowAnimation = keyframes`
  0%, 100% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
  50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 0 30px rgba(255, 215, 0, 0.6); }
`;

// Стилизованные компоненты
const CardContainer = styled.div<{
  $size: string;
  $isSelected: boolean;
  $isFlipped: boolean;
  $animationType: string;
  $disabled: boolean;
  $suitColor: string;
}>`
  perspective: 1000px;
  cursor: ${props => props.$disabled ? 'not-allowed' : 'pointer'};
  opacity: ${props => props.$disabled ? 0.6 : 1};

  ${props => {
    switch (props.$size) {
      case 'small':
        return 'width: 60px; height: 84px;';
      case 'large':
        return 'width: 100px; height: 140px;';
      default:
        return 'width: 80px; height: 112px;';
    }
  }}

  ${props => {
    switch (props.$animationType) {
      case 'flip':
        return `animation: ${flipAnimation} 0.6s ease-in-out;`;
      case 'slide':
        return `animation: ${slideAnimation} 0.5s ease-out;`;
      case 'bounce':
        return `animation: ${bounceAnimation} 1s ease-in-out;`;
      case 'glow':
        return `animation: ${glowAnimation} 2s ease-in-out infinite;`;
      default:
        return '';
    }
  }}

  &:hover {
    ${props => !props.$disabled && `
      transform: translateY(-4px);
      transition: transform 0.3s ease;
    `}
  }
`;

const CardInner = styled.div<{ $isFlipped: boolean }>`
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
  transform: ${props => props.$isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)'};
`;

const CardFace = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

  &.back {
    transform: rotateY(180deg);
  }
`;

const CardFront = styled.div<{ $suitColor: string }>`
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FFFFFF, #F8F9FA);
  border: 2px solid #E2E8F0;
  border-radius: 12px;
  position: relative;

  &:hover {
    border-color: ${props => props.$suitColor};
  }
`;

const CardBack = styled.div`
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  border: 2px solid #654321;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.1) 10px,
      transparent 10px,
      transparent 20px
    );
  }
`;

const BackPattern = styled.div`
  font-size: 2rem;
  color: #654321;
  z-index: 1;
`;

const TopCorner = styled.div`
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 0.8rem;
  font-weight: bold;
`;

const BottomCorner = styled.div`
  position: absolute;
  bottom: 8px;
  right: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 0.8rem;
  font-weight: bold;
  transform: rotate(180deg);
`;

const RankText = styled.div<{ color?: string }>`
  color: ${props => props.color || '#2d3748'};
  line-height: 1;
  font-weight: bold;
`;

const SuitSymbol = styled.div<{ color?: string }>`
  color: ${props => props.color || '#2d3748'};
  line-height: 1;
  margin-top: 2px;
`;

const CenterSuit = styled.div<{ color?: string }>`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
  color: ${props => props.color || '#2d3748'};
  font-weight: bold;
`;

export default AnimatedCard;
