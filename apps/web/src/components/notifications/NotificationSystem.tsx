import React, { useState, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';
import { useSocket } from '../../hooks/useSocket';
import { useAuth } from '../../hooks/useAuth';

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info' | 'achievement' | 'friend_request' | 'game_invite';
  title: string;
  message: string;
  timestamp: Date;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
    variant?: 'primary' | 'secondary' | 'danger';
  }>;
  data?: any;
}

const slideIn = keyframes`
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
`;

const slideOut = keyframes`
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
`;

const NotificationContainer = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 400px;
`;

const NotificationCard = styled.div<{ type: Notification['type']; isExiting: boolean }>`
  background: ${props => {
    switch (props.type) {
      case 'success': return 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)';
      case 'error': return 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)';
      case 'warning': return 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)';
      case 'info': return 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)';
      case 'achievement': return 'linear-gradient(135deg, #ffd700 0%, #ffb300 100%)';
      case 'friend_request': return 'linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%)';
      case 'game_invite': return 'linear-gradient(135deg, #00bcd4 0%, #0097a7 100%)';
      default: return 'linear-gradient(135deg, #666 0%, #444 100%)';
    }
  }};
  color: white;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  animation: ${props => props.isExiting ? slideOut : slideIn} 0.3s ease-out;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const NotificationHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
`;

const NotificationTitle = styled.h4`
  margin: 0;
  font-size: 16px;
  font-weight: bold;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
  
  &:hover {
    opacity: 1;
  }
`;

const NotificationMessage = styled.p`
  margin: 0 0 10px 0;
  font-size: 14px;
  line-height: 1.4;
`;

const NotificationTimestamp = styled.div`
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 10px;
`;

const NotificationActions = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 10px;
`;

const ActionButton = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  background: ${props => {
    switch (props.variant) {
      case 'primary': return 'rgba(255, 255, 255, 0.2)';
      case 'secondary': return 'rgba(255, 255, 255, 0.1)';
      case 'danger': return 'rgba(244, 67, 54, 0.3)';
      default: return 'rgba(255, 255, 255, 0.15)';
    }
  }};
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 6px 12px;
  border-radius: 5px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
  }
`;

const NotificationIcon = styled.div<{ type: Notification['type'] }>`
  font-size: 20px;
  margin-right: 10px;
  
  &::before {
    content: ${props => {
      switch (props.type) {
        case 'success': return '"✅"';
        case 'error': return '"❌"';
        case 'warning': return '"⚠️"';
        case 'info': return '"ℹ️"';
        case 'achievement': return '"🏆"';
        case 'friend_request': return '"👥"';
        case 'game_invite': return '"🎮"';
        default: return '"📢"';
      }
    }};
  }
`;

export const NotificationSystem: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [exitingNotifications, setExitingNotifications] = useState<Set<string>>(new Set());
  const { socket } = useSocket();
  const { user } = useAuth();

  useEffect(() => {
    if (!socket || !user) return;

    // Обработчики уведомлений
    socket.on('notification', (notification: Omit<Notification, 'id' | 'timestamp'>) => {
      addNotification(notification);
    });

    socket.on('achievement_unlocked', (data: { achievement: any }) => {
      addNotification({
        type: 'achievement',
        title: 'Achievement Unlocked!',
        message: `You've earned "${data.achievement.name}": ${data.achievement.description}`,
        duration: 8000
      });
    });

    socket.on('friend_request_received', (data: { from: { name: string; id: string } }) => {
      addNotification({
        type: 'friend_request',
        title: 'Friend Request',
        message: `${data.from.name} wants to be your friend`,
        actions: [
          {
            label: 'Accept',
            action: () => socket.emit('accept_friend_request', { fromUserId: data.from.id }),
            variant: 'primary'
          },
          {
            label: 'Decline',
            action: () => socket.emit('decline_friend_request', { fromUserId: data.from.id }),
            variant: 'secondary'
          }
        ]
      });
    });

    socket.on('game_invitation', (data: { from: { name: string; id: string }; gameType: string; roomId: string }) => {
      addNotification({
        type: 'game_invite',
        title: 'Game Invitation',
        message: `${data.from.name} invited you to play ${data.gameType}`,
        actions: [
          {
            label: 'Join',
            action: () => {
              // Переход к игре
              window.location.href = `/games/${data.gameType}/room/${data.roomId}`;
            },
            variant: 'primary'
          },
          {
            label: 'Decline',
            action: () => socket.emit('decline_game_invitation', { fromUserId: data.from.id, roomId: data.roomId }),
            variant: 'secondary'
          }
        ]
      });
    });

    socket.on('rating_updated', (data: { oldRating: number; newRating: number; change: number }) => {
      const isIncrease = data.change > 0;
      addNotification({
        type: isIncrease ? 'success' : 'warning',
        title: 'Rating Updated',
        message: `Your rating ${isIncrease ? 'increased' : 'decreased'} by ${Math.abs(data.change)} points (${data.oldRating} → ${data.newRating})`,
        duration: 5000
      });
    });

    socket.on('tournament_starting', (data: { tournament: { name: string; id: string } }) => {
      addNotification({
        type: 'info',
        title: 'Tournament Starting',
        message: `Tournament "${data.tournament.name}" is about to begin!`,
        actions: [
          {
            label: 'Join',
            action: () => {
              window.location.href = `/tournaments/${data.tournament.id}`;
            },
            variant: 'primary'
          }
        ]
      });
    });

    return () => {
      socket.off('notification');
      socket.off('achievement_unlocked');
      socket.off('friend_request_received');
      socket.off('game_invitation');
      socket.off('rating_updated');
      socket.off('tournament_starting');
    };
  }, [socket, user]);

  const addNotification = (notificationData: Omit<Notification, 'id' | 'timestamp'>) => {
    const notification: Notification = {
      ...notificationData,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
      duration: notificationData.duration || 5000
    };

    setNotifications(prev => [notification, ...prev]);

    // Автоматическое удаление
    if (notification.duration > 0) {
      setTimeout(() => {
        removeNotification(notification.id);
      }, notification.duration);
    }
  };

  const removeNotification = (id: string) => {
    setExitingNotifications(prev => new Set(prev).add(id));
    
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== id));
      setExitingNotifications(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }, 300);
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    
    if (diff < 60000) {
      return 'Just now';
    } else if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}m ago`;
    } else {
      return timestamp.toLocaleTimeString();
    }
  };

  return (
    <NotificationContainer>
      {notifications.map(notification => (
        <NotificationCard
          key={notification.id}
          type={notification.type}
          isExiting={exitingNotifications.has(notification.id)}
        >
          <NotificationHeader>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <NotificationIcon type={notification.type} />
              <NotificationTitle>{notification.title}</NotificationTitle>
            </div>
            <CloseButton onClick={() => removeNotification(notification.id)}>
              ×
            </CloseButton>
          </NotificationHeader>
          
          <NotificationMessage>{notification.message}</NotificationMessage>
          
          <NotificationTimestamp>
            {formatTimestamp(notification.timestamp)}
          </NotificationTimestamp>
          
          {notification.actions && notification.actions.length > 0 && (
            <NotificationActions>
              {notification.actions.map((action, index) => (
                <ActionButton
                  key={index}
                  variant={action.variant}
                  onClick={() => {
                    action.action();
                    removeNotification(notification.id);
                  }}
                >
                  {action.label}
                </ActionButton>
              ))}
            </NotificationActions>
          )}
        </NotificationCard>
      ))}
    </NotificationContainer>
  );
};

// Хук для использования системы уведомлений
export const useNotifications = () => {
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    // Отправляем событие для добавления уведомления
    window.dispatchEvent(new CustomEvent('addNotification', { detail: notification }));
  };

  const showSuccess = (title: string, message: string, duration?: number) => {
    addNotification({ type: 'success', title, message, duration });
  };

  const showError = (title: string, message: string, duration?: number) => {
    addNotification({ type: 'error', title, message, duration });
  };

  const showWarning = (title: string, message: string, duration?: number) => {
    addNotification({ type: 'warning', title, message, duration });
  };

  const showInfo = (title: string, message: string, duration?: number) => {
    addNotification({ type: 'info', title, message, duration });
  };

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    addNotification
  };
};
