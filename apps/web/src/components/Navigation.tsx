import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';
import { useRouter } from 'next/router';

interface NavigationProps {
  currentSection?: number;
}

const navigationItems = [
  { id: 'home', label: 'Главная', icon: '🏠', href: '/' },
  { id: 'games', label: 'Игры', icon: '🎮', href: '/games' },
  { id: 'metaverse', label: 'Метавселенная', icon: '🌍', href: '/metaverse' },
  { id: 'tournaments', label: 'Турниры', icon: '🏆', href: '/tournaments' },
  { id: 'streaming', label: 'Стриминг', icon: '📺', href: '/streaming' },
  { id: 'nft', label: 'NFT', icon: '💎', href: '/nft' },
  { id: 'profile', label: 'Профиль', icon: '👤', href: '/profile' }
];

export const Navigation: React.FC<NavigationProps> = ({ currentSection = 0 }) => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [activeItem, setActiveItem] = useState('home');

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    // Определяем активный элемент на основе текущего пути
    const currentPath = router.pathname;
    const activeNav = navigationItems.find(item => item.href === currentPath);
    if (activeNav) {
      setActiveItem(activeNav.id);
    }
  }, [router.pathname]);

  const handleNavigation = (href: string, id: string) => {
    setActiveItem(id);
    router.push(href);
    setIsOpen(false);
  };

  return (
    <>
      {/* Основная навигация */}
      <NavContainer
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.8 }}
        scrolled={scrolled}
      >
        <NavContent>
          {/* Логотип */}
          <Logo onClick={() => handleNavigation('/', 'home')}>
            <LogoIcon>🎮</LogoIcon>
            <LogoText>
              <LogoTitle>Козырь Мастер</LogoTitle>
              <LogoVersion>4.0</LogoVersion>
            </LogoText>
          </Logo>

          {/* Навигационные элементы для десктопа */}
          <NavItems>
            {navigationItems.map((item) => (
              <NavItem
                key={item.id}
                active={activeItem === item.id}
                onClick={() => handleNavigation(item.href, item.id)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <NavIcon>{item.icon}</NavIcon>
                <NavLabel>{item.label}</NavLabel>
                {activeItem === item.id && (
                  <ActiveIndicator
                    layoutId="activeIndicator"
                    initial={false}
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                  />
                )}
              </NavItem>
            ))}
          </NavItems>

          {/* Кнопки действий */}
          <NavActions>
            <ActionButton
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => router.push('/wallet')}
            >
              <ActionIcon>💰</ActionIcon>
              <span>Кошелёк</span>
            </ActionButton>
            
            <ActionButton
              primary
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => router.push('/play')}
            >
              <ActionIcon>🚀</ActionIcon>
              <span>Играть</span>
            </ActionButton>
          </NavActions>

          {/* Мобильное меню */}
          <MobileMenuButton
            onClick={() => setIsOpen(!isOpen)}
            whileTap={{ scale: 0.95 }}
          >
            <MenuIcon isOpen={isOpen}>
              <span />
              <span />
              <span />
            </MenuIcon>
          </MobileMenuButton>
        </NavContent>
      </NavContainer>

      {/* Мобильное меню */}
      <AnimatePresence>
        {isOpen && (
          <MobileMenu
            initial={{ opacity: 0, x: '100%' }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: '100%' }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          >
            <MobileMenuContent>
              <MobileMenuHeader>
                <Logo onClick={() => handleNavigation('/', 'home')}>
                  <LogoIcon>🎮</LogoIcon>
                  <LogoText>
                    <LogoTitle>Козырь Мастер</LogoTitle>
                    <LogoVersion>4.0</LogoVersion>
                  </LogoText>
                </Logo>
                
                <CloseButton
                  onClick={() => setIsOpen(false)}
                  whileTap={{ scale: 0.95 }}
                >
                  ✕
                </CloseButton>
              </MobileMenuHeader>

              <MobileNavItems>
                {navigationItems.map((item, index) => (
                  <MobileNavItem
                    key={item.id}
                    active={activeItem === item.id}
                    onClick={() => handleNavigation(item.href, item.id)}
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <MobileNavIcon>{item.icon}</MobileNavIcon>
                    <MobileNavLabel>{item.label}</MobileNavLabel>
                  </MobileNavItem>
                ))}
              </MobileNavItems>

              <MobileActions>
                <MobileActionButton
                  onClick={() => {
                    router.push('/wallet');
                    setIsOpen(false);
                  }}
                  whileTap={{ scale: 0.95 }}
                >
                  💰 Кошелёк
                </MobileActionButton>
                
                <MobileActionButton
                  primary
                  onClick={() => {
                    router.push('/play');
                    setIsOpen(false);
                  }}
                  whileTap={{ scale: 0.95 }}
                >
                  🚀 Играть сейчас
                </MobileActionButton>
              </MobileActions>
            </MobileMenuContent>
          </MobileMenu>
        )}
      </AnimatePresence>

      {/* Overlay для мобильного меню */}
      <AnimatePresence>
        {isOpen && (
          <MobileOverlay
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsOpen(false)}
          />
        )}
      </AnimatePresence>
    </>
  );
};

// Стилизованные компоненты
const NavContainer = styled(motion.nav)<{ scrolled: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: ${props => props.scrolled 
    ? 'rgba(0, 0, 0, 0.9)' 
    : 'rgba(0, 0, 0, 0.1)'};
  backdrop-filter: blur(20px);
  border-bottom: 1px solid ${props => props.scrolled 
    ? 'rgba(255, 255, 255, 0.1)' 
    : 'transparent'};
  transition: all 0.3s ease;
`;

const NavContent = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
`;

const LogoIcon = styled.div`
  font-size: 2rem;
  filter: drop-shadow(0 0 10px rgba(74, 144, 226, 0.5));
`;

const LogoText = styled.div`
  display: flex;
  flex-direction: column;
  
  @media (max-width: 480px) {
    display: none;
  }
`;

const LogoTitle = styled.div`
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  line-height: 1;
`;

const LogoVersion = styled.div`
  font-size: 0.75rem;
  color: #4a90e2;
  font-weight: 600;
`;

const NavItems = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  @media (max-width: 1024px) {
    display: none;
  }
`;

const NavItem = styled(motion.button)<{ active: boolean }>`
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: none;
  background: ${props => props.active ? 'rgba(74, 144, 226, 0.2)' : 'transparent'};
  color: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.8)'};
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  
  &:hover {
    background: rgba(74, 144, 226, 0.1);
    color: #4a90e2;
  }
`;

const NavIcon = styled.span`
  font-size: 1.1rem;
`;

const NavLabel = styled.span`
  font-size: 0.9rem;
`;

const ActiveIndicator = styled(motion.div)`
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 2px;
  background: linear-gradient(90deg, #4a90e2, #7b68ee);
  border-radius: 1px;
`;

const NavActions = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  
  @media (max-width: 1024px) {
    display: none;
  }
`;

const ActionButton = styled(motion.button)<{ primary?: boolean }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: ${props => props.primary ? 'none' : '2px solid rgba(255, 255, 255, 0.2)'};
  background: ${props => props.primary 
    ? 'linear-gradient(135deg, #4a90e2, #7b68ee)' 
    : 'transparent'};
  color: white;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  
  &:hover {
    background: ${props => props.primary 
      ? 'linear-gradient(135deg, #5ba0f2, #8b78fe)' 
      : 'rgba(255, 255, 255, 0.1)'};
    transform: translateY(-2px);
  }
`;

const ActionIcon = styled.span`
  font-size: 1rem;
`;

const MobileMenuButton = styled(motion.button)`
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  
  @media (max-width: 1024px) {
    display: block;
  }
`;

const MenuIcon = styled.div<{ isOpen: boolean }>`
  width: 24px;
  height: 18px;
  position: relative;
  
  span {
    display: block;
    position: absolute;
    height: 2px;
    width: 100%;
    background: white;
    border-radius: 1px;
    opacity: 1;
    left: 0;
    transform-origin: left center;
    transition: all 0.3s ease;
    
    &:nth-child(1) {
      top: 0;
      transform: ${props => props.isOpen ? 'rotate(45deg)' : 'rotate(0)'};
    }
    
    &:nth-child(2) {
      top: 8px;
      opacity: ${props => props.isOpen ? 0 : 1};
    }
    
    &:nth-child(3) {
      top: 16px;
      transform: ${props => props.isOpen ? 'rotate(-45deg)' : 'rotate(0)'};
    }
  }
`;

const MobileMenu = styled(motion.div)`
  position: fixed;
  top: 0;
  right: 0;
  width: 300px;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(20px);
  z-index: 200;
  
  @media (max-width: 480px) {
    width: 100vw;
  }
`;

const MobileMenuContent = styled.div`
  padding: 2rem;
  height: 100%;
  display: flex;
  flex-direction: column;
`;

const MobileMenuHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
`;

const CloseButton = styled(motion.button)`
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
`;

const MobileNavItems = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const MobileNavItem = styled(motion.button)<{ active: boolean }>`
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: none;
  background: ${props => props.active ? 'rgba(74, 144, 226, 0.2)' : 'transparent'};
  color: ${props => props.active ? '#4a90e2' : 'white'};
  border-radius: 10px;
  cursor: pointer;
  text-align: left;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(74, 144, 226, 0.1);
  }
`;

const MobileNavIcon = styled.span`
  font-size: 1.5rem;
`;

const MobileNavLabel = styled.span`
  font-size: 1.1rem;
  font-weight: 500;
`;

const MobileActions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
`;

const MobileActionButton = styled(motion.button)<{ primary?: boolean }>`
  padding: 1rem;
  border: ${props => props.primary ? 'none' : '2px solid rgba(255, 255, 255, 0.2)'};
  background: ${props => props.primary 
    ? 'linear-gradient(135deg, #4a90e2, #7b68ee)' 
    : 'transparent'};
  color: white;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
`;

const MobileOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 150;
`;

export default Navigation;
