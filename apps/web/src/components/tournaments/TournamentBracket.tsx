import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

interface TournamentMatch {
  id: string;
  roundNumber: number;
  matchNumber: number;
  player1?: {
    id: string;
    name: string;
    seed: number;
  };
  player2?: {
    id: string;
    name: string;
    seed: number;
  };
  winner?: {
    id: string;
    name: string;
  };
  status: 'pending' | 'active' | 'finished' | 'walkover';
  scheduledTime?: Date;
  score?: {
    player1: number;
    player2: number;
  };
}

interface Tournament {
  id: string;
  name: string;
  type: 'single_elimination' | 'double_elimination' | 'round_robin';
  status: 'upcoming' | 'registration' | 'active' | 'finished';
  maxParticipants: number;
  currentParticipants: number;
  entryFee: number;
  prizePool: number;
  startTime: Date;
  matches: TournamentMatch[];
  participants: Array<{
    id: string;
    name: string;
    seed: number;
    isEliminated: boolean;
  }>;
}

interface TournamentBracketProps {
  tournament: Tournament;
  onMatchClick?: (match: TournamentMatch) => void;
}

const BracketContainer = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  overflow-x: auto;
`;

const BracketGrid = styled.div<{ rounds: number }>`
  display: grid;
  grid-template-columns: repeat(${props => props.rounds}, 1fr);
  gap: 40px;
  min-width: ${props => props.rounds * 250}px;
  padding: 20px;
`;

const RoundColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const RoundHeader = styled.div`
  text-align: center;
  font-weight: bold;
  font-size: 16px;
  color: #4a90e2;
  margin-bottom: 20px;
  padding: 10px;
  background: rgba(74, 144, 226, 0.1);
  border-radius: 8px;
`;

const MatchCard = styled.div<{ status: TournamentMatch['status'] }>`
  background: ${props => {
    switch (props.status) {
      case 'active': return 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)';
      case 'finished': return 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)';
      case 'pending': return 'rgba(255, 255, 255, 0.1)';
      case 'walkover': return 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)';
      default: return 'rgba(255, 255, 255, 0.05)';
    }
  }};
  border: 1px solid ${props => {
    switch (props.status) {
      case 'active': return '#4caf50';
      case 'finished': return '#2196f3';
      case 'pending': return 'rgba(255, 255, 255, 0.3)';
      case 'walkover': return '#ff9800';
      default: return 'rgba(255, 255, 255, 0.2)';
    }
  }};
  border-radius: 10px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  min-height: 120px;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  }
`;

const MatchHeader = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 12px;
  opacity: 0.8;
`;

const MatchNumber = styled.span`
  font-weight: bold;
`;

const MatchStatus = styled.span<{ status: TournamentMatch['status'] }>`
  background: ${props => {
    switch (props.status) {
      case 'active': return 'rgba(255, 255, 255, 0.2)';
      case 'finished': return 'rgba(255, 255, 255, 0.2)';
      case 'pending': return 'rgba(255, 193, 7, 0.3)';
      case 'walkover': return 'rgba(255, 255, 255, 0.2)';
      default: return 'rgba(255, 255, 255, 0.1)';
    }
  }};
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  text-transform: uppercase;
`;

const PlayerSlot = styled.div<{ isWinner?: boolean; isEmpty?: boolean }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  margin-bottom: 5px;
  background: ${props => {
    if (props.isEmpty) return 'rgba(255, 255, 255, 0.05)';
    if (props.isWinner) return 'rgba(255, 215, 0, 0.2)';
    return 'rgba(255, 255, 255, 0.1)';
  }};
  border: 1px solid ${props => {
    if (props.isEmpty) return 'rgba(255, 255, 255, 0.1)';
    if (props.isWinner) return '#ffd700';
    return 'rgba(255, 255, 255, 0.2)';
  }};
  border-radius: 6px;
  font-size: 14px;
  
  ${props => props.isWinner && `
    font-weight: bold;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
  `}
`;

const PlayerName = styled.span`
  flex: 1;
  text-align: left;
`;

const PlayerSeed = styled.span`
  font-size: 12px;
  opacity: 0.7;
  margin-right: 8px;
`;

const PlayerScore = styled.span`
  font-weight: bold;
  min-width: 20px;
  text-align: center;
`;

const MatchTime = styled.div`
  font-size: 11px;
  opacity: 0.6;
  text-align: center;
  margin-top: 5px;
`;

const ConnectorLine = styled.div`
  position: absolute;
  right: -20px;
  top: 50%;
  width: 40px;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  
  &::after {
    content: '';
    position: absolute;
    right: -1px;
    top: -3px;
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
  }
`;

const TournamentInfo = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const InfoCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const InfoTitle = styled.h4`
  margin: 0 0 10px 0;
  color: #4a90e2;
  font-size: 14px;
`;

const InfoValue = styled.div`
  font-size: 18px;
  font-weight: bold;
`;

export const TournamentBracket: React.FC<TournamentBracketProps> = ({
  tournament,
  onMatchClick
}) => {
  const [selectedMatch, setSelectedMatch] = useState<TournamentMatch | null>(null);

  const getRoundName = (roundNumber: number, totalRounds: number) => {
    const roundsFromEnd = totalRounds - roundNumber;
    switch (roundsFromEnd) {
      case 0: return 'Final';
      case 1: return 'Semi-Final';
      case 2: return 'Quarter-Final';
      case 3: return 'Round of 16';
      case 4: return 'Round of 32';
      default: return `Round ${roundNumber}`;
    }
  };

  const getMatchesByRound = () => {
    const rounds: { [key: number]: TournamentMatch[] } = {};
    
    tournament.matches.forEach(match => {
      if (!rounds[match.roundNumber]) {
        rounds[match.roundNumber] = [];
      }
      rounds[match.roundNumber].push(match);
    });

    // Сортируем матчи в каждом раунде
    Object.keys(rounds).forEach(roundNum => {
      rounds[parseInt(roundNum)].sort((a, b) => a.matchNumber - b.matchNumber);
    });

    return rounds;
  };

  const handleMatchClick = (match: TournamentMatch) => {
    setSelectedMatch(match);
    onMatchClick?.(match);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatPrize = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const roundsData = getMatchesByRound();
  const totalRounds = Math.max(...Object.keys(roundsData).map(Number));

  return (
    <BracketContainer>
      <TournamentInfo>
        <InfoCard>
          <InfoTitle>Tournament</InfoTitle>
          <InfoValue>{tournament.name}</InfoValue>
        </InfoCard>
        
        <InfoCard>
          <InfoTitle>Participants</InfoTitle>
          <InfoValue>{tournament.currentParticipants}/{tournament.maxParticipants}</InfoValue>
        </InfoCard>
        
        <InfoCard>
          <InfoTitle>Prize Pool</InfoTitle>
          <InfoValue>{formatPrize(tournament.prizePool)}</InfoValue>
        </InfoCard>
        
        <InfoCard>
          <InfoTitle>Status</InfoTitle>
          <InfoValue style={{ textTransform: 'capitalize' }}>{tournament.status}</InfoValue>
        </InfoCard>
      </TournamentInfo>

      <BracketGrid rounds={totalRounds}>
        {Object.entries(roundsData)
          .sort(([a], [b]) => parseInt(a) - parseInt(b))
          .map(([roundNum, matches]) => (
            <RoundColumn key={roundNum}>
              <RoundHeader>
                {getRoundName(parseInt(roundNum), totalRounds)}
              </RoundHeader>
              
              {matches.map(match => (
                <MatchCard
                  key={match.id}
                  status={match.status}
                  onClick={() => handleMatchClick(match)}
                >
                  <MatchHeader>
                    <MatchNumber>Match {match.matchNumber}</MatchNumber>
                    <MatchStatus status={match.status}>{match.status}</MatchStatus>
                  </MatchHeader>

                  <PlayerSlot 
                    isWinner={match.winner?.id === match.player1?.id}
                    isEmpty={!match.player1}
                  >
                    {match.player1 ? (
                      <>
                        <PlayerSeed>#{match.player1.seed}</PlayerSeed>
                        <PlayerName>{match.player1.name}</PlayerName>
                        <PlayerScore>{match.score?.player1 || '-'}</PlayerScore>
                      </>
                    ) : (
                      <PlayerName style={{ opacity: 0.5 }}>TBD</PlayerName>
                    )}
                  </PlayerSlot>

                  <PlayerSlot 
                    isWinner={match.winner?.id === match.player2?.id}
                    isEmpty={!match.player2}
                  >
                    {match.player2 ? (
                      <>
                        <PlayerSeed>#{match.player2.seed}</PlayerSeed>
                        <PlayerName>{match.player2.name}</PlayerName>
                        <PlayerScore>{match.score?.player2 || '-'}</PlayerScore>
                      </>
                    ) : (
                      <PlayerName style={{ opacity: 0.5 }}>TBD</PlayerName>
                    )}
                  </PlayerSlot>

                  {match.scheduledTime && (
                    <MatchTime>
                      {formatTime(match.scheduledTime)}
                    </MatchTime>
                  )}

                  {parseInt(roundNum) < totalRounds && <ConnectorLine />}
                </MatchCard>
              ))}
            </RoundColumn>
          ))}
      </BracketGrid>
    </BracketContainer>
  );
};
