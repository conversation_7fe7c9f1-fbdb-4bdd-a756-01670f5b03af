import { Card } from './Card';

export interface PokerPlayer {
  id: string;
  name: string;
  chips: number;
  holeCards: Card[];
  currentBet: number;
  totalBet: number;
  isActive: boolean;
  isFolded: boolean;
  isAllIn: boolean;
  position: number;
  lastAction?: PokerAction;
}

export interface PokerAction {
  type: 'fold' | 'check' | 'call' | 'bet' | 'raise' | 'all_in';
  amount?: number;
  timestamp: Date;
}

export interface PokerHand {
  cards: Card[];
  rank: HandRank;
  value: number;
  description: string;
}

export enum HandRank {
  HIGH_CARD = 1,
  PAIR = 2,
  TWO_PAIR = 3,
  THREE_OF_A_KIND = 4,
  STRAIGHT = 5,
  FLUSH = 6,
  FULL_HOUSE = 7,
  FOUR_OF_A_KIND = 8,
  STRAIGHT_FLUSH = 9,
  ROYAL_FLUSH = 10
}

export interface PokerGameState {
  id: string;
  players: PokerPlayer[];
  deck: Card[];
  communityCards: Card[];
  pot: number;
  sidePots: Array<{ amount: number; eligiblePlayers: string[] }>;
  currentPlayerIndex: number;
  dealerIndex: number;
  smallBlindIndex: number;
  bigBlindIndex: number;
  phase: 'preflop' | 'flop' | 'turn' | 'river' | 'showdown' | 'finished';
  currentBet: number;
  minRaise: number;
  smallBlind: number;
  bigBlind: number;
  actionHistory: Array<{ playerId: string; action: PokerAction }>;
  winners: Array<{ playerId: string; hand: PokerHand; amount: number }>;
  handNumber: number;
}

export interface PokerRoom {
  id: string;
  name: string;
  maxPlayers: number;
  currentPlayers: number;
  smallBlind: number;
  bigBlind: number;
  gameState?: PokerGameState;
  isPrivate: boolean;
  password?: string;
  status: 'waiting' | 'playing' | 'finished';
  createdAt: Date;
  host: {
    id: string;
    name: string;
  };
}

export interface PokerRoomSettings {
  name: string;
  maxPlayers: number;
  smallBlind: number;
  bigBlind: number;
  startingChips: number;
  isPrivate: boolean;
  password?: string;
}

export interface PokerStats {
  handsPlayed: number;
  handsWon: number;
  winRate: number;
  totalWinnings: number;
  biggestPot: number;
  bestHand: PokerHand | null;
  favoritePosition: number;
  averageVPIP: number; // Voluntarily Put money In Pot
  averagePFR: number;  // Pre-Flop Raise
  averageAggression: number;
}
