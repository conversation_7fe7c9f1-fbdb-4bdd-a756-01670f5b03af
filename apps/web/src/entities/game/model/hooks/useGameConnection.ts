/**
 * Хук для управления подключением к игровому серверу
 */

import { useState, useCallback, useEffect } from 'react';
import { NetworkError } from '../multiplayerService';

// Типы для состояния подключения
export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'error';

export interface Room {
  id: string;
  name: string;
  host: string;
  players: Array<{
    id: string;
    name: string;
    isReady: boolean;
  }>;
  playerId: string;
  maxPlayers: number;
  status: 'waiting' | 'playing' | 'finished';
}

export interface UseGameConnectionOptions {
  serverUrl: string;
  autoConnect?: boolean;
}

/**
 * Хук для управления подключением к игровому серверу
 * @param options Настройки подключения
 */
export function useGameConnection(options: UseGameConnectionOptions) {
  const { serverUrl, autoConnect = true } = options;
  
  // Состояние подключения
  const [status, setStatus] = useState<ConnectionStatus>('disconnected');
  const [error, setError] = useState<Error | null>(null);
  
  // Состояние комнаты
  const [currentRoom, setCurrentRoom] = useState<Room | null>(null);
  const [gameState, setGameState] = useState<any>(null);
  
  // Состояние сообщений
  const [messages, setMessages] = useState<Array<{
    playerId: string;
    playerName: string;
    message: string;
    timestamp: number;
  }>>([]);
  
  // Имитация подключения к серверу
  const connect = useCallback(async (playerId: string, sessionToken: string) => {
    setStatus('connecting');
    setError(null);
    
    try {
      // Имитация задержки сети
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Имитация успешного подключения
      setStatus('connected');
      
      return true;
    } catch (error) {
      setStatus('error');
      setError(error instanceof Error ? error : new NetworkError('Ошибка подключения к серверу'));
      throw error;
    }
  }, []);
  
  // Имитация отключения от сервера
  const disconnect = useCallback(async () => {
    try {
      // Имитация задержки сети
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Сбрасываем состояние
      setCurrentRoom(null);
      setGameState(null);
      setStatus('disconnected');
      
      return true;
    } catch (error) {
      setError(error instanceof Error ? error : new NetworkError('Ошибка при отключении от сервера'));
      throw error;
    }
  }, []);
  
  // Имитация присоединения к комнате
  const joinRoom = useCallback(async (roomId: string, playerName: string) => {
    if (status !== 'connected') {
      throw new NetworkError('Нет подключения к серверу');
    }
    
    try {
      // Имитация задержки сети
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Имитация проверки существования комнаты
      if (Math.random() > 0.95) {
        throw new NetworkError('Комната не найдена', 'ROOM_NOT_FOUND');
      }
      
      // Имитация успешного присоединения к комнате
      const playerId = `player-${Date.now()}`;
      
      const room: Room = {
        id: roomId,
        name: `Комната #${roomId}`,
        host: playerId, // В реальном приложении хост будет определяться сервером
        players: [
          {
            id: playerId,
            name: playerName,
            isReady: false,
          },
        ],
        playerId,
        maxPlayers: 4,
        status: 'waiting',
      };
      
      setCurrentRoom(room);
      
      // Добавляем системное сообщение
      setMessages(prev => [
        ...prev,
        {
          playerId: 'system',
          playerName: 'Система',
          message: `Игрок ${playerName} присоединился к комнате`,
          timestamp: Date.now(),
        },
      ]);
      
      return room;
    } catch (error) {
      setError(error instanceof Error ? error : new NetworkError('Ошибка при присоединении к комнате'));
      throw error;
    }
  }, [status]);
  
  // Имитация выхода из комнаты
  const leaveRoom = useCallback(async () => {
    if (!currentRoom) {
      return;
    }
    
    try {
      // Имитация задержки сети
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Сбрасываем состояние комнаты
      setCurrentRoom(null);
      setGameState(null);
      
      return true;
    } catch (error) {
      setError(error instanceof Error ? error : new NetworkError('Ошибка при выходе из комнаты'));
      throw error;
    }
  }, [currentRoom]);
  
  // Имитация установки готовности игрока
  const setReady = useCallback(async (isReady: boolean) => {
    if (!currentRoom) {
      throw new NetworkError('Вы не находитесь в комнате');
    }
    
    try {
      // Имитация задержки сети
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Обновляем состояние игрока
      setCurrentRoom(prev => {
        if (!prev) return null;
        
        return {
          ...prev,
          players: prev.players.map(player => 
            player.id === prev.playerId ? { ...player, isReady } : player
          ),
        };
      });
      
      return true;
    } catch (error) {
      setError(error instanceof Error ? error : new NetworkError('Ошибка при изменении готовности'));
      throw error;
    }
  }, [currentRoom]);
  
  // Имитация начала игры
  const startGame = useCallback(async () => {
    if (!currentRoom) {
      throw new NetworkError('Вы не находитесь в комнате');
    }
    
    if (currentRoom.playerId !== currentRoom.host) {
      throw new NetworkError('Только хост может начать игру');
    }
    
    if (!currentRoom.players.every(player => player.isReady)) {
      throw new NetworkError('Не все игроки готовы');
    }
    
    if (currentRoom.players.length < 2) {
      throw new NetworkError('Недостаточно игроков для начала игры');
    }
    
    try {
      // Имитация задержки сети
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Обновляем состояние комнаты
      setCurrentRoom(prev => {
        if (!prev) return null;
        
        return {
          ...prev,
          status: 'playing',
        };
      });
      
      // Имитация начального состояния игры
      setGameState({
        gameStatus: 'in_progress',
        currentPlayerId: currentRoom.players[0].id,
        currentPlayerIndex: 0,
        players: currentRoom.players.map(player => ({
          id: player.id,
          name: player.name,
          hand: [],
          isActive: player.id === currentRoom.players[0].id,
        })),
        // Другие свойства игрового состояния
      });
      
      // Добавляем системное сообщение
      setMessages(prev => [
        ...prev,
        {
          playerId: 'system',
          playerName: 'Система',
          message: 'Игра началась!',
          timestamp: Date.now(),
        },
      ]);
      
      return true;
    } catch (error) {
      setError(error instanceof Error ? error : new NetworkError('Ошибка при начале игры'));
      throw error;
    }
  }, [currentRoom]);
  
  // Имитация выполнения хода
  const makeMove = useCallback(async (moveData: any) => {
    if (!currentRoom || currentRoom.status !== 'playing') {
      throw new NetworkError('Игра не активна');
    }
    
    if (!gameState || gameState.currentPlayerId !== currentRoom.playerId) {
      throw new NetworkError('Сейчас не ваш ход');
    }
    
    try {
      // Имитация задержки сети
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Имитация обновления игрового состояния
      // В реальном приложении здесь будет логика обработки хода
      
      // Добавляем системное сообщение
      setMessages(prev => [
        ...prev,
        {
          playerId: 'system',
          playerName: 'Система',
          message: `Игрок ${currentRoom.players.find(p => p.id === currentRoom.playerId)?.name} сделал ход`,
          timestamp: Date.now(),
        },
      ]);
      
      return { success: true };
    } catch (error) {
      setError(error instanceof Error ? error : new NetworkError('Ошибка при выполнении хода'));
      throw error;
    }
  }, [currentRoom, gameState]);
  
  // Имитация отправки сообщения в чат
  const sendMessage = useCallback(async (message: string) => {
    if (!currentRoom) {
      throw new NetworkError('Вы не находитесь в комнате');
    }
    
    try {
      // Имитация задержки сети
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Добавляем сообщение в чат
      const playerName = currentRoom.players.find(p => p.id === currentRoom.playerId)?.name || 'Неизвестный';
      
      setMessages(prev => [
        ...prev,
        {
          playerId: currentRoom.playerId,
          playerName,
          message,
          timestamp: Date.now(),
        },
      ]);
      
      return true;
    } catch (error) {
      setError(error instanceof Error ? error : new NetworkError('Ошибка при отправке сообщения'));
      throw error;
    }
  }, [currentRoom]);
  
  // Автоматическое подключение при монтировании компонента
  useEffect(() => {
    if (autoConnect) {
      connect('guest', 'guest-session').catch(console.error);
    }
    
    // Отключаемся при размонтировании компонента
    return () => {
      disconnect().catch(console.error);
    };
  }, [autoConnect, connect, disconnect]);
  
  return {
    status,
    error,
    currentRoom,
    gameState,
    messages,
    connect,
    disconnect,
    joinRoom,
    leaveRoom,
    setReady,
    startGame,
    makeMove,
    sendMessage,
  };
}