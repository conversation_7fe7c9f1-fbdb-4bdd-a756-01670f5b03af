/**
 * Улучшенный хук для интеграции многопользовательского режима с игровым ядром
 */

import { useState, useEffect, useCallback } from 'react';
import { useGameConnection } from './useGameConnection';
import { multiplayerService } from '../multiplayerService';
import { GameSettings } from '../types';
import { defaultGameSettings } from '../../config/gameConfig';

// Типы для состояния многопользовательской игры
export interface UseMultiplayerGameOptions {
  serverUrl: string;
  roomId?: string;
  autoConnect?: boolean;
  playerName?: string;
  authToken?: string;
}

/**
 * Улучшенный хук для интеграции многопользовательского режима с игровым ядром
 * @param options Настройки подключения и игры
 */
export function useMultiplayerGame(options: UseMultiplayerGameOptions) {
  const {
    serverUrl,
    roomId,
    autoConnect = true,
    playerName = `Игрок-${Math.floor(Math.random() * 1000)}`,
    authToken
  } = options;

  // Используем базовый хук для подключения к игровому серверу
  const gameConnection = useGameConnection({
    serverUrl,
    autoConnect: false
  });

  const {
    status,
    error,
    currentRoom,
    gameState,
    messages,
    connect,
    joinRoom,
    leaveRoom,
    setReady,
    startGame,
    makeMove,
    sendMessage: sendChatMessage
  } = gameConnection;

  // Состояние для отслеживания синхронизации с игровым ядром
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncError, setSyncError] = useState<Error | null>(null);
  const [settings, setSettings] = useState<GameSettings>(defaultGameSettings);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [isReconnecting, setIsReconnecting] = useState(false);

  // Устанавливаем токен авторизации при его изменении
  useEffect(() => {
    if (authToken) {
      multiplayerService.setAuthToken(authToken);
    }
  }, [authToken]);

  // Подключаемся к серверу при монтировании компонента
  useEffect(() => {
    if (!autoConnect) return;

    const connectToServer = async () => {
      try {
        const playerId = `player-${Date.now()}`;
        const sessionToken = authToken || 'guest-session'; // В будущем здесь будет настоящий токен авторизации
        
        await connect(playerId, sessionToken);
        
        // Если указан ID комнаты, присоединяемся к ней
        if (roomId) {
          await joinRoom(roomId, playerName);
        }
      } catch (err) {
        console.error('Ошибка при подключении:', err);
      }
    };
    
    connectToServer();
    
    // Отключаемся при размонтировании компонента
    return () => {
      if (status === 'connected' && currentRoom) {
        leaveRoom().catch(console.error);
      }
    };
  }, [autoConnect, roomId, playerName, connect, joinRoom, leaveRoom, status, currentRoom, authToken]);

  // Обработчик для синхронизации хода с другими игроками
  const syncMove = useCallback((moveData: any) => {
    if (!currentRoom || currentRoom.status !== 'playing') {
      return Promise.reject(new Error('Игра не активна'));
    }
    
    setIsSyncing(true);
    setSyncError(null);
    
    return makeMove(moveData)
      .then((result) => {
        setIsSyncing(false);
        return result;
      })
      .catch((err) => {
        setIsSyncing(false);
        setSyncError(err instanceof Error ? err : new Error('Ошибка синхронизации'));
        throw err;
      });
  }, [currentRoom, makeMove]);

  // Обработчик для отправки сообщения в чат
  const sendMessage = useCallback((message: string) => {
    if (!message.trim()) return Promise.resolve(false);
    
    return sendChatMessage(message)
      .catch((err) => {
        console.error('Ошибка при отправке сообщения:', err);
        return false;
      });
  }, [sendChatMessage]);

  // Обработчик для обновления настроек игры
  const updateSettings = useCallback((newSettings: GameSettings) => {
    setSettings(newSettings);
    
    if (currentRoom) {
      // В реальном приложении здесь будет запрос к API
      multiplayerService.updateGameSettings(currentRoom.id, newSettings)
        .catch((err) => {
          console.error('Ошибка при обновлении настроек:', err);
        });
    }
  }, [currentRoom]);

  // Обработчик для переподключения при потере соединения
  const reconnect = useCallback(async () => {
    if (isReconnecting || status === 'connected') return;
    
    setIsReconnecting(true);
    setReconnectAttempts(prev => prev + 1);
    
    try {
      const playerId = currentRoom?.playerId || `player-${Date.now()}`;
      const sessionToken = authToken || 'guest-session';
      
      await connect(playerId, sessionToken);
      
      // Если был в комнате, пытаемся переподключиться к ней
      if (currentRoom) {
        await joinRoom(currentRoom.id, playerName);
      }
      
      setReconnectAttempts(0);
      return true;
    } catch (err) {
      console.error('Ошибка при переподключении:', err);
      return false;
    } finally {
      setIsReconnecting(false);
    }
  }, [isReconnecting, status, currentRoom, connect, joinRoom, playerName, authToken]);

  // Проверка, является ли текущий игрок активным (его ход)
  const isActivePlayer = useCallback(() => {
    if (!gameState || !currentRoom) return false;
    
    // Логика определения активного игрока на основе состояния игры
    return gameState.currentPlayerId === currentRoom.playerId;
  }, [gameState, currentRoom]);

  // Автоматическое переподключение при ошибке соединения
  useEffect(() => {
    if (error && status === 'error' && reconnectAttempts < 3 && !isReconnecting) {
      const timer = setTimeout(() => {
        reconnect().catch(console.error);
      }, 5000 * (reconnectAttempts + 1)); // Увеличиваем время между попытками
      
      return () => clearTimeout(timer);
    }
  }, [error, status, reconnectAttempts, isReconnecting, reconnect]);

  return {
    // Базовые свойства и методы из useGameConnection
    ...gameConnection,
    
    // Дополнительные свойства и методы для многопользовательской игры
    isSyncing,
    syncError,
    syncMove,
    isActivePlayer: isActivePlayer(),
    settings,
    updateSettings,
    reconnect,
    reconnectAttempts,
    isReconnecting,
    sendMessage,
    
    // Вспомогательные методы для управления игрой
    isHost: currentRoom?.host === currentRoom?.playerId,
    allPlayersReady: currentRoom?.players.every((p: any) => p.isReady) ?? false,
    enoughPlayers: (currentRoom?.players.length ?? 0) >= 2
  };
}