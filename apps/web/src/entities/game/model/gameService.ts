import { createAsyncThunk } from "@reduxjs/toolkit";
import { CardModel, CardSuit, CardRank } from "../../card/model/types";
import {
  PlayerAction,
  GameStatus,
  PlayerRole,
  PlayerModel,
  gameStateUpdated,
} from "./slice";

// Интерфейс для взаимодействия с игровым ядром
interface GameCoreAPI {
  startGame: (players: number, gameType: string) => Promise<any>;
  makeMove: (
    playerId: string,
    cardId: string,
    action: PlayerAction,
    gameState: any,
  ) => Promise<any>;
  getGameState: (gameId: string) => Promise<any>;
}

// Вспомогательные функции для игровой логики
const canAttackWith = (
  card: CardModel,
  tableCards: CardModel[][],
  trumpSuit: string,
): boolean => {
  // Если стол пуст, можно атаковать любой картой
  if (tableCards.length === 0) return true;

  // Собираем все ранги карт на столе
  const ranksOnTable = tableCards.flat().map((c) => c.rank);

  // Можно атаковать только картой с рангом, который уже есть на столе
  return ranksOnTable.includes(card.rank);
};

const canDefendWith = (
  attackCard: CardModel,
  defendCard: CardModel,
  trumpSuit: string,
): boolean => {
  // Защита той же масти: ранг защищающей карты должен быть выше
  if (attackCard.suit === defendCard.suit) {
    return defendCard.rank > attackCard.rank;
  }

  // Защита козырем: если атакующая карта не козырь, а защищающая козырь
  if (defendCard.suit === trumpSuit && attackCard.suit !== trumpSuit) {
    return true;
  }

  // Во всех остальных случаях защита невозможна
  return false;
};

const findUndefendedAttack = (tableCards: CardModel[][]): number => {
  return tableCards.findIndex((pair) => pair.length === 1);
};

const allAttacksDefended = (tableCards: CardModel[][]): boolean => {
  return tableCards.every((pair) => pair.length === 2);
};

const dealCards = (
  players: PlayerModel[],
  deck: CardModel[],
  minCardsInHand: number = 6,
): { updatedPlayers: PlayerModel[]; updatedDeck: CardModel[] } => {
  const updatedPlayers = [...players];
  let updatedDeck = [...deck];

  // Раздаем карты игрокам, начиная с атакующего
  const playerOrder = [...updatedPlayers].sort((a, b) => {
    if (a.role === "attacker") return -1;
    if (b.role === "attacker") return 1;
    if (a.role === "defender") return -1;
    if (b.role === "defender") return 1;
    return 0;
  });

  for (const player of playerOrder) {
    while (player.cards.length < minCardsInHand && updatedDeck.length > 0) {
      const card = updatedDeck.shift();
      if (card) {
        // Делаем карту видимой для игрока
        player.cards.push({
          ...card,
          isVisible: player.id.includes("player_0"),
        });
      }
    }
  }

  return { updatedPlayers, updatedDeck };
};

const switchPlayerRoles = (players: PlayerModel[]): PlayerModel[] => {
  const updatedPlayers = [...players];

  // Находим текущего атакующего и защищающегося
  const attackerIndex = updatedPlayers.findIndex((p) => p.role === "attacker");
  const defenderIndex = updatedPlayers.findIndex((p) => p.role === "defender");

  if (attackerIndex !== -1 && defenderIndex !== -1) {
    // Защищающийся становится атакующим
    updatedPlayers[defenderIndex].role = "attacker";
    updatedPlayers[defenderIndex].isActive = true;

    // Атакующий становится ожидающим
    updatedPlayers[attackerIndex].role = "waiting";
    updatedPlayers[attackerIndex].isActive = false;

    // Если есть ожидающие игроки, первый из них становится защищающимся
    const nextDefenderIndex = updatedPlayers.findIndex(
      (p) => p.role === "waiting" && p.id !== updatedPlayers[attackerIndex].id,
    );
    if (nextDefenderIndex !== -1) {
      updatedPlayers[nextDefenderIndex].role = "defender";
      updatedPlayers[nextDefenderIndex].isActive = false; // Пока ход атакующего
    }
  }

  return updatedPlayers;
};

const checkGameOver = (
  players: PlayerModel[],
  deck: CardModel[],
): { isOver: boolean; winnerId: string | null } => {
  // Игра заканчивается, когда у одного игрока нет карт и колода пуста
  if (deck.length === 0) {
    const playersWithoutCards = players.filter((p) => p.cards.length === 0);
    if (playersWithoutCards.length > 0) {
      return { isOver: true, winnerId: playersWithoutCards[0].id };
    }

    // Если остался только один игрок с картами, он проиграл
    const playersWithCards = players.filter((p) => p.cards.length > 0);
    if (playersWithCards.length === 1) {
      return {
        isOver: true,
        winnerId:
          players.find((p) => p.id !== playersWithCards[0].id)?.id || null,
      };
    }
  }

  return { isOver: false, winnerId: null };
};

// Реализация игрового ядра
const gameCoreAPI: GameCoreAPI = {
  startGame: async (players, gameType) => {
    console.log(`Запуск игры с ${players} игроками, тип: ${gameType}`);

    // Генерация колоды карт
    const suits: CardSuit[] = ["hearts", "diamonds", "clubs", "spades"];
    const ranks: CardRank[] = [6, 7, 8, 9, 10, 11, 12, 13, 14] as CardRank[]; // В дураке используются карты от 6 до туза (14)

    const deck: CardModel[] = [];
    suits.forEach((suit) => {
      ranks.forEach((rank) => {
        deck.push({
          id: `${suit}_${rank}`,
          suit,
          rank,
          isVisible: false,
        });
      });
    });

    // Перемешивание колоды
    const shuffledDeck = [...deck].sort(() => Math.random() - 0.5);

    // Раздача карт игрокам
    const playerCards: CardModel[][] = [];
    for (let i = 0; i < players; i++) {
      playerCards.push(
        shuffledDeck
          .splice(0, 6)
          .map((card) => ({ ...card, isVisible: i === 0 })),
      );
    }

    // Козырная карта (последняя в колоде)
    const trumpCard = shuffledDeck[shuffledDeck.length - 1];
    // Перемещаем козырную карту в начало колоды, чтобы она была видна
    const trumpCardCopy = { ...trumpCard, isVisible: true };
    shuffledDeck.splice(shuffledDeck.length - 1, 1);
    shuffledDeck.unshift(trumpCardCopy);

    return {
      gameId: "game_" + Date.now(),
      players: Array.from({ length: players }, (_, i) => ({
        id: `player_${i}`,
        name: i === 0 ? "Вы" : `Игрок ${i}`,
        cards: playerCards[i],
        role: i === 0 ? "attacker" : i === 1 ? "defender" : "waiting",
        isActive: i === 0,
      })),
      deck: shuffledDeck,
      trumpCard: trumpCardCopy,
      trumpSuit: trumpCard.suit,
      tableCards: [],
      discardPile: [],
      gameStatus: GameStatus.IN_PROGRESS,
    };
  },

  makeMove: async (playerId, cardId, action, gameState) => {
    console.log(
      `Игрок ${playerId} выполняет действие ${action} с картой ${cardId}`,
    );

    // Копируем текущее состояние игры для обработки
    const currentState = { ...gameState };
    let { players, deck, tableCards, discardPile, trumpSuit } = currentState;

    // Находим текущего игрока
    const playerIndex = players.findIndex((p) => p.id === playerId);
    if (playerIndex === -1) {
      return { success: false, error: "Игрок не найден" };
    }

    const player = players[playerIndex];
    let updatedPlayers = [...players];
    let updatedTableCards = [...tableCards];
    let updatedDeck = [...deck];
    let updatedDiscardPile = [...discardPile];
    let gameOver = { isOver: false, winnerId: null };

    // Обработка различных действий игрока
    switch (action) {
      case PlayerAction.ATTACK:
        if (player.role !== "attacker" || !player.isActive) {
          return { success: false, error: "Сейчас не ваш ход для атаки" };
        }

        // Находим карту, которой игрок атакует
        const attackCard = player.cards.find((c) => c.id === cardId);
        if (!attackCard) {
          return { success: false, error: "Карта не найдена" };
        }

        // Проверяем, можно ли атаковать этой картой
        if (!canAttackWith(attackCard, updatedTableCards, trumpSuit)) {
          return { success: false, error: "Нельзя атаковать этой картой" };
        }

        // Удаляем карту из руки игрока
        updatedPlayers[playerIndex].cards = player.cards.filter(
          (c) => c.id !== cardId,
        );

        // Добавляем карту на стол
        updatedTableCards.push([{ ...attackCard, isVisible: true }]);

        // Активируем защищающегося игрока
        const defenderIndex = updatedPlayers.findIndex(
          (p) => p.role === "defender",
        );
        if (defenderIndex !== -1) {
          updatedPlayers[defenderIndex].isActive = true;
          updatedPlayers[playerIndex].isActive = false;
        }

        break;

      case PlayerAction.DEFEND:
        if (player.role !== "defender" || !player.isActive) {
          return { success: false, error: "Сейчас не ваш ход для защиты" };
        }

        // Находим незащищенную атаку
        const undefendedAttackIndex = findUndefendedAttack(updatedTableCards);
        if (undefendedAttackIndex === -1) {
          return { success: false, error: "Нет незащищенных атак" };
        }

        // Находим карту, которой игрок защищается
        const defendCard = player.cards.find((c) => c.id === cardId);
        if (!defendCard) {
          return { success: false, error: "Карта не найдена" };
        }

        // Проверяем, можно ли защититься этой картой
        const attackingCard = updatedTableCards[undefendedAttackIndex][0];
        if (!canDefendWith(attackingCard, defendCard, trumpSuit)) {
          return { success: false, error: "Нельзя защититься этой картой" };
        }

        // Удаляем карту из руки игрока
        updatedPlayers[playerIndex].cards = player.cards.filter(
          (c) => c.id !== cardId,
        );

        // Добавляем карту на стол
        updatedTableCards[undefendedAttackIndex].push({
          ...defendCard,
          isVisible: true,
        });

        // Если все атаки отбиты, возвращаем ход атакующему
        if (allAttacksDefended(updatedTableCards)) {
          const attackerIndex = updatedPlayers.findIndex(
            (p) => p.role === "attacker",
          );
          if (attackerIndex !== -1) {
            updatedPlayers[attackerIndex].isActive = true;
            updatedPlayers[playerIndex].isActive = false;
          }
        }

        break;

      case PlayerAction.TAKE:
        if (player.role !== "defender" || !player.isActive) {
          return { success: false, error: "Сейчас не ваш ход для взятия карт" };
        }

        // Защищающийся берет все карты со стола
        const allTableCards = updatedTableCards.flat();
        updatedPlayers[playerIndex].cards = [...player.cards, ...allTableCards];

        // Очищаем стол
        updatedTableCards = [];

        // Раздаем карты игрокам
        const dealResult = dealCards(updatedPlayers, updatedDeck);
        updatedPlayers = dealResult.updatedPlayers;
        updatedDeck = dealResult.updatedDeck;

        // Меняем роли игроков
        updatedPlayers = switchPlayerRoles(updatedPlayers);

        break;

      case PlayerAction.PASS:
        // Пас (бито) - можно использовать только когда все атаки отбиты
        if (!allAttacksDefended(updatedTableCards)) {
          return {
            success: false,
            error: 'Нельзя сказать "бито", пока не все атаки отбиты',
          };
        }

        // Перемещаем все карты со стола в отбой
        updatedDiscardPile = [
          ...updatedDiscardPile,
          ...updatedTableCards.flat(),
        ];
        updatedTableCards = [];

        // Раздаем карты игрокам
        const dealResult2 = dealCards(updatedPlayers, updatedDeck);
        updatedPlayers = dealResult2.updatedPlayers;
        updatedDeck = dealResult2.updatedDeck;

        // Меняем роли игроков
        updatedPlayers = switchPlayerRoles(updatedPlayers);

        break;

      case PlayerAction.DONE:
        if (player.role !== "attacker" || !player.isActive) {
          return {
            success: false,
            error: "Сейчас не ваш ход для завершения атаки",
          };
        }

        // Если на столе есть карты и все атаки отбиты, перемещаем их в отбой
        if (
          updatedTableCards.length > 0 &&
          allAttacksDefended(updatedTableCards)
        ) {
          updatedDiscardPile = [
            ...updatedDiscardPile,
            ...updatedTableCards.flat(),
          ];
          updatedTableCards = [];

          // Раздаем карты игрокам
          const dealResult3 = dealCards(updatedPlayers, updatedDeck);
          updatedPlayers = dealResult3.updatedPlayers;
          updatedDeck = dealResult3.updatedDeck;

          // Меняем роли игроков
          updatedPlayers = switchPlayerRoles(updatedPlayers);
        } else {
          return {
            success: false,
            error: "Нельзя завершить атаку, пока не все атаки отбиты",
          };
        }

        break;
    }

    // Проверяем, закончилась ли игра
    gameOver = checkGameOver(updatedPlayers, updatedDeck);

    return {
      success: true,
      players: updatedPlayers,
      currentPlayerId: updatedPlayers.find((p) => p.isActive)?.id || null,
      tableCards: updatedTableCards,
      deck: updatedDeck,
      discardPile: updatedDiscardPile,
      gameOver,
    };
  },

  getGameState: async (gameId) => {
    console.log(`Получение состояния игры ${gameId}`);
    return {
      // В реальном приложении здесь будет запрос к серверу
      success: true,
      // Текущее состояние игры
    };
  },
};

// Асинхронные thunk-действия для Redux
export const startGame = createAsyncThunk(
  "game/startGame",
  async (
    { players, gameType }: { players: number; gameType: string },
    { rejectWithValue },
  ) => {
    try {
      const gameState = await gameCoreAPI.startGame(players, gameType);
      return gameState;
    } catch (error) {
      return rejectWithValue("Не удалось начать игру");
    }
  },
);

export const makeMove = createAsyncThunk(
  "game/makeMove",
  async (
    {
      playerId,
      cardId,
      action,
    }: {
      playerId: string;
      cardId?: string;
      action: PlayerAction;
    },
    { rejectWithValue, getState, dispatch },
  ) => {
    try {
      // Получаем текущее состояние игры из Redux
      const gameState = (getState() as any).game;

      // Выполняем ход
      const result = await gameCoreAPI.makeMove(
        playerId,
        cardId || "",
        action,
        gameState,
      );

      if (!result.success) {
        return rejectWithValue(result.error || "Не удалось выполнить ход");
      }

      // Обновляем состояние игры
      dispatch(
        gameStateUpdated({
          players: result.players,
          currentPlayerId: result.currentPlayerId,
          tableCards: result.tableCards,
          deck: result.deck,
          discardPile: result.discardPile,
        }),
      );

      // Если игра закончилась, обновляем статус
      if (result.gameOver && result.gameOver.isOver) {
        dispatch({
          type: "game/gameFinished",
          payload: result.gameOver.winnerId,
        });
      }

      return result;
    } catch (error) {
      return rejectWithValue("Не удалось выполнить ход");
    }
  },
);

export const getGameState = createAsyncThunk(
  "game/getGameState",
  async (gameId: string, { rejectWithValue }) => {
    try {
      const gameState = await gameCoreAPI.getGameState(gameId);
      return gameState;
    } catch (error) {
      return rejectWithValue("Не удалось получить состояние игры");
    }
  },
);
