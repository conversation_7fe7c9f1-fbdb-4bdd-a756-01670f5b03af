import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { Player, PlayerAction, Card } from '@a1-k/core';
import { GameNotificationItem } from '../ui/GameNotifications';
import { GameSettings } from './types';
import { defaultGameSettings } from '../config/gameConfig';

// Типы для многопользовательского режима
export interface GameInfo {
  id: string;
  name: string;
  players: number;
  maxPlayers: number;
  status: 'waiting' | 'in_progress' | 'finished';
}

export interface ChatMessage {
  id: string;
  sender: string;
  senderId: string;
  text: string;
  timestamp: number;
  isSystem?: boolean;
}

export interface PlayerMoveData {
  action: PlayerAction;
  playerId?: string;
  gameId?: string;
  card?: Card;
  targetCard?: Card;
}

export interface MoveHistoryItem {
  playerId: string;
  playerName: string;
  action: PlayerAction;
  card?: Card;
  targetCard?: Card;
  timestamp: number;
}

export interface PlayerStats {
  playerId: string;
  playerName: string;
  gamesPlayed: number;
  gamesWon: number;
  attacksCount: number;
  successfulDefends: number;
  cardsPlayed: number;
  cardsTaken: number;
}

// Интерфейс для комнаты
export interface GameRoom {
  id: string;
  name: string;
  host: string;
  playerId: string;
  players: Array<{
    id: string;
    name: string;
    isReady: boolean;
  }>;
  status: 'waiting' | 'playing' | 'finished';
  maxPlayers: number;
}

// Интерфейс контекста многопользовательской игры
interface MultiplayerContextType {
  // Состояние игрока
  playerId: string;
  playerName: string;
  
  // Состояние игры
  gameId: string | undefined;
  isLoading: boolean;
  isSyncing: boolean;
  isConnected: boolean;
  connectionError: Error | null;
  
  // Игровые данные
  players: Player[];
  availableGames: GameInfo[];
  chatMessages: ChatMessage[];
  notifications: GameNotificationItem[];
  moveHistory: MoveHistoryItem[];
  playerStats: PlayerStats[];
  settings: GameSettings;
  gameState: any; // Тип зависит от реализации игрового ядра
  currentRoom: GameRoom | null; // Текущая комната
  
  // Методы
  joinGame: (gameId: string) => Promise<void>;
  createGame: () => Promise<string>;
  leaveGame: () => Promise<void>;
  sendMessage: (message: string) => void;
  playerMove: (moveData: PlayerMoveData) => Promise<any>;
  updateSettings: (newSettings: GameSettings) => void;
  reconnect: () => Promise<void>;
  refreshGames: () => Promise<void>;
}

// Создаем контекст с начальными значениями
const MultiplayerContext = createContext<MultiplayerContextType | undefined>(undefined);

// Провайдер контекста
export const MultiplayerProvider: React.FC<{
  children: ReactNode;
  initialGameId?: string;
}> = ({ children, initialGameId }) => {
  // Состояние игрока
  const [playerId] = useState<string>('player1'); // В реальном приложении будет получаться из авторизации
  const [playerName] = useState<string>('Игрок 1'); // В реальном приложении будет получаться из авторизации
  
  // Состояние игры
  const [gameId, setGameId] = useState<string | undefined>(initialGameId);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSyncing, setIsSyncing] = useState<boolean>(false);
  const [isConnected, setIsConnected] = useState<boolean>(true); // Имитация подключения
  const [connectionError, setConnectionError] = useState<Error | null>(null);
  
  // Игровые данные
  const [players, setPlayers] = useState<Player[]>([]);
  const [availableGames, setAvailableGames] = useState<GameInfo[]>([]);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [notifications, setNotifications] = useState<GameNotificationItem[]>([]);
  const [moveHistory, setMoveHistory] = useState<MoveHistoryItem[]>([]);
  const [playerStats, setPlayerStats] = useState<PlayerStats[]>([]);
  const [settings, setSettings] = useState<GameSettings>(defaultGameSettings);
  const [gameState, setGameState] = useState<any>(undefined);
  
  // Инициализация данных при монтировании компонента
  useEffect(() => {
    // Имитация загрузки данных с сервера
    setPlayers([
      { id: 'player1', name: 'Игрок 1', hand: [], isActive: true },
      { id: 'player2', name: 'Игрок 2', hand: [], isActive: false },
    ]);
    
    setAvailableGames([
      {
        id: 'game1',
        name: 'Игра #1',
        players: 1,
        maxPlayers: 2,
        status: 'waiting',
      },
      {
        id: 'game2',
        name: 'Игра #2',
        players: 2,
        maxPlayers: 2,
        status: 'in_progress',
      },
    ]);
    
    setChatMessages([
      {
        id: '1',
        sender: 'Система',
        senderId: 'system',
        text: 'Добро пожаловать в многопользовательский режим!',
        timestamp: Date.now(),
        isSystem: true,
      },
    ]);
    
    setNotifications([
      {
        id: '1',
        message: 'Добро пожаловать в многопользовательский режим!',
        type: 'info',
        timestamp: Date.now(),
        duration: 5000,
      },
    ]);
  }, []);
  
  // Обработчик присоединения к игре
  const joinGame = useCallback(async (gameId: string) => {
    setIsLoading(true);
    
    try {
      // Имитация задержки сети
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setGameId(gameId);
      // В реальном приложении здесь будет запрос к API для присоединения к игре
      
      // Добавляем уведомление
      setNotifications(prev => [
        ...prev,
        {
          id: Date.now().toString(),
          message: `Вы присоединились к игре #${gameId}`,
          type: 'success',
          timestamp: Date.now(),
          duration: 3000,
        }
      ]);
      
      // Добавляем сообщение в чат
      setChatMessages(prev => [
        ...prev,
        {
          id: Date.now().toString(),
          sender: 'Система',
          senderId: 'system',
          text: `Игрок ${playerName} присоединился к игре`,
          timestamp: Date.now(),
          isSystem: true,
        }
      ]);
    } catch (error) {
      // Обработка ошибок
      setConnectionError(error instanceof Error ? error : new Error('Ошибка при присоединении к игре'));
      setNotifications(prev => [
        ...prev,
        {
          id: Date.now().toString(),
          message: 'Ошибка при присоединении к игре',
          type: 'error',
          timestamp: Date.now(),
          duration: 5000,
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  }, [playerName]);
  
  // Обработчик создания игры
  const createGame = useCallback(async () => {
    setIsLoading(true);
    
    try {
      // Имитация задержки сети
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Генерируем случайный ID игры
      const newGameId = `game${Date.now()}`;
      
      // В реальном приложении здесь будет запрос к API для создания игры
      setGameId(newGameId);
      
      // Добавляем уведомление
      setNotifications(prev => [
        ...prev,
        {
          id: Date.now().toString(),
          message: `Вы создали игру #${newGameId}`,
          type: 'success',
          timestamp: Date.now(),
          duration: 3000,
        }
      ]);
      
      return newGameId;
    } catch (error) {
      // Обработка ошибок
      setConnectionError(error instanceof Error ? error : new Error('Ошибка при создании игры'));
      setNotifications(prev => [
        ...prev,
        {
          id: Date.now().toString(),
          message: 'Ошибка при создании игры',
          type: 'error',
          timestamp: Date.now(),
          duration: 5000,
        }
      ]);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  // Обработчик выхода из игры
  const leaveGame = useCallback(async () => {
    setIsLoading(true);
    
    try {
      // Имитация задержки сети
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // В реальном приложении здесь будет запрос к API для выхода из игры
      setGameId(undefined);
      
      // Добавляем уведомление
      setNotifications(prev => [
        ...prev,
        {
          id: Date.now().toString(),
          message: 'Вы вышли из игры',
          type: 'info',
          timestamp: Date.now(),
          duration: 3000,
        }
      ]);
    } catch (error) {
      // Обработка ошибок
      setConnectionError(error instanceof Error ? error : new Error('Ошибка при выходе из игры'));
      setNotifications(prev => [
        ...prev,
        {
          id: Date.now().toString(),
          message: 'Ошибка при выходе из игры',
          type: 'error',
          timestamp: Date.now(),
          duration: 5000,
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  // Обработчик отправки сообщения
  const sendMessage = useCallback((message: string) => {
    // Добавляем сообщение в чат
    setChatMessages(prev => [
      ...prev,
      {
        id: Date.now().toString(),
        sender: playerName,
        senderId: playerId,
        text: message,
        timestamp: Date.now(),
      }
    ]);
  }, [playerId, playerName]);
  
  // Обработчик хода игрока
  const playerMove = useCallback(async (moveData: PlayerMoveData) => {
    setIsSyncing(true);
    
    try {
      // Имитация задержки сети
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // В реальном приложении здесь будет запрос к API для выполнения хода
      console.log('Ход игрока:', moveData);
      
      // Добавляем уведомление
      setNotifications(prev => [
        ...prev,
        {
          id: Date.now().toString(),
          message: `Ход выполнен: ${moveData.action}`,
          type: 'game',
          timestamp: Date.now(),
          duration: 2000,
        }
      ]);
      
      return { success: true };
    } catch (error) {
      // Обработка ошибок
      setConnectionError(error instanceof Error ? error : new Error('Ошибка при выполнении хода'));
      setNotifications(prev => [
        ...prev,
        {
          id: Date.now().toString(),
          message: 'Ошибка при выполнении хода',
          type: 'error',
          timestamp: Date.now(),
          duration: 5000,
        }
      ]);
      throw error;
    } finally {
      setIsSyncing(false);
    }
  }, []);
  
  // Обработчик изменения настроек
  const updateSettings = useCallback((newSettings: GameSettings) => {
    setSettings(newSettings);
    
    // Добавляем уведомление
    setNotifications(prev => [
      ...prev,
      {
        id: Date.now().toString(),
        message: 'Настройки сохранены',
        type: 'success',
        timestamp: Date.now(),
        duration: 3000,
      }
    ]);
  }, []);
  
  // Обработчик переподключения
  const reconnect = useCallback(async () => {
    setIsLoading(true);
    setConnectionError(null);
    
    try {
      // Имитация задержки сети
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setIsConnected(true);
      
      // Добавляем уведомление
      setNotifications(prev => [
        ...prev,
        {
          id: Date.now().toString(),
          message: 'Подключение восстановлено',
          type: 'success',
          timestamp: Date.now(),
          duration: 3000,
        }
      ]);
    } catch (error) {
      setIsConnected(false);
      setConnectionError(error instanceof Error ? error : new Error('Ошибка при переподключении'));
      setNotifications(prev => [
        ...prev,
        {
          id: Date.now().toString(),
          message: 'Ошибка при переподключении',
          type: 'error',
          timestamp: Date.now(),
          duration: 5000,
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  // Обработчик обновления списка доступных игр
  const refreshGames = useCallback(async () => {
    setIsLoading(true);
    
    try {
      // Имитация задержки сети
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // В реальном приложении здесь будет запрос к API для получения списка игр
      // Для имитации просто обновляем время в названиях игр
      setAvailableGames(prev => prev.map(game => ({
        ...game,
        name: `${game.name.split('#')[0]}#${Date.now().toString().slice(-4)}`,
      })));
      
      // Добавляем уведомление
      setNotifications(prev => [
        ...prev,
        {
          id: Date.now().toString(),
          message: 'Список игр обновлен',
          type: 'info',
          timestamp: Date.now(),
          duration: 2000,
        }
      ]);
    } catch (error) {
      setConnectionError(error instanceof Error ? error : new Error('Ошибка при обновлении списка игр'));
      setNotifications(prev => [
        ...prev,
        {
          id: Date.now().toString(),
          message: 'Ошибка при обновлении списка игр',
          type: 'error',
          timestamp: Date.now(),
          duration: 5000,
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  // Создаем объект текущей комнаты на основе gameId
  const currentRoom = gameId ? {
    id: gameId,
    name: `Комната #${gameId}`,
    host: 'player1', // В реальном приложении будет определяться на сервере
    playerId,
    players: players.map(player => ({
      id: player.id,
      name: player.name,
      isReady: player.id === playerId // Для примера, в реальном приложении будет приходить с сервера
    })),
    status: gameState?.gameStatus === 'in_progress' ? 'playing' : 'waiting',
    maxPlayers: 4
  } : null;

  // Значение контекста
  const contextValue: MultiplayerContextType = {
    // Состояние игрока
    playerId,
    playerName,
    
    // Состояние игры
    gameId,
    isLoading,
    isSyncing,
    isConnected,
    connectionError,
    
    // Игровые данные
    players,
    availableGames,
    chatMessages,
    notifications,
    moveHistory,
    playerStats,
    settings,
    gameState,
    currentRoom,
    
    // Методы
    joinGame,
    createGame,
    leaveGame,
    sendMessage,
    playerMove,
    updateSettings,
    reconnect,
    refreshGames,
  };
  
  return (
    <MultiplayerContext.Provider value={contextValue}>
      {children}
    </MultiplayerContext.Provider>
  );
};

// Хук для использования контекста
export const useMultiplayer = () => {
  const context = useContext(MultiplayerContext);
  
  if (context === undefined) {
    throw new Error('useMultiplayer должен использоваться внутри MultiplayerProvider');
  }
  
  return context;
};