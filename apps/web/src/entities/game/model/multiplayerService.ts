/**
 * Сервис для работы с многопользовательским режимом
 * Отвечает за взаимодействие с сервером и обработку сетевых операций
 */

import { Player, PlayerAction, Card } from '@a1-k/core';
import { GameInfo, ChatMessage, PlayerMoveData, MoveHistoryItem, PlayerStats } from './MultiplayerContext';
import { GameSettings } from './types';

// Интерфейс для достижений игрока
export interface PlayerAchievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  unlockedAt: number;
  progress?: number;
  maxProgress?: number;
}

// Интерфейс для отчета о рейтинге игрока
export interface PlayerRatingReport {
  playerId: string;
  playerName: string;
  rating: number;
  rank: string;
  winRate: number;
  totalGames: number;
  achievements: PlayerAchievement[];
  recentGames: {
    gameId: string;
    result: 'win' | 'loss' | 'draw';
    rating: number;
    ratingChange: number;
    date: number;
  }[];
}

// Класс для обработки ошибок сети
export class NetworkError extends Error {
  constructor(message: string, public code?: string, public originalError?: Error) {
    super(message);
    this.name = 'NetworkError';
  }
}

// Интерфейс для конфигурации сервиса
export interface MultiplayerServiceConfig {
  apiUrl: string;
  socketUrl?: string;
  reconnectAttempts?: number;
  reconnectDelay?: number;
  requestTimeout?: number;
}

import { SocketManager, createSocketConnection } from '../../../features/multiplayer/api/socket';

// Класс сервиса для работы с многопользовательским режимом
export class MultiplayerService {
  private apiUrl: string;
  private socketUrl: string;
  private reconnectAttempts: number;
  private reconnectDelay: number;
  private requestTimeout: number;
  private authToken?: string;
  private socketManager: SocketManager | null = null;
  
  constructor(config: MultiplayerServiceConfig) {
    this.apiUrl = config.apiUrl;
    this.socketUrl = config.socketUrl || this.apiUrl;
    this.reconnectAttempts = config.reconnectAttempts || 3;
    this.reconnectDelay = config.reconnectDelay || 2000;
    this.requestTimeout = config.requestTimeout || 5000;
    
    // Инициализация WebSocket соединения
    this.initSocketConnection();
  }
  
  // Инициализация WebSocket соединения
  private initSocketConnection(): void {
    try {
      this.socketManager = createSocketConnection({
        serverUrl: this.socketUrl,
        autoConnect: false,
        reconnection: true,
        reconnectionAttempts: this.reconnectAttempts,
        reconnectionDelay: this.reconnectDelay
      });
    } catch (error) {
      console.error('Ошибка при инициализации WebSocket соединения:', error);
    }
  }
  
  // Установка токена авторизации
  setAuthToken(token: string) {
    this.authToken = token;
  }
  
  // Подключение к серверу с авторизацией
  async connect(playerId: string, playerName: string): Promise<void> {
    if (!this.socketManager) {
      this.initSocketConnection();
    }
    
    if (!this.socketManager) {
      throw new NetworkError('Не удалось инициализировать соединение', 'INIT_ERROR');
    }
    
    try {
      // Устанавливаем обработчики событий
      this.setupSocketListeners();
      
      // Проверяем наличие токена авторизации
      if (!this.authToken) {
        // Если токена нет, выполняем запрос на авторизацию
        await this.authenticate(playerId, playerName);
      }
      
      // Подключаемся к серверу с токеном авторизации
      await this.socketManager.connect(this.authToken);
      
      return;
    } catch (error) {
      if (error instanceof Error) {
        throw new NetworkError(
          `Ошибка при подключении к серверу: ${error.message}`,
          'CONNECTION_ERROR',
          error
        );
      }
      throw new NetworkError('Неизвестная ошибка при подключении', 'UNKNOWN_ERROR');
    }
  }
  
  // Метод для авторизации пользователя
  private async authenticate(playerId: string, playerName: string): Promise<void> {
    try {
      // Формируем URL для запроса авторизации
      const authUrl = `${this.apiUrl}/auth/login`;
      
      // Выполняем запрос на авторизацию
      const response = await fetch(authUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          playerId,
          playerName,
          // Дополнительные данные для авторизации
          deviceId: this.getDeviceId(),
          timestamp: Date.now()
        })
      });
      
      if (!response.ok) {
        throw new Error(`Ошибка авторизации: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Сохраняем токен авторизации
      this.authToken = data.token;
      
      // Сохраняем токен в локальное хранилище для последующих сессий
      if (typeof window !== 'undefined') {
        localStorage.setItem('kozyr-master-auth-token', this.authToken);
      }
    } catch (error) {
      console.error('Ошибка при авторизации:', error);
      throw new NetworkError(
        'Не удалось выполнить авторизацию',
        'AUTH_ERROR',
        error instanceof Error ? error : undefined
      );
    }
  }
  
  // Получение уникального идентификатора устройства
  private getDeviceId(): string {
    if (typeof window !== 'undefined') {
      // Пытаемся получить сохраненный deviceId
      const savedDeviceId = localStorage.getItem('kozyr-master-device-id');
      
      if (savedDeviceId) {
        return savedDeviceId;
      }
      
      // Если deviceId не найден, генерируем новый
      const newDeviceId = `device_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      localStorage.setItem('kozyr-master-device-id', newDeviceId);
      
      return newDeviceId;
    }
    
    // Для SSR возвращаем временный идентификатор
    return `temp_device_${Date.now()}`;
  }
  
  // Отключение от сервера
  disconnect(): void {
    if (this.socketManager) {
      this.socketManager.disconnect();
    }
  }
  
  // Настройка обработчиков событий сокета
  private setupSocketListeners(): void {
    if (!this.socketManager) return;
    
    this.socketManager.setEvents({
      onConnect: () => {
        console.log('Соединение с сервером установлено');
      },
      onDisconnect: (reason) => {
        console.log(`Соединение с сервером разорвано: ${reason}`);
      },
      onError: (error) => {
        console.error('Ошибка соединения:', error);
      },
      onGameStateUpdate: (gameState) => {
        // Здесь будет обработка обновления состояния игры
        console.log('Получено обновление состояния игры:', gameState);
        // В реальном приложении здесь будет обновление состояния в контексте
      },
      onGameEvent: (event) => {
        // Здесь будет обработка игровых событий
        console.log('Получено игровое событие:', event);
        // В реальном приложении здесь будет обработка событий
      }
    });
  }
  
  // Получение списка доступных игр
  async getAvailableGames(): Promise<GameInfo[]> {
    // В реальном приложении здесь будет запрос к API
    // Имитация задержки сети
    await this.delay(1000);
    
    // Имитация ответа сервера
    return [
      {
        id: 'game1',
        name: 'Игра #1',
        players: 1,
        maxPlayers: 2,
        status: 'waiting',
      },
      {
        id: 'game2',
        name: 'Игра #2',
        players: 2,
        maxPlayers: 2,
        status: 'in_progress',
      },
    ];
  }
  
  // Создание новой игры
  async createGame(settings: GameSettings): Promise<string> {
    // В реальном приложении здесь будет запрос к API
    // Имитация задержки сети
    await this.delay(1500);
    
    // Генерируем случайный ID игры
    const newGameId = `game${Date.now()}`;
    
    return newGameId;
  }
  
  // Присоединение к игре с проверкой доступности
  async joinGame(gameId: string, playerName: string): Promise<void> {
    if (!this.socketManager) {
      throw new NetworkError('Не удалось инициализировать соединение', 'INIT_ERROR');
    }
    
    try {
      // Проверяем доступность игры перед присоединением
      const gameStatus = await this.checkGameAvailability(gameId);
      
      // Если игра недоступна, выбрасываем ошибку
      if (!gameStatus.available) {
        throw new Error(gameStatus.reason || 'Игра недоступна для присоединения');
      }
      
      // Имитация задержки сети
      await this.delay(1000);
      
      // Если игра доступна, отправляем запрос на присоединение
      await this.socketManager.emit('joinGame', { 
        gameId,
        playerName,
        authToken: this.authToken,
        timestamp: Date.now()
      });
      
      // Сохраняем информацию о последней игре в локальное хранилище
      if (typeof window !== 'undefined') {
        localStorage.setItem('kozyr-master-last-game', gameId);
      }
      
      console.log(`Игрок ${playerName} присоединился к игре ${gameId}`);
      
      return;
    } catch (error) {
      // Логируем ошибку
      console.error('Ошибка при присоединении к игре:', error);
      
      if (error instanceof Error) {
        throw new NetworkError(
          `Ошибка при присоединении к игре: ${error.message}`,
          'JOIN_GAME_ERROR',
          error
        );
      }
      throw new NetworkError('Неизвестная ошибка при присоединении к игре', 'UNKNOWN_ERROR');
    }
  }
  
  // Проверка доступности игры
  private async checkGameAvailability(gameId: string): Promise<{ available: boolean; reason?: string }> {
    try {
      // Формируем URL для запроса проверки доступности игры
      const checkUrl = `${this.apiUrl}/games/${gameId}/status`;
      
      // Выполняем запрос на проверку доступности игры
      const response = await fetch(checkUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`
        }
      });
      
      if (!response.ok) {
        // Если сервер вернул ошибку, получаем детали
        const errorData = await response.json();
        return { 
          available: false, 
          reason: errorData.message || `Ошибка сервера: ${response.status} ${response.statusText}` 
        };
      }
      
      const data = await response.json();
      
      // Проверяем статус игры
      if (data.status === 'in_progress' && data.playerCount >= data.maxPlayers) {
        return { available: false, reason: 'Игра уже заполнена' };
      }
      
      if (data.status === 'finished') {
        return { available: false, reason: 'Игра уже завершена' };
      }
      
      if (data.status === 'canceled') {
        return { available: false, reason: 'Игра была отменена' };
      }
      
      // Если все проверки пройдены, игра доступна
      return { available: true };
    } catch (error) {
      console.error('Ошибка при проверке доступности игры:', error);
      
      // В случае ошибки сети используем имитацию проверки
      // Это временное решение для демонстрации
      if (Math.random() > 0.2) {
        return { available: true };
      }
      
      return { 
        available: false, 
        reason: 'Не удалось проверить доступность игры. Пожалуйста, попробуйте позже.' 
      };
    }
  }
  
  // Выход из игры
  async leaveGame(gameId: string, playerId: string): Promise<void> {
    // В реальном приложении здесь будет запрос к API
    // Имитация задержки сети
    await this.delay(1000);
  }
  
  // Отправка сообщения в чат
  async sendChatMessage(gameId: string, message: string, playerId: string, playerName: string): Promise<ChatMessage> {
    // В реальном приложении здесь будет запрос к API
    // Имитация задержки сети
    await this.delay(500);
    
    // Создаем новое сообщение
    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      sender: playerName,
      senderId: playerId,
      text: message,
      timestamp: Date.now(),
    };
    
    return newMessage;
  }
  
  // Выполнение хода
  async makeMove(gameId: string, moveData: PlayerMoveData): Promise<any> {
    try {
      // Проверяем наличие соединения с сервером
      if (!this.socketManager) {
        throw new NetworkError('Нет соединения с сервером', 'NO_CONNECTION');
      }
      
      // Добавляем идентификатор игры в данные хода
      const movePayload = {
        ...moveData,
        gameId
      };
      
      // Отправляем ход на сервер через WebSocket с указанием таймаута
      const result = await this.socketManager.makeMove(movePayload, this.requestTimeout);
      
      // Возвращаем результат выполнения хода
      return result;
    } catch (error) {
      // Обрабатываем ошибки
      if (error instanceof Error) {
        throw new NetworkError(
          error.message || 'Ошибка при выполнении хода', 
          'MOVE_ERROR', 
          error
        );
      }
      throw new NetworkError('Неизвестная ошибка при выполнении хода', 'UNKNOWN_ERROR');
    }
  }
  
  // Получение истории ходов
  async getMoveHistory(gameId: string): Promise<MoveHistoryItem[]> {
    // В реальном приложении здесь будет запрос к API
    // Имитация задержки сети
    await this.delay(700);
    
    // Имитация ответа сервера
    return [];
  }
  
  // Получение статистики игроков
  async getPlayerStats(gameId: string): Promise<PlayerStats[]> {
    // В реальном приложении здесь будет запрос к API
    // Имитация задержки сети
    await this.delay(700);
    
    // Имитация ответа сервера
    return [];
  }
  
  // Обновление настроек игры
  async updateGameSettings(gameId: string, settings: GameSettings): Promise<void> {
    // В реальном приложении здесь будет запрос к API
    // Имитация задержки сети
    await this.delay(600);
  }
  
  /**
   * Обновление достижений игрока
   * @param playerId ID игрока
   * @param achievementId ID достижения для обновления
   * @param progress Текущий прогресс достижения (опционально)
   * @returns Обновленный список достижений игрока
   */
  async updatePlayerAchievements(playerId: string, achievementId: string, progress?: number): Promise<PlayerAchievement[]> {
    try {
      // Проверяем наличие соединения с сервером
      if (!this.socketManager) {
        throw new NetworkError('Нет соединения с сервером', 'NO_CONNECTION');
      }
      
      // Формируем URL для запроса обновления достижений
      const achievementsUrl = `${this.apiUrl}/players/${playerId}/achievements`;
      
      // Выполняем запрос на обновление достижений
      const response = await fetch(achievementsUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`
        },
        body: JSON.stringify({
          achievementId,
          progress,
          timestamp: Date.now()
        })
      });
      
      if (!response.ok) {
        throw new Error(`Ошибка обновления достижений: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Возвращаем обновленный список достижений
      return data.achievements;
    } catch (error) {
      console.error('Ошибка при обновлении достижений:', error);
      
      // В случае ошибки сети используем имитацию ответа
      // Это временное решение для демонстрации
      await this.delay(500);
      
      return [
        {
          id: achievementId,
          name: 'Тестовое достижение',
          description: 'Описание тестового достижения',
          icon: 'trophy',
          unlockedAt: progress && progress >= 100 ? Date.now() : 0,
          progress: progress || 0,
          maxProgress: 100
        }
      ];
    }
  }
  
  /**
   * Генерация отчета о рейтинге игрока
   * @param playerId ID игрока
   * @returns Отчет о рейтинге игрока
   */
  async generateRatingReport(playerId: string): Promise<PlayerRatingReport> {
    try {
      // Формируем URL для запроса отчета о рейтинге
      const ratingUrl = `${this.apiUrl}/players/${playerId}/rating`;
      
      // Выполняем запрос на получение отчета о рейтинге
      const response = await fetch(ratingUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`Ошибка получения отчета о рейтинге: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Возвращаем отчет о рейтинге
      return data;
    } catch (error) {
      console.error('Ошибка при получении отчета о рейтинге:', error);
      
      // В случае ошибки сети используем имитацию ответа
      // Это временное решение для демонстрации
      await this.delay(700);
      
      // Генерируем тестовый отчет о рейтинге
      return {
        playerId,
        playerName: 'Тестовый игрок',
        rating: 1200,
        rank: 'Новичок',
        winRate: 0.5,
        totalGames: 10,
        achievements: [
          {
            id: 'achievement1',
            name: 'Первая победа',
            description: 'Выиграйте свою первую игру',
            icon: 'trophy',
            unlockedAt: Date.now() - 86400000 // Вчера
          },
          {
            id: 'achievement2',
            name: 'Серия побед',
            description: 'Выиграйте 3 игры подряд',
            icon: 'fire',
            unlockedAt: 0,
            progress: 1,
            maxProgress: 3
          }
        ],
        recentGames: [
          {
            gameId: 'game1',
            result: 'win',
            rating: 1200,
            ratingChange: 15,
            date: Date.now() - 86400000 // Вчера
          },
          {
            gameId: 'game2',
            result: 'loss',
            rating: 1185,
            ratingChange: -15,
            date: Date.now() - 43200000 // 12 часов назад
          }
        ]
      };
    }
  }
  
  /**
   * Расчет рейтинга для подбора соперников
   * @param playerId ID игрока
   * @param gameMode Режим игры для расчета рейтинга
   * @returns Рейтинг игрока для подбора соперников
   */
  async calculateMatchmakingRating(playerId: string, gameMode: 'classic' | 'fast' | 'custom'): Promise<number> {
    try {
      // Формируем URL для запроса рейтинга
      const ratingUrl = `${this.apiUrl}/matchmaking/rating/${playerId}?mode=${gameMode}`;
      
      // Выполняем запрос на получение рейтинга
      const response = await fetch(ratingUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`Ошибка получения рейтинга: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Возвращаем рейтинг для подбора соперников
      return data.rating;
    } catch (error) {
      console.error('Ошибка при получении рейтинга для подбора соперников:', error);
      
      // В случае ошибки сети используем имитацию ответа
      // Это временное решение для демонстрации
      await this.delay(300);
      
      // Генерируем случайный рейтинг в диапазоне 1000-1500
      return 1000 + Math.floor(Math.random() * 500);
    }
  }
  
  /**
   * Применение снижения рейтинга за неактивность
   * @param playerId ID игрока
   * @param inactiveDays Количество дней неактивности
   * @returns Обновленный рейтинг игрока
   */
  async applyInactivityDecay(playerId: string, inactiveDays: number): Promise<{ oldRating: number; newRating: number }> {
    try {
      // Формируем URL для запроса снижения рейтинга
      const decayUrl = `${this.apiUrl}/players/${playerId}/rating/decay`;
      
      // Выполняем запрос на снижение рейтинга
      const response = await fetch(decayUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`
        },
        body: JSON.stringify({
          inactiveDays,
          timestamp: Date.now()
        })
      });
      
      if (!response.ok) {
        throw new Error(`Ошибка снижения рейтинга: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Возвращаем старый и новый рейтинг
      return {
        oldRating: data.oldRating,
        newRating: data.newRating
      };
    } catch (error) {
      console.error('Ошибка при снижении рейтинга за неактивность:', error);
      
      // В случае ошибки сети используем имитацию ответа
      // Это временное решение для демонстрации
      await this.delay(400);
      
      // Генерируем тестовые данные
      const oldRating = 1200;
      const decayAmount = Math.min(100, inactiveDays * 5); // 5 очков за день неактивности, максимум 100
      
      return {
        oldRating,
        newRating: oldRating - decayAmount
      };
    }
  }
  
  // Вспомогательный метод для имитации задержки сети
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  // Вспомогательный метод для повторных попыток при ошибках сети
  private async retryOperation<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error | undefined;
    
    for (let attempt = 0; attempt < this.reconnectAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // Если это не ошибка сети, не пытаемся повторить
        if (!(error instanceof NetworkError)) {
          throw error;
        }
        
        // Ждем перед следующей попыткой
        await this.delay(this.reconnectDelay);
      }
    }
    
    // Если все попытки не удались, выбрасываем последнюю ошибку
    throw lastError || new NetworkError('Превышено количество попыток подключения');
  }
}

// Создаем экземпляр сервиса с настройками по умолчанию
export const multiplayerService = new MultiplayerService({
  apiUrl: process.env.NEXT_PUBLIC_API_URL || 'https://api.example.com',
  socketUrl: process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001',
});