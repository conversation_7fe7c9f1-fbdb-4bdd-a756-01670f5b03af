import { Player, Card, GameRules, DurakVariant, PlayerAction } from '@a1-k/core';

/**
 * Типы для игровых компонентов
 */

// Тип для темы игры
export type GameTheme = 'light' | 'dark' | 'colorful' | 'monochrome';

// Тип для настроек игры
export interface GameSettings {
  // Общие настройки
  theme: GameTheme;
  soundEnabled: boolean;
  showHints: boolean;
  animationsEnabled: boolean;
  autoSuggest: boolean;
  turnTimerEnabled: boolean;
  turnTimerDuration: number;
  cardSize: 'small' | 'medium' | 'large';
  
  // Настройки игрового процесса
  initialCards?: number; // Начальное количество карт
  trumpDetermination?: 'random' | 'lastCard' | 'firstCard'; // Способ определения козыря
  allowFirstAttackWithTrump?: boolean; // Разрешить первый ход козырем
  cardTheme?: 'classic' | 'modern' | 'retro'; // Тема карт
  tableBackground?: 'green' | 'blue' | 'red' | 'dark'; // Фон стола
  showAnimations?: boolean; // Показывать анимации
  
  // Настройки звука
  musicEnabled?: boolean; // Включить музыку
  soundVolume?: number; // Громкость звуков (0-100)
  musicVolume?: number; // Громкость музыки (0-100)
  
  // Настройки многопользовательского режима
  matchmaking?: {
    skillLevel?: 'any' | 'beginner' | 'intermediate' | 'expert'; // Уровень подбора соперников
    regionPreference?: 'any' | 'local' | 'global'; // Региональные предпочтения
    maxWaitTime?: number; // Максимальное время ожидания в секундах
  };
  privacy?: {
    isPrivate?: boolean; // Приватная игра (только по приглашению)
    allowSpectators?: boolean; // Разрешить наблюдателей
    friendsOnly?: boolean; // Только для друзей
  };
  network?: {
    connectionQuality?: 'any' | 'good' | 'excellent'; // Требования к качеству соединения
    reconnectTimeout?: number; // Тайм-аут для переподключения в секундах
  };
  
  // Профиль настроек
  profileName?: string; // Имя профиля настроек
  isDefault?: boolean; // Является ли профилем по умолчанию
}

// Тип для действий игрока с дополнительными данными
export interface PlayerMoveData {
  action: PlayerAction;
  cardIndex?: number;
  playerId?: string;
  gameId?: string;
}

// Тип для уведомлений
export type NotificationType = 'attack' | 'defend' | 'take' | 'pass' | 'error' | 'sync';

export interface GameNotification {
  show: boolean;
  message: string;
  type: NotificationType;
}

// Тип для анимаций карт
export type CardAnimationType = 'attack' | 'defend' | 'take';

// Тип для состояния игры в многопользовательском режиме
export interface MultiplayerGameState {
  players: Player[];
  tableCards: Card[][];
  trumpCard: Card | null;
  trumpSuit?: string;
  currentPlayerIndex: number;
  attackerIndex: number;
  defenderIndex: number;
  gameStatus: 'not_started' | 'in_progress' | 'finished';
  error?: string;
  deck: Card[];
  gameId: string;
  rules: GameRules;
}

// Тип для пропсов компонента игры
export interface DurakGameComponentProps {
  gameState?: MultiplayerGameState;
  onMove?: (moveData: PlayerMoveData) => Promise<any>;
  isActivePlayer?: boolean;
  isSyncing?: boolean;
  onGameEnd?: (winner: Player) => void;
  onSyncError?: (error: Error) => void;
  playerName?: string; // Имя текущего игрока для многопользовательского режима
  gameId?: string; // ID игры для многопользовательского режима
  settings?: GameSettings; // Настройки игры
}

// Тип для состояния анимации карты
export interface CardAnimationState {
  cardIndex: number | null;
  type: CardAnimationType;
}

// Тип для состояния новых карт на столе
export type TableCardState = boolean[][];