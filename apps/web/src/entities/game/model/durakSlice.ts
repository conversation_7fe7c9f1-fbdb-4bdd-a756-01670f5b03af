import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { GameState, Player, Card, GameStatus, PlayerAction, DurakGame, GameRules, DurakVariant } from '@kozyr-master/core'; // Импортируем типы из пакета core

// Начальное состояние для среза
const initialState: GameState & { gameInstance: DurakGame | null; error: string | null } = {
  players: [],
  deck: [],
  tableCards: [],
  discardPile: [],
  trumpCard: undefined,
  trumpSuit: null as any, // Инициализируем как null или подходящим значением по умолчанию
  currentPlayerIndex: -1,
  attackerIndex: -1,
  defenderIndex: -1,
  gameStatus: GameStatus.NOT_STARTED,
  winner: undefined,
  passCount: 0,
  gameInstance: null, // Добавляем для хранения экземпляра игры
  error: null, // Для хранения сообщений об ошибках
};

const durakSlice = createSlice({
  name: 'durak',
  initialState,
  reducers: {
    // Редьюсер для инициализации новой игры
    initializeGame: (state, action: PayloadAction<{ players: Player[], rules: GameRules }>) => {
      try {
        const game = new DurakGame(action.payload.players, action.payload.rules);
        const gameState = game.getState();
        // Обновляем состояние Redux из состояния игры
        Object.assign(state, gameState);
        state.gameInstance = game; // Сохраняем экземпляр игры
        state.gameStatus = GameStatus.NOT_STARTED; // Устанавливаем статус
        state.error = null;
      } catch (e: any) {
        state.error = e.message || 'Failed to initialize game';
        console.error("Error initializing game:", e);
      }
    },
    // Редьюсер для старта игры
    startGame: (state) => {
      if (state.gameInstance && state.gameStatus === GameStatus.NOT_STARTED) {
        try {
          state.gameInstance.startGame();
          const newState = state.gameInstance.getState();
          Object.assign(state, newState);
          state.error = null;
        } catch (e: any) {
          state.error = e.message || 'Failed to start game';
          console.error("Error starting game:", e);
        }
      } else {
        state.error = 'Game instance not available or game already started/finished.';
      }
    },
    // Редьюсер для выполнения хода
    makeMove: (state, action: PayloadAction<{ playerId: string; action: PlayerAction; cardIndex?: number }>) => {
      if (state.gameInstance && state.gameStatus === GameStatus.IN_PROGRESS) {
        try {
          const { playerId, action: playerAction, cardIndex } = action.payload;
          const moveSuccessful = state.gameInstance.makeMove(playerId, playerAction, cardIndex);
          if (moveSuccessful) {
            const newState = state.gameInstance.getState();
            Object.assign(state, newState);
            state.error = null;
          } else {
            // Можно установить ошибку, если ход не удался, но DurakGame уже логирует ошибки
            // state.error = 'Invalid move';
            console.warn('Move was not successful according to game logic.');
          }
        } catch (e: any) {
          state.error = e.message || 'Failed to make move';
          console.error("Error making move:", e);
        }
      } else {
        state.error = 'Game instance not available or game not in progress.';
      }
    },
    // Можно добавить другие редьюсеры по мере необходимости
    setError: (state, action: PayloadAction<string | null>) => {
        state.error = action.payload;
    }
  },
});

export const { initializeGame, startGame, makeMove, setError } = durakSlice.actions;
export default durakSlice.reducer;