/**
 * Профили настроек для разных режимов игры
 */

export interface GameProfile {
  name: string;
  description: string;
  rules: {
    initialCardsCount: number;
    timePerMove: number; // в секундах
    winScore: number;
    allowTaking: boolean;
    allowDefending: boolean;
    allowPassing: boolean;
    maxPlayersCount: number;
    minPlayersCount: number;
    deckType: 'standard36' | 'standard52' | 'custom';
    trumpSelection: 'random' | 'fixed' | 'player';
    roundsToWin: number;
  };
  ui: {
    theme: 'classic' | 'modern' | 'dark';
    animations: boolean;
    sounds: boolean;
    showTimer: boolean;
    showScores: boolean;
    showDeckCount: boolean;
  };
}

/**
 * Доступные профили настроек игры
 */
export const gameProfiles: Record<string, GameProfile> = {
  classic: {
    name: 'Классический',
    description: 'Традиционные правила игры в дурака',
    rules: {
      initialCardsCount: 6,
      timePerMove: 30,
      winScore: 100,
      allowTaking: true,
      allowDefending: true,
      allowPassing: true,
      maxPlayersCount: 6,
      minPlayersCount: 2,
      deckType: 'standard36',
      trumpSelection: 'random',
      roundsToWin: 1,
    },
    ui: {
      theme: 'classic',
      animations: true,
      sounds: true,
      showTimer: true,
      showScores: true,
      showDeckCount: true,
    },
  },
  fast: {
    name: 'Быстрая игра',
    description: 'Ускоренный вариант с меньшим количеством карт и временем на ход',
    rules: {
      initialCardsCount: 4,
      timePerMove: 15,
      winScore: 50,
      allowTaking: true,
      allowDefending: true,
      allowPassing: false,
      maxPlayersCount: 4,
      minPlayersCount: 2,
      deckType: 'standard36',
      trumpSelection: 'random',
      roundsToWin: 1,
    },
    ui: {
      theme: 'modern',
      animations: true,
      sounds: true,
      showTimer: true,
      showScores: true,
      showDeckCount: true,
    },
  },
  multiplayer: {
    name: 'Многопользовательский',
    description: 'Оптимизированные настройки для игры по сети',
    rules: {
      initialCardsCount: 6,
      timePerMove: 45,
      winScore: 150,
      allowTaking: true,
      allowDefending: true,
      allowPassing: true,
      maxPlayersCount: 6,
      minPlayersCount: 2,
      deckType: 'standard36',
      trumpSelection: 'random',
      roundsToWin: 1,
    },
    ui: {
      theme: 'modern',
      animations: true,
      sounds: true,
      showTimer: true,
      showScores: true,
      showDeckCount: true,
    },
  },
  tournament: {
    name: 'Турнирный',
    description: 'Правила для соревновательной игры',
    rules: {
      initialCardsCount: 6,
      timePerMove: 60,
      winScore: 200,
      allowTaking: true,
      allowDefending: true,
      allowPassing: true,
      maxPlayersCount: 4,
      minPlayersCount: 2,
      deckType: 'standard36',
      trumpSelection: 'random',
      roundsToWin: 3,
    },
    ui: {
      theme: 'classic',
      animations: false,
      sounds: false,
      showTimer: true,
      showScores: true,
      showDeckCount: true,
    },
  },
  custom: {
    name: 'Пользовательский',
    description: 'Настраиваемые правила игры',
    rules: {
      initialCardsCount: 6,
      timePerMove: 30,
      winScore: 100,
      allowTaking: true,
      allowDefending: true,
      allowPassing: true,
      maxPlayersCount: 6,
      minPlayersCount: 2,
      deckType: 'standard36',
      trumpSelection: 'random',
      roundsToWin: 1,
    },
    ui: {
      theme: 'classic',
      animations: true,
      sounds: true,
      showTimer: true,
      showScores: true,
      showDeckCount: true,
    },
  },
};

/**
 * Получение профиля настроек по имени
 * 
 * @param profileName Имя профиля
 * @returns Профиль настроек или профиль по умолчанию
 */
export const getGameProfile = (profileName: string): GameProfile => {
  return gameProfiles[profileName] || gameProfiles.classic;
};

/**
 * Создание пользовательского профиля на основе существующего
 * 
 * @param basedOn Имя базового профиля
 * @param customSettings Пользовательские настройки
 * @returns Новый профиль настроек
 */
export const createCustomProfile = (
  basedOn: string = 'classic',
  customSettings: Partial<GameProfile>
): GameProfile => {
  const baseProfile = getGameProfile(basedOn);
  
  return {
    ...baseProfile,
    ...customSettings,
    rules: {
      ...baseProfile.rules,
      ...(customSettings.rules || {}),
    },
    ui: {
      ...baseProfile.ui,
      ...(customSettings.ui || {}),
    },
  };
};