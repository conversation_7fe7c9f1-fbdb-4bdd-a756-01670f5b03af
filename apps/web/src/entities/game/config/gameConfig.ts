import { GameSettings, GameTheme } from '../model/types';

/**
 * Конфигурация игры по умолчанию
 */

// Доступные темы игры
export const availableThemes: GameTheme[] = ['light', 'dark', 'colorful', 'monochrome'];

// Настройки игры по умолчанию
export const defaultGameSettings: GameSettings = {
  // Общие настройки
  theme: 'light',
  soundEnabled: true,
  showHints: true,
  animationsEnabled: true,
  autoSuggest: false,
  turnTimerEnabled: true,
  turnTimerDuration: 30,
  cardSize: 'medium',
  
  // Настройки игрового процесса
  initialCards: 6,
  trumpDetermination: 'lastCard',
  allowFirstAttackWithTrump: false,
  cardTheme: 'classic',
  tableBackground: 'green',
  showAnimations: true,
  
  // Настройки звука
  musicEnabled: false,
  soundVolume: 70,
  musicVolume: 50,
  
  // Настройки многопользовательского режима
  matchmaking: {
    skillLevel: 'any',
    regionPreference: 'any',
    maxWaitTime: 60
  },
  privacy: {
    isPrivate: false,
    allowSpectators: true,
    friendsOnly: false
  },
  network: {
    connectionQuality: 'any',
    reconnectTimeout: 30
  },
  
  // Профиль настроек
  profileName: 'Стандартный',
  isDefault: true
};

// Конфигурация для разных размеров карт
export const cardSizeConfig = {
  small: {
    width: 80,
    height: 112,
    scale: 0.8,
  },
  medium: {
    width: 100,
    height: 140,
    scale: 1,
  },
  large: {
    width: 120,
    height: 168,
    scale: 1.2,
  },
};

// Конфигурация для звуков игры
export const soundConfig = {
  dealCard: '/sounds/deal-card.mp3',
  playCard: '/sounds/play-card.mp3',
  takeCards: '/sounds/take-cards.mp3',
  win: '/sounds/win.mp3',
  lose: '/sounds/lose.mp3',
  error: '/sounds/error.mp3',
  notification: '/sounds/notification.mp3',
  turnStart: '/sounds/turn-start.mp3',
};

// Конфигурация для подсказок
export const hintsConfig = {
  attack: 'Выберите карту для атаки. Вы можете атаковать картой того же достоинства, что уже есть на столе.',
  defend: 'Выберите карту для защиты. Карта должна быть той же масти и старше, либо козырем.',
  take: 'Если не можете отбиться, возьмите все карты со стола.',
  pass: 'Если все карты на столе отбиты, вы можете передать ход.',
  firstMove: 'Выберите любую карту для первого хода.',
  trumpCard: 'Это козырная карта. Козыри бьют любую карту другой масти.',
};

// Конфигурация для анимаций
export const animationConfig = {
  dealCard: 0.5, // длительность в секундах
  playCard: 0.3,
  takeCards: 0.8,
  win: 1.5,
  notification: 3,
};

// Конфигурация для таймера хода
export const timerConfig = {
  warning: 15, // секунд до предупреждения
  critical: 5, // секунд до критического состояния
};