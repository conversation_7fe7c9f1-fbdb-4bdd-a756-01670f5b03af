/**
 * Хук для работы с WebSocket соединением в многопользовательском режиме
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import gameSocketService, { 
  SocketEvent, 
  RoomInfo, 
  ChatMessage 
} from '../socket/gameSocketService';
import { GameState } from '../../model/types';

export interface UseGameConnectionOptions {
  serverUrl?: string;
  autoConnect?: boolean;
}

export interface UseGameConnectionResult {
  status: 'disconnected' | 'connecting' | 'connected' | 'error';
  error: Error | null;
  currentRoom: RoomInfo | null;
  gameState: GameState | null;
  messages: ChatMessage[];
  connect: (playerId: string, sessionToken: string) => Promise<void>;
  disconnect: () => void;
  joinRoom: (roomId: string, playerName: string) => Promise<RoomInfo>;
  leaveRoom: () => Promise<void>;
  setReady: (isReady: boolean) => Promise<void>;
  startGame: () => Promise<void>;
  makeMove: (moveData: any) => Promise<GameState>;
  sendMessage: (message: string) => Promise<void>;
}

/**
 * Хук для работы с WebSocket соединением в многопользовательском режиме
 * @param options Настройки подключения
 */
export function useGameConnection(options: UseGameConnectionOptions = {}): UseGameConnectionResult {
  const { autoConnect = false } = options;
  
  // Состояние подключения
  const [status, setStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const [error, setError] = useState<Error | null>(null);
  
  // Состояние игры
  const [currentRoom, setCurrentRoom] = useState<RoomInfo | null>(null);
  const [gameState, setGameState] = useState<GameState | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  
  // Сохраняем ID игрока и токен сессии для возможного переподключения
  const playerIdRef = useRef<string | null>(null);
  const sessionTokenRef = useRef<string | null>(null);
  
  // Обработчики событий сокета
  const setupSocketListeners = useCallback(() => {
    // Подключение установлено
    gameSocketService.on(SocketEvent.CONNECT, () => {
      setStatus('connected');
      setError(null);
    });
    
    // Соединение разорвано
    gameSocketService.on(SocketEvent.DISCONNECT, () => {
      setStatus('disconnected');
    });
    
    // Ошибка соединения
    gameSocketService.on(SocketEvent.ERROR, (err: Error) => {
      setStatus('error');
      setError(err);
    });
    
    // Обновление информации о комнате
    gameSocketService.on(SocketEvent.ROOM_UPDATE, (roomInfo: RoomInfo) => {
      setCurrentRoom(roomInfo);
    });
    
    // Игрок присоединился к комнате
    gameSocketService.on(SocketEvent.PLAYER_JOINED, (data: { player: any, room: RoomInfo }) => {
      setCurrentRoom(data.room);
      
      // Добавляем системное сообщение в чат
      setMessages(prev => [
        ...prev,
        {
          id: `system_${Date.now()}`,
          playerId: 'system',
          playerName: 'Система',
          message: `Игрок ${data.player.name} присоединился к комнате`,
          timestamp: Date.now(),
          isSystem: true
        }
      ]);
    });
    
    // Игрок покинул комнату
    gameSocketService.on(SocketEvent.PLAYER_LEFT, (data: { playerId: string, playerName: string, room: RoomInfo }) => {
      setCurrentRoom(data.room);
      
      // Добавляем системное сообщение в чат
      setMessages(prev => [
        ...prev,
        {
          id: `system_${Date.now()}`,
          playerId: 'system',
          playerName: 'Система',
          message: `Игрок ${data.playerName} покинул комнату`,
          timestamp: Date.now(),
          isSystem: true
        }
      ]);
    });
    
    // Игра началась
    gameSocketService.on(SocketEvent.GAME_START, (data: { gameState: GameState, room: RoomInfo }) => {
      setGameState(data.gameState);
      setCurrentRoom(data.room);
      
      // Добавляем системное сообщение в чат
      setMessages(prev => [
        ...prev,
        {
          id: `system_${Date.now()}`,
          playerId: 'system',
          playerName: 'Система',
          message: 'Игра началась!',
          timestamp: Date.now(),
          isSystem: true
        }
      ]);
    });
    
    // Обновление состояния игры
    gameSocketService.on(SocketEvent.GAME_UPDATE, (data: { gameState: GameState }) => {
      setGameState(data.gameState);
    });
    
    // Игра завершена
    gameSocketService.on(SocketEvent.GAME_END, (data: { gameState: GameState, room: RoomInfo, winner: string }) => {
      setGameState(data.gameState);
      setCurrentRoom(data.room);
      
      // Добавляем системное сообщение в чат
      setMessages(prev => [
        ...prev,
        {
          id: `system_${Date.now()}`,
          playerId: 'system',
          playerName: 'Система',
          message: `Игра завершена! Победитель: ${data.winner}`,
          timestamp: Date.now(),
          isSystem: true
        }
      ]);
    });
    
    // Новое сообщение в чате
    gameSocketService.on(SocketEvent.CHAT_MESSAGE, (message: ChatMessage) => {
      setMessages(prev => [...prev, message]);
    });
  }, []);
  
  // Удаление обработчиков событий
  const removeSocketListeners = useCallback(() => {
    gameSocketService.removeAllListeners();
  }, []);
  
  // Подключение к серверу
  const connect = useCallback(async (playerId: string, sessionToken: string) => {
    try {
      setStatus('connecting');
      setError(null);
      
      // Сохраняем данные для возможного переподключения
      playerIdRef.current = playerId;
      sessionTokenRef.current = sessionToken;
      
      await gameSocketService.connect(playerId, sessionToken);
    } catch (err) {
      setStatus('error');
      setError(err instanceof Error ? err : new Error('Ошибка подключения'));
      throw err;
    }
  }, []);
  
  // Отключение от сервера
  const disconnect = useCallback(() => {
    gameSocketService.disconnect();
    setStatus('disconnected');
    setCurrentRoom(null);
    setGameState(null);
    playerIdRef.current = null;
    sessionTokenRef.current = null;
  }, []);
  
  // Присоединение к комнате
  const joinRoom = useCallback(async (roomId: string, playerName: string) => {
    try {
      const roomInfo = await gameSocketService.joinRoom(roomId, playerName);
      setCurrentRoom(roomInfo);
      setMessages([]); // Очищаем историю сообщений при входе в новую комнату
      return roomInfo;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Ошибка при присоединении к комнате'));
      throw err;
    }
  }, []);
  
  // Выход из комнаты
  const leaveRoom = useCallback(async () => {
    try {
      await gameSocketService.leaveRoom();
      setCurrentRoom(null);
      setGameState(null);
      setMessages([]);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Ошибка при выходе из комнаты'));
      throw err;
    }
  }, []);
  
  // Установка статуса готовности
  const setReady = useCallback(async (isReady: boolean) => {
    try {
      await gameSocketService.setReady(isReady);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Ошибка при изменении статуса готовности'));
      throw err;
    }
  }, []);
  
  // Запуск игры
  const startGame = useCallback(async () => {
    try {
      await gameSocketService.startGame();
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Ошибка при запуске игры'));
      throw err;
    }
  }, []);
  
  // Выполнение хода
  const makeMove = useCallback(async (moveData: any) => {
    try {
      const updatedGameState = await gameSocketService.makeMove(moveData);
      return updatedGameState;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Ошибка при выполнении хода'));
      throw err;
    }
  }, []);
  
  // Отправка сообщения в чат
  const sendMessage = useCallback(async (message: string) => {
    try {
      await gameSocketService.sendChatMessage(message);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Ошибка при отправке сообщения'));
      throw err;
    }
  }, []);
  
  // Устанавливаем обработчики событий при монтировании компонента
  useEffect(() => {
    setupSocketListeners();
    
    // Автоматическое подключение, если включено
    if (autoConnect && playerIdRef.current && sessionTokenRef.current) {
      connect(playerIdRef.current, sessionTokenRef.current).catch(() => {});
    }
    
    // Удаляем обработчики при размонтировании компонента
    return () => {
      removeSocketListeners();
    };
  }, [setupSocketListeners, removeSocketListeners, autoConnect, connect]);
  
  return {
    status,
    error,
    currentRoom,
    gameState,
    messages,
    connect,
    disconnect,
    joinRoom,
    leaveRoom,
    setReady,
    startGame,
    makeMove,
    sendMessage
  };
}