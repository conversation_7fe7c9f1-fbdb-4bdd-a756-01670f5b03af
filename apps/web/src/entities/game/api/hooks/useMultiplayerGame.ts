/**
 * Хук для управления многопользовательской игрой
 * Предоставляет удобный API для работы с многопользовательским режимом
 */

import { useState, useEffect, useCallback } from 'react';
import { useGameConnection, UseGameConnectionOptions } from './useGameConnection';
import { GameState, PlayerMoveData } from '../../model/types';
import { Player } from '@a1-k/core';

export interface UseMultiplayerGameOptions extends UseGameConnectionOptions {
  roomId?: string;
  playerName?: string;
}

export interface UseMultiplayerGameResult {
  // Состояние подключения
  status: 'disconnected' | 'connecting' | 'connected' | 'error';
  error: Error | null;
  
  // Состояние игры
  currentRoom: any | null;
  gameState: GameState | null;
  players: Player[];
  availableGames: Array<{
    id: string;
    name: string;
    players: number;
    maxPlayers: number;
    status: 'waiting' | 'in_progress' | 'finished';
  }>;
  chatMessages: Array<{
    id: string;
    sender: string;
    senderId: string;
    text: string;
    timestamp: number;
    isSystem?: boolean;
  }>;
  
  // Флаги состояния
  isLoading: boolean;
  isSyncing: boolean;
  syncError: Error | null;
  
  // Методы для управления подключением
  connect: (playerId: string, sessionToken: string) => Promise<void>;
  disconnect: () => void;
  
  // Методы для управления комнатой
  joinRoom: (roomId: string, playerName: string) => Promise<any>;
  leaveRoom: () => Promise<void>;
  createRoom: (roomName: string, maxPlayers?: number) => Promise<any>;
  setReady: (isReady: boolean) => Promise<void>;
  startGame: () => Promise<void>;
  
  // Методы для игрового процесса
  makeMove: (moveData: PlayerMoveData) => Promise<GameState>;
  sendMessage: (message: string) => Promise<void>;
  
  // Вспомогательные методы
  isActivePlayer: () => boolean;
  isHost: boolean;
  allPlayersReady: boolean;
  enoughPlayers: boolean;
  fetchAvailableGames: () => Promise<void>;
}

/**
 * Хук для управления многопользовательской игрой
 * @param options Настройки подключения и игры
 */
export function useMultiplayerGame(options: UseMultiplayerGameOptions = {}): UseMultiplayerGameResult {
  const {
    serverUrl,
    autoConnect = true,
    roomId,
    playerName = `Игрок-${Math.floor(Math.random() * 1000)}`
  } = options;
  
  // Используем базовый хук для подключения к игровому серверу
  const gameConnection = useGameConnection({
    serverUrl,
    autoConnect: false // Управляем подключением вручную
  });
  
  // Состояние для отслеживания загрузки и синхронизации
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSyncing, setIsSyncing] = useState<boolean>(false);
  const [syncError, setSyncError] = useState<Error | null>(null);
  
  // Состояние для списка доступных игр
  const [availableGames, setAvailableGames] = useState<Array<{
    id: string;
    name: string;
    players: number;
    maxPlayers: number;
    status: 'waiting' | 'in_progress' | 'finished';
  }>>([]);
  
  // Преобразуем сообщения из формата сервера в формат UI
  const chatMessages = gameConnection.messages.map(msg => ({
    id: msg.id,
    sender: msg.playerName,
    senderId: msg.playerId,
    text: msg.message,
    timestamp: msg.timestamp,
    isSystem: msg.isSystem
  }));
  
  // Преобразуем игроков из формата комнаты в формат игры
  const players: Player[] = gameConnection.currentRoom?.players.map((player: any) => ({
    id: player.id,
    name: player.name,
    hand: [], // Данные о картах приходят в gameState
    isActive: gameConnection.gameState?.currentPlayerId === player.id
  })) || [];
  
  // Подключение к серверу
  const connect = useCallback(async (playerId: string, sessionToken: string) => {
    setIsLoading(true);
    try {
      await gameConnection.connect(playerId, sessionToken);
      
      // Если указан ID комнаты, присоединяемся к ней
      if (roomId) {
        await gameConnection.joinRoom(roomId, playerName);
      }
      
      // Загружаем список доступных игр
      await fetchAvailableGames();
    } catch (err) {
      console.error('Ошибка при подключении:', err);
    } finally {
      setIsLoading(false);
    }
  }, [roomId, playerName, gameConnection]);
  
  // Создание новой комнаты
  const createRoom = useCallback(async (roomName: string, maxPlayers: number = 4) => {
    setIsLoading(true);
    try {
      // В реальном приложении здесь будет запрос к API для создания комнаты
      // Сейчас используем заглушку
      const newRoomId = `room_${Date.now()}`;
      const roomInfo = await gameConnection.joinRoom(newRoomId, playerName);
      return roomInfo;
    } catch (err) {
      console.error('Ошибка при создании комнаты:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [playerName, gameConnection]);
  
  // Получение списка доступных игр
  const fetchAvailableGames = useCallback(async () => {
    setIsLoading(true);
    try {
      // В реальном приложении здесь будет запрос к API для получения списка игр
      // Сейчас используем заглушку
      setAvailableGames([
        {
          id: 'game1',
          name: 'Игра #1',
          players: 1,
          maxPlayers: 4,
          status: 'waiting',
        },
        {
          id: 'game2',
          name: 'Игра #2',
          players: 2,
          maxPlayers: 4,
          status: 'in_progress',
        },
      ]);
    } catch (err) {
      console.error('Ошибка при получении списка игр:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  // Обработчик для синхронизации хода с другими игроками
  const makeMove = useCallback(async (moveData: PlayerMoveData) => {
    if (!gameConnection.currentRoom || gameConnection.currentRoom.status !== 'playing') {
      return Promise.reject(new Error('Игра не активна'));
    }
    
    setIsSyncing(true);
    setSyncError(null);
    
    try {
      const result = await gameConnection.makeMove(moveData);
      return result;
    } catch (err) {
      setSyncError(err instanceof Error ? err : new Error('Ошибка синхронизации'));
      throw err;
    } finally {
      setIsSyncing(false);
    }
  }, [gameConnection]);
  
  // Проверка, является ли текущий игрок активным (его ход)
  const isActivePlayer = useCallback(() => {
    if (!gameConnection.gameState || !gameConnection.currentRoom) return false;
    
    return gameConnection.gameState.currentPlayerId === gameConnection.currentRoom.playerId;
  }, [gameConnection.gameState, gameConnection.currentRoom]);
  
  // Вспомогательные флаги для управления игрой
  const isHost = gameConnection.currentRoom?.host === gameConnection.currentRoom?.playerId;
  const allPlayersReady = gameConnection.currentRoom?.players.every((p: any) => p.isReady);
  const enoughPlayers = (gameConnection.currentRoom?.players.length || 0) >= 2;
  
  // Автоматическое подключение при монтировании компонента
  useEffect(() => {
    if (autoConnect) {
      // В реальном приложении здесь будет получение ID игрока и токена из авторизации
      const playerId = `player-${Date.now()}`;
      const sessionToken = 'guest-session';
      
      connect(playerId, sessionToken).catch(console.error);
    }
    
    return () => {
      // Отключаемся при размонтировании компонента
      if (gameConnection.status === 'connected') {
        gameConnection.disconnect();
      }
    };
  }, [autoConnect, connect, gameConnection]);
  
  return {
    // Состояние подключения
    status: gameConnection.status,
    error: gameConnection.error,
    
    // Состояние игры
    currentRoom: gameConnection.currentRoom,
    gameState: gameConnection.gameState,
    players,
    availableGames,
    chatMessages,
    
    // Флаги состояния
    isLoading,
    isSyncing,
    syncError,
    
    // Методы для управления подключением
    connect,
    disconnect: gameConnection.disconnect,
    
    // Методы для управления комнатой
    joinRoom: gameConnection.joinRoom,
    leaveRoom: gameConnection.leaveRoom,
    createRoom,
    setReady: gameConnection.setReady,
    startGame: gameConnection.startGame,
    
    // Методы для игрового процесса
    makeMove,
    sendMessage: gameConnection.sendMessage,
    
    // Вспомогательные методы
    isActivePlayer,
    isHost,
    allPlayersReady,
    enoughPlayers,
    fetchAvailableGames
  };
}