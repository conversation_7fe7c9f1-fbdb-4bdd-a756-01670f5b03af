/**
 * Сервис для работы с WebSocket соединением в многопользовательском режиме
 */

import { EventEmitter } from 'events';
import { Player, Card, PlayerAction } from '@a1-k/core';
import { GameState, GameSettings } from '../../model/types';

// Типы событий сокета
export enum SocketEvent {
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  ERROR = 'error',
  JOIN_ROOM = 'join_room',
  LEAVE_ROOM = 'leave_room',
  ROOM_JOINED = 'room_joined',
  ROOM_LEFT = 'room_left',
  ROOM_UPDATE = 'room_update',
  PLAYER_JOINED = 'player_joined',
  PLAYER_LEFT = 'player_left',
  PLAYER_READY = 'player_ready',
  GAME_START = 'game_start',
  GAME_UPDATE = 'game_update',
  GAME_END = 'game_end',
  PLAYER_MOVE = 'player_move',
  CHAT_MESSAGE = 'chat_message',
}

// Типы сообщений
export interface ChatMessage {
  id: string;
  playerId: string;
  playerName: string;
  message: string;
  timestamp: number;
  isSystem?: boolean;
}

export interface RoomInfo {
  id: string;
  name: string;
  host: string;
  players: RoomPlayer[];
  maxPlayers: number;
  status: 'waiting' | 'playing' | 'finished';
  gameSettings: GameSettings;
  createdAt: number;
  playerId?: string; // ID текущего игрока в комнате
}

export interface RoomPlayer {
  id: string;
  name: string;
  isReady: boolean;
  isHost: boolean;
  joinedAt: number;
}

export interface GameSocketOptions {
  url: string;
  autoReconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

/**
 * Класс для работы с WebSocket соединением в многопользовательском режиме
 */
export class GameSocketService extends EventEmitter {
  private socket: WebSocket | null = null;
  private url: string;
  private autoReconnect: boolean;
  private reconnectInterval: number;
  private maxReconnectAttempts: number;
  private reconnectAttempts: number = 0;
  private isConnecting: boolean = false;
  private playerId: string | null = null;
  private sessionToken: string | null = null;
  private currentRoomId: string | null = null;
  
  constructor(options: GameSocketOptions) {
    super();
    this.url = options.url;
    this.autoReconnect = options.autoReconnect ?? true;
    this.reconnectInterval = options.reconnectInterval ?? 3000;
    this.maxReconnectAttempts = options.maxReconnectAttempts ?? 5;
  }
  
  /**
   * Подключение к серверу
   * @param playerId ID игрока
   * @param sessionToken Токен сессии
   */
  public connect(playerId: string, sessionToken: string): Promise<void> {
    if (this.socket && (this.socket.readyState === WebSocket.OPEN || this.socket.readyState === WebSocket.CONNECTING)) {
      return Promise.resolve();
    }
    
    this.playerId = playerId;
    this.sessionToken = sessionToken;
    this.isConnecting = true;
    
    return new Promise((resolve, reject) => {
      try {
        // Формируем URL с параметрами авторизации
        const connectionUrl = `${this.url}?playerId=${playerId}&token=${sessionToken}`;
        this.socket = new WebSocket(connectionUrl);
        
        // Обработчики событий сокета
        this.socket.onopen = () => {
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.emit(SocketEvent.CONNECT);
          resolve();
        };
        
        this.socket.onclose = (event) => {
          this.handleDisconnect(event);
        };
        
        this.socket.onerror = (error) => {
          this.emit(SocketEvent.ERROR, new Error('Ошибка WebSocket соединения'));
          if (this.isConnecting) {
            reject(new Error('Не удалось установить соединение'));
            this.isConnecting = false;
          }
        };
        
        this.socket.onmessage = (event) => {
          this.handleMessage(event);
        };
      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }
  
  /**
   * Отключение от сервера
   */
  public disconnect(): void {
    if (!this.socket) return;
    
    this.autoReconnect = false; // Отключаем автоматическое переподключение
    this.socket.close();
    this.socket = null;
    this.currentRoomId = null;
  }
  
  /**
   * Присоединение к комнате
   * @param roomId ID комнаты
   * @param playerName Имя игрока
   */
  public joinRoom(roomId: string, playerName: string): Promise<RoomInfo> {
    return this.sendRequest(SocketEvent.JOIN_ROOM, { roomId, playerName });
  }
  
  /**
   * Выход из комнаты
   */
  public leaveRoom(): Promise<void> {
    if (!this.currentRoomId) {
      return Promise.resolve();
    }
    
    return this.sendRequest(SocketEvent.LEAVE_ROOM, { roomId: this.currentRoomId })
      .then(() => {
        this.currentRoomId = null;
      });
  }
  
  /**
   * Установка статуса готовности игрока
   * @param isReady Статус готовности
   */
  public setReady(isReady: boolean): Promise<void> {
    if (!this.currentRoomId) {
      return Promise.reject(new Error('Вы не находитесь в комнате'));
    }
    
    return this.sendRequest(SocketEvent.PLAYER_READY, { 
      roomId: this.currentRoomId,
      isReady 
    });
  }
  
  /**
   * Запуск игры (только для хоста)
   */
  public startGame(): Promise<void> {
    if (!this.currentRoomId) {
      return Promise.reject(new Error('Вы не находитесь в комнате'));
    }
    
    return this.sendRequest(SocketEvent.GAME_START, { roomId: this.currentRoomId });
  }
  
  /**
   * Выполнение хода
   * @param moveData Данные хода
   */
  public makeMove(moveData: any): Promise<GameState> {
    if (!this.currentRoomId) {
      return Promise.reject(new Error('Вы не находитесь в комнате'));
    }
    
    return this.sendRequest(SocketEvent.PLAYER_MOVE, {
      roomId: this.currentRoomId,
      ...moveData
    });
  }
  
  /**
   * Отправка сообщения в чат
   * @param message Текст сообщения
   */
  public sendChatMessage(message: string): Promise<void> {
    if (!this.currentRoomId) {
      return Promise.reject(new Error('Вы не находитесь в комнате'));
    }
    
    return this.sendRequest(SocketEvent.CHAT_MESSAGE, {
      roomId: this.currentRoomId,
      message
    });
  }
  
  /**
   * Отправка запроса на сервер
   * @param event Тип события
   * @param data Данные запроса
   */
  private sendRequest<T>(event: SocketEvent, data: any): Promise<T> {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      // Если сокет закрыт, но автоматическое переподключение включено, пытаемся переподключиться
      if (this.autoReconnect && this.playerId && this.sessionToken && 
          this.reconnectAttempts < this.maxReconnectAttempts) {
        return new Promise((resolve, reject) => {
          this.connect(this.playerId!, this.sessionToken!)
            .then(() => {
              // После успешного переподключения повторяем запрос
              this.sendRequest<T>(event, data)
                .then(resolve)
                .catch(reject);
            })
            .catch(error => {
              reject(new Error(`Не удалось переподключиться: ${error.message}`));
            });
        });
      }
      return Promise.reject(new Error('Нет соединения с сервером'));
    }
    
    return new Promise((resolve, reject) => {
      try {
        // Генерируем уникальный ID запроса
        const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // Устанавливаем таймаут для запроса
        const timeout = setTimeout(() => {
          this.removeAllListeners(`response_${requestId}`);
          reject(new Error('Превышено время ожидания ответа от сервера'));
        }, 10000);
        
        // Ожидаем ответ от сервера
        this.once(`response_${requestId}`, (response: any) => {
          clearTimeout(timeout);
          
          if (!response) {
            reject(new Error('Получен пустой ответ от сервера'));
            return;
          }
          
          if (response.error) {
            reject(new Error(response.error));
          } else {
            // Если это ответ на присоединение к комнате, сохраняем ID текущей комнаты
            if (event === SocketEvent.JOIN_ROOM && response.id) {
              this.currentRoomId = response.id;
            }
            
            resolve(response as T);
          }
        });
        
        // Отправляем запрос на сервер
        const message = JSON.stringify({
          event,
          requestId,
          data
        });
        
        this.socket.send(message);
      } catch (error) {
        reject(new Error(`Ошибка при отправке запроса: ${error instanceof Error ? error.message : String(error)}`));
      }
    });
  }
  
  /**
   * Обработка отключения от сервера
   */
  private handleDisconnect(event: CloseEvent): void {
    this.emit(SocketEvent.DISCONNECT, event);
    
    // Если включено автоматическое переподключение и не превышено максимальное количество попыток
    if (this.autoReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      
      console.log(`Попытка переподключения ${this.reconnectAttempts} из ${this.maxReconnectAttempts}`);
      
      setTimeout(() => {
        if (this.playerId && this.sessionToken) {
          // Сохраняем текущий ID комнаты перед переподключением
          const roomIdToReconnect = this.currentRoomId;
          
          this.connect(this.playerId, this.sessionToken)
            .then(() => {
              // Если было активное подключение к комнате, пытаемся переподключиться
              if (roomIdToReconnect) {
                // Используем последнее известное имя игрока или значение по умолчанию
                const playerName = 'Reconnected Player';
                
                this.joinRoom(roomIdToReconnect, playerName)
                  .then(roomInfo => {
                    console.log(`Успешное переподключение к комнате: ${roomInfo.name}`);
                  })
                  .catch(error => {
                    console.error(`Ошибка при переподключении к комнате: ${error.message}`);
                    this.currentRoomId = null;
                  });
              }
            })
            .catch(error => {
              console.error(`Ошибка при переподключении: ${error.message}`);
              // Если не удалось переподключиться, увеличиваем задержку для следующей попытки
              this.reconnectInterval = Math.min(this.reconnectInterval * 1.5, 30000); // Максимум 30 секунд
            });
        }
      }, this.reconnectInterval);
    } else if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.emit(SocketEvent.ERROR, new Error('Превышено максимальное количество попыток переподключения'));
      // Сбрасываем состояние комнаты
      this.currentRoomId = null;
    }
  }
  
  /**
   * Обработка входящих сообщений
   */
  private handleMessage(event: MessageEvent): void {
    if (!event || !event.data) {
      console.error('Получено пустое сообщение');
      return;
    }
    
    try {
      const message = JSON.parse(event.data);
      
      // Проверяем, что сообщение является объектом
      if (!message || typeof message !== 'object') {
        console.error('Получено некорректное сообщение:', event.data);
        return;
      }
      
      // Если это ответ на запрос
      if (message.requestId) {
        this.emit(`response_${message.requestId}`, message.data || {});
        return;
      }
      
      // Если это событие от сервера
      if (message.event) {
        this.emit(message.event, message.data || {});
      }
    } catch (error) {
      console.error('Ошибка при обработке сообщения:', error);
      // Логируем проблемное сообщение для отладки
      try {
        console.error('Содержимое проблемного сообщения:', event.data);
      } catch (e) {
        console.error('Не удалось вывести содержимое сообщения');
      }
    }
  }
}

// Создаем и экспортируем экземпляр сервиса
const gameSocketService = new GameSocketService({
  // В реальном приложении здесь будет URL WebSocket сервера
  url: 'ws://localhost:3001',
  autoReconnect: true,
});

export default gameSocketService;