/**
 * Экспорт API для многопользовательского режима
 */

// Сервисы
export { default as gameSocketService } from './socket/gameSocketService';
export { SocketEvent } from './socket/gameSocketService';

// Хуки
export { useGameConnection } from './hooks/useGameConnection';
export { useMultiplayerGame } from './hooks/useMultiplayerGame';

// Типы
export type { 
  RoomInfo,
  ChatMessage,
  GameSocketOptions 
} from './socket/gameSocketService';

export type {
  UseGameConnectionOptions,
  UseGameConnectionResult
} from './hooks/useGameConnection';

export type {
  UseMultiplayerGameOptions,
  UseMultiplayerGameResult
} from './hooks/useMultiplayerGame';