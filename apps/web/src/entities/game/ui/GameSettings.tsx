import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { GameSettings as GameSettingsType, GameTheme } from '../model/types';
import { defaultGameSettings, availableThemes, cardSizeConfig } from '../config/gameConfig';
import { colors, mixins } from '@/shared/ui/colors';

interface GameSettingsProps {
  settings?: GameSettingsType;
  onSettingsChange?: (settings: GameSettingsType) => void;
  isVisible: boolean;
  onClose: () => void;
}

const SettingsOverlay = styled.div<{ isVisible: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: ${props => props.isVisible ? 'flex' : 'none'};
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const SettingsPanel = styled.div<{ theme: GameTheme }>`
  background-color: ${props => colors.backgrounds[props.theme]};
  color: ${props => colors.text[props.theme]};
  border-radius: 8px;
  padding: 20px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  ${mixins.uiShadow}
`;

const SettingsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid ${colors.secondary};
  padding-bottom: 10px;
`;

const SettingsTitle = styled.h2`
  margin: 0;
  font-size: 1.5rem;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: ${colors.secondary};
  
  &:hover {
    color: ${colors.danger};
  }
`;

const SettingsSection = styled.div`
  margin-bottom: 20px;
`;

const SectionTitle = styled.h3`
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.2rem;
  color: ${colors.primary};
`;

const SettingRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
`;

const SettingLabel = styled.label`
  font-weight: 500;
`;

const ToggleSwitch = styled.label`
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  
  input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  span {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
    
    &:before {
      position: absolute;
      content: "";
      height: 16px;
      width: 16px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
  }
  
  input:checked + span {
    background-color: ${colors.primary};
  }
  
  input:checked + span:before {
    transform: translateX(26px);
  }
`;

const SelectWrapper = styled.div`
  position: relative;
  
  select {
    appearance: none;
    padding: 8px 30px 8px 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
    font-size: 1rem;
    
    &:focus {
      outline: none;
      border-color: ${colors.primary};
    }
  }
  
  &:after {
    content: '▼';
    font-size: 0.8rem;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
  }
`;

const RangeSlider = styled.input`
  width: 100%;
  margin: 10px 0;
  -webkit-appearance: none;
  height: 8px;
  border-radius: 4px;
  background: #d3d3d3;
  outline: none;
  
  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: ${colors.primary};
    cursor: pointer;
  }
  
  &::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: ${colors.primary};
    cursor: pointer;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
`;

const Button = styled.button<{ variant?: 'primary' | 'secondary' }>`
  ${props => mixins.buttonStyle(props.variant === 'primary' ? colors.primary : colors.secondary)}
  padding: 8px 15px;
`;

const CardSizePreview = styled.div<{ size: 'small' | 'medium' | 'large' }>`
  width: ${props => cardSizeConfig[props.size].width}px;
  height: ${props => cardSizeConfig[props.size].height}px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: ${props => props.size === 'small' ? '1rem' : props.size === 'medium' ? '1.2rem' : '1.5rem'};
  color: red;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  margin: 0 auto;
`;

export const GameSettings: React.FC<GameSettingsProps> = ({
  settings,
  onSettingsChange,
  isVisible,
  onClose
}) => {
  // Используем переданные настройки или настройки по умолчанию
  const [currentSettings, setCurrentSettings] = useState<GameSettingsType>(
    settings || defaultGameSettings
  );
  
  // Обновляем локальные настройки при изменении пропсов
  useEffect(() => {
    if (settings) {
      setCurrentSettings(settings);
    }
  }, [settings]);
  
  // Обработчики изменения настроек
  const handleToggleSetting = (key: keyof GameSettingsType) => {
    const newSettings = {
      ...currentSettings,
      [key]: !currentSettings[key]
    };
    setCurrentSettings(newSettings);
  };
  
  const handleSelectChange = (key: keyof GameSettingsType, value: any) => {
    const newSettings = {
      ...currentSettings,
      [key]: value
    };
    setCurrentSettings(newSettings);
  };
  
  const handleRangeChange = (key: keyof GameSettingsType, value: number) => {
    const newSettings = {
      ...currentSettings,
      [key]: value
    };
    setCurrentSettings(newSettings);
  };
  
  // Сохранение настроек
  const handleSave = () => {
    if (onSettingsChange) {
      onSettingsChange(currentSettings);
    }
    onClose();
  };
  
  // Сброс настроек
  const handleReset = () => {
    setCurrentSettings(defaultGameSettings);
  };
  
  return (
    <SettingsOverlay isVisible={isVisible} onClick={onClose}>
      <SettingsPanel 
        theme={currentSettings.theme} 
        onClick={(e) => e.stopPropagation()}
      >
        <SettingsHeader>
          <SettingsTitle>Настройки игры</SettingsTitle>
          <CloseButton onClick={onClose}>×</CloseButton>
        </SettingsHeader>
        
        <SettingsSection>
          <SectionTitle>Внешний вид</SectionTitle>
          
          <SettingRow>
            <SettingLabel>Тема</SettingLabel>
            <SelectWrapper>
              <select 
                value={currentSettings.theme}
                onChange={(e) => handleSelectChange('theme', e.target.value as GameTheme)}
              >
                {availableThemes.map(theme => (
                  <option key={theme} value={theme}>
                    {theme === 'light' ? 'Светлая' :
                     theme === 'dark' ? 'Темная' :
                     theme === 'colorful' ? 'Цветная' : 'Монохромная'}
                  </option>
                ))}
              </select>
            </SelectWrapper>
          </SettingRow>
          
          <SettingRow>
            <SettingLabel>Размер карт</SettingLabel>
            <SelectWrapper>
              <select 
                value={currentSettings.cardSize}
                onChange={(e) => handleSelectChange('cardSize', e.target.value as 'small' | 'medium' | 'large')}
              >
                <option value="small">Маленький</option>
                <option value="medium">Средний</option>
                <option value="large">Большой</option>
              </select>
            </SelectWrapper>
          </SettingRow>
          
          <CardSizePreview size={currentSettings.cardSize}>♥</CardSizePreview>
        </SettingsSection>
        
        <SettingsSection>
          <SectionTitle>Игровой процесс</SectionTitle>
          
          <SettingRow>
            <SettingLabel>Звуковые эффекты</SettingLabel>
            <ToggleSwitch>
              <input 
                type="checkbox" 
                checked={currentSettings.soundEnabled}
                onChange={() => handleToggleSetting('soundEnabled')}
              />
              <span></span>
            </ToggleSwitch>
          </SettingRow>
          
          <SettingRow>
            <SettingLabel>Анимации</SettingLabel>
            <ToggleSwitch>
              <input 
                type="checkbox" 
                checked={currentSettings.animationsEnabled}
                onChange={() => handleToggleSetting('animationsEnabled')}
              />
              <span></span>
            </ToggleSwitch>
          </SettingRow>
          
          <SettingRow>
            <SettingLabel>Подсказки</SettingLabel>
            <ToggleSwitch>
              <input 
                type="checkbox" 
                checked={currentSettings.showHints}
                onChange={() => handleToggleSetting('showHints')}
              />
              <span></span>
            </ToggleSwitch>
          </SettingRow>
          
          <SettingRow>
            <SettingLabel>Автоподсказка хода</SettingLabel>
            <ToggleSwitch>
              <input 
                type="checkbox" 
                checked={currentSettings.autoSuggest}
                onChange={() => handleToggleSetting('autoSuggest')}
              />
              <span></span>
            </ToggleSwitch>
          </SettingRow>
        </SettingsSection>
        
        <SettingsSection>
          <SectionTitle>Таймер хода</SectionTitle>
          
          <SettingRow>
            <SettingLabel>Включить таймер</SettingLabel>
            <ToggleSwitch>
              <input 
                type="checkbox" 
                checked={currentSettings.turnTimerEnabled}
                onChange={() => handleToggleSetting('turnTimerEnabled')}
              />
              <span></span>
            </ToggleSwitch>
          </SettingRow>
          
          <SettingRow>
            <SettingLabel>Длительность хода: {currentSettings.turnTimerDuration} сек</SettingLabel>
            <div style={{ width: '100%', maxWidth: '200px' }}>
              <RangeSlider 
                type="range" 
                min="10" 
                max="60" 
                step="5"
                value={currentSettings.turnTimerDuration}
                onChange={(e) => handleRangeChange('turnTimerDuration', parseInt(e.target.value))}
                disabled={!currentSettings.turnTimerEnabled}
              />
            </div>
          </SettingRow>
        </SettingsSection>
        
        <ButtonGroup>
          <Button onClick={handleReset}>Сбросить</Button>
          <Button variant="primary" onClick={handleSave}>Сохранить</Button>
        </ButtonGroup>
      </SettingsPanel>
    </SettingsOverlay>
  );
};