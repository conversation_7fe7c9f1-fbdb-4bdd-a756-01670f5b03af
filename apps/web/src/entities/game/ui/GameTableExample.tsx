import React, { useState } from "react";
import styled from "styled-components";
import { GameTable } from "./GameTable";
import { CardTheme, CardEffect, CardLayout } from "../../card/model/types";

// Компонент для демонстрации возможностей GameTable
export const GameTableExample: React.FC = () => {
  // Состояния для настроек игрового стола
  const [selectedDeckType, setSelectedDeckType] =
    useState<CardTheme>("classic");
  const [selectedTheme, setSelectedTheme] = useState<
    "light" | "dark" | "colorful" | "monochrome"
  >("dark");
  const [selectedEffect, setSelectedEffect] = useState<CardEffect>("none");
  const [selectedLayout, setSelectedLayout] = useState<CardLayout>("fan");
  const [enableDragDrop, setEnableDragDrop] = useState<boolean>(true);
  const [showHints, setShowHints] = useState<boolean>(true);
  const [enableSound, setEnableSound] = useState<boolean>(true);
  const [autoPlay, setAutoPlay] = useState<boolean>(true);
  const [players, setPlayers] = useState<number>(2);

  // Типы колод (темы)
  const deckTypes = [
    { id: "classic" as CardTheme, label: "Классическая" },
    { id: "modern" as CardTheme, label: "Современная" },
    { id: "minimal" as CardTheme, label: "Минималистичная" },
    { id: "fantasy" as CardTheme, label: "Фэнтези" },
  ];

  // Темы оформления
  const themes = [
    {
      id: "light" as "light" | "dark" | "colorful" | "monochrome",
      label: "Светлая",
    },
    {
      id: "dark" as "light" | "dark" | "colorful" | "monochrome",
      label: "Темная",
    },
    {
      id: "colorful" as "light" | "dark" | "colorful" | "monochrome",
      label: "Цветная",
    },
    {
      id: "monochrome" as "light" | "dark" | "colorful" | "monochrome",
      label: "Монохромная",
    },
  ];

  // Специальные эффекты
  const effects = [
    { id: "none" as CardEffect, label: "Без эффекта" },
    { id: "holographic" as CardEffect, label: "Голографический" },
    { id: "metallic" as CardEffect, label: "Металлический" },
    { id: "neon" as CardEffect, label: "Неоновый" },
    { id: "shadow" as CardEffect, label: "Тень" },
  ];

  // Варианты расположения
  const layouts = [
    { id: "grid" as CardLayout, label: "Сетка" },
    { id: "fan" as CardLayout, label: "Веер" },
    { id: "stack" as CardLayout, label: "Стопка" },
    { id: "circle" as CardLayout, label: "Круг" },
  ];

  // Количество игроков
  const playerOptions = [
    { value: 2, label: "2 игрока" },
    { value: 3, label: "3 игрока" },
    { value: 4, label: "4 игрока" },
  ];

  return (
    <ExampleContainer>
      <ExampleHeader>Демонстрация игрового стола</ExampleHeader>

      <ControlPanel>
        <ControlSection>
          <SectionTitle>Тип колоды</SectionTitle>
          <ButtonGroup>
            {deckTypes.map((deck) => (
              <ControlButton
                key={deck.id}
                isActive={selectedDeckType === deck.id}
                onClick={() => setSelectedDeckType(deck.id)}
              >
                {deck.label}
              </ControlButton>
            ))}
          </ButtonGroup>
        </ControlSection>

        <ControlSection>
          <SectionTitle>Тема оформления</SectionTitle>
          <ButtonGroup>
            {themes.map((theme) => (
              <ControlButton
                key={theme.id}
                isActive={selectedTheme === theme.id}
                onClick={() => setSelectedTheme(theme.id)}
              >
                {theme.label}
              </ControlButton>
            ))}
          </ButtonGroup>
        </ControlSection>

        <ControlSection>
          <SectionTitle>Специальные эффекты</SectionTitle>
          <ButtonGroup>
            {effects.map((effect) => (
              <ControlButton
                key={effect.id}
                isActive={selectedEffect === effect.id}
                onClick={() => setSelectedEffect(effect.id)}
              >
                {effect.label}
              </ControlButton>
            ))}
          </ButtonGroup>
        </ControlSection>

        <ControlSection>
          <SectionTitle>Расположение карт</SectionTitle>
          <ButtonGroup>
            {layouts.map((layout) => (
              <ControlButton
                key={layout.id}
                isActive={selectedLayout === layout.id}
                onClick={() => setSelectedLayout(layout.id)}
              >
                {layout.label}
              </ControlButton>
            ))}
          </ButtonGroup>
        </ControlSection>

        <ControlSection>
          <SectionTitle>Количество игроков</SectionTitle>
          <ButtonGroup>
            {playerOptions.map((option) => (
              <ControlButton
                key={option.value}
                isActive={players === option.value}
                onClick={() => setPlayers(option.value)}
              >
                {option.label}
              </ControlButton>
            ))}
          </ButtonGroup>
        </ControlSection>

        <ControlSection>
          <SectionTitle>Дополнительные настройки</SectionTitle>
          <ToggleGroup>
            <ToggleButton
              isActive={enableDragDrop}
              onClick={() => setEnableDragDrop(!enableDragDrop)}
            >
              {enableDragDrop
                ? "🔄 Перетаскивание включено"
                : "❌ Перетаскивание выключено"}
            </ToggleButton>

            <ToggleButton
              isActive={showHints}
              onClick={() => setShowHints(!showHints)}
            >
              {showHints ? "💡 Подсказки включены" : "❌ Подсказки выключены"}
            </ToggleButton>

            <ToggleButton
              isActive={enableSound}
              onClick={() => setEnableSound(!enableSound)}
            >
              {enableSound ? "🔊 Звук включен" : "🔇 Звук выключен"}
            </ToggleButton>

            <ToggleButton
              isActive={autoPlay}
              onClick={() => setAutoPlay(!autoPlay)}
            >
              {autoPlay ? "🤖 Автоигра включена" : "👤 Автоигра выключена"}
            </ToggleButton>
          </ToggleGroup>
        </ControlSection>
      </ControlPanel>

      <GameTableWrapper>
        <GameTable
          deckType={selectedDeckType}
          tableTheme={selectedTheme}
          cardEffect={selectedEffect}
          layout={selectedLayout}
          enableDragDrop={enableDragDrop}
          showHints={showHints}
          enableSound={enableSound}
          autoPlay={autoPlay}
          players={players}
        />
      </GameTableWrapper>

      <ExampleFooter>
        <p>
          Демонстрация интеграции компонентов PlayerHand и Card с расширенными
          возможностями
        </p>
        <p>
          Перетаскивайте карты, используйте подсказки и наслаждайтесь игрой!
        </p>
      </ExampleFooter>
    </ExampleContainer>
  );
};

// Стилизованные компоненты
const ExampleContainer = styled.div`
  padding: 30px;
  background-color: #f0f2f5;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  max-width: 1400px;
  margin: 0 auto;
`;

const ExampleHeader = styled.h1`
  font-size: 32px;
  text-align: center;
  margin-bottom: 30px;
  color: #2c3e50;
`;

const ControlPanel = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
  background-color: #fff;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
`;

const ControlSection = styled.div`
  flex: 1 1 300px;
  margin-bottom: 15px;
`;

const SectionTitle = styled.h3`
  font-size: 18px;
  margin-bottom: 10px;
  color: #34495e;
  font-weight: 600;
`;

const ButtonGroup = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
`;

const ControlButton = styled.button<{ isActive: boolean }>`
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background-color: ${(props) => (props.isActive ? "#3498db" : "#e0e0e0")};
  color: ${(props) => (props.isActive ? "white" : "#333")};
  font-weight: ${(props) => (props.isActive ? "bold" : "normal")};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.isActive ? "#2980b9" : "#d0d0d0")};
    transform: translateY(-2px);
  }
`;

const ToggleGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

const ToggleButton = styled.button<{ isActive: boolean }>`
  padding: 10px;
  border: none;
  border-radius: 6px;
  background-color: ${(props) => (props.isActive ? "#2ecc71" : "#e74c3c")};
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    opacity: 0.9;
    transform: translateY(-2px);
  }
`;

const GameTableWrapper = styled.div`
  margin-bottom: 30px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
`;

const ExampleFooter = styled.div`
  text-align: center;
  margin-top: 20px;
  color: #7f8c8d;
  font-size: 14px;

  p {
    margin: 5px 0;
  }
`;
