import React, { useState, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';

export interface GameNotificationItem {
  id: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'game';
  timestamp: number;
  duration?: number; // Длительность показа в мс (по умолчанию 5000)
  isRead?: boolean;
}

interface GameNotificationsProps {
  notifications: GameNotificationItem[];
  onDismiss?: (id: string) => void;
  onDismissAll?: () => void;
  onMarkAsRead?: (id: string) => void;
  maxNotifications?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

const slideIn = keyframes`
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
`;

const slideOut = keyframes`
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
`;

const getPositionStyles = (position: string) => {
  switch (position) {
    case 'top-left':
      return 'top: 20px; left: 20px;';
    case 'bottom-right':
      return 'bottom: 20px; right: 20px;';
    case 'bottom-left':
      return 'bottom: 20px; left: 20px;';
    case 'top-right':
    default:
      return 'top: 20px; right: 20px;';
  }
};

const NotificationsContainer = styled.div<{ position: string }>`
  position: fixed;
  ${props => getPositionStyles(props.position)};
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
  max-height: 80vh;
  overflow-y: auto;
  padding: 5px;

  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 5px;
  }
`;

const NotificationItem = styled.div<{ type: string; isExiting: boolean }>`
  display: flex;
  padding: 12px 15px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-bottom: 8px;
  animation: ${props => props.isExiting ? slideOut : slideIn} 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
  background-color: ${props => {
    switch (props.type) {
      case 'success': return '#4cc9f0';
      case 'warning': return '#f9c74f';
      case 'error': return '#f72585';
      case 'game': return '#4361ee';
      default: return '#0f3460';
    }
  }};
  color: white;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background-color: ${props => {
      switch (props.type) {
        case 'success': return '#4895ef';
        case 'warning': return '#f3722c';
        case 'error': return '#d00000';
        case 'game': return '#3a0ca3';
        default: return '#16213e';
      }
    }};
  }
`;

const NotificationContent = styled.div`
  flex: 1;
`;

const NotificationMessage = styled.p`
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.4;
`;

const NotificationTime = styled.span`
  font-size: 0.75rem;
  opacity: 0.8;
  margin-top: 5px;
  display: block;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
  padding: 0;
  margin-left: 10px;
  align-self: flex-start;

  &:hover {
    opacity: 1;
  }
`;

const ClearAllButton = styled.button`
  background-color: rgba(0, 0, 0, 0.2);
  border: none;
  color: white;
  padding: 8px 12px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.8rem;
  margin-top: 5px;
  align-self: flex-end;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.3);
  }
`;

const ProgressBar = styled.div<{ duration: number; type: string }>`
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background-color: rgba(255, 255, 255, 0.5);
  width: 100%;
  animation: shrink ${props => props.duration}ms linear forwards;

  @keyframes shrink {
    from { width: 100%; }
    to { width: 0%; }
  }
`;

const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
};

export const GameNotifications: React.FC<GameNotificationsProps> = ({
  notifications,
  onDismiss,
  onDismissAll,
  onMarkAsRead,
  maxNotifications = 5,
  position = 'top-right'
}) => {
  const [exitingNotifications, setExitingNotifications] = useState<Record<string, boolean>>({});
  const [visibleNotifications, setVisibleNotifications] = useState<GameNotificationItem[]>([]);

  // Обновляем видимые уведомления при изменении входных данных
  useEffect(() => {
    setVisibleNotifications(notifications.slice(-maxNotifications));
  }, [notifications, maxNotifications]);

  // Обработчик закрытия уведомления
  const handleDismiss = (id: string) => {
    // Анимация выхода
    setExitingNotifications(prev => ({ ...prev, [id]: true }));
    
    // Задержка перед удалением для завершения анимации
    setTimeout(() => {
      if (onDismiss) {
        onDismiss(id);
      }
    }, 300); // Длительность анимации
  };

  // Автоматическое закрытие уведомлений по истечении времени
  useEffect(() => {
    const timers: NodeJS.Timeout[] = [];
    
    visibleNotifications.forEach(notification => {
      if (!notification.isRead) {
        const duration = notification.duration || 5000;
        const timer = setTimeout(() => {
          handleDismiss(notification.id);
        }, duration);
        
        timers.push(timer);
      }
    });
    
    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [visibleNotifications]);

  // Если нет уведомлений, не рендерим компонент
  if (visibleNotifications.length === 0) {
    return null;
  }

  return (
    <NotificationsContainer position={position}>
      {visibleNotifications.map(notification => (
        <NotificationItem 
          key={notification.id} 
          type={notification.type}
          isExiting={!!exitingNotifications[notification.id]}
          onAnimationEnd={() => {
            if (exitingNotifications[notification.id]) {
              // Если анимация выхода завершена, можно удалить уведомление из состояния
              setExitingNotifications(prev => {
                const newState = { ...prev };
                delete newState[notification.id];
                return newState;
              });
            }
          }}
        >
          <NotificationContent>
            <NotificationMessage>{notification.message}</NotificationMessage>
            <NotificationTime>{formatTime(notification.timestamp)}</NotificationTime>
          </NotificationContent>
          <CloseButton onClick={() => handleDismiss(notification.id)}>
            ×
          </CloseButton>
          {!notification.isRead && notification.duration && (
            <ProgressBar 
              duration={notification.duration} 
              type={notification.type} 
            />
          )}
        </NotificationItem>
      ))}
      
      {visibleNotifications.length > 1 && onDismissAll && (
        <ClearAllButton onClick={onDismissAll}>
          Очистить все
        </ClearAllButton>
      )}
    </NotificationsContainer>
  );
};