import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';

interface ChatMessage {
  id: string;
  sender: string;
  senderId: string;
  text: string;
  timestamp: number;
  isSystem?: boolean;
}

interface GameChatProps {
  messages: ChatMessage[];
  onSendMessage: (text: string) => void;
  currentPlayerId: string;
  currentPlayerName: string;
  isMinimized?: boolean;
  onToggleMinimize?: () => void;
  maxMessages?: number;
}

const ChatContainer = styled.div<{ isMinimized: boolean }>`
  display: flex;
  flex-direction: column;
  background-color: rgba(22, 33, 62, 0.9);
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  width: ${props => props.isMinimized ? '300px' : '350px'};
  height: ${props => props.isMinimized ? '50px' : '400px'};
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 100;
  transition: all 0.3s ease;
  overflow: hidden;
`;

const ChatHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #0f3460;
  color: white;
  cursor: pointer;

  h3 {
    margin: 0;
    font-size: 1rem;
  }
`;

const ChatToggleButton = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
`;

const MessagesContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;

  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(15, 52, 96, 0.2);
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(233, 69, 96, 0.5);
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(233, 69, 96, 0.8);
  }
`;

const MessageBubble = styled.div<{ isCurrentUser: boolean; isSystem: boolean }>`
  max-width: 80%;
  padding: 8px 12px;
  border-radius: 12px;
  word-break: break-word;
  font-size: 0.9rem;
  align-self: ${props => props.isCurrentUser ? 'flex-end' : 'flex-start'};
  
  ${props => props.isSystem 
    ? `
      background-color: rgba(67, 97, 238, 0.3);
      color: #e0e0e0;
      font-style: italic;
      align-self: center;
      text-align: center;
    `
    : props.isCurrentUser
      ? `
        background-color: #e94560;
        color: white;
        border-bottom-right-radius: 4px;
      `
      : `
        background-color: #0f3460;
        color: white;
        border-bottom-left-radius: 4px;
      `
  }
`;

const MessageSender = styled.div<{ isSystem: boolean }>`
  font-size: 0.8rem;
  margin-bottom: 2px;
  font-weight: bold;
  color: ${props => props.isSystem ? '#a0a0a0' : '#e0e0e0'};
  display: ${props => props.isSystem ? 'none' : 'block'};
`;

const MessageTime = styled.span`
  font-size: 0.7rem;
  opacity: 0.7;
  margin-left: 5px;
`;

const InputContainer = styled.form`
  display: flex;
  padding: 10px;
  background-color: rgba(15, 52, 96, 0.5);
  gap: 10px;
`;

const ChatInput = styled.input`
  flex: 1;
  padding: 8px 12px;
  border-radius: 20px;
  border: none;
  background-color: #16213e;
  color: white;
  outline: none;
  transition: all 0.3s ease;

  &:focus {
    box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.5);
  }

  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
`;

const SendButton = styled.button`
  background-color: #e94560;
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #d63553;
    transform: scale(1.05);
  }

  &:disabled {
    background-color: #555;
    cursor: not-allowed;
  }
`;

const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
};

export const GameChat: React.FC<GameChatProps> = ({
  messages,
  onSendMessage,
  currentPlayerId,
  currentPlayerName,
  isMinimized = false,
  onToggleMinimize,
  maxMessages = 50
}) => {
  const [messageText, setMessageText] = useState('');
  const [localIsMinimized, setLocalIsMinimized] = useState(isMinimized);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isAtBottom, setIsAtBottom] = useState(true);

  // Используем внешнее состояние, если оно предоставлено
  const minimized = onToggleMinimize ? isMinimized : localIsMinimized;

  // Прокрутка к последнему сообщению при добавлении новых
  useEffect(() => {
    if (!minimized && isAtBottom && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    } else if (minimized) {
      // Если чат свернут, увеличиваем счетчик непрочитанных сообщений
      setUnreadCount(prev => prev + 1);
    }
  }, [messages, minimized, isAtBottom]);

  // Отслеживаем положение прокрутки
  useEffect(() => {
    const handleScroll = () => {
      if (messagesContainerRef.current) {
        const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
        const isBottom = scrollHeight - scrollTop - clientHeight < 20;
        setIsAtBottom(isBottom);
      }
    };

    const container = messagesContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, []);

  // Обработчик отправки сообщения
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (messageText.trim()) {
      onSendMessage(messageText.trim());
      setMessageText('');
    }
  };

  // Обработчик сворачивания/разворачивания чата
  const handleToggleMinimize = () => {
    if (onToggleMinimize) {
      onToggleMinimize();
    } else {
      setLocalIsMinimized(!localIsMinimized);
    }
    
    // Сбрасываем счетчик непрочитанных при разворачивании
    if (minimized) {
      setUnreadCount(0);
    }
  };

  // Ограничиваем количество отображаемых сообщений
  const displayedMessages = messages.slice(-maxMessages);

  return (
    <ChatContainer isMinimized={minimized}>
      <ChatHeader onClick={handleToggleMinimize}>
        <h3>
          Чат {unreadCount > 0 && minimized ? `(${unreadCount})` : ''}
        </h3>
        <ChatToggleButton>
          {minimized ? '▲' : '▼'}
        </ChatToggleButton>
      </ChatHeader>

      {!minimized && (
        <>
          <MessagesContainer ref={messagesContainerRef}>
            {displayedMessages.map(message => (
              <div key={message.id}>
                <MessageBubble 
                  isCurrentUser={message.senderId === currentPlayerId}
                  isSystem={!!message.isSystem}
                >
                  <MessageSender isSystem={!!message.isSystem}>
                    {message.sender}
                  </MessageSender>
                  {message.text}
                  <MessageTime>{formatTime(message.timestamp)}</MessageTime>
                </MessageBubble>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </MessagesContainer>

          <InputContainer onSubmit={handleSubmit}>
            <ChatInput 
              type="text" 
              value={messageText}
              onChange={(e) => setMessageText(e.target.value)}
              placeholder="Введите сообщение..."
            />
            <SendButton type="submit" disabled={!messageText.trim()}>
              ➤
            </SendButton>
          </InputContainer>
        </>
      )}
    </ChatContainer>
  );
};