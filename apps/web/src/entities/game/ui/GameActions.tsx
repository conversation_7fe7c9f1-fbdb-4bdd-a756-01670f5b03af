import React, { <PERSON> } from "react";
import styled from "styled-components";
import { PlayerAction } from "../model/slice";

interface GameActionsProps {
  isActive: boolean;
  playerRole: "attacker" | "defender" | "waiting";
  onAction: (action: PlayerAction) => void;
}

const ActionsContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 10px;
  margin: 20px 0;
`;

const ActionButton = styled.button<{ actionType: string }>`
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: ${(props) => {
    switch (props.actionType) {
      case "attack":
        return "#4CAF50";
      case "defend":
        return "#2196F3";
      case "take":
        return "#F44336";
      case "pass":
        return "#FF9800";
      case "done":
        return "#9C27B0";
      default:
        return "#757575";
    }
  }};
  color: white;

  &:hover {
    opacity: 0.9;
    transform: translateY(-2px);
  }

  &:disabled {
    background-color: #757575;
    cursor: not-allowed;
    opacity: 0.7;
    transform: none;
  }
`;

export const GameActions: FC<GameActionsProps> = ({
  isActive,
  playerRole,
  onAction,
}) => {
  return (
    <ActionsContainer>
      {playerRole === "attacker" && (
        <>
          <ActionButton
            actionType="attack"
            disabled={!isActive}
            onClick={() => onAction(PlayerAction.ATTACK)}
          >
            Атаковать
          </ActionButton>
          <ActionButton
            actionType="done"
            disabled={!isActive}
            onClick={() => onAction(PlayerAction.DONE)}
          >
            Завершить
          </ActionButton>
        </>
      )}

      {playerRole === "defender" && (
        <>
          <ActionButton
            actionType="defend"
            disabled={!isActive}
            onClick={() => onAction(PlayerAction.DEFEND)}
          >
            Защищаться
          </ActionButton>
          <ActionButton
            actionType="take"
            disabled={!isActive}
            onClick={() => onAction(PlayerAction.TAKE)}
          >
            Взять
          </ActionButton>
        </>
      )}

      {playerRole === "waiting" && (
        <ActionButton
          actionType="pass"
          disabled={!isActive}
          onClick={() => onAction(PlayerAction.PASS)}
        >
          Пас
        </ActionButton>
      )}
    </ActionsContainer>
  );
};
