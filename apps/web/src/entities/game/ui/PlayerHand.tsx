import React, {
  FC,
  useState,
  useEffect,
  useMemo,
  useRef,
  DragEvent,
} from "react";
import styled, { keyframes, css } from "styled-components";
import { Card } from "../../card";
import { CardModel, CardSuit } from "../../card/model/types";

interface PlayerHandProps {
  cards: CardModel[];
  isActive: boolean;
  onCardSelect?: (card: CardModel) => void;
  canPlayCard?: (card: CardModel) => boolean; // Функция для проверки возможности хода картой
  sortCards?: boolean; // Флаг для сортировки карт
  groupBySuit?: boolean; // Флаг для группировки карт по масти
  showPlayableIndicator?: boolean; // Показывать индикатор для карт, которыми можно сходить
  compactView?: boolean; // Компактное отображение карт
  enableDragDrop?: boolean; // Включить возможность перетаскивания карт
  onCardDrop?: (card: CardModel, target: "table" | "hand") => void; // Обработчик события перетаскивания карты
  suggestCard?: boolean; // Автоматически предлагать карту для хода
  showHints?: boolean; // Показывать подсказки для новичков
  cardSize?: "small" | "medium" | "large"; // Размер карт
}

// Анимация для подсветки активного игрока
const pulseAnimation = keyframes`
  0% { box-shadow: 0 0 10px rgba(255, 255, 0, 0.3); }
  50% { box-shadow: 0 0 20px rgba(255, 255, 0, 0.6); }
  100% { box-shadow: 0 0 10px rgba(255, 255, 0, 0.3); }
`;

// Анимация для выбранной карты
const selectedCardAnimation = keyframes`
  0% { transform: translateY(-20px) rotate(0deg); box-shadow: 0 10px 20px rgba(255, 215, 0, 0.4); }
  50% { transform: translateY(-30px) rotate(2deg); box-shadow: 0 15px 30px rgba(255, 215, 0, 0.8), 0 0 15px gold; }
  100% { transform: translateY(-20px) rotate(0deg); box-shadow: 0 10px 20px rgba(255, 215, 0, 0.4); }
`;

// Анимация для новой карты
const newCardAnimation = keyframes`
  0% { transform: translateY(50px) scale(0.8); opacity: 0; }
  70% { transform: translateY(-10px) scale(1.05); opacity: 1; }
  85% { transform: translateY(5px) scale(0.95); opacity: 1; }
  100% { transform: translateY(0) scale(1); opacity: 1; }
`;

// Анимация для карты, которой можно сходить
const playableCardAnimation = keyframes`
  0% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.3); }
  50% { box-shadow: 0 0 15px rgba(255, 215, 0, 0.8); }
  100% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.3); }
`;

const HandContainer = styled.div<{ isActive: boolean; compact?: boolean }>`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  padding: ${(props) => (props.compact ? "10px" : "15px")};
  border-radius: 10px;
  background-color: ${(props) =>
    props.isActive ? "rgba(255, 255, 200, 0.2)" : "transparent"};
  box-shadow: ${(props) =>
    props.isActive ? "0 0 10px rgba(255, 255, 0, 0.3)" : "none"};
  transition: all 0.3s ease;
  animation: ${(props) => (props.isActive ? pulseAnimation : "none")} 2s
    infinite;
  position: relative;
  min-height: 200px;
  margin-bottom: 20px;
`;

// Анимация для перетаскивания карты
const dragCardAnimation = keyframes`
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1) rotate(3deg); opacity: 0.9; box-shadow: 0 15px 25px rgba(255, 215, 0, 0.5); }
  100% { transform: scale(1.1) rotate(-3deg); opacity: 0.8; box-shadow: 0 15px 25px rgba(255, 215, 0, 0.5); }
`;

const CardWrapper = styled.div<{
  isNew?: boolean;
  isPlayable: boolean;
  canPlay?: boolean;
  isPreview?: boolean;
  showIndicator?: boolean;
  compact?: boolean;
  isDragging?: boolean;
  size?: "small" | "medium" | "large";
}>`
  margin: ${(props) => (props.compact ? "0 -25px" : "0 -15px")};
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
  animation: ${(props) => {
      if (props.isDragging) return `${dragCardAnimation} 0.2s forwards`;
      if (props.isNew) return newCardAnimation;
      if (props.canPlay && props.showIndicator)
        return `${playableCardAnimation} 2s infinite`;
      return "none";
    }}
    0.5s ease-out;
  position: relative;
  cursor: ${(props) => (props.isPlayable ? "grab" : "default")};

  /* Размер карты */
  transform: ${(props) => {
    if (props.size === "small") return "scale(0.8)";
    if (props.size === "large") return "scale(1.2)";
    return "scale(1)";
  }};
  transform-origin: center bottom;

  &:hover {
    transform: ${(props) => {
      const baseScale =
        props.size === "small"
          ? "scale(0.8)"
          : props.size === "large"
            ? "scale(1.2)"
            : "scale(1)";
      if (props.isPreview) return "translateY(-30px) scale(1.2)";
      if (props.compact) return `translateY(-10px) ${baseScale}`;
      return `translateY(-15px) ${baseScale}`;
    }};
    z-index: 10;
    ${(props) =>
      props.isPlayable && "box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);"}
  }

  &:after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid ${(props) => (props.canPlay ? "gold" : "gray")};
    opacity: ${(props) => {
      if (!props.showIndicator) return 0;
      return props.canPlay ? 0.8 : 0.3;
    }};
    transition: opacity 0.3s ease;
  }

  ${(props) =>
    props.isPlayable &&
    props.showIndicator &&
    `
    &:hover:after {
      opacity: 1;
    }
  `}

  ${(props) =>
    props.isPreview &&
    css`
      position: absolute;
      top: -50px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 100;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.6);
    `}
  
  ${(props) =>
    props.canPlay &&
    props.showIndicator &&
    css`
      &:before {
        content: "";
        position: absolute;
        top: -5px;
        right: -5px;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        background-color: gold;
        box-shadow: 0 0 10px rgba(255, 215, 0, 0.7);
        z-index: 5;
      }
    `}
  
  /* Анимация для выбранной карты */
  ${(props) =>
    props.isPlayable &&
    css`
      &.selected {
        animation: ${selectedCardAnimation} 1.5s ease-in-out infinite;
        z-index: 20;
        box-shadow: 0 10px 30px rgba(255, 215, 0, 0.6);
      }
    `}
  
  /* Стиль при перетаскивании */
  ${(props) =>
    props.isDragging &&
    css`
      opacity: 0.6;
      cursor: grabbing;
    `}
`;

const PlayerInfo = styled.div<{ isActive: boolean }>`
  text-align: center;
  margin-bottom: 15px;
  color: ${(props) => (props.isActive ? "#ffff99" : "white")};
  font-size: 18px;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  transition: all 0.3s ease;
  padding: 8px 16px;
  border-radius: 20px;
  background-color: ${(props) =>
    props.isActive ? "rgba(0, 0, 0, 0.5)" : "transparent"};
  display: inline-block;
  animation: ${(props) => (props.isActive ? pulseAnimation : "none")} 2s
    infinite;
  box-shadow: ${(props) =>
    props.isActive ? "0 0 15px rgba(255, 255, 0, 0.5)" : "none"};
`;

const SuitGroup = styled.div<{ suit: CardSuit; compact?: boolean }>`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin: 5px 0;
  padding: 10px;
  border-radius: 8px;
  background-color: ${(props) => {
    switch (props.suit) {
      case "hearts":
        return "rgba(255, 0, 0, 0.1)";
      case "diamonds":
        return "rgba(255, 0, 0, 0.1)";
      case "clubs":
        return "rgba(0, 0, 0, 0.1)";
      case "spades":
        return "rgba(0, 0, 0, 0.1)";
      default:
        return "transparent";
    }
  }};
  position: relative;
  width: 100%;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${(props) => {
      switch (props.suit) {
        case "hearts":
          return "rgba(255, 0, 0, 0.2)";
        case "diamonds":
          return "rgba(255, 0, 0, 0.2)";
        case "clubs":
          return "rgba(0, 0, 0, 0.2)";
        case "spades":
          return "rgba(0, 0, 0, 0.2)";
        default:
          return "transparent";
      }
    }};
  }
`;

const SuitLabel = styled.div<{ suit: CardSuit }>`
  position: absolute;
  top: -10px;
  left: 10px;
  padding: 2px 8px;
  border-radius: 10px;
  background-color: ${(props) => {
    switch (props.suit) {
      case "hearts":
        return "rgba(255, 0, 0, 0.7)";
      case "diamonds":
        return "rgba(255, 0, 0, 0.7)";
      case "clubs":
        return "rgba(0, 0, 0, 0.7)";
      case "spades":
        return "rgba(0, 0, 0, 0.7)";
      default:
        return "transparent";
    }
  }};
  color: white;
  font-size: 12px;
  font-weight: bold;
`;

const getSuitSymbol = (suit: CardSuit): string => {
  switch (suit) {
    case "hearts":
      return "♥";
    case "diamonds":
      return "♦";
    case "clubs":
      return "♣";
    case "spades":
      return "♠";
    default:
      return "";
  }
};

// Стили для зоны сброса карт
const DropZone = styled.div<{ isActive: boolean; isOver?: boolean }>`
  border: 3px dashed
    ${(props) =>
      props.isOver
        ? "gold"
        : props.isActive
          ? "rgba(255, 255, 0, 0.5)"
          : "rgba(255, 255, 255, 0.3)"};
  border-radius: 10px;
  padding: 15px;
  margin: 10px 0;
  min-height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: ${(props) =>
    props.isOver ? "rgba(255, 215, 0, 0.3)" : "transparent"};
  transition: all 0.3s ease;
  color: ${(props) => (props.isOver ? "gold" : "rgba(255, 255, 255, 0.7)")};
  font-size: 16px;
  text-align: center;
  box-shadow: ${(props) =>
    props.isOver ? "0 0 20px rgba(255, 215, 0, 0.7)" : "none"};
  animation: ${(props) => (props.isOver ? pulseAnimation : "none")} 1s infinite;

  &::before {
    content: "🃏";
    font-size: 28px;
    margin-right: 10px;
    opacity: ${(props) => (props.isOver ? "1" : "0.5")};
    transform: ${(props) =>
      props.isOver ? "scale(1.3) rotate(5deg)" : "scale(1)"};
    transition: all 0.3s ease;
  }
`;

// Стили для подсказок
const HintBox = styled.div`
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 12px 18px;
  margin: 10px 0;
  color: white;
  font-size: 15px;
  box-shadow:
    0 3px 15px rgba(0, 0, 0, 0.4),
    0 0 10px rgba(255, 215, 0, 0.3);
  position: relative;
  border-left: 5px solid gold;
  animation: fadeIn 0.5s ease-in-out;
  line-height: 1.4;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

// Дополнительные стили для разных типов подсказок
const HintIcon = styled.span`
  margin-right: 8px;
  font-size: 16px;
`;

const SuggestedCardIndicator = styled.div`
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background-color: gold;
  color: black;
  padding: 3px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  z-index: 5;
`;

const ControlPanel = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
  gap: 10px;
  flex-wrap: wrap;
`;

const ControlButton = styled.button<{ active?: boolean }>`
  background-color: ${(props) =>
    props.active ? "rgba(0, 100, 0, 0.7)" : "rgba(0, 0, 0, 0.5)"};
  color: white;
  border: none;
  border-radius: 15px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: ${(props) =>
    props.active ? "0 0 10px rgba(0, 255, 0, 0.3)" : "none"};

  &:hover {
    background-color: ${(props) =>
      props.active ? "rgba(0, 120, 0, 0.8)" : "rgba(0, 0, 0, 0.7)"};
    transform: translateY(-2px);
  }

  &:active {
    transform: scale(0.95);
  }
`;

export const PlayerHand: FC<PlayerHandProps> = ({
  cards,
  isActive,
  onCardSelect,
  canPlayCard,
  sortCards = true,
  groupBySuit = true,
  showPlayableIndicator = true,
  compactView = false,
  enableDragDrop = false,
  onCardDrop,
  suggestCard = false,
  showHints = false,
  cardSize = "medium",
}) => {
  // Отслеживаем новые карты для анимации
  const [prevCards, setPrevCards] = useState<string[]>([]);
  const [newCardIds, setNewCardIds] = useState<string[]>([]);
  const [previewCard, setPreviewCard] = useState<CardModel | null>(null);
  const [isSorted, setIsSorted] = useState<boolean>(sortCards);
  const [isGrouped, setIsGrouped] = useState<boolean>(groupBySuit);
  const [showIndicator, setShowIndicator] = useState<boolean>(
    showPlayableIndicator,
  );
  const [isCompact, setIsCompact] = useState<boolean>(compactView);
  const [isDragging, setIsDragging] = useState<string | null>(null);
  const [isDropZoneOver, setIsDropZoneOver] = useState<boolean>(false);
  const [showHintsState, setShowHintsState] = useState<boolean>(showHints);
  const [cardSizeState, setCardSizeState] = useState<
    "small" | "medium" | "large"
  >(cardSize);

  // Ссылка на контейнер для карт
  const handContainerRef = useRef<HTMLDivElement>(null);

  // Эффект для отслеживания новых карт
  useEffect(() => {
    const currentCardIds = cards.map((card) => card.id);
    const newIds = currentCardIds.filter((id) => !prevCards.includes(id));

    if (newIds.length > 0) {
      setNewCardIds(newIds);
      // Сбрасываем флаг новой карты через некоторое время
      const timer = setTimeout(() => setNewCardIds([]), 1000);
      return () => clearTimeout(timer);
    }

    setPrevCards(currentCardIds);
  }, [cards, prevCards]);

  // Сортировка и группировка карт
  const sortedCards = useMemo(() => {
    if (!isSorted) return cards;

    return [...cards].sort((a, b) => {
      // Сначала сортируем по масти
      const suitOrder: Record<CardSuit, number> = {
        hearts: 1,
        diamonds: 2,
        clubs: 3,
        spades: 4,
      };

      if (isGrouped) {
        if (suitOrder[a.suit] !== suitOrder[b.suit]) {
          return suitOrder[a.suit] - suitOrder[b.suit];
        }
      }

      // Затем по значению (от большего к меньшему)
      return Number(b.rank) - Number(a.rank);
    });
  }, [cards, isSorted, isGrouped]);

  // Группировка карт по масти
  const cardGroups = useMemo(() => {
    if (!isGrouped) return { ungrouped: sortedCards };

    return sortedCards.reduce<Record<string, CardModel[]>>((groups, card) => {
      if (!groups[card.suit]) {
        groups[card.suit] = [];
      }
      groups[card.suit].push(card);
      return groups;
    }, {});
  }, [sortedCards, isGrouped]);

  // Состояние для выбранной карты
  const [selectedCard, setSelectedCard] = useState<string | null>(null);

  // Обработчик клика по карте
  const handleCardClick = (card: CardModel) => {
    if (onCardSelect && isActive) {
      // Если карта уже выбрана, снимаем выбор
      if (selectedCard === card.id) {
        setSelectedCard(null);
      } else {
        // Иначе выбираем карту
        setSelectedCard(card.id);
      }
      onCardSelect(card);
    }
  };

  // Обработчики для предпросмотра карты
  const handleMouseEnter = (card: CardModel) => {
    // Не показываем предпросмотр для выбранной карты
    if (selectedCard !== card.id) {
      setPreviewCard(card);
    }
  };

  const handleMouseLeave = () => {
    setPreviewCard(null);
  };

  // Сброс выбранной карты при изменении активности игрока
  useEffect(() => {
    if (!isActive) {
      setSelectedCard(null);
    }
  }, [isActive]);

  // Предлагаемая карта для хода
  const suggestedCard = useMemo(() => {
    if (!suggestCard || !isActive || cards.length === 0) return null;

    // Находим карты, которыми можно сходить
    const playableCards = cards.filter((card) =>
      canPlayCard ? canPlayCard(card) : true,
    );

    if (playableCards.length === 0) return null;

    // Простая логика: предлагаем карту с наименьшим значением
    return playableCards.reduce((lowest, current) => {
      return Number(current.rank) < Number(lowest.rank) ? current : lowest;
    }, playableCards[0]);
  }, [cards, isActive, suggestCard, canPlayCard]);

  // Обработчики для drag-and-drop
  const handleDragStart = (e: DragEvent<HTMLDivElement>, card: CardModel) => {
    if (!enableDragDrop || !isActive) return;

    setIsDragging(card.id);

    // Устанавливаем данные для перетаскивания
    e.dataTransfer.setData("application/json", JSON.stringify(card));
    e.dataTransfer.effectAllowed = "move";

    // Устанавливаем прозрачное изображение для перетаскивания
    const dragImage = new Image();
    dragImage.src =
      "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";
    e.dataTransfer.setDragImage(dragImage, 0, 0);
  };

  const handleDragEnd = () => {
    setIsDragging(null);
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    if (!enableDragDrop) return;
    e.preventDefault();
    setIsDropZoneOver(true);
  };

  const handleDragLeave = () => {
    setIsDropZoneOver(false);
  };

  const handleDrop = (
    e: DragEvent<HTMLDivElement>,
    target: "table" | "hand",
  ) => {
    if (!enableDragDrop || !onCardDrop) return;
    e.preventDefault();

    setIsDropZoneOver(false);

    try {
      const cardData = e.dataTransfer.getData("application/json");
      if (cardData) {
        const card = JSON.parse(cardData) as CardModel;
        onCardDrop(card, target);
      }
    } catch (error) {
      console.error("Ошибка при обработке перетаскивания карты:", error);
    }
  };

  // Рендер карты
  const renderCard = (card: CardModel) => {
    const isNewCard = newCardIds.includes(card.id);
    const canPlay = canPlayCard ? canPlayCard(card) : isActive;
    const isSelected = selectedCard === card.id;
    const isSuggested = suggestedCard?.id === card.id;

    return (
      <CardWrapper
        key={card.id}
        isNew={isNewCard}
        isPlayable={isActive}
        canPlay={canPlay}
        showIndicator={showIndicator}
        compact={isCompact}
        className={isSelected ? "selected" : ""}
        onMouseEnter={() => handleMouseEnter(card)}
        onMouseLeave={handleMouseLeave}
        isDragging={isDragging === card.id}
        size={cardSizeState}
        draggable={enableDragDrop && isActive}
        onDragStart={(e) => handleDragStart(e, card)}
        onDragEnd={handleDragEnd}
      >
        {isSuggested && suggestCard && (
          <SuggestedCardIndicator>Рекомендуемый ход</SuggestedCardIndicator>
        )}
        <Card
          suit={card.suit}
          rank={Number(card.rank)}
          isSelected={previewCard?.id === card.id || isSuggested}
          isPlayable={canPlay}
          onClick={() => handleCardClick(card)}
        />
      </CardWrapper>
    );
  };

  return (
    <div>
      <PlayerInfo isActive={isActive}>
        {isActive ? "Ваш ход" : "Ожидание хода"}
      </PlayerInfo>

      {showHintsState && isActive && (
        <HintBox>
          <HintIcon>💡</HintIcon>
          {suggestedCard ? (
            <>
              Совет: Рассмотрите возможность сыграть картой с наименьшим
              значением. Это может сохранить ваши сильные карты для более важных
              ходов.
            </>
          ) : canPlayCard && cards.some((card) => canPlayCard(card)) ? (
            <>
              Совет: У вас есть карты, которыми можно сходить. Обратите внимание
              на карты с золотым индикатором.
            </>
          ) : (
            <>
              Совет: Внимательно оцените ситуацию на столе перед ходом. Помните,
              что козырные карты лучше приберечь для защиты.
            </>
          )}
        </HintBox>
      )}

      <ControlPanel>
        <ControlButton
          onClick={() => setIsSorted(!isSorted)}
          active={isSorted}
          aria-pressed={isSorted}
          title="Сортировка карт"
        >
          {isSorted ? "Отключить сортировку" : "Включить сортировку"}
        </ControlButton>
        <ControlButton
          onClick={() => setIsGrouped(!isGrouped)}
          active={isGrouped}
          aria-pressed={isGrouped}
          title="Группировка по масти"
        >
          {isGrouped ? "Разгруппировать" : "Группировать по масти"}
        </ControlButton>
        <ControlButton
          onClick={() => setShowIndicator(!showIndicator)}
          active={showIndicator}
          aria-pressed={showIndicator}
          title="Индикаторы карт"
        >
          {showIndicator ? "Скрыть индикаторы" : "Показать индикаторы"}
        </ControlButton>
        <ControlButton
          onClick={() => setIsCompact(!isCompact)}
          active={isCompact}
          aria-pressed={isCompact}
          title="Компактный вид"
        >
          {isCompact ? "Обычный вид" : "Компактный вид"}
        </ControlButton>
        <ControlButton
          onClick={() => setShowHintsState(!showHintsState)}
          active={showHintsState}
          aria-pressed={showHintsState}
          title="Подсказки"
        >
          {showHintsState ? "Скрыть подсказки" : "Показать подсказки"}
        </ControlButton>
        <ControlButton
          onClick={() => {
            const nextSize =
              cardSizeState === "small"
                ? "medium"
                : cardSizeState === "medium"
                  ? "large"
                  : "small";
            setCardSizeState(nextSize);
          }}
          active={cardSizeState !== "medium"}
          title="Размер карт"
        >
          {cardSizeState === "small"
            ? "Маленькие карты"
            : cardSizeState === "medium"
              ? "Средние карты"
              : "Большие карты"}
        </ControlButton>
        <ControlButton
          onClick={() => {
            const nextSpeed =
              animationSpeedState === "slow"
                ? "normal"
                : animationSpeedState === "normal"
                  ? "fast"
                  : "slow";
            setAnimationSpeedState(nextSpeed);
          }}
          active={animationSpeedState !== "normal"}
          title="Скорость анимаций"
        >
          {animationSpeedState === "slow"
            ? "Медленные анимации"
            : animationSpeedState === "normal"
              ? "Обычные анимации"
              : "Быстрые анимации"}
        </ControlButton>
        <ControlButton
          onClick={() => setHighContrastState(!highContrastState)}
          active={highContrastState}
          aria-pressed={highContrastState}
          title="Высокий контраст"
        >
          {highContrastState ? "Обычный контраст" : "Высокий контраст"}
        </ControlButton>
      </ControlPanel>

      {enableDragDrop && isActive && (
        <DropZone
          isActive={isActive}
          isOver={isDropZoneOver}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, "table")}
        >
          {isDropZoneOver
            ? "Отпустите карту, чтобы сыграть"
            : "Перетащите карту сюда, чтобы сыграть"}
        </DropZone>
      )}

      <HandContainer
        isActive={isActive}
        compact={isCompact}
        ref={handContainerRef}
        onDragOver={enableDragDrop ? handleDragOver : undefined}
        onDragLeave={enableDragDrop ? handleDragLeave : undefined}
        onDrop={enableDragDrop ? (e) => handleDrop(e, "hand") : undefined}
      >
        {previewCard && (
          <CardWrapper
            isPreview={true}
            isPlayable={true}
            canPlay={canPlayCard ? canPlayCard(previewCard) : isActive}
            showIndicator={showIndicator}
            compact={isCompact}
            size={cardSizeState}
          >
            <Card
              suit={previewCard.suit}
              rank={Number(previewCard.rank)}
              isSelected={true}
              isPlayable={true}
              onClick={() => handleCardClick(previewCard)}
            />
          </CardWrapper>
        )}

        {isGrouped
          ? Object.entries(cardGroups).map(([suit, cards]) => (
              <SuitGroup key={suit} suit={suit as CardSuit} compact={isCompact}>
                <SuitLabel suit={suit as CardSuit}>
                  {getSuitSymbol(suit as CardSuit)}{" "}
                  {suit === "hearts"
                    ? "Червы"
                    : suit === "diamonds"
                      ? "Бубны"
                      : suit === "clubs"
                        ? "Трефы"
                        : "Пики"}
                  {cards.length > 0 && ` (${cards.length})`}
                </SuitLabel>
                {cards.map(renderCard)}
              </SuitGroup>
            ))
          : sortedCards.map(renderCard)}
      </HandContainer>

      {/* Предпросмотр карты */}
      {previewCard && (
        <CardWrapper
          isPlayable={isActive}
          canPlay={canPlayCard ? canPlayCard(previewCard) : isActive}
          isPreview={true}
          showIndicator={false}
        >
          <Card
            suit={previewCard.suit}
            rank={Number(previewCard.rank)}
            isSelected={true}
            isPlayable={canPlayCard ? canPlayCard(previewCard) : isActive}
          />
        </CardWrapper>
      )}
    </div>
  );
};

// Экспортируем компонент как именованный экспорт для соответствия с index.ts
export { PlayerHand };
