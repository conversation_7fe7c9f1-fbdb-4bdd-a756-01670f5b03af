import React, { FC, useEffect, useState, useRef } from "react";
import styled, { keyframes, css } from "styled-components";
import { Card } from "../../card";
import { PlayerHand } from "./PlayerHand";
import {
  CardModel,
  CardSuit,
  CardTheme,
  CardEffect,
  CardLayout,
} from "../../card/model/types";

interface GameTableProps {
  tableCards?: CardModel[][];
  trumpCard?: CardModel | null;
  deck?: CardModel[];
  onCardClick?: (card: CardModel) => void;
  deckType?: CardTheme;
  tableTheme?: "light" | "dark" | "colorful" | "monochrome";
  cardEffect?: CardEffect;
  layout?: CardLayout;
  enableDragDrop?: boolean;
  showHints?: boolean;
  enableSound?: boolean;
  autoPlay?: boolean;
  players?: number; // Количество игроков (от 2 до 4)
  isMultiplayer?: boolean; // Флаг многопользовательского режима
  gameState?: any; // Состояние игры с сервера
  playerId?: string; // Идентификатор текущего игрока
  onPlayerMove?: (moveData: any) => Promise<any>; // Функция для отправки хода на сервер
}

const TableContainer = styled.div<{ theme?: string }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 20px;
  background-color: ${(props) => {
    switch (props.theme) {
      case "dark":
        return "#1e2a38";
      case "light":
        return "#f8f9fa";
      case "colorful":
        return "#4ca1af";
      case "monochrome":
        return "#2c3e50";
      default:
        return "#277714";
    }
  }};
  color: ${(props) => (props.theme === "light" ? "#333" : "#fff")};
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
  min-height: 700px;
  margin: 0 auto;
`;

// Анимации для карт
const dealCardAnimation = keyframes`
  0% {
    transform: translate(-100px, -100px) rotate(-10deg);
    opacity: 0;
  }
  100% {
    transform: translate(0, 0) rotate(0);
    opacity: 1;
  }
`;

const defendCardAnimation = keyframes`
  0% {
    transform: translate(50px, -50px) rotate(10deg);
    opacity: 0;
  }
  100% {
    transform: translate(0, 0) rotate(0);
    opacity: 1;
  }
`;

const CardsArea = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  margin: 20px 0;
  min-height: 200px;
  position: relative;
`;

const AttackDefendPair = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 10px;
  position: relative;
`;

const CardPair = styled.div`
  position: relative;
  width: 100px;
  height: 140px;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
    z-index: 10;
  }
`;

const AttackCardWrapper = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  animation: ${dealCardAnimation} 0.5s ease-out;
`;

const DefendCardWrapper = styled.div`
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 2;
  animation: ${defendCardAnimation} 0.5s ease-out;
`;

const DeckArea = styled.div`
  display: flex;
  align-items: center;
  margin-top: 20px;
  position: relative;
  transition: all 0.3s ease;
`;

const DeckWrapper = styled.div`
  position: relative;
  width: 100px;
  height: 140px;
  margin-right: 20px;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-5px);
  }
`;

const DeckCard = styled.div`
  position: absolute;
  width: 100px;
  height: 140px;
  background-color: #16213e;
  border-radius: 8px;
  border: 2px solid #0f3460;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
`;

const DeckCardStacked = styled(DeckCard)<{ index: number }>`
  top: ${(props) => props.index * -2}px;
  left: ${(props) => props.index * -2}px;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }
`;

const DeckCount = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-weight: bold;
  font-size: 14px;
  transition: all 0.3s ease;
`;

// Анимации
const highlightAnimation = keyframes`
  0% { box-shadow: 0 0 0 rgba(255, 215, 0, 0); }
  50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.7); }
  100% { box-shadow: 0 0 0 rgba(255, 215, 0, 0); }
`;

// Анимация пульсации
const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
`;

const ActionHighlight = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  pointer-events: none;
  animation: ${highlightAnimation} 2s infinite;
  z-index: 0;
`;

const TrumpCardArea = styled.div`
  position: relative;
  width: 100px;
  height: 140px;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
`;

const TrumpCardWrapper = styled.div`
  position: absolute;
  transform: rotate(90deg);
  transform-origin: center center;
  transition: all 0.3s ease;
`;

const TrumpLabel = styled.div`
  position: absolute;
  top: -25px;
  left: 0;
  width: 100%;
  text-align: center;
  color: white;
  font-size: 14px;
  font-weight: bold;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 2px 5px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
`;

// Дополнительные стилизованные компоненты
const GameTitle = styled.h2`
  font-size: 28px;
  margin-bottom: 20px;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
`;

const InfoPanel = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-bottom: 20px;
  padding: 10px;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.2);
`;

const GameInfo = styled.div`
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 10px;
  font-size: 16px;
  color: inherit;
  text-align: center;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
`;

const InfoItem = styled.div`
  font-size: 14px;
  padding: 5px 10px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
`;

const GameMessage = styled.div`
  font-size: 18px;
  font-weight: bold;
  padding: 10px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.15);
  animation: ${pulse} 2s infinite;
  text-align: center;
  width: 100%;
`;

const PlayersContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 20px;
  margin-top: 20px;
`;

const PlayerContainer = styled.div<{ isActive: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  border-radius: 10px;
  background-color: ${(props) =>
    props.isActive ? "rgba(255, 255, 200, 0.1)" : "rgba(0, 0, 0, 0.2)"};
  box-shadow: ${(props) =>
    props.isActive ? "0 0 15px rgba(255, 255, 0, 0.3)" : "none"};
  transition: all 0.3s ease;
  animation: ${(props) => (props.isActive ? highlightAnimation : "none")} 2s
    infinite;
`;

const PlayerLabel = styled.div`
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
`;

const ControlPanel = styled.div`
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
  width: 100%;
`;

const ControlButton = styled.button`
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  background-color: #3498db;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

  &:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
  }
`;

const TutorialOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const TutorialContent = styled.div`
  background-color: #fff;
  padding: 30px;
  border-radius: 10px;
  max-width: 600px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
`;

const TutorialTitle = styled.h3`
  font-size: 24px;
  margin-bottom: 20px;
  color: #333;
  text-align: center;
`;

const TutorialText = styled.div`
  font-size: 16px;
  line-height: 1.6;
  color: #555;
  margin-bottom: 20px;

  p {
    margin-bottom: 10px;
  }
`;

const CloseButton = styled.button`
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  background-color: #e74c3c;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  display: block;
  margin: 0 auto;

  &:hover {
    background-color: #c0392b;
  }
`;

// Генерация уникального ID для карты
const generateCardId = () => `card_${Math.random().toString(36).substr(2, 9)}`;

// Функция для создания колоды карт
const createDeck = (): CardModel[] => {
  const suits: CardSuit[] = ["hearts", "diamonds", "clubs", "spades"];
  const ranks = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13];
  const deck: CardModel[] = [];

  suits.forEach((suit) => {
    ranks.forEach((rank) => {
      deck.push({
        id: generateCardId(),
        suit,
        rank,
        isVisible: true,
      });
    });
  });

  return deck;
};

// Функция для перемешивания колоды
const shuffleDeck = (deck: CardModel[]): CardModel[] => {
  const shuffled = [...deck];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

// Функция для раздачи карт игрокам
const dealCards = (
  deck: CardModel[],
  numPlayers: number,
  cardsPerPlayer: number,
): CardModel[][] => {
  const players: CardModel[][] = Array(numPlayers)
    .fill([])
    .map(() => []);

  for (let i = 0; i < cardsPerPlayer; i++) {
    for (let j = 0; j < numPlayers; j++) {
      if (deck.length > 0) {
        const card = deck.pop();
        if (card) {
          players[j] = [...players[j], card];
        }
      }
    }
  }

  return players;
};

export const GameTable: FC<GameTableProps> = ({
  tableCards: initialTableCards,
  trumpCard: initialTrumpCard,
  deck: initialDeck = [],
  onCardClick,
  deckType = "classic",
  tableTheme = "dark",
  cardEffect = "none",
  layout = "fan",
  enableDragDrop = true,
  showHints = true,
  enableSound = true,
  autoPlay = false,
  players = 2,
}) => {
  // Состояние для анимации
  const [showHighlight, setShowHighlight] = useState(false);

  // Состояния для игрового стола
  const [deck, setDeck] = useState<CardModel[]>(initialDeck);
  const [playersHands, setPlayersHands] = useState<CardModel[][]>([]);
  const [tableCards, setTableCards] = useState<CardModel[][]>(
    initialTableCards || [],
  );
  const [currentPlayer, setCurrentPlayer] = useState<number>(0);
  const [gameStarted, setGameStarted] = useState<boolean>(false);
  const [trumpCard, setTrumpCard] = useState<CardModel | null>(
    initialTrumpCard || null,
  );
  const [gameMessage, setGameMessage] = useState<string>("");
  const [lastAction, setLastAction] = useState<string>("");
  const [draggedCard, setDraggedCard] = useState<CardModel | null>(null);
  const [showTutorial, setShowTutorial] = useState<boolean>(false);

  // Ссылка на игровой стол для определения координат
  const tableRef = useRef<HTMLDivElement>(null);

  // Эффект для анимации при изменении карт на столе
  useEffect(() => {
    if (tableCards.length > 0) {
      setShowHighlight(true);
      const timer = setTimeout(() => setShowHighlight(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [tableCards]);
  // Инициализация игры
  const initGame = () => {
    // Если есть начальная колода, используем её, иначе создаем новую
    if (initialDeck && initialDeck.length > 0) {
      setDeck(initialDeck);
    } else {
      // Создаем и перемешиваем колоду
      const newDeck = shuffleDeck(createDeck());

      // Выбираем козырную карту
      const trump = newDeck[0];
      setTrumpCard(trump);

      // Раздаем карты игрокам (по 6 карт каждому)
      const hands = dealCards(newDeck.slice(1), players, 6);
      setPlayersHands(hands);

      // Оставшаяся колода
      setDeck(newDeck.slice(1 + players * 6));
    }

    // Начинаем игру
    setGameStarted(true);
    setCurrentPlayer(0);
    setGameMessage("Игра началась! Ход первого игрока.");
  };

  // Эффект для инициализации игры при монтировании компонента
  useEffect(() => {
    if (!initialDeck || initialDeck.length === 0) {
      initGame();
    }
  }, [players]);

  // Функция для проверки возможности хода картой
  const canPlayCard = (card: CardModel): boolean => {
    // Если на столе нет карт, можно ходить любой
    if (tableCards.length === 0) return true;

    // Иначе можно ходить картой той же масти или того же ранга
    return tableCards.some((pair) =>
      pair.some(
        (tableCard) =>
          tableCard.suit === card.suit || tableCard.rank === card.rank,
      ),
    );
  };

  // Обработчик выбора карты игроком
  const handleCardSelect = (card: CardModel) => {
    if (!canPlayCard(card)) {
      setGameMessage("Этой картой нельзя сходить!");
      return;
    }
    
    // Если это многопользовательский режим, отправляем ход на сервер
    if (isMultiplayer && onPlayerMove && playerId) {
      // Формируем данные о ходе
      const moveData = {
        action: 'play',
        playerId,
        card: {
          id: card.id,
          rank: card.rank,
          suit: card.suit,
          value: card.value
        },
        position: {
          player: currentPlayer,
          tablePosition: tableCards.length === 0 ? 0 : 
            tableCards[tableCards.length - 1].length === 2 ? tableCards.length : tableCards.length - 1
        }
      };
      
      // Показываем индикатор загрузки
      setGameMessage("Отправка хода на сервер...");
      
      // Отправляем ход на сервер
      onPlayerMove(moveData)
        .then((result) => {
          console.log('Ход успешно отправлен:', result);
          // Обновление состояния произойдет через обработчик событий сокета
        })
        .catch((error) => {
          console.error('Ошибка при отправке хода:', error);
          setGameMessage(`Ошибка: ${error.message || 'Не удалось выполнить ход'}`);
        });
    } else {
      // Локальный режим - обрабатываем ход на клиенте
      // Удаляем карту из руки игрока
      const updatedHands = [...playersHands];
      const playerHand = updatedHands[currentPlayer];
      const cardIndex = playerHand.findIndex((c) => c.id === card.id);

      if (cardIndex !== -1) {
        playerHand.splice(cardIndex, 1);
        updatedHands[currentPlayer] = playerHand;
        setPlayersHands(updatedHands);

        // Добавляем карту на стол
        const newTableCards = [...tableCards];
        if (
          newTableCards.length === 0 ||
          newTableCards[newTableCards.length - 1].length === 2
        ) {
          newTableCards.push([card]);
        } else {
          newTableCards[newTableCards.length - 1].push(card);
        }
        setTableCards(newTableCards);

        setLastAction(
          `Игрок ${currentPlayer + 1} сыграл ${card.rank} ${card.suit}`,
        );

        // Переход хода к следующему игроку
        setCurrentPlayer((currentPlayer + 1) % players);
        setGameMessage(`Ход игрока ${((currentPlayer + 1) % players) + 1}`);
      }
    }

    // Вызываем внешний обработчик, если он есть
    if (onCardClick) {
      onCardClick(card);
    }
  };

  // Обработчик перетаскивания карты
  const handleCardDrag = (card: CardModel, target: "table" | "hand") => {
    if (target === "table" && canPlayCard(card)) {
      handleCardSelect(card);
    }
  };

  // Функция для добора карт из колоды
  const drawCard = () => {
    if (deck.length > 0) {
      const updatedDeck = [...deck];
      const card = updatedDeck.pop();

      if (card) {
        const updatedHands = [...playersHands];
        updatedHands[currentPlayer] = [...updatedHands[currentPlayer], card];

        setDeck(updatedDeck);
        setPlayersHands(updatedHands);
        setLastAction(`Игрок ${currentPlayer + 1} взял карту из колоды`);
      }
    } else {
      setGameMessage("Колода пуста!");
    }
  };

  // Функция для перезапуска игры
  const restartGame = () => {
    setTableCards([]);
    setLastAction("");
    initGame();
  };

  // Функция для отображения подсказки
  const showHint = () => {
    const playerHand = playersHands[currentPlayer] || [];
    const playableCard = playerHand.find((card) => canPlayCard(card));

    if (playableCard) {
      setGameMessage(
        `Подсказка: Вы можете сыграть ${playableCard.rank} ${playableCard.suit}`,
      );
    } else {
      setGameMessage("Подсказка: Возьмите карту из колоды");
    }
  };

  // Функция для автоматического хода компьютера
  useEffect(() => {
    if (
      autoPlay &&
      currentPlayer !== 0 &&
      gameStarted &&
      playersHands.length > 0
    ) {
      const timer = setTimeout(() => {
        const computerHand = playersHands[currentPlayer] || [];
        const playableCard = computerHand.find((card) => canPlayCard(card));

        if (playableCard) {
          handleCardSelect(playableCard);
        } else {
          drawCard();
          setCurrentPlayer((currentPlayer + 1) % players);
          setGameMessage(`Ход игрока ${((currentPlayer + 1) % players) + 1}`);
        }
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [currentPlayer, autoPlay, gameStarted, playersHands]);

  return (
    <TableContainer theme={tableTheme} ref={tableRef}>
      {showHighlight && <ActionHighlight />}

      <GameTitle>Игровой стол</GameTitle>

      {/* Информационная панель */}
      <InfoPanel>
        <GameInfo>
          <InfoItem>Текущий игрок: {currentPlayer + 1}</InfoItem>
          <InfoItem>Карт в колоде: {deck.length}</InfoItem>
          <InfoItem>Последнее действие: {lastAction}</InfoItem>
        </GameInfo>
        <GameMessage>{gameMessage}</GameMessage>
      </InfoPanel>

      {/* Игровой стол с картами */}
      <CardsArea>
        {tableCards.map((pair, pairIndex) => (
          <AttackDefendPair key={`pair-${pairIndex}`}>
            <CardPair>
              {pair[0] && (
                <AttackCardWrapper>
                  <Card
                    suit={pair[0].suit}
                    rank={Number(pair[0].rank)}
                    theme={deckType}
                    effect={cardEffect}
                    enableSound={enableSound}
                    onClick={() => onCardClick && onCardClick(pair[0])}
                  />
                </AttackCardWrapper>
              )}

              {pair[1] && (
                <DefendCardWrapper>
                  <Card
                    suit={pair[1].suit}
                    rank={Number(pair[1].rank)}
                    theme={deckType}
                    effect={cardEffect}
                    enableSound={enableSound}
                    onClick={() => onCardClick && onCardClick(pair[1])}
                  />
                </DefendCardWrapper>
              )}
            </CardPair>
          </AttackDefendPair>
        ))}
      </CardsArea>

      {/* Колода и козырь */}
      <DeckArea>
        {deck.length > 0 && (
          <DeckWrapper onClick={drawCard}>
            {[0, 1, 2, 3, 4].map(
              (index) =>
                index < Math.min(5, deck.length) && (
                  <DeckCardStacked key={`deck-card-${index}`} index={index} />
                ),
            )}
            <DeckCount>{deck.length} карт</DeckCount>
          </DeckWrapper>
        )}

        {trumpCard && (
          <TrumpCardArea>
            <TrumpLabel>Козырь</TrumpLabel>
            <TrumpCardWrapper>
              <Card
                suit={trumpCard.suit}
                rank={Number(trumpCard.rank)}
                theme={deckType}
                effect={cardEffect}
                enableSound={enableSound}
              />
            </TrumpCardWrapper>
          </TrumpCardArea>
        )}
      </DeckArea>

      {/* Руки игроков */}
      {playersHands.length > 0 && (
        <PlayersContainer>
          {playersHands.map((hand, playerIndex) => (
            <PlayerContainer
              key={`player-${playerIndex}`}
              isActive={playerIndex === currentPlayer}
            >
              <PlayerLabel>
                Игрок {playerIndex + 1}
                {playerIndex === 0 ? " (вы)" : ""}
              </PlayerLabel>
              <PlayerHand
                cards={hand.map((card) => ({
                  ...card,
                  isVisible: playerIndex === 0 || autoPlay, // Карты видны только для первого игрока (пользователя)
                }))}
                isActive={playerIndex === currentPlayer}
                onCardSelect={playerIndex === 0 ? handleCardSelect : undefined}
                canPlayCard={canPlayCard}
                sortCards
                groupBySuit
                showPlayableIndicator={showHints}
                compactView={playerIndex !== 0}
                enableDragDrop={enableDragDrop && playerIndex === 0}
                onCardDrop={handleCardDrag}
                showHints={showHints && playerIndex === 0}
                cardSize={playerIndex === 0 ? "medium" : "small"}
              />
            </PlayerContainer>
          ))}
        </PlayersContainer>
      )}

      {/* Панель управления */}
      <ControlPanel>
        <ControlButton onClick={restartGame}>Новая игра</ControlButton>
        {showHints && (
          <ControlButton onClick={showHint}>Подсказка</ControlButton>
        )}
        <ControlButton onClick={() => setShowTutorial(!showTutorial)}>
          {showTutorial ? "Скрыть правила" : "Показать правила"}
        </ControlButton>
      </ControlPanel>

      {/* Обучение */}
      {showTutorial && (
        <TutorialOverlay>
          <TutorialContent>
            <TutorialTitle>Правила игры</TutorialTitle>
            <TutorialText>
              <p>1. Каждый игрок получает по 6 карт.</p>
              <p>2. Первый игрок ходит любой картой.</p>
              <p>
                3. Следующий игрок должен положить карту той же масти или того
                же ранга.
              </p>
              <p>
                4. Если у игрока нет подходящей карты, он берет карту из колоды.
              </p>
              <p>5. Побеждает игрок, который первым избавится от всех карт.</p>
            </TutorialText>
            <CloseButton onClick={() => setShowTutorial(false)}>
              Закрыть
            </CloseButton>
          </TutorialContent>
        </TutorialOverlay>
      )}
    </TableContainer>
  );
};
