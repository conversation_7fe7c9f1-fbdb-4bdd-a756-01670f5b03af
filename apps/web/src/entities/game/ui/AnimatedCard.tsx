import React, { useState, useEffect } from 'react';
import { useSpring, animated, config } from 'react-spring';
import styled from 'styled-components';
import { Card } from '@a1-k/core';
import { CardComponent } from '../../card/ui/CardComponent';

interface AnimatedCardProps {
  card: Card;
  isTrump?: boolean;
  animationType?: 'deal' | 'attack' | 'defend' | 'take' | 'return' | 'flip' | 'hover';
  position?: { x: number; y: number };
  targetPosition?: { x: number; y: number };
  onAnimationComplete?: () => void;
  onClick?: () => void;
  isPlayable?: boolean;
  isSelected?: boolean;
  delay?: number;
  style?: React.CSSProperties;
  className?: string;
}

const CardContainer = styled(animated.div)<{ isPlayable?: boolean; isSelected?: boolean }>`
  position: relative;
  cursor: ${props => props.isPlayable ? 'pointer' : 'default'};
  transform-style: preserve-3d;
  will-change: transform, opacity;
  
  ${props => props.isSelected && `
    box-shadow: 0 0 15px 5px rgba(255, 215, 0, 0.7);
    z-index: 10;
  `}
  
  &:hover {
    z-index: 5;
  }
`;

const CardBack = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  transform: rotateY(180deg);
  background-color: #16213e;
  border-radius: 8px;
  border: 2px solid #0f3460;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
`;

const CardFront = styled.div`
  backface-visibility: hidden;
`;

export const AnimatedCard: React.FC<AnimatedCardProps> = ({
  card,
  isTrump = false,
  animationType = 'hover',
  position = { x: 0, y: 0 },
  targetPosition,
  onAnimationComplete,
  onClick,
  isPlayable = false,
  isSelected = false,
  delay = 0,
  style,
  className
}) => {
  const [isFlipped, setIsFlipped] = useState(animationType === 'flip');
  const [isAnimating, setIsAnimating] = useState(!!animationType && animationType !== 'hover');
  
  // Определяем начальные и конечные значения для анимации в зависимости от типа
  const getAnimationProps = () => {
    const defaultFrom = { 
      x: position.x, 
      y: position.y, 
      opacity: 1, 
      rotateZ: 0, 
      scale: 1 
    };
    
    const defaultTo = { 
      x: targetPosition?.x || position.x, 
      y: targetPosition?.y || position.y, 
      opacity: 1, 
      rotateZ: 0, 
      scale: 1 
    };
    
    switch (animationType) {
      case 'deal':
        return {
          from: { 
            x: position.x - 100, 
            y: position.y - 100, 
            opacity: 0, 
            rotateZ: -10, 
            scale: 0.8 
          },
          to: defaultTo,
          config: { tension: 180, friction: 12 }
        };
      case 'attack':
        return {
          from: defaultFrom,
          to: { 
            ...defaultTo, 
            scale: 1.05, 
            y: targetPosition ? targetPosition.y : position.y - 20 
          },
          config: { tension: 300, friction: 10 }
        };
      case 'defend':
        return {
          from: { 
            ...defaultFrom, 
            rotateZ: 10 
          },
          to: { 
            ...defaultTo, 
            rotateZ: 0 
          },
          config: { tension: 200, friction: 15 }
        };
      case 'take':
        return {
          from: defaultFrom,
          to: { 
            ...defaultTo, 
            scale: 0.8, 
            opacity: 0.7 
          },
          config: { tension: 120, friction: 14 }
        };
      case 'return':
        return {
          from: defaultFrom,
          to: defaultTo,
          config: { tension: 170, friction: 26 }
        };
      case 'flip':
        return {
          from: { rotateY: isFlipped ? 180 : 0 },
          to: { rotateY: isFlipped ? 0 : 180 },
          config: { mass: 1, tension: 500, friction: 80 }
        };
      case 'hover':
      default:
        return {
          from: {},
          to: {},
          config: config.gentle
        };
    }
  };
  
  const { from, to, config: springConfig } = getAnimationProps();
  
  // Основная анимация карты
  const props = useSpring({
    from,
    to,
    config: springConfig,
    delay,
    onRest: () => {
      setIsAnimating(false);
      if (onAnimationComplete) {
        onAnimationComplete();
      }
    }
  });
  
  // Анимация при наведении (только если не выполняется другая анимация)
  const hoverProps = useSpring({
    scale: isPlayable && !isAnimating ? (isSelected ? 1.1 : 1) : 1,
    y: isPlayable && !isAnimating ? (isSelected ? -15 : 0) : 0,
    boxShadow: isPlayable && !isAnimating
      ? (isSelected 
          ? '0 15px 25px rgba(255, 215, 0, 0.5)' 
          : '0 5px 15px rgba(0, 0, 0, 0.2)')
      : '0 2px 5px rgba(0, 0, 0, 0.1)',
    config: { tension: 300, friction: 20 }
  });
  
  // Эффект для анимации переворота карты
  useEffect(() => {
    if (animationType === 'flip') {
      setIsFlipped(!isFlipped);
    }
  }, [animationType]);
  
  // Обработчик клика по карте
  const handleClick = () => {
    if (isPlayable && onClick) {
      onClick();
    }
  };
  
  return (
    <CardContainer
      style={{
        ...style,
        transform: props.rotateY
          ? props.rotateY.to(r => `perspective(1000px) rotateY(${r}deg)`)
          : props.scale && props.rotateZ
            ? props.scale.to((s, r) => `scale(${s}) rotateZ(${r}deg)`)
            : undefined,
        x: props.x,
        y: props.y,
        opacity: props.opacity,
        ...hoverProps
      }}
      isPlayable={isPlayable}
      isSelected={isSelected}
      onClick={handleClick}
      className={className}
    >
      {animationType === 'flip' ? (
        <>
          <CardFront style={{ transform: props.rotateY?.to(r => `rotateY(${r}deg)`) }}>
            <CardComponent card={card} isTrump={isTrump} />
          </CardFront>
          <CardBack />
        </>
      ) : (
        <CardComponent card={card} isTrump={isTrump} />
      )}
    </CardContainer>
  );
};