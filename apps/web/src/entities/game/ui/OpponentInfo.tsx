import React, { <PERSON> } from "react";
import styled from "styled-components";
import { PlayerModel } from "../model/slice";

interface OpponentInfoProps {
  player: PlayerModel;
  isActive: boolean;
}

const OpponentContainer = styled.div<{ isActive: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  margin: 10px;
  border-radius: 8px;
  background-color: ${(props) =>
    props.isActive ? "rgba(255, 255, 200, 0.2)" : "rgba(0, 0, 0, 0.3)"};
  box-shadow: ${(props) =>
    props.isActive
      ? "0 0 10px rgba(255, 255, 0, 0.3)"
      : "0 0 5px rgba(0, 0, 0, 0.2)"};
  transition: all 0.3s ease;
`;

const PlayerName = styled.div`
  font-size: 16px;
  font-weight: bold;
  color: white;
  margin-bottom: 5px;
`;

const PlayerRole = styled.div<{ role: string }>`
  font-size: 14px;
  color: ${(props) => {
    switch (props.role) {
      case "attacker":
        return "#4CAF50";
      case "defender":
        return "#2196F3";
      case "waiting":
        return "#FF9800";
      default:
        return "white";
    }
  }};
  margin-bottom: 10px;
`;

const CardCount = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #16213e;
  border-radius: 5px;
  padding: 5px 10px;
  color: white;
`;

const CardIcon = styled.span`
  margin-right: 5px;
  font-size: 16px;
`;

const getRoleText = (role: string): string => {
  switch (role) {
    case "attacker":
      return "Атакует";
    case "defender":
      return "Защищается";
    case "waiting":
      return "Ожидает";
    default:
      return "";
  }
};

export const OpponentInfo: FC<OpponentInfoProps> = ({ player, isActive }) => {
  return (
    <OpponentContainer isActive={isActive}>
      <PlayerName>{player.name}</PlayerName>
      <PlayerRole role={player.role}>{getRoleText(player.role)}</PlayerRole>
      <CardCount>
        <CardIcon>🃏</CardIcon>
        {player.cards.length}
      </CardCount>
    </OpponentContainer>
  );
};
