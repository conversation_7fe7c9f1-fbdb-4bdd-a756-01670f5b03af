import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '@/app/store';
import { initializeGame, startGame, makeMove } from '../model/durakSlice';
import { Player, GameRules, DurakVariant, PlayerAction, Card } from '@a1-k/core';
import { CardComponent } from '../../card/ui/CardComponent';
// Импортируем общие компоненты и утилиты
import {
  GameWrapper, ErrorMessage, StatusMessage, ActionButton, GameArea,
  GameInfo, TableArea, TableCardsContainer, CardPair, CardWrapper,
  PlayersArea, PlayerInfo, HandContainer, ActionButtonsContainer,
  SyncIndicator, TurnIndicator, TurnTimer, ActionNotification, WinnerDisplay
} from '@/shared/ui/components';
import { ThemeType } from '@/shared/ui/colors';
import { winAnimation, takeCards, slideIn, opponentCardMove } from '@/shared/ui/animations';

// Пример начальных данных (можно получать из API или настроек)
const initialPlayers: Player[] = [
  { id: 'player1', name: 'Игрок 1', hand: [], isActive: false },
  { id: 'player2', name: 'Игрок 2', hand: [], isActive: false },
];

const initialRules: GameRules = {
  variant: DurakVariant.THROWING,
  numberOfPlayers: initialPlayers.length,
  deckSize: 36,
  initialHandSize: 6,
  attackLimit: 6,
};

// Используем общие компоненты и утилиты вместо локальных определений стилей

// Используем типы из файла типов
import { 
  DurakGameComponentProps, 
  GameNotification, 
  CardAnimationState, 
  TableCardState,
  PlayerMoveData,
  GameSettings as GameSettingsType
} from '../model/types';
import { defaultGameSettings } from '../config/gameConfig';
import { GameSettings } from './GameSettings';
import { canPlayCard, findBestCardToPlay } from '../lib/gameUtils';

export const DurakGameComponent: React.FC<DurakGameComponentProps> = ({
  gameState,
  onMove,
  isActivePlayer = true,
  isSyncing = false,
  onGameEnd,
  onSyncError,
  playerName,
  gameId,
  settings: propSettings
}) => {
  // Используем настройки из пропсов или настройки по умолчанию
  const [settings, setSettings] = useState<GameSettingsType>(propSettings || defaultGameSettings);
  
  // Состояние для отображения окна настроек
  const [showSettings, setShowSettings] = useState<boolean>(false);
  
  // Состояние для таймера хода
  const [turnTimeLeft, setTurnTimeLeft] = useState<number>(settings.turnTimerDuration);
  
  // Состояние для уведомлений о действиях
  const [notification, setNotification] = useState<GameNotification>({
    show: false, 
    message: '', 
    type: 'attack'
  });
  
  // Состояние для анимации карт
  const [animatingCard, setAnimatingCard] = useState<number | null>(null);
  const [animationType, setAnimationType] = useState<CardAnimationState['type']>('attack');
  
  // Состояние для отображения победителя
  const [showWinner, setShowWinner] = useState<boolean>(false);
  const [winner, setWinner] = useState<Player | null>(null);
  
  // Состояние для отслеживания новых карт на столе
  const [newTableCards, setNewTableCards] = useState<TableCardState>([]);
  
  // Обновляем настройки при изменении пропсов
  useEffect(() => {
    if (propSettings) {
      setSettings(propSettings);
    }
  }, [propSettings]);
  
  // Обработчик изменения настроек
  const handleSettingsChange = (newSettings: GameSettingsType) => {
    setSettings(newSettings);
    // Если изменилась длительность таймера, обновляем текущий таймер
    if (newSettings.turnTimerDuration !== settings.turnTimerDuration) {
      setTurnTimeLeft(newSettings.turnTimerDuration);
    }
  };
  
  // Функция для подсказки лучшего хода
  const suggestBestMove = () => {
    if (!settings.autoSuggest || !isActivePlayer) return;
    
    const currentPlayer = players[currentPlayerIndex];
    if (!currentPlayer) return;
    
    const isAttacker = currentPlayerIndex === attackerIndex;
    const bestCardIndex = findBestCardToPlay(
      currentPlayer.hand,
      tableCards,
      isAttacker,
      trumpSuit
    );
    
    if (bestCardIndex >= 0) {
      // Подсвечиваем лучшую карту для хода
      setAnimatingCard(bestCardIndex);
      setAnimationType(isAttacker ? 'attack' : 'defend');
      
      // Через 2 секунды убираем подсветку
      setTimeout(() => {
        setAnimatingCard(null);
      }, 2000);
    }
  };
  
  
  const dispatch = useDispatch<AppDispatch>();
  
  // Используем состояние из Redux, если не передано внешнее состояние
  const storeState = useSelector((state: RootState) => state.durak);
  
  // Приоритет отдаем внешнему состоянию (для многопользовательского режима)
  const { 
    players,
    tableCards,
    trumpCard,
    trumpSuit,
    currentPlayerIndex,
    attackerIndex,
    defenderIndex,
    gameStatus,
    error 
  } = gameState || storeState;

  // Инициализация игры при монтировании компонента (только для одиночной игры)
  useEffect(() => {
    // Инициализируем игру только если нет внешнего состояния (не многопользовательский режим)
    if (!gameState) {
      dispatch(initializeGame({ players: initialPlayers, rules: initialRules }));
    }
  }, [dispatch, gameState]);

  // Отслеживаем изменения в tableCards для анимации
  useEffect(() => {
    if (tableCards && tableCards.length > 0) {
      // Инициализируем массив для отслеживания новых карт
      const newCardsState = tableCards.map(pair => {
        return [pair[0] ? true : false, pair[1] ? true : false];
      });
      setNewTableCards(newCardsState);
      
      // Через 500мс сбрасываем флаги новых карт
      const timer = setTimeout(() => {
        setNewTableCards(tableCards.map(() => [false, false]));
      }, 500);
      
      return () => clearTimeout(timer);
    }
  }, [tableCards]);

  // Отслеживаем изменение статуса игры
  useEffect(() => {
    if (gameStatus === 'finished') {
      // Находим победителя (игрок без карт)
      const winningPlayer = players.find(p => p.hand.length === 0);
      if (winningPlayer) {
        setWinner(winningPlayer);
        setShowWinner(true);
        
        // Вызываем колбэк окончания игры, если он предоставлен
        if (onGameEnd) {
          onGameEnd(winningPlayer);
        }
      }
    }
  }, [gameStatus, players, onGameEnd]);

  const handleStartGame = () => {
    if (gameState && onMove) {
      // Для многопользовательского режима
      onMove({ action: 'START_GAME', gameId })
        .catch(err => {
          console.error('Ошибка при запуске игры:', err);
          setNotification({ 
            show: true, 
            message: `Ошибка: ${err.message || 'Не удалось запустить игру'}`, 
            type: 'error' 
          });
          if (onSyncError) onSyncError(err);
        });
    } else {
      // Для одиночного режима
      dispatch(startGame());
    }
    
    // Показываем уведомление о начале игры
    setNotification({ 
      show: true, 
      message: 'Игра началась!', 
      type: 'sync' 
    });
  };

  // Эффект для таймера хода
  useEffect(() => {
    // Запускаем таймер только если игра в процессе и это активный игрок
    if (gameStatus === 'in_progress' && isActivePlayer) {
      const timer = setInterval(() => {
        setTurnTimeLeft((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            // Автоматически берем карты, если время вышло и игрок защищается
            if (currentPlayerIndex === defenderIndex) {
              handlePlayerMove(PlayerAction.TAKE);
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      
      // Сбрасываем таймер при размонтировании или изменении активного игрока
      return () => {
        clearInterval(timer);
      };
    }
  }, [gameStatus, isActivePlayer, currentPlayerIndex, defenderIndex]);
  
  // Сбрасываем таймер при смене хода
  useEffect(() => {
    setTurnTimeLeft(30);
  }, [currentPlayerIndex]);
  
  // Эффект для скрытия уведомления через 3 секунды
  useEffect(() => {
    if (notification.show) {
      const timer = setTimeout(() => {
        setNotification(prev => ({ ...prev, show: false }));
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [notification.show]);

  // Обработчик хода игрока
  const handlePlayerMove = (action: PlayerAction, cardIndex?: number) => {
    const currentPlayer = players[currentPlayerIndex];
    if (!currentPlayer || !isActivePlayer || isSyncing) return;
    
    // Показываем уведомление о действии
    let actionMessage = '';
    let actionType: 'attack' | 'defend' | 'take' | 'pass' | 'error' | 'sync' = 'attack';
    
    switch (action) {
      case PlayerAction.ATTACK:
        actionMessage = 'Атака!';
        actionType = 'attack';
        setAnimationType('attack');
        break;
      case PlayerAction.DEFEND:
        actionMessage = 'Защита!';
        actionType = 'defend';
        setAnimationType('defend');
        break;
      case PlayerAction.TAKE:
        actionMessage = 'Беру карты';
        actionType = 'take';
        setAnimationType('take');
        break;
      case PlayerAction.PASS:
        actionMessage = 'Бито!';
        actionType = 'pass';
        break;
    }
    
    setNotification({ show: true, message: actionMessage, type: actionType });
    
    // Если передан обработчик хода для многопользовательской игры, используем его
    if (onMove && gameState) {
      // Анимируем карту перед отправкой хода
      if (cardIndex !== undefined) {
        setAnimatingCard(cardIndex);
        setTimeout(() => setAnimatingCard(null), 500);
      }
      
      onMove({ playerId: currentPlayer.id, action, cardIndex, gameId })
        .catch(err => {
          console.error('Ошибка при выполнении хода:', err);
          setNotification({ 
            show: true, 
            message: `Ошибка: ${err.message || 'Не удалось выполнить ход'}`, 
            type: 'error' 
          });
          if (onSyncError) onSyncError(err);
        });
    } else {
      // Иначе используем локальное состояние через Redux
      dispatch(makeMove({ playerId: currentPlayer.id, action, cardIndex }));
    }
    
    // Сбрасываем таймер после хода
    setTurnTimeLeft(30);
  };
  
  // Обработчик закрытия окна победителя
  const handleCloseWinner = () => {
    setShowWinner(false);
  };

  return (
    <GameWrapper disabled={isSyncing || !isActivePlayer}>
      {isSyncing && <SyncIndicator>Синхронизация...</SyncIndicator>}
      {gameStatus === 'in_progress' && (
        <TurnIndicator isYourTurn={isActivePlayer}>
          {isActivePlayer ? 'Ваш ход' : 'Ход соперника'}
        </TurnIndicator>
      )}
      {gameStatus === 'in_progress' && isActivePlayer && (
        <TurnTimer timeLeft={turnTimeLeft} />
      )}
      {notification.show && (
        <ActionNotification type={notification.type}>
          {notification.message}
        </ActionNotification>
      )}
      
      <h1>Игра "Дурак"{gameId ? ` #${gameId}` : ''}</h1>
      {error && <ErrorMessage>Ошибка: {error}</ErrorMessage>}
      <StatusMessage>
        Статус игры: {gameStatus === 'not_started' ? 'Ожидание начала' : 
                     gameStatus === 'in_progress' ? 'В процессе' : 'Завершена'}
        {isSyncing && ' (Синхронизация...)'}
        {playerName && ` | Вы играете как: ${playerName}`}
      </StatusMessage>
      {gameStatus === 'not_started' && (
        <ActionButton onClick={handleStartGame} disabled={!isActivePlayer || isSyncing}>
          Начать игру
        </ActionButton>
      )}

      {gameStatus === 'in_progress' && (
        <GameArea>
          <GameInfo>
            <div>
              Козырь: {trumpCard ? <CardComponent card={trumpCard} isTrump={true} /> : 'Не определен'} ({trumpSuit})
            </div>
            <div>Ходит игрок: {players[currentPlayerIndex]?.name}</div>
            <div>Атакующий: {players[attackerIndex]?.name}</div>
            <div>Защищающийся: {players[defenderIndex]?.name}</div>
          </GameInfo>

          <TableArea>
            <h2>Стол:</h2>
            <TableCardsContainer>
              {tableCards.map((pair, index) => (
                <CardPair 
                  key={index} 
                  isNew={newTableCards[index] && (newTableCards[index][0] || newTableCards[index][1])}
                  isDefending={pair[1] && newTableCards[index] && newTableCards[index][1]}
                >
                  <CardWrapper isAnimating={newTableCards[index] && newTableCards[index][0]} animationType="attack">
                    <CardComponent card={pair[0]} isTrump={pair[0].suit === trumpSuit} />
                  </CardWrapper>
                  {pair[1] && (
                    <CardWrapper isAnimating={newTableCards[index] && newTableCards[index][1]} animationType="defend">
                      <CardComponent card={pair[1]} isTrump={pair[1].suit === trumpSuit} />
                    </CardWrapper>
                  )}
                </CardPair>
              ))}
            </TableCardsContainer>
          </TableArea>

          <PlayersArea>
            <h2>Игроки:</h2>
            {players.map((player) => (
              <PlayerInfo 
                key={player.id} 
                isActive={player.isActive} 
                isCurrentPlayer={gameState && player.id === players[currentPlayerIndex]?.id}
              >
                <h3>
                  {player.name} 
                  {player.isActive ? '(Ходит)' : ''}
                  {playerName && player.name === playerName && ' (Вы)'}
                </h3>
                <p>Карты: {player.hand.length}</p>
                <HandContainer isActive={player.isActive && isActivePlayer}>
                  {player.hand.map((card, index) => (
                    <CardWrapper 
                      key={index}
                      isAnimating={animatingCard === index} 
                      animationType={animationType}
                    >
                      <CardComponent
                        card={card}
                        isTrump={card.suit === trumpSuit}
                        onClick={() => {
                          if (player.isActive && isActivePlayer && !isSyncing) {
                            const action = currentPlayerIndex === attackerIndex ? PlayerAction.ATTACK : PlayerAction.DEFEND;
                            handlePlayerMove(action, index);
                          }
                        }}
                        style={{
                          cursor: (player.isActive && isActivePlayer && !isSyncing) ? 'pointer' : 'default',
                          opacity: (player.isActive && isActivePlayer && !isSyncing) ? 1 : 0.8
                        }}
                      />
                    </CardWrapper>
                  ))}
                </HandContainer>
                {/* Кнопки действий */} 
                <ActionButtonsContainer>
                  {player.isActive && currentPlayerIndex === defenderIndex && (
                    <ActionButton 
                      onClick={() => handlePlayerMove(PlayerAction.TAKE)} 
                      color="#ffc107"
                      disabled={!isActivePlayer || isSyncing}
                    >
                      Взять
                    </ActionButton>
                  )}
                  {player.isActive && currentPlayerIndex === attackerIndex && tableCards.length > 0 && tableCards.every(pair => pair.length === 2) && (
                     <ActionButton 
                       onClick={() => handlePlayerMove(PlayerAction.PASS)} 
                       color="#28a745"
                       disabled={!isActivePlayer || isSyncing}
                     >
                       Пас (Бито)
                     </ActionButton>
                  )}
                </ActionButtonsContainer>
              </PlayerInfo>
            ))}
          </PlayersArea>
        </GameArea>
      )}

      {gameStatus === 'finished' && !showWinner && (
        <StatusMessage>Игра окончена! Победитель: {players.find(p => p.hand.length === 0)?.name || 'Не определен (ничья?)'}</StatusMessage>
      )}
      
      {showWinner && (
        <WinnerDisplay>
          <h2>Игра завершена!</h2>
          <p>Победитель: {winner?.name}</p>
          <button onClick={handleCloseWinner}>Закрыть</button>
        </WinnerDisplay>
      )}
    </GameWrapper>
  );
};
};