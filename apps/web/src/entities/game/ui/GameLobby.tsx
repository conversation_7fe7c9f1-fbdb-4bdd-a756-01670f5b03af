import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Player } from '@a1-k/core';

interface GameLobbyProps {
  players: Player[];
  currentPlayerId?: string;
  onJoinGame?: (gameId: string) => void;
  onCreateGame?: () => void;
  onLeaveGame?: () => void;
  availableGames?: Array<{
    id: string;
    name: string;
    players: number;
    maxPlayers: number;
    status: 'waiting' | 'in_progress' | 'finished';
  }>;
  isLoading?: boolean;
}

const LobbyContainer = styled.div`
  display: flex;
  flex-direction: column;
  background-color: #1a1a2e;
  color: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  max-width: 800px;
  margin: 0 auto;
`;

const LobbyHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #333;
  padding-bottom: 10px;

  h2 {
    margin: 0;
    font-size: 1.8rem;
    color: #e94560;
  }
`;

const TabsContainer = styled.div`
  display: flex;
  margin-bottom: 20px;
`;

const Tab = styled.button<{ active: boolean }>`
  padding: 10px 20px;
  background-color: ${props => props.active ? '#e94560' : '#16213e'};
  color: white;
  border: none;
  border-radius: 5px 5px 0 0;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 5px;
  font-weight: ${props => props.active ? 'bold' : 'normal'};

  &:hover {
    background-color: ${props => props.active ? '#e94560' : '#0f3460'};
  }
`;

const GamesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
  max-height: 300px;
  overflow-y: auto;
  padding-right: 10px;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #16213e;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: #0f3460;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #e94560;
  }
`;

const GameItem = styled.div<{ status: string }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #16213e;
  border-radius: 8px;
  transition: all 0.3s ease;
  border-left: 4px solid ${props => {
    switch(props.status) {
      case 'waiting': return '#4cc9f0';
      case 'in_progress': return '#f72585';
      case 'finished': return '#4361ee';
      default: return '#4cc9f0';
    }
  }};

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  }
`;

const GameInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 5px;

  h3 {
    margin: 0;
    font-size: 1.2rem;
  }

  p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.8;
  }
`;

const StatusBadge = styled.span<{ status: string }>`
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  background-color: ${props => {
    switch(props.status) {
      case 'waiting': return '#4cc9f0';
      case 'in_progress': return '#f72585';
      case 'finished': return '#4361ee';
      default: return '#4cc9f0';
    }
  }};
  color: white;
`;

const PlayersList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
`;

const PlayerItem = styled.div<{ isCurrentPlayer: boolean }>`
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background-color: ${props => props.isCurrentPlayer ? '#0f3460' : '#16213e'};
  border-radius: 8px;
  transition: all 0.3s ease;

  ${props => props.isCurrentPlayer && `
    border-left: 4px solid #e94560;
  `}
`;

const PlayerAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #0f3460;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-weight: bold;
  color: white;
`;

const PlayerName = styled.div`
  font-weight: ${props => props.theme.isCurrentPlayer ? 'bold' : 'normal'};
`;

const ActionButton = styled.button<{ primary?: boolean }>`
  padding: 10px 20px;
  background-color: ${props => props.primary ? '#e94560' : '#16213e'};
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;

  &:hover {
    background-color: ${props => props.primary ? '#d63553' : '#0f3460'};
    transform: translateY(-2px);
  }

  &:disabled {
    background-color: #333;
    cursor: not-allowed;
    transform: none;
  }
`;

const ButtonsContainer = styled.div`
  display: flex;
  gap: 10px;
  margin-top: 20px;
  justify-content: center;
`;

const LoadingSpinner = styled.div`
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 4px solid #e94560;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin: 20px auto;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

export const GameLobby: React.FC<GameLobbyProps> = ({
  players,
  currentPlayerId,
  onJoinGame,
  onCreateGame,
  onLeaveGame,
  availableGames = [],
  isLoading = false
}) => {
  const [activeTab, setActiveTab] = useState<'games' | 'players'>('games');
  const [selectedGameId, setSelectedGameId] = useState<string | null>(null);

  const handleJoinGame = () => {
    if (selectedGameId && onJoinGame) {
      onJoinGame(selectedGameId);
    }
  };

  return (
    <LobbyContainer>
      <LobbyHeader>
        <h2>Лобби игры "Дурак"</h2>
      </LobbyHeader>

      <TabsContainer>
        <Tab 
          active={activeTab === 'games'} 
          onClick={() => setActiveTab('games')}
        >
          Доступные игры
        </Tab>
        <Tab 
          active={activeTab === 'players'} 
          onClick={() => setActiveTab('players')}
        >
          Игроки онлайн ({players.length})
        </Tab>
      </TabsContainer>

      {isLoading ? (
        <LoadingSpinner />
      ) : (
        <>
          {activeTab === 'games' && (
            <>
              <GamesList>
                {availableGames.length > 0 ? (
                  availableGames.map(game => (
                    <GameItem 
                      key={game.id} 
                      status={game.status}
                      onClick={() => setSelectedGameId(game.id)}
                      style={{ 
                        border: selectedGameId === game.id ? '2px solid #e94560' : 'none',
                        cursor: 'pointer'
                      }}
                    >
                      <GameInfo>
                        <h3>{game.name}</h3>
                        <p>Игроки: {game.players}/{game.maxPlayers}</p>
                      </GameInfo>
                      <StatusBadge status={game.status}>
                        {game.status === 'waiting' ? 'Ожидание' : 
                         game.status === 'in_progress' ? 'В процессе' : 'Завершена'}
                      </StatusBadge>
                    </GameItem>
                  ))
                ) : (
                  <p style={{ textAlign: 'center', opacity: 0.7 }}>
                    Нет доступных игр. Создайте новую!
                  </p>
                )}
              </GamesList>

              <ButtonsContainer>
                <ActionButton 
                  primary 
                  onClick={onCreateGame}
                >
                  Создать игру
                </ActionButton>
                <ActionButton 
                  onClick={handleJoinGame}
                  disabled={!selectedGameId || !onJoinGame}
                >
                  Присоединиться
                </ActionButton>
              </ButtonsContainer>
            </>
          )}

          {activeTab === 'players' && (
            <PlayersList>
              {players.length > 0 ? (
                players.map(player => (
                  <PlayerItem 
                    key={player.id} 
                    isCurrentPlayer={player.id === currentPlayerId}
                  >
                    <PlayerAvatar>
                      {player.name.charAt(0).toUpperCase()}
                    </PlayerAvatar>
                    <PlayerName>
                      {player.name} {player.id === currentPlayerId ? '(Вы)' : ''}
                    </PlayerName>
                  </PlayerItem>
                ))
              ) : (
                <p style={{ textAlign: 'center', opacity: 0.7 }}>
                  Нет игроков онлайн
                </p>
              )}
            </PlayersList>
          )}
        </>
      )}

      {onLeaveGame && (
        <ActionButton onClick={onLeaveGame} style={{ alignSelf: 'center' }}>
          Выйти из лобби
        </ActionButton>
      )}
    </LobbyContainer>
  );
};