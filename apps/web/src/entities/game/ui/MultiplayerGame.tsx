import React, { useState } from 'react';
import styled from 'styled-components';
import { Player, PlayerAction, Card } from '@a1-k/core';
import { DurakGameComponent } from './DurakGame';
import { GameLobby } from './GameLobby';
import { GameChat } from './GameChat';
import { GameNotifications, GameNotificationItem } from './GameNotifications';
import { GameStats } from './GameStats';
import { MultiplayerGameState, PlayerMoveData, GameSettings } from '../model/types';
import { MultiplayerLobby } from './multiplayer/MultiplayerLobby';
import { ConnectionErrorHandler } from './multiplayer/ConnectionErrorHandler';

interface MultiplayerGameProps {
  gameId?: string;
  playerId: string;
  playerName: string;
  onJoinGame?: (gameId: string) => Promise<void>;
  onCreateGame?: () => Promise<string>;
  onLeaveGame?: () => Promise<void>;
  onSendMessage?: (message: string) => void;
  onPlayerMove?: (moveData: PlayerMoveData) => Promise<any>;
  gameState?: MultiplayerGameState;
  players: Player[];
  availableGames?: Array<{
    id: string;
    name: string;
    players: number;
    maxPlayers: number;
    status: 'waiting' | 'in_progress' | 'finished';
  }>;
  chatMessages?: Array<{
    id: string;
    sender: string;
    senderId: string;
    text: string;
    timestamp: number;
    isSystem?: boolean;
  }>;
  notifications?: GameNotificationItem[];
  moveHistory?: Array<{
    playerId: string;
    playerName: string;
    action: PlayerAction;
    card?: Card;
    targetCard?: Card;
    timestamp: number;
  }>;
  playerStats?: Array<{
    playerId: string;
    playerName: string;
    gamesPlayed: number;
    gamesWon: number;
    attacksCount: number;
    successfulDefends: number;
    cardsPlayed: number;
    cardsTaken: number;
  }>;
  isLoading?: boolean;
  isSyncing?: boolean;
  settings?: GameSettings;
  onSettingsChange?: (settings: GameSettings) => void;
}

const GameContainer = styled.div`
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #1a1a2e;
  color: white;
  position: relative;
`;

const LoadingOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
`;

const LoadingSpinner = styled.div`
  border: 6px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 6px solid #e94560;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const SettingsButton = styled.button`
  position: fixed;
  top: 20px;
  left: 20px;
  background-color: #0f3460;
  color: white;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  cursor: pointer;
  z-index: 90;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;

  &:hover {
    background-color: #e94560;
    transform: rotate(30deg);
  }
`;

export const MultiplayerGame: React.FC<MultiplayerGameProps> = ({
  gameId,
  playerId,
  playerName,
  onJoinGame,
  onCreateGame,
  onLeaveGame,
  onSendMessage,
  onPlayerMove,
  gameState,
  players,
  availableGames = [],
  chatMessages = [],
  notifications = [],
  moveHistory = [],
  playerStats = [],
  isLoading = false,
  isSyncing = false,
  settings,
  onSettingsChange
}) => {
  // Состояние для отображения настроек
  const [showSettings, setShowSettings] = useState<boolean>(false);
  
  // Состояние для отображения статистики
  const [showStats, setShowStats] = useState<boolean>(false);
  
  // Состояние для минимизации чата
  const [isChatMinimized, setIsChatMinimized] = useState<boolean>(true);
  
  // Определяем, является ли текущий игрок активным
  const isActivePlayer = gameState ? 
    gameState.players[gameState.currentPlayerIndex]?.id === playerId : 
    false;
  
  // Обработчик хода игрока
  const handlePlayerMove = async (moveData: PlayerMoveData) => {
    if (onPlayerMove) {
      try {
        return await onPlayerMove({
          ...moveData,
          playerId,
          gameId
        });
      } catch (error) {
        console.error('Ошибка при выполнении хода:', error);
        throw error;
      }
    }
    return Promise.reject(new Error('Обработчик хода не предоставлен'));
  };
  
  // Обработчик окончания игры
  const handleGameEnd = (winner: Player) => {
    // Добавляем системное сообщение в чат
    if (onSendMessage) {
      onSendMessage(`Игра завершена! Победитель: ${winner.name}`);
    }
  };
  
  // Обработчик ошибки синхронизации
  const handleSyncError = (error: Error) => {
    console.error('Ошибка синхронизации:', error);
    // Можно добавить уведомление об ошибке
  };
  
  // Обработчик отправки сообщения в чат
  const handleSendMessage = (text: string) => {
    if (onSendMessage) {
      onSendMessage(text);
    }
  };
  
  // Обработчик изменения настроек
  const handleSettingsChange = (newSettings: GameSettings) => {
    if (onSettingsChange) {
      onSettingsChange(newSettings);
    }
    setShowSettings(false);
  };
  
  return (
    <ConnectionErrorHandler>
      <GameContainer>
        {/* Отображаем лобби, если нет активной игры */}
        {!gameState || gameState.gameStatus === 'not_started' ? (
          !gameId ? (
            <MultiplayerLobby />
          ) : (
            <GameLobby 
              players={players}
              currentPlayerId={playerId}
              onJoinGame={onJoinGame}
              onCreateGame={onCreateGame}
              onLeaveGame={onLeaveGame}
              availableGames={availableGames}
              isLoading={isLoading}
            />
          )
        ) : (
          // Отображаем игру, если есть активная игра
          <DurakGameComponent 
            gameState={gameState}
            onMove={handlePlayerMove}
            isActivePlayer={isActivePlayer}
            isSyncing={isSyncing}
            onGameEnd={handleGameEnd}
            onSyncError={handleSyncError}
            playerName={playerName}
            gameId={gameId}
            settings={settings}
          />
        )}
      
      {/* Чат (отображается всегда) */}
      {onSendMessage && (
        <GameChat 
          messages={chatMessages}
          onSendMessage={handleSendMessage}
          currentPlayerId={playerId}
          currentPlayerName={playerName}
          isMinimized={isChatMinimized}
          onToggleMinimize={() => setIsChatMinimized(!isChatMinimized)}
        />
      )}
      
      {/* Уведомления */}
      <GameNotifications 
        notifications={notifications}
      />
      
      {/* Статистика (только для активной игры) */}
      {gameState && gameState.gameStatus === 'in_progress' && (
        <GameStats 
          players={gameState.players}
          moveHistory={moveHistory}
          playerStats={playerStats}
          currentPlayerId={playerId}
          isExpanded={showStats}
          onToggleExpand={() => setShowStats(!showStats)}
        />
      )}
      
      {/* Кнопка настроек */}
      <SettingsButton onClick={() => setShowSettings(true)}>
        ⚙️
      </SettingsButton>
      
      {/* Индикатор загрузки */}
      {isLoading && (
        <LoadingOverlay>
          <LoadingSpinner />
        </LoadingOverlay>
      )}
    </GameContainer>
    </ConnectionErrorHandler>
  );
};