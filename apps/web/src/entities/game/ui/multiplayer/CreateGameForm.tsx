import React, { useState } from 'react';
import styled from 'styled-components';

interface CreateGameFormProps {
  onCreateGame: (gameData: GameFormData) => Promise<string>;
  isLoading: boolean;
}

export interface GameFormData {
  name: string;
  maxPlayers: number;
  isPrivate: boolean;
  password?: string;
  gameMode: 'classic' | 'fast' | 'team';
  initialCards: number;
}

const CreateGameForm: React.FC<CreateGameFormProps> = ({ onCreateGame, isLoading }) => {
  // Состояние формы
  const [formData, setFormData] = useState<GameFormData>({
    name: `Игра ${Math.floor(Math.random() * 1000)}`,
    maxPlayers: 4,
    isPrivate: false,
    password: '',
    gameMode: 'classic',
    initialCards: 6
  });
  
  // Состояние ошибок валидации
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Обработчик изменения полей формы
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    // Для чекбоксов обрабатываем отдельно
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
      
      // Если приватная игра отключена, сбрасываем пароль
      if (name === 'isPrivate' && !checked) {
        setFormData(prev => ({
          ...prev,
          password: ''
        }));
        
        // Очищаем ошибку пароля, если она была
        if (errors.password) {
          setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors.password;
            return newErrors;
          });
        }
      }
      return;
    }
    
    // Для числовых полей преобразуем значение
    if (type === 'number') {
      setFormData(prev => ({
        ...prev,
        [name]: parseInt(value, 10) || 0
      }));
      return;
    }
    
    // Для остальных полей просто обновляем значение
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Очищаем ошибку поля при изменении
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };
  
  // Валидация формы
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    // Проверка названия игры
    if (!formData.name.trim()) {
      newErrors.name = 'Название игры обязательно';
    } else if (formData.name.length < 3) {
      newErrors.name = 'Название должно содержать минимум 3 символа';
    } else if (formData.name.length > 30) {
      newErrors.name = 'Название не должно превышать 30 символов';
    }
    
    // Проверка количества игроков
    if (formData.maxPlayers < 2) {
      newErrors.maxPlayers = 'Минимум 2 игрока';
    } else if (formData.maxPlayers > 6) {
      newErrors.maxPlayers = 'Максимум 6 игроков';
    }
    
    // Проверка пароля для приватной игры
    if (formData.isPrivate && (!formData.password || formData.password.length < 4)) {
      newErrors.password = 'Пароль должен содержать минимум 4 символа';
    }
    
    // Проверка количества начальных карт
    if (formData.initialCards < 4) {
      newErrors.initialCards = 'Минимум 4 карты';
    } else if (formData.initialCards > 10) {
      newErrors.initialCards = 'Максимум 10 карт';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Обработчик отправки формы
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Валидируем форму перед отправкой
    if (!validateForm()) {
      return;
    }
    
    try {
      // Вызываем функцию создания игры
      await onCreateGame(formData);
      
      // Сбрасываем форму после успешного создания
      setFormData({
        name: `Игра ${Math.floor(Math.random() * 1000)}`,
        maxPlayers: 4,
        isPrivate: false,
        password: '',
        gameMode: 'classic',
        initialCards: 6
      });
    } catch (error) {
      console.error('Ошибка при создании игры:', error);
      // Можно добавить обработку ошибок от сервера
    }
  };

  return (
    <FormContainer>
      <FormHeader>
        <h2>Создать новую игру</h2>
      </FormHeader>
      
      <Form onSubmit={handleSubmit}>
        <FormGroup>
          <Label htmlFor="name">Название игры</Label>
          <Input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            error={!!errors.name}
            placeholder="Введите название игры"
            disabled={isLoading}
          />
          {errors.name && <ErrorMessage>{errors.name}</ErrorMessage>}
        </FormGroup>
        
        <FormRow>
          <FormGroup>
            <Label htmlFor="maxPlayers">Максимум игроков</Label>
            <Input
              type="number"
              id="maxPlayers"
              name="maxPlayers"
              min="2"
              max="6"
              value={formData.maxPlayers}
              onChange={handleChange}
              error={!!errors.maxPlayers}
              disabled={isLoading}
            />
            {errors.maxPlayers && <ErrorMessage>{errors.maxPlayers}</ErrorMessage>}
          </FormGroup>
          
          <FormGroup>
            <Label htmlFor="initialCards">Начальных карт</Label>
            <Input
              type="number"
              id="initialCards"
              name="initialCards"
              min="4"
              max="10"
              value={formData.initialCards}
              onChange={handleChange}
              error={!!errors.initialCards}
              disabled={isLoading}
            />
            {errors.initialCards && <ErrorMessage>{errors.initialCards}</ErrorMessage>}
          </FormGroup>
        </FormRow>
        
        <FormGroup>
          <Label htmlFor="gameMode">Режим игры</Label>
          <Select
            id="gameMode"
            name="gameMode"
            value={formData.gameMode}
            onChange={handleChange}
            disabled={isLoading}
          >
            <option value="classic">Классический</option>
            <option value="fast">Быстрая игра</option>
            <option value="team">Командная игра</option>
          </Select>
        </FormGroup>
        
        <FormGroup>
          <CheckboxContainer>
            <Checkbox
              type="checkbox"
              id="isPrivate"
              name="isPrivate"
              checked={formData.isPrivate}
              onChange={handleChange}
              disabled={isLoading}
            />
            <CheckboxLabel htmlFor="isPrivate">Приватная игра</CheckboxLabel>
          </CheckboxContainer>
        </FormGroup>
        
        {formData.isPrivate && (
          <FormGroup>
            <Label htmlFor="password">Пароль</Label>
            <Input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              error={!!errors.password}
              placeholder="Введите пароль для игры"
              disabled={isLoading}
            />
            {errors.password && <ErrorMessage>{errors.password}</ErrorMessage>}
          </FormGroup>
        )}
        
        <SubmitButton type="submit" disabled={isLoading}>
          {isLoading ? 'Создание...' : 'Создать игру'}
        </SubmitButton>
      </Form>
    </FormContainer>
  );
};

// Стилизованные компоненты
const FormContainer = styled.div`
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
`;

const FormHeader = styled.div`
  margin-bottom: 1.5rem;
  
  h2 {
    margin: 0;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const Label = styled.label`
  font-weight: bold;
  font-size: 0.9rem;
`;

const Input = styled.input<{ error?: boolean }>`
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid ${({ error }) => error ? '#F44336' : '#444'};
  background-color: #333;
  color: white;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: ${({ error }) => error ? '#F44336' : '#2196F3'};
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
`;

const Select = styled.select`
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #333;
  color: white;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: #2196F3;
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const Checkbox = styled.input`
  width: 18px;
  height: 18px;
  cursor: pointer;
  
  &:disabled {
    cursor: not-allowed;
  }
`;

const CheckboxLabel = styled.label`
  cursor: pointer;
`;

const ErrorMessage = styled.div`
  color: #F44336;
  font-size: 0.8rem;
  margin-top: 0.25rem;
`;

const SubmitButton = styled.button`
  padding: 0.75rem 1rem;
  border-radius: 4px;
  border: none;
  background-color: #4CAF50;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 1rem;
  
  &:hover:not(:disabled) {
    background-color: #45a049;
  }
  
  &:disabled {
    background-color: #cccccc;
    color: #666666;
    cursor: not-allowed;
  }
`;

export default CreateGameForm;