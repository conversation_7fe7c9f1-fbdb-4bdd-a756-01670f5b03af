import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useRouter } from 'next/router';

interface Game {
  id: string;
  name: string;
  players: number;
  maxPlayers: number;
  status: 'waiting' | 'in_progress' | 'finished';
  host?: string;
  createdAt?: number;
}

interface GamesListProps {
  games: Game[];
  isLoading: boolean;
  onJoinGame: (gameId: string) => void;
  onRefreshGames: () => void;
}

const GamesList: React.FC<GamesListProps> = ({
  games,
  isLoading,
  onJoinGame,
  onRefreshGames
}) => {
  const router = useRouter();
  const [filteredGames, setFilteredGames] = useState<Game[]>(games);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Обновляем отфильтрованные игры при изменении исходных данных или фильтров
  useEffect(() => {
    let result = [...games];
    
    // Применяем фильтр по поисковому запросу
    if (searchTerm) {
      result = result.filter(game =>
        game.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        game.id.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Применяем фильтр по статусу
    if (statusFilter !== 'all') {
      result = result.filter(game => game.status === statusFilter);
    }
    
    // Применяем сортировку
    result.sort((a, b) => {
      let valueA, valueB;
      
      switch (sortBy) {
        case 'name':
          valueA = a.name;
          valueB = b.name;
          break;
        case 'players':
          valueA = a.players;
          valueB = b.players;
          break;
        case 'createdAt':
        default:
          valueA = a.createdAt || 0;
          valueB = b.createdAt || 0;
          break;
      }
      
      if (valueA < valueB) return sortOrder === 'asc' ? -1 : 1;
      if (valueA > valueB) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
    
    setFilteredGames(result);
  }, [games, searchTerm, statusFilter, sortBy, sortOrder]);

  // Обработчик присоединения к игре
  const handleJoinGame = (gameId: string) => {
    onJoinGame(gameId);
  };

  // Обработчик изменения сортировки
  const handleSortChange = (field: string) => {
    if (sortBy === field) {
      // Если поле то же, меняем порядок сортировки
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // Если поле новое, устанавливаем его и сбрасываем порядок на desc
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  // Получаем иконку для сортировки
  const getSortIcon = (field: string) => {
    if (sortBy !== field) return null;
    return sortOrder === 'asc' ? '↑' : '↓';
  };

  return (
    <GamesListContainer>
      <GamesListHeader>
        <h2>Доступные игры</h2>
        <RefreshButton onClick={onRefreshGames} disabled={isLoading}>
          {isLoading ? 'Обновление...' : 'Обновить список'}
        </RefreshButton>
      </GamesListHeader>
      
      <FiltersContainer>
        <SearchInput
          type="text"
          placeholder="Поиск по названию или ID..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        
        <FilterSelect
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
        >
          <option value="all">Все игры</option>
          <option value="waiting">Ожидание</option>
          <option value="in_progress">В процессе</option>
          <option value="finished">Завершенные</option>
        </FilterSelect>
      </FiltersContainer>
      
      {filteredGames.length > 0 ? (
        <GamesTable>
          <thead>
            <tr>
              <TableHeader onClick={() => handleSortChange('name')}>
                Название {getSortIcon('name')}
              </TableHeader>
              <TableHeader onClick={() => handleSortChange('players')}>
                Игроки {getSortIcon('players')}
              </TableHeader>
              <TableHeader>
                Статус
              </TableHeader>
              <TableHeader>
                Действия
              </TableHeader>
            </tr>
          </thead>
          <tbody>
            {filteredGames.map((game) => (
              <TableRow key={game.id}>
                <TableCell>{game.name}</TableCell>
                <TableCell>{game.players}/{game.maxPlayers}</TableCell>
                <TableCell>
                  <GameStatus status={game.status}>
                    {game.status === 'waiting' && 'Ожидание'}
                    {game.status === 'in_progress' && 'В процессе'}
                    {game.status === 'finished' && 'Завершена'}
                  </GameStatus>
                </TableCell>
                <TableCell>
                  <JoinButton 
                    onClick={() => handleJoinGame(game.id)}
                    disabled={game.status !== 'waiting' || game.players >= game.maxPlayers}
                  >
                    {game.status === 'waiting' && game.players < game.maxPlayers
                      ? 'Присоединиться'
                      : game.status === 'waiting' 
                        ? 'Комната заполнена'
                        : 'Недоступно'}
                  </JoinButton>
                </TableCell>
              </TableRow>
            ))}
          </tbody>
        </GamesTable>
      ) : (
        <EmptyState>
          {isLoading 
            ? 'Загрузка списка игр...'
            : searchTerm || statusFilter !== 'all'
              ? 'Нет игр, соответствующих фильтрам'
              : 'Нет доступных игр. Создайте новую!'}
        </EmptyState>
      )}
    </GamesListContainer>
  );
};

// Стилизованные компоненты
const GamesListContainer = styled.div`
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
`;

const GamesListHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  
  h2 {
    margin: 0;
  }
`;

const RefreshButton = styled.button`
  padding: 0.5rem 1rem;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
  
  &:hover:not(:disabled) {
    background-color: #0b7dda;
  }
  
  &:disabled {
    background-color: #cccccc;
    color: #666666;
    cursor: not-allowed;
  }
`;

const FiltersContainer = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const SearchInput = styled.input`
  flex: 1;
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #333;
  color: white;
  font-size: 1rem;
`;

const FilterSelect = styled.select`
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #333;
  color: white;
  font-size: 1rem;
  min-width: 150px;
`;

const GamesTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
`;

const TableHeader = styled.th`
  text-align: left;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.4);
  }
`;

const TableRow = styled.tr`
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  
  &:last-child {
    border-bottom: none;
  }
`;

const TableCell = styled.td`
  padding: 1rem;
`;

const GameStatus = styled.span<{ status: string }>`
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  background-color: ${({ status }) => 
    status === 'waiting' ? '#FFC107' :
    status === 'in_progress' ? '#2196F3' :
    status === 'finished' ? '#4CAF50' : '#9E9E9E'};
  color: white;
`;

const JoinButton = styled.button`
  padding: 0.5rem 1rem;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
  
  &:hover:not(:disabled) {
    background-color: #45a049;
  }
  
  &:disabled {
    background-color: #cccccc;
    color: #666666;
    cursor: not-allowed;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem 0;
  color: #ccc;
  font-size: 1.2rem;
`;

export default GamesList;