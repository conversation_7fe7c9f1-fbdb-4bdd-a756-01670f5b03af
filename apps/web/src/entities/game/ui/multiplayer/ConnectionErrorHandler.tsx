import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useMultiplayer } from '../../model/MultiplayerContext';

interface ConnectionErrorHandlerProps {
  children: React.ReactNode;
}

const ErrorOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(5px);
`;

const ErrorContainer = styled.div`
  background-color: #2a2a3c;
  border-radius: 10px;
  padding: 30px;
  max-width: 500px;
  width: 100%;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  text-align: center;
`;

const ErrorTitle = styled.h2`
  color: #e94560;
  margin-bottom: 15px;
  font-size: 1.5rem;
`;

const ErrorMessage = styled.p`
  color: #fff;
  margin-bottom: 20px;
  font-size: 1rem;
  line-height: 1.5;
`;

const ReconnectButton = styled.button`
  background-color: #e94560;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;

  &:hover {
    background-color: #d13652;
    transform: translateY(-2px);
  }

  &:disabled {
    background-color: #6c6c7c;
    cursor: not-allowed;
    transform: none;
  }
`;

const ReconnectingMessage = styled.div`
  color: #e94560;
  margin-top: 15px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const Spinner = styled.div`
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 3px solid #e94560;
  width: 20px;
  height: 20px;
  margin-right: 10px;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

/**
 * Компонент для обработки ошибок подключения в многопользовательском режиме
 * Отображает оверлей с ошибкой и кнопкой переподключения
 */
export const ConnectionErrorHandler: React.FC<ConnectionErrorHandlerProps> = ({ children }) => {
  const { connectionError, isConnected, reconnect, isLoading } = useMultiplayer();
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [isReconnecting, setIsReconnecting] = useState(false);
  const [showError, setShowError] = useState(false);
  
  // Задержка перед показом ошибки, чтобы избежать мигания при кратковременных проблемах
  useEffect(() => {
    let timer: NodeJS.Timeout;
    
    if (connectionError && !isConnected) {
      timer = setTimeout(() => {
        setShowError(true);
      }, 2000);
    } else {
      setShowError(false);
    }
    
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [connectionError, isConnected]);
  
  // Автоматическое переподключение при потере соединения
  useEffect(() => {
    let reconnectTimer: NodeJS.Timeout;
    
    if (connectionError && !isConnected && reconnectAttempts < 3) {
      setIsReconnecting(true);
      
      reconnectTimer = setTimeout(async () => {
        try {
          await reconnect();
          setReconnectAttempts(0);
        } catch (error) {
          setReconnectAttempts(prev => prev + 1);
        } finally {
          setIsReconnecting(false);
        }
      }, 5000 * (reconnectAttempts + 1)); // Увеличиваем время между попытками
    }
    
    return () => {
      if (reconnectTimer) clearTimeout(reconnectTimer);
    };
  }, [connectionError, isConnected, reconnect, reconnectAttempts]);
  
  // Обработчик кнопки переподключения
  const handleReconnect = async () => {
    setIsReconnecting(true);
    
    try {
      await reconnect();
      setReconnectAttempts(0);
    } catch (error) {
      setReconnectAttempts(prev => prev + 1);
    } finally {
      setIsReconnecting(false);
    }
  };
  
  // Если нет ошибки или соединение активно, просто отображаем дочерние компоненты
  if (!showError) {
    return <>{children}</>;
  }
  
  return (
    <>
      {children}
      
      <ErrorOverlay>
        <ErrorContainer>
          <ErrorTitle>Ошибка подключения</ErrorTitle>
          <ErrorMessage>
            {connectionError?.message || 'Произошла ошибка при подключении к серверу.'}
            {reconnectAttempts > 0 && (
              <div style={{ marginTop: '10px', color: '#e94560' }}>
                Количество попыток: {reconnectAttempts}/3
              </div>
            )}
          </ErrorMessage>
          
          {isReconnecting || isLoading ? (
            <ReconnectingMessage>
              <Spinner />
              Переподключение...
            </ReconnectingMessage>
          ) : (
            <ReconnectButton 
              onClick={handleReconnect}
              disabled={isReconnecting || isLoading}
            >
              Переподключиться
            </ReconnectButton>
          )}
        </ErrorContainer>
      </ErrorOverlay>
    </>
  );
};