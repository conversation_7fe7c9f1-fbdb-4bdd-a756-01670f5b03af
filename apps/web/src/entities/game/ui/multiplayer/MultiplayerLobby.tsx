import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useRouter } from 'next/router';
import { useMultiplayer } from '../../model/MultiplayerContext';
import { ConnectionErrorHandler } from './ConnectionErrorHandler';

const LobbyContainer = styled.div`
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #1a1a2e;
  color: white;
  padding: 20px;
`;

const LobbyHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #333;
`;

const LobbyTitle = styled.h1`
  font-size: 2rem;
  color: #e94560;
  margin: 0;
`;

const RefreshButton = styled.button`
  background-color: #0f3460;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 15px;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;

  &:hover {
    background-color: #e94560;
  }

  &:disabled {
    background-color: #333;
    cursor: not-allowed;
  }
`;

const LobbyContent = styled.div`
  display: flex;
  flex: 1;
  gap: 20px;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const GamesListSection = styled.div`
  flex: 2;
  background-color: #16213e;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
`;

const CreateGameSection = styled.div`
  flex: 1;
  background-color: #16213e;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
`;

const GamesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 15px;
  max-height: 500px;
  overflow-y: auto;
  padding-right: 10px;
  
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #0f3460;
    border-radius: 10px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #e94560;
    border-radius: 10px;
  }
`;

const GameItem = styled.div`
  background-color: #0f3460;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
`;

const GameInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const GameName = styled.h3`
  margin: 0 0 5px 0;
  font-size: 1.1rem;
`;

const GameStatus = styled.div<{ status: 'waiting' | 'in_progress' | 'finished' }>`
  font-size: 0.8rem;
  padding: 3px 8px;
  border-radius: 10px;
  display: inline-block;
  width: fit-content;
  background-color: ${props => 
    props.status === 'waiting' ? '#4caf50' : 
    props.status === 'in_progress' ? '#ff9800' : '#f44336'};
`;

const GamePlayers = styled.div`
  font-size: 0.9rem;
  color: #ccc;
  margin-top: 5px;
`;

const JoinButton = styled.button`
  background-color: #e94560;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 15px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #d13652;
  }

  &:disabled {
    background-color: #6c6c7c;
    cursor: not-allowed;
  }
`;

const CreateGameButton = styled.button`
  background-color: #e94560;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 12px 20px;
  font-size: 1rem;
  cursor: pointer;
  margin-top: auto;
  transition: all 0.3s ease;

  &:hover {
    background-color: #d13652;
    transform: translateY(-2px);
  }

  &:disabled {
    background-color: #6c6c7c;
    cursor: not-allowed;
    transform: none;
  }
`;

const EmptyGamesMessage = styled.div`
  text-align: center;
  padding: 30px;
  color: #ccc;
  font-style: italic;
`;

const LoadingSpinner = styled.div`
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 4px solid #e94560;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin: 0 auto;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const GameSettingsForm = styled.div`
  margin-bottom: 20px;
`;

const FormGroup = styled.div`
  margin-bottom: 15px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 5px;
  font-size: 0.9rem;
  color: #ccc;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #333;
  background-color: #0f3460;
  color: white;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: #e94560;
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #333;
  background-color: #0f3460;
  color: white;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: #e94560;
  }
`;

/**
 * Компонент лобби для многопользовательского режима
 * Отображает список доступных игр и форму создания новой игры
 */
export const MultiplayerLobby: React.FC = () => {
  const router = useRouter();
  const {
    availableGames,
    isLoading,
    joinGame,
    createGame,
    refreshGames,
    playerName
  } = useMultiplayer();
  
  const [gameName, setGameName] = useState(`Игра ${playerName}`);
  const [maxPlayers, setMaxPlayers] = useState('2');
  
  // Обновляем список игр при монтировании компонента
  useEffect(() => {
    refreshGames();
  }, [refreshGames]);
  
  // Обработчик присоединения к игре
  const handleJoinGame = async (gameId: string) => {
    try {
      await joinGame(gameId);
      router.push(`/multiplayer?gameId=${gameId}`);
    } catch (error) {
      console.error('Ошибка при присоединении к игре:', error);
    }
  };
  
  // Обработчик создания новой игры
  const handleCreateGame = async () => {
    try {
      const newGameId = await createGame();
      router.push(`/multiplayer?gameId=${newGameId}`);
    } catch (error) {
      console.error('Ошибка при создании игры:', error);
    }
  };
  
  return (
    <ConnectionErrorHandler>
      <LobbyContainer>
        <LobbyHeader>
          <LobbyTitle>Многопользовательский режим</LobbyTitle>
          <RefreshButton onClick={refreshGames} disabled={isLoading}>
            {isLoading ? 'Обновление...' : 'Обновить список игр'}
          </RefreshButton>
        </LobbyHeader>
        
        <LobbyContent>
          <GamesListSection>
            <h2>Доступные игры</h2>
            
            {isLoading ? (
              <LoadingSpinner />
            ) : availableGames.length === 0 ? (
              <EmptyGamesMessage>
                Нет доступных игр. Создайте новую игру!
              </EmptyGamesMessage>
            ) : (
              <GamesList>
                {availableGames.map(game => (
                  <GameItem key={game.id}>
                    <GameInfo>
                      <GameName>{game.name}</GameName>
                      <GameStatus status={game.status}>
                        {game.status === 'waiting' ? 'Ожидание' : 
                         game.status === 'in_progress' ? 'В процессе' : 'Завершена'}
                      </GameStatus>
                      <GamePlayers>
                        Игроки: {game.players}/{game.maxPlayers}
                      </GamePlayers>
                    </GameInfo>
                    
                    <JoinButton 
                      onClick={() => handleJoinGame(game.id)}
                      disabled={isLoading || game.status !== 'waiting' || game.players >= game.maxPlayers}
                    >
                      {game.status !== 'waiting' ? 'Недоступно' : 
                       game.players >= game.maxPlayers ? 'Заполнено' : 'Присоединиться'}
                    </JoinButton>
                  </GameItem>
                ))}
              </GamesList>
            )}
          </GamesListSection>
          
          <CreateGameSection>
            <h2>Создать новую игру</h2>
            
            <GameSettingsForm>
              <FormGroup>
                <Label htmlFor="gameName">Название игры</Label>
                <Input 
                  id="gameName"
                  type="text"
                  value={gameName}
                  onChange={(e) => setGameName(e.target.value)}
                  placeholder="Введите название игры"
                />
              </FormGroup>
              
              <FormGroup>
                <Label htmlFor="maxPlayers">Максимальное количество игроков</Label>
                <Select 
                  id="maxPlayers"
                  value={maxPlayers}
                  onChange={(e) => setMaxPlayers(e.target.value)}
                >
                  <option value="2">2 игрока</option>
                  <option value="3">3 игрока</option>
                  <option value="4">4 игрока</option>
                  <option value="6">6 игроков</option>
                </Select>
              </FormGroup>
            </GameSettingsForm>
            
            <CreateGameButton 
              onClick={handleCreateGame}
              disabled={isLoading || !gameName.trim()}
            >
              {isLoading ? 'Создание...' : 'Создать игру'}
            </CreateGameButton>
          </CreateGameSection>
        </LobbyContent>
      </LobbyContainer>
    </ConnectionErrorHandler>
  );
};