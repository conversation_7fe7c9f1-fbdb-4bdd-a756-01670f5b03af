import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { GameSettings as GameSettingsType } from '../../model/types';

interface GameSettingsProps {
  settings: GameSettingsType;
  onSave: (settings: GameSettingsType) => void;
  onCancel?: () => void;
  isVisible: boolean;
}

const GameSettings: React.FC<GameSettingsProps> = ({
  settings,
  onSave,
  onCancel,
  isVisible
}) => {
  const [localSettings, setLocalSettings] = useState<GameSettingsType>(settings);
  const [isDirty, setIsDirty] = useState(false);

  // Обновляем локальные настройки при изменении пропсов
  useEffect(() => {
    setLocalSettings(settings);
    setIsDirty(false);
  }, [settings, isVisible]);

  // Обработчик изменения настроек
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    // Для чекбоксов обрабатываем отдельно
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setLocalSettings(prev => ({
        ...prev,
        [name]: checked
      }));
    }
    // Для числовых полей преобразуем значение
    else if (type === 'number') {
      setLocalSettings(prev => ({
        ...prev,
        [name]: parseInt(value, 10) || 0
      }));
    }
    // Для остальных полей просто обновляем значение
    else {
      setLocalSettings(prev => ({
        ...prev,
        [name]: value
      }));
    }
    
    setIsDirty(true);
  };

  // Обработчик сохранения настроек
  const handleSave = () => {
    onSave(localSettings);
    setIsDirty(false);
  };

  // Обработчик отмены изменений
  const handleCancel = () => {
    setLocalSettings(settings);
    setIsDirty(false);
    if (onCancel) onCancel();
  };

  if (!isVisible) return null;

  return (
    <SettingsOverlay>
      <SettingsContainer>
        <SettingsHeader>
          <h2>Настройки игры</h2>
          <CloseButton onClick={handleCancel}>×</CloseButton>
        </SettingsHeader>
        
        <SettingsContent>
          <SettingsSection>
            <SectionTitle>Игровой процесс</SectionTitle>
            
            <FormGroup>
              <Label htmlFor="initialCards">Начальное количество карт</Label>
              <Input
                type="number"
                id="initialCards"
                name="initialCards"
                min="4"
                max="10"
                value={localSettings.initialCards}
                onChange={handleChange}
              />
            </FormGroup>
            
            <FormGroup>
              <Label htmlFor="trumpDetermination">Определение козыря</Label>
              <Select
                id="trumpDetermination"
                name="trumpDetermination"
                value={localSettings.trumpDetermination}
                onChange={handleChange}
              >
                <option value="random">Случайная масть</option>
                <option value="lastCard">Последняя карта колоды</option>
                <option value="firstCard">Первая карта колоды</option>
              </Select>
            </FormGroup>
            
            <FormGroup>
              <CheckboxContainer>
                <Checkbox
                  type="checkbox"
                  id="allowFirstAttackWithTrump"
                  name="allowFirstAttackWithTrump"
                  checked={localSettings.allowFirstAttackWithTrump}
                  onChange={handleChange}
                />
                <CheckboxLabel htmlFor="allowFirstAttackWithTrump">
                  Разрешить первый ход козырем
                </CheckboxLabel>
              </CheckboxContainer>
            </FormGroup>
          </SettingsSection>
          
          <SettingsSection>
            <SectionTitle>Визуальные настройки</SectionTitle>
            
            <FormGroup>
              <Label htmlFor="cardTheme">Тема карт</Label>
              <Select
                id="cardTheme"
                name="cardTheme"
                value={localSettings.cardTheme}
                onChange={handleChange}
              >
                <option value="classic">Классическая</option>
                <option value="modern">Современная</option>
                <option value="retro">Ретро</option>
              </Select>
            </FormGroup>
            
            <FormGroup>
              <Label htmlFor="tableBackground">Фон стола</Label>
              <Select
                id="tableBackground"
                name="tableBackground"
                value={localSettings.tableBackground}
                onChange={handleChange}
              >
                <option value="green">Зеленый</option>
                <option value="blue">Синий</option>
                <option value="red">Красный</option>
                <option value="dark">Темный</option>
              </Select>
            </FormGroup>
            
            <FormGroup>
              <CheckboxContainer>
                <Checkbox
                  type="checkbox"
                  id="showAnimations"
                  name="showAnimations"
                  checked={localSettings.showAnimations}
                  onChange={handleChange}
                />
                <CheckboxLabel htmlFor="showAnimations">
                  Показывать анимации
                </CheckboxLabel>
              </CheckboxContainer>
            </FormGroup>
          </SettingsSection>
          
          <SettingsSection>
            <SectionTitle>Звук</SectionTitle>
            
            <FormGroup>
              <CheckboxContainer>
                <Checkbox
                  type="checkbox"
                  id="soundEnabled"
                  name="soundEnabled"
                  checked={localSettings.soundEnabled}
                  onChange={handleChange}
                />
                <CheckboxLabel htmlFor="soundEnabled">
                  Включить звуки
                </CheckboxLabel>
              </CheckboxContainer>
            </FormGroup>
            
            {localSettings.soundEnabled && (
              <FormGroup>
                <Label htmlFor="soundVolume">Громкость</Label>
                <RangeContainer>
                  <RangeInput
                    type="range"
                    id="soundVolume"
                    name="soundVolume"
                    min="0"
                    max="100"
                    value={localSettings.soundVolume}
                    onChange={handleChange}
                  />
                  <RangeValue>{localSettings.soundVolume}%</RangeValue>
                </RangeContainer>
              </FormGroup>
            )}
            
            <FormGroup>
              <CheckboxContainer>
                <Checkbox
                  type="checkbox"
                  id="musicEnabled"
                  name="musicEnabled"
                  checked={localSettings.musicEnabled}
                  onChange={handleChange}
                />
                <CheckboxLabel htmlFor="musicEnabled">
                  Включить музыку
                </CheckboxLabel>
              </CheckboxContainer>
            </FormGroup>
            
            {localSettings.musicEnabled && (
              <FormGroup>
                <Label htmlFor="musicVolume">Громкость музыки</Label>
                <RangeContainer>
                  <RangeInput
                    type="range"
                    id="musicVolume"
                    name="musicVolume"
                    min="0"
                    max="100"
                    value={localSettings.musicVolume}
                    onChange={handleChange}
                  />
                  <RangeValue>{localSettings.musicVolume}%</RangeValue>
                </RangeContainer>
              </FormGroup>
            )}
          </SettingsSection>
        </SettingsContent>
        
        <SettingsFooter>
          <SaveButton onClick={handleSave} disabled={!isDirty}>
            Сохранить настройки
          </SaveButton>
          <CancelButton onClick={handleCancel}>
            Отмена
          </CancelButton>
        </SettingsFooter>
      </SettingsContainer>
    </SettingsOverlay>
  );
};

// Стилизованные компоненты
const SettingsOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
`;

const SettingsContainer = styled.div`
  background-color: #1a1a2e;
  border-radius: 8px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const SettingsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background-color: #0f3460;
  
  h2 {
    margin: 0;
    color: white;
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
  opacity: 0.7;
  transition: opacity 0.2s;
  
  &:hover {
    opacity: 1;
  }
`;

const SettingsContent = styled.div`
  padding: 1.5rem;
  color: white;
  overflow-y: auto;
  flex: 1;
`;

const SettingsSection = styled.div`
  margin-bottom: 2rem;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const SectionTitle = styled.h3`
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #4CAF50;
`;

const FormGroup = styled.div`
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-weight: bold;
  font-size: 0.9rem;
`;

const Input = styled.input`
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #333;
  color: white;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: #2196F3;
  }
`;

const Select = styled.select`
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #333;
  color: white;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: #2196F3;
  }
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const Checkbox = styled.input`
  width: 18px;
  height: 18px;
  cursor: pointer;
`;

const CheckboxLabel = styled.label`
  cursor: pointer;
`;

const RangeContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const RangeInput = styled.input`
  flex: 1;
  height: 6px;
  -webkit-appearance: none;
  appearance: none;
  background: #444;
  border-radius: 3px;
  outline: none;
  
  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #2196F3;
    cursor: pointer;
  }
  
  &::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #2196F3;
    cursor: pointer;
    border: none;
  }
`;

const RangeValue = styled.div`
  min-width: 40px;
  text-align: right;
  font-size: 0.9rem;
`;

const SettingsFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  background-color: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
`;

const SaveButton = styled.button`
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  border: none;
  background-color: #4CAF50;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover:not(:disabled) {
    background-color: #45a049;
  }
  
  &:disabled {
    background-color: #cccccc;
    color: #666666;
    cursor: not-allowed;
  }
`;

const CancelButton = styled.button`
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  border: none;
  background-color: #9E9E9E;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #757575;
  }
`;

export default GameSettings;