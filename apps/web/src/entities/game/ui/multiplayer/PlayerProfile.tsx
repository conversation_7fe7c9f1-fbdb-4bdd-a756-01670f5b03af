import React, { useState } from 'react';
import styled from 'styled-components';

interface PlayerStats {
  playerId: string;
  playerName: string;
  gamesPlayed: number;
  gamesWon: number;
  attacksCount: number;
  successfulDefends: number;
  cardsPlayed: number;
  cardsTaken: number;
}

interface PlayerProfileProps {
  playerId: string;
  playerName: string;
  stats?: PlayerStats;
  avatarUrl?: string;
  onUpdateProfile?: (data: { name: string; avatarUrl?: string }) => Promise<void>;
  onLogout?: () => void;
}

const PlayerProfile: React.FC<PlayerProfileProps> = ({
  playerId,
  playerName,
  stats,
  avatarUrl,
  onUpdateProfile,
  onLogout
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [newName, setNewName] = useState(playerName);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Вычисляем процент побед
  const winRate = stats ? Math.round((stats.gamesWon / Math.max(stats.gamesPlayed, 1)) * 100) : 0;

  // Обработчик сохранения изменений профиля
  const handleSaveProfile = async () => {
    if (!onUpdateProfile) return;
    if (!newName.trim()) {
      setError('Имя не может быть пустым');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await onUpdateProfile({ name: newName.trim() });
      setIsEditing(false);
    } catch (err) {
      setError('Не удалось обновить профиль');
      console.error('Ошибка при обновлении профиля:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ProfileContainer>
      <ProfileHeader>
        <h2>Профиль игрока</h2>
        {onLogout && (
          <LogoutButton onClick={onLogout}>
            Выйти
          </LogoutButton>
        )}
      </ProfileHeader>

      <ProfileContent>
        <AvatarSection>
          <Avatar src={avatarUrl || '/default-avatar.png'} alt={playerName} />
          <PlayerID>ID: {playerId}</PlayerID>
        </AvatarSection>

        <InfoSection>
          {isEditing ? (
            <EditForm>
              <FormGroup>
                <Label>Имя игрока</Label>
                <Input
                  type="text"
                  value={newName}
                  onChange={(e) => setNewName(e.target.value)}
                  disabled={isLoading}
                />
                {error && <ErrorMessage>{error}</ErrorMessage>}
              </FormGroup>
              <ButtonGroup>
                <SaveButton onClick={handleSaveProfile} disabled={isLoading}>
                  {isLoading ? 'Сохранение...' : 'Сохранить'}
                </SaveButton>
                <CancelButton onClick={() => {
                  setIsEditing(false);
                  setNewName(playerName);
                  setError(null);
                }} disabled={isLoading}>
                  Отмена
                </CancelButton>
              </ButtonGroup>
            </EditForm>
          ) : (
            <>
              <PlayerName>{playerName}</PlayerName>
              {onUpdateProfile && (
                <EditButton onClick={() => setIsEditing(true)}>
                  Редактировать
                </EditButton>
              )}
            </>
          )}
        </InfoSection>
      </ProfileContent>

      {stats && (
        <StatsSection>
          <StatsHeader>
            <h3>Статистика игрока</h3>
          </StatsHeader>
          <StatsGrid>
            <StatItem>
              <StatLabel>Игр сыграно</StatLabel>
              <StatValue>{stats.gamesPlayed}</StatValue>
            </StatItem>
            <StatItem>
              <StatLabel>Побед</StatLabel>
              <StatValue>{stats.gamesWon}</StatValue>
            </StatItem>
            <StatItem>
              <StatLabel>Процент побед</StatLabel>
              <StatValue>{winRate}%</StatValue>
            </StatItem>
            <StatItem>
              <StatLabel>Атак</StatLabel>
              <StatValue>{stats.attacksCount}</StatValue>
            </StatItem>
            <StatItem>
              <StatLabel>Успешных защит</StatLabel>
              <StatValue>{stats.successfulDefends}</StatValue>
            </StatItem>
            <StatItem>
              <StatLabel>Карт сыграно</StatLabel>
              <StatValue>{stats.cardsPlayed}</StatValue>
            </StatItem>
            <StatItem>
              <StatLabel>Карт взято</StatLabel>
              <StatValue>{stats.cardsTaken}</StatValue>
            </StatItem>
          </StatsGrid>
        </StatsSection>
      )}
    </ProfileContainer>
  );
};

// Стилизованные компоненты
const ProfileContainer = styled.div`
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 1.5rem;
  color: white;
`;

const ProfileHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  
  h2 {
    margin: 0;
  }
`;

const LogoutButton = styled.button`
  padding: 0.5rem 1rem;
  background-color: #F44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #d32f2f;
  }
`;

const ProfileContent = styled.div`
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
`;

const AvatarSection = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const Avatar = styled.img`
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #2196F3;
  margin-bottom: 0.5rem;
`;

const PlayerID = styled.div`
  font-size: 0.8rem;
  color: #aaa;
`;

const InfoSection = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

const PlayerName = styled.h3`
  font-size: 1.5rem;
  margin: 0 0 1rem 0;
`;

const EditButton = styled.button`
  align-self: flex-start;
  padding: 0.5rem 1rem;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #0b7dda;
  }
  
  @media (max-width: 768px) {
    align-self: center;
  }
`;

const EditForm = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-weight: bold;
  font-size: 0.9rem;
`;

const Input = styled.input`
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #333;
  color: white;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: #2196F3;
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
`;

const ErrorMessage = styled.div`
  color: #F44336;
  font-size: 0.8rem;
  margin-top: 0.25rem;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  
  @media (max-width: 768px) {
    justify-content: center;
  }
`;

const SaveButton = styled.button`
  padding: 0.75rem 1rem;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
  
  &:hover:not(:disabled) {
    background-color: #45a049;
  }
  
  &:disabled {
    background-color: #cccccc;
    color: #666666;
    cursor: not-allowed;
  }
`;

const CancelButton = styled.button`
  padding: 0.75rem 1rem;
  background-color: #9E9E9E;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
  
  &:hover:not(:disabled) {
    background-color: #757575;
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
`;

const StatsSection = styled.div`
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1.5rem;
`;

const StatsHeader = styled.div`
  margin-bottom: 1.5rem;
  
  h3 {
    margin: 0;
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1.5rem;
`;

const StatItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
`;

const StatLabel = styled.div`
  font-size: 0.9rem;
  color: #ccc;
  margin-bottom: 0.5rem;
`;

const StatValue = styled.div`
  font-size: 1.5rem;
  font-weight: bold;
  color: #4CAF50;
`;

export default PlayerProfile;