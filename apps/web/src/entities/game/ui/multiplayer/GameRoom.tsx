import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import styled from 'styled-components';
import { DurakGameComponent } from '../DurakGame';
import { useMultiplayer } from '../../model/MultiplayerContext';
import { ConnectionErrorHandler } from './ConnectionErrorHandler';
import { useMultiplayerGame } from '../../model/hooks/useMultiplayerGame';
import { GameState } from '../../model/types';

interface GameRoomProps {
  roomId?: string;
}

interface PlayerType {
  id: string;
  name: string;
  isReady: boolean;
}

interface RoomType {
  id: string;
  name: string;
  host: string;
  playerId: string;
  players: PlayerType[];
  status: 'waiting' | 'playing' | 'finished';
  maxPlayers: number;
}

interface ChatMessageType {
  playerName: string;
  message: string;
  timestamp: number;
}

interface GamePlayComponentProps {
  gameState: GameState;
  playerId: string;
  playerName: string;
  gameId: string;
  isSyncing: boolean;
  onMove: (moveData: any) => Promise<any>;
  onLeave: () => void;
}

// Компонент для отображения игрового процесса
const GamePlayComponent: React.FC<GamePlayComponentProps> = ({ 
  gameState, 
  playerId, 
  playerName, 
  gameId, 
  isSyncing, 
  onMove, 
  onLeave 
}) => {
  // Обработчик хода в игре для синхронизации с другими игроками
  const handleGameMove = (moveData: any) => {
    if (!gameState || gameState.gameStatus !== 'in_progress') {
      return Promise.reject(new Error('Невозможно сделать ход'));
    }
    
    return onMove({
      ...moveData,
      playerId,
      gameId
    });
  };

  return (
    <GameContainer>
      <GameHeader>
        <h1>Игра в процессе</h1>
        <GameStatusIndicator>
          {isSyncing ? 'Синхронизация...' : 
           (gameState.players[gameState.currentPlayerIndex]?.id === playerId) ? 'Ваш ход' : 'Ход соперника'}
        </GameStatusIndicator>
      </GameHeader>
      
      <DurakGameComponent 
        gameState={gameState} 
        onMove={handleGameMove}
        isActivePlayer={gameState.players[gameState.currentPlayerIndex]?.id === playerId}
        isSyncing={isSyncing}
        playerName={playerName}
        gameId={gameId}
      />
      
      <GameControls>
        <LeaveButton onClick={onLeave}>
          Выйти из игры
        </LeaveButton>
      </GameControls>
    </GameContainer>
  );
};

interface PlayersListComponentProps {
  room: RoomType;
  isHost: boolean;
  allPlayersReady: boolean;
  enoughPlayers: boolean;
  onToggleReady: () => void;
  onStartGame: () => void;
  onLeaveRoom: () => void;
}

// Компонент для отображения списка игроков
const PlayersListComponent: React.FC<PlayersListComponentProps> = ({ 
  room, 
  isHost, 
  allPlayersReady, 
  enoughPlayers, 
  onToggleReady, 
  onStartGame, 
  onLeaveRoom 
}) => {
  return (
    <PlayersSection>
      <h2>Игроки</h2>
      <PlayersList>
        {room.players.map((player) => (
          <PlayerItem key={player.id}>
            <PlayerInfo>
              <PlayerName>
                {player.name}
                {player.id === room.host && ' (Хост)'}
                {player.id === room.playerId && ' (Вы)'}
              </PlayerName>
              <PlayerStatus isReady={player.isReady}>
                {player.isReady ? 'Готов' : 'Не готов'}
              </PlayerStatus>
            </PlayerInfo>
          </PlayerItem>
        ))}
      </PlayersList>
      
      <RoomStatus>
        <div>Статус комнаты: {room.status === 'waiting' ? 'Ожидание' : 'Игра'}</div>
        <div>Игроков: {room.players.length}/{room.maxPlayers}</div>
      </RoomStatus>
      
      <RoomActions>
        <ActionButton onClick={onToggleReady}>
          {room.players.find((p) => p.id === room.playerId)?.isReady 
            ? 'Отменить готовность' 
            : 'Готов к игре'}
        </ActionButton>
        
        {isHost && (
          <StartGameButton 
            onClick={onStartGame}
            disabled={!allPlayersReady || !enoughPlayers}
          >
            {!enoughPlayers 
              ? 'Нужно минимум 2 игрока' 
              : !allPlayersReady 
              ? 'Не все игроки готовы' 
              : 'Начать игру'}
          </StartGameButton>
        )}
        
        <LeaveButton onClick={onLeaveRoom}>
          Покинуть комнату
        </LeaveButton>
      </RoomActions>
    </PlayersSection>
  );
};

interface ChatComponentProps {
  messages: ChatMessageType[];
  chatMessage: string;
  setChatMessage: (message: string) => void;
  onSendMessage: () => void;
}

// Компонент для отображения чата
const ChatComponent: React.FC<ChatComponentProps> = ({ 
  messages, 
  chatMessage, 
  setChatMessage, 
  onSendMessage 
}) => {
  return (
    <ChatSection>
      <h2>Чат комнаты</h2>
      <ChatMessages>
        {messages.length === 0 ? (
          <EmptyChatMessage>
            Сообщений пока нет. Будьте первым!
          </EmptyChatMessage>
        ) : (
          messages.map((msg, index) => (
            <ChatMessage key={index}>
              <ChatMessageHeader>
                <ChatMessageAuthor>{msg.playerName}</ChatMessageAuthor>
                <ChatMessageTime>
                  {new Date(msg.timestamp).toLocaleTimeString()}
                </ChatMessageTime>
              </ChatMessageHeader>
              <ChatMessageContent>{msg.message}</ChatMessageContent>
            </ChatMessage>
          ))
        )}
      </ChatMessages>
      <ChatInputContainer>
        <ChatInput
          type="text"
          value={chatMessage}
          onChange={(e) => setChatMessage(e.target.value)}
          placeholder="Введите сообщение..."
          onKeyPress={(e) => e.key === 'Enter' && onSendMessage()}
        />
        <ChatSendButton onClick={onSendMessage}>
          Отправить
        </ChatSendButton>
      </ChatInputContainer>
    </ChatSection>
  );
};

// Основной компонент игровой комнаты
const GameRoom: React.FC<GameRoomProps> = ({ roomId }) => {
  const router = useRouter();
  const [chatMessage, setChatMessage] = useState('');
  
  // Используем ID комнаты из пропсов или из URL
  const roomIdFromRouter = router.query.id as string;
  const currentRoomId = roomId || roomIdFromRouter;
  
  // Используем контекст для доступа к состоянию и методам
  const {
    gameId,
    playerId,
    playerName,
    players,
    gameState,
    chatMessages: messages,
    isLoading,
    isSyncing,
    isConnected,
    connectionError: error,
    joinGame,
    leaveGame,
    sendMessage,
    playerMove: syncMove,
    currentRoom
  } = useMultiplayer();
  
  // Используем хук для управления многопользовательской игрой
  const {
    isHost,
    allPlayersReady,
    enoughPlayers,
    setReady
  } = useMultiplayerGame({
    serverUrl: process.env.NEXT_PUBLIC_MULTIPLAYER_API_URL || 'http://localhost:3001',
    roomId: currentRoomId,
    playerName,
    autoConnect: false // Подключение управляется через контекст
  });

  // Подключаемся к серверу при монтировании компонента
  useEffect(() => {
    if (!currentRoomId) {
      router.push('/multiplayer');
      return;
    }
    
    // Присоединяемся к игре, если есть ID комнаты
    if (currentRoomId && currentRoomId !== gameId) {
      joinGame(currentRoomId);
    }
    
    // Отключаемся от комнаты при размонтировании компонента
    return () => {
      if (gameId) {
        leaveGame();
      }
    };
  }, [currentRoomId, gameId, joinGame, leaveGame, router]);
  
  // Обработчик ошибок подключения
  useEffect(() => {
    if (error) {
      console.error('Ошибка в игровой комнате:', error);
      if (error.message.includes('не найдена') || error.message.includes('not found')) {
        router.push('/multiplayer');
      }
    }
  }, [error, router]);

  // Обработчик отправки сообщения в чат
  const handleSendMessage = () => {
    if (!chatMessage.trim()) return;
    
    sendMessage(chatMessage);
    setChatMessage('');
  };

  // Обработчик изменения статуса готовности
  const handleToggleReady = () => {
    if (currentRoom) {
      const isReady = currentRoom.players.find(p => p.id === playerId)?.isReady;
      // Вызываем метод из контекста для изменения статуса готовности
      setReady(!isReady);
    }
  };

  // Обработчик начала игры (только для хоста)
  const handleStartGame = () => {
    if (isHost && allPlayersReady && enoughPlayers) {
      // Вызываем метод из контекста для начала игры
      // В реальной реализации здесь будет вызов метода startGame из контекста
      console.log('Начало игры');
      // Здесь должен быть вызов метода startGame из контекста
      // startGame();
    }
  };
  
  // Обработчик хода в игре для синхронизации с другими игроками
  const handleGameMove = (moveData: any) => {
    return syncMove(moveData);
  };

  // Обработчик выхода из комнаты
  const handleLeaveRoom = () => {
    leaveGame();
    router.push('/multiplayer');
  };

  // Если игра уже началась, показываем игровой компонент
  if (gameState && gameState.gameStatus === 'in_progress') {
    return (
      <ConnectionErrorHandler>
        <GamePlayComponent
          gameState={gameState}
          playerId={playerId}
          playerName={playerName}
          gameId={gameId}
          isSyncing={isSyncing}
          onMove={handleGameMove}
          onLeave={handleLeaveRoom}
        />
      </ConnectionErrorHandler>
    );
  }

  return (
    <ConnectionErrorHandler>
      <RoomContainer>
        <RoomHeader>
          <h1>Комната #{gameId || 'Загрузка...'}</h1>
          <ConnectionStatus status={isConnected ? 'connected' : 'error'}>
            {isConnected ? 'Подключено' : 'Ошибка подключения'}
          </ConnectionStatus>
        </RoomHeader>

      {currentRoom ? (
        <RoomContent>
          <PlayersListComponent
            room={currentRoom}
            isHost={isHost}
            allPlayersReady={allPlayersReady}
            enoughPlayers={enoughPlayers}
            onToggleReady={handleToggleReady}
            onStartGame={handleStartGame}
            onLeaveRoom={handleLeaveRoom}
          />
          
          <ChatComponent
            messages={messages}
            chatMessage={chatMessage}
            setChatMessage={setChatMessage}
            onSendMessage={handleSendMessage}
          />
        </RoomContent>
      ) : (
        <LoadingContainer>
          <LoadingMessage>Загрузка комнаты...</LoadingMessage>
        </LoadingContainer>
      )}
    </RoomContainer>
  );
};

// Стилизованные компоненты
const RoomContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  color: white;
  min-height: 100vh;
`;

const RoomHeader = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  h1 {
    margin: 0;
    font-size: 2rem;
  }
`;

const ConnectionStatus = styled.div<{ status: string }>`
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: bold;
  background-color: ${({ status }) => 
    status === 'connected' ? '#4CAF50' :
    status === 'connecting' ? '#FFC107' :
    status === 'error' ? '#F44336' : '#9E9E9E'};
`;

const ErrorMessage = styled.div`
  background-color: #F44336;
  color: white;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
`;

const LoadingMessage = styled.div`
  font-size: 1.5rem;
  color: #ccc;
`;

const RoomContent = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const PlayersSection = styled.section`
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 1.5rem;
  
  h2 {
    margin-top: 0;
    margin-bottom: 1rem;
  }
`;

const PlayersList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0 0 1.5rem 0;
`;

const PlayerItem = styled.li`
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  margin-bottom: 0.5rem;
`;

const PlayerInfo = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const PlayerName = styled.div`
  font-weight: bold;
`;

const PlayerStatus = styled.div<{ isReady: boolean }>`
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  background-color: ${({ isReady }) => isReady ? '#4CAF50' : '#F44336'};
  color: white;
`;

const RoomStatus = styled.div`
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  
  div {
    margin-bottom: 0.5rem;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
`;

const RoomActions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const ActionButton = styled.button`
  padding: 0.75rem 1rem;
  border-radius: 4px;
  border: none;
  background-color: #2196F3;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover:not(:disabled) {
    background-color: #0b7dda;
  }
  
  &:disabled {
    background-color: #cccccc;
    color: #666666;
    cursor: not-allowed;
  }
`;

const StartGameButton = styled(ActionButton)`
  background-color: #4CAF50;
  
  &:hover:not(:disabled) {
    background-color: #45a049;
  }
`;

const LeaveButton = styled(ActionButton)`
  background-color: #F44336;
  
  &:hover:not(:disabled) {
    background-color: #d32f2f;
  }
`;

const ChatSection = styled.section`
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  height: 100%;
  
  h2 {
    margin-top: 0;
    margin-bottom: 1rem;
  }
`;

const ChatMessages = styled.div`
  flex: 1;
  overflow-y: auto;
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  max-height: 400px;
`;

const EmptyChatMessage = styled.div`
  text-align: center;
  color: #ccc;
  padding: 2rem 0;
`;

const ChatMessage = styled.div`
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  
  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
`;

const ChatMessageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
`;

const ChatMessageAuthor = styled.div`
  font-weight: bold;
`;

const ChatMessageTime = styled.div`
  font-size: 0.8rem;
  color: #ccc;
`;

const ChatMessageContent = styled.div`
  word-break: break-word;
`;

const ChatInputContainer = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const ChatInput = styled.input`
  flex: 1;
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #333;
  color: white;
  font-size: 1rem;
`;

const ChatSendButton = styled.button`
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  border: none;
  background-color: #4CAF50;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  
  &:hover {
    background-color: #45a049;
  }
`;

const GameContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: #16213e;
  border-radius: 8px;
  overflow: hidden;
`;

const GameHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #0f3460;
  border-bottom: 1px solid #1a1a2e;
  
  h1 {
    margin: 0;
    font-size: 1.5rem;
    color: white;
  }
`;

const GameStatusIndicator = styled.div`
  padding: 0.5rem 1rem;
  background-color: #e94560;
  border-radius: 4px;
  font-weight: bold;
  color: white;
`;

const GameControls = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding: 1rem;
  background-color: #0f3460;
  border-top: 1px solid #1a1a2e;

  background-color: #2196F3;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #0b7dda;
  }
`;

export default GameRoom;