import React, { useState, useEffect, useCallback } from 'react';
import styled, { keyframes, css } from 'styled-components';

export interface Notification {
  id: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: number;
  duration?: number; // Длительность отображения в мс (по умолчанию 5000)
  autoClose?: boolean; // Автоматически закрывать (по умолчанию true)
}

interface NotificationCenterProps {
  notifications: Notification[];
  onDismiss: (id: string) => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
  maxNotifications?: number; // Максимальное количество одновременно отображаемых уведомлений
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({
  notifications,
  onDismiss,
  position = 'top-right',
  maxNotifications = 5
}) => {
  // Отображаем только последние N уведомлений
  const visibleNotifications = notifications
    .sort((a, b) => b.timestamp - a.timestamp)
    .slice(0, maxNotifications);

  // Обработчик закрытия уведомления
  const handleDismiss = useCallback((id: string) => {
    onDismiss(id);
  }, [onDismiss]);

  return (
    <NotificationContainer position={position}>
      {visibleNotifications.map((notification) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onDismiss={handleDismiss}
        />
      ))}
    </NotificationContainer>
  );
};

interface NotificationItemProps {
  notification: Notification;
  onDismiss: (id: string) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification, onDismiss }) => {
  const { id, message, type, duration = 5000, autoClose = true } = notification;
  const [isExiting, setIsExiting] = useState(false);

  // Автоматическое закрытие уведомления через указанное время
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (autoClose) {
      timeoutId = setTimeout(() => {
        setIsExiting(true);
        // Даем время на анимацию выхода перед удалением
        setTimeout(() => onDismiss(id), 300);
      }, duration);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [id, duration, autoClose, onDismiss]);

  // Обработчик закрытия по клику
  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => onDismiss(id), 300);
  };

  return (
    <NotificationWrapper type={type} isExiting={isExiting}>
      <NotificationIcon type={type}>
        {type === 'info' && 'ℹ️'}
        {type === 'success' && '✅'}
        {type === 'warning' && '⚠️'}
        {type === 'error' && '❌'}
      </NotificationIcon>
      <NotificationContent>
        <NotificationMessage>{message}</NotificationMessage>
        <NotificationTime>
          {new Date(notification.timestamp).toLocaleTimeString()}
        </NotificationTime>
      </NotificationContent>
      <CloseButton onClick={handleClose}>×</CloseButton>
    </NotificationWrapper>
  );
};

// Анимации
const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
`;

const fadeOut = keyframes`
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(-20px); }
`;

// Позиционирование контейнера в зависимости от выбранной позиции
const getPositionStyles = (position: string) => {
  switch (position) {
    case 'top-left':
      return css`
        top: 20px;
        left: 20px;
      `;
    case 'top-center':
      return css`
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
      `;
    case 'top-right':
      return css`
        top: 20px;
        right: 20px;
      `;
    case 'bottom-left':
      return css`
        bottom: 20px;
        left: 20px;
      `;
    case 'bottom-center':
      return css`
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
      `;
    case 'bottom-right':
      return css`
        bottom: 20px;
        right: 20px;
      `;
    default:
      return css`
        top: 20px;
        right: 20px;
      `;
  }
};

// Стилизованные компоненты
const NotificationContainer = styled.div<{ position: string }>`
  position: fixed;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
  ${({ position }) => getPositionStyles(position)};
`;

const NotificationWrapper = styled.div<{ type: string; isExiting: boolean }>`
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background-color: ${({ type }) => 
    type === 'info' ? '#2196F3' :
    type === 'success' ? '#4CAF50' :
    type === 'warning' ? '#FFC107' :
    type === 'error' ? '#F44336' : '#333333'};
  animation: ${({ isExiting }) => isExiting ? fadeOut : fadeIn} 0.3s ease-in-out;
  min-width: 300px;
  max-width: 100%;
`;

const NotificationIcon = styled.div<{ type: string }>`
  margin-right: 12px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const NotificationContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const NotificationMessage = styled.div`
  color: white;
  font-size: 14px;
  margin-bottom: 4px;
  word-break: break-word;
`;

const NotificationTime = styled.div`
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  margin-left: 8px;
  opacity: 0.7;
  transition: opacity 0.2s;
  
  &:hover {
    opacity: 1;
  }
`;

export default NotificationCenter;