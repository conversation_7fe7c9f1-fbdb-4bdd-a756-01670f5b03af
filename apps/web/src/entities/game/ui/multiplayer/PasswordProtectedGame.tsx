import React, { useState } from 'react';
import styled from 'styled-components';
import { useRouter } from 'next/router';

interface PasswordProtectedGameProps {
  gameId: string;
  gameName: string;
  onJoinGame: (gameId: string, password: string) => Promise<void>;
  onCancel: () => void;
}

const PasswordProtectedGame: React.FC<PasswordProtectedGameProps> = ({
  gameId,
  gameName,
  onJoinGame,
  onCancel
}) => {
  const router = useRouter();
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Обработчик отправки формы
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!password.trim()) {
      setError('Пожалуйста, введите пароль');
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      await on<PERSON><PERSON>n<PERSON><PERSON>(gameId, password);
      // Переход в игру будет выполнен в родительском компоненте
    } catch (err) {
      console.error('Ошибка при входе в игру:', err);
      setError('Неверный пароль или ошибка подключения');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ModalOverlay>
      <ModalContainer>
        <ModalHeader>
          <h2>Приватная игра</h2>
          <CloseButton onClick={onCancel}>×</CloseButton>
        </ModalHeader>
        
        <ModalContent>
          <GameInfo>
            Для входа в игру <GameName>"{gameName}"</GameName> требуется пароль
          </GameInfo>
          
          <Form onSubmit={handleSubmit}>
            <FormGroup>
              <Label htmlFor="password">Пароль</Label>
              <Input
                type="password"
                id="password"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  if (error) setError(null);
                }}
                placeholder="Введите пароль игры"
                disabled={isLoading}
                autoFocus
              />
              {error && <ErrorMessage>{error}</ErrorMessage>}
            </FormGroup>
            
            <ButtonGroup>
              <SubmitButton type="submit" disabled={isLoading}>
                {isLoading ? 'Проверка...' : 'Присоединиться'}
              </SubmitButton>
              <CancelButton type="button" onClick={onCancel} disabled={isLoading}>
                Отмена
              </CancelButton>
            </ButtonGroup>
          </Form>
        </ModalContent>
      </ModalContainer>
    </ModalOverlay>
  );
};

// Стилизованные компоненты
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
`;

const ModalContainer = styled.div`
  background-color: #1a1a2e;
  border-radius: 8px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  overflow: hidden;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background-color: #0f3460;
  
  h2 {
    margin: 0;
    color: white;
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
  opacity: 0.7;
  transition: opacity 0.2s;
  
  &:hover {
    opacity: 1;
  }
`;

const ModalContent = styled.div`
  padding: 1.5rem;
  color: white;
`;

const GameInfo = styled.div`
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  text-align: center;
`;

const GameName = styled.span`
  font-weight: bold;
  color: #4CAF50;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-weight: bold;
  font-size: 0.9rem;
`;

const Input = styled.input`
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #333;
  color: white;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: #2196F3;
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
`;

const ErrorMessage = styled.div`
  color: #F44336;
  font-size: 0.8rem;
  margin-top: 0.25rem;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
`;

const SubmitButton = styled.button`
  flex: 1;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  border: none;
  background-color: #4CAF50;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover:not(:disabled) {
    background-color: #45a049;
  }
  
  &:disabled {
    background-color: #cccccc;
    color: #666666;
    cursor: not-allowed;
  }
`;

const CancelButton = styled.button`
  flex: 1;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  border: none;
  background-color: #9E9E9E;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover:not(:disabled) {
    background-color: #757575;
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
`;

export default PasswordProtectedGame;