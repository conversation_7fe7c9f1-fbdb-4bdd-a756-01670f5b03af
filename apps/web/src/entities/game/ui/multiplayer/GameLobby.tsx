import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import styled from 'styled-components';
import { useGameConnection } from '@a1-k/utils/hooks';

interface GameLobbyProps {
  serverUrl: string;
}

const GameLobby: React.FC<GameLobbyProps> = ({ serverUrl }) => {
  const router = useRouter();
  const [roomName, setRoomName] = useState('');
  const [playerName, setPlayerName] = useState('');
  const [isCreatingRoom, setIsCreatingRoom] = useState(false);

  // Используем хук для подключения к игровому серверу
  const {
    status,
    error,
    rooms,
    connect,
    getRooms,
    createRoom,
    joinRoom
  } = useGameConnection({
    serverUrl,
    autoConnect: false
  });

  // Подключаемся к серверу при монтировании компонента
  useEffect(() => {
    // Генерируем случайное имя игрока, если не задано
    const randomName = `Игрок-${Math.floor(Math.random() * 1000)}`;
    setPlayerName(randomName);
    
    // Подключаемся к серверу
    const connectToServer = async () => {
      const playerId = `player-${Date.now()}`;
      const sessionToken = 'guest-session'; // В будущем здесь будет настоящий токен авторизации
      await connect(playerId, sessionToken);
      await getRooms(); // Получаем список комнат после подключения
    };
    
    connectToServer();
    
    // Устанавливаем интервал для обновления списка комнат
    const interval = setInterval(() => {
      if (status === 'connected') {
        getRooms();
      }
    }, 5000);
    
    return () => clearInterval(interval);
  }, []);

  // Обработчик создания комнаты
  const handleCreateRoom = async () => {
    if (!roomName.trim()) {
      alert('Введите название комнаты');
      return;
    }
    
    setIsCreatingRoom(true);
    try {
      const room = await createRoom({
        name: roomName,
        gameType: 'durak',
        maxPlayers: 4,
        isPrivate: false,
        playerName
      });
      
      // Переходим на страницу комнаты
      router.push(`/game/room/${room.id}`);
    } catch (err) {
      console.error('Ошибка при создании комнаты:', err);
      alert(`Не удалось создать комнату: ${err instanceof Error ? err.message : 'Неизвестная ошибка'}`);
    } finally {
      setIsCreatingRoom(false);
    }
  };

  // Обработчик присоединения к комнате
  const handleJoinRoom = async (roomId: string) => {
    try {
      await joinRoom(roomId, playerName);
      router.push(`/game/room/${roomId}`);
    } catch (err) {
      console.error('Ошибка при присоединении к комнате:', err);
      alert(`Не удалось присоединиться к комнате: ${err instanceof Error ? err.message : 'Неизвестная ошибка'}`);
    }
  };

  return (
    <LobbyContainer>
      <LobbyHeader>
        <h1>Лобби игры "Козырь Мастер"</h1>
        <ConnectionStatus status={status}>
          {status === 'connected' ? 'Подключено' : 
           status === 'connecting' ? 'Подключение...' : 
           status === 'error' ? 'Ошибка подключения' : 'Отключено'}
        </ConnectionStatus>
      </LobbyHeader>

      {error && (
        <ErrorMessage>
          Ошибка: {error.message}
        </ErrorMessage>
      )}

      <PlayerNameSection>
        <label htmlFor="playerName">Ваше имя:</label>
        <input
          id="playerName"
          type="text"
          value={playerName}
          onChange={(e) => setPlayerName(e.target.value)}
          placeholder="Введите ваше имя"
        />
      </PlayerNameSection>

      <CreateRoomSection>
        <h2>Создать новую комнату</h2>
        <div>
          <input
            type="text"
            value={roomName}
            onChange={(e) => setRoomName(e.target.value)}
            placeholder="Название комнаты"
          />
          <button 
            onClick={handleCreateRoom} 
            disabled={isCreatingRoom || status !== 'connected'}
          >
            {isCreatingRoom ? 'Создание...' : 'Создать комнату'}
          </button>
        </div>
      </CreateRoomSection>

      <RoomListSection>
        <h2>Доступные комнаты</h2>
        {rooms.length === 0 ? (
          <EmptyRoomMessage>
            {status === 'connected' 
              ? 'Нет доступных комнат. Создайте новую!' 
              : 'Подключитесь к серверу, чтобы увидеть список комнат'}
          </EmptyRoomMessage>
        ) : (
          <RoomList>
            {rooms.map((room) => (
              <RoomItem key={room.id}>
                <RoomInfo>
                  <RoomName>{room.name}</RoomName>
                  <RoomDetails>
                    <span>Игроков: {room.players.length}/{room.maxPlayers}</span>
                    <span>Статус: {room.status === 'waiting' ? 'Ожидание' : 
                                   room.status === 'playing' ? 'Игра идет' : 'Завершена'}</span>
                  </RoomDetails>
                </RoomInfo>
                <JoinButton 
                  onClick={() => handleJoinRoom(room.id)}
                  disabled={room.status !== 'waiting' || room.players.length >= room.maxPlayers}
                >
                  {room.status !== 'waiting' ? 'Недоступно' : 
                   room.players.length >= room.maxPlayers ? 'Заполнено' : 'Присоединиться'}
                </JoinButton>
              </RoomItem>
            ))}
          </RoomList>
        )}
        <RefreshButton onClick={() => getRooms()} disabled={status !== 'connected'}>
          Обновить список
        </RefreshButton>
      </RoomListSection>
    </LobbyContainer>
  );
};

// Стилизованные компоненты
const LobbyContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
  color: white;
`;

const LobbyHeader = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  h1 {
    margin: 0;
    font-size: 2rem;
  }
`;

const ConnectionStatus = styled.div<{ status: string }>`
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: bold;
  background-color: ${({ status }) => 
    status === 'connected' ? '#4CAF50' :
    status === 'connecting' ? '#FFC107' :
    status === 'error' ? '#F44336' : '#9E9E9E'};
`;

const ErrorMessage = styled.div`
  background-color: #F44336;
  color: white;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
`;

const PlayerNameSection = styled.div`
  margin-bottom: 2rem;
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
  }
  
  input {
    padding: 0.75rem;
    border-radius: 4px;
    border: 1px solid #444;
    background-color: #333;
    color: white;
    width: 100%;
    max-width: 300px;
    font-size: 1rem;
  }
`;

const CreateRoomSection = styled.section`
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  
  h2 {
    margin-top: 0;
    margin-bottom: 1rem;
  }
  
  div {
    display: flex;
    gap: 1rem;
  }
  
  input {
    flex: 1;
    padding: 0.75rem;
    border-radius: 4px;
    border: 1px solid #444;
    background-color: #333;
    color: white;
    font-size: 1rem;
  }
  
  button {
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    border: none;
    background-color: #4CAF50;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover:not(:disabled) {
      background-color: #45a049;
    }
    
    &:disabled {
      background-color: #cccccc;
      color: #666666;
      cursor: not-allowed;
    }
  }
`;

const RoomListSection = styled.section`
  h2 {
    margin-top: 0;
    margin-bottom: 1rem;
  }
`;

const EmptyRoomMessage = styled.div`
  padding: 2rem;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  margin-bottom: 1rem;
`;

const RoomList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0 0 1rem 0;
`;

const RoomItem = styled.li`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  margin-bottom: 0.5rem;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.3);
  }
`;

const RoomInfo = styled.div`
  flex: 1;
`;

const RoomName = styled.h3`
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
`;

const RoomDetails = styled.div`
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #ccc;
`;

const JoinButton = styled.button`
  padding: 0.5rem 1rem;
  border-radius: 4px;
  border: none;
  background-color: #2196F3;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover:not(:disabled) {
    background-color: #0b7dda;
  }
  
  &:disabled {
    background-color: #cccccc;
    color: #666666;
    cursor: not-allowed;
  }
`;

const RefreshButton = styled.button`
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  border: none;
  background-color: #607D8B;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover:not(:disabled) {
    background-color: #546E7A;
  }
  
  &:disabled {
    background-color: #cccccc;
    color: #666666;
    cursor: not-allowed;
  }
`;

export default GameLobby;