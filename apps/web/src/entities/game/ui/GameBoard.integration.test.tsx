import { render, screen, fireEvent } from '@testing-library/react';
import { GameBoard } from './GameBoard';
import { GameProvider } from '../model/GameProvider';

// Мокируем зависимости
jest.mock('@shared/lib/i18n', () => ({
  useTranslation: () => ({ t: (key: string) => key })
}));

// Мокируем компонент Card для упрощения тестирования
jest.mock('@entities/card/ui/Card', () => ({
  Card: ({ suit, rank, onClick, isSelected, data-testid }: any) => (
    <div 
      data-testid={data-testid || 'card'}
      className={`card ${suit} ${isSelected ? 'selected' : ''}`}
      onClick={onClick}
    >
      {rank}
    </div>
  )
}));

describe('Интеграционные тесты GameBoard', () => {
  const mockPlayerCards = [
    { suit: 'hearts', rank: 'A', id: '1' },
    { suit: 'clubs', rank: 'K', id: '2' },
  ];
  
  const mockTrumpCard = { suit: 'diamonds', rank: '10', id: '3' };
  
  it('позволяет игроку выбрать карту', () => {
    const handleCardPlay = jest.fn();
    
    render(
      <GameProvider>
        <GameBoard 
          playerCards={mockPlayerCards}
          tableCards={[]}
          trumpCard={mockTrumpCard}
          deckCount={20}
          onCardPlay={handleCardPlay}
          data-testid="game-board"
        />
      </GameProvider>
    );
    
    // Находим карты игрока
    const playerCards = screen.getAllByTestId('card');
    expect(playerCards.length).toBe(2);
    
    // Кликаем по первой карте
    fireEvent.click(playerCards[0]);
    
    // Проверяем, что обработчик был вызван с правильной картой
    expect(handleCardPlay).toHaveBeenCalledWith(mockPlayerCards[0]);
  });
  
  it('отображает козырную карту', () => {
    render(
      <GameProvider>
        <GameBoard 
          playerCards={mockPlayerCards}
          tableCards={[]}
          trumpCard={mockTrumpCard}
          deckCount={20}
          onCardPlay={() => {}}
          data-testid="game-board"
        />
      </GameProvider>
    );
    
    // Проверяем, что козырная карта отображается
    const trumpCardElement = screen.getByTestId('trump-card');
    expect(trumpCardElement).toBeInTheDocument();
    expect(trumpCardElement).toHaveTextContent('10');
  });
  
  it('отображает количество карт в колоде', () => {
    render(
      <GameProvider>
        <GameBoard 
          playerCards={mockPlayerCards}
          tableCards={[]}
          trumpCard={mockTrumpCard}
          deckCount={20}
          onCardPlay={() => {}}
          data-testid="game-board"
        />
      </GameProvider>
    );
    
    // Проверяем, что количество карт в колоде отображается
    const deckCountElement = screen.getByTestId('deck-count');
    expect(deckCountElement).toBeInTheDocument();
    expect(deckCountElement).toHaveTextContent('20');
  });
  
  it('обновляет стол при добавлении карты', () => {
    const { rerender } = render(
      <GameProvider>
        <GameBoard 
          playerCards={mockPlayerCards}
          tableCards={[]}
          trumpCard={mockTrumpCard}
          deckCount={20}
          onCardPlay={() => {}}
          data-testid="game-board"
        />
      </GameProvider>
    );
    
    // Проверяем, что на столе нет карт
    expect(screen.queryByTestId('table-card')).not.toBeInTheDocument();
    
    // Обновляем пропсы, добавляя карту на стол
    rerender(
      <GameProvider>
        <GameBoard 
          playerCards={mockPlayerCards}
          tableCards={[{ suit: 'hearts', rank: 'A', id: '1' }]}
          trumpCard={mockTrumpCard}
          deckCount={20}
          onCardPlay={() => {}}
          data-testid="game-board"
        />
      </GameProvider>
    );
    
    // Проверяем, что карта появилась на столе
    const tableCard = screen.getByTestId('table-card');
    expect(tableCard).toBeInTheDocument();
    expect(tableCard).toHaveTextContent('A');
  });
});