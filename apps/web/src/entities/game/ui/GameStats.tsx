import React, { useState } from 'react';
import styled from 'styled-components';
import { Player, Card, PlayerAction } from '@a1-k/core';

interface GameMove {
  playerId: string;
  playerName: string;
  action: PlayerAction;
  card?: Card;
  targetCard?: Card;
  timestamp: number;
}

interface PlayerStats {
  playerId: string;
  playerName: string;
  gamesPlayed: number;
  gamesWon: number;
  attacksCount: number;
  successfulDefends: number;
  cardsPlayed: number;
  cardsTaken: number;
}

interface GameStatsProps {
  players: Player[];
  moveHistory: GameMove[];
  playerStats: PlayerStats[];
  currentPlayerId?: string;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
}

const StatsContainer = styled.div<{ isExpanded: boolean }>`
  background-color: rgba(22, 33, 62, 0.95);
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  width: ${props => props.isExpanded ? '600px' : '300px'};
  height: ${props => props.isExpanded ? '500px' : '50px'};
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 90;
  transition: all 0.3s ease;
  overflow: hidden;
  color: white;
`;

const StatsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #0f3460;
  cursor: pointer;

  h3 {
    margin: 0;
    font-size: 1rem;
  }
`;

const ToggleButton = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
`;

const TabsContainer = styled.div`
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
`;

const Tab = styled.button<{ active: boolean }>`
  padding: 10px 15px;
  background-color: ${props => props.active ? '#e94560' : 'transparent'};
  color: white;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
  flex: 1;
  font-size: 0.9rem;

  &:hover {
    background-color: ${props => props.active ? '#e94560' : 'rgba(233, 69, 96, 0.2)'};
  }
`;

const ContentContainer = styled.div`
  padding: 15px;
  overflow-y: auto;
  height: calc(100% - 100px);

  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(15, 52, 96, 0.2);
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(233, 69, 96, 0.5);
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(233, 69, 96, 0.8);
  }
`;

const StatsTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  font-size: 0.9rem;

  th, td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  th {
    background-color: rgba(15, 52, 96, 0.5);
    font-weight: bold;
  }

  tr:nth-child(even) {
    background-color: rgba(15, 52, 96, 0.2);
  }

  tr:hover {
    background-color: rgba(233, 69, 96, 0.1);
  }

  tr.current-player {
    background-color: rgba(233, 69, 96, 0.2);
    font-weight: bold;
  }
`;

const MoveItem = styled.div<{ type: PlayerAction }>`
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: rgba(15, 52, 96, 0.3);
  border-radius: 8px;
  border-left: 4px solid ${props => {
    switch(props.type) {
      case PlayerAction.ATTACK: return '#f72585';
      case PlayerAction.DEFEND: return '#4cc9f0';
      case PlayerAction.TAKE: return '#f9c74f';
      case PlayerAction.PASS: return '#90be6d';
      default: return '#4361ee';
    }
  }};
`;

const MoveInfo = styled.div`
  flex: 1;
`;

const MoveName = styled.div`
  font-weight: bold;
  margin-bottom: 3px;
`;

const MoveDetails = styled.div`
  font-size: 0.8rem;
  opacity: 0.8;
`;

const MoveTime = styled.div`
  font-size: 0.75rem;
  opacity: 0.6;
`;

const NoDataMessage = styled.div`
  text-align: center;
  padding: 20px;
  opacity: 0.7;
  font-style: italic;
`;

const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
};

const getActionName = (action: PlayerAction): string => {
  switch(action) {
    case PlayerAction.ATTACK: return 'Атака';
    case PlayerAction.DEFEND: return 'Защита';
    case PlayerAction.TAKE: return 'Взять карты';
    case PlayerAction.PASS: return 'Бито';
    default: return 'Неизвестное действие';
  }
};

export const GameStats: React.FC<GameStatsProps> = ({
  players,
  moveHistory,
  playerStats,
  currentPlayerId,
  isExpanded = false,
  onToggleExpand
}) => {
  const [localIsExpanded, setLocalIsExpanded] = useState(isExpanded);
  const [activeTab, setActiveTab] = useState<'history' | 'stats'>('history');
  
  // Используем внешнее состояние, если оно предоставлено
  const expanded = onToggleExpand ? isExpanded : localIsExpanded;

  // Обработчик сворачивания/разворачивания
  const handleToggleExpand = () => {
    if (onToggleExpand) {
      onToggleExpand();
    } else {
      setLocalIsExpanded(!localIsExpanded);
    }
  };

  return (
    <StatsContainer isExpanded={expanded}>
      <StatsHeader onClick={handleToggleExpand}>
        <h3>Статистика игры</h3>
        <ToggleButton>
          {expanded ? '▼' : '▲'}
        </ToggleButton>
      </StatsHeader>

      {expanded && (
        <>
          <TabsContainer>
            <Tab 
              active={activeTab === 'history'} 
              onClick={() => setActiveTab('history')}
            >
              История ходов
            </Tab>
            <Tab 
              active={activeTab === 'stats'} 
              onClick={() => setActiveTab('stats')}
            >
              Статистика игроков
            </Tab>
          </TabsContainer>

          <ContentContainer>
            {activeTab === 'history' ? (
              moveHistory.length > 0 ? (
                moveHistory.map((move, index) => (
                  <MoveItem key={index} type={move.action}>
                    <MoveInfo>
                      <MoveName>
                        {move.playerName}: {getActionName(move.action)}
                      </MoveName>
                      <MoveDetails>
                        {move.card && `Карта: ${move.card.rank} ${move.card.suit}`}
                        {move.targetCard && ` → ${move.targetCard.rank} ${move.targetCard.suit}`}
                      </MoveDetails>
                      <MoveTime>{formatTime(move.timestamp)}</MoveTime>
                    </MoveInfo>
                  </MoveItem>
                ))
              ) : (
                <NoDataMessage>История ходов пуста</NoDataMessage>
              )
            ) : (
              playerStats.length > 0 ? (
                <StatsTable>
                  <thead>
                    <tr>
                      <th>Игрок</th>
                      <th>Игры</th>
                      <th>Победы</th>
                      <th>Атаки</th>
                      <th>Защиты</th>
                      <th>Сыграно карт</th>
                      <th>Взято карт</th>
                    </tr>
                  </thead>
                  <tbody>
                    {playerStats.map(stat => (
                      <tr 
                        key={stat.playerId}
                        className={stat.playerId === currentPlayerId ? 'current-player' : ''}
                      >
                        <td>{stat.playerName}</td>
                        <td>{stat.gamesPlayed}</td>
                        <td>{stat.gamesWon}</td>
                        <td>{stat.attacksCount}</td>
                        <td>{stat.successfulDefends}</td>
                        <td>{stat.cardsPlayed}</td>
                        <td>{stat.cardsTaken}</td>
                      </tr>
                    ))}
                  </tbody>
                </StatsTable>
              ) : (
                <NoDataMessage>Статистика недоступна</NoDataMessage>
              )
            )}
          </ContentContainer>
        </>
      )}
    </StatsContainer>
  );
};