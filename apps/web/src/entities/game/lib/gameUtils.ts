import { Card, Player, PlayerAction } from '@a1-k/core';

/**
 * Утилиты для работы с игровой логикой
 * Эти функции могут быть использованы в разных компонентах игры
 */

/**
 * Проверяет, может ли игрок сделать ход данной картой
 * @param card Карта, которой игрок хочет сходить
 * @param tableCards Карты на столе
 * @param isAttacker Является ли игрок атакующим
 * @param trumpSuit Козырная масть
 * @returns Возможность хода
 */
export const canPlayCard = (
  card: Card,
  tableCards: Card[][],
  isAttacker: boolean,
  trumpSuit?: string
): boolean => {
  // Если стол пустой и игрок атакующий, то можно ходить любой картой
  if (tableCards.length === 0 && isAttacker) {
    return true;
  }

  // Если игрок атакующий, проверяем, есть ли на столе карта с таким же достоинством
  if (isAttacker) {
    const ranks = tableCards
      .flat()
      .filter(c => c !== null)
      .map(c => c.rank);
    return ranks.includes(card.rank);
  } else {
    // Если игрок защищающийся, проверяем, может ли он отбиться
    // Находим последнюю пару карт, где нет карты защиты
    const undefendedPair = tableCards.find(pair => pair.length === 1 || pair[1] === null);
    if (!undefendedPair) return false;
    
    const attackCard = undefendedPair[0];
    if (!attackCard) return false;
    
    // Проверяем, может ли карта побить атакующую карту
    return canBeatCard(card, attackCard, trumpSuit);
  }
};

/**
 * Проверяет, может ли одна карта побить другую
 * @param defenderCard Карта защищающегося
 * @param attackerCard Карта атакующего
 * @param trumpSuit Козырная масть
 * @returns Возможность побить карту
 */
export const canBeatCard = (
  defenderCard: Card,
  attackerCard: Card,
  trumpSuit?: string
): boolean => {
  // Козырь бьет некозырную карту
  if (defenderCard.suit === trumpSuit && attackerCard.suit !== trumpSuit) {
    return true;
  }
  
  // Некозырная карта не может побить козырную
  if (defenderCard.suit !== trumpSuit && attackerCard.suit === trumpSuit) {
    return false;
  }
  
  // Если масти одинаковые, то сравниваем достоинства
  if (defenderCard.suit === attackerCard.suit) {
    // Преобразуем буквенные значения в числовые для сравнения
    const rankValues: Record<string, number> = {
      '6': 6, '7': 7, '8': 8, '9': 9, '10': 10, 'J': 11, 'Q': 12, 'K': 13, 'A': 14
    };
    
    return rankValues[defenderCard.rank] > rankValues[attackerCard.rank];
  }
  
  // Если масти разные и ни одна не козырь, то побить нельзя
  return false;
};

/**
 * Проверяет, может ли игрок выполнить действие
 * @param action Действие игрока
 * @param isAttacker Является ли игрок атакующим
 * @param isDefender Является ли игрок защищающимся
 * @param tableCards Карты на столе
 * @returns Возможность выполнить действие
 */
export const canPerformAction = (
  action: PlayerAction,
  isAttacker: boolean,
  isDefender: boolean,
  tableCards: Card[][]
): boolean => {
  switch (action) {
    case PlayerAction.ATTACK:
      return isAttacker;
    case PlayerAction.DEFEND:
      return isDefender && tableCards.some(pair => pair.length === 1 || pair[1] === null);
    case PlayerAction.TAKE:
      return isDefender && tableCards.length > 0;
    case PlayerAction.PASS:
      return isAttacker && tableCards.length > 0 && tableCards.every(pair => pair.length === 2 && pair[1] !== null);
    default:
      return false;
  }
};

/**
 * Сортирует карты в руке игрока
 * @param cards Карты в руке
 * @param trumpSuit Козырная масть
 * @param groupBySuit Группировать ли по масти
 * @returns Отсортированные карты
 */
export const sortPlayerCards = (
  cards: Card[],
  trumpSuit?: string,
  groupBySuit: boolean = true
): Card[] => {
  const rankValues: Record<string, number> = {
    '6': 6, '7': 7, '8': 8, '9': 9, '10': 10, 'J': 11, 'Q': 12, 'K': 13, 'A': 14
  };
  
  // Сортировка по достоинству и масти
  return [...cards].sort((a, b) => {
    if (groupBySuit) {
      // Козыри всегда в конце
      if (a.suit === trumpSuit && b.suit !== trumpSuit) return 1;
      if (a.suit !== trumpSuit && b.suit === trumpSuit) return -1;
      
      // Сортировка по масти
      if (a.suit !== b.suit) {
        return a.suit.localeCompare(b.suit);
      }
    }
    
    // Сортировка по достоинству
    return rankValues[a.rank] - rankValues[b.rank];
  });
};

/**
 * Находит лучшую карту для хода
 * @param hand Карты в руке
 * @param tableCards Карты на столе
 * @param isAttacker Является ли игрок атакующим
 * @param trumpSuit Козырная масть
 * @returns Индекс лучшей карты или -1, если нет подходящей карты
 */
export const findBestCardToPlay = (
  hand: Card[],
  tableCards: Card[][],
  isAttacker: boolean,
  trumpSuit?: string
): number => {
  // Находим все карты, которыми можно сходить
  const playableCards = hand.map((card, index) => ({
    card,
    index,
    canPlay: canPlayCard(card, tableCards, isAttacker, trumpSuit)
  })).filter(item => item.canPlay);
  
  if (playableCards.length === 0) return -1;
  
  const rankValues: Record<string, number> = {
    '6': 6, '7': 7, '8': 8, '9': 9, '10': 10, 'J': 11, 'Q': 12, 'K': 13, 'A': 14
  };
  
  if (isAttacker) {
    // Для атакующего лучше ходить с младшей карты
    return playableCards.sort((a, b) => {
      // Некозыри предпочтительнее козырей
      if (a.card.suit === trumpSuit && b.card.suit !== trumpSuit) return 1;
      if (a.card.suit !== trumpSuit && b.card.suit === trumpSuit) return -1;
      
      // Сортировка по достоинству (от меньшего к большему)
      return rankValues[a.card.rank] - rankValues[b.card.rank];
    })[0].index;
  } else {
    // Для защищающегося лучше отбиваться минимально возможной картой
    const undefendedPair = tableCards.find(pair => pair.length === 1 || pair[1] === null);
    if (!undefendedPair || !undefendedPair[0]) return -1;
    
    const attackCard = undefendedPair[0];
    
    return playableCards.sort((a, b) => {
      // Сортировка по достоинству (от меньшего к большему)
      if (a.card.suit === b.card.suit) {
        return rankValues[a.card.rank] - rankValues[b.card.rank];
      }
      
      // Некозыри предпочтительнее козырей
      if (a.card.suit === trumpSuit && b.card.suit !== trumpSuit) return 1;
      if (a.card.suit !== trumpSuit && b.card.suit === trumpSuit) return -1;
      
      return 0;
    })[0].index;
  }
};

/**
 * Проверяет, закончилась ли игра
 * @param players Игроки
 * @param deckEmpty Пуста ли колода
 * @returns Закончилась ли игра и победитель (если есть)
 */
export const checkGameEnd = (
  players: Player[],
  deckEmpty: boolean
): { isGameOver: boolean; winner: Player | null } => {
  // Игра заканчивается, когда у одного из игроков нет карт и колода пуста
  const playersWithoutCards = players.filter(p => p.hand.length === 0);
  
  if (playersWithoutCards.length > 0 && deckEmpty) {
    return { isGameOver: true, winner: playersWithoutCards[0] };
  }
  
  // Если колода пуста и у всех игроков, кроме одного, нет карт, то оставшийся игрок проиграл
  if (deckEmpty && playersWithoutCards.length === players.length - 1) {
    const loser = players.find(p => p.hand.length > 0);
    return { isGameOver: true, winner: loser ? null : null };
  }
  
  return { isGameOver: false, winner: null };
};