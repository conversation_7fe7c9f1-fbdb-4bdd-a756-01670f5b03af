/**
 * Сервис для обработки ошибок сети и восстановления соединения
 */

import { useState, useEffect, useCallback } from 'react';

export interface NetworkError extends Error {
  code?: string;
  status?: number;
  isNetworkError?: boolean;
}

export interface ErrorHandlingOptions {
  maxRetries?: number; // Максимальное количество попыток переподключения
  retryDelay?: number; // Задержка между попытками в мс
  onMaxRetriesExceeded?: () => void; // Колбэк при превышении максимального количества попыток
  shouldRetry?: (error: NetworkError) => boolean; // Функция для определения, нужно ли повторять попытку
}

export interface ErrorHandlingResult {
  error: NetworkError | null; // Текущая ошибка
  isRecovering: boolean; // Флаг восстановления соединения
  retryCount: number; // Текущее количество попыток
  retry: () => void; // Функция для ручного повтора
  clearError: () => void; // Функция для очистки ошибки
  setError: (error: NetworkError) => void; // Функция для установки ошибки
}

/**
 * Хук для обработки ошибок сети и автоматического восстановления соединения
 */
export function useErrorHandling(
  operation: () => Promise<any>,
  options: ErrorHandlingOptions = {}
): ErrorHandlingResult {
  const {
    maxRetries = 3,
    retryDelay = 2000,
    onMaxRetriesExceeded,
    shouldRetry = (error: NetworkError) => {
      // По умолчанию повторяем попытку для ошибок сети и 5xx ошибок сервера
      return (
        error.isNetworkError ||
        error.code === 'ECONNREFUSED' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ETIMEDOUT' ||
        (error.status && error.status >= 500 && error.status < 600)
      );
    }
  } = options;

  const [error, setError] = useState<NetworkError | null>(null);
  const [isRecovering, setIsRecovering] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [retryTimeoutId, setRetryTimeoutId] = useState<NodeJS.Timeout | null>(null);

  // Очистка ошибки
  const clearError = useCallback(() => {
    setError(null);
    setIsRecovering(false);
    setRetryCount(0);
    if (retryTimeoutId) {
      clearTimeout(retryTimeoutId);
      setRetryTimeoutId(null);
    }
  }, [retryTimeoutId]);

  // Функция для повторной попытки
  const retry = useCallback(async () => {
    if (!error) return;

    setIsRecovering(true);
    try {
      await operation();
      clearError();
    } catch (newError) {
      const typedError = newError as NetworkError;
      setError(typedError);

      if (shouldRetry(typedError) && retryCount < maxRetries) {
        const nextRetryCount = retryCount + 1;
        setRetryCount(nextRetryCount);

        // Экспоненциальная задержка между попытками
        const delay = retryDelay * Math.pow(1.5, nextRetryCount - 1);
        const timeoutId = setTimeout(retry, delay);
        setRetryTimeoutId(timeoutId);
      } else {
        setIsRecovering(false);
        if (retryCount >= maxRetries && onMaxRetriesExceeded) {
          onMaxRetriesExceeded();
        }
      }
    }
  }, [error, retryCount, maxRetries, retryDelay, shouldRetry, operation, clearError, onMaxRetriesExceeded]);

  // Автоматический запуск восстановления при появлении ошибки
  useEffect(() => {
    if (error && shouldRetry(error) && retryCount === 0) {
      const timeoutId = setTimeout(retry, retryDelay);
      setRetryTimeoutId(timeoutId);
      setRetryCount(1);
      setIsRecovering(true);
    }

    return () => {
      if (retryTimeoutId) {
        clearTimeout(retryTimeoutId);
      }
    };
  }, [error, shouldRetry, retry, retryCount, retryDelay, retryTimeoutId]);

  // Функция для установки ошибки извне
  const setErrorHandler = useCallback((newError: NetworkError) => {
    setError(newError);
  }, []);

  return {
    error,
    isRecovering,
    retryCount,
    retry,
    clearError,
    setError: setErrorHandler
  };
}

/**
 * Класс для обработки ошибок сети и восстановления соединения
 */
export class ErrorHandlingService {
  private maxRetries: number;
  private retryDelay: number;
  private onMaxRetriesExceeded?: () => void;
  private shouldRetry: (error: NetworkError) => boolean;

  constructor(options: ErrorHandlingOptions = {}) {
    this.maxRetries = options.maxRetries || 3;
    this.retryDelay = options.retryDelay || 2000;
    this.onMaxRetriesExceeded = options.onMaxRetriesExceeded;
    this.shouldRetry = options.shouldRetry || ((error: NetworkError) => {
      return (
        error.isNetworkError ||
        error.code === 'ECONNREFUSED' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ETIMEDOUT' ||
        (error.status && error.status >= 500 && error.status < 600)
      );
    });
  }

  /**
   * Выполняет операцию с автоматическими повторными попытками в случае ошибки
   */
  async executeWithRetry<T>(operation: () => Promise<T>): Promise<T> {
    let retryCount = 0;

    const execute = async (): Promise<T> => {
      try {
        return await operation();
      } catch (error) {
        // Классифицируем ошибку перед обработкой
        const typedError = this.classifyError(error);

        if (this.shouldRetry(typedError) && retryCount < this.maxRetries) {
          retryCount++;
          console.log(`Попытка переподключения ${retryCount} из ${this.maxRetries}`);
          const delay = this.retryDelay * Math.pow(1.5, retryCount - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
          return execute();
        } else {
          if (retryCount >= this.maxRetries && this.onMaxRetriesExceeded) {
            this.onMaxRetriesExceeded();
          }
          throw typedError;
        }
      }
    };

    return execute();
  }

  /**
   * Классифицирует ошибку и добавляет дополнительную информацию
   */
  classifyError(error: any): NetworkError {
    const networkError: NetworkError = error instanceof Error ? error : new Error(String(error));
    
    // Копируем свойства из исходной ошибки
    if (error && typeof error === 'object') {
      if (error.code) networkError.code = error.code;
      if (error.status) networkError.status = error.status;
    }

    // Определяем тип ошибки
    if (
      (typeof error === 'object' && error?.message?.includes('network')) ||
      (typeof error === 'object' && error?.message?.includes('соединение')) ||
      (typeof error === 'object' && error?.message?.includes('connection')) ||
      (typeof error === 'object' && error?.code === 'ECONNREFUSED') ||
      (typeof error === 'object' && error?.code === 'ECONNRESET') ||
      (typeof error === 'object' && error?.code === 'ETIMEDOUT')
    ) {
      networkError.isNetworkError = true;
    }

    return networkError;
  }

  /**
   * Форматирует сообщение об ошибке для отображения пользователю
   */
  formatErrorMessage(error: NetworkError): string {
    if (!error) {
      return 'Произошла неизвестная ошибка.';
    }

    if (error.isNetworkError) {
      return 'Ошибка сети. Проверьте подключение к интернету.';
    }

    // Обработка ошибок по коду
    if (error.code) {
      switch (error.code) {
        case 'ECONNREFUSED':
          return 'Не удалось подключиться к серверу. Сервер может быть недоступен.';
        case 'ECONNRESET':
          return 'Соединение было сброшено. Пожалуйста, попробуйте еще раз.';
        case 'ETIMEDOUT':
          return 'Время ожидания истекло. Проверьте скорость вашего интернет-соединения.';
        case 'ENOTFOUND':
          return 'Не удалось найти сервер. Проверьте ваше подключение к интернету.';
        case 'OFFLINE':
          return 'Вы находитесь в автономном режиме. Пожалуйста, подключитесь к интернету.';
      }
    }

    // Обработка ошибок по HTTP-статусу
    if (error.status) {
      switch (error.status) {
        case 400:
          return 'Некорректный запрос. Пожалуйста, проверьте введенные данные.';
        case 401:
          return 'Необходима авторизация. Пожалуйста, войдите в систему.';
        case 403:
          return 'Доступ запрещен. У вас нет прав для выполнения этого действия.';
        case 404:
          return 'Ресурс не найден. Возможно, он был удален или перемещен.';
        case 408:
          return 'Время ожидания запроса истекло. Пожалуйста, попробуйте позже.';
        case 429:
          return 'Слишком много запросов. Пожалуйста, попробуйте позже.';
        case 500:
          return 'Внутренняя ошибка сервера. Пожалуйста, попробуйте позже.';
        case 502:
          return 'Ошибка шлюза. Пожалуйста, попробуйте позже.';
        case 503:
          return 'Сервис временно недоступен. Пожалуйста, попробуйте позже.';
        case 504:
          return 'Время ожидания шлюза истекло. Пожалуйста, попробуйте позже.';
        default:
          if (error.status >= 500) {
            return 'Ошибка сервера. Пожалуйста, попробуйте позже.';
          } else if (error.status >= 400) {
            return `Ошибка запроса (${error.status}). Пожалуйста, проверьте введенные данные.`;
          }
          return `Ошибка запроса (${error.status}). Пожалуйста, попробуйте позже.`;
      }
    }

    return error.message || 'Произошла неизвестная ошибка.';
  }
}

export default ErrorHandlingService;