// Система тем для карт
import { createGlobalStyle } from "styled-components";
import { CardTheme, CardColorTheme, CardEffect } from "../model/types";

// Интерфейс для определения темы карты
export interface CardThemeConfig {
  name: string;
  borderRadius: string;
  fontFamily: string;
  boxShadow: string;
  backImage?: string;
  cornerStyle?: string;
  symbolStyle?: string;
  animations?: Record<string, any>;
}

// Интерфейс для определения цветовой темы
export interface CardColorThemeConfig {
  name: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  redSuitColor: string;
  blackSuitColor: string;
  accentColor?: string;
  secondaryColor?: string;
  filter?: string;
}

// Интерфейс для определения эффекта
export interface CardEffectConfig {
  name: string;
  cssEffect: string;
  animation?: string;
  filter?: string;
  transform?: string;
  transition?: string;
}

// Определение тем карт
export const cardThemes: Record<CardTheme, CardThemeConfig> = {
  classic: {
    name: "Классическая",
    borderRadius: "8px",
    fontFamily: '"Times New Roman", serif',
    boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
    cornerStyle: "standard",
    symbolStyle: "traditional",
  },
  modern: {
    name: "Современная",
    borderRadius: "12px",
    fontFamily: '"Roboto", sans-serif',
    boxShadow: "0 8px 16px rgba(0, 0, 0, 0.15)",
    cornerStyle: "rounded",
    symbolStyle: "simplified",
  },
  minimal: {
    name: "Минималистичная",
    borderRadius: "4px",
    fontFamily: '"Helvetica Neue", sans-serif',
    boxShadow: "none",
    cornerStyle: "flat",
    symbolStyle: "geometric",
  },
  fantasy: {
    name: "Фэнтези",
    borderRadius: "16px",
    fontFamily: '"Fantasy", cursive',
    boxShadow: "0 10px 20px rgba(0, 0, 0, 0.2)",
    cornerStyle: "ornate",
    symbolStyle: "decorative",
  },
};

// Определение цветовых тем
export const cardColorThemes: Record<CardColorTheme, CardColorThemeConfig> = {
  light: {
    name: "Светлая",
    backgroundColor: "#fff",
    textColor: "#000",
    borderColor: "#ddd",
    redSuitColor: "#d32f2f",
    blackSuitColor: "#000",
    accentColor: "#f5f5f5",
  },
  dark: {
    name: "Темная",
    backgroundColor: "#222",
    textColor: "#fff",
    borderColor: "#444",
    redSuitColor: "#ff5252",
    blackSuitColor: "#fff",
    accentColor: "#333",
  },
  colorful: {
    name: "Цветная",
    backgroundColor: "#f0f8ff",
    textColor: "#333",
    borderColor: "#b3e5fc",
    redSuitColor: "#d32f2f",
    blackSuitColor: "#2e7d32",
    accentColor: "#e1f5fe",
  },
  monochrome: {
    name: "Монохромная",
    backgroundColor: "#f5f5f5",
    textColor: "#333",
    borderColor: "#ddd",
    redSuitColor: "#555",
    blackSuitColor: "#333",
    filter: "grayscale(100%)",
  },
};

// Определение эффектов
export const cardEffects: Record<CardEffect, CardEffectConfig> = {
  none: {
    name: "Без эффекта",
    cssEffect: "",
  },
  holographic: {
    name: "Голографический",
    cssEffect:
      "linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.8) 50%, rgba(255,255,255,0.2) 100%)",
    filter: "hue-rotate(calc(var(--mouse-x, 0) * 360deg))",
    transition: "all 0.3s ease",
  },
  metallic: {
    name: "Металлический",
    cssEffect:
      "linear-gradient(to bottom, #d3d3d3 0%, #a9a9a9 50%, #d3d3d3 100%)",
    filter: "brightness(calc(0.8 + var(--mouse-y, 0) * 0.4))",
    transition: "all 0.2s ease",
  },
  neon: {
    name: "Неоновый",
    cssEffect:
      "box-shadow: 0 0 10px rgba(0, 255, 255, 0.7), 0 0 20px rgba(0, 255, 255, 0.5), 0 0 30px rgba(0, 255, 255, 0.3)",
    filter: "brightness(1.2)",
    transition: "all 0.2s ease",
  },
  shadow: {
    name: "Тень",
    cssEffect: "box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5)",
    transform: "translateY(-5px)",
    transition: "transform 0.3s ease, box-shadow 0.3s ease",
  },
};

// Функция для получения стилей темы карты
export const getCardThemeStyles = (theme: CardTheme = "classic") => {
  return cardThemes[theme] || cardThemes.classic;
};

// Функция для получения стилей цветовой темы
export const getCardColorThemeStyles = (
  colorTheme: CardColorTheme = "light",
  isRed: boolean = false,
) => {
  const theme = cardColorThemes[colorTheme] || cardColorThemes.light;
  return {
    ...theme,
    color: isRed ? theme.redSuitColor : theme.blackSuitColor,
  };
};

// Функция для получения стилей эффекта
export const getCardEffectStyles = (effect: CardEffect = "none") => {
  return cardEffects[effect] || cardEffects.none;
};

// Функция для применения темы к карте
export const applyCardTheme = (
  theme: CardTheme = "classic",
  colorTheme: CardColorTheme = "light",
  effect: CardEffect = "none",
  isRed: boolean = false,
) => {
  const themeStyles = getCardThemeStyles(theme);
  const colorStyles = getCardColorThemeStyles(colorTheme, isRed);
  const effectStyles = getCardEffectStyles(effect);

  return {
    borderRadius: themeStyles.borderRadius,
    fontFamily: themeStyles.fontFamily,
    boxShadow: effectStyles.cssEffect
      ? effectStyles.cssEffect
      : themeStyles.boxShadow,
    backgroundColor: colorStyles.backgroundColor,
    color: colorStyles.color,
    borderColor: colorStyles.borderColor,
    filter: colorStyles.filter || effectStyles.filter || "",
    transform: effectStyles.transform || "",
    transition: effectStyles.transition || "all 0.2s ease",
  };
};

// Глобальные стили для поддержки тем
export const CardThemeGlobalStyles = createGlobalStyle`
  :root {
    --card-red-color: #d32f2f;
    --card-black-color: #000;
    --card-bg-light: #fff;
    --card-bg-dark: #222;
    --card-border-light: #ddd;
    --card-border-dark: #444;
  }
  
  /* Дополнительные глобальные стили для поддержки тем */
  body.theme-dark {
    --card-bg-light: #222;
    --card-bg-dark: #111;
    --card-border-light: #444;
    --card-border-dark: #333;
  }
`;

// Экспорт компонентов
export * from "./ThemeProvider";
export * from "./ThemeSelector";
