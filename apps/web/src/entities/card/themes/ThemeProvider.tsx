import React, { createContext, useContext, useState, ReactNode } from "react";
import { CardTheme, CardColorTheme, CardEffect } from "../model/types";
import {
  CardThemeGlobalStyles,
  getCardThemeStyles,
  getCardColorThemeStyles,
  getCardEffectStyles,
} from ".";

// Интерфейс контекста темы
interface CardThemeContextType {
  theme: CardTheme;
  colorTheme: CardColorTheme;
  effect: CardEffect;
  setTheme: (theme: CardTheme) => void;
  setColorTheme: (colorTheme: CardColorTheme) => void;
  setEffect: (effect: CardEffect) => void;
  getThemeStyles: (isRed?: boolean) => {
    borderRadius: string;
    fontFamily: string;
    boxShadow: string;
    backgroundColor: string;
    color: string;
    borderColor: string;
    filter: string;
    transform: string;
    transition: string;
  };
}

// Создание контекста с значениями по умолчанию
const CardThemeContext = createContext<CardThemeContextType>({
  theme: "classic",
  colorTheme: "light",
  effect: "none",
  setTheme: () => {},
  setColorTheme: () => {},
  setEffect: () => {},
  getThemeStyles: () => ({
    borderRadius: "8px",
    fontFamily: '"Times New Roman", serif',
    boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
    backgroundColor: "#fff",
    color: "#000",
    borderColor: "#ddd",
    filter: "",
    transform: "",
    transition: "all 0.2s ease",
  }),
});

// Хук для использования контекста темы
export const useCardTheme = () => useContext(CardThemeContext);

interface CardThemeProviderProps {
  children: ReactNode;
  initialTheme?: CardTheme;
  initialColorTheme?: CardColorTheme;
  initialEffect?: CardEffect;
}

// Компонент провайдера темы
export const CardThemeProvider: React.FC<CardThemeProviderProps> = ({
  children,
  initialTheme = "classic",
  initialColorTheme = "light",
  initialEffect = "none",
}) => {
  const [theme, setTheme] = useState<CardTheme>(initialTheme);
  const [colorTheme, setColorTheme] =
    useState<CardColorTheme>(initialColorTheme);
  const [effect, setEffect] = useState<CardEffect>(initialEffect);

  // Функция для получения комбинированных стилей темы
  const getThemeStyles = (isRed: boolean = false) => {
    const themeStyles = getCardThemeStyles(theme);
    const colorStyles = getCardColorThemeStyles(colorTheme, isRed);
    const effectStyles = getCardEffectStyles(effect);

    return {
      borderRadius: themeStyles.borderRadius,
      fontFamily: themeStyles.fontFamily,
      boxShadow: effectStyles.cssEffect
        ? effectStyles.cssEffect
        : themeStyles.boxShadow,
      backgroundColor: colorStyles.backgroundColor,
      color: colorStyles.color,
      borderColor: colorStyles.borderColor,
      filter: colorStyles.filter || effectStyles.filter || "",
      transform: effectStyles.transform || "",
      transition: effectStyles.transition || "all 0.2s ease",
    };
  };

  const value = {
    theme,
    colorTheme,
    effect,
    setTheme,
    setColorTheme,
    setEffect,
    getThemeStyles,
  };

  return (
    <CardThemeContext.Provider value={value}>
      <CardThemeGlobalStyles />
      {children}
    </CardThemeContext.Provider>
  );
};
