import React from "react";
import styled from "styled-components";
import { CardTheme, CardColorTheme, CardEffect } from "../model/types";
import { cardThemes, cardColorThemes, cardEffects } from ".";
import { useCardTheme } from "./ThemeProvider";

const ThemeSelectorContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 8px;
  background-color: #f5f5f5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const SelectorGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const SelectorTitle = styled.h3`
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
`;

const OptionsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
`;

const OptionButton = styled.button<{ isSelected: boolean }>`
  padding: 0.5rem 1rem;
  border: 1px solid ${(props) => (props.isSelected ? "#2196f3" : "#ddd")};
  border-radius: 4px;
  background-color: ${(props) => (props.isSelected ? "#e3f2fd" : "#fff")};
  color: ${(props) => (props.isSelected ? "#0d47a1" : "#333")};
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;

  &:hover {
    background-color: ${(props) => (props.isSelected ? "#bbdefb" : "#f5f5f5")};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.4);
  }
`;

interface ThemeSelectorProps {
  className?: string;
}

export const ThemeSelector: React.FC<ThemeSelectorProps> = ({ className }) => {
  const { theme, colorTheme, effect, setTheme, setColorTheme, setEffect } =
    useCardTheme();

  return (
    <ThemeSelectorContainer className={className}>
      <SelectorGroup>
        <SelectorTitle>Тема колоды</SelectorTitle>
        <OptionsContainer>
          {Object.entries(cardThemes).map(([themeId, themeConfig]) => (
            <OptionButton
              key={themeId}
              isSelected={theme === themeId}
              onClick={() => setTheme(themeId as CardTheme)}
            >
              {themeConfig.name}
            </OptionButton>
          ))}
        </OptionsContainer>
      </SelectorGroup>

      <SelectorGroup>
        <SelectorTitle>Цветовая тема</SelectorTitle>
        <OptionsContainer>
          {Object.entries(cardColorThemes).map(([themeId, themeConfig]) => (
            <OptionButton
              key={themeId}
              isSelected={colorTheme === themeId}
              onClick={() => setColorTheme(themeId as CardColorTheme)}
            >
              {themeConfig.name}
            </OptionButton>
          ))}
        </OptionsContainer>
      </SelectorGroup>

      <SelectorGroup>
        <SelectorTitle>Эффекты</SelectorTitle>
        <OptionsContainer>
          {Object.entries(cardEffects).map(([effectId, effectConfig]) => (
            <OptionButton
              key={effectId}
              isSelected={effect === effectId}
              onClick={() => setEffect(effectId as CardEffect)}
            >
              {effectConfig.name}
            </OptionButton>
          ))}
        </OptionsContainer>
      </SelectorGroup>
    </ThemeSelectorContainer>
  );
};
