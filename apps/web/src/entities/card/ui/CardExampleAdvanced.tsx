import React, { useState, useEffect } from 'react';
import { Card } from './Card';
import styled from 'styled-components';
import { keyframes } from 'styled-components';
import { CardSuit, CardAnimationType, CardAnimationSpeed, CardTheme, CardEffect } from '../model/types';

// Расширенный пример использования компонента Card с продвинутыми возможностями
export const CardExampleAdvanced: React.FC = () => {
  // Состояния для базовых настроек
  const [selectedCardIndex, setSelectedCardIndex] = useState<number | null>(null);
  const [selectedAnimationIndex, setSelectedAnimationIndex] = useState<number | null>(null);
  const [selectedSpeedIndex, setSelectedSpeedIndex] = useState<number | null>(null);
  const [selectedContrastIndex, setSelectedContrastIndex] = useState<number | null>(null);
  const [selectedFaceIndex, setSelectedFaceIndex] = useState<number | null>(null);
  const [soundEnabled, setSoundEnabled] = useState<boolean>(true);
  
  // Новые состояния для расширенных возможностей
  const [selectedDeckType, setSelectedDeckType] = useState<CardTheme>('classic');
  const [selectedTheme, setSelectedTheme] = useState<string>('light');
  const [selectedLayout, setSelectedLayout] = useState<string>('grid');
  const [isAnimating, setIsAnimating] = useState<boolean>(false);
  const [selectedEffect, setSelectedEffect] = useState<CardEffect>('none');
  const [showLayoutPreview, setShowLayoutPreview] = useState<boolean>(false);
  const [showEffectsPreview, setShowEffectsPreview] = useState<boolean>(false);
  const [showDeckTypePreview, setShowDeckTypePreview] = useState<boolean>(false);
  const [customCardConfig, setCustomCardConfig] = useState({
    suit: 'hearts' as CardSuit,
    rank: 'A',
    animationType: 'simple' as CardAnimationType,
    animationSpeed: 'normal' as CardAnimationSpeed,
    highContrast: false,
    isFaceDown: false,
    theme: 'classic' as CardTheme,
    effect: 'none' as CardEffect,
    colorTheme: 'light'
  });
  
  // Состояние для расширенного конструктора карт
  const [showAdvancedConstructor, setShowAdvancedConstructor] = useState<boolean>(false);
  
  // Примеры карт с разными настройками
  const cards = [
    { suit: 'hearts', rank: 'A' },
    { suit: 'diamonds', rank: 10 },
    { suit: 'clubs', rank: 'K' },
    { suit: 'spades', rank: 'Q' },
    { suit: 'hearts', rank: 'J' },
  ];
  
  // Примеры анимаций
  const animations = [
    { type: 'simple', label: 'Простая' },
    { type: 'pulse', label: 'Пульсация' },
    { type: 'flip', label: 'Переворот' },
    { type: '3d-rotate', label: '3D вращение' },
    { type: 'glow', label: 'Свечение' },
    { type: 'cascade', label: 'Каскад' },
    { type: 'particle', label: 'Частицы' },
  ];
  
  // Примеры скоростей
  const speeds = [
    { speed: 'slow', label: 'Медленно' },
    { speed: 'normal', label: 'Нормально' },
    { speed: 'fast', label: 'Быстро' },
    { speed: 'instant', label: 'Мгновенно' },
  ];
  
  // Примеры контрастности
  const contrastExamples = [
    { suit: 'hearts', rank: 'A', highContrast: false, label: 'Обычный' },
    { suit: 'hearts', rank: 'A', highContrast: true, label: 'Высокий контраст' },
  ];
  
  // Примеры лицевой/обратной стороны
  const faceExamples = [
    { suit: 'spades', rank: 'K', isFaceDown: false, label: 'Лицевая сторона' },
    { suit: 'spades', rank: 'K', isFaceDown: true, label: 'Рубашкой вверх' },
  ];
  
  // Типы колод (темы)
  const deckTypes = [
    { id: 'classic', label: 'Классическая' },
    { id: 'modern', label: 'Современная' },
    { id: 'minimal', label: 'Минималистичная' },
    { id: 'fantasy', label: 'Фэнтези' },
  ];
  
  // Специальные эффекты
  const effects = [
    { id: 'none', label: 'Без эффекта' },
    { id: 'holographic', label: 'Голографический' },
    { id: 'metallic', label: 'Металлический' },
    { id: 'neon', label: 'Неоновый' },
    { id: 'shadow', label: 'Тень' },
  ];
  
  // Темы оформления
  const themes = [
    { id: 'light', label: 'Светлая' },
    { id: 'dark', label: 'Темная' },
    { id: 'colorful', label: 'Цветная' },
    { id: 'monochrome', label: 'Монохромная' },
  ];
  
  // Варианты расположения
  const layouts = [
    { id: 'grid', label: 'Сетка' },
    { id: 'fan', label: 'Веер' },
    { id: 'stack', label: 'Стопка' },
    { id: 'circle', label: 'Круг' },
  ];
  
  // Обработчики событий
  const handleCardClick = (index: number) => {
    setSelectedCardIndex(index === selectedCardIndex ? null : index);
  };
  
  const handleAnimationClick = (index: number) => {
    setSelectedAnimationIndex(index === selectedAnimationIndex ? null : index);
    if (index !== selectedAnimationIndex) {
      setCustomCardConfig(prev => ({
        ...prev,
        animationType: animations[index].type as any
      }));
    }
  };
  
  const handleSpeedClick = (index: number) => {
    setSelectedSpeedIndex(index === selectedSpeedIndex ? null : index);
    if (index !== selectedSpeedIndex) {
      setCustomCardConfig(prev => ({
        ...prev,
        animationSpeed: speeds[index].speed as any
      }));
    }
  };
  
  const handleContrastClick = (index: number) => {
    setSelectedContrastIndex(index === selectedContrastIndex ? null : index);
    if (index !== selectedContrastIndex) {
      setCustomCardConfig(prev => ({
        ...prev,
        highContrast: contrastExamples[index].highContrast
      }));
    }
  };
  
  const handleFaceClick = (index: number) => {
    setSelectedFaceIndex(index === selectedFaceIndex ? null : index);
    if (index !== selectedFaceIndex) {
      setCustomCardConfig(prev => ({
        ...prev,
        isFaceDown: faceExamples[index].isFaceDown
      }));
    }
  };
  
  const toggleSound = () => {
    setSoundEnabled(!soundEnabled);
  };
  
  const handleDeckTypeChange = (id: CardTheme) => {
    setSelectedDeckType(id);
    setCustomCardConfig(prev => ({
      ...prev,
      theme: id
    }));
  };
  
  const handleThemeChange = (id: string) => {
    setSelectedTheme(id);
    setCustomCardConfig(prev => ({
      ...prev,
      colorTheme: id
    }));
  };
  
  const handleLayoutChange = (id: string) => {
    setSelectedLayout(id);
  };
  
  const handleEffectChange = (id: CardEffect) => {
    setSelectedEffect(id);
    setCustomCardConfig(prev => ({
      ...prev,
      effect: id
    }));
  };
  
  const toggleAdvancedConstructor = () => {
    setShowAdvancedConstructor(!showAdvancedConstructor);
  };
  
  const toggleLayoutPreview = () => {
    setShowLayoutPreview(!showLayoutPreview);
  };
  
  const toggleEffectsPreview = () => {
    setShowEffectsPreview(!showEffectsPreview);
  };
  
  const toggleDeckTypePreview = () => {
    setShowDeckTypePreview(!showDeckTypePreview);
  };
  
  // Функция для генерации карт для предпросмотра
  const generatePreviewCards = (count: number = 5): Array<{suit: CardSuit, rank: string | number}> => {
    const suits: CardSuit[] = ['hearts', 'diamonds', 'clubs', 'spades'];
    const ranks = ['A', 2, 3, 4, 5, 6, 7, 8, 9, 10, 'J', 'Q', 'K'];
    
    return Array.from({ length: count }, (_, i) => ({
      suit: suits[i % suits.length],
      rank: ranks[i % ranks.length]
    }));
  };
  
  // Дублирующиеся обработчики удалены
  
  const handleSuitChange = (suit: CardSuit) => {
    setCustomCardConfig(prev => ({
      ...prev,
      suit
    }));
  };
  
  const handleRankChange = (rank: string | number) => {
    setCustomCardConfig(prev => ({
      ...prev,
      rank
    }));
  };
  
  const triggerAnimation = () => {
    setIsAnimating(true);
    setTimeout(() => setIsAnimating(false), 1500);
  };
  
  // Эффект для демонстрации анимации при изменении настроек
  useEffect(() => {
    if (selectedAnimationIndex !== null || selectedSpeedIndex !== null) {
      triggerAnimation();
    }
  }, [selectedAnimationIndex, selectedSpeedIndex]);
  
  return (
    <ExampleContainer theme={selectedTheme}>
      <ExampleHeader>Продвинутая демонстрация карт</ExampleHeader>
      
      <ControlPanel>
        <ControlSection>
          <SectionTitle>Тип колоды</SectionTitle>
          <ButtonGroup>
            {deckTypes.map((deck) => (
              <ControlButton 
                key={deck.id}
                isActive={selectedDeckType === deck.id}
                onClick={() => handleDeckTypeChange(deck.id)}
              >
                {deck.label}
              </ControlButton>
            ))}
          </ButtonGroup>
        </ControlSection>
        
        <ControlSection>
          <SectionTitle>Тема оформления</SectionTitle>
          <ButtonGroup>
            {themes.map((theme) => (
              <ControlButton 
                key={theme.id}
                isActive={selectedTheme === theme.id}
                onClick={() => handleThemeChange(theme.id)}
              >
                {theme.label}
              </ControlButton>
            ))}
          </ButtonGroup>
        </ControlSection>
        
        <ControlSection>
          <SectionTitle>Расположение</SectionTitle>
          <ButtonGroup>
            {layouts.map((layout) => (
              <ControlButton 
                key={layout.id}
                isActive={selectedLayout === layout.id}
                onClick={() => handleLayoutChange(layout.id)}
              >
                {layout.label}
              </ControlButton>
            ))}
          </ButtonGroup>
        </ControlSection>
        
        <ControlSection>
          <SectionTitle>Специальные эффекты</SectionTitle>
          <ButtonGroup>
            {effects.map((effect) => (
              <ControlButton 
                key={effect.id}
                isActive={selectedEffect === effect.id}
                onClick={() => handleEffectChange(effect.id as CardEffect)}
              >
                {effect.label}
              </ControlButton>
            ))}
          </ButtonGroup>
        </ControlSection>
      </ControlPanel>
      
      <ExampleSection>
        <SectionTitle>Масти и ранги</SectionTitle>
        <SectionDescription>Нажмите на карту, чтобы выбрать ее</SectionDescription>
        <CardsContainer layout={selectedLayout}>
          {cards.map((card, index) => (
            <CardWrapper key={index} layout={selectedLayout} index={index}>
              <Card
                suit={card.suit as 'hearts' | 'diamonds' | 'clubs' | 'spades'}
                rank={card.rank}
                isSelected={index === selectedCardIndex}
                onClick={() => handleCardClick(index)}
                enableSound={soundEnabled}
                animationSpeed="normal"
              />
              <CardLabel>
                {card.rank} {card.suit}
              </CardLabel>
            </CardWrapper>
          ))}
        </CardsContainer>
      </ExampleSection>
      
      <ExampleSection>
        <SectionTitle>Типы анимаций</SectionTitle>
        <SectionDescription>Выберите карту, чтобы увидеть анимацию</SectionDescription>
        <CardsContainer layout={selectedLayout}>
          {animations.map((animation, index) => (
            <CardWrapper key={`anim-${index}`} layout={selectedLayout} index={index}>
              <Card
                suit="hearts"
                rank="A"
                isSelected={index === selectedAnimationIndex}
                onClick={() => handleAnimationClick(index)}
                animationType={animation.type as any}
                enableSound={soundEnabled}
              />
              <CardLabel>{animation.label}</CardLabel>
            </CardWrapper>
          ))}
        </CardsContainer>
      </ExampleSection>
      
      <ExampleSection>
        <SectionTitle>Скорость анимации</SectionTitle>
        <SectionDescription>Выберите карту для демонстрации скорости</SectionDescription>
        <CardsContainer layout={selectedLayout}>
          {speeds.map((speed, index) => (
            <CardWrapper key={`speed-${index}`} layout={selectedLayout} index={index}>
              <Card
                suit="diamonds"
                rank="10"
                isSelected={index === selectedSpeedIndex}
                onClick={() => handleSpeedClick(index)}
                animationSpeed={speed.speed as any}
                enableSound={soundEnabled}
              />
              <CardLabel>{speed.label}</CardLabel>
            </CardWrapper>
          ))}
        </CardsContainer>
      </ExampleSection>
      
      <ExampleSection>
        <SectionTitle>Режим высокого контраста</SectionTitle>
        <SectionDescription>Сравните обычный и контрастный режимы</SectionDescription>
        <CardsContainer layout={selectedLayout}>
          {contrastExamples.map((example, index) => (
            <CardWrapper key={`contrast-${index}`} layout={selectedLayout} index={index}>
              <Card
                suit={example.suit as 'hearts' | 'diamonds' | 'clubs' | 'spades'}
                rank={example.rank}
                isSelected={index === selectedContrastIndex}
                onClick={() => handleContrastClick(index)}
                highContrast={example.highContrast}
                enableSound={soundEnabled}
              />
              <CardLabel>{example.label}</CardLabel>
            </CardWrapper>
          ))}
        </CardsContainer>
      </ExampleSection>
      
      <ExampleSection>
        <SectionTitle>Лицевая и обратная стороны</SectionTitle>
        <SectionDescription>Демонстрация карт лицом и рубашкой вверх</SectionDescription>
        <CardsContainer layout={selectedLayout}>
          {faceExamples.map((example, index) => (
            <CardWrapper key={`face-${index}`} layout={selectedLayout} index={index}>
              <Card
                suit={example.suit as 'hearts' | 'diamonds' | 'clubs' | 'spades'}
                rank={example.rank}
                isSelected={index === selectedFaceIndex}
                onClick={() => handleFaceClick(index)}
                isFaceDown={example.isFaceDown}
                enableSound={soundEnabled}
              />
              <CardLabel>{example.label}</CardLabel>
            </CardWrapper>
          ))}
        </CardsContainer>
      </ExampleSection>
      
      {/* Кнопки для переключения предпросмотров */}
      <PreviewToggleContainer>
        <PreviewToggleButton onClick={toggleLayoutPreview} isActive={showLayoutPreview}>
          {showLayoutPreview ? '📐 Скрыть варианты расположения' : '📐 Показать варианты расположения'}
        </PreviewToggleButton>
        
        <PreviewToggleButton onClick={toggleEffectsPreview} isActive={showEffectsPreview}>
          {showEffectsPreview ? '✨ Скрыть специальные эффекты' : '✨ Показать специальные эффекты'}
        </PreviewToggleButton>
        
        <PreviewToggleButton onClick={toggleDeckTypePreview} isActive={showDeckTypePreview}>
          {showDeckTypePreview ? '🃏 Скрыть типы колод' : '🃏 Показать типы колод'}
        </PreviewToggleButton>
      </PreviewToggleContainer>
      
      {/* Секция предпросмотра вариантов расположения */}
      {showLayoutPreview && (
        <ExampleSection>
          <SectionTitle>Варианты расположения карт</SectionTitle>
          <SectionDescription>Демонстрация различных способов расположения карт</SectionDescription>
          
          {layouts.map((layout) => (
            <LayoutPreviewSection key={layout.id}>
              <LayoutTitle>{layout.label}</LayoutTitle>
              <CardsContainer layout={layout.id}>
                {generatePreviewCards(5).map((card, index) => (
                  <CardWrapper key={`layout-${layout.id}-${index}`} layout={layout.id} index={index}>
                    <Card
                      suit={card.suit}
                      rank={card.rank}
                      theme={selectedDeckType}
                      enableSound={soundEnabled}
                    />
                  </CardWrapper>
                ))}
              </CardsContainer>
            </LayoutPreviewSection>
          ))}
        </ExampleSection>
      )}
      
      {/* Секция предпросмотра специальных эффектов */}
      {showEffectsPreview && (
        <ExampleSection>
          <SectionTitle>Специальные эффекты</SectionTitle>
          <SectionDescription>Демонстрация различных визуальных эффектов для карт</SectionDescription>
          
          <EffectsGrid>
            {effects.filter(effect => effect.id !== 'none').map((effect) => (
              <EffectPreviewCard key={effect.id}>
                <EffectTitle>{effect.label}</EffectTitle>
                <EffectCardContainer>
                  <Card
                    suit="hearts"
                    rank="A"
                    theme={selectedDeckType}
                    effect={effect.id as CardEffect}
                    enableSound={soundEnabled}
                  />
                </EffectCardContainer>
                <EffectDescription>
                  {effect.id === 'holographic' && 'Переливающийся эффект с изменением цветов при движении'}
                  {effect.id === 'metallic' && 'Металлический блеск с отражающей поверхностью'}
                  {effect.id === 'neon' && 'Яркое свечение по контуру карты'}
                  {effect.id === 'shadow' && 'Объемная тень, создающая эффект парения'}
                </EffectDescription>
              </EffectPreviewCard>
            ))}
          </EffectsGrid>
        </ExampleSection>
      )}
      
      {/* Секция предпросмотра типов колод */}
      {showDeckTypePreview && (
        <ExampleSection>
          <SectionTitle>Типы колод</SectionTitle>
          <SectionDescription>Сравнение различных стилей оформления карт</SectionDescription>
          
          <DeckTypesGrid>
            {deckTypes.map((deck) => (
              <DeckTypePreview key={deck.id}>
                <DeckTypeTitle>{deck.label}</DeckTypeTitle>
                <DeckTypeCards>
                  <Card
                    suit="hearts"
                    rank="A"
                    theme={deck.id as CardTheme}
                    enableSound={soundEnabled}
                  />
                  <Card
                    suit="spades"
                    rank="K"
                    theme={deck.id as CardTheme}
                    enableSound={soundEnabled}
                  />
                  <Card
                    suit="diamonds"
                    rank="Q"
                    theme={deck.id as CardTheme}
                    enableSound={soundEnabled}
                  />
                </DeckTypeCards>
                <DeckTypeDescription>
                  {deck.id === 'classic' && 'Традиционный дизайн игральных карт с классическими символами'}
                  {deck.id === 'modern' && 'Современный минималистичный дизайн с чистыми линиями'}
                  {deck.id === 'minimal' && 'Ультра-минималистичный дизайн с упрощенными символами'}
                  {deck.id === 'fantasy' && 'Фэнтезийный дизайн с декоративными элементами и орнаментами'}
                </DeckTypeDescription>
              </DeckTypePreview>
            ))}
          </DeckTypesGrid>
        </ExampleSection>
      )}
      
      <AdvancedConstructorToggle onClick={toggleAdvancedConstructor} isActive={showAdvancedConstructor}>
        {showAdvancedConstructor ? '🔧 Закрыть расширенный конструктор' : '🔧 Открыть расширенный конструктор карт'}
      </AdvancedConstructorToggle>
      
      {showAdvancedConstructor ? (
        <AdvancedCardConstructor>
          <ConstructorHeader>Расширенный конструктор карт</ConstructorHeader>
          
          <ConstructorGrid>
            <ConstructorSection>
              <SectionTitle>Масть</SectionTitle>
              <ButtonGroup>
                <ControlButton 
                  isActive={customCardConfig.suit === 'hearts'}
                  onClick={() => handleSuitChange('hearts')}
                >
                  ♥ Червы
                </ControlButton>
                <ControlButton 
                  isActive={customCardConfig.suit === 'diamonds'}
                  onClick={() => handleSuitChange('diamonds')}
                >
                  ♦ Бубны
                </ControlButton>
                <ControlButton 
                  isActive={customCardConfig.suit === 'clubs'}
                  onClick={() => handleSuitChange('clubs')}
                >
                  ♣ Трефы
                </ControlButton>
                <ControlButton 
                  isActive={customCardConfig.suit === 'spades'}
                  onClick={() => handleSuitChange('spades')}
                >
                  ♠ Пики
                </ControlButton>
              </ButtonGroup>
            </ConstructorSection>
            
            <ConstructorSection>
              <SectionTitle>Ранг</SectionTitle>
              <RankButtonsContainer>
                {['A', 2, 3, 4, 5, 6, 7, 8, 9, 10, 'J', 'Q', 'K'].map((r) => (
                  <RankButton 
                    key={r}
                    isActive={customCardConfig.rank === r}
                    onClick={() => handleRankChange(r)}
                  >
                    {r}
                  </RankButton>
                ))}
              </RankButtonsContainer>
            </ConstructorSection>
            
            <ConstructorSection>
              <SectionTitle>Тип анимации</SectionTitle>
              <ButtonGroup>
                {animations.map((animation, index) => (
                  <ControlButton 
                    key={`anim-${index}`}
                    isActive={customCardConfig.animationType === animation.type}
                    onClick={() => handleAnimationClick(index)}
                  >
                    {animation.label}
                  </ControlButton>
                ))}
              </ButtonGroup>
            </ConstructorSection>
            
            <ConstructorSection>
              <SectionTitle>Скорость анимации</SectionTitle>
              <ButtonGroup>
                {speeds.map((speed, index) => (
                  <ControlButton 
                    key={`speed-${index}`}
                    isActive={customCardConfig.animationSpeed === speed.speed}
                    onClick={() => handleSpeedClick(index)}
                  >
                    {speed.label}
                  </ControlButton>
                ))}
              </ButtonGroup>
            </ConstructorSection>
            
            <ConstructorSection>
              <SectionTitle>Тема карты</SectionTitle>
              <ButtonGroup>
                {deckTypes.map((deck) => (
                  <ControlButton 
                    key={deck.id}
                    isActive={customCardConfig.theme === deck.id}
                    onClick={() => handleDeckTypeChange(deck.id as CardTheme)}
                  >
                    {deck.label}
                  </ControlButton>
                ))}
              </ButtonGroup>
            </ConstructorSection>
            
            <ConstructorSection>
              <SectionTitle>Цветовая тема</SectionTitle>
              <ButtonGroup>
                {themes.map((theme) => (
                  <ControlButton 
                    key={theme.id}
                    isActive={customCardConfig.colorTheme === theme.id}
                    onClick={() => handleThemeChange(theme.id)}
                  >
                    {theme.label}
                  </ControlButton>
                ))}
              </ButtonGroup>
            </ConstructorSection>
            
            <ConstructorSection>
              <SectionTitle>Специальные эффекты</SectionTitle>
              <ButtonGroup>
                {effects.map((effect) => (
                  <ControlButton 
                    key={effect.id}
                    isActive={customCardConfig.effect === effect.id}
                    onClick={() => handleEffectChange(effect.id as CardEffect)}
                  >
                    {effect.label}
                  </ControlButton>
                ))}
              </ButtonGroup>
            </ConstructorSection>
            
            <ConstructorSection>
              <SectionTitle>Дополнительные настройки</SectionTitle>
              <ButtonGroup>
                <ControlButton 
                  isActive={customCardConfig.highContrast}
                  onClick={() => setCustomCardConfig(prev => ({ ...prev, highContrast: !prev.highContrast }))}
                >
                  {customCardConfig.highContrast ? 'Высокий контраст ✓' : 'Высокий контраст'}
                </ControlButton>
                <ControlButton 
                  isActive={customCardConfig.isFaceDown}
                  onClick={() => setCustomCardConfig(prev => ({ ...prev, isFaceDown: !prev.isFaceDown }))}
                >
                  {customCardConfig.isFaceDown ? 'Рубашкой вверх ✓' : 'Рубашкой вверх'}
                </ControlButton>
              </ButtonGroup>
            </ConstructorSection>
          </ConstructorGrid>
          
          <ConstructorPreview>
            <SectionTitle>Предпросмотр</SectionTitle>
            <PreviewContainer>
              <Card
                suit={customCardConfig.suit}
                rank={customCardConfig.rank}
                isSelected={isAnimating}
                animationType={customCardConfig.animationType}
                animationSpeed={customCardConfig.animationSpeed}
                highContrast={customCardConfig.highContrast}
                isFaceDown={customCardConfig.isFaceDown}
                theme={customCardConfig.theme}
                effect={customCardConfig.effect}
                colorTheme={customCardConfig.colorTheme as 'light' | 'dark' | 'colorful' | 'monochrome'}
                enableSound={soundEnabled}
              />
            </PreviewContainer>
            <PreviewButton onClick={triggerAnimation}>
              Запустить анимацию
            </PreviewButton>
          </ConstructorPreview>
        </AdvancedCardConstructor>
      ) : (
        <ExampleSection>
          <SectionTitle>Конструктор карт</SectionTitle>
          <SectionDescription>Настройте свою карту</SectionDescription>
          <CardBuilderContainer>
            <CardPreviewWrapper>
              <Card
                suit={customCardConfig.suit}
                rank={customCardConfig.rank}
                isSelected={isAnimating}
                animationType={customCardConfig.animationType}
                animationSpeed={customCardConfig.animationSpeed}
                highContrast={customCardConfig.highContrast}
                isFaceDown={customCardConfig.isFaceDown}
                enableSound={soundEnabled}
                theme={customCardConfig.theme}
                effect={customCardConfig.effect}
                colorTheme={customCardConfig.colorTheme as 'light' | 'dark' | 'colorful' | 'monochrome'}
              />
            </CardPreviewWrapper>
            
            <CardBuilderControls>
              <ControlGroup>
                <ControlLabel>Масть:</ControlLabel>
                <SelectControl 
                  value={customCardConfig.suit}
                  onChange={(e) => setCustomCardConfig({...customCardConfig, suit: e.target.value as any})}
                >
                  <option value="hearts">Червы</option>
                  <option value="diamonds">Бубны</option>
                  <option value="clubs">Трефы</option>
                  <option value="spades">Пики</option>
                </SelectControl>
              </ControlGroup>
              
              <ControlGroup>
                <ControlLabel>Ранг:</ControlLabel>
                <SelectControl 
                  value={customCardConfig.rank}
                  onChange={(e) => setCustomCardConfig({...customCardConfig, rank: e.target.value})}
                >
                  <option value="A">Туз</option>
                  <option value="K">Король</option>
                  <option value="Q">Дама</option>
                  <option value="J">Валет</option>
                  <option value="10">10</option>
                  <option value="9">9</option>
                  <option value="8">8</option>
                  <option value="7">7</option>
                  <option value="6">6</option>
                </SelectControl>
              </ControlGroup>
              
              <ControlGroup>
                <ControlLabel>Анимация:</ControlLabel>
                <SelectControl 
                  value={customCardConfig.animationType}
                  onChange={(e) => setCustomCardConfig({...customCardConfig, animationType: e.target.value as any})}
                >
                  {animations.map(anim => (
                    <option key={anim.type} value={anim.type}>{anim.label}</option>
                  ))}
                </SelectControl>
              </ControlGroup>
              
              <ControlGroup>
                <ControlLabel>Скорость:</ControlLabel>
                <SelectControl 
                  value={customCardConfig.animationSpeed}
                  onChange={(e) => setCustomCardConfig({...customCardConfig, animationSpeed: e.target.value as any})}
                >
                  {speeds.map(speed => (
                    <option key={speed.speed} value={speed.speed}>{speed.label}</option>
                  ))}
                </SelectControl>
              </ControlGroup>
              
              <ControlGroup>
                <ControlLabel>Эффект:</ControlLabel>
                <SelectControl 
                  value={customCardConfig.effect}
                  onChange={(e) => setCustomCardConfig({...customCardConfig, effect: e.target.value as any})}
                >
                  {effects.map(effect => (
                    <option key={effect.id} value={effect.id}>{effect.label}</option>
                  ))}
                </SelectControl>
              </ControlGroup>
              
              <ControlGroup>
                <CheckboxControl 
                  type="checkbox" 
                  id="highContrast" 
                  checked={customCardConfig.highContrast}
                  onChange={(e) => setCustomCardConfig({...customCardConfig, highContrast: e.target.checked})}
                />
                <CheckboxLabel htmlFor="highContrast">Высокий контраст</CheckboxLabel>
              </ControlGroup>
              
              <ControlGroup>
                <CheckboxControl 
                  type="checkbox" 
                  id="isFaceDown" 
                  checked={customCardConfig.isFaceDown}
                  onChange={(e) => setCustomCardConfig({...customCardConfig, isFaceDown: e.target.checked})}
                />
                <CheckboxLabel htmlFor="isFaceDown">Рубашкой вверх</CheckboxLabel>
              </ControlGroup>
              
              <AnimateButton onClick={triggerAnimation}>Анимировать</AnimateButton>
            </CardBuilderControls>
          </CardBuilderContainer>
        </ExampleSection>
      )}
      
      <ExampleSection>
        <SectionTitle>Настройки</SectionTitle>
        <SettingsContainer>
          <SoundToggle onClick={toggleSound} isActive={soundEnabled}>
            {soundEnabled ? '🔊 Звук включен' : '🔇 Звук выключен'}
          </SoundToggle>
        </SettingsContainer>
      </ExampleSection>
    </ExampleContainer>
  );
};

// Анимации
const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
`;

const slideIn = keyframes`
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
`;

const popIn = keyframes`
  0% { opacity: 0; transform: scale(0.8); }
  70% { transform: scale(1.05); }
  100% { opacity: 1; transform: scale(1); }
`;

const shimmer = keyframes`
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
`;

const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
`;

// Стилизованные компоненты
const ExampleContainer = styled.div<{ theme: string }>`
  padding: 30px;
  background-color: ${props => {
    switch(props.theme) {
      case 'dark': return '#2c3e50';
      case 'colorful': return '#f8f9d2';
      case 'monochrome': return '#e0e0e0';
      default: return '#f8f9fa';
    }
  }};
  color: ${props => props.theme === 'dark' ? '#ecf0f1' : '#333'};
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 1200px;
  margin: 0 auto;
  transition: background-color 0.3s, color 0.3s;
`;

const ExampleHeader = styled.h1`
  text-align: center;
  margin-bottom: 30px;
  font-size: 2rem;
  animation: ${fadeIn} 0.5s ease-out;
`;

const ControlPanel = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  animation: ${fadeIn} 0.5s ease-out;
`;

const ControlSection = styled.div`
  flex: 1;
  min-width: 200px;
`;

const ButtonGroup = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
`;

const ControlButton = styled.button<{ isActive: boolean }>`
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background-color: ${props => props.isActive ? '#4a6cf7' : '#e0e0e0'};
  color: ${props => props.isActive ? 'white' : '#333'};
  cursor: pointer;
  transition: all 0.2s;
  font-weight: ${props => props.isActive ? 'bold' : 'normal'};
  
  &:hover {
    background-color: ${props => props.isActive ? '#3a5ce7' : '#d0d0d0'};
  }
`;

const ExampleSection = styled.section`
  margin-bottom: 40px;
  animation: ${fadeIn} 0.5s ease-out;
  padding: 20px;
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: #333;
`;

const SectionDescription = styled.p`
  margin-bottom: 20px;
  color: #666;
`;

const CardsContainer = styled.div<{ layout: string }>`
  display: ${props => props.layout === 'grid' ? 'grid' : 'flex'};
  grid-template-columns: ${props => props.layout === 'grid' ? 'repeat(auto-fill, minmax(150px, 1fr))' : 'none'};
  flex-direction: ${props => {
    switch(props.layout) {
      case 'fan': return 'row';
      case 'stack': return 'column';
      case 'circle': return 'row';
      default: return 'row';
    }
  }};
  flex-wrap: ${props => props.layout === 'circle' ? 'wrap' : 'nowrap'};
  justify-content: center;
  align-items: center;
  gap: ${props => props.layout === 'stack' ? '2px' : '20px'};
  margin: 20px 0;
  position: relative;
  
  ${props => props.layout === 'fan' && `
    transform-origin: bottom left;
    padding-left: 50px;
    height: 300px;
    perspective: 600px;
  `}
  
  ${props => props.layout === 'stack' && `
    height: 300px;
    padding-top: 50px;
    padding-bottom: 50px;
  `}
  
  ${props => props.layout === 'circle' && `
    border-radius: 50%;
    padding: 50px;
    width: 400px;
    height: 400px;
    margin: 0 auto;
    position: relative;
  `}
`;

const CardWrapper = styled.div<{ layout?: string; index?: number }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10px;
  transition: all 0.3s ease;
  
  ${props => props.layout === 'fan' && `
    transform: rotate(calc(${props.index || 0} * 10deg - 20deg));
    margin-left: -40px;
    transform-origin: bottom center;
    z-index: calc(${props.index || 0} + 1);
    &:hover {
      transform: translateY(-20px) rotate(calc(${props.index || 0} * 10deg - 20deg));
      z-index: 10;
    }
  `}
  
  ${props => props.layout === 'stack' && `
    position: relative;
    margin-top: -120px;
    z-index: calc(${props.index || 0} + 1);
    &:first-child {
      margin-top: 0;
    }
    &:hover {
      transform: translateX(20px);
      z-index: 10;
    }
  `}
  
  ${props => props.layout === 'circle' && `
    position: absolute;
    top: 50%;
    left: 50%;
    margin: 0;
    transform: translate(-50%, -50%) rotate(calc(${props.index || 0} * 72deg)) translateX(150px) rotate(calc(${props.index || 0} * -72deg));
    &:hover {
      transform: translate(-50%, -50%) rotate(calc(${props.index || 0} * 72deg)) translateX(160px) rotate(calc(${props.index || 0} * -72deg)) scale(1.1);
    }
  `}
  
  &:hover {
    transform: ${props => props.layout === 'grid' ? 'scale(1.05)' : ''};
    z-index: ${props => props.layout === 'grid' ? 'auto' : '10'};
  }
`;

const CardLabel = styled.div`
  margin-top: 10px;
  font-size: 0.9rem;
  color: #666;
  text-align: center;
`;

const SettingsContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 20px;
`;

const SoundToggle = styled.button<{ isActive: boolean }>`
  padding: 10px 20px;
  border: none;
  border-radius: 30px;
  background-color: ${props => props.isActive ? '#4CAF50' : '#f44336'};
  color: white;
  cursor: pointer;
  transition: background-color 0.3s;
  font-weight: bold;
  
  &:hover {
    background-color: ${props => props.isActive ? '#3e8e41' : '#d32f2f'};
  }
`;

const CardBuilderContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  justify-content: center;
  align-items: flex-start;
  margin-top: 20px;
`;

const CardPreviewWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 200px;
  min-height: 300px;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 10px;
  animation: ${pulse} 2s infinite ease-in-out;
`;

const CardBuilderControls = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;
  min-width: 250px;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 10px;
`;

const ControlGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

const ControlLabel = styled.label`
  min-width: 80px;
  font-weight: bold;
`;

const SelectControl = styled.select`
  padding: 8px;
  border-radius: 5px;
  border: 1px solid #ccc;
  background-color: white;
  flex: 1;
`;

const CheckboxControl = styled.input`
  margin: 0;
`;

const CheckboxLabel = styled.label`
  margin-left: 5px;
`;

const AnimateButton = styled.button`
  padding: 10px 15px;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  margin-top: 10px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #3a5ce7;
  }
`;

// Стили для кнопок переключения предпросмотров
const PreviewToggleContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 15px;
  margin: 20px 0;
  animation: ${fadeIn} 0.5s ease;
`;

const PreviewToggleButton = styled.button<{ isActive: boolean }>`
  padding: 10px 18px;
  background-color: ${props => props.isActive ? '#4a6da7' : '#6c8eca'};
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  
  &:hover {
    background-color: ${props => props.isActive ? '#3a5d97' : '#5c7eba'};
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
  }
`;

// Стили для секции предпросмотра расположений
const LayoutPreviewSection = styled.div`
  margin: 25px 0;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  animation: ${slideIn} 0.5s ease;
`;

const LayoutTitle = styled.h4`
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #2c3e50;
  text-align: center;
`;

// Стили для секции предпросмотра эффектов
const EffectsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
`;

const EffectPreviewCard = styled.div`
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  animation: ${popIn} 0.5s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
  }
`;

const EffectTitle = styled.h4`
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #2c3e50;
`;

const EffectCardContainer = styled.div`
  margin: 15px 0;
  transform: scale(1.1);
`;

const EffectDescription = styled.p`
  font-size: 14px;
  color: #5d6d7e;
  text-align: center;
  margin-top: 15px;
`;

// Стили для секции предпросмотра типов колод
const DeckTypesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 25px;
  margin-top: 20px;
`;

const DeckTypePreview = styled.div`
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  animation: ${popIn} 0.5s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
  }
`;

const DeckTypeTitle = styled.h4`
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #2c3e50;
`;

const DeckTypeCards = styled.div`
  display: flex;
  gap: 10px;
  margin: 15px 0;
  
  > * {
    transform: scale(0.85);
  }
`;

const DeckTypeDescription = styled.p`
  font-size: 14px;
  color: #5d6d7e;
  text-align: center;
  margin-top: 15px;
`;

// Компоненты для расширенного конструктора карт
const AdvancedCardConstructor = styled.div`
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 25px;
  margin: 30px 0;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  animation: ${fadeIn} 0.4s ease-out;
`;

const ConstructorHeader = styled.h2`
  text-align: center;
  margin-bottom: 25px;
  color: #4a6cf7;
  font-size: 24px;
  border-bottom: 2px solid #4a6cf7;
  padding-bottom: 15px;
`;

const ConstructorGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
`;

const ConstructorSection = styled.div`
  background-color: rgba(255, 255, 255, 0.05);
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

const ConstructorPreview = styled.div`
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30px;
  padding: 25px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 16px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
`;

const PreviewContainer = styled.div`
  margin: 20px 0;
  transform: scale(1.2);
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.3);
  }
`;

const PreviewButton = styled.button`
  padding: 12px 24px;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.2s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  
  &:hover {
    background-color: #3a5ce7;
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }
  
  &:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
`;

const RankButtonsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
`;

const RankButton = styled.button<{ isActive: boolean }>`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background-color: ${props => props.isActive ? '#4a6cf7' : '#e0e0e0'};
  color: ${props => props.isActive ? 'white' : '#333'};
  font-weight: ${props => props.isActive ? 'bold' : 'normal'};
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: ${props => props.isActive ? '#3a5ce7' : '#d0d0d0'};
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(1px);
  }
`;

const AdvancedConstructorToggle = styled.button<{ isActive: boolean }>`
  padding: 12px 24px;
  background-color: ${props => props.isActive ? '#ff9800' : '#4a6cf7'};
  color: white;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  font-weight: bold;
  margin: 20px auto;
  display: block;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  
  &:hover {
    background-color: ${props => props.isActive ? '#f57c00' : '#3a5ce7'};
    transform: translateY(-3px);
    box-shadow: 0 6px 14px rgba(0, 0, 0, 0.15);
  }
  
  &:active {
    transform: translateY(1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
  
  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    background-size: 200% 100%;
    border-radius: inherit;
    animation: ${props => props.isActive ? shimmer : 'none'} 2s infinite linear;
  }
`;