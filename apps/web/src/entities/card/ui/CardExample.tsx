import React, { useState } from 'react';
import { Card } from './Card';
import styled from 'styled-components';
import { keyframes } from 'styled-components';

// Пример использования компонента Card с новыми возможностями
export const CardExample: React.FC = () => {
  const [selectedCardIndex, setSelectedCardIndex] = useState<number | null>(null);
  const [selectedAnimationIndex, setSelectedAnimationIndex] = useState<number | null>(null);
  const [selectedSpeedIndex, setSelectedSpeedIndex] = useState<number | null>(null);
  const [selectedContrastIndex, setSelectedContrastIndex] = useState<number | null>(null);
  const [selectedFaceIndex, setSelectedFaceIndex] = useState<number | null>(null);
  const [soundEnabled, setSoundEnabled] = useState<boolean>(true);
  
  // Примеры карт с разными настройками
  const cards = [
    { suit: 'hearts', rank: 'A' },
    { suit: 'diamonds', rank: 10 },
    { suit: 'clubs', rank: 'K' },
    { suit: 'spades', rank: 'Q' },
    { suit: 'hearts', rank: 'J' },
  ];
  
  // Примеры анимаций
  const animations = [
    { type: 'simple', label: 'Простая' },
    { type: 'pulse', label: 'Пульсация' },
    { type: 'flip', label: 'Переворот' },
  ];
  
  // Примеры скоростей
  const speeds = [
    { speed: 'slow', label: 'Медленно' },
    { speed: 'normal', label: 'Нормально' },
    { speed: 'fast', label: 'Быстро' },
  ];
  
  // Примеры контрастности
  const contrastExamples = [
    { suit: 'hearts', rank: 'A', highContrast: false, label: 'Обычный' },
    { suit: 'hearts', rank: 'A', highContrast: true, label: 'Высокий контраст' },
  ];
  
  // Примеры лицевой/обратной стороны
  const faceExamples = [
    { suit: 'spades', rank: 'K', isFaceDown: false, label: 'Лицевая сторона' },
    { suit: 'spades', rank: 'K', isFaceDown: true, label: 'Рубашкой вверх' },
  ];
  
  const handleCardClick = (index: number) => {
    setSelectedCardIndex(index === selectedCardIndex ? null : index);
  };
  
  const handleAnimationClick = (index: number) => {
    setSelectedAnimationIndex(index === selectedAnimationIndex ? null : index);
  };
  
  const handleSpeedClick = (index: number) => {
    setSelectedSpeedIndex(index === selectedSpeedIndex ? null : index);
  };
  
  const handleContrastClick = (index: number) => {
    setSelectedContrastIndex(index === selectedContrastIndex ? null : index);
  };
  
  const handleFaceClick = (index: number) => {
    setSelectedFaceIndex(index === selectedFaceIndex ? null : index);
  };
  
  const toggleSound = () => {
    setSoundEnabled(!soundEnabled);
  };
  
  return (
    <ExampleContainer>
      <ExampleHeader>Интерактивная демонстрация карт</ExampleHeader>
      
      <ExampleSection>
        <SectionTitle>Масти и ранги</SectionTitle>
        <SectionDescription>Нажмите на карту, чтобы выбрать ее</SectionDescription>
        <CardsContainer>
          {cards.map((card, index) => (
            <CardWrapper key={index}>
              <Card
                suit={card.suit as 'hearts' | 'diamonds' | 'clubs' | 'spades'}
                rank={card.rank}
                isSelected={index === selectedCardIndex}
                onClick={() => handleCardClick(index)}
                enableSound={soundEnabled}
                animationSpeed="normal"
              />
              <CardLabel>
                {card.rank} {card.suit}
              </CardLabel>
            </CardWrapper>
          ))}
        </CardsContainer>
      </ExampleSection>
      
      <ExampleSection>
        <SectionTitle>Типы анимаций</SectionTitle>
        <SectionDescription>Выберите карту, чтобы увидеть анимацию</SectionDescription>
        <CardsContainer>
          {animations.map((animation, index) => (
            <CardWrapper key={`anim-${index}`}>
              <Card
                suit="hearts"
                rank="A"
                isSelected={index === selectedAnimationIndex}
                onClick={() => handleAnimationClick(index)}
                animationType={animation.type as 'simple' | 'pulse' | 'flip'}
                enableSound={soundEnabled}
              />
              <CardLabel>{animation.label}</CardLabel>
            </CardWrapper>
          ))}
        </CardsContainer>
      </ExampleSection>
      
      <ExampleSection>
        <SectionTitle>Скорость анимации</SectionTitle>
        <SectionDescription>Выберите карту для демонстрации скорости</SectionDescription>
        <CardsContainer>
          {speeds.map((speed, index) => (
            <CardWrapper key={`speed-${index}`}>
              <Card
                suit="diamonds"
                rank="10"
                isSelected={index === selectedSpeedIndex}
                onClick={() => handleSpeedClick(index)}
                animationSpeed={speed.speed as 'slow' | 'normal' | 'fast'}
                enableSound={soundEnabled}
              />
              <CardLabel>{speed.label}</CardLabel>
            </CardWrapper>
          ))}
        </CardsContainer>
      </ExampleSection>
      
      <ExampleSection>
        <SectionTitle>Режим высокого контраста</SectionTitle>
        <SectionDescription>Сравните обычный и контрастный режимы</SectionDescription>
        <CardsContainer>
          {contrastExamples.map((example, index) => (
            <CardWrapper key={`contrast-${index}`}>
              <Card
                suit={example.suit as 'hearts' | 'diamonds' | 'clubs' | 'spades'}
                rank={example.rank}
                isSelected={index === selectedContrastIndex}
                onClick={() => handleContrastClick(index)}
                highContrast={example.highContrast}
                enableSound={soundEnabled}
              />
              <CardLabel>{example.label}</CardLabel>
            </CardWrapper>
          ))}
        </CardsContainer>
      </ExampleSection>
      
      <ExampleSection>
        <SectionTitle>Лицевая и обратная стороны</SectionTitle>
        <SectionDescription>Демонстрация карт лицом и рубашкой вверх</SectionDescription>
        <CardsContainer>
          {faceExamples.map((example, index) => (
            <CardWrapper key={`face-${index}`}>
              <Card
                suit={example.suit as 'hearts' | 'diamonds' | 'clubs' | 'spades'}
                rank={example.rank}
                isSelected={index === selectedFaceIndex}
                onClick={() => handleFaceClick(index)}
                isFaceDown={example.isFaceDown}
                enableSound={soundEnabled}
              />
              <CardLabel>{example.label}</CardLabel>
            </CardWrapper>
          ))}
        </CardsContainer>
      </ExampleSection>
      
      <ExampleSection>
        <SectionTitle>Настройки</SectionTitle>
        <SettingsContainer>
          <SoundToggle onClick={toggleSound} isActive={soundEnabled}>
            {soundEnabled ? '🔊 Звук включен' : '🔇 Звук выключен'}
          </SoundToggle>
        </SettingsContainer>
      </ExampleSection>
    </ExampleContainer>
  );
};

const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
`;

const ExampleContainer = styled.div`
  padding: 30px;
  background-color: #f8f9fa;
  border-radius: 16px;
  max-width: 900px;
  margin: 0 auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  animation: ${fadeIn} 0.5s ease-out;
`;

const ExampleHeader = styled.h1`
  text-align: center;
  color: #2c3e50;
  margin-bottom: 30px;
  font-size: 28px;
  font-weight: 600;
`;

const ExampleSection = styled.section`
  margin-bottom: 40px;
  padding: 20px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
  }
`;

const SectionTitle = styled.h2`
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 22px;
  font-weight: 500;
`;

const SectionDescription = styled.p`
  color: #7f8c8d;
  margin-bottom: 20px;
  font-size: 16px;
`;

const CardsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  margin-bottom: 20px;
  justify-content: center;
`;

const CardWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: translateY(-5px);
  }
`;

const CardLabel = styled.div`
  font-size: 14px;
  color: #666;
  text-align: center;
  max-width: 120px;
  background-color: #f1f3f5;
  padding: 6px 10px;
  border-radius: 6px;
  font-weight: 500;
`;

const SettingsContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 20px;
`;

const SoundToggle = styled.button<{ isActive: boolean }>`
  background-color: ${props => props.isActive ? '#4a90e2' : '#e0e0e0'};
  color: ${props => props.isActive ? 'white' : '#666'};
  border: none;
  border-radius: 30px;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: ${props => props.isActive ? '#3a7bc8' : '#d0d0d0'};
    transform: scale(1.05);
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${props => props.isActive ? 'rgba(74, 144, 226, 0.5)' : 'rgba(224, 224, 224, 0.5)'};
  }
`;}]}}}