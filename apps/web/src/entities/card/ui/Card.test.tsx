import { render, screen, fireEvent } from '@testing-library/react';
import { Card } from './Card';

// Мокируем зависимости
jest.mock('@shared/lib/i18n', () => ({
  useTranslation: () => ({ t: (key: string) => key })
}));

describe('Компонент Card', () => {
  it('корректно отображается с переданными пропсами', () => {
    const handleClick = jest.fn();
    render(
      <Card 
        suit="hearts" 
        rank="A" 
        isFlipped={false} 
        onClick={handleClick} 
        data-testid="card"
      />
    );
    
    const cardElement = screen.getByTestId('card');
    expect(cardElement).toBeInTheDocument();
    expect(cardElement).toHaveTextContent('A');
    expect(cardElement).toHaveClass('hearts');
  });
  
  it('вызывает onClick при клике', () => {
    const handleClick = jest.fn();
    render(
      <Card 
        suit="hearts" 
        rank="A" 
        isFlipped={false} 
        onClick={handleClick} 
        data-testid="card"
      />
    );
    
    fireEvent.click(screen.getByTestId('card'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  it('показывает рубашку карты, когда isFlipped=true', () => {
    render(
      <Card 
        suit="hearts" 
        rank="A" 
        isFlipped={true} 
        onClick={() => {}} 
        data-testid="card"
      />
    );
    
    const cardElement = screen.getByTestId('card');
    expect(cardElement).toHaveClass('flipped');
    expect(cardElement).not.toHaveTextContent('A');
  });
  
  it('применяет класс selected, когда isSelected=true', () => {
    render(
      <Card 
        suit="hearts" 
        rank="A" 
        isFlipped={false} 
        isSelected={true}
        onClick={() => {}} 
        data-testid="card"
      />
    );
    
    const cardElement = screen.getByTestId('card');
    expect(cardElement).toHaveClass('selected');
  });
});