import React from 'react';
import styled from 'styled-components';
import { Card } from '@a1-k/core'; // Импортируем тип Card из core

interface CardProps {
  card: Card | null; // Карта может быть null (например, для рубашки)
  isTrump?: boolean;
  onClick?: () => void;
  style?: React.CSSProperties;
  className?: string;
}

// Определяем масти и их символы/цвета
const suitSymbols: { [key: string]: { symbol: string; color: string } } = {
  H: { symbol: '♥', color: 'red' }, // Hearts
  D: { symbol: '♦', color: 'red' }, // Diamonds
  C: { symbol: '♣', color: 'black' }, // Clubs
  S: { symbol: '♠', color: 'black' }, // Spades
};

const CardWrapper = styled.div<{
  suitColor: string;
  isTrump?: boolean;
  clickable: boolean;
}>`
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 10px 5px;
  margin: 2px;
  min-width: 40px; /* Минимальная ширина */
  display: inline-flex; /* Используем inline-flex для центрирования */
  flex-direction: column;
  align-items: center; /* Центрируем содержимое по горизонтали */
  justify-content: center; /* Центрируем содержимое по вертикали */
  background-color: white;
  color: ${(props) => props.suitColor};
  font-size: 1.2em;
  font-weight: bold;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  cursor: ${(props) => (props.clickable ? 'pointer' : 'default')};
  transition: transform 0.1s ease-in-out, box-shadow 0.1s ease-in-out;
  border-color: ${(props) => (props.isTrump ? 'gold' : '#ccc')}; /* Выделяем козырь */
  border-width: ${(props) => (props.isTrump ? '2px' : '1px')};

  &:hover {
    transform: ${(props) => (props.clickable ? 'translateY(-3px)' : 'none')};
    box-shadow: ${(props) =>
      props.clickable ? '2px 2px 5px rgba(0, 0, 0, 0.3)' : '1px 1px 3px rgba(0, 0, 0, 0.2)'};
  }
`;

const CardRank = styled.span`
  display: block;
`;

const CardSuit = styled.span`
  display: block;
  font-size: 1.1em; /* Немного увеличим символ масти */
`;

const CardBack = styled(CardWrapper)`
  background-color: #6c757d; /* Цвет рубашки */
  color: transparent; /* Скрываем текст */
  border-color: #5a6268;
  min-height: 60px; /* Примерная высота карты */
  &:hover {
    transform: none;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }
`;

export const CardComponent: React.FC<CardProps> = ({ card, isTrump, onClick, style, className }) => {
  if (!card) {
    // Отображаем рубашку карты
    return <CardBack suitColor="transparent" clickable={false} style={style} className={className}>?</CardBack>;
  }

  const { rank, suit } = card;
  const suitInfo = suitSymbols[suit] || { symbol: '?', color: 'black' };

  return (
    <CardWrapper
      suitColor={suitInfo.color}
      isTrump={isTrump}
      onClick={onClick}
      clickable={!!onClick}
      style={style}
      className={className}
    >
      <CardRank>{rank}</CardRank>
      <CardSuit>{suitInfo.symbol}</CardSuit>
    </CardWrapper>
  );
};