// Типы для сущности Card

export type CardSuit = "hearts" | "diamonds" | "clubs" | "spades";
export type CardRank = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13;

// Расширенные типы для улучшенных компонентов карт
export type CardAnimationType =
  | "simple"
  | "pulse"
  | "flip"
  | "3d-rotate"
  | "glow"
  | "cascade"
  | "particle";
export type CardAnimationSpeed = "slow" | "normal" | "fast" | "instant";
export type CardTheme = "classic" | "modern" | "minimal" | "fantasy";
export type CardColorTheme = "light" | "dark" | "colorful" | "monochrome";
export type CardLayout = "grid" | "fan" | "stack" | "circle";
export type CardEffect =
  | "none"
  | "holographic"
  | "metallic"
  | "neon"
  | "shadow";

export interface CardModel {
  id: string;
  suit: CardSuit;
  rank: CardRank;
  isVisible: boolean; // Видна ли карта игроку
  theme?: CardTheme; // Тема оформления карты
  effect?: CardEffect; // Специальный эффект карты
}

// Состояние для хранения карт в Redux
export interface CardsState {
  playerCards: CardModel[];
  tableCards: CardModel[];
  trumpCard?: CardModel;
  isLoading: boolean;
  error: string | null;
  selectedTheme: CardTheme; // Выбранная тема колоды
  selectedColorTheme: CardColorTheme; // Выбранная цветовая тема
}
