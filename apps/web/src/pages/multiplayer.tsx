import React from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { MultiplayerGame } from '../entities/game/ui/MultiplayerGame';
import { MultiplayerProvider, useMultiplayer } from '../entities/game/model/MultiplayerContext';

// Компонент для отображения содержимого многопользовательского режима
const MultiplayerContent: React.FC = () => {
  const router = useRouter();
  
  // Используем контекст для доступа к состоянию и методам
  const {
    gameId,
    playerId,
    playerName,
    players,
    availableGames,
    chatMessages,
    notifications,
    moveHistory,
    playerStats,
    gameState,
    isLoading,
    isSyncing,
    settings,
    joinGame,
    createGame,
    leaveGame,
    sendMessage,
    playerMove,
    updateSettings,
    connectionError,
    isConnected,
    reconnect
  } = useMultiplayer();
  
  // Обработчик ошибок подключения
  React.useEffect(() => {
    if (connectionError) {
      console.error('Ошибка подключения:', connectionError);
      // Можно добавить автоматическое переподключение при определенных ошибках
      if (!isConnected && connectionError.message.includes('соединение')) {
        reconnect();
      }
    }
  }, [connectionError, isConnected, reconnect]);
  
  return (
    <>
      <Head>
        <title>Дурак - Многопользовательский режим</title>
        <meta name="description" content="Многопользовательский режим игры в Дурака" />
      </Head>
      
      <MultiplayerGame
        gameId={gameId}
        playerId={playerId}
        playerName={playerName}
        onJoinGame={joinGame}
        onCreateGame={createGame}
        onLeaveGame={leaveGame}
        onSendMessage={sendMessage}
        onPlayerMove={playerMove}
        gameState={gameState}
        players={players}
        availableGames={availableGames}
        chatMessages={chatMessages}
        notifications={notifications}
        moveHistory={moveHistory}
        playerStats={playerStats}
        isLoading={isLoading}
        isSyncing={isSyncing}
        settings={settings}
        onSettingsChange={updateSettings}
      />
    </>
  );
};

// Основной компонент страницы с провайдером контекста
const MultiplayerPage: React.FC = () => {
  const router = useRouter();
  const { gameId: routeGameId } = router.query;
  
  return (
    <MultiplayerProvider initialGameId={routeGameId as string | undefined}>
      <MultiplayerContent />
    </MultiplayerProvider>
  );
};

export default MultiplayerPage;