import Head from "next/head";
import { useState, useEffect, useCallback } from "react";
import styled from "styled-components";
import { useRouter } from "next/router";
import AnimatedCard from "@/components/Card/AnimatedCard";
// Временно используем прямой импорт для отладки
// import {
//   DurakGame,
//   BotFactory,
//   BotDifficulty,
//   GameStatus,
//   PlayerAction,
//   DurakVariant,
//   GameEventData,
//   GameEvent
// } from "@kozyr-master/core";

// Создаем заглушки для тестирования UI
const DurakGame = null;
const BotFactory = null;
const BotDifficulty = { MEDIUM: 'medium' };
const GameStatus = { NOT_STARTED: 'not_started', IN_PROGRESS: 'in_progress', FINISHED: 'finished' };
const PlayerAction = { ATTACK: 'attack', DEFEND: 'defend', TAKE: 'take', PASS: 'pass' };
const DurakVariant = { CLASSIC: 'classic' };
const GameEvent = { GAME_STARTED: 'game_started', GAME_ENDED: 'game_ended', PLAYER_MOVED: 'player_moved' };

const DurakGamePage = () => {
  const router = useRouter();
  const [game, setGame] = useState<any>(null);

  // Инициализируем состояние сразу с заглушкой
  const [gameState, setGameState] = useState<any>({
    gameStatus: GameStatus.NOT_STARTED,
    players: [
      {
        id: "human",
        name: "Вы",
        hand: [
          { rank: "7", suit: "♠" },
          { rank: "K", suit: "♥" },
          { rank: "9", suit: "♦" },
          { rank: "A", suit: "♣" },
          { rank: "10", suit: "♠" },
          { rank: "J", suit: "♥" }
        ],
        isActive: true
      },
      {
        id: "bot",
        name: "Бот",
        hand: Array(6).fill({ rank: "?", suit: "?" }),
        isActive: false
      }
    ],
    currentPlayerIndex: 0,
    attackerIndex: 0,
    defenderIndex: 1,
    tableCards: [],
    deck: Array(24).fill({ rank: "?", suit: "?" }),
    trumpCard: { rank: "6", suit: "♠" },
    trumpSuit: "♠"
  });

  const [selectedCard, setSelectedCard] = useState<number | null>(null);
  const [gameLog, setGameLog] = useState<string[]>(["Игра готова к запуску"]);
  const [isGameStarted, setIsGameStarted] = useState(false);

  // Обработчик событий игры
  const handleGameEvent = useCallback((eventData: GameEventData) => {
    console.log('Game event:', eventData);
    
    setGameState(eventData.gameState);
    
    // Добавляем сообщение в лог
    if (eventData.message) {
      setGameLog(prev => [...prev.slice(-9), eventData.message!]);
    }

    // Обрабатываем разные типы событий
    switch (eventData.type) {
      case GameEvent.GAME_STARTED:
        setIsGameStarted(true);
        break;
      case GameEvent.GAME_ENDED:
        setIsGameStarted(false);
        break;
    }
  }, []);

  // Создание новой игры
  const createNewGame = useCallback(() => {
    // Временная заглушка
    console.log("Создание новой игры (заглушка)");

    const mockGameState = {
      gameStatus: GameStatus.NOT_STARTED,
      players: [
        {
          id: "human",
          name: "Вы",
          hand: [
            { rank: "7", suit: "♠" },
            { rank: "K", suit: "♥" },
            { rank: "9", suit: "♦" },
            { rank: "A", suit: "♣" },
            { rank: "10", suit: "♠" },
            { rank: "J", suit: "♥" }
          ],
          isActive: true
        },
        {
          id: "bot",
          name: "Бот",
          hand: Array(6).fill({ rank: "?", suit: "?" }),
          isActive: false
        }
      ],
      currentPlayerIndex: 0,
      attackerIndex: 0,
      defenderIndex: 1,
      tableCards: [],
      deck: Array(24).fill({ rank: "?", suit: "?" }),
      trumpCard: { rank: "6", suit: "♠" },
      trumpSuit: "♠"
    };

    setGameState(mockGameState);
    setGameLog(["Новая игра создана"]);
    setSelectedCard(null);
    setIsGameStarted(false);

    return null;
  }, []);

  // Запуск игры
  const startGame = useCallback(() => {
    console.log("Запуск игры (заглушка)");
    setIsGameStarted(true);
    setGameState(prev => prev ? { ...prev, gameStatus: GameStatus.IN_PROGRESS } : null);
    setGameLog(prev => [...prev, "Игра началась! Ваш ход."]);
  }, []);

  // Ход игрока
  const makePlayerMove = useCallback((action: any, cardIndex?: number) => {
    if (!gameState) return false;

    console.log(`Ход игрока (заглушка): ${action}, карта: ${cardIndex}`);

    let message = "";
    switch (action) {
      case PlayerAction.ATTACK:
        if (cardIndex !== undefined && gameState.players[0].hand[cardIndex]) {
          const card = gameState.players[0].hand[cardIndex];
          message = `Вы атакуете картой ${card.rank}${card.suit}`;
          // Добавляем карту на стол
          setGameState(prev => prev ? {
            ...prev,
            tableCards: [...prev.tableCards, [card]]
          } : null);
        }
        break;
      case PlayerAction.DEFEND:
        message = `Вы защищаетесь`;
        break;
      case PlayerAction.TAKE:
        message = `Вы берете карты`;
        break;
      case PlayerAction.PASS:
        message = `Вы говорите "бито"`;
        break;
    }

    setGameLog(prev => [...prev.slice(-9), message]);
    setSelectedCard(null);

    return true;
  }, [gameState]);

  // Инициализация игры при загрузке компонента
  useEffect(() => {
    // Временная заглушка для тестирования UI
    const mockGameState = {
      gameStatus: GameStatus.NOT_STARTED,
      players: [
        {
          id: "human",
          name: "Вы",
          hand: [
            { rank: "7", suit: "♠" },
            { rank: "K", suit: "♥" },
            { rank: "9", suit: "♦" },
            { rank: "A", suit: "♣" },
            { rank: "10", suit: "♠" },
            { rank: "J", suit: "♥" }
          ],
          isActive: true
        },
        {
          id: "bot",
          name: "Бот",
          hand: [
            { rank: "?", suit: "?" },
            { rank: "?", suit: "?" },
            { rank: "?", suit: "?" },
            { rank: "?", suit: "?" },
            { rank: "?", suit: "?" },
            { rank: "?", suit: "?" }
          ],
          isActive: false
        }
      ],
      currentPlayerIndex: 0,
      attackerIndex: 0,
      defenderIndex: 1,
      tableCards: [],
      deck: Array(24).fill({ rank: "?", suit: "?" }),
      trumpCard: { rank: "6", suit: "♠" },
      trumpSuit: "♠"
    };

    setGameState(mockGameState);
    setGameLog(["Игра готова к запуску"]);
  }, []);

  // Обработчики действий
  const handleCardClick = (cardIndex: number) => {
    if (!gameState || gameState.gameStatus !== GameStatus.IN_PROGRESS) return;
    
    const currentPlayer = gameState.players[gameState.currentPlayerIndex];
    if (currentPlayer.id !== "human") return;

    setSelectedCard(cardIndex);
  };

  const handleAttack = () => {
    if (selectedCard !== null) {
      makePlayerMove(PlayerAction.ATTACK, selectedCard);
    }
  };

  const handleDefend = () => {
    if (selectedCard !== null) {
      makePlayerMove(PlayerAction.DEFEND, selectedCard);
    }
  };

  const handleTake = () => {
    makePlayerMove(PlayerAction.TAKE);
  };

  const handlePass = () => {
    makePlayerMove(PlayerAction.PASS);
  };

  const handleBackToHome = () => {
    router.push("/");
  };

  if (!gameState) {
    return <LoadingContainer>Загрузка игры...</LoadingContainer>;
  }

  const humanPlayer = gameState.players.find((p: any) => p.id === "human");
  const botPlayer = gameState.players.find((p: any) => p.id !== "human");
  const isHumanTurn = gameState.players[gameState.currentPlayerIndex]?.id === "human";

  return (
    <Container>
      <Head>
        <title>Дурак - Козырь Мастер</title>
        <meta name="description" content="Играйте в Дурака против бота" />
      </Head>

      <GameHeader>
        <BackButton onClick={handleBackToHome}>← Назад</BackButton>
        <GameTitle>Дурак</GameTitle>
        <NewGameButton onClick={createNewGame}>Новая игра</NewGameButton>
      </GameHeader>

      <GameBoard>
        {/* Карты бота */}
        <BotArea>
          <PlayerInfo>
            <PlayerName>{botPlayer?.name}</PlayerName>
            <CardCount>Карт: {botPlayer?.hand.length}</CardCount>
          </PlayerInfo>
          <BotCards>
            {Array.from({ length: botPlayer?.hand.length || 0 }).map((_, index) => (
              <AnimatedCard
                key={index}
                rank="?"
                suit="?"
                isFlipped={true}
                size="small"
                disabled={true}
              />
            ))}
          </BotCards>
        </BotArea>

        {/* Центральная область */}
        <CenterArea>
          <GameInfo>
            <TrumpInfo>
              Козырь: {gameState.trumpCard ? `${gameState.trumpCard.rank} ${gameState.trumpCard.suit}` : "Неизвестно"}
            </TrumpInfo>
            <DeckInfo>Колода: {gameState.deck.length}</DeckInfo>
            <StatusInfo>
              {gameState.gameStatus === GameStatus.NOT_STARTED && "Игра не начата"}
              {gameState.gameStatus === GameStatus.IN_PROGRESS && (isHumanTurn ? "Ваш ход" : "Ход бота")}
              {gameState.gameStatus === GameStatus.FINISHED && 
                `Игра окончена! ${gameState.winner?.name === "Вы" ? "Вы победили!" : "Бот победил!"}`
              }
            </StatusInfo>
          </GameInfo>

          {/* Стол */}
          <TableArea>
            <TableTitle>Стол</TableTitle>
            <TableCards>
              {gameState.tableCards.map((pair: any[], pairIndex: number) => (
                <CardPair key={pairIndex}>
                  {pair.map((card: any, cardIndex: number) => (
                    <TableCardWrapper key={cardIndex} isDefense={cardIndex === 1}>
                      <AnimatedCard
                        rank={card.rank}
                        suit={card.suit}
                        size="medium"
                        animationType="slide"
                        disabled={true}
                      />
                    </TableCardWrapper>
                  ))}
                </CardPair>
              ))}
            </TableCards>
          </TableArea>
        </CenterArea>

        {/* Карты игрока */}
        <PlayerArea>
          <PlayerInfo>
            <PlayerName>{humanPlayer?.name}</PlayerName>
            <CardCount>Карт: {humanPlayer?.hand.length}</CardCount>
          </PlayerInfo>
          <PlayerCards>
            {humanPlayer?.hand.map((card: any, index: number) => (
              <AnimatedCard
                key={index}
                rank={card.rank}
                suit={card.suit}
                isSelected={selectedCard === index}
                onClick={() => handleCardClick(index)}
                animationType={selectedCard === index ? 'glow' : 'none'}
                size="medium"
              />
            ))}
          </PlayerCards>
        </PlayerArea>
      </GameBoard>

      {/* Панель управления */}
      <ControlPanel>
        {!isGameStarted ? (
          <StartButton onClick={startGame}>Начать игру</StartButton>
        ) : (
          <ActionButtons>
            <ActionButton 
              onClick={handleAttack} 
              disabled={!isHumanTurn || selectedCard === null}
            >
              Атаковать
            </ActionButton>
            <ActionButton 
              onClick={handleDefend} 
              disabled={!isHumanTurn || selectedCard === null}
            >
              Защищаться
            </ActionButton>
            <ActionButton 
              onClick={handleTake} 
              disabled={!isHumanTurn}
            >
              Взять
            </ActionButton>
            <ActionButton 
              onClick={handlePass} 
              disabled={!isHumanTurn}
            >
              Пас
            </ActionButton>
          </ActionButtons>
        )}
      </ControlPanel>

      {/* Лог игры */}
      <GameLogArea>
        <LogTitle>Лог игры</LogTitle>
        <LogContent>
          {gameLog.map((message, index) => (
            <LogMessage key={index}>{message}</LogMessage>
          ))}
        </LogContent>
      </GameLogArea>
    </Container>
  );
};

// Стилизованные компоненты
const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  display: flex;
  flex-direction: column;
`;

const LoadingContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  font-size: 1.5rem;
`;

const GameHeader = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
`;

const BackButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const GameTitle = styled.h1`
  font-size: 2rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
`;

const NewGameButton = styled.button`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;

  &:hover {
    background: linear-gradient(135deg, #45a049, #4CAF50);
    transform: translateY(-2px);
  }
`;

const GameBoard = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  gap: 1rem;
`;

const BotArea = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
`;

const PlayerArea = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
`;

const PlayerInfo = styled.div`
  display: flex;
  gap: 2rem;
  align-items: center;
`;

const PlayerName = styled.h3`
  margin: 0;
  font-size: 1.2rem;
`;

const CardCount = styled.span`
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
`;

const BotCards = styled.div`
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
`;

const BotCard = styled.div`
  width: 60px;
  height: 84px;
  background: linear-gradient(135deg, #8B4513, #A0522D);
  border: 2px solid #654321;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  position: relative;

  &::after {
    content: "🂠";
    font-size: 2rem;
  }
`;

const PlayerCards = styled.div`
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
  max-width: 100%;
`;

const PlayerCard = styled.div<{ isSelected: boolean }>`
  width: 80px;
  height: 112px;
  background: ${props => props.isSelected ?
    'linear-gradient(135deg, #FFD700, #FFA500)' :
    'linear-gradient(135deg, #FFFFFF, #F0F0F0)'
  };
  border: 3px solid ${props => props.isSelected ? '#FF6B35' : '#DDD'};
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  color: #333;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  }
`;

const CardRank = styled.div`
  font-size: 1.2rem;
  font-weight: bold;
`;

const CardSuit = styled.div`
  font-size: 1.5rem;
`;

const CenterArea = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  padding: 2rem 0;
`;

const GameInfo = styled.div`
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
`;

const TrumpInfo = styled.div`
  background: rgba(255, 215, 0, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 1px solid rgba(255, 215, 0, 0.3);
`;

const DeckInfo = styled.div`
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const StatusInfo = styled.div`
  background: rgba(76, 175, 80, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 1px solid rgba(76, 175, 80, 0.3);
  font-weight: 600;
`;

const TableArea = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  min-height: 150px;
`;

const TableTitle = styled.h3`
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
`;

const TableCards = styled.div`
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  min-height: 100px;
  align-items: center;
`;

const CardPair = styled.div`
  display: flex;
  gap: 0.25rem;
  position: relative;
`;

const TableCardWrapper = styled.div<{ isDefense?: boolean }>`
  transform: ${props => props.isDefense ? 'rotate(15deg) translateX(-20px)' : 'none'};
  z-index: ${props => props.isDefense ? 2 : 1};
  transition: transform 0.3s ease;
`;

const TableCard = styled.div<{ isDefense?: boolean }>`
  width: 70px;
  height: 98px;
  background: linear-gradient(135deg, #FFFFFF, #F0F0F0);
  border: 2px solid #DDD;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-weight: bold;
  font-size: 0.9rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
`;

const ControlPanel = styled.div`
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: center;
`;

const StartButton = styled.button`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(76, 175, 80, 0.3);

  &:hover {
    background: linear-gradient(135deg, #45a049, #4CAF50);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
`;

const ActionButton = styled.button<{ disabled?: boolean }>`
  background: ${props => props.disabled ?
    'rgba(255, 255, 255, 0.1)' :
    'linear-gradient(135deg, #2196F3, #1976D2)'
  };
  border: none;
  color: ${props => props.disabled ? 'rgba(255, 255, 255, 0.5)' : 'white'};
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.3s ease;
  box-shadow: ${props => props.disabled ? 'none' : '0 4px 12px rgba(33, 150, 243, 0.3)'};

  &:hover {
    ${props => !props.disabled && `
      background: linear-gradient(135deg, #1976D2, #2196F3);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(33, 150, 243, 0.4);
    `}
  }
`;

const GameLogArea = styled.div`
  background: rgba(0, 0, 0, 0.3);
  padding: 1rem;
  max-height: 200px;
  overflow-y: auto;
`;

const LogTitle = styled.h4`
  margin: 0 0 1rem 0;
  color: rgba(255, 255, 255, 0.8);
`;

const LogContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
`;

const LogMessage = styled.div`
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  padding: 0.25rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
`;

export default DurakGamePage;
