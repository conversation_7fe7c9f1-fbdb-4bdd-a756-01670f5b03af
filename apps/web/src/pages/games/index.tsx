import React from 'react';
import Head from 'next/head';
import styled from 'styled-components';
import { GameLobby } from '../../entities/game/ui/multiplayer';

const GamesPage: React.FC = () => {
  return (
    <Container>
      <Head>
        <title>Игры - Козырь Мастер</title>
        <meta
          name="description"
          content="Выберите или создайте игровую комнату"
        />
      </Head>

      <Main>
        <GameLobby serverUrl="http://localhost:3001" />
      </Main>
    </Container>
  );
};

const Container = styled.div`
  min-height: 100vh;
  padding: 0 0.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #1a1a2e;
  color: white;
`;

const Main = styled.main`
  padding: 3rem 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 1200px;
`;

export default GamesPage;