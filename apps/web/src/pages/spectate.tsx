import Head from "next/head";
import { useState, useEffect } from "react";
import styled from "styled-components";
import { useRouter } from "next/router";
import { useSocket } from "@/hooks/useSocket";
import AnimatedCard from "@/components/Card/AnimatedCard";

const SpectatePage = () => {
  const router = useRouter();
  const socket = useSocket();
  const [chatMessage, setChatMessage] = useState("");

  const handleBackToHome = () => {
    router.push("/");
  };

  const handleBackToGames = () => {
    if (socket.isSpectating && socket.currentSpectatorGame) {
      socket.leaveSpectator(socket.currentSpectatorGame.gameId);
    }
    router.push("/spectate/games");
  };

  // Загружаем список игр при подключении
  useEffect(() => {
    if (socket.connected) {
      socket.getSpectatorGames();
    }
  }, [socket.connected]);

  const handleSendMessage = () => {
    if (chatMessage.trim() && socket.currentSpectatorGame) {
      socket.sendSpectatorChatMessage(socket.currentSpectatorGame.gameId, chatMessage.trim());
      setChatMessage("");
    }
  };

  // Если не наблюдаем за игрой, показываем список игр
  if (!socket.isSpectating || !socket.currentSpectatorGame) {
    return (
      <Container>
        <Head>
          <title>Наблюдение за играми - Козырь Мастер</title>
          <meta name="description" content="Наблюдайте за играми других игроков" />
        </Head>

        <Header>
          <BackButton onClick={handleBackToHome}>← Назад</BackButton>
          <Title>Наблюдение за играми</Title>
        </Header>

        <Main>
          {!socket.connected ? (
            <NotConnected>
              <Message>Подключитесь к серверу, чтобы наблюдать за играми</Message>
              <ConnectButton onClick={() => router.push("/multiplayer")}>
                Подключиться
              </ConnectButton>
            </NotConnected>
          ) : !socket.player ? (
            <NotConnected>
              <Message>Войдите в игру, чтобы наблюдать за играми</Message>
              <ConnectButton onClick={() => router.push("/multiplayer")}>
                Войти в игру
              </ConnectButton>
            </NotConnected>
          ) : (
            <GamesSection>
              <SectionHeader>
                <SectionTitle>Активные игры ({socket.spectatorGames.length})</SectionTitle>
                <RefreshButton onClick={() => socket.getSpectatorGames()}>
                  🔄 Обновить
                </RefreshButton>
              </SectionHeader>

              {socket.spectatorGames.length === 0 ? (
                <EmptyState>
                  Нет активных игр для просмотра
                </EmptyState>
              ) : (
                <GamesGrid>
                  {socket.spectatorGames.map((game) => (
                    <GameCard key={game.gameId}>
                      <GameHeader>
                        <GameName>{game.roomName}</GameName>
                        <StatusBadge>
                          {game.status === 'playing' ? '🎮 Идет' : '⏳ Ожидание'}
                        </StatusBadge>
                      </GameHeader>

                      <PlayersInfo>
                        <PlayersTitle>Игроки:</PlayersTitle>
                        <PlayersList>
                          {game.players.map((player, index) => (
                            <PlayerName key={player.id}>
                              {player.name}
                              {index < game.players.length - 1 && ', '}
                            </PlayerName>
                          ))}
                        </PlayersList>
                      </PlayersInfo>

                      <GameStats>
                        <StatItem>
                          <StatIcon>👥</StatIcon>
                          <StatText>{game.spectators.length} зрителей</StatText>
                        </StatItem>
                        {game.startedAt && (
                          <StatItem>
                            <StatIcon>⏰</StatIcon>
                            <StatText>
                              {Math.round((Date.now() - new Date(game.startedAt).getTime()) / 60000)}м
                            </StatText>
                          </StatItem>
                        )}
                      </GameStats>

                      <GameActions>
                        <WatchButton 
                          onClick={() => socket.joinSpectator(game.gameId)}
                          disabled={game.status !== 'playing'}
                        >
                          {game.status === 'playing' ? 'Смотреть' : 'Недоступно'}
                        </WatchButton>
                      </GameActions>
                    </GameCard>
                  ))}
                </GamesGrid>
              )}
            </GamesSection>
          )}
        </Main>
      </Container>
    );
  }

  // Показываем интерфейс наблюдения за игрой
  const game = socket.currentSpectatorGame;
  const gameState = game.gameState;

  return (
    <Container>
      <Head>
        <title>Наблюдение: {game.roomName} - Козырь Мастер</title>
      </Head>

      <Header>
        <BackButton onClick={handleBackToGames}>← К списку игр</BackButton>
        <Title>Наблюдение: {game.roomName}</Title>
        <SpectatorCount>👥 {game.spectators.length} зрителей</SpectatorCount>
      </Header>

      <Main>
        <GameLayout>
          <GameBoard>
            {/* Информация об игроках */}
            <PlayersArea>
              {game.players.map((player, index) => (
                <PlayerSection key={player.id}>
                  <PlayerInfo>
                    <PlayerName>{player.name}</PlayerName>
                    {gameState?.gameState?.players?.[index] && (
                      <CardCount>
                        Карт: {gameState.gameState.players[index].handSize || 0}
                      </CardCount>
                    )}
                  </PlayerInfo>
                  <PlayerCards>
                    {Array.from({ 
                      length: gameState?.gameState?.players?.[index]?.handSize || 0 
                    }).map((_, cardIndex) => (
                      <AnimatedCard
                        key={cardIndex}
                        rank="?"
                        suit="?"
                        isFlipped={true}
                        size="small"
                        disabled={true}
                      />
                    ))}
                  </PlayerCards>
                </PlayerSection>
              ))}
            </PlayersArea>

            {/* Центральная область игры */}
            <CenterArea>
              <GameInfo>
                <InfoItem>
                  <strong>Козырь:</strong> {gameState?.gameState?.trumpCard ? 
                    `${gameState.gameState.trumpCard.rank} ${gameState.gameState.trumpCard.suit}` : 
                    "Неизвестно"
                  }
                </InfoItem>
                <InfoItem>
                  <strong>Колода:</strong> {gameState?.gameState?.deck?.length || 0}
                </InfoItem>
                <InfoItem>
                  <strong>Текущий игрок:</strong> {
                    gameState?.gameState?.players?.[gameState?.gameState?.currentPlayerIndex]?.name || "Неизвестно"
                  }
                </InfoItem>
              </GameInfo>

              {/* Стол */}
              <TableArea>
                <TableTitle>Стол</TableTitle>
                <TableCards>
                  {gameState?.gameState?.tableCards?.map((pair: any[], pairIndex: number) => (
                    <CardPair key={pairIndex}>
                      {pair.map((card: any, cardIndex: number) => (
                        <TableCardWrapper key={cardIndex} $isDefense={cardIndex === 1}>
                          <AnimatedCard
                            rank={card.rank}
                            suit={card.suit}
                            size="medium"
                            animationType="slide"
                            disabled={true}
                          />
                        </TableCardWrapper>
                      ))}
                    </CardPair>
                  ))}
                  {(!gameState?.gameState?.tableCards || gameState.gameState.tableCards.length === 0) && (
                    <EmptyTable>Стол пуст</EmptyTable>
                  )}
                </TableCards>
              </TableArea>
            </CenterArea>
          </GameBoard>

          {/* Чат зрителей */}
          <SpectatorChat>
            <ChatTitle>Чат зрителей</ChatTitle>
            <ChatMessages>
              {socket.spectatorChatMessages.map((msg) => (
                <ChatMessage key={msg.id} $type={msg.type}>
                  <MessageHeader>
                    <MessageAuthor $type={msg.type}>
                      {msg.spectatorName}
                    </MessageAuthor>
                    <MessageTime>
                      {new Date(msg.timestamp).toLocaleTimeString()}
                    </MessageTime>
                  </MessageHeader>
                  <MessageText>{msg.message}</MessageText>
                </ChatMessage>
              ))}
            </ChatMessages>
            <ChatInput>
              <Input
                type="text"
                placeholder="Сообщение для зрителей..."
                value={chatMessage}
                onChange={(e) => setChatMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              />
              <SendButton onClick={handleSendMessage} disabled={!chatMessage.trim()}>
                ➤
              </SendButton>
            </ChatInput>
          </SpectatorChat>
        </GameLayout>
      </Main>
    </Container>
  );
};

// Стилизованные компоненты
const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
`;

const Header = styled.header`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
`;

const BackButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const Title = styled.h1`
  font-size: 2rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
`;

const SpectatorCount = styled.div`
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 600;
`;

const Main = styled.main`
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
`;

const NotConnected = styled.div`
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 3rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const Message = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
`;

const ConnectButton = styled.button`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
`;

const GamesSection = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 2rem;
`;

const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const SectionTitle = styled.h2`
  margin: 0;
  color: #FFD700;
  font-size: 1.8rem;
`;

const RefreshButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.2rem;
`;

const GamesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
`;

const GameCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }
`;

const GameHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

const GameName = styled.h3`
  margin: 0;
  color: white;
  font-size: 1.3rem;
`;

const StatusBadge = styled.div`
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #4CAF50;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
`;

const PlayersInfo = styled.div`
  margin-bottom: 1rem;
`;

const PlayersTitle = styled.h4`
  margin: 0 0 0.5rem 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
`;

const PlayersList = styled.div`
  display: flex;
  flex-wrap: wrap;
`;

const PlayerName = styled.span`
  color: white;
  font-weight: 600;
`;

const GameStats = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const StatItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const StatIcon = styled.span`
  font-size: 1rem;
`;

const StatText = styled.span`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
`;

const GameActions = styled.div`
  display: flex;
  justify-content: center;
`;

const WatchButton = styled.button<{ disabled?: boolean }>`
  background: ${props => props.disabled ?
    'rgba(255, 255, 255, 0.1)' :
    'linear-gradient(135deg, #2196F3, #1976D2)'
  };
  border: none;
  color: ${props => props.disabled ? 'rgba(255, 255, 255, 0.5)' : 'white'};
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.3s ease;
  width: 100%;

  &:hover {
    ${props => !props.disabled && `
      background: linear-gradient(135deg, #1976D2, #2196F3);
      transform: translateY(-1px);
    `}
  }
`;

const GameLayout = styled.div`
  display: flex;
  gap: 1rem;
  height: calc(100vh - 200px);
`;

const GameBoard = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
`;

const PlayersArea = styled.div`
  display: flex;
  justify-content: space-around;
  gap: 2rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
`;

const PlayerSection = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
`;

const PlayerInfo = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
`;

const CardCount = styled.span`
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
`;

const PlayerCards = styled.div`
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
  justify-content: center;
`;

const CenterArea = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const GameInfo = styled.div`
  display: flex;
  gap: 2rem;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 12px;
`;

const InfoItem = styled.div`
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
`;

const TableArea = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  min-height: 200px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  padding: 1rem;
`;

const TableTitle = styled.h3`
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
`;

const TableCards = styled.div`
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  flex: 1;
`;

const CardPair = styled.div`
  display: flex;
  gap: 0.25rem;
  position: relative;
`;

const TableCardWrapper = styled.div<{ $isDefense?: boolean }>`
  transform: ${props => props.$isDefense ? 'rotate(15deg) translateX(-20px)' : 'none'};
  z-index: ${props => props.$isDefense ? 2 : 1};
  transition: transform 0.3s ease;
`;

const EmptyTable = styled.div`
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
  font-size: 1.1rem;
`;

const SpectatorChat = styled.div`
  width: 300px;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const ChatTitle = styled.h3`
  margin: 0;
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #FFD700;
`;

const ChatMessages = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 400px;
`;

const ChatMessage = styled.div<{ $type: string }>`
  padding: 0.5rem;
  border-radius: 4px;
  background: ${props => {
    switch (props.$type) {
      case 'system': return 'rgba(255, 193, 7, 0.1)';
      default: return 'rgba(255, 255, 255, 0.05)';
    }
  }};
  border-left: 3px solid ${props => {
    switch (props.$type) {
      case 'system': return '#FFC107';
      default: return 'rgba(255, 255, 255, 0.2)';
    }
  }};
`;

const MessageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
`;

const MessageAuthor = styled.span<{ $type: string }>`
  font-weight: 600;
  font-size: 0.9rem;
  color: ${props => {
    switch (props.$type) {
      case 'system': return '#FFC107';
      default: return '#2196F3';
    }
  }};
`;

const MessageTime = styled.span`
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
`;

const MessageText = styled.div`
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  font-size: 0.9rem;
`;

const ChatInput = styled.div`
  display: flex;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
`;

const Input = styled.input`
  flex: 1;
  padding: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.9rem;

  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  &:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  }
`;

const SendButton = styled.button<{ disabled?: boolean }>`
  background: ${props => props.disabled ?
    'rgba(255, 255, 255, 0.1)' :
    'linear-gradient(135deg, #4CAF50, #45a049)'
  };
  color: ${props => props.disabled ? 'rgba(255, 255, 255, 0.5)' : 'white'};
  border: none;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.3s ease;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    ${props => !props.disabled && `
      background: linear-gradient(135deg, #45a049, #4CAF50);
    `}
  }
`;

export default SpectatePage;
