import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import styled from 'styled-components';
import { useSocket } from '../../hooks/useSocket';
import { useAuth } from '../../hooks/useAuth';
import { Button } from '../../components/common/Button';
import { Modal } from '../../components/common/Modal';
import { useNotifications } from '../../components/notifications/NotificationSystem';

interface Tournament {
  id: string;
  name: string;
  description: string;
  gameType: string;
  type: 'single_elimination' | 'double_elimination' | 'round_robin';
  status: 'upcoming' | 'registration' | 'active' | 'finished';
  maxParticipants: number;
  currentParticipants: number;
  entryFee: number;
  prizePool: number;
  registrationStart: Date;
  registrationEnd: Date;
  startTime: Date;
  endTime?: Date;
  organizer: {
    id: string;
    name: string;
  };
  winner?: {
    id: string;
    name: string;
  };
  isRegistered: boolean;
}

const TournamentsContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  padding: 20px;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 40px;
  color: white;
`;

const Title = styled.h1`
  font-size: 48px;
  margin: 0 0 20px 0;
  background: linear-gradient(135deg, #ffd700 0%, #ffb300 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
`;

const Subtitle = styled.p`
  font-size: 18px;
  opacity: 0.8;
  margin: 0;
`;

const FilterContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 30px;
  flex-wrap: wrap;
`;

const FilterButton = styled.button<{ active: boolean }>`
  background: ${props => props.active ? 
    'linear-gradient(135deg, #4a90e2 0%, #357abd 100%)' : 
    'rgba(255, 255, 255, 0.1)'
  };
  border: 1px solid ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.3)'};
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    background: ${props => props.active ? 
      'linear-gradient(135deg, #357abd 0%, #2c5aa0 100%)' : 
      'rgba(255, 255, 255, 0.2)'
    };
    transform: translateY(-2px);
  }
`;

const TournamentsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
`;

const TournamentCard = styled.div<{ status: Tournament['status'] }>`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid ${props => {
    switch (props.status) {
      case 'registration': return '#4caf50';
      case 'active': return '#ff9800';
      case 'finished': return '#2196f3';
      default: return 'rgba(255, 255, 255, 0.2)';
    }
  }};
  color: white;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: ${props => {
      switch (props.status) {
        case 'registration': return 'linear-gradient(90deg, #4caf50 0%, #45a049 100%)';
        case 'active': return 'linear-gradient(90deg, #ff9800 0%, #f57c00 100%)';
        case 'finished': return 'linear-gradient(90deg, #2196f3 0%, #1976d2 100%)';
        default: return 'linear-gradient(90deg, #666 0%, #444 100%)';
      }
    }};
  }
`;

const TournamentHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
`;

const TournamentName = styled.h3`
  margin: 0;
  font-size: 20px;
  color: #4a90e2;
`;

const StatusBadge = styled.div<{ status: Tournament['status'] }>`
  background: ${props => {
    switch (props.status) {
      case 'registration': return 'rgba(76, 175, 80, 0.2)';
      case 'active': return 'rgba(255, 152, 0, 0.2)';
      case 'finished': return 'rgba(33, 150, 243, 0.2)';
      default: return 'rgba(255, 255, 255, 0.1)';
    }
  }};
  color: ${props => {
    switch (props.status) {
      case 'registration': return '#4caf50';
      case 'active': return '#ff9800';
      case 'finished': return '#2196f3';
      default: return 'white';
    }
  }};
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
`;

const TournamentInfo = styled.div`
  margin-bottom: 20px;
`;

const InfoRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
`;

const InfoLabel = styled.span`
  opacity: 0.7;
`;

const InfoValue = styled.span`
  font-weight: bold;
`;

const PrizePool = styled.div`
  background: linear-gradient(135deg, #ffd700 0%, #ffb300 100%);
  color: #333;
  padding: 10px;
  border-radius: 8px;
  text-align: center;
  font-weight: bold;
  margin-bottom: 15px;
`;

const TournamentActions = styled.div`
  display: flex;
  gap: 10px;
`;

const CreateTournamentForm = styled.form`
  display: grid;
  gap: 15px;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 5px;
`;

const Label = styled.label`
  font-weight: bold;
  color: white;
`;

const Input = styled.input`
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
  
  &:focus {
    outline: none;
    border-color: #4a90e2;
    background: rgba(255, 255, 255, 0.15);
  }
`;

const Select = styled.select`
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  
  option {
    background: #1a1a2e;
    color: white;
  }
  
  &:focus {
    outline: none;
    border-color: #4a90e2;
    background: rgba(255, 255, 255, 0.15);
  }
`;

const TextArea = styled.textarea`
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  resize: vertical;
  min-height: 80px;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
  
  &:focus {
    outline: none;
    border-color: #4a90e2;
    background: rgba(255, 255, 255, 0.15);
  }
`;

export default function TournamentsPage() {
  const [tournaments, setTournaments] = useState<Tournament[]>([]);
  const [filter, setFilter] = useState<'all' | 'registration' | 'active' | 'finished'>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [loading, setLoading] = useState(true);
  
  const router = useRouter();
  const { socket } = useSocket();
  const { user } = useAuth();
  const { showSuccess, showError } = useNotifications();

  useEffect(() => {
    if (!socket) return;

    // Загружаем турниры
    socket.emit('tournaments:get_list');

    socket.on('tournaments:list', (data: Tournament[]) => {
      setTournaments(data);
      setLoading(false);
    });

    socket.on('tournaments:registered', (data: { tournamentId: string }) => {
      setTournaments(prev => prev.map(t => 
        t.id === data.tournamentId 
          ? { ...t, isRegistered: true, currentParticipants: t.currentParticipants + 1 }
          : t
      ));
      showSuccess('Registration Successful', 'You have been registered for the tournament!');
    });

    socket.on('tournaments:created', (tournament: Tournament) => {
      setTournaments(prev => [tournament, ...prev]);
      showSuccess('Tournament Created', 'Your tournament has been created successfully!');
      setShowCreateModal(false);
    });

    socket.on('tournaments:error', (data: { message: string }) => {
      showError('Error', data.message);
    });

    return () => {
      socket.off('tournaments:list');
      socket.off('tournaments:registered');
      socket.off('tournaments:created');
      socket.off('tournaments:error');
    };
  }, [socket]);

  const handleRegister = (tournamentId: string) => {
    if (!socket || !user) return;
    socket.emit('tournaments:register', { tournamentId });
  };

  const handleViewTournament = (tournamentId: string) => {
    router.push(`/tournaments/${tournamentId}`);
  };

  const handleCreateTournament = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!socket) return;

    const formData = new FormData(e.currentTarget);
    const tournamentData = {
      name: formData.get('name'),
      description: formData.get('description'),
      gameType: formData.get('gameType'),
      type: formData.get('type'),
      maxParticipants: parseInt(formData.get('maxParticipants') as string),
      entryFee: parseFloat(formData.get('entryFee') as string),
      startTime: new Date(formData.get('startTime') as string),
    };

    socket.emit('tournaments:create', tournamentData);
  };

  const filteredTournaments = tournaments.filter(tournament => {
    if (filter === 'all') return true;
    return tournament.status === filter;
  });

  const formatDateTime = (date: Date) => {
    return new Date(date).toLocaleString();
  };

  const formatPrize = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusText = (status: Tournament['status']) => {
    switch (status) {
      case 'registration': return 'Open Registration';
      case 'active': return 'In Progress';
      case 'finished': return 'Completed';
      default: return 'Upcoming';
    }
  };

  if (loading) {
    return (
      <TournamentsContainer>
        <div style={{ textAlign: 'center', color: 'white', marginTop: '100px' }}>
          Loading tournaments...
        </div>
      </TournamentsContainer>
    );
  }

  return (
    <TournamentsContainer>
      <Header>
        <Title>🏆 Tournaments</Title>
        <Subtitle>Compete with the best players and win amazing prizes!</Subtitle>
      </Header>

      <FilterContainer>
        <FilterButton active={filter === 'all'} onClick={() => setFilter('all')}>
          All Tournaments
        </FilterButton>
        <FilterButton active={filter === 'registration'} onClick={() => setFilter('registration')}>
          Open Registration
        </FilterButton>
        <FilterButton active={filter === 'active'} onClick={() => setFilter('active')}>
          In Progress
        </FilterButton>
        <FilterButton active={filter === 'finished'} onClick={() => setFilter('finished')}>
          Completed
        </FilterButton>
        <Button onClick={() => setShowCreateModal(true)}>
          Create Tournament
        </Button>
      </FilterContainer>

      <TournamentsGrid>
        {filteredTournaments.map(tournament => (
          <TournamentCard key={tournament.id} status={tournament.status}>
            <TournamentHeader>
              <TournamentName>{tournament.name}</TournamentName>
              <StatusBadge status={tournament.status}>
                {getStatusText(tournament.status)}
              </StatusBadge>
            </TournamentHeader>

            <TournamentInfo>
              <InfoRow>
                <InfoLabel>Game:</InfoLabel>
                <InfoValue>{tournament.gameType}</InfoValue>
              </InfoRow>
              <InfoRow>
                <InfoLabel>Type:</InfoLabel>
                <InfoValue>{tournament.type.replace('_', ' ')}</InfoValue>
              </InfoRow>
              <InfoRow>
                <InfoLabel>Players:</InfoLabel>
                <InfoValue>{tournament.currentParticipants}/{tournament.maxParticipants}</InfoValue>
              </InfoRow>
              <InfoRow>
                <InfoLabel>Entry Fee:</InfoLabel>
                <InfoValue>{formatPrize(tournament.entryFee)}</InfoValue>
              </InfoRow>
              <InfoRow>
                <InfoLabel>Start Time:</InfoLabel>
                <InfoValue>{formatDateTime(tournament.startTime)}</InfoValue>
              </InfoRow>
            </TournamentInfo>

            <PrizePool>
              Prize Pool: {formatPrize(tournament.prizePool)}
            </PrizePool>

            <TournamentActions>
              <Button 
                variant="secondary" 
                size="small" 
                onClick={() => handleViewTournament(tournament.id)}
              >
                View Details
              </Button>
              
              {tournament.status === 'registration' && !tournament.isRegistered && (
                <Button 
                  variant="success" 
                  size="small"
                  onClick={() => handleRegister(tournament.id)}
                  disabled={tournament.currentParticipants >= tournament.maxParticipants}
                >
                  Register
                </Button>
              )}
              
              {tournament.isRegistered && (
                <Button variant="primary" size="small" disabled>
                  Registered
                </Button>
              )}
            </TournamentActions>
          </TournamentCard>
        ))}
      </TournamentsGrid>

      {showCreateModal && (
        <Modal onClose={() => setShowCreateModal(false)} title="Create Tournament">
          <CreateTournamentForm onSubmit={handleCreateTournament}>
            <FormGroup>
              <Label>Tournament Name</Label>
              <Input name="name" type="text" required placeholder="Enter tournament name" />
            </FormGroup>

            <FormGroup>
              <Label>Description</Label>
              <TextArea name="description" placeholder="Describe your tournament" />
            </FormGroup>

            <FormGroup>
              <Label>Game Type</Label>
              <Select name="gameType" required>
                <option value="durak">Durak</option>
                <option value="poker">Poker</option>
                <option value="preferans">Preferans</option>
              </Select>
            </FormGroup>

            <FormGroup>
              <Label>Tournament Type</Label>
              <Select name="type" required>
                <option value="single_elimination">Single Elimination</option>
                <option value="double_elimination">Double Elimination</option>
                <option value="round_robin">Round Robin</option>
              </Select>
            </FormGroup>

            <FormGroup>
              <Label>Max Participants</Label>
              <Select name="maxParticipants" required>
                <option value="8">8 Players</option>
                <option value="16">16 Players</option>
                <option value="32">32 Players</option>
                <option value="64">64 Players</option>
              </Select>
            </FormGroup>

            <FormGroup>
              <Label>Entry Fee ($)</Label>
              <Input name="entryFee" type="number" min="0" step="0.01" required placeholder="0.00" />
            </FormGroup>

            <FormGroup>
              <Label>Start Time</Label>
              <Input name="startTime" type="datetime-local" required />
            </FormGroup>

            <div style={{ display: 'flex', gap: '10px', marginTop: '20px' }}>
              <Button type="submit" variant="success">
                Create Tournament
              </Button>
              <Button type="button" variant="secondary" onClick={() => setShowCreateModal(false)}>
                Cancel
              </Button>
            </div>
          </CreateTournamentForm>
        </Modal>
      )}
    </TournamentsContainer>
  );
}
