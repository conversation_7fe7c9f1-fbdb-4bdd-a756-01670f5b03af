import React from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import styled from 'styled-components';
import { GameRoom } from '../../../entities/game/ui/multiplayer';

const GameRoomPage: React.FC = () => {
  const router = useRouter();
  const { id } = router.query;

  return (
    <Container>
      <Head>
        <title>Игровая комната - Козырь Мастер</title>
        <meta
          name="description"
          content="Игровая комната для многопользовательской игры"
        />
      </Head>

      <Main>
        {id ? (
          <GameRoom serverUrl="http://localhost:3001" roomId={id as string} />
        ) : (
          <LoadingMessage>Загрузка комнаты...</LoadingMessage>
        )}
      </Main>
    </Container>
  );
};

const Container = styled.div`
  min-height: 100vh;
  padding: 0 0.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #1a1a2e;
  color: white;
`;

const Main = styled.main`
  padding: 3rem 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 1200px;
`;

const LoadingMessage = styled.div`
  font-size: 1.5rem;
  color: #ccc;
  text-align: center;
  padding: 2rem;
`;

export default GameRoomPage;