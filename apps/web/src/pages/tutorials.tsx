import Head from "next/head";
import styled from "styled-components";
import { useRouter } from "next/router";

const TutorialsPage = () => {
  const router = useRouter();

  const handleBackToHome = () => {
    router.push("/");
  };

  return (
    <Container>
      <Head>
        <title>Обучение - Козырь Мастер</title>
        <meta name="description" content="Изучите правила карточных игр" />
      </Head>

      <Header>
        <BackButton onClick={handleBackToHome}>← Назад</BackButton>
        <Title>Обучение</Title>
      </Header>

      <Main>
        <Content>
          <Section>
            <SectionTitle>🎴 Правила игры "Дурак"</SectionTitle>
            <RulesList>
              <Rule>
                <RuleTitle>Цель игры:</RuleTitle>
                <RuleText>Избавиться от всех карт. Последний игрок с картами становится "дураком".</RuleText>
              </Rule>
              
              <Rule>
                <RuleTitle>Подготовка:</RuleTitle>
                <RuleText>Используется колода из 36 карт (от 6 до туза). Каждому игроку раздается по 6 карт.</RuleText>
              </Rule>
              
              <Rule>
                <RuleTitle>Козырь:</RuleTitle>
                <RuleText>Нижняя карта колоды определяет козырную масть. Козыри бьют любые некозырные карты.</RuleText>
              </Rule>
              
              <Rule>
                <RuleTitle>Ход игры:</RuleTitle>
                <RuleText>
                  Атакующий кладет карту, защитник должен отбить ее картой той же масти, но старше, 
                  или любым козырем. Если не может отбить - берет все карты со стола.
                </RuleText>
              </Rule>
              
              <Rule>
                <RuleTitle>Подкидывание:</RuleTitle>
                <RuleText>
                  После успешной защиты атакующий может подкинуть карты того же достоинства, 
                  что уже лежат на столе.
                </RuleText>
              </Rule>
              
              <Rule>
                <RuleTitle>Пополнение:</RuleTitle>
                <RuleText>
                  После каждого хода игроки добирают карты из колоды до 6 карт в руке 
                  (сначала атакующий, потом защитник).
                </RuleText>
              </Rule>
            </RulesList>
          </Section>

          <Section>
            <SectionTitle>💡 Стратегические советы</SectionTitle>
            <TipsList>
              <Tip>🎯 Старайтесь избавляться от младших карт в первую очередь</Tip>
              <Tip>🛡️ Берегите козыри для защиты от сильных карт</Tip>
              <Tip>⚡ Атакуйте картами, которых у вас много</Tip>
              <Tip>🧠 Запоминайте, какие карты уже вышли из игры</Tip>
              <Tip>🎲 Иногда лучше взять карты, чем тратить ценные козыри</Tip>
            </TipsList>
          </Section>

          <Section>
            <SectionTitle>🎮 Управление в игре</SectionTitle>
            <ControlsList>
              <Control>
                <ControlKey>Клик по карте</ControlKey>
                <ControlDesc>Выбрать карту для хода</ControlDesc>
              </Control>
              <Control>
                <ControlKey>Кнопка "Атаковать"</ControlKey>
                <ControlDesc>Атаковать выбранной картой</ControlDesc>
              </Control>
              <Control>
                <ControlKey>Кнопка "Защищаться"</ControlKey>
                <ControlDesc>Отбить атаку выбранной картой</ControlDesc>
              </Control>
              <Control>
                <ControlKey>Кнопка "Взять"</ControlKey>
                <ControlDesc>Взять все карты со стола</ControlDesc>
              </Control>
              <Control>
                <ControlKey>Кнопка "Пас"</ControlKey>
                <ControlDesc>Закончить ход (бито)</ControlDesc>
              </Control>
            </ControlsList>
          </Section>
        </Content>
      </Main>
    </Container>
  );
};

const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
`;

const Header = styled.header`
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
`;

const BackButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 2rem;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const Title = styled.h1`
  font-size: 2.5rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
`;

const Main = styled.main`
  padding: 2rem;
`;

const Content = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const Section = styled.section`
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 2rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  color: #FFD700;
`;

const RulesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const Rule = styled.div`
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #4CAF50;
`;

const RuleTitle = styled.h3`
  margin: 0 0 0.5rem 0;
  color: #4CAF50;
  font-size: 1.1rem;
`;

const RuleText = styled.p`
  margin: 0;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
`;

const TipsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const Tip = styled.div`
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #2196F3;
  color: rgba(255, 255, 255, 0.9);
`;

const ControlsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const Control = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #FF9800;
  
  @media (max-width: 600px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
`;

const ControlKey = styled.span`
  background: rgba(255, 152, 0, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
  color: #FFB74D;
`;

const ControlDesc = styled.span`
  color: rgba(255, 255, 255, 0.9);
`;

export default TutorialsPage;
