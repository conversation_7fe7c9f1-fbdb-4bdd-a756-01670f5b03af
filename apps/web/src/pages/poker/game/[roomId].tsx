import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import styled from 'styled-components';
import { useSocket } from '../../../hooks/useSocket';
import { useAuth } from '../../../hooks/useAuth';
import { PokerTable } from '../../../components/poker/PokerTable';
import { PokerGameState, PokerAction, PokerRoom } from '../../../types/Poker';
import { Button } from '../../../components/common/Button';
import { LoadingSpinner } from '../../../components/common/LoadingSpinner';

const GameContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  padding: 20px;
`;

const GameHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  backdrop-filter: blur(10px);
`;

const RoomInfo = styled.div`
  color: white;
  
  h2 {
    margin: 0 0 10px 0;
    font-size: 24px;
  }
  
  p {
    margin: 0;
    opacity: 0.8;
  }
`;

const GameActions = styled.div`
  display: flex;
  gap: 10px;
`;

const ChatPanel = styled.div`
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 300px;
  height: 400px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 10px;
  padding: 15px;
  color: white;
  display: flex;
  flex-direction: column;
`;

const ChatMessages = styled.div`
  flex: 1;
  overflow-y: auto;
  margin-bottom: 10px;
  padding-right: 10px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
`;

const ChatMessage = styled.div`
  margin-bottom: 8px;
  font-size: 14px;
  
  .sender {
    font-weight: bold;
    color: #4a90e2;
  }
  
  .timestamp {
    font-size: 12px;
    opacity: 0.6;
    margin-left: 10px;
  }
`;

const ChatInput = styled.input`
  width: 100%;
  padding: 8px;
  border: none;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
  
  &:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.2);
  }
`;

const WaitingMessage = styled.div`
  text-align: center;
  color: white;
  font-size: 18px;
  margin-top: 50px;
`;

const ErrorMessage = styled.div`
  text-align: center;
  color: #ff6b6b;
  font-size: 18px;
  margin-top: 50px;
`;

interface ChatMessage {
  id: string;
  sender: string;
  message: string;
  timestamp: Date;
  type: 'chat' | 'system' | 'action';
}

export default function PokerGamePage() {
  const router = useRouter();
  const { roomId } = router.query;
  const { user } = useAuth();
  const { socket, isConnected } = useSocket();
  
  const [room, setRoom] = useState<PokerRoom | null>(null);
  const [gameState, setGameState] = useState<PokerGameState | null>(null);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!socket || !roomId || !user) return;

    // Присоединяемся к покерной комнате
    socket.emit('poker:join_room', { roomId, playerId: user.id });

    // Обработчики событий
    socket.on('poker:room_joined', (data: { room: PokerRoom; gameState?: PokerGameState }) => {
      setRoom(data.room);
      setGameState(data.gameState || null);
      setLoading(false);
    });

    socket.on('poker:game_state_updated', (newGameState: PokerGameState) => {
      setGameState(newGameState);
    });

    socket.on('poker:hand_started', (newGameState: PokerGameState) => {
      setGameState(newGameState);
      addSystemMessage('New hand started!');
    });

    socket.on('poker:hand_finished', (data: { gameState: PokerGameState; winners: any[] }) => {
      setGameState(data.gameState);
      data.winners.forEach(winner => {
        addSystemMessage(`${winner.playerName} wins $${winner.amount} with ${winner.hand.description}`);
      });
    });

    socket.on('poker:player_action', (data: { playerId: string; playerName: string; action: PokerAction }) => {
      addActionMessage(data.playerName, data.action);
    });

    socket.on('poker:chat_message', (message: ChatMessage) => {
      setChatMessages(prev => [...prev, message]);
    });

    socket.on('poker:error', (data: { message: string }) => {
      setError(data.message);
    });

    socket.on('poker:room_not_found', () => {
      setError('Room not found');
      setLoading(false);
    });

    return () => {
      socket.off('poker:room_joined');
      socket.off('poker:game_state_updated');
      socket.off('poker:hand_started');
      socket.off('poker:hand_finished');
      socket.off('poker:player_action');
      socket.off('poker:chat_message');
      socket.off('poker:error');
      socket.off('poker:room_not_found');
    };
  }, [socket, roomId, user]);

  const handleAction = (action: PokerAction) => {
    if (!socket || !roomId || !user) return;
    
    socket.emit('poker:player_action', {
      roomId,
      playerId: user.id,
      action
    });
  };

  const handleStartHand = () => {
    if (!socket || !roomId) return;
    
    socket.emit('poker:start_hand', { roomId });
  };

  const handleLeaveRoom = () => {
    if (!socket || !roomId || !user) return;
    
    socket.emit('poker:leave_room', { roomId, playerId: user.id });
    router.push('/poker');
  };

  const handleChatSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!socket || !roomId || !user || !chatInput.trim()) return;
    
    socket.emit('poker:chat_message', {
      roomId,
      playerId: user.id,
      message: chatInput.trim()
    });
    
    setChatInput('');
  };

  const addSystemMessage = (message: string) => {
    const systemMessage: ChatMessage = {
      id: Date.now().toString(),
      sender: 'System',
      message,
      timestamp: new Date(),
      type: 'system'
    };
    setChatMessages(prev => [...prev, systemMessage]);
  };

  const addActionMessage = (playerName: string, action: PokerAction) => {
    let actionText = '';
    switch (action.type) {
      case 'fold':
        actionText = 'folds';
        break;
      case 'check':
        actionText = 'checks';
        break;
      case 'call':
        actionText = 'calls';
        break;
      case 'bet':
        actionText = `bets $${action.amount}`;
        break;
      case 'raise':
        actionText = `raises to $${action.amount}`;
        break;
      case 'all_in':
        actionText = 'goes all-in';
        break;
    }
    
    const actionMessage: ChatMessage = {
      id: Date.now().toString(),
      sender: playerName,
      message: actionText,
      timestamp: new Date(),
      type: 'action'
    };
    setChatMessages(prev => [...prev, actionMessage]);
  };

  if (!isConnected) {
    return (
      <GameContainer>
        <WaitingMessage>Connecting to server...</WaitingMessage>
      </GameContainer>
    );
  }

  if (loading) {
    return (
      <GameContainer>
        <LoadingSpinner />
      </GameContainer>
    );
  }

  if (error) {
    return (
      <GameContainer>
        <ErrorMessage>{error}</ErrorMessage>
        <div style={{ textAlign: 'center', marginTop: '20px' }}>
          <Button onClick={() => router.push('/poker')}>
            Back to Lobby
          </Button>
        </div>
      </GameContainer>
    );
  }

  if (!room || !user) {
    return (
      <GameContainer>
        <ErrorMessage>Room not found</ErrorMessage>
      </GameContainer>
    );
  }

  const isHost = room.host.id === user.id;
  const canStartHand = isHost && gameState && gameState.players.length >= 2 && gameState.phase === 'finished';

  return (
    <GameContainer>
      <GameHeader>
        <RoomInfo>
          <h2>{room.name}</h2>
          <p>
            Players: {room.currentPlayers}/{room.maxPlayers} | 
            Blinds: ${room.smallBlind}/${room.bigBlind}
          </p>
        </RoomInfo>
        
        <GameActions>
          {canStartHand && (
            <Button variant="success" onClick={handleStartHand}>
              Start New Hand
            </Button>
          )}
          <Button variant="secondary" onClick={handleLeaveRoom}>
            Leave Room
          </Button>
        </GameActions>
      </GameHeader>

      {gameState ? (
        <PokerTable
          gameState={gameState}
          currentPlayerId={user.id}
          onAction={handleAction}
        />
      ) : (
        <WaitingMessage>
          Waiting for game to start...
          {isHost && room.currentPlayers >= 2 && (
            <div style={{ marginTop: '20px' }}>
              <Button variant="success" onClick={handleStartHand}>
                Start Game
              </Button>
            </div>
          )}
        </WaitingMessage>
      )}

      <ChatPanel>
        <h3 style={{ margin: '0 0 15px 0', fontSize: '16px' }}>Chat</h3>
        <ChatMessages>
          {chatMessages.map(message => (
            <ChatMessage key={message.id}>
              <span className="sender">{message.sender}:</span> {message.message}
              <span className="timestamp">
                {message.timestamp.toLocaleTimeString()}
              </span>
            </ChatMessage>
          ))}
        </ChatMessages>
        <form onSubmit={handleChatSubmit}>
          <ChatInput
            type="text"
            value={chatInput}
            onChange={(e) => setChatInput(e.target.value)}
            placeholder="Type a message..."
            maxLength={200}
          />
        </form>
      </ChatPanel>
    </GameContainer>
  );
}
