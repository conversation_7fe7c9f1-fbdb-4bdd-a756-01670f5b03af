import Head from "next/head";
import styled from "styled-components";
import { useRouter } from "next/router";

const HomePage = () => {
  const router = useRouter();

  const handleStartPlaying = () => {
    router.push("/games/durak");
  };

  const handleMultiplayer = () => {
    router.push("/multiplayer");
  };

  const handleViewTournaments = () => {
    router.push("/tournaments");
  };

  const handleViewTutorials = () => {
    router.push("/tutorials");
  };

  return (
    <Container>
      <Head>
        <title>Козырь Мастер - Карточные игры онлайн</title>
        <meta
          name="description"
          content="Играйте в популярные карточные игры онлайн с друзьями"
        />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Main>
        <Hero>
          <Title>Добро пожаловать в Козырь Мастер</Title>
          <Subtitle>
            Платформа для игры в популярные карточные игры онлайн
          </Subtitle>
        </Hero>

        <CardContainer>
          <GameCard>
            <CardIcon>🎮</CardIcon>
            <CardTitle>Одиночная игра</CardTitle>
            <CardDescription>
              Играйте против бота и оттачивайте свои навыки
            </CardDescription>
            <StartButton onClick={handleStartPlaying}>
              Играть против бота
            </StartButton>
          </GameCard>

          <GameCard>
            <CardIcon>🌐</CardIcon>
            <CardTitle>Многопользовательская игра</CardTitle>
            <CardDescription>
              Играйте с другими игроками онлайн в реальном времени
            </CardDescription>
            <MultiplayerButton onClick={handleMultiplayer}>
              Играть онлайн
            </MultiplayerButton>
          </GameCard>

          <GameCard>
            <CardIcon>🏆</CardIcon>
            <CardTitle>Турниры</CardTitle>
            <CardDescription>
              Участвуйте в турнирах и выигрывайте призы
            </CardDescription>
            <ActionButton onClick={handleViewTournaments}>
              Смотреть турниры
            </ActionButton>
          </GameCard>

          <GameCard>
            <CardIcon>📚</CardIcon>
            <CardTitle>Обучение</CardTitle>
            <CardDescription>
              Изучите правила игр и стратегии
            </CardDescription>
            <ActionButton onClick={handleViewTutorials}>
              Изучить правила
            </ActionButton>
          </GameCard>
        </CardContainer>
      </Main>

      <Footer>
        <p>© 2023 Козырь Мастер. Все права защищены.</p>
      </Footer>
    </Container>
  );
};

// Стилизованные компоненты
const Container = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
`;

const Main = styled.main`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
`;

const Hero = styled.div`
  text-align: center;
  margin-bottom: 4rem;
`;

const Title = styled.h1`
  font-size: 3.5rem;
  margin-bottom: 1rem;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 700;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  margin: 0 auto;

  @media (max-width: 768px) {
    font-size: 1.2rem;
  }
`;

const CardContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  width: 100%;
`;

const GameCard = styled.div`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
  }
`;

const CardIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const CardTitle = styled.h2`
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: #333;
  font-weight: 600;
`;

const CardDescription = styled.p`
  font-size: 1rem;
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.6;
`;

const StartButton = styled.button`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(76, 175, 80, 0.3);

  &:hover {
    background: linear-gradient(135deg, #45a049, #4CAF50);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
`;

const ActionButton = styled.button`
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(33, 150, 243, 0.3);

  &:hover {
    background: linear-gradient(135deg, #1976D2, #2196F3);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
`;

const Footer = styled.footer`
  width: 100%;
  padding: 1.5rem;
  text-align: center;
  background: rgba(0, 0, 0, 0.1);
  color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
`;

export default HomePage;
