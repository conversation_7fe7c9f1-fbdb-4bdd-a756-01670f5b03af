import Head from "next/head";
import styled from "styled-components";
import { useRouter } from "next/router";

const HomePage = () => {
  const router = useRouter();

  const handleStartPlaying = () => {
    router.push("/games");
  };

  return (
    <Container>
      <Head>
        <title>Козырь Мастер - Карточные игры онлайн</title>
        <meta
          name="description"
          content="Играйте в популярные карточные игры онлайн с друзьями"
        />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Main>
        <Title>Добро пожаловать в Козырь Мастер</Title>
        <Description>
          Платформа для игры в популярные карточные игры онлайн
        </Description>

        <CardContainer>
          <Card>
            <CardTitle>Начать игру</CardTitle>
            <CardDescription>
              Выберите тип игры и начните играть прямо сейчас
            </CardDescription>
            <StartButton onClick={handleStartPlaying}>Играть</StartButton>
            <CardTitle>Играть сейчас</CardTitle>
            <CardDescription>
              Присоединяйтесь к игре или создайте свою
            </CardDescription>
          </Card>

          <Card>
            <CardTitle>Турниры</CardTitle>
            <CardDescription>
              Участвуйте в турнирах и выигрывайте призы
            </CardDescription>
          </Card>

          <Card>
            <CardTitle>Обучение</CardTitle>
            <CardDescription>Изучите правила игр и стратегии</CardDescription>
          </Card>
        </CardContainer>
      </Main>

      <Footer>
        <p>© 2023 Козырь Мастер. Все права защищены.</p>
      </Footer>
    </Container>
  );
};

export default HomePage;

// Стилизованные компоненты
const Container = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
`;

const Main = styled.main`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
`;

const Title = styled.h1`
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
  text-align: center;
`;

const Description = styled.p`
  font-size: 1.5rem;
  margin-bottom: 3rem;
  color: var(--text-color);
  text-align: center;
`;

const CardContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  max-width: 1200px;
  width: 100%;
`;

const Card = styled.div`
  background-color: var(--card-background);
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  width: 300px;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
`;

const CardTitle = styled.h2`
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
`;

const CardDescription = styled.p`
  font-size: 1rem;
  color: var(--text-color);
`;

const StartButton = styled.button`
  background-color: var(--primary-color, #4caf50);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  margin-top: 1rem;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: var(--primary-dark, #388e3c);
  }
`;

const Footer = styled.footer`
  width: 100%;
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
  text-align: center;
  background-color: var(--card-background);
`;
