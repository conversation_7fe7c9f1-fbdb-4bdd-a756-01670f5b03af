import React from "react";
import Head from "next/head";
import { useRouter } from "next/router";
import styled from "styled-components";

const GamesPage = () => {
  const router = useRouter();

  const handleGameSelect = (gameType: string) => {
    // В будущем здесь можно передавать параметры игры
    router.push(`/game?type=${gameType}`);
  };

  return (
    <Container>
      <Head>
        <title>Выбор игры - Козырь Мастер</title>
        <meta
          name="description"
          content="Выберите карточную игру для начала игрового процесса"
        />
      </Head>

      <Main>
        <Title>Выберите игру</Title>

        <GamesGrid>
          <GameCard onClick={() => handleGameSelect("durak_classic")}>
            <GameIcon>♠️</GameIcon>
            <GameTitle>Дурак классический</GameTitle>
            <GameDescription>
              Классический вариант популярной карточной игры
            </GameDescription>
          </GameCard>

          <GameCard onClick={() => handleGameSelect("durak_perevodnoy")}>
            <GameIcon>♥️</GameIcon>
            <GameTitle>Дурак переводной</GameTitle>
            <GameDescription>
              Вариант игры с возможностью перевода карт
            </GameDescription>
          </GameCard>

          <GameCard onClick={() => handleGameSelect("durak_podkidnoy")}>
            <GameIcon>♦️</GameIcon>
            <GameTitle>Дурак подкидной</GameTitle>
            <GameDescription>
              Вариант игры с возможностью подкидывать карты
            </GameDescription>
          </GameCard>

          <GameCard disabled>
            <GameIcon>♣️</GameIcon>
            <GameTitle>Дурак командный</GameTitle>
            <GameDescription>Командный вариант игры (скоро)</GameDescription>
            <ComingSoon>Скоро</ComingSoon>
          </GameCard>
        </GamesGrid>
      </Main>
    </Container>
  );
};

const Container = styled.div`
  min-height: 100vh;
  padding: 0 0.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #1a1a2e;
  color: white;
`;

const Main = styled.main`
  padding: 3rem 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 1200px;
`;

const Title = styled.h1`
  margin: 0 0 2rem 0;
  line-height: 1.15;
  font-size: 2.5rem;
  text-align: center;
`;

const GamesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
  width: 100%;
  margin-top: 2rem;
`;

const GameCard = styled.div<{ disabled?: boolean }>`
  padding: 1.5rem;
  background-color: #16213e;
  color: white;
  border-radius: 10px;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
  position: relative;
  cursor: ${(props) => (props.disabled ? "not-allowed" : "pointer")};
  opacity: ${(props) => (props.disabled ? 0.7 : 1)};

  &:hover {
    transform: ${(props) => (props.disabled ? "none" : "translateY(-5px)")};
    box-shadow: ${(props) =>
      props.disabled ? "none" : "0 10px 20px rgba(0, 0, 0, 0.2)"};
  }
`;

const GameIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const GameTitle = styled.h2`
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
`;

const GameDescription = styled.p`
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
  color: #b3b3b3;
`;

const ComingSoon = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #e94560;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: bold;
`;

export default GamesPage;
