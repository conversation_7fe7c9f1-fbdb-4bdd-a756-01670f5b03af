import React, { useEffect } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import styled from "styled-components";
import { useDispatch, useSelector } from "react-redux";
// Удалены импорты для старого UI
// import {
//   GameTable,
//   PlayerHand,
//   GameActions,
//   OpponentInfo,
// } from "../entities/game/ui";
import { DurakGameComponent } from "../entities/game/ui/DurakGame"; // Импортируем компонент Дурака
import { RootState, AppDispatch } from "../app/store"; // Используем AppDispatch
// import { PlayerAction, GameStatus } from "../entities/game/model/slice"; // Удалено
import { PlayerAction, GameStatus } from '@a1-k/core'; // Импортируем из core
// import { startGame, makeMove } from "../entities/game/model/gameService"; // Удалено, используем durakSlice
import { initializeGame, startGame, makeMove } from '../entities/game/model/durakSlice'; // Импортируем из durakSlice
// import { CardModel } from "../entities/card/model/types"; // Удалено, т.к. старый интерфейс удален

const GamePage = () => {
  const router = useRouter();
  // const dispatch = useDispatch<AppDispatch>(); // Не используется напрямую здесь
  const { type } = router.query;

  // Получаем статус игры из Redux store (durak slice)
  const gameStatus = useSelector((state: RootState) => state.durak.gameStatus);

  // Удален useEffect для startGame, т.к. DurakGameComponent управляет инициализацией/стартом

  // Удалены handleCardSelect и handleAction, т.к. старый интерфейс удален

  // Удалено получение currentPlayer, т.к. старый интерфейс удален

  // Удалена проверка isLoading, т.к. старый интерфейс удален
  // Можно добавить проверку gameStatus, если нужно показать что-то до старта

  // Условный рендеринг: показываем DurakGameComponent или сообщение
  if (type === 'durak') {
    return (
      <>
        <Head>
          <title>Игра - Дурак</title>
          <meta name="description" content="Игра Дурак" />
        </Head>
        <GameContainer>
          <DurakGameComponent />
        </GameContainer>
      </>
    );
  } else {
    // Если тип игры не 'durak', показываем сообщение
    return (
      <GameContainer>
        <Head>
          <title>Ошибка - Неверный тип игры</title>
        </Head>
        <LoadingContainer> {/* Используем существующий стиль для центрирования */}
          <h2>Ошибка</h2>
          <p>Выбран неверный тип игры. Пожалуйста, вернитесь и выберите "Дурак".</p>
          {/* Можно добавить кнопку для возврата */}
        </LoadingContainer>
      </GameContainer>
    );
  }
};

const GameContainer = styled.div`
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #1a1a2e;
  color: white;
`;

const GameHeader = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #16213e;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

  h1 {
    margin: 0;
    font-size: 1.5rem;
  }
`;

const GameStatus = styled.div`
  padding: 0.5rem 1rem;
  background-color: #0f3460;
  border-radius: 4px;
  font-weight: bold;
`;

const GameContent = styled.main`
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  gap: 1rem;
`;

const OpponentsContainer = styled.div`
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 1rem;
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: white;
  text-align: center;

  h2 {
    margin-bottom: 1rem;
    font-size: 2rem;
  }

  p {
    font-size: 1.2rem;
    opacity: 0.8;
  }
`;

const PlayerHandContainer = styled.div`
  margin-top: auto;
  padding: 1rem;
`;

export default GamePage;
