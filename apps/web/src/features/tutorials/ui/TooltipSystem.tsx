import React, { useState, useEffect, useRef } from 'react';
import styled, { keyframes } from 'styled-components';

// Типы для системы подсказок
type TooltipData = {
  id: string;
  content: string;
  targetSelector: string; // CSS селектор для элемента, к которому привязана подсказка
  position?: 'top' | 'right' | 'bottom' | 'left';
  condition?: () => boolean; // Условие для показа подсказки
  showOnce?: boolean; // Показывать подсказку только один раз
  delay?: number; // Задержка перед показом подсказки в мс
  gameType?: string; // Тип игры, для которой предназначена подсказка
};

type TooltipSystemProps = {
  tooltips: TooltipData[];
  currentGameType?: string;
  isEnabled: boolean;
  viewedTooltips?: string[]; // ID просмотренных подсказок
  onTooltipShown?: (tooltipId: string) => void;
};

// Анимации
const fadeIn = keyframes`
  from { opacity: 0; }
  to { opacity: 1; }
`;

const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
`;

// Стилизованные компоненты
const TooltipContainer = styled.div<{ position: string }>`
  position: absolute;
  background-color: var(--card-background, #fff);
  color: var(--text-color, #333);
  padding: 12px 16px;
  border-radius: 6px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  max-width: 250px;
  font-size: 14px;
  line-height: 1.4;
  animation: ${fadeIn} 0.3s ease-in-out, ${pulse} 2s ease-in-out infinite;
  
  &:after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border: 8px solid transparent;
    
    ${props => props.position === 'top' && `
      border-top-color: var(--card-background, #fff);
      bottom: -16px;
      left: 50%;
      transform: translateX(-50%);
    `}
    
    ${props => props.position === 'right' && `
      border-right-color: var(--card-background, #fff);
      left: -16px;
      top: 50%;
      transform: translateY(-50%);
    `}
    
    ${props => props.position === 'bottom' && `
      border-bottom-color: var(--card-background, #fff);
      top: -16px;
      left: 50%;
      transform: translateX(-50%);
    `}
    
    ${props => props.position === 'left' && `
      border-left-color: var(--card-background, #fff);
      right: -16px;
      top: 50%;
      transform: translateY(-50%);
    `}
  }
`;

const CloseButton = styled.button`
  position: absolute;
  top: 5px;
  right: 5px;
  background: transparent;
  border: none;
  color: var(--text-color, #333);
  font-size: 16px;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s;
  
  &:hover {
    opacity: 1;
  }
`;

// Компонент системы подсказок
export const TooltipSystem: React.FC<TooltipSystemProps> = ({
  tooltips,
  currentGameType,
  isEnabled,
  viewedTooltips = [],
  onTooltipShown
}) => {
  const [activeTooltip, setActiveTooltip] = useState<TooltipData | null>(null);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const tooltipRef = useRef<HTMLDivElement>(null);
  
  // Функция для проверки и показа подходящей подсказки
  const checkAndShowTooltip = () => {
    if (!isEnabled) return;
    
    // Фильтруем подсказки по типу игры и просмотренным
    const availableTooltips = tooltips.filter(tooltip => {
      // Проверяем тип игры, если указан
      if (tooltip.gameType && tooltip.gameType !== currentGameType) {
        return false;
      }
      
      // Проверяем, была ли подсказка уже просмотрена
      if (tooltip.showOnce && viewedTooltips.includes(tooltip.id)) {
        return false;
      }
      
      // Проверяем наличие целевого элемента
      const targetElement = document.querySelector(tooltip.targetSelector);
      if (!targetElement) {
        return false;
      }
      
      // Проверяем условие, если оно есть
      if (tooltip.condition && !tooltip.condition()) {
        return false;
      }
      
      return true;
    });
    
    // Если есть доступные подсказки, показываем первую
    if (availableTooltips.length > 0) {
      const tooltipToShow = availableTooltips[0];
      
      // Если есть задержка, ждем указанное время
      if (tooltipToShow.delay) {
        setTimeout(() => {
          setActiveTooltip(tooltipToShow);
          positionTooltip(tooltipToShow);
          
          if (onTooltipShown) {
            onTooltipShown(tooltipToShow.id);
          }
        }, tooltipToShow.delay);
      } else {
        setActiveTooltip(tooltipToShow);
        positionTooltip(tooltipToShow);
        
        if (onTooltipShown) {
          onTooltipShown(tooltipToShow.id);
        }
      }
    }
  };
  
  // Функция для позиционирования подсказки относительно целевого элемента
  const positionTooltip = (tooltip: TooltipData) => {
    const targetElement = document.querySelector(tooltip.targetSelector);
    if (!targetElement) return;
    
    const position = tooltip.position || 'bottom';
    const rect = targetElement.getBoundingClientRect();
    
    let top = 0;
    let left = 0;
    
    switch (position) {
      case 'top':
        top = rect.top - 10;
        left = rect.left + rect.width / 2;
        break;
      case 'right':
        top = rect.top + rect.height / 2;
        left = rect.right + 10;
        break;
      case 'bottom':
        top = rect.bottom + 10;
        left = rect.left + rect.width / 2;
        break;
      case 'left':
        top = rect.top + rect.height / 2;
        left = rect.left - 10;
        break;
    }
    
    setPosition({ top, left });
  };
  
  // Проверяем подсказки при изменении зависимостей
  useEffect(() => {
    checkAndShowTooltip();
    
    // Добавляем обработчик изменения размера окна
    const handleResize = () => {
      if (activeTooltip) {
        positionTooltip(activeTooltip);
      }
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [tooltips, currentGameType, isEnabled, viewedTooltips]);
  
  // Обработчик закрытия подсказки
  const handleClose = () => {
    setActiveTooltip(null);
  };
  
  // Если нет активной подсказки, ничего не рендерим
  if (!activeTooltip) {
    return null;
  }
  
  return (
    <TooltipContainer
      ref={tooltipRef}
      position={activeTooltip.position || 'bottom'}
      style={{
        top: `${position.top}px`,
        left: `${position.left}px`,
        transform: 'translate(-50%, -50%)'
      }}
    >
      {activeTooltip.content}
      <CloseButton onClick={handleClose}>×</CloseButton>
    </TooltipContainer>
  );
};

// Экспорт типов для использования в других компонентах
export type { TooltipData };