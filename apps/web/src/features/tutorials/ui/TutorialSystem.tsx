import React, { useState, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';
import { useRouter } from 'next/router';

// Типы для системы туториалов
type TutorialStep = {
  id: string;
  title: string;
  content: string;
  targetElement?: string; // CSS селектор для элемента, к которому привязан шаг
  position?: 'top' | 'right' | 'bottom' | 'left';
  action?: () => void; // Действие, которое нужно выполнить на этом шаге
  image?: string; // Опциональное изображение для иллюстрации
};

type Tutorial = {
  id: string;
  name: string;
  gameType: string;
  description: string;
  steps: TutorialStep[];
  isRequired?: boolean; // Обязательный ли туториал для новых пользователей
};

type TutorialSystemProps = {
  tutorials: Tutorial[];
  currentGameType?: string;
  onComplete?: (tutorialId: string) => void;
  onSkip?: (tutorialId: string) => void;
  isVisible?: boolean;
  completedTutorials?: string[]; // ID завершенных туториалов
};

// Анимации
const fadeIn = keyframes`
  from { opacity: 0; }
  to { opacity: 1; }
`;

const slideIn = keyframes`
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
`;

// Стилизованные компоненты
const TutorialOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: ${fadeIn} 0.3s ease-in-out;
`;

const TutorialCard = styled.div`
  background-color: var(--card-background, #fff);
  color: var(--text-color, #333);
  border-radius: 8px;
  padding: 20px;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: ${slideIn} 0.3s ease-in-out;
  position: relative;
`;

const TutorialTitle = styled.h3`
  margin-top: 0;
  color: var(--primary-color, #2c3e50);
  font-size: 1.5rem;
`;

const TutorialContent = styled.div`
  margin: 15px 0;
  line-height: 1.5;
`;

const TutorialImage = styled.img`
  max-width: 100%;
  border-radius: 4px;
  margin: 10px 0;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
`;

const Button = styled.button`
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
  
  &:hover {
    opacity: 0.9;
  }
`;

const PrimaryButton = styled(Button)`
  background-color: var(--accent-color, #3498db);
  color: white;
`;

const SecondaryButton = styled(Button)`
  background-color: transparent;
  color: var(--accent-color, #3498db);
`;

const ProgressIndicator = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 15px;
`;

const ProgressDot = styled.div<{ active: boolean }>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin: 0 4px;
  background-color: ${props => props.active ? 'var(--accent-color, #3498db)' : 'var(--border-color, #ddd)'};
  transition: background-color 0.2s;
`;

const Tooltip = styled.div<{ position: string }>`
  position: absolute;
  background-color: var(--card-background, #fff);
  color: var(--text-color, #333);
  padding: 12px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  max-width: 250px;
  animation: ${fadeIn} 0.3s ease-in-out;
  
  &:after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border: 8px solid transparent;
    
    ${props => props.position === 'top' && `
      border-top-color: var(--card-background, #fff);
      bottom: -16px;
      left: 50%;
      transform: translateX(-50%);
    `}
    
    ${props => props.position === 'right' && `
      border-right-color: var(--card-background, #fff);
      left: -16px;
      top: 50%;
      transform: translateY(-50%);
    `}
    
    ${props => props.position === 'bottom' && `
      border-bottom-color: var(--card-background, #fff);
      top: -16px;
      left: 50%;
      transform: translateX(-50%);
    `}
    
    ${props => props.position === 'left' && `
      border-left-color: var(--card-background, #fff);
      right: -16px;
      top: 50%;
      transform: translateY(-50%);
    `}
  }
`;

// Компонент системы туториалов
export const TutorialSystem: React.FC<TutorialSystemProps> = ({
  tutorials,
  currentGameType,
  onComplete,
  onSkip,
  isVisible = true,
  completedTutorials = []
}) => {
  const router = useRouter();
  const [activeTutorial, setActiveTutorial] = useState<Tutorial | null>(null);
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
  
  // Находим подходящий туториал для текущей игры
  useEffect(() => {
    if (!isVisible) return;
    
    // Если есть текущий тип игры, находим соответствующий туториал
    if (currentGameType) {
      const matchingTutorial = tutorials.find(t => 
        t.gameType === currentGameType && !completedTutorials.includes(t.id)
      );
      
      if (matchingTutorial) {
        setActiveTutorial(matchingTutorial);
        setCurrentStep(0);
      }
    }
  }, [currentGameType, tutorials, completedTutorials, isVisible]);
  
  // Позиционирование подсказки относительно целевого элемента
  useEffect(() => {
    if (!activeTutorial || !activeTutorial.steps[currentStep]?.targetElement) return;
    
    const targetSelector = activeTutorial.steps[currentStep].targetElement;
    if (!targetSelector) return;
    
    const targetElement = document.querySelector(targetSelector);
    if (!targetElement) return;
    
    const position = activeTutorial.steps[currentStep].position || 'bottom';
    const rect = targetElement.getBoundingClientRect();
    
    let top = 0;
    let left = 0;
    
    switch (position) {
      case 'top':
        top = rect.top - 10;
        left = rect.left + rect.width / 2;
        break;
      case 'right':
        top = rect.top + rect.height / 2;
        left = rect.right + 10;
        break;
      case 'bottom':
        top = rect.bottom + 10;
        left = rect.left + rect.width / 2;
        break;
      case 'left':
        top = rect.top + rect.height / 2;
        left = rect.left - 10;
        break;
    }
    
    setTooltipPosition({ top, left });
  }, [activeTutorial, currentStep]);
  
  // Обработчики навигации по шагам туториала
  const handleNext = () => {
    if (!activeTutorial) return;
    
    // Выполняем действие, если оно есть
    if (activeTutorial.steps[currentStep]?.action) {
      activeTutorial.steps[currentStep].action?.();
    }
    
    // Если это последний шаг, завершаем туториал
    if (currentStep === activeTutorial.steps.length - 1) {
      handleComplete();
    } else {
      // Иначе переходим к следующему шагу
      setCurrentStep(currentStep + 1);
    }
  };
  
  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };
  
  const handleComplete = () => {
    if (!activeTutorial) return;
    
    if (onComplete) {
      onComplete(activeTutorial.id);
    }
    
    // Сбрасываем состояние
    setActiveTutorial(null);
    setCurrentStep(0);
  };
  
  const handleSkip = () => {
    if (!activeTutorial) return;
    
    if (onSkip) {
      onSkip(activeTutorial.id);
    }
    
    // Сбрасываем состояние
    setActiveTutorial(null);
    setCurrentStep(0);
  };
  
  // Если нет активного туториала или он не должен быть видимым, ничего не рендерим
  if (!activeTutorial || !isVisible) {
    return null;
  }
  
  const currentTutorialStep = activeTutorial.steps[currentStep];
  
  // Если у шага есть целевой элемент, показываем подсказку
  if (currentTutorialStep.targetElement) {
    return (
      <TutorialOverlay onClick={handleSkip}>
        <Tooltip 
          position={currentTutorialStep.position || 'bottom'}
          style={{
            top: `${tooltipPosition.top}px`,
            left: `${tooltipPosition.left}px`,
            transform: 'translate(-50%, -50%)'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <TutorialTitle>{currentTutorialStep.title}</TutorialTitle>
          <TutorialContent>{currentTutorialStep.content}</TutorialContent>
          
          <ButtonContainer>
            {currentStep > 0 && (
              <SecondaryButton onClick={handlePrevious}>Назад</SecondaryButton>
            )}
            <PrimaryButton onClick={handleNext}>
              {currentStep === activeTutorial.steps.length - 1 ? 'Завершить' : 'Далее'}
            </PrimaryButton>
          </ButtonContainer>
        </Tooltip>
      </TutorialOverlay>
    );
  }
  
  // Иначе показываем карточку туториала
  return (
    <TutorialOverlay>
      <TutorialCard>
        <TutorialTitle>{currentTutorialStep.title}</TutorialTitle>
        <TutorialContent>{currentTutorialStep.content}</TutorialContent>
        
        {currentTutorialStep.image && (
          <TutorialImage src={currentTutorialStep.image} alt="Иллюстрация" />
        )}
        
        <ButtonContainer>
          <SecondaryButton onClick={handleSkip}>
            {activeTutorial.isRequired ? 'Позже' : 'Пропустить'}
          </SecondaryButton>
          
          <div>
            {currentStep > 0 && (
              <SecondaryButton onClick={handlePrevious} style={{ marginRight: '8px' }}>
                Назад
              </SecondaryButton>
            )}
            <PrimaryButton onClick={handleNext}>
              {currentStep === activeTutorial.steps.length - 1 ? 'Завершить' : 'Далее'}
            </PrimaryButton>
          </div>
        </ButtonContainer>
        
        <ProgressIndicator>
          {activeTutorial.steps.map((_, index) => (
            <ProgressDot key={index} active={index === currentStep} />
          ))}
        </ProgressIndicator>
      </TutorialCard>
    </TutorialOverlay>
  );
};

// Экспорт типов для использования в других компонентах
export type { Tutorial, TutorialStep };