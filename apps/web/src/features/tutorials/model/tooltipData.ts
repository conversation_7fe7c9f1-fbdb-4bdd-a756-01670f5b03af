import { TooltipData } from '../ui/TooltipSystem';

// Данные для всплывающих подсказок
export const tooltipData: TooltipData[] = [
  // Общие подсказки для всех игр
  {
    id: 'game_settings',
    content: 'Нажмите здесь, чтобы изменить настройки игры, включая правила и визуальные параметры.',
    targetSelector: '.settings-button',
    position: 'bottom',
    showOnce: true,
    delay: 3000
  },
  {
    id: 'card_selection',
    content: 'Нажмите на карту, чтобы выбрать её для хода. Выбранная карта будет подсвечена.',
    targetSelector: '.player-hand',
    position: 'top',
    showOnce: true,
    delay: 5000,
    condition: () => document.querySelectorAll('.player-hand .card').length > 0
  },
  {
    id: 'trump_card',
    content: 'Это козырная карта. Карты этой масти бьют любые карты других мастей.',
    targetSelector: '.trump-card',
    position: 'right',
    showOnce: true,
    delay: 2000
  },
  
  // Подсказки для игры "Дурак классический"
  {
    id: 'durak_classic_attack',
    content: 'Ваш ход! Выберите карту для атаки.',
    targetSelector: '.player-hand',
    position: 'top',
    gameType: 'durak_classic',
    condition: () => {
      // Проверяем, что сейчас ход игрока и он атакует
      const gameState = window.gameState;
      return gameState && gameState.currentPlayer === 'player' && gameState.currentAction === 'attack';
    }
  },
  {
    id: 'durak_classic_defense',
    content: 'Защищайтесь! Выберите карту для защиты или нажмите "Взять".',
    targetSelector: '.table-area',
    position: 'bottom',
    gameType: 'durak_classic',
    condition: () => {
      // Проверяем, что сейчас ход игрока и он защищается
      const gameState = window.gameState;
      return gameState && gameState.currentPlayer === 'player' && gameState.currentAction === 'defense';
    }
  },
  {
    id: 'durak_classic_take',
    content: 'Если у вас нет подходящих карт для защиты, нажмите "Взять", чтобы забрать все карты со стола.',
    targetSelector: '.take-button',
    position: 'right',
    gameType: 'durak_classic',
    condition: () => {
      // Проверяем, что игрок защищается и на столе есть карты
      const gameState = window.gameState;
      return gameState && 
             gameState.currentPlayer === 'player' && 
             gameState.currentAction === 'defense' && 
             document.querySelectorAll('.table-area .card').length > 0;
    }
  },
  
  // Подсказки для игры "Дурак переводной"
  {
    id: 'durak_perevodnoy_transfer',
    content: 'У вас есть карта того же достоинства! Вы можете перевести атаку на следующего игрока.',
    targetSelector: '.transfer-button',
    position: 'right',
    gameType: 'durak_perevodnoy',
    condition: () => {
      // Проверяем, что игрок может перевести атаку
      const gameState = window.gameState;
      return gameState && 
             gameState.currentPlayer === 'player' && 
             gameState.currentAction === 'defense' && 
             gameState.canTransfer === true;
    }
  },
  
  // Подсказки для игры "Дурак подкидной"
  {
    id: 'durak_podkidnoy_add',
    content: 'Вы можете подкинуть карту того же достоинства, что уже есть на столе.',
    targetSelector: '.add-card-button',
    position: 'right',
    gameType: 'durak_podkidnoy',
    condition: () => {
      // Проверяем, что игрок может подкинуть карту
      const gameState = window.gameState;
      return gameState && 
             gameState.currentPlayer === 'player' && 
             gameState.currentAction === 'attack' && 
             document.querySelectorAll('.table-area .card').length > 0 && 
             document.querySelectorAll('.table-area .card').length < 6;
    }
  }
];

// Функция для получения подсказок по типу игры
export const getTooltipsByGameType = (gameType?: string): TooltipData[] => {
  if (!gameType) {
    // Возвращаем только общие подсказки, если тип игры не указан
    return tooltipData.filter(tooltip => !tooltip.gameType);
  }
  
  // Возвращаем общие подсказки и подсказки для указанного типа игры
  return tooltipData.filter(tooltip => !tooltip.gameType || tooltip.gameType === gameType);
};

// Функция для получения подсказки по ID
export const getTooltipById = (tooltipId: string): TooltipData | undefined => {
  return tooltipData.find(tooltip => tooltip.id === tooltipId);
};

// Объявляем глобальный тип для доступа к состоянию игры из условий подсказок
declare global {
  interface Window {
    gameState?: {
      currentPlayer: string;
      currentAction: string;
      canTransfer?: boolean;
    };
  }
}