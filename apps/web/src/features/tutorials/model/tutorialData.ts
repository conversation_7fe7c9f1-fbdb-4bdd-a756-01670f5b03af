import { Tutorial } from '../ui/TutorialSystem';

// Туториалы для различных карточных игр
export const tutorialData: Tutorial[] = [
  // Туториал для игры "Дурак классический"
  {
    id: 'durak_classic_tutorial',
    name: 'Обучение игре "Дурак классический"',
    gameType: 'durak_classic',
    description: 'Изучите основные правила и стратегии игры в классического дурака',
    isRequired: true,
    steps: [
      {
        id: 'durak_classic_intro',
        title: 'Добро пожаловать в игру "Дурак"!',
        content: 'Дурак - одна из самых популярных карточных игр. Цель игры - избавиться от всех карт. Игрок, который остался с картами последним, считается "дураком".',
        image: '/images/tutorials/durak_intro.svg'
      },
      {
        id: 'durak_classic_cards',
        title: 'Карты и козыри',
        content: 'Игра ведется колодой из 36 карт. В начале игры определяется козырная масть, которая бьет любую карту другой масти.',
        targetElement: '.deck-container',
        position: 'bottom'
      },
      {
        id: 'durak_classic_hand',
        title: 'Ваши карты',
        content: 'Это ваша рука с картами. Выбирайте карты для атаки или защиты, нажимая на них.',
        targetElement: '.player-hand',
        position: 'top'
      },
      {
        id: 'durak_classic_attack',
        title: 'Атака',
        content: 'Когда ваш ход, вы можете атаковать соперника, выбрав карту из своей руки. Соперник должен будет отбиться картой той же масти, но большего достоинства, или козырем.',
        targetElement: '.game-actions',
        position: 'left'
      },
      {
        id: 'durak_classic_defense',
        title: 'Защита',
        content: 'Когда атакуют вас, вы должны отбиваться. Выберите карту той же масти, но большего достоинства, или козырь, чтобы отбить атаку.',
        targetElement: '.table-area',
        position: 'bottom'
      },
      {
        id: 'durak_classic_take',
        title: 'Взять карты',
        content: 'Если вы не можете или не хотите отбиваться, нажмите кнопку "Взять". Все карты со стола перейдут к вам, и ход перейдет к сопернику.',
        targetElement: '.take-button',
        position: 'right'
      },
      {
        id: 'durak_classic_done',
        title: 'Завершение хода',
        content: 'После успешной защиты нажмите "Бито", чтобы сбросить отбитые карты и передать ход.',
        targetElement: '.done-button',
        position: 'right'
      },
      {
        id: 'durak_classic_end',
        title: 'Завершение игры',
        content: 'Игра заканчивается, когда у одного из игроков не остается карт. Последний игрок с картами считается проигравшим.',
        image: '/images/tutorials/durak_end.svg'
      },
      {
        id: 'durak_classic_tips',
        title: 'Советы по стратегии',
        content: 'Старайтесь сохранять козыри для важных моментов. Начинайте атаку с карт, которых у вас несколько одного достоинства. Следите за картами, которые уже вышли из игры.',
      }
    ]
  },
  
  // Туториал для игры "Дурак переводной"
  {
    id: 'durak_perevodnoy_tutorial',
    name: 'Обучение игре "Дурак переводной"',
    gameType: 'durak_perevodnoy',
    description: 'Изучите правила и особенности игры в переводного дурака',
    isRequired: false,
    steps: [
      {
        id: 'durak_perevodnoy_intro',
        title: 'Дурак переводной',
        content: 'Переводной дурак - вариант игры, в котором защищающийся игрок может "перевести" атаку на следующего игрока при определенных условиях.',
        image: '/images/tutorials/durak_perevodnoy.svg'
      },
      {
        id: 'durak_perevodnoy_transfer',
        title: 'Перевод атаки',
        content: 'Если у вас есть карта того же достоинства, что и карта атаки, вы можете перевести атаку на следующего игрока, выложив эту карту.',
        targetElement: '.transfer-button',
        position: 'right'
      },
      {
        id: 'durak_perevodnoy_strategy',
        title: 'Стратегия перевода',
        content: 'Перевод атаки - мощный инструмент. Используйте его, чтобы избежать необходимости отбиваться от сложных комбинаций или когда у вас мало козырей.',
      }
    ]
  },
  
  // Туториал для игры "Дурак подкидной"
  {
    id: 'durak_podkidnoy_tutorial',
    name: 'Обучение игре "Дурак подкидной"',
    gameType: 'durak_podkidnoy',
    description: 'Изучите правила и особенности игры в подкидного дурака',
    isRequired: false,
    steps: [
      {
        id: 'durak_podkidnoy_intro',
        title: 'Дурак подкидной',
        content: 'В подкидном дураке атакующий игрок и другие игроки могут подкидывать карты того же достоинства, что уже есть на столе.',
        image: '/images/tutorials/durak_podkidnoy.svg'
      },
      {
        id: 'durak_podkidnoy_adding',
        title: 'Подкидывание карт',
        content: 'Если на столе уже есть карты, вы можете подкинуть карту того же достоинства, что и любая из карт на столе.',
        targetElement: '.add-card-button',
        position: 'right'
      },
      {
        id: 'durak_podkidnoy_limit',
        title: 'Ограничение подкидывания',
        content: 'Обратите внимание, что подкидывать можно не более 6 карт или не больше, чем карт в руке у защищающегося игрока.',
      }
    ]
  }
];

// Функция для получения туториала по типу игры
export const getTutorialByGameType = (gameType: string): Tutorial | undefined => {
  return tutorialData.find(tutorial => tutorial.gameType === gameType);
};

// Функция для получения туториала по ID
export const getTutorialById = (tutorialId: string): Tutorial | undefined => {
  return tutorialData.find(tutorial => tutorial.id === tutorialId);
};