/**
 * Компонент для отображения списка комнат в многопользовательском режиме
 */
import React, { useEffect } from 'react';
import { useGameConnection } from '../model/hooks';
import { GameRoom } from '../types';

interface RoomsListProps {
  serverUrl: string;
  onJoinRoom?: (roomId: string) => void;
  onCreateRoom?: () => void;
}

/**
 * Компонент списка доступных игровых комнат
 */
export const RoomsList: React.FC<RoomsListProps> = ({
  serverUrl,
  onJoinRoom,
  onCreateRoom,
}) => {
  const {
    status,
    error,
    rooms,
    getRooms,
    connect,
    playerName,
  } = useGameConnection({
    serverUrl,
    autoConnect: true,
  });

  // Получение списка комнат при монтировании компонента
  useEffect(() => {
    if (status === 'connected') {
      getRooms();
    }
  }, [status, getRooms]);

  // Обновление списка комнат каждые 10 секунд
  useEffect(() => {
    if (status === 'connected') {
      const interval = setInterval(() => {
        getRooms();
      }, 10000);

      return () => clearInterval(interval);
    }
  }, [status, getRooms]);

  // Обработчик присоединения к комнате
  const handleJoinRoom = (roomId: string) => {
    if (onJoinRoom) {
      onJoinRoom(roomId);
    }
  };

  // Обработчик создания новой комнаты
  const handleCreateRoom = () => {
    if (onCreateRoom) {
      onCreateRoom();
    }
  };

  // Отображение статуса подключения
  if (status === 'disconnected' || status === 'connecting') {
    return (
      <div className="rooms-list-container">
        <div className="connection-status">
          <p>{status === 'disconnected' ? 'Отключено от сервера' : 'Подключение к серверу...'}</p>
        </div>
      </div>
    );
  }

  // Отображение ошибки подключения
  if (status === 'error') {
    return (
      <div className="rooms-list-container">
        <div className="connection-error">
          <p>Ошибка подключения: {error}</p>
          <button onClick={() => connect(playerName || 'Гость')}>Повторить подключение</button>
        </div>
      </div>
    );
  }

  return (
    <div className="rooms-list-container">
      <div className="rooms-list-header">
        <h2>Доступные комнаты</h2>
        <button className="refresh-button" onClick={getRooms}>
          Обновить
        </button>
        <button className="create-room-button" onClick={handleCreateRoom}>
          Создать комнату
        </button>
      </div>

      {rooms.length === 0 ? (
        <div className="no-rooms">
          <p>Нет доступных комнат. Создайте новую комнату или обновите список.</p>
        </div>
      ) : (
        <div className="rooms-list">
          {rooms.map((room: GameRoom) => (
            <div key={room.id} className="room-item">
              <div className="room-info">
                <h3>{room.name}</h3>
                <p>
                  Игроки: {room.players.length}/{room.maxPlayers}
                </p>
                <p>Статус: {room.status === 'waiting' ? 'Ожидание' : room.status === 'playing' ? 'Игра' : 'Завершена'}</p>
              </div>
              <div className="room-actions">
                <button
                  className="join-button"
                  onClick={() => handleJoinRoom(room.id)}
                  disabled={room.status !== 'waiting' || room.players.length >= room.maxPlayers}
                >
                  {room.status !== 'waiting'
                    ? 'Игра идет'
                    : room.players.length >= room.maxPlayers
                    ? 'Комната заполнена'
                    : 'Присоединиться'}
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};