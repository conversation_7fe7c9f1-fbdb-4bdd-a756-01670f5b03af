/**
 * Компонент для отображения игровой комнаты в многопользовательском режиме
 */
import React, { useEffect } from 'react';
import { useGameConnection } from '../model/hooks';
import { Player } from '../types';

interface GameRoomProps {
  serverUrl: string;
  roomId: string;
  onLeaveRoom?: () => void;
}

/**
 * Компонент игровой комнаты
 */
export const GameRoom: React.FC<GameRoomProps> = ({
  serverUrl,
  roomId,
  onLeaveRoom,
}) => {
  const {
    status,
    error,
    currentRoom,
    gameState,
    joinRoom,
    leaveRoom,
    setReady,
    startGame,
    isHost,
    canStartGame,
    playerName,
    playerId,
  } = useGameConnection({
    serverUrl,
    autoConnect: true,
  });

  // Присоединение к комнате при монтировании компонента
  useEffect(() => {
    if (status === 'connected' && roomId) {
      joinRoom(roomId);
    }
  }, [status, roomId, joinRoom]);

  // Обработчик выхода из комнаты
  const handleLeaveRoom = () => {
    leaveRoom();
    if (onLeaveRoom) {
      onLeaveRoom();
    }
  };

  // Обработчик установки готовности
  const handleSetReady = () => {
    if (currentRoom) {
      const currentPlayer = currentRoom.players.find(p => p.id === playerId);
      if (currentPlayer) {
        setReady(!currentPlayer.isReady);
      }
    }
  };

  // Обработчик начала игры
  const handleStartGame = () => {
    startGame();
  };

  // Отображение статуса подключения
  if (status === 'disconnected' || status === 'connecting') {
    return (
      <div className="game-room-container">
        <div className="connection-status">
          <p>{status === 'disconnected' ? 'Отключено от сервера' : 'Подключение к серверу...'}</p>
        </div>
      </div>
    );
  }

  // Отображение ошибки подключения
  if (status === 'error') {
    return (
      <div className="game-room-container">
        <div className="connection-error">
          <p>Ошибка подключения: {error}</p>
        </div>
      </div>
    );
  }

  // Если комната не найдена
  if (!currentRoom) {
    return (
      <div className="game-room-container">
        <div className="room-not-found">
          <p>Комната не найдена или вы не присоединились к комнате</p>
          <button onClick={handleLeaveRoom}>Вернуться к списку комнат</button>
        </div>
      </div>
    );
  }

  return (
    <div className="game-room-container">
      <div className="game-room-header">
        <h2>{currentRoom.name}</h2>
        <div className="room-status">
          <p>Статус: {currentRoom.status === 'waiting' ? 'Ожидание игроков' : currentRoom.status === 'playing' ? 'Игра идет' : 'Игра завершена'}</p>
        </div>
        <button className="leave-room-button" onClick={handleLeaveRoom}>
          Покинуть комнату
        </button>
      </div>

      <div className="players-list">
        <h3>Игроки</h3>
        <div className="players-container">
          {currentRoom.players.map((player: Player) => (
            <div
              key={player.id}
              className={`player-item ${player.id === playerId ? 'current-player' : ''} ${player.isHost ? 'host-player' : ''}`}
            >
              <div className="player-info">
                <p className="player-name">
                  {player.name} {player.isHost ? '(Хост)' : ''} {player.id === playerId ? '(Вы)' : ''}
                </p>
                <p className="player-status">
                  {player.isReady ? 'Готов' : 'Не готов'}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="game-room-actions">
        {currentRoom.status === 'waiting' && (
          <>
            <button
              className={`ready-button ${currentRoom.players.find(p => p.id === playerId)?.isReady ? 'not-ready' : 'ready'}`}
              onClick={handleSetReady}
            >
              {currentRoom.players.find(p => p.id === playerId)?.isReady ? 'Не готов' : 'Готов'}
            </button>

            {isHost() && (
              <button
                className="start-game-button"
                onClick={handleStartGame}
                disabled={!canStartGame()}
              >
                Начать игру
              </button>
            )}
          </>
        )}
      </div>

      {gameState && currentRoom.status === 'playing' && (
        <div className="game-state-info">
          <h3>Игра началась</h3>
          <p>Ход: {gameState.turnNumber}</p>
          <p>Активный игрок: {currentRoom.players.find(p => p.id === gameState.activePlayerId)?.name || 'Неизвестно'}</p>
        </div>
      )}
    </div>
  );
};