/**
 * Компонент для создания новой комнаты в многопользовательском режиме
 */
import React, { useState } from 'react';
import { useGameConnection } from '../model/hooks';
import { GameSettingsForm } from '@/features/game-settings';
import { GameProfile, getGameProfile } from '@/entities/game/config/profiles';

interface CreateRoomFormProps {
  serverUrl: string;
  onCancel?: () => void;
  onRoomCreated?: (roomId: string) => void;
}

/**
 * Форма создания новой игровой комнаты
 */
export const CreateRoomForm: React.FC<CreateRoomFormProps> = ({
  serverUrl,
  onCancel,
  onRoomCreated,
}) => {
  const [roomName, setRoomName] = useState('');
  const [playerName, setPlayerName] = useState(localStorage.getItem('playerName') || '');
  const [gameSettings, setGameSettings] = useState<GameProfile>(getGameProfile('multiplayer'));
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);

  const { createRoom, status, currentRoom } = useGameConnection({
    serverUrl,
    autoConnect: true,
  });

  // Обработчик изменения имени комнаты
  const handleRoomNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRoomName(e.target.value);
  };

  // Обработчик изменения имени игрока
  const handlePlayerNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPlayerName(e.target.value);
    localStorage.setItem('playerName', e.target.value);
  };

  // Обработчик сохранения настроек игры
  const handleSettingsSave = (settings: GameProfile) => {
    setGameSettings(settings);
    setShowAdvancedSettings(false);
  };

  // Обработчик создания комнаты
  const handleCreateRoom = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!roomName.trim()) {
      alert('Пожалуйста, введите название комнаты');
      return;
    }
    
    if (!playerName.trim()) {
      alert('Пожалуйста, введите ваше имя');
      return;
    }
    
    createRoom(roomName);
  };

  // Если комната создана, вызываем обработчик
  React.useEffect(() => {
    if (currentRoom && onRoomCreated) {
      onRoomCreated(currentRoom.id);
    }
  }, [currentRoom, onRoomCreated]);

  return (
    <div className="create-room-form-container">
      <h2>Создание новой комнаты</h2>
      
      <form onSubmit={handleCreateRoom} className="create-room-form">
        <div className="form-group">
          <label htmlFor="roomName">Название комнаты:</label>
          <input
            type="text"
            id="roomName"
            value={roomName}
            onChange={handleRoomNameChange}
            placeholder="Введите название комнаты"
            className="form-control"
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="playerName">Ваше имя:</label>
          <input
            type="text"
            id="playerName"
            value={playerName}
            onChange={handlePlayerNameChange}
            placeholder="Введите ваше имя"
            className="form-control"
            required
          />
        </div>
        
        <div className="form-group">
          <button
            type="button"
            className="btn btn-link"
            onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
          >
            {showAdvancedSettings ? 'Скрыть настройки' : 'Показать настройки игры'}
          </button>
        </div>
        
        {showAdvancedSettings && (
          <div className="advanced-settings">
            <GameSettingsForm
              initialProfile="multiplayer"
              onSave={handleSettingsSave}
              onCancel={() => setShowAdvancedSettings(false)}
            />
          </div>
        )}
        
        <div className="form-actions">
          <button
            type="submit"
            className="btn btn-primary"
            disabled={status !== 'connected' || !roomName.trim() || !playerName.trim()}
          >
            Создать комнату
          </button>
          
          {onCancel && (
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onCancel}
            >
              Отмена
            </button>
          )}
        </div>
      </form>
    </div>
  );
};