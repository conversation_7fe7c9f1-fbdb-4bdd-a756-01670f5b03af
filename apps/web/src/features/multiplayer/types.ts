/**
 * Типы для многопользовательского режима
 */

import { Card } from '@/entities/card';

/**
 * Статус соединения с сервером
 */
export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'error';

/**
 * Статус игровой комнаты
 */
export type RoomStatus = 'waiting' | 'playing' | 'finished';

/**
 * Тип игрового события
 */
export type GameEventType = 
  | 'playerJoined' 
  | 'playerLeft' 
  | 'playerReady' 
  | 'gameStarted' 
  | 'gameEnded' 
  | 'moveMade' 
  | 'turnChanged' 
  | 'cardPlayed' 
  | 'cardTaken';

/**
 * Игрок в многопользовательском режиме
 */
export interface Player {
  id: string;
  name: string;
  isReady: boolean;
  isHost: boolean;
  isActive: boolean;
  cards: Card[];
  score: number;
}

/**
 * Игровая комната
 */
export interface GameRoom {
  id: string;
  name: string;
  players: Player[];
  status: RoomStatus;
  maxPlayers: number;
  gameSettings: GameSettings;
  createdAt: string;
}

/**
 * Настройки игры
 */
export interface GameSettings {
  gameMode: 'classic' | 'fast' | 'custom';
  timePerMove: number;
  initialCardsCount: number;
  winScore: number;
  allowSpectators: boolean;
  isPrivate: boolean;
}

/**
 * Состояние игры
 */
export interface GameState {
  roomId: string;
  status: RoomStatus;
  players: Player[];
  activePlayerId: string | null;
  trumpCard: Card | null;
  deck: {
    remainingCards: number;
  };
  table: {
    attackingCards: Card[];
    defendingCards: Card[];
  };
  lastAction: {
    type: string;
    playerId: string;
    timestamp: string;
  } | null;
  turnNumber: number;
  winner: Player | null;
}

/**
 * Игровое событие
 */
export interface GameEvent {
  type: GameEventType;
  payload: any;
  timestamp: string;
}

/**
 * Состояние многопользовательского режима
 */
export interface MultiplayerState {
  connection: {
    status: ConnectionStatus;
    error: string | null;
  };
  rooms: GameRoom[];
  currentRoom: GameRoom | null;
  gameState: GameState | null;
  messages: ChatMessage[];
  playerInfo: {
    id: string | null;
    name: string | null;
  };
}

/**
 * Сообщение в чате
 */
export interface ChatMessage {
  id: string;
  playerId: string;
  playerName: string;
  message: string;
  timestamp: string;
  isSystem: boolean;
}

/**
 * Данные для выполнения хода
 */
export interface MoveData {
  action: 'attack' | 'defend' | 'take' | 'pass';
  cardIndex?: number;
  targetCardIndex?: number;
}