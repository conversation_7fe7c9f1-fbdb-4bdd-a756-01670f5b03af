/**
 * Модуль для работы с WebSocket соединением в многопользовательском режиме
 */
import { io, Socket } from 'socket.io-client';
import { GameEvent, GameRoom, GameState, Player } from '../types';

interface SocketOptions {
  serverUrl: string;
  autoConnect?: boolean;
  reconnection?: boolean;
  reconnectionAttempts?: number;
  reconnectionDelay?: number;
}

interface SocketEvents {
  onConnect?: () => void;
  onDisconnect?: (reason: string) => void;
  onError?: (error: Error) => void;
  onGameStateUpdate?: (gameState: GameState) => void;
  onGameEvent?: (event: GameEvent) => void;
  onRoomsUpdate?: (rooms: GameRoom[]) => void;
}

/**
 * Класс для управления WebSocket соединением
 */
export class SocketManager {
  private socket: Socket | null = null;
  private events: SocketEvents = {};
  private options: SocketOptions;

  constructor(options: SocketOptions) {
    this.options = {
      autoConnect: false,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      ...options
    };

    if (this.options.autoConnect) {
      this.connect();
    }
  }

  /**
   * Установка соединения с сервером
   * @param authToken - токен авторизации (опционально)
   */
  public connect(authToken?: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.socket = io(this.options.serverUrl, {
          autoConnect: true,
          reconnection: this.options.reconnection,
          reconnectionAttempts: this.options.reconnectionAttempts,
          reconnectionDelay: this.options.reconnectionDelay,
          auth: authToken ? { token: authToken } : undefined
        });

        this.socket.on('connect', () => {
          if (this.events.onConnect) this.events.onConnect();
          resolve();
        });

        this.socket.on('disconnect', (reason) => {
          if (this.events.onDisconnect) this.events.onDisconnect(reason);
        });

        this.socket.on('connect_error', (error) => {
          if (this.events.onError) this.events.onError(error);
          reject(error);
        });

        this.socket.on('gameStateUpdate', (gameState: GameState) => {
          if (this.events.onGameStateUpdate) this.events.onGameStateUpdate(gameState);
        });

        this.socket.on('gameEvent', (event: GameEvent) => {
          if (this.events.onGameEvent) this.events.onGameEvent(event);
        });

        this.socket.on('roomsUpdate', (rooms: GameRoom[]) => {
          if (this.events.onRoomsUpdate) this.events.onRoomsUpdate(rooms);
        });
      } catch (error) {
        if (this.events.onError) this.events.onError(error as Error);
        reject(error);
      }
    });
  }

  /**
   * Отключение от сервера
   */
  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  /**
   * Установка обработчиков событий
   */
  public setEvents(events: SocketEvents): void {
    this.events = { ...this.events, ...events };
  }

  /**
   * Получение списка доступных комнат
   */
  public getRooms(): void {
    if (this.socket) {
      this.socket.emit('getRooms');
    }
  }

  /**
   * Создание новой комнаты
   */
  public createRoom(roomName: string, playerName: string): void {
    if (this.socket) {
      this.socket.emit('createRoom', { roomName, playerName });
    }
  }

  /**
   * Присоединение к комнате
   */
  public joinRoom(roomId: string, playerName: string): void {
    if (this.socket) {
      this.socket.emit('joinRoom', { roomId, playerName });
    }
  }

  /**
   * Выход из комнаты
   */
  public leaveRoom(): void {
    if (this.socket) {
      this.socket.emit('leaveRoom');
    }
  }

  /**
   * Установка готовности игрока
   */
  public setReady(isReady: boolean): void {
    if (this.socket) {
      this.socket.emit('setReady', { isReady });
    }
  }

  /**
   * Начало игры
   */
  public startGame(): void {
    if (this.socket) {
      this.socket.emit('startGame');
    }
  }

  /**
   * Выполнение хода
   * @param moveData - данные о ходе игрока
   * @param timeout - таймаут ожидания ответа в миллисекундах (по умолчанию 10000)
   * @returns Promise с результатом выполнения хода
   */
  public makeMove(moveData: any, timeout: number = 10000): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Нет соединения с сервером'));
        return;
      }
      
      // Отправляем данные о ходе на сервер
      this.socket.emit('makeMove', moveData);
      
      // Ожидаем ответ от сервера
      const onGameStateUpdate = (gameState: any) => {
        this.socket?.off('gameStateUpdate', onGameStateUpdate);
        this.socket?.off('gameError', onGameError);
        resolve(gameState);
      };
      
      const onGameError = (error: any) => {
        this.socket?.off('gameStateUpdate', onGameStateUpdate);
        this.socket?.off('gameError', onGameError);
        reject(new Error(error.message || 'Ошибка при выполнении хода'));
      };
      
      // Устанавливаем обработчики событий
      this.socket.once('gameStateUpdate', onGameStateUpdate);
      this.socket.once('gameError', onGameError);
      
      // Устанавливаем таймаут для ответа
      const timeoutId = setTimeout(() => {
        this.socket?.off('gameStateUpdate', onGameStateUpdate);
        this.socket?.off('gameError', onGameError);
        reject(new Error('Превышено время ожидания ответа от сервера'));
      }, timeout);
    });
  }

  /**
   * Отправка сообщения в чат
   */
  public sendMessage(message: string): void {
    if (this.socket) {
      this.socket.emit('chatMessage', { message });
    }
  }

  /**
   * Проверка состояния соединения
   */
  public isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Отправка произвольного события на сервер
   * @param event - название события
   * @param data - данные события
   * @returns Promise, который разрешается после отправки события
   */
  public emit(event: string, data: any): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Нет соединения с сервером'));
        return;
      }
      
      this.socket.emit(event, data, (error: any) => {
        if (error) {
          reject(new Error(error.message || 'Ошибка при отправке события'));
        } else {
          resolve();
        }
      });
    });
  }
}

/**
 * Создание экземпляра менеджера сокетов
 */
export const createSocketConnection = (options: SocketOptions): SocketManager => {
  return new SocketManager(options);
};