/**
 * Модуль многопользовательского режима
 */

// API
export { createSocketConnection } from './api/socket';

// Модели и хуки
export { MultiplayerProvider, useMultiplayer } from './model/context';
export { useGameConnection, useGameChat } from './model/hooks';

// Типы
export type {
  ConnectionStatus,
  RoomStatus,
  GameEventType,
  Player,
  GameRoom,
  GameSettings,
  GameState,
  GameEvent,
  MultiplayerState,
  ChatMessage,
  MoveData
} from './types';