/**
 * Контекст для управления состоянием многопользовательского режима
 */
import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { createSocketConnection, SocketManager } from '../api/socket';
import { 
  MultiplayerState, 
  ConnectionStatus, 
  GameRoom, 
  GameState, 
  GameEvent,
  ChatMessage,
  MoveData
} from '../types';

// Начальное состояние
const initialState: MultiplayerState = {
  connection: {
    status: 'disconnected',
    error: null,
  },
  rooms: [],
  currentRoom: null,
  gameState: null,
  messages: [],
  playerInfo: {
    id: null,
    name: null,
  },
};

// Типы действий
type MultiplayerAction =
  | { type: 'SET_CONNECTION_STATUS'; payload: { status: ConnectionStatus; error?: string | null } }
  | { type: 'SET_ROOMS'; payload: GameRoom[] }
  | { type: 'SET_CURRENT_ROOM'; payload: GameRoom | null }
  | { type: 'SET_GAME_STATE'; payload: GameState | null }
  | { type: 'ADD_MESSAGE'; payload: ChatMessage }
  | { type: 'SET_PLAYER_INFO'; payload: { id: string; name: string } }
  | { type: 'RESET_STATE' };

// Редьюсер для обработки действий
const multiplayerReducer = (state: MultiplayerState, action: MultiplayerAction): MultiplayerState => {
  switch (action.type) {
    case 'SET_CONNECTION_STATUS':
      return {
        ...state,
        connection: {
          status: action.payload.status,
          error: action.payload.error !== undefined ? action.payload.error : state.connection.error,
        },
      };
    case 'SET_ROOMS':
      return {
        ...state,
        rooms: action.payload,
      };
    case 'SET_CURRENT_ROOM':
      return {
        ...state,
        currentRoom: action.payload,
      };
    case 'SET_GAME_STATE':
      return {
        ...state,
        gameState: action.payload,
      };
    case 'ADD_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload],
      };
    case 'SET_PLAYER_INFO':
      return {
        ...state,
        playerInfo: {
          id: action.payload.id,
          name: action.payload.name,
        },
      };
    case 'RESET_STATE':
      return {
        ...initialState,
        playerInfo: state.playerInfo, // Сохраняем информацию о игроке
      };
    default:
      return state;
  }
};

// Интерфейс контекста
interface MultiplayerContextValue {
  state: MultiplayerState;
  connect: (serverUrl: string, playerName: string) => Promise<void>;
  disconnect: () => void;
  getRooms: () => void;
  createRoom: (roomName: string) => void;
  joinRoom: (roomId: string) => void;
  leaveRoom: () => void;
  setReady: (isReady: boolean) => void;
  startGame: () => void;
  makeMove: (moveData: MoveData) => void;
  sendMessage: (message: string) => void;
}

// Создание контекста
const MultiplayerContext = createContext<MultiplayerContextValue | null>(null);

// Свойства провайдера
interface MultiplayerProviderProps {
  children: ReactNode;
}

/**
 * Провайдер контекста многопользовательского режима
 */
export const MultiplayerProvider: React.FC<MultiplayerProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(multiplayerReducer, initialState);
  const socketRef = React.useRef<SocketManager | null>(null);

  // Подключение к серверу
  const connect = async (serverUrl: string, playerName: string): Promise<void> => {
    try {
      dispatch({ type: 'SET_CONNECTION_STATUS', payload: { status: 'connecting' } });
      
      socketRef.current = createSocketConnection({
        serverUrl,
        autoConnect: true,
        reconnection: true,
      });

      socketRef.current.setEvents({
        onConnect: () => {
          dispatch({ type: 'SET_CONNECTION_STATUS', payload: { status: 'connected' } });
          dispatch({ 
            type: 'SET_PLAYER_INFO', 
            payload: { 
              id: socketRef.current?.isConnected() ? socketRef.current.socket?.id || '' : '',
              name: playerName 
            } 
          });
        },
        onDisconnect: (reason) => {
          dispatch({ type: 'SET_CONNECTION_STATUS', payload: { status: 'disconnected' } });
        },
        onError: (error) => {
          dispatch({ 
            type: 'SET_CONNECTION_STATUS', 
            payload: { status: 'error', error: error.message } 
          });
        },
        onRoomsUpdate: (rooms) => {
          dispatch({ type: 'SET_ROOMS', payload: rooms });
        },
        onGameStateUpdate: (gameState) => {
          dispatch({ type: 'SET_GAME_STATE', payload: gameState });
        },
        onGameEvent: (event) => {
          // Обработка игровых событий
          switch (event.type) {
            case 'playerJoined':
            case 'playerLeft':
            case 'playerReady':
            case 'gameStarted':
            case 'gameEnded':
              // Добавление системного сообщения
              dispatch({
                type: 'ADD_MESSAGE',
                payload: {
                  id: `system-${Date.now()}`,
                  playerId: 'system',
                  playerName: 'Система',
                  message: event.payload.message,
                  timestamp: event.timestamp,
                  isSystem: true,
                },
              });
              break;
          }
        },
      });

      await socketRef.current.connect();
    } catch (error) {
      dispatch({ 
        type: 'SET_CONNECTION_STATUS', 
        payload: { status: 'error', error: (error as Error).message } 
      });
      throw error;
    }
  };

  // Отключение от сервера
  const disconnect = (): void => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      dispatch({ type: 'RESET_STATE' });
    }
  };

  // Получение списка комнат
  const getRooms = (): void => {
    if (socketRef.current) {
      socketRef.current.getRooms();
    }
  };

  // Создание комнаты
  const createRoom = (roomName: string): void => {
    if (socketRef.current && state.playerInfo.name) {
      socketRef.current.createRoom(roomName, state.playerInfo.name);
    }
  };

  // Присоединение к комнате
  const joinRoom = (roomId: string): void => {
    if (socketRef.current && state.playerInfo.name) {
      socketRef.current.joinRoom(roomId, state.playerInfo.name);
    }
  };

  // Выход из комнаты
  const leaveRoom = (): void => {
    if (socketRef.current) {
      socketRef.current.leaveRoom();
      dispatch({ type: 'SET_CURRENT_ROOM', payload: null });
      dispatch({ type: 'SET_GAME_STATE', payload: null });
    }
  };

  // Установка готовности
  const setReady = (isReady: boolean): void => {
    if (socketRef.current) {
      socketRef.current.setReady(isReady);
    }
  };

  // Начало игры
  const startGame = (): void => {
    if (socketRef.current) {
      socketRef.current.startGame();
    }
  };

  // Выполнение хода
  const makeMove = (moveData: MoveData): void => {
    if (socketRef.current) {
      socketRef.current.makeMove(moveData);
    }
  };

  // Отправка сообщения
  const sendMessage = (message: string): void => {
    if (socketRef.current) {
      socketRef.current.sendMessage(message);
      
      // Добавляем сообщение в локальный список
      if (state.playerInfo.id && state.playerInfo.name) {
        dispatch({
          type: 'ADD_MESSAGE',
          payload: {
            id: `${state.playerInfo.id}-${Date.now()}`,
            playerId: state.playerInfo.id,
            playerName: state.playerInfo.name,
            message,
            timestamp: new Date().toISOString(),
            isSystem: false,
          },
        });
      }
    }
  };

  // Очистка при размонтировании
  useEffect(() => {
    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, []);

  const contextValue: MultiplayerContextValue = {
    state,
    connect,
    disconnect,
    getRooms,
    createRoom,
    joinRoom,
    leaveRoom,
    setReady,
    startGame,
    makeMove,
    sendMessage,
  };

  return (
    <MultiplayerContext.Provider value={contextValue}>
      {children}
    </MultiplayerContext.Provider>
  );
};

/**
 * Хук для использования контекста многопользовательского режима
 */
export const useMultiplayer = (): MultiplayerContextValue => {
  const context = useContext(MultiplayerContext);
  if (!context) {
    throw new Error('useMultiplayer должен использоваться внутри MultiplayerProvider');
  }
  return context;
};