/**
 * Хуки для работы с многопользовательским режимом
 */
import { useState, useEffect, useCallback } from 'react';
import { useMultiplayer } from './context';
import { ConnectionStatus, GameRoom, GameState, ChatMessage, MoveData } from '../types';

interface UseGameConnectionOptions {
  serverUrl: string;
  autoConnect?: boolean;
  playerName?: string;
}

interface UseGameConnectionResult {
  // Состояние соединения
  status: ConnectionStatus;
  error: string | null;
  
  // Данные
  rooms: GameRoom[];
  currentRoom: GameRoom | null;
  gameState: GameState | null;
  messages: ChatMessage[];
  playerName: string | null;
  playerId: string | null;
  
  // Методы
  connect: (playerName: string) => Promise<void>;
  disconnect: () => void;
  getRooms: () => void;
  createRoom: (roomName: string) => void;
  joinRoom: (roomId: string) => void;
  leaveRoom: () => void;
  setReady: (isReady: boolean) => void;
  startGame: () => void;
  makeMove: (moveData: MoveData) => void;
  sendMessage: (message: string) => void;
  
  // Вспомогательные методы
  isHost: () => boolean;
  isMyTurn: () => boolean;
  canStartGame: () => boolean;
}

/**
 * Хук для работы с многопользовательским режимом
 * 
 * @param options Параметры подключения
 * @returns Методы и состояние для работы с многопользовательским режимом
 */
export const useGameConnection = (options: UseGameConnectionOptions): UseGameConnectionResult => {
  const { 
    serverUrl, 
    autoConnect = false, 
    playerName: initialPlayerName = ''
  } = options;
  
  const [playerName, setPlayerName] = useState<string | null>(initialPlayerName || null);
  const multiplayer = useMultiplayer();
  const { state, connect: connectToServer, ...methods } = multiplayer;
  
  // Автоматическое подключение при монтировании
  useEffect(() => {
    if (autoConnect && playerName) {
      connectToServer(serverUrl, playerName).catch(console.error);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  
  // Метод подключения с сохранением имени игрока
  const connect = useCallback(async (name: string) => {
    setPlayerName(name);
    await connectToServer(serverUrl, name);
  }, [connectToServer, serverUrl]);
  
  // Проверка, является ли текущий игрок хостом комнаты
  const isHost = useCallback((): boolean => {
    if (!state.currentRoom || !state.playerInfo.id) return false;
    
    const currentPlayer = state.currentRoom.players.find(p => p.id === state.playerInfo.id);
    return currentPlayer?.isHost || false;
  }, [state.currentRoom, state.playerInfo.id]);
  
  // Проверка, является ли текущий ход игрока
  const isMyTurn = useCallback((): boolean => {
    if (!state.gameState || !state.playerInfo.id) return false;
    
    return state.gameState.activePlayerId === state.playerInfo.id;
  }, [state.gameState, state.playerInfo.id]);
  
  // Проверка, можно ли начать игру
  const canStartGame = useCallback((): boolean => {
    if (!state.currentRoom || !isHost()) return false;
    
    // Игру можно начать, если есть хотя бы 2 игрока и все готовы
    const readyPlayers = state.currentRoom.players.filter(p => p.isReady);
    return state.currentRoom.players.length >= 2 && 
           readyPlayers.length === state.currentRoom.players.length;
  }, [state.currentRoom, isHost]);
  
  return {
    // Состояние
    status: state.connection.status,
    error: state.connection.error,
    rooms: state.rooms,
    currentRoom: state.currentRoom,
    gameState: state.gameState,
    messages: state.messages,
    playerName: state.playerInfo.name,
    playerId: state.playerInfo.id,
    
    // Методы
    connect,
    disconnect: methods.disconnect,
    getRooms: methods.getRooms,
    createRoom: methods.createRoom,
    joinRoom: methods.joinRoom,
    leaveRoom: methods.leaveRoom,
    setReady: methods.setReady,
    startGame: methods.startGame,
    makeMove: methods.makeMove,
    sendMessage: methods.sendMessage,
    
    // Вспомогательные методы
    isHost,
    isMyTurn,
    canStartGame,
  };
};

/**
 * Хук для работы с чатом в многопользовательском режиме
 * 
 * @returns Методы и состояние для работы с чатом
 */
export const useGameChat = () => {
  const { messages, sendMessage, playerName } = useGameConnection({
    serverUrl: '', // Не используется, так как мы не подключаемся здесь
  });
  
  return {
    messages,
    sendMessage,
    playerName,
  };
};