import React, { useState, useEffect, useCallback } from 'react';
import { AchievementNotification } from './AchievementNotification';
import { Achievement, AchievementEvent, UserAchievements } from '../model/types';
import { checkAchievementUnlock, trackAchievementProgress } from '../utils/achievementUtils';

interface AchievementNotificationManagerProps {
  userAchievements: UserAchievements;
  onAchievementUnlocked: (achievementId: string) => void;
  onProgressUpdated: (achievementId: string, progress: number) => void;
}

/**
 * Компонент для управления уведомлениями о достижениях
 * Отслеживает игровые события и показывает уведомления при получении новых достижений
 */
export const AchievementNotificationManager: React.FC<AchievementNotificationManagerProps> = ({
  userAchievements,
  onAchievementUnlocked,
  onProgressUpdated
}) => {
  // Состояние для хранения очереди уведомлений
  const [notificationQueue, setNotificationQueue] = useState<string[]>([]);
  // Текущее отображаемое уведомление
  const [currentNotification, setCurrentNotification] = useState<string | null>(null);

  // Обработчик игровых событий
  const handleGameEvent = useCallback((event: AchievementEvent) => {
    // Проверяем, разблокировано ли новое достижение
    const unlockedAchievements = checkAchievementUnlock(userAchievements, event);
    
    // Если есть новые разблокированные достижения, добавляем их в очередь
    if (unlockedAchievements.length > 0) {
      setNotificationQueue(prev => [...prev, ...unlockedAchievements]);
      
      // Вызываем колбэк для каждого разблокированного достижения
      unlockedAchievements.forEach(achievementId => {
        onAchievementUnlocked(achievementId);
      });
    }
    
    // Обновляем прогресс достижений
    const updatedProgress = trackAchievementProgress(userAchievements, event);
    
    // Вызываем колбэк для каждого обновленного прогресса
    Object.entries(updatedProgress).forEach(([achievementId, progress]) => {
      onProgressUpdated(achievementId, progress);
    });
  }, [userAchievements, onAchievementUnlocked, onProgressUpdated]);

  // Эффект для обработки очереди уведомлений
  useEffect(() => {
    // Если нет текущего уведомления и очередь не пуста
    if (!currentNotification && notificationQueue.length > 0) {
      // Берем первое уведомление из очереди
      const nextNotification = notificationQueue[0];
      // Устанавливаем его как текущее
      setCurrentNotification(nextNotification);
      // Удаляем его из очереди
      setNotificationQueue(prev => prev.slice(1));
    }
  }, [currentNotification, notificationQueue]);

  // Функция для закрытия текущего уведомления
  const handleCloseNotification = useCallback(() => {
    setCurrentNotification(null);
  }, []);

  // Регистрируем глобальный обработчик событий
  useEffect(() => {
    // Функция для обработки пользовательских событий
    const eventHandler = (e: CustomEvent<AchievementEvent>) => {
      handleGameEvent(e.detail);
    };

    // Регистрируем обработчик
    window.addEventListener('achievement-event', eventHandler as EventListener);

    // Очищаем обработчик при размонтировании
    return () => {
      window.removeEventListener('achievement-event', eventHandler as EventListener);
    };
  }, [handleGameEvent]);

  // Вспомогательная функция для отправки события (для тестирования)
  const dispatchAchievementEvent = (eventData: AchievementEvent) => {
    const event = new CustomEvent('achievement-event', { detail: eventData });
    window.dispatchEvent(event);
  };

  // Экспортируем функцию для использования в других компонентах
  (window as any).dispatchAchievementEvent = dispatchAchievementEvent;

  return (
    <>
      {currentNotification && (
        <AchievementNotification
          achievementId={currentNotification}
          onClose={handleCloseNotification}
          autoHideDuration={5000}
        />
      )}
    </>
  );
};