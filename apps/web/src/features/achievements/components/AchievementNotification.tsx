import React, { useEffect, useState } from 'react';
import styled, { keyframes } from 'styled-components';
import { Achievement } from '../model/types';
import { getAchievementById } from '../model/achievementData';

interface AchievementNotificationProps {
  achievementId: string;
  onClose: () => void;
  autoHideDuration?: number; // Время в мс, через которое уведомление автоматически скроется
}

// Анимации
const slideIn = keyframes`
  from { transform: translateY(100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
`;

const glow = keyframes`
  0% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
  50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
  100% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
`;

// Стилизованные компоненты
const NotificationContainer = styled.div`
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  animation: ${slideIn} 0.5s ease-out forwards;
  max-width: 350px;
  width: 100%;
`;

const NotificationCard = styled.div`
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px;
  display: flex;
  align-items: center;
  animation: ${glow} 2s infinite;
  border: 2px solid gold;
`;

const IconContainer = styled.div<{ bgColor: string }>`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: ${props => props.bgColor};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-right: 16px;
  flex-shrink: 0;
`;

const ContentContainer = styled.div`
  flex: 1;
`;

const Title = styled.h3`
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
`;

const Description = styled.p`
  margin: 0;
  font-size: 14px;
  color: #666;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #999;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  margin-left: 8px;
  &:hover {
    color: #666;
  }
`;

const AchievementLabel = styled.div`
  background-color: #f8f0c6;
  color: #b59a3d;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 8px;
`;

/**
 * Компонент для отображения уведомления о полученном достижении
 */
export const AchievementNotification: React.FC<AchievementNotificationProps> = ({
  achievementId,
  onClose,
  autoHideDuration = 5000 // По умолчанию 5 секунд
}) => {
  const [achievement, setAchievement] = useState<Achievement | null>(null);

  useEffect(() => {
    // Получаем данные о достижении
    const achievementData = getAchievementById(achievementId);
    if (achievementData) {
      setAchievement(achievementData);
    }

    // Устанавливаем таймер для автоматического скрытия
    const timer = setTimeout(() => {
      onClose();
    }, autoHideDuration);

    // Очищаем таймер при размонтировании
    return () => clearTimeout(timer);
  }, [achievementId, onClose, autoHideDuration]);

  if (!achievement) return null;

  return (
    <NotificationContainer>
      <NotificationCard>
        <IconContainer bgColor={achievement.iconBgColor}>
          {achievement.icon}
        </IconContainer>
        <ContentContainer>
          <AchievementLabel>Новое достижение!</AchievementLabel>
          <Title>{achievement.name}</Title>
          <Description>{achievement.description}</Description>
          {achievement.reward && (
            <Description style={{ marginTop: '4px', color: '#b59a3d' }}>
              Награда: {achievement.reward.name}
            </Description>
          )}
        </ContentContainer>
        <CloseButton onClick={onClose}>
          ✕
        </CloseButton>
      </NotificationCard>
    </NotificationContainer>
  );
};