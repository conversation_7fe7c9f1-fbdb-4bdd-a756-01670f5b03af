import React, { useState, useMemo } from 'react';
import { Achievement, AchievementCategory, AchievementDifficulty, UserAchievements } from '../model/types';
import { AchievementCard } from './AchievementCard';
import { getAchievementsByCategory, getAchievementsByDifficulty } from '../model/achievementData';
import { getSortedAchievements } from '../utils/achievementUtils';

interface AchievementsListProps {
  userAchievements: UserAchievements;
  onAchievementClick?: (achievement: Achievement) => void;
}

/**
 * Компонент для отображения списка достижений с фильтрацией
 */
export const AchievementsList: React.FC<AchievementsListProps> = ({
  userAchievements,
  onAchievementClick
}) => {
  // Состояние для фильтров
  const [selectedCategory, setSelectedCategory] = useState<AchievementCategory | 'all'>('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState<AchievementDifficulty | 'all'>('all');
  const [showUnlockedOnly, setShowUnlockedOnly] = useState(false);
  const [showWithRewardsOnly, setShowWithRewardsOnly] = useState(false);
  
  // Получаем отфильтрованный и отсортированный список достижений
  const filteredAchievements = useMemo(() => {
    // Начинаем со всех достижений или с выбранной категории
    let achievements = selectedCategory === 'all' 
      ? [...getAchievementsByCategory(AchievementCategory.BEGINNER),
         ...getAchievementsByCategory(AchievementCategory.GAMEPLAY),
         ...getAchievementsByCategory(AchievementCategory.MASTERY),
         ...getAchievementsByCategory(AchievementCategory.SOCIAL),
         ...getAchievementsByCategory(AchievementCategory.COLLECTION),
         ...getAchievementsByCategory(AchievementCategory.SPECIAL)]
      : getAchievementsByCategory(selectedCategory);
    
    // Фильтруем по сложности
    if (selectedDifficulty !== 'all') {
      achievements = achievements.filter(a => a.difficulty === selectedDifficulty);
    }
    
    // Фильтруем по разблокированным
    if (showUnlockedOnly) {
      achievements = achievements.filter(a => 
        userAchievements.unlockedAchievements.includes(a.id));
    }
    
    // Фильтруем по наличию наград
    if (showWithRewardsOnly) {
      achievements = achievements.filter(a => a.reward !== undefined);
    }
    
    // Скрываем скрытые достижения, если они не разблокированы
    achievements = achievements.filter(a => 
      !a.hidden || userAchievements.unlockedAchievements.includes(a.id));
    
    // Сортируем результаты
    return getSortedAchievements(achievements);
  }, [selectedCategory, selectedDifficulty, showUnlockedOnly, showWithRewardsOnly, userAchievements]);
  
  return (
    <div className="container mx-auto p-4">
      {/* Фильтры */}
      <div className="bg-white p-4 rounded-lg shadow-md mb-6">
        <h2 className="text-xl font-bold mb-4">Фильтры</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Фильтр по категории */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Категория
            </label>
            <select
              className="w-full p-2 border border-gray-300 rounded-md"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value as AchievementCategory | 'all')}
            >
              <option value="all">Все категории</option>
              <option value={AchievementCategory.BEGINNER}>Для начинающих</option>
              <option value={AchievementCategory.GAMEPLAY}>Игровой процесс</option>
              <option value={AchievementCategory.MASTERY}>Мастерство</option>
              <option value={AchievementCategory.SOCIAL}>Социальные</option>
              <option value={AchievementCategory.COLLECTION}>Коллекционные</option>
              <option value={AchievementCategory.SPECIAL}>Особые</option>
            </select>
          </div>
          
          {/* Фильтр по сложности */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Сложность
            </label>
            <select
              className="w-full p-2 border border-gray-300 rounded-md"
              value={selectedDifficulty}
              onChange={(e) => setSelectedDifficulty(e.target.value as AchievementDifficulty | 'all')}
            >
              <option value="all">Любая сложность</option>
              <option value={AchievementDifficulty.EASY}>Легкие</option>
              <option value={AchievementDifficulty.MEDIUM}>Средние</option>
              <option value={AchievementDifficulty.HARD}>Сложные</option>
              <option value={AchievementDifficulty.VERY_HARD}>Очень сложные</option>
            </select>
          </div>
          
          {/* Дополнительные фильтры */}
          <div className="flex flex-col justify-center space-y-2">
            <label className="inline-flex items-center">
              <input
                type="checkbox"
                className="form-checkbox h-5 w-5 text-blue-600"
                checked={showUnlockedOnly}
                onChange={() => setShowUnlockedOnly(!showUnlockedOnly)}
              />
              <span className="ml-2 text-gray-700">Только разблокированные</span>
            </label>
            
            <label className="inline-flex items-center">
              <input
                type="checkbox"
                className="form-checkbox h-5 w-5 text-blue-600"
                checked={showWithRewardsOnly}
                onChange={() => setShowWithRewardsOnly(!showWithRewardsOnly)}
              />
              <span className="ml-2 text-gray-700">С наградами</span>
            </label>
          </div>
          
          {/* Статистика */}
          <div className="bg-gray-50 p-3 rounded-md">
            <h3 className="font-medium text-gray-800 mb-2">Статистика</h3>
            <p className="text-sm text-gray-600">
              Разблокировано: {userAchievements.unlockedAchievements.length} из {filteredAchievements.length}
            </p>
            <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
              <div 
                className="bg-green-600 h-2.5 rounded-full"
                style={{ width: `${Math.round((userAchievements.unlockedAchievements.length / filteredAchievements.length) * 100)}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Список достижений */}
      {filteredAchievements.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAchievements.map((achievement) => (
            <AchievementCard
              key={achievement.id}
              achievement={achievement}
              isUnlocked={userAchievements.unlockedAchievements.includes(achievement.id)}
              onClick={onAchievementClick}
              showProgress={true}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-10">
          <p className="text-gray-500 text-lg">Нет достижений, соответствующих выбранным фильтрам</p>
        </div>
      )}
    </div>
  );
};