import React from 'react';
import { Achievement } from '../model/types';
import { getAchievementCompletionPercentage } from '../utils/achievementUtils';

interface AchievementCardProps {
  achievement: Achievement;
  isUnlocked?: boolean;
  onClick?: (achievement: Achievement) => void;
  showProgress?: boolean;
}

/**
 * Компонент для отображения карточки достижения
 */
export const AchievementCard: React.FC<AchievementCardProps> = ({
  achievement,
  isUnlocked = false,
  onClick,
  showProgress = true
}) => {
  const {
    name,
    description,
    icon,
    iconBgColor,
    difficulty,
    hidden,
    progressBased,
    currentProgress,
    targetProgress,
    reward
  } = achievement;

  // Если достижение скрыто и не разблокировано, показываем заглушку
  const isHidden = hidden && !isUnlocked;
  
  // Вычисляем процент выполнения
  const completionPercentage = getAchievementCompletionPercentage(achievement);

  // Определяем стили в зависимости от сложности
  const difficultyStyles = {
    easy: 'border-green-400',
    medium: 'border-blue-400',
    hard: 'border-orange-400',
    very_hard: 'border-red-500'
  };

  const borderClass = difficultyStyles[difficulty] || 'border-gray-300';
  
  return (
    <div 
      className={`relative rounded-lg p-4 border-2 ${borderClass} ${isUnlocked ? 'bg-gradient-to-br from-yellow-50 to-amber-100' : 'bg-white'} shadow-md transition-all duration-300 hover:shadow-lg cursor-pointer ${
        isUnlocked ? 'opacity-100' : 'opacity-80'
      }`}
      onClick={() => onClick && onClick(achievement)}
    >
      {/* Иконка достижения */}
      <div 
        className="absolute -top-4 -left-4 w-12 h-12 rounded-full flex items-center justify-center text-2xl shadow-md"
        style={{ backgroundColor: isHidden ? '#9E9E9E' : iconBgColor }}
      >
        {isHidden ? '?' : icon}
      </div>

      {/* Содержимое карточки */}
      <div className="ml-6 mt-2">
        <h3 className="font-bold text-lg">
          {isHidden ? 'Скрытое достижение' : name}
          {isUnlocked && (
            <span className="ml-2 text-green-600">✓</span>
          )}
        </h3>
        
        <p className="text-gray-700 mt-1">
          {isHidden ? 'Выполните особое условие, чтобы открыть это достижение' : description}
        </p>

        {/* Индикатор прогресса */}
        {progressBased && showProgress && !isHidden && (
          <div className="mt-3">
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div 
                className="bg-blue-600 h-2.5 rounded-full transition-all duration-500"
                style={{ width: `${completionPercentage}%` }}
              ></div>
            </div>
            <div className="text-xs text-gray-600 mt-1 text-right">
              {currentProgress}/{targetProgress} ({completionPercentage}%)
            </div>
          </div>
        )}

        {/* Награда за достижение */}
        {reward && !isHidden && (
          <div className="mt-2 text-sm text-purple-700 font-medium">
            Награда: {reward.name}
          </div>
        )}
      </div>

      {/* Метка сложности */}
      <div className="absolute top-2 right-2 text-xs font-medium px-2 py-1 rounded-full bg-gray-100">
        {{
          easy: 'Легкое',
          medium: 'Среднее',
          hard: 'Сложное',
          very_hard: 'Очень сложное'
        }[difficulty]}
      </div>
    </div>
  );
};