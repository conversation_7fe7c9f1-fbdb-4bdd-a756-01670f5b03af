import React from 'react';
import { Achievement, UserAchievements } from '../model/types';
import { getAchievementCompletionPercentage, getNextAchievementInSeries, formatTimeToAchievement } from '../utils/achievementUtils';

interface AchievementDetailsProps {
  achievement: Achievement;
  userAchievements: UserAchievements;
  onClose: () => void;
}

/**
 * Компонент для отображения детальной информации о достижении
 */
export const AchievementDetails: React.FC<AchievementDetailsProps> = ({
  achievement,
  userAchievements,
  onClose
}) => {
  const isUnlocked = userAchievements.unlockedAchievements.includes(achievement.id);
  const completionPercentage = getAchievementCompletionPercentage(achievement);
  const nextAchievement = getNextAchievementInSeries(achievement.id);
  
  // Получаем дату разблокировки, если достижение разблокировано
  const unlockedDate = userAchievements.unlockedDates[achievement.id];
  
  // Форматируем дату для отображения
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ru-RU', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Заголовок */}
        <div 
          className="p-6 border-b flex items-center justify-between"
          style={{ backgroundColor: achievement.iconBgColor + '20' }} // Полупрозрачный фон цвета иконки
        >
          <div className="flex items-center">
            <div 
              className="w-16 h-16 rounded-full flex items-center justify-center text-3xl shadow-md mr-4"
              style={{ backgroundColor: achievement.iconBgColor }}
            >
              {achievement.icon}
            </div>
            
            <div>
              <h2 className="text-2xl font-bold">{achievement.name}</h2>
              <p className="text-gray-600">{achievement.description}</p>
            </div>
          </div>
          
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-xl"
          >
            ×
          </button>
        </div>
        
        {/* Содержимое */}
        <div className="p-6">
          {/* Статус достижения */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">Статус:</span>
              <span 
                className={`px-3 py-1 rounded-full text-sm font-medium ${
                  isUnlocked ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                }`}
              >
                {isUnlocked ? 'Разблокировано' : 'Не разблокировано'}
              </span>
            </div>
            
            {isUnlocked && unlockedDate && (
              <div className="text-sm text-gray-600 mt-1">
                Получено: {formatDate(unlockedDate)}
              </div>
            )}
          </div>
          
          {/* Прогресс */}
          {achievement.progressBased && (
            <div className="mb-6">
              <h3 className="font-medium mb-2">Прогресс:</h3>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className="bg-blue-600 h-3 rounded-full transition-all duration-500"
                  style={{ width: `${completionPercentage}%` }}
                ></div>
              </div>
              <div className="flex justify-between mt-1 text-sm text-gray-600">
                <span>{achievement.currentProgress || 0}</span>
                <span>{completionPercentage}%</span>
                <span>{achievement.targetProgress}</span>
              </div>
            </div>
          )}
          
          {/* Информация о достижении */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div>
              <h3 className="font-medium mb-2">Категория:</h3>
              <p className="text-gray-700">
                {{
                  beginner: 'Для начинающих',
                  gameplay: 'Игровой процесс',
                  mastery: 'Мастерство',
                  social: 'Социальные',
                  collection: 'Коллекционные',
                  special: 'Особые'
                }[achievement.category]}
              </p>
            </div>
            
            <div>
              <h3 className="font-medium mb-2">Сложность:</h3>
              <p className="text-gray-700">
                {{
                  easy: 'Легкое',
                  medium: 'Среднее',
                  hard: 'Сложное',
                  very_hard: 'Очень сложное'
                }[achievement.difficulty]}
              </p>
            </div>
            
            {achievement.gameType && (
              <div>
                <h3 className="font-medium mb-2">Тип игры:</h3>
                <p className="text-gray-700">
                  {{
                    durak_podkidnoy: 'Дурак подкидной',
                    durak_perevodnoy: 'Дурак переводной',
                    fool_online: 'Дурак онлайн'
                  }[achievement.gameType] || achievement.gameType}
                </p>
              </div>
            )}
          </div>
          
          {/* Награда */}
          {achievement.reward && (
            <div className="mb-6">
              <h3 className="font-medium mb-2">Награда:</h3>
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="mr-4 text-2xl">
                    {{
                      badge: '🏅',
                      avatar_frame: '🖼️',
                      title: '📜',
                      in_game_currency: '💰'
                    }[achievement.reward.type]}
                  </div>
                  <div>
                    <p className="font-medium text-purple-800">{achievement.reward.name}</p>
                    {achievement.reward.description && (
                      <p className="text-sm text-purple-600 mt-1">{achievement.reward.description}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Следующее достижение в серии */}
          {nextAchievement && (
            <div className="mb-6">
              <h3 className="font-medium mb-2">Следующее достижение в серии:</h3>
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div 
                    className="w-10 h-10 rounded-full flex items-center justify-center text-xl mr-3"
                    style={{ backgroundColor: nextAchievement.iconBgColor }}
                  >
                    {nextAchievement.icon}
                  </div>
                  <div>
                    <p className="font-medium">{nextAchievement.name}</p>
                    <p className="text-sm text-gray-600">{nextAchievement.description}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Советы */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-medium text-blue-800 mb-2">Советы по получению:</h3>
            <p className="text-blue-700 text-sm">
              {achievement.id === 'perfect_defense' && 'Старайтесь накопить козырные карты для защиты.'}
              {achievement.id === 'transfer_master' && 'Держите карты одного достоинства для перевода.'}
              {achievement.id === 'quick_win' && 'Начинайте с высоких карт, чтобы быстрее избавиться от них.'}
              {achievement.id === 'comeback' && 'Не сдавайтесь, даже если у вас осталась одна карта!'}
              {achievement.id === 'trump_master' && 'Старайтесь сохранять козырные карты до конца игры.'}
              {achievement.id.includes('win_streak') && 'Играйте регулярно и анализируйте свои ошибки после каждой игры.'}
              {achievement.id.includes('games_played') && 'Просто наслаждайтесь игрой, и это достижение придет само собой.'}
              {achievement.id.includes('daily_player') && 'Заходите в игру каждый день, даже если у вас есть всего несколько минут.'}
              {!achievement.id.match(/perfect_defense|transfer_master|quick_win|comeback|trump_master|win_streak|games_played|daily_player/) && 
                'Продолжайте играть и улучшать свои навыки, и вы обязательно получите это достижение!'}
            </p>
          </div>
        </div>
        
        {/* Кнопки */}
        <div className="p-4 border-t bg-gray-50 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Закрыть
          </button>
        </div>
      </div>
    </div>
  );
};