import React, { useEffect, useCallback } from 'react';
import { useAchievements } from '../providers/AchievementProvider';
import { AchievementEvent, AchievementCategory } from '../model/types';

interface AchievementTrackerProps {
  gameType?: string; // Тип игры (дурак, преферанс и т.д.)
  children: React.ReactNode;
}

/**
 * Компонент для отслеживания игровых событий и автоматического присуждения достижений
 * Интегрируется с игровым процессом и отправляет события в систему достижений
 */
export const AchievementTracker: React.FC<AchievementTrackerProps> = ({
  gameType,
  children
}) => {
  const { dispatchGameEvent } = useAchievements();

  // Обработчик события начала игры
  const handleGameStart = useCallback(() => {
    dispatchGameEvent({
      type: 'game_started',
      gameType: gameType || 'unknown',
      timestamp: new Date().toISOString(),
      data: {}
    });
  }, [dispatchGameEvent, gameType]);

  // Обработчик события завершения игры
  const handleGameEnd = useCallback((isWin: boolean) => {
    dispatchGameEvent({
      type: isWin ? 'game_won' : 'game_lost',
      gameType: gameType || 'unknown',
      timestamp: new Date().toISOString(),
      data: { isWin }
    });

    // Также отправляем общее событие завершения игры
    dispatchGameEvent({
      type: 'game_completed',
      gameType: gameType || 'unknown',
      timestamp: new Date().toISOString(),
      data: { isWin }
    });
  }, [dispatchGameEvent, gameType]);

  // Обработчик события хода в игре
  const handleGameMove = useCallback((moveData: any) => {
    dispatchGameEvent({
      type: 'game_move',
      gameType: gameType || 'unknown',
      timestamp: new Date().toISOString(),
      data: moveData
    });
  }, [dispatchGameEvent, gameType]);

  // Обработчик события использования специальной карты или комбинации
  const handleSpecialMove = useCallback((moveType: string, moveData: any) => {
    dispatchGameEvent({
      type: 'special_move',
      gameType: gameType || 'unknown',
      timestamp: new Date().toISOString(),
      data: { moveType, ...moveData }
    });
  }, [dispatchGameEvent, gameType]);

  // Обработчик события социального взаимодействия
  const handleSocialInteraction = useCallback((interactionType: string, userData: any) => {
    dispatchGameEvent({
      type: 'social_interaction',
      gameType: gameType || 'unknown',
      timestamp: new Date().toISOString(),
      data: { interactionType, userData }
    });
  }, [dispatchGameEvent, gameType]);

  // Регистрируем глобальные обработчики событий
  useEffect(() => {
    // Создаем объект с методами для отслеживания событий
    const achievementTracker = {
      gameStart: handleGameStart,
      gameEnd: handleGameEnd,
      gameMove: handleGameMove,
      specialMove: handleSpecialMove,
      socialInteraction: handleSocialInteraction
    };

    // Добавляем объект в глобальный контекст для доступа из игровых компонентов
    (window as any).achievementTracker = achievementTracker;

    // Очищаем при размонтировании
    return () => {
      delete (window as any).achievementTracker;
    };
  }, [handleGameStart, handleGameEnd, handleGameMove, handleSpecialMove, handleSocialInteraction]);

  // Пример использования в компоненте игры:
  // const { achievementTracker } = window as any;
  // achievementTracker.gameStart();
  // achievementTracker.gameEnd(true); // победа
  // achievementTracker.specialMove('козырь', { cardValue: 'туз', suit: 'пики' });

  return <>{children}</>;
};

/**
 * Вспомогательные функции для отправки событий достижений
 */

// Функция для отправки события начала игры
export const trackGameStart = (gameType: string) => {
  if ((window as any).achievementTracker) {
    (window as any).achievementTracker.gameStart();
  }
};

// Функция для отправки события завершения игры
export const trackGameEnd = (gameType: string, isWin: boolean) => {
  if ((window as any).achievementTracker) {
    (window as any).achievementTracker.gameEnd(isWin);
  }
};

// Функция для отправки события хода в игре
export const trackGameMove = (gameType: string, moveData: any) => {
  if ((window as any).achievementTracker) {
    (window as any).achievementTracker.gameMove(moveData);
  }
};

// Функция для отправки события использования специальной карты или комбинации
export const trackSpecialMove = (gameType: string, moveType: string, moveData: any) => {
  if ((window as any).achievementTracker) {
    (window as any).achievementTracker.specialMove(moveType, moveData);
  }
};

// Функция для отправки события социального взаимодействия
export const trackSocialInteraction = (interactionType: string, userData: any) => {
  if ((window as any).achievementTracker) {
    (window as any).achievementTracker.socialInteraction(interactionType, userData);
  }
};