import { Achievement, AchievementCategory, AchievementDifficulty, UserAchievements } from '../model/types';
import { achievementData, getAchievementById } from '../model/achievementData';

/**
 * Проверяет, разблокировано ли достижение у пользователя
 */
export const isAchievementUnlocked = (
  userAchievements: UserAchievements,
  achievementId: string
): boolean => {
  return userAchievements.unlockedAchievements.includes(achievementId);
};

/**
 * Возвращает процент выполнения достижения
 */
export const getAchievementCompletionPercentage = (
  achievement: Achievement
): number => {
  if (!achievement.progressBased || !achievement.targetProgress) {
    return isNaN(achievement.currentProgress || 0) ? 0 : 100;
  }

  const progress = achievement.currentProgress || 0;
  const target = achievement.targetProgress;
  
  return Math.min(Math.round((progress / target) * 100), 100);
};

/**
 * Возвращает отсортированный список достижений по категории и сложности
 */
export const getSortedAchievements = (
  achievements: Achievement[],
  sortByDifficulty: boolean = true
): Achievement[] => {
  return [...achievements].sort((a, b) => {
    // Сначала сортируем по категории
    if (a.category !== b.category) {
      return a.category.localeCompare(b.category);
    }
    
    // Затем по сложности, если указано
    if (sortByDifficulty) {
      const difficultyOrder = {
        [AchievementDifficulty.EASY]: 1,
        [AchievementDifficulty.MEDIUM]: 2,
        [AchievementDifficulty.HARD]: 3,
        [AchievementDifficulty.VERY_HARD]: 4
      };
      
      return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty];
    }
    
    return 0;
  });
};

/**
 * Возвращает список рекомендуемых достижений для пользователя
 * на основе его текущего прогресса
 */
export const getRecommendedAchievements = (
  userAchievements: UserAchievements,
  count: number = 3
): Achievement[] => {
  // Получаем все незаблокированные достижения
  const unlockedIds = userAchievements.unlockedAchievements;
  const lockedAchievements = achievementData.filter(
    achievement => !unlockedIds.includes(achievement.id) && !achievement.hidden
  );
  
  // Находим достижения, которые близки к выполнению
  const achievementsWithProgress = lockedAchievements
    .filter(achievement => achievement.progressBased)
    .map(achievement => {
      const currentProgress = userAchievements.progress[achievement.id] || 0;
      const targetProgress = achievement.targetProgress || 1;
      const completionRate = currentProgress / targetProgress;
      
      return {
        achievement,
        completionRate
      };
    })
    .filter(item => item.completionRate > 0)
    .sort((a, b) => b.completionRate - a.completionRate)
    .slice(0, count)
    .map(item => item.achievement);
  
  // Если у нас недостаточно достижений с прогрессом, добавляем легкие достижения
  if (achievementsWithProgress.length < count) {
    const easyAchievements = lockedAchievements
      .filter(achievement => 
        achievement.difficulty === AchievementDifficulty.EASY && 
        !achievementsWithProgress.some(a => a.id === achievement.id)
      )
      .slice(0, count - achievementsWithProgress.length);
    
    return [...achievementsWithProgress, ...easyAchievements];
  }
  
  return achievementsWithProgress;
};

/**
 * Рассчитывает общий прогресс пользователя по всем достижениям
 */
export const calculateOverallProgress = (userAchievements: UserAchievements): number => {
  const totalAchievements = achievementData.length;
  const unlockedCount = userAchievements.unlockedAchievements.length;
  
  return Math.round((unlockedCount / totalAchievements) * 100);
};

/**
 * Возвращает следующее достижение в серии
 * (например, после win_streak_3 предложит win_streak_5)
 */
export const getNextAchievementInSeries = (
  achievementId: string
): Achievement | undefined => {
  // Извлекаем базовый ID и номер из ID достижения
  const match = achievementId.match(/(.+)_(\d+)$/);
  if (!match) return undefined;
  
  const [, baseId, currentNumber] = match;
  const currentValue = parseInt(currentNumber, 10);
  
  // Ищем достижение с тем же базовым ID и большим номером
  const nextAchievements = achievementData.filter(achievement => {
    const nextMatch = achievement.id.match(/(.+)_(\d+)$/);
    if (!nextMatch) return false;
    
    const [, nextBaseId, nextNumber] = nextMatch;
    const nextValue = parseInt(nextNumber, 10);
    
    return nextBaseId === baseId && nextValue > currentValue;
  });
  
  // Сортируем и берем следующее по порядку
  if (nextAchievements.length > 0) {
    return nextAchievements.sort((a, b) => {
      const aValue = parseInt(a.id.match(/_(\d+)$/)?.[1] || '0', 10);
      const bValue = parseInt(b.id.match(/_(\d+)$/)?.[1] || '0', 10);
      return aValue - bValue;
    })[0];
  }
  
  return undefined;
};

/**
 * Форматирует время, оставшееся до получения временного достижения
 */
export const formatTimeToAchievement = (daysLeft: number): string => {
  if (daysLeft <= 0) return 'Сегодня';
  if (daysLeft === 1) return 'Завтра';
  
  // Правильное склонение для русского языка
  const lastDigit = daysLeft % 10;
  const lastTwoDigits = daysLeft % 100;
  
  if (lastDigit === 1 && lastTwoDigits !== 11) {
    return `Через ${daysLeft} день`;
  } else if ([2, 3, 4].includes(lastDigit) && ![12, 13, 14].includes(lastTwoDigits)) {
    return `Через ${daysLeft} дня`;
  } else {
    return `Через ${daysLeft} дней`;
  }
};