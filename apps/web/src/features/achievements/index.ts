/**
 * Индексный файл для экспорта всех компонентов и функций системы достижений
 */

// Экспорт типов
export * from './model/types';

// Экспорт данных и функций для работы с достижениями
export * from './model/achievementData';

// Экспорт утилит
export * from './utils/achievementUtils';

// Экспорт сервисов
export * from './services/achievementService';

// Экспорт провайдеров
export * from './providers/AchievementProvider';

// Экспорт компонентов
export * from './components/AchievementCard';
export * from './components/AchievementsList';
export * from './components/AchievementDetails';
export * from './components/AchievementNotification';
export * from './components/AchievementNotificationManager';
export * from './components/AchievementTracker';

// Экспорт страниц
export * from './pages/AchievementsPage';
export * from './pages/UserProfilePage';

// Экспорт UI компонентов
export * from './ui/AchievementSystem';