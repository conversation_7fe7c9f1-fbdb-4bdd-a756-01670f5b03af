import React, { useState, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';
import { Achievement } from '../model/types';

type AchievementSystemProps = {
  achievements: Achievement[];
  unlockedAchievements: string[];
  onAchievementClick?: (achievementId: string) => void;
};

// Анимации
const slideIn = keyframes`
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
`;

const fadeIn = keyframes`
  from { opacity: 0; }
  to { opacity: 1; }
`;

const glow = keyframes`
  0% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
  50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
  100% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
`;

// Стилизованные компоненты
const AchievementsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
`;

const AchievementTitle = styled.h2`
  color: var(--primary-color, #2c3e50);
  margin-bottom: 20px;
  text-align: center;
`;

const AchievementGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
`;

const AchievementCard = styled.div<{ unlocked: boolean }>`
  background-color: var(--card-background, #fff);
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  opacity: ${props => props.unlocked ? 1 : 0.7};
  filter: ${props => props.unlocked ? 'none' : 'grayscale(0.8)'};
  animation: ${fadeIn} 0.5s ease-in-out;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  ${props => props.unlocked && `
    animation: ${glow} 2s infinite;
  `}
`;

const AchievementIcon = styled.div<{ bgColor: string }>`
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: ${props => props.bgColor || 'var(--accent-color, #3498db)'};
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12px;
  font-size: 24px;
`;

const AchievementName = styled.h3`
  margin: 0 0 8px 0;
  font-size: 16px;
  color: var(--text-color, #333);
`;

const AchievementDescription = styled.p`
  margin: 0;
  font-size: 14px;
  color: var(--text-color, #333);
  opacity: 0.8;
`;

const AchievementDate = styled.span`
  font-size: 12px;
  color: var(--text-color, #333);
  opacity: 0.6;
  margin-top: 8px;
`;

const AchievementProgress = styled.div`
  width: 100%;
  margin-top: 12px;
`;

const ProgressBar = styled.div<{ progress: number }>`
  height: 6px;
  background-color: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
  position: relative;
  
  &:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: ${props => `${props.progress}%`};
    background-color: var(--accent-color, #3498db);
    border-radius: 3px;
    transition: width 0.3s ease-in-out;
  }
`;

const ProgressText = styled.div`
  font-size: 12px;
  text-align: right;
  margin-top: 4px;
  color: var(--text-color, #333);
  opacity: 0.8;
`;

const NotificationContainer = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

const NotificationCard = styled.div`
  background-color: var(--card-background, #fff);
  border-left: 4px solid var(--accent-color, #3498db);
  border-radius: 4px;
  padding: 12px 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 12px;
  width: 300px;
  animation: ${slideIn} 0.3s ease-in-out;
`;

const NotificationIcon = styled.div<{ bgColor: string }>`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.bgColor || 'var(--accent-color, #3498db)'};
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  flex-shrink: 0;
`;

const NotificationContent = styled.div`
  flex: 1;
`;

const NotificationTitle = styled.h4`
  margin: 0 0 4px 0;
  font-size: 14px;
  color: var(--text-color, #333);
`;

const NotificationText = styled.p`
  margin: 0;
  font-size: 12px;
  color: var(--text-color, #333);
  opacity: 0.8;
`;

// Компонент уведомления о достижении
const AchievementNotification: React.FC<{
  achievement: Achievement;
  onClose: () => void;
}> = ({ achievement, onClose }) => {
  useEffect(() => {
    // Автоматически закрываем уведомление через 5 секунд
    const timer = setTimeout(() => {
      onClose();
    }, 5000);
    
    return () => clearTimeout(timer);
  }, [onClose]);
  
  return (
    <NotificationCard>
      <NotificationIcon bgColor={achievement.iconBgColor}>
        {achievement.icon}
      </NotificationIcon>
      <NotificationContent>
        <NotificationTitle>Достижение разблокировано!</NotificationTitle>
        <NotificationText>{achievement.name}</NotificationText>
      </NotificationContent>
    </NotificationCard>
  );
};

// Основной компонент системы достижений
export const AchievementSystem: React.FC<AchievementSystemProps> = ({
  achievements,
  unlockedAchievements,
  onAchievementClick
}) => {
  const [notifications, setNotifications] = useState<Achievement[]>([]);
  
  // Обработчик клика по достижению
  const handleAchievementClick = (achievementId: string) => {
    if (onAchievementClick) {
      onAchievementClick(achievementId);
    }
  };
  
  // Закрытие уведомления
  const handleCloseNotification = (achievementId: string) => {
    setNotifications(prev => prev.filter(a => a.id !== achievementId));
  };
  
  return (
    <>
      <AchievementsContainer>
        <AchievementTitle>Достижения</AchievementTitle>
        <AchievementGrid>
          {achievements.map(achievement => {
            const isUnlocked = unlockedAchievements.includes(achievement.id);
            
            return (
              <AchievementCard 
                key={achievement.id} 
                unlocked={isUnlocked}
                onClick={() => handleAchievementClick(achievement.id)}
              >
                <AchievementIcon bgColor={achievement.iconBgColor}>
                  {achievement.icon}
                </AchievementIcon>
                <AchievementName>{achievement.name}</AchievementName>
                <AchievementDescription>
                  {achievement.description}
                </AchievementDescription>
                
                {achievement.progressBased && (
                  <AchievementProgress>
                    <ProgressBar progress={achievement.currentProgress || 0} />
                    <ProgressText>
                      {achievement.currentProgress || 0}/{achievement.targetProgress}
                    </ProgressText>
                  </AchievementProgress>
                )}
                
                {isUnlocked && achievement.unlockedDate && (
                  <AchievementDate>
                    Получено: {new Date(achievement.unlockedDate).toLocaleDateString()}
                  </AchievementDate>
                )}
              </AchievementCard>
            );
          })}
        </AchievementGrid>
      </AchievementsContainer>
      
      {/* Контейнер для уведомлений */}
      <NotificationContainer>
        {notifications.map(achievement => (
          <AchievementNotification
            key={achievement.id}
            achievement={achievement}
            onClose={() => handleCloseNotification(achievement.id)}
          />
        )}
      </NotificationContainer>
    </>
  );
};