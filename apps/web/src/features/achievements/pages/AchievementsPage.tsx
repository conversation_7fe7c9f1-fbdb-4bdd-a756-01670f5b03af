import React, { useState, useEffect } from 'react';
import { Achievement, UserAchievements } from '../model/types';
import { AchievementsList } from '../components/AchievementsList';
import { AchievementDetails } from '../components/AchievementDetails';
import { getRecommendedAchievements, calculateOverallProgress } from '../utils/achievementUtils';

/**
 * Главная страница достижений
 */
export const AchievementsPage: React.FC = () => {
  // Здесь в реальном приложении данные будут загружаться из API
  // Для демонстрации используем моковые данные
  const [userAchievements, setUserAchievements] = useState<UserAchievements>({
    userId: 'user123',
    unlockedAchievements: ['first_game', 'first_win', 'tutorial_complete', 'games_played_10'],
    progress: {
      'games_played_50': 25,
      'games_played_100': 25,
      'daily_player': 3,
      'all_games': 2,
      'transfer_master': 1
    },
    unlockedDates: {
      'first_game': new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      'first_win': new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
      'tutorial_complete': new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
      'games_played_10': new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
    }
  });
  
  // Состояние для отображения детальной информации о достижении
  const [selectedAchievement, setSelectedAchievement] = useState<Achievement | null>(null);
  
  // Получаем рекомендуемые достижения
  const recommendedAchievements = getRecommendedAchievements(userAchievements);
  
  // Общий прогресс пользователя
  const overallProgress = calculateOverallProgress(userAchievements);
  
  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Заголовок страницы */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-8 px-4">
        <div className="container mx-auto">
          <h1 className="text-3xl font-bold mb-2">Достижения</h1>
          <p className="text-blue-100">Открывайте достижения и получайте награды за свои успехи в игре</p>
          
          {/* Общий прогресс */}
          <div className="mt-6">
            <div className="flex justify-between text-sm mb-1">
              <span>Общий прогресс</span>
              <span>{overallProgress}%</span>
            </div>
            <div className="w-full bg-blue-800 bg-opacity-50 rounded-full h-2.5">
              <div 
                className="bg-yellow-400 h-2.5 rounded-full transition-all duration-500"
                style={{ width: `${overallProgress}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto py-8 px-4">
        {/* Рекомендуемые достижения */}
        <div className="mb-10">
          <h2 className="text-2xl font-bold mb-4">Рекомендуемые достижения</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {recommendedAchievements.map((achievement) => (
              <div 
                key={achievement.id}
                className="bg-white rounded-lg shadow-md p-4 border-l-4 border-blue-500 cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => setSelectedAchievement(achievement)}
              >
                <div className="flex items-center mb-3">
                  <div 
                    className="w-10 h-10 rounded-full flex items-center justify-center text-xl mr-3"
                    style={{ backgroundColor: achievement.iconBgColor }}
                  >
                    {achievement.icon}
                  </div>
                  <div>
                    <h3 className="font-bold">{achievement.name}</h3>
                    <p className="text-sm text-gray-600">{achievement.description}</p>
                  </div>
                </div>
                
                {achievement.progressBased && (
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ 
                          width: `${Math.round(((userAchievements.progress[achievement.id] || 0) / (achievement.targetProgress || 1)) * 100)}%` 
                        }}
                      ></div>
                    </div>
                    <div className="text-xs text-right mt-1 text-gray-500">
                      {userAchievements.progress[achievement.id] || 0}/{achievement.targetProgress}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
        
        {/* Список всех достижений */}
        <AchievementsList 
          userAchievements={userAchievements}
          onAchievementClick={setSelectedAchievement}
        />
      </div>
      
      {/* Модальное окно с детальной информацией */}
      {selectedAchievement && (
        <AchievementDetails 
          achievement={selectedAchievement}
          userAchievements={userAchievements}
          onClose={() => setSelectedAchievement(null)}
        />
      )}
    </div>
  );
};