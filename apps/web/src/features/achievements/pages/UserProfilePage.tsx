import React, { useState, useEffect } from 'react';
import { Achievement, UserAchievements } from '../model/types';
import { AchievementCard } from '../components/AchievementCard';
import { AchievementDetails } from '../components/AchievementDetails';
import { calculateOverallProgress, getRecentAchievements, getNextAchievementsToUnlock } from '../utils/achievementUtils';
import { achievementData } from '../model/achievementData';

interface UserProfilePageProps {
  userId: string;
  userName: string;
  userAvatar?: string;
}

/**
 * Страница профиля пользователя с отображением достижений
 */
export const UserProfilePage: React.FC<UserProfilePageProps> = ({
  userId,
  userName,
  userAvatar
}) => {
  // В реальном приложении данные будут загружаться из API
  // Для демонстрации используем моковые данные
  const [userAchievements, setUserAchievements] = useState<UserAchievements>({
    userId,
    unlockedAchievements: ['first_game', 'first_win', 'tutorial_complete', 'games_played_10'],
    progress: {
      'games_played_50': 25,
      'games_played_100': 25,
      'daily_player': 3,
      'all_games': 2,
      'transfer_master': 1
    },
    unlockedDates: {
      'first_game': new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      'first_win': new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
      'tutorial_complete': new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
      'games_played_10': new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
    }
  });
  
  // Состояние для отображения детальной информации о достижении
  const [selectedAchievement, setSelectedAchievement] = useState<Achievement | null>(null);
  
  // Получаем общий прогресс пользователя
  const overallProgress = calculateOverallProgress(userAchievements);
  
  // Получаем последние разблокированные достижения
  const recentAchievements = getRecentAchievements(userAchievements, 3);
  
  // Получаем следующие достижения, которые можно разблокировать
  const nextAchievements = getNextAchievementsToUnlock(userAchievements, 3);
  
  // Обработчик клика по карточке достижения с расширенной информацией
  const handleAchievementClick = (achievement: Achievement) => {
    // Получаем дополнительную информацию о достижении
    const achievementDetails = {
      ...achievement,
      // Добавляем информацию о прогрессе
      currentProgress: userAchievements.progress[achievement.id] || 0,
      // Добавляем информацию о дате разблокировки
      unlockedDate: userAchievements.unlockedDates[achievement.id],
      // Проверяем, разблокировано ли достижение
      isUnlocked: userAchievements.unlockedAchievements.includes(achievement.id),
      // Добавляем историю достижения (если есть)
      history: getAchievementHistory(achievement.id),
      // Добавляем рекомендации по получению
      recommendations: getAchievementRecommendations(achievement)
    };
    
    // Устанавливаем выбранное достижение с расширенной информацией
    setSelectedAchievement(achievementDetails);
    
    // Отправляем аналитику о просмотре достижения
    trackAchievementView(achievement.id);
  };
  
  // Получение истории достижения
  const getAchievementHistory = (achievementId: string) => {
    // В реальном приложении здесь будет запрос к API
    // Для демонстрации возвращаем заглушку
    return {
      firstUnlockedBy: 'Игрок123',
      totalUnlocks: 1243,
      rarity: 'Редкое (8% игроков)',
      relatedAchievements: ['first_win', 'win_streak_3']
    };
  };
  
  // Получение персонализированных рекомендаций по достижению
  const getAchievementRecommendations = (achievement: Achievement) => {
    // Получаем текущий прогресс
    const progress = userAchievements.progress[achievement.id] || 0;
    
    // Проверяем, разблокировано ли уже достижение
    if (userAchievements.unlockedAchievements.includes(achievement.id)) {
      return 'Достижение уже разблокировано! Попробуйте получить другие достижения.';
    }
    
    // В зависимости от типа достижения возвращаем персонализированные рекомендации
    if (achievement.progressBased && achievement.targetProgress) {
      const remaining = achievement.targetProgress - progress;
      const percentComplete = Math.floor((progress / achievement.targetProgress) * 100);
      
      return `Прогресс: ${percentComplete}%. Осталось: ${remaining}. Продолжайте играть, чтобы разблокировать это достижение.`;
    }
    
    // Рекомендации в зависимости от типа требований
    if (achievement.requirements && achievement.requirements.type) {
      switch (achievement.requirements.type) {
        case 'gamesPlayed':
          return `Сыграйте ещё ${achievement.requirements.value - progress} игр, чтобы получить это достижение.`;
        
        case 'gamesWon':
          return `Выиграйте ещё ${achievement.requirements.value - progress} игр, чтобы получить это достижение.`;
        
        case 'rating':
          return `Повысьте свой рейтинг до ${achievement.requirements.value}. Текущий рейтинг: ${progress}.`;
        
        case 'winStreak':
          return `Выиграйте ${achievement.requirements.value} игр подряд. Текущая серия побед: ${progress}.`;
        
        case 'cardsPlayed':
          return `Сыграйте ещё ${achievement.requirements.value - progress} карт, чтобы получить это достижение.`;
        
        case 'specialCardsPlayed':
          return `Сыграйте ещё ${achievement.requirements.value - progress} специальных карт, чтобы получить это достижение.`;
        
        case 'tournamentWins':
          return `Выиграйте ещё ${achievement.requirements.value - progress} турниров, чтобы получить это достижение.`;
        
        case 'friendsInvited':
          return `Пригласите ещё ${achievement.requirements.value - progress} друзей, чтобы получить это достижение.`;
        
        case 'daysActive':
          return `Будьте активны ещё ${achievement.requirements.value - progress} дней, чтобы получить это достижение.`;
      }
    }
    
    // Рекомендации по категориям
    if (achievement.category) {
      switch (achievement.category) {
        case 'MASTERY':
          return 'Улучшайте свои навыки игры, чтобы получить это достижение.';
        
        case 'EXPLORATION':
          return 'Исследуйте различные аспекты игры и попробуйте новые режимы.';
        
        case 'SOCIAL':
          return 'Взаимодействуйте с другими игроками, приглашайте друзей и участвуйте в командных играх.';
        
        case 'COLLECTION':
          return 'Соберите больше карт и разблокируйте новые колоды.';
        
        case 'CHALLENGE':
          return 'Попробуйте выполнить сложные задания и испытания в игре.';
      }
    }
    
    // Общая рекомендация, если ничего не подошло
    return 'Исследуйте различные аспекты игры, чтобы разблокировать это достижение.';
  };
  
  // Отслеживание просмотра достижения
  const trackAchievementView = (achievementId: string) => {
    // В реальном приложении здесь будет отправка аналитики
    console.log(`Просмотр достижения: ${achievementId}`);
  };
  
  // Обработчик закрытия детальной информации о достижении
  const handleCloseDetails = () => {
    setSelectedAchievement(null);
  };
  
  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Шапка профиля */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-8 px-4">
        <div className="container mx-auto">
          <div className="flex items-center">
            {userAvatar ? (
              <img 
                src={userAvatar} 
                alt={userName} 
                className="w-24 h-24 rounded-full border-4 border-white shadow-md"
              />
            ) : (
              <div className="w-24 h-24 rounded-full bg-white text-indigo-700 flex items-center justify-center text-3xl font-bold shadow-md">
                {userName.charAt(0).toUpperCase()}
              </div>
            )}
            <div className="ml-6">
              <h1 className="text-3xl font-bold mb-2">{userName}</h1>
              <p className="text-blue-100">ID: {userId}</p>
            </div>
          </div>
          
          {/* Общий прогресс */}
          <div className="mt-6">
            <div className="flex justify-between text-sm mb-1">
              <span>Общий прогресс достижений</span>
              <span>{overallProgress}%</span>
            </div>
            <div className="w-full bg-blue-200 rounded-full h-2.5">
              <div 
                className="bg-blue-500 h-2.5 rounded-full" 
                style={{ width: `${overallProgress}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto py-8 px-4">
        {/* Статистика достижений */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-2">Разблокировано достижений</h3>
            <p className="text-3xl font-bold text-indigo-600">
              {userAchievements.unlockedAchievements.length} / {achievementData.length}
            </p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-2">Редкие достижения</h3>
            <p className="text-3xl font-bold text-indigo-600">
              {userAchievements.unlockedAchievements.filter(id => {
                const achievement = achievementData.find(a => a.id === id);
                return achievement && achievement.difficulty === 'very_hard';
              }).length}
            </p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-2">Получено наград</h3>
            <p className="text-3xl font-bold text-indigo-600">
              {userAchievements.unlockedAchievements.filter(id => {
                const achievement = achievementData.find(a => a.id === id);
                return achievement && achievement.reward;
              }).length}
            </p>
          </div>
        </div>
        
        {/* Последние разблокированные достижения */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-4">Последние достижения</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {recentAchievements.map(achievement => (
              <AchievementCard
                key={achievement.id}
                achievement={achievement}
                isUnlocked={true}
                onClick={handleAchievementClick}
              />
            ))}
          </div>
        </div>
        
        {/* Следующие достижения для разблокировки */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-4">Следующие цели</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {nextAchievements.map(achievement => (
              <AchievementCard
                key={achievement.id}
                achievement={achievement}
                isUnlocked={false}
                onClick={handleAchievementClick}
                showProgress={true}
              />
            ))}
          </div>
        </div>
      </div>
      
      {/* Модальное окно с детальной информацией о достижении */}
      {selectedAchievement && (
        <AchievementDetails
          achievement={selectedAchievement}
          userAchievements={userAchievements}
          onClose={handleCloseDetails}
        />
      )}
    </div>
  );
};