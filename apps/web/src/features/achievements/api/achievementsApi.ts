import { UserAchievements } from '../model/types';

/**
 * API для работы с достижениями
 */
export const achievementsApi = {
  /**
   * Получить достижения пользователя
   * @param userId ID пользователя
   */
  getUserAchievements: async (userId: string): Promise<UserAchievements> => {
    try {
      // В реальном приложении здесь будет запрос к API
      // Для демонстрации имитируем задержку и возвращаем моковые данные
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            userId,
            unlockedAchievements: ['first_game', 'first_win', 'tutorial_complete', 'games_played_10'],
            progress: {
              'games_played_50': 25,
              'games_played_100': 25,
              'daily_player': 3,
              'all_games': 2,
              'transfer_master': 1
            },
            unlockedDates: {
              'first_game': new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
              'first_win': new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
              'tutorial_complete': new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
              'games_played_10': new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
            }
          });
        }, 800); // Имитация задержки сети
      });
    } catch (error) {
      console.error('Ошибка при получении достижений пользователя:', error);
      throw error;
    }
  },

  /**
   * Обновить прогресс достижения
   * @param userId ID пользователя
   * @param achievementId ID достижения
   * @param progress Новое значение прогресса
   */
  updateAchievementProgress: async (
    userId: string,
    achievementId: string,
    progress: number
  ): Promise<void> => {
    try {
      // В реальном приложении здесь будет запрос к API
      // Для демонстрации имитируем задержку
      return new Promise((resolve) => {
        setTimeout(() => {
          console.log(`Обновлен прогресс достижения ${achievementId} для пользователя ${userId}: ${progress}`);
          resolve();
        }, 500);
      });
    } catch (error) {
      console.error('Ошибка при обновлении прогресса достижения:', error);
      throw error;
    }
  },

  /**
   * Получить историю достижения
   * @param achievementId ID достижения
   */
  getAchievementHistory: async (achievementId: string) => {
    try {
      // В реальном приложении здесь будет запрос к API
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            firstUnlockedBy: 'Игрок123',
            totalUnlocks: 1243,
            rarity: 'Редкое (8% игроков)',
            relatedAchievements: ['first_win', 'win_streak_3']
          });
        }, 600);
      });
    } catch (error) {
      console.error('Ошибка при получении истории достижения:', error);
      throw error;
    }
  },

  /**
   * Отправить аналитику о просмотре достижения
   * @param userId ID пользователя
   * @param achievementId ID достижения
   */
  trackAchievementView: async (userId: string, achievementId: string): Promise<void> => {
    try {
      // В реальном приложении здесь будет запрос к API аналитики
      console.log(`Просмотр достижения: ${achievementId} пользователем ${userId}`);
    } catch (error) {
      console.error('Ошибка при отправке аналитики:', error);
      // Не выбрасываем ошибку, чтобы не прерывать пользовательский опыт
    }
  }
};