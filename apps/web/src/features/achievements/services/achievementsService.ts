import { Achievement, UserAchievements } from '../model/types';
import { achievementsApi } from '../api/achievementsApi';
import { achievementData } from '../model/achievementData';

/**
 * Сервис для работы с достижениями
 */
export const achievementsService = {
  /**
   * Получить достижения пользователя
   * @param userId ID пользователя
   */
  getUserAchievements: async (userId: string): Promise<UserAchievements> => {
    try {
      return await achievementsApi.getUserAchievements(userId);
    } catch (error) {
      console.error('Ошибка в сервисе достижений:', error);
      // Возвращаем пустые данные в случае ошибки, чтобы приложение не сломалось
      return {
        userId,
        unlockedAchievements: [],
        progress: {},
        unlockedDates: {}
      };
    }
  },

  /**
   * Получить последние разблокированные достижения
   * @param userAchievements Достижения пользователя
   * @param count Количество достижений для возврата
   */
  getRecentAchievements: (userAchievements: UserAchievements, count: number = 3): Achievement[] => {
    const { unlockedAchievements, unlockedDates } = userAchievements;
    
    // Получаем разблокированные достижения с датами
    const achievementsWithDates = unlockedAchievements
      .map(id => {
        const achievement = achievementData.find(a => a.id === id);
        if (!achievement) return null;
        
        return {
          achievement,
          date: unlockedDates[id] ? new Date(unlockedDates[id]) : new Date(0)
        };
      })
      .filter(item => item !== null) as { achievement: Achievement; date: Date }[];
    
    // Сортируем по дате (от новых к старым)
    achievementsWithDates.sort((a, b) => b.date.getTime() - a.date.getTime());
    
    // Возвращаем только достижения
    return achievementsWithDates
      .slice(0, count)
      .map(item => item.achievement);
  },

  /**
   * Получить следующие достижения для разблокировки
   * @param userAchievements Достижения пользователя
   * @param count Количество достижений для возврата
   */
  getNextAchievementsToUnlock: (userAchievements: UserAchievements, count: number = 3): Achievement[] => {
    const { unlockedAchievements, progress } = userAchievements;
    
    // Получаем все незаблокированные достижения
    const lockedAchievements = achievementData.filter(
      achievement => !unlockedAchievements.includes(achievement.id) && !achievement.hidden
    );
    
    // Находим достижения с наибольшим прогрессом
    const achievementsWithProgress = lockedAchievements
      .filter(achievement => achievement.progressBased && achievement.targetProgress)
      .map(achievement => {
        const currentProgress = progress[achievement.id] || 0;
        const targetProgress = achievement.targetProgress || 1;
        const completionRate = currentProgress / targetProgress;
        
        return {
          achievement: {
            ...achievement,
            currentProgress
          },
          completionRate
        };
      })
      .sort((a, b) => b.completionRate - a.completionRate)
      .slice(0, count);
    
    // Если у нас недостаточно достижений с прогрессом, добавляем легкие достижения
    if (achievementsWithProgress.length < count) {
      const easyAchievements = lockedAchievements
        .filter(achievement => 
          achievement.difficulty === 'easy' && 
          !achievementsWithProgress.some(a => a.achievement.id === achievement.id)
        )
        .slice(0, count - achievementsWithProgress.length)
        .map(achievement => ({
          achievement,
          completionRate: 0
        }));
      
      return [...achievementsWithProgress, ...easyAchievements].map(item => item.achievement);
    }
    
    return achievementsWithProgress.map(item => item.achievement);
  },

  /**
   * Получить историю достижения
   * @param achievementId ID достижения
   */
  getAchievementHistory: async (achievementId: string) => {
    try {
      return await achievementsApi.getAchievementHistory(achievementId);
    } catch (error) {
      console.error('Ошибка при получении истории достижения:', error);
      // Возвращаем заглушку в случае ошибки
      return {
        firstUnlockedBy: 'Неизвестно',
        totalUnlocks: 0,
        rarity: 'Неизвестно',
        relatedAchievements: []
      };
    }
  },

  /**
   * Отправить аналитику о просмотре достижения
   * @param userId ID пользователя
   * @param achievementId ID достижения
   */
  trackAchievementView: (userId: string, achievementId: string): void => {
    achievementsApi.trackAchievementView(userId, achievementId)
      .catch(error => console.error('Ошибка при отправке аналитики:', error));
  },

  /**
   * Получить персонализированные рекомендации по достижению
   * @param achievement Достижение
   * @param userAchievements Достижения пользователя
   */
  getAchievementRecommendations: (achievement: Achievement, userAchievements: UserAchievements): string => {
    // Получаем текущий прогресс
    const progress = userAchievements.progress[achievement.id] || 0;
    
    // Проверяем, разблокировано ли уже достижение
    if (userAchievements.unlockedAchievements.includes(achievement.id)) {
      return 'Достижение уже разблокировано! Попробуйте получить другие достижения.';
    }
    
    // В зависимости от типа достижения возвращаем персонализированные рекомендации
    if (achievement.progressBased && achievement.targetProgress) {
      const remaining = achievement.targetProgress - progress;
      const percentComplete = Math.floor((progress / achievement.targetProgress) * 100);
      
      return `Прогресс: ${percentComplete}%. Осталось: ${remaining}. Продолжайте играть, чтобы разблокировать это достижение.`;
    }
    
    // Рекомендации в зависимости от категории
    if (achievement.category) {
      switch (achievement.category) {
        case 'beginner':
          return 'Это базовое достижение, которое легко получить. Продолжайте играть!';
        
        case 'gameplay':
          return 'Сосредоточьтесь на игровом процессе и стратегии, чтобы получить это достижение.';
        
        case 'mastery':
          return 'Улучшайте свои навыки игры, чтобы получить это достижение.';
        
        case 'social':
          return 'Взаимодействуйте с другими игроками, приглашайте друзей и участвуйте в командных играх.';
        
        case 'collection':
          return 'Соберите больше карт и разблокируйте новые колоды.';
        
        case 'special':
          return 'Это особое достижение с уникальными условиями получения. Исследуйте игру!';
      }
    }
    
    // Общая рекомендация, если ничего не подошло
    return 'Исследуйте различные аспекты игры, чтобы разблокировать это достижение.';
  }
};