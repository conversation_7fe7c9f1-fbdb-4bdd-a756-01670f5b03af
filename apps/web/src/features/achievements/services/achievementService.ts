import { Achievement, AchievementEvent, UserAchievements } from '../model/types';
import { achievementData, getAchievementById } from '../model/achievementData';
import { checkAchievementUnlock, trackAchievementProgress } from '../utils/achievementUtils';

/**
 * Сервис для работы с достижениями
 * Отвечает за отслеживание прогресса, сохранение и загрузку данных о достижениях
 */
export class AchievementService {
  private static instance: AchievementService;
  private userAchievements: UserAchievements | null = null;
  private eventListeners: ((achievementId: string) => void)[] = [];
  private progressListeners: ((achievementId: string, progress: number) => void)[] = [];

  // Приватный конструктор для реализации паттерна Singleton
  private constructor() {}

  /**
   * Получение экземпляра сервиса (Singleton)
   */
  public static getInstance(): AchievementService {
    if (!AchievementService.instance) {
      AchievementService.instance = new AchievementService();
    }
    return AchievementService.instance;
  }

  /**
   * Инициализация сервиса для конкретного пользователя
   * @param userId ID пользователя
   */
  public async initialize(userId: string): Promise<void> {
    // Загружаем данные о достижениях пользователя
    this.userAchievements = await this.loadUserAchievements(userId);
    
    // Регистрируем обработчик событий
    this.registerEventHandler();
  }

  /**
   * Загрузка данных о достижениях пользователя
   * @param userId ID пользователя
   */
  private async loadUserAchievements(userId: string): Promise<UserAchievements> {
    try {
      // В реальном приложении здесь будет запрос к API
      // Для демонстрации используем localStorage
      const savedData = localStorage.getItem(`achievements_${userId}`);
      
      if (savedData) {
        return JSON.parse(savedData) as UserAchievements;
      }
      
      // Если данных нет, создаем новый объект
      return {
        userId,
        unlockedAchievements: [],
        progress: {},
        unlockedDates: {}
      };
    } catch (error) {
      console.error('Ошибка при загрузке достижений:', error);
      // Возвращаем пустой объект в случае ошибки
      return {
        userId,
        unlockedAchievements: [],
        progress: {},
        unlockedDates: {}
      };
    }
  }

  /**
   * Сохранение данных о достижениях пользователя
   */
  private async saveUserAchievements(): Promise<void> {
    if (!this.userAchievements) return;
    
    try {
      // В реальном приложении здесь будет запрос к API
      // Для демонстрации используем localStorage
      localStorage.setItem(
        `achievements_${this.userAchievements.userId}`,
        JSON.stringify(this.userAchievements)
      );
    } catch (error) {
      console.error('Ошибка при сохранении достижений:', error);
    }
  }

  /**
   * Регистрация обработчика событий для отслеживания игровых событий
   */
  private registerEventHandler(): void {
    // Функция для обработки пользовательских событий
    const eventHandler = (e: CustomEvent<AchievementEvent>) => {
      this.handleGameEvent(e.detail);
    };

    // Регистрируем обработчик
    window.addEventListener('achievement-event', eventHandler as EventListener);
  }

  /**
   * Обработка игрового события
   * @param event Игровое событие
   */
  public handleGameEvent(event: AchievementEvent): void {
    if (!this.userAchievements) return;
    
    // Проверяем, разблокировано ли новое достижение
    const unlockedAchievements = checkAchievementUnlock(this.userAchievements, event);
    
    // Если есть новые разблокированные достижения
    if (unlockedAchievements.length > 0) {
      // Обновляем данные пользователя
      unlockedAchievements.forEach(achievementId => {
        if (!this.userAchievements?.unlockedAchievements.includes(achievementId)) {
          // Добавляем достижение в список разблокированных
          this.userAchievements?.unlockedAchievements.push(achievementId);
          
          // Сохраняем дату разблокировки
          if (this.userAchievements?.unlockedDates) {
            this.userAchievements.unlockedDates[achievementId] = new Date().toISOString();
          }
          
          // Уведомляем слушателей о разблокировке достижения
          this.notifyAchievementUnlocked(achievementId);
        }
      });
      
      // Сохраняем обновленные данные
      this.saveUserAchievements();
    }
    
    // Обновляем прогресс достижений
    const updatedProgress = trackAchievementProgress(this.userAchievements, event);
    
    // Если есть обновления прогресса
    if (Object.keys(updatedProgress).length > 0) {
      // Обновляем данные пользователя
      Object.entries(updatedProgress).forEach(([achievementId, progress]) => {
        if (this.userAchievements?.progress) {
          this.userAchievements.progress[achievementId] = progress;
        }
        
        // Уведомляем слушателей об обновлении прогресса
        this.notifyProgressUpdated(achievementId, progress);
        
        // Проверяем, достигнут ли целевой прогресс
        const achievement = getAchievementById(achievementId);
        if (achievement?.progressBased && achievement.targetProgress && progress >= achievement.targetProgress) {
          // Если достижение еще не разблокировано
          if (!this.userAchievements?.unlockedAchievements.includes(achievementId)) {
            // Добавляем достижение в список разблокированных
            this.userAchievements?.unlockedAchievements.push(achievementId);
            
            // Сохраняем дату разблокировки
            if (this.userAchievements?.unlockedDates) {
              this.userAchievements.unlockedDates[achievementId] = new Date().toISOString();
            }
            
            // Уведомляем слушателей о разблокировке достижения
            this.notifyAchievementUnlocked(achievementId);
          }
        }
      });
      
      // Сохраняем обновленные данные
      this.saveUserAchievements();
    }
  }

  /**
   * Получение данных о достижениях пользователя
   */
  public getUserAchievements(): UserAchievements | null {
    return this.userAchievements;
  }

  /**
   * Добавление слушателя разблокировки достижений
   * @param listener Функция-слушатель
   */
  public addAchievementUnlockedListener(listener: (achievementId: string) => void): void {
    this.eventListeners.push(listener);
  }

  /**
   * Удаление слушателя разблокировки достижений
   * @param listener Функция-слушатель
   */
  public removeAchievementUnlockedListener(listener: (achievementId: string) => void): void {
    this.eventListeners = this.eventListeners.filter(l => l !== listener);
  }

  /**
   * Уведомление слушателей о разблокировке достижения
   * @param achievementId ID разблокированного достижения
   */
  private notifyAchievementUnlocked(achievementId: string): void {
    this.eventListeners.forEach(listener => listener(achievementId));
  }

  /**
   * Добавление слушателя обновления прогресса достижений
   * @param listener Функция-слушатель
   */
  public addProgressUpdatedListener(listener: (achievementId: string, progress: number) => void): void {
    this.progressListeners.push(listener);
  }

  /**
   * Удаление слушателя обновления прогресса достижений
   * @param listener Функция-слушатель
   */
  public removeProgressUpdatedListener(listener: (achievementId: string, progress: number) => void): void {
    this.progressListeners = this.progressListeners.filter(l => l !== listener);
  }

  /**
   * Уведомление слушателей об обновлении прогресса достижения
   * @param achievementId ID достижения
   * @param progress Новое значение прогресса
   */
  private notifyProgressUpdated(achievementId: string, progress: number): void {
    this.progressListeners.forEach(listener => listener(achievementId, progress));
  }

  /**
   * Вспомогательная функция для отправки игрового события
   * @param eventData Данные события
   */
  public dispatchGameEvent(eventData: AchievementEvent): void {
    const event = new CustomEvent('achievement-event', { detail: eventData });
    window.dispatchEvent(event);
  }
}

// Экспортируем экземпляр сервиса для удобного использования
export const achievementService = AchievementService.getInstance();