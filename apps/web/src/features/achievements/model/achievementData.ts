import { Achievement, AchievementCategory, AchievementDifficulty } from './types';

/**
 * Данные о достижениях в игре "Козырь Мастер"
 * Расширенная версия с дополнительными достижениями для повышения вовлеченности игроков
 */
export const achievementData: Achievement[] = [
  // Достижения для начинающих
  {
    id: 'first_game',
    name: 'Первый шаг',
    description: 'Сыграйте свою первую игру',
    icon: '🎮',
    iconBgColor: '#4CAF50',
    category: AchievementCategory.BEGINNER,
    difficulty: AchievementDifficulty.EASY
  },
  {
    id: 'first_win',
    name: 'Первая победа',
    description: 'Выиграйте свою первую игру',
    icon: '🏆',
    iconBgColor: '#FFC107',
    category: AchievementCategory.BEGINNER,
    difficulty: AchievementDifficulty.EASY
  },
  {
    id: 'tutorial_complete',
    name: 'Ученик',
    description: 'Пройдите все обучающие туториалы',
    icon: '📚',
    iconBgColor: '#2196F3',
    category: AchievementCategory.BEGINNER,
    difficulty: AchievementDifficulty.EASY,
    progressBased: true,
    currentProgress: 0,
    targetProgress: 3 // Количество туториалов
  },
  {
    id: 'first_friend',
    name: 'Социализация',
    description: 'Добавьте первого друга',
    icon: '👋',
    iconBgColor: '#00BCD4',
    category: AchievementCategory.BEGINNER,
    difficulty: AchievementDifficulty.EASY
  },
  {
    id: 'first_chat',
    name: 'Общительный',
    description: 'Отправьте первое сообщение в чате',
    icon: '💬',
    iconBgColor: '#26A69A',
    category: AchievementCategory.BEGINNER,
    difficulty: AchievementDifficulty.EASY
  },
  {
    id: 'profile_complete',
    name: 'Личность',
    description: 'Заполните все поля профиля',
    icon: '👤',
    iconBgColor: '#7E57C2',
    category: AchievementCategory.BEGINNER,
    difficulty: AchievementDifficulty.EASY,
    progressBased: true,
    currentProgress: 0,
    targetProgress: 5 // Количество полей профиля
  },
  
  // Достижения игрового процесса
  {
    id: 'win_streak_3',
    name: 'Полоса удачи',
    description: 'Выиграйте 3 игры подряд',
    icon: '🔥',
    iconBgColor: '#FF5722',
    category: AchievementCategory.GAMEPLAY,
    difficulty: AchievementDifficulty.MEDIUM
  },
  {
    id: 'win_streak_5',
    name: 'Непобедимый',
    description: 'Выиграйте 5 игр подряд',
    icon: '⚡',
    iconBgColor: '#FF9800',
    category: AchievementCategory.GAMEPLAY,
    difficulty: AchievementDifficulty.HARD
  },
  {
    id: 'win_streak_10',
    name: 'Легенда',
    description: 'Выиграйте 10 игр подряд',
    icon: '👑',
    iconBgColor: '#E91E63',
    category: AchievementCategory.GAMEPLAY,
    difficulty: AchievementDifficulty.VERY_HARD
  },
  {
    id: 'games_played_10',
    name: 'Любитель',
    description: 'Сыграйте 10 игр',
    icon: '🃏',
    iconBgColor: '#9C27B0',
    category: AchievementCategory.GAMEPLAY,
    difficulty: AchievementDifficulty.EASY,
    progressBased: true,
    currentProgress: 0,
    targetProgress: 10
  },
  {
    id: 'games_played_50',
    name: 'Энтузиаст',
    description: 'Сыграйте 50 игр',
    icon: '♠️',
    iconBgColor: '#673AB7',
    category: AchievementCategory.GAMEPLAY,
    difficulty: AchievementDifficulty.MEDIUM,
    progressBased: true,
    currentProgress: 0,
    targetProgress: 50
  },
  {
    id: 'games_played_100',
    name: 'Профессионал',
    description: 'Сыграйте 100 игр',
    icon: '♥️',
    iconBgColor: '#3F51B5',
    category: AchievementCategory.GAMEPLAY,
    difficulty: AchievementDifficulty.HARD,
    progressBased: true,
    currentProgress: 0,
    targetProgress: 100
  },
  
  // Достижения мастерства
  {
    id: 'perfect_defense',
    name: 'Идеальная защита',
    description: 'Отбейтесь от 6 карт за один ход',
    icon: '🛡️',
    iconBgColor: '#607D8B',
    category: AchievementCategory.MASTERY,
    difficulty: AchievementDifficulty.HARD,
    gameType: 'durak_podkidnoy'
  },
  {
    id: 'transfer_master',
    name: 'Мастер перевода',
    description: 'Переведите атаку 3 раза за одну игру',
    icon: '↪️',
    iconBgColor: '#795548',
    category: AchievementCategory.MASTERY,
    difficulty: AchievementDifficulty.MEDIUM,
    gameType: 'durak_perevodnoy',
    progressBased: true,
    currentProgress: 0,
    targetProgress: 3
  },
  {
    id: 'trump_master',
    name: 'Козырный туз',
    description: 'Выиграйте игру, используя все козырные карты',
    icon: '♦️',
    iconBgColor: '#F44336',
    category: AchievementCategory.MASTERY,
    difficulty: AchievementDifficulty.HARD
  },
  {
    id: 'quick_win',
    name: 'Молниеносная победа',
    description: 'Выиграйте игру за менее чем 2 минуты',
    icon: '⚡',
    iconBgColor: '#FFEB3B',
    category: AchievementCategory.MASTERY,
    difficulty: AchievementDifficulty.MEDIUM
  },
  {
    id: 'no_take_win',
    name: 'Чистая победа',
    description: 'Выиграйте игру, ни разу не взяв карты',
    icon: '✨',
    iconBgColor: '#FFA000',
    category: AchievementCategory.MASTERY,
    difficulty: AchievementDifficulty.VERY_HARD,
    hidden: true,
    reward: {
      type: 'badge',
      value: 'clean_master',
      name: 'Значок чистой игры'
    }
  },
  {
    id: 'last_card_win',
    name: 'На грани',
    description: 'Выиграйте игру, когда в колоде не осталось карт',
    icon: '🃏',
    iconBgColor: '#D32F2F',
    category: AchievementCategory.MASTERY,
    difficulty: AchievementDifficulty.HARD
  },
  {
    id: 'bluff_master',
    name: 'Мастер блефа',
    description: 'Заставьте соперника взять карты, когда у вас не было подходящих',
    icon: '🎭',
    iconBgColor: '#7B1FA2',
    category: AchievementCategory.MASTERY,
    difficulty: AchievementDifficulty.HARD,
    gameType: 'durak_podkidnoy'
  },
  
  // Социальные достижения
  {
    id: 'friend_game',
    name: 'Дружеская игра',
    description: 'Сыграйте игру с другом',
    icon: '👥',
    iconBgColor: '#00BCD4',
    category: AchievementCategory.SOCIAL,
    difficulty: AchievementDifficulty.EASY
  },
  {
    id: 'tournament_participant',
    name: 'Участник турнира',
    description: 'Примите участие в турнире',
    icon: '🏅',
    iconBgColor: '#CDDC39',
    category: AchievementCategory.SOCIAL,
    difficulty: AchievementDifficulty.MEDIUM
  },
  {
    id: 'tournament_winner',
    name: 'Чемпион',
    description: 'Выиграйте турнир',
    icon: '🏆',
    iconBgColor: '#FFD700',
    category: AchievementCategory.SOCIAL,
    difficulty: AchievementDifficulty.VERY_HARD,
    reward: {
      type: 'avatar_frame',
      value: 'champion_frame',
      name: 'Чемпионская рамка'
    }
  },
  {
    id: 'friends_10',
    name: 'Популярный',
    description: 'Добавьте 10 друзей',
    icon: '🌟',
    iconBgColor: '#1976D2',
    category: AchievementCategory.SOCIAL,
    difficulty: AchievementDifficulty.MEDIUM,
    progressBased: true,
    currentProgress: 0,
    targetProgress: 10
  },
  {
    id: 'club_creator',
    name: 'Основатель',
    description: 'Создайте клуб игроков',
    icon: '🏛️',
    iconBgColor: '#5D4037',
    category: AchievementCategory.SOCIAL,
    difficulty: AchievementDifficulty.MEDIUM,
    reward: {
      type: 'title',
      value: 'club_founder',
      name: 'Основатель клуба'
    }
  },
  {
    id: 'club_champion',
    name: 'Гордость клуба',
    description: 'Выиграйте турнир от имени своего клуба',
    icon: '🏰',
    iconBgColor: '#880E4F',
    category: AchievementCategory.SOCIAL,
    difficulty: AchievementDifficulty.VERY_HARD,
    reward: {
      type: 'badge',
      value: 'club_champion',
      name: 'Значок чемпиона клуба'
    }
  },
  {
    id: 'emote_master',
    name: 'Эмоциональный',
    description: 'Используйте все эмоции во время игры',
    icon: '😎',
    iconBgColor: '#FF6D00',
    category: AchievementCategory.SOCIAL,
    difficulty: AchievementDifficulty.EASY,
    progressBased: true,
    currentProgress: 0,
    targetProgress: 8 // Количество доступных эмоций
  },
  
  // Коллекционные достижения
  {
    id: 'all_games',
    name: 'Коллекционер',
    description: 'Сыграйте во все доступные игры',
    icon: '🎴',
    iconBgColor: '#009688',
    category: AchievementCategory.COLLECTION,
    difficulty: AchievementDifficulty.MEDIUM,
    progressBased: true,
    currentProgress: 0,
    targetProgress: 3 // Количество доступных игр
  },
  {
    id: 'card_collector',
    name: 'Карточный коллекционер',
    description: 'Откройте все колоды карт',
    icon: '🎭',
    iconBgColor: '#00796B',
    category: AchievementCategory.COLLECTION,
    difficulty: AchievementDifficulty.HARD,
    progressBased: true,
    currentProgress: 0,
    targetProgress: 5, // Количество колод
    reward: {
      type: 'in_game_currency',
      value: 500,
      name: 'Игровая валюта'
    }
  },
  {
    id: 'avatar_collector',
    name: 'Модник',
    description: 'Соберите 10 аватаров',
    icon: '👔',
    iconBgColor: '#0097A7',
    category: AchievementCategory.COLLECTION,
    difficulty: AchievementDifficulty.MEDIUM,
    progressBased: true,
    currentProgress: 0,
    targetProgress: 10
  },
  {
    id: 'table_collector',
    name: 'Дизайнер',
    description: 'Откройте все игровые столы',
    icon: '🎨',
    iconBgColor: '#00838F',
    category: AchievementCategory.COLLECTION,
    difficulty: AchievementDifficulty.MEDIUM,
    progressBased: true,
    currentProgress: 0,
    targetProgress: 3 // Количество игровых столов
  },
  
  // Особые достижения
  {
    id: 'comeback',
    name: 'Феникс',
    description: 'Выиграйте игру, имея только одну карту против 5+ карт у соперника',
    icon: '🔄',
    iconBgColor: '#8BC34A',
    category: AchievementCategory.SPECIAL,
    difficulty: AchievementDifficulty.VERY_HARD,
    hidden: true
  },
  {
    id: 'daily_player',
    name: 'Постоянный игрок',
    description: 'Играйте в игру 7 дней подряд',
    icon: '📅',
    iconBgColor: '#03A9F4',
    category: AchievementCategory.SPECIAL,
    difficulty: AchievementDifficulty.MEDIUM,
    progressBased: true,
    currentProgress: 0,
    targetProgress: 7
  },
  {
    id: 'monthly_player',
    name: 'Преданный игрок',
    description: 'Играйте в игру 30 дней подряд',
    icon: '📆',
    iconBgColor: '#0288D1',
    category: AchievementCategory.SPECIAL,
    difficulty: AchievementDifficulty.HARD,
    progressBased: true,
    currentProgress: 0,
    targetProgress: 30,
    reward: {
      type: 'avatar_frame',
      value: 'loyal_frame',
      name: 'Рамка преданного игрока'
    }
  },
  {
    id: 'night_owl',
    name: 'Ночная сова',
    description: 'Сыграйте 10 игр между полуночью и 5 утра',
    icon: '🦉',
    iconBgColor: '#311B92',
    category: AchievementCategory.SPECIAL,
    difficulty: AchievementDifficulty.MEDIUM,
    progressBased: true,
    currentProgress: 0,
    targetProgress: 10,
    hidden: true
  },
  {
    id: 'seasonal_champion',
    name: 'Сезонный чемпион',
    description: 'Займите первое место в сезонном рейтинге',
    icon: '🏅',
    iconBgColor: '#C62828',
    category: AchievementCategory.SPECIAL,
    difficulty: AchievementDifficulty.VERY_HARD,
    reward: {
      type: 'title',
      value: 'seasonal_champion',
      name: 'Сезонный чемпион'
    }
  },
  {
    id: 'beta_tester',
    name: 'Бета-тестер',
    description: 'Участвуйте в бета-тестировании новых функций',
    icon: '🔍',
    iconBgColor: '#455A64',
    category: AchievementCategory.SPECIAL,
    difficulty: AchievementDifficulty.MEDIUM,
    hidden: true
  }
];

/**
 * Функция для получения достижений по категории
 */
export const getAchievementsByCategory = (category: AchievementCategory): Achievement[] => {
  return achievementData.filter(achievement => achievement.category === category);
};

/**
 * Функция для получения достижений по типу игры
 */
export const getAchievementsByGameType = (gameType?: string): Achievement[] => {
  if (!gameType) {
    return achievementData.filter(achievement => !achievement.gameType);
  }
  
  return achievementData.filter(
    achievement => !achievement.gameType || achievement.gameType === gameType
  );
};

/**
 * Функция для получения достижения по ID
 */
export const getAchievementById = (achievementId: string): Achievement | undefined => {
  return achievementData.find(achievement => achievement.id === achievementId);
};

/**
 * Функция для обновления прогресса достижения
 */
export const updateAchievementProgress = (
  achievements: Achievement[],
  achievementId: string,
  progress: number
): Achievement[] => {
  return achievements.map(achievement => {
    if (achievement.id === achievementId && achievement.progressBased) {
      return {
        ...achievement,
        currentProgress: Math.min(progress, achievement.targetProgress || 0)
      };
    }
    return achievement;
  });
};

/**
 * Функция для получения достижений по сложности
 */
export const getAchievementsByDifficulty = (difficulty: AchievementDifficulty): Achievement[] => {
  return achievementData.filter(achievement => achievement.difficulty === difficulty);
};

/**
 * Функция для получения достижений с наградами
 */
export const getAchievementsWithRewards = (): Achievement[] => {
  return achievementData.filter(achievement => achievement.reward !== undefined);
};

/**
 * Функция для получения скрытых достижений
 */
export const getHiddenAchievements = (): Achievement[] => {
  return achievementData.filter(achievement => achievement.hidden === true);
};

/**
 * Функция для проверки выполнения всех достижений в категории
 */
export const checkCategoryCompletion = (
  userAchievements: string[],
  category: AchievementCategory
): boolean => {
  const categoryAchievements = getAchievementsByCategory(category);
  return categoryAchievements.every(achievement => userAchievements.includes(achievement.id));
};