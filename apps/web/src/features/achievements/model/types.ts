/**
 * Типы для системы достижений
 */

// Тип для достижения
export interface Achievement {
  id: string;           // Уникальный идентификатор достижения
  name: string;         // Название достижения
  description: string;  // Описание достижения
  icon: string;         // Иконка достижения (эмодзи или символ)
  iconBgColor: string;  // Цвет фона для иконки
  category: AchievementCategory; // Категория достижения
  difficulty: AchievementDifficulty; // Сложность достижения
  gameType?: string;    // Тип игры, к которой относится достижение (если применимо)
  hidden?: boolean;     // Скрытое ли достижение до разблокировки
  progressBased?: boolean; // Основано ли достижение на прогрессе
  currentProgress?: number; // Текущий прогресс (для достижений на основе прогресса)
  targetProgress?: number;  // Целевой прогресс (для достижений на основе прогресса)
  unlockedDate?: string;    // Дата разблокировки достижения
  reward?: AchievementReward; // Награда за достижение
}

// Категории достижений
export enum AchievementCategory {
  BEGINNER = 'beginner',     // Достижения для начинающих
  GAMEPLAY = 'gameplay',     // Игровой процесс
  MASTERY = 'mastery',       // Мастерство
  SOCIAL = 'social',         // Социальные достижения
  COLLECTION = 'collection', // Коллекционирование
  SPECIAL = 'special'        // Особые достижения
}

// Сложность достижений
export enum AchievementDifficulty {
  EASY = 'easy',         // Легкие достижения
  MEDIUM = 'medium',     // Средние достижения
  HARD = 'hard',         // Сложные достижения
  VERY_HARD = 'very_hard' // Очень сложные достижения
}

// Тип награды за достижение
export interface AchievementReward {
  type: 'badge' | 'avatar_frame' | 'title' | 'in_game_currency'; // Тип награды
  value: string | number; // Значение награды (идентификатор или количество)
  name: string;          // Название награды
  description?: string;  // Описание награды
}

// Тип для события, которое может привести к разблокировке достижения
export interface AchievementEvent {
  type: string;          // Тип события
  gameType?: string;     // Тип игры, в которой произошло событие
  payload?: any;         // Дополнительные данные события
}

// Тип для хранения прогресса достижений пользователя
export interface UserAchievements {
  userId: string;                // ID пользователя
  unlockedAchievements: string[]; // Список ID разблокированных достижений
  progress: {                    // Прогресс по достижениям
    [achievementId: string]: number; // ID достижения -> текущий прогресс
  };
  unlockedDates: {              // Даты разблокировки достижений
    [achievementId: string]: string; // ID достижения -> дата разблокировки
  };
}