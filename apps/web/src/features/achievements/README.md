# Система достижений для проекта "Козырь Мастер"

## Обзор

Система достижений предназначена для повышения вовлеченности пользователей в игровой процесс, предоставляя им возможность получать награды за различные действия и достижения в игре. Система включает в себя компоненты для отображения достижений, отслеживания прогресса, уведомления о новых достижениях и интеграции с основным приложением.

## Структура системы

### Компоненты

1. **AchievementCard** - карточка для отображения информации о достижении
2. **AchievementsList** - список достижений с возможностью фильтрации
3. **AchievementDetails** - детальная информация о достижении
4. **AchievementNotification** - уведомление о получении нового достижения
5. **AchievementNotificationManager** - управление уведомлениями о достижениях
6. **AchievementTracker** - отслеживание игровых событий для автоматического присуждения достижений

### Страницы

1. **AchievementsPage** - страница со списком всех достижений
2. **UserProfilePage** - страница профиля пользователя с отображением прогресса и разблокированных достижений

### Сервисы

1. **AchievementService** - сервис для работы с достижениями (отслеживание прогресса, сохранение и загрузка данных)

### Провайдеры

1. **AchievementProvider** - провайдер контекста для доступа к данным о достижениях из любого компонента

## Интеграция с приложением

### Шаг 1: Добавление провайдера

Оберните ваше приложение в `AchievementProvider` для доступа к системе достижений:

```tsx
import { AchievementProvider } from '@/features/achievements';

const App = () => {
  return (
    <AchievementProvider userId="user123">
      {/* Ваше приложение */}
    </AchievementProvider>
  );
};
```

### Шаг 2: Добавление трекера событий

Добавьте компонент `AchievementTracker` в игровые компоненты для отслеживания событий:

```tsx
import { AchievementTracker } from '@/features/achievements';

const GameComponent = () => {
  return (
    <AchievementTracker gameType="durak">
      {/* Игровой компонент */}
    </AchievementTracker>
  );
};
```

### Шаг 3: Отправка игровых событий

Используйте функции трекера для отправки событий:

```tsx
import { trackGameStart, trackGameEnd, trackSpecialMove } from '@/features/achievements';

// При начале игры
trackGameStart('durak');

// При завершении игры
trackGameEnd('durak', true); // true - победа, false - поражение

// При использовании специальной карты или комбинации
trackSpecialMove('durak', 'trump_card', { cardValue: 'ace', suit: 'spades' });
```

### Шаг 4: Добавление страницы достижений

Добавьте страницу достижений в ваше приложение:

```tsx
import { AchievementsPage } from '@/features/achievements';

// В вашем роутере
<Route path="/achievements" element={<AchievementsPage />} />
```

### Шаг 5: Добавление страницы профиля

Добавьте страницу профиля пользователя с отображением достижений:

```tsx
import { UserProfilePage } from '@/features/achievements';

// В вашем роутере
<Route path="/profile/:userId" element={<UserProfilePage userId={userId} userName={userName} />} />
```

## Типы событий

Система достижений отслеживает следующие типы событий:

1. `game_started` - начало игры
2. `game_completed` - завершение игры
3. `game_won` - победа в игре
4. `game_lost` - поражение в игре
5. `game_move` - ход в игре
6. `special_move` - использование специальной карты или комбинации
7. `social_interaction` - социальное взаимодействие (добавление друга, отправка сообщения и т.д.)

## Пример использования контекста

Вы можете использовать хук `useAchievements` для доступа к данным о достижениях из любого компонента:

```tsx
import { useAchievements } from '@/features/achievements';

const MyComponent = () => {
  const { userAchievements, isLoading, dispatchGameEvent } = useAchievements();
  
  if (isLoading) {
    return <div>Загрузка...</div>;
  }
  
  return (
    <div>
      <h2>Разблокированные достижения: {userAchievements?.unlockedAchievements.length}</h2>
      {/* Остальной код */}
    </div>
  );
};
```

## Настройка достижений

Достижения настраиваются в файле `model/achievementData.ts`. Вы можете добавлять новые достижения, изменять существующие или удалять ненужные.

## Расширение системы

Система достижений легко расширяется. Вы можете добавить новые типы событий, новые достижения или новые компоненты для отображения достижений.

## Заключение

Система достижений является важной частью проекта "Козырь Мастер" и помогает повысить вовлеченность пользователей в игровой процесс. Она предоставляет гибкие возможности для настройки и расширения, а также легко интегрируется с основным приложением.