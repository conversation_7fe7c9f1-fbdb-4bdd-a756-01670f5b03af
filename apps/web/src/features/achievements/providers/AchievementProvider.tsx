import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { UserAchievements, AchievementEvent } from '../model/types';
import { achievementService } from '../services/achievementService';
import { AchievementNotificationManager } from '../components/AchievementNotificationManager';

// Создаем контекст для достижений
interface AchievementContextType {
  userAchievements: UserAchievements | null;
  isLoading: boolean;
  dispatchGameEvent: (event: AchievementEvent) => void;
}

const AchievementContext = createContext<AchievementContextType>({
  userAchievements: null,
  isLoading: true,
  dispatchGameEvent: () => {}
});

// Хук для использования контекста достижений
export const useAchievements = () => useContext(AchievementContext);

interface AchievementProviderProps {
  userId: string;
  children: ReactNode;
}

/**
 * Провайдер для системы достижений
 * Инициализирует сервис достижений и предоставляет доступ к данным через контекст
 */
export const AchievementProvider: React.FC<AchievementProviderProps> = ({ userId, children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [userAchievements, setUserAchievements] = useState<UserAchievements | null>(null);

  // Инициализация сервиса достижений
  useEffect(() => {
    const initializeAchievements = async () => {
      setIsLoading(true);
      
      try {
        // Инициализируем сервис для текущего пользователя
        await achievementService.initialize(userId);
        
        // Получаем данные о достижениях
        const achievements = achievementService.getUserAchievements();
        setUserAchievements(achievements);
      } catch (error) {
        console.error('Ошибка при инициализации системы достижений:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    initializeAchievements();
  }, [userId]);

  // Обработчик разблокировки достижений
  useEffect(() => {
    const handleAchievementUnlocked = (achievementId: string) => {
      // Обновляем состояние при разблокировке достижения
      setUserAchievements(prev => {
        if (!prev) return prev;
        
        // Обновляем состояние
        const updatedState = {
          ...prev,
          unlockedAchievements: [...prev.unlockedAchievements, achievementId],
          unlockedDates: {
            ...prev.unlockedDates,
            [achievementId]: new Date().toISOString()
          }
        };
        
        // Показываем уведомление о новом достижении
        setTimeout(() => showAchievementNotification(achievementId), 100);
        
        return updatedState;
      });
    };
    
    // Обработчик обновления прогресса
    const handleProgressUpdated = (achievementId: string, progress: number) => {
      // Обновляем состояние при изменении прогресса
      setUserAchievements(prev => {
        if (!prev) return prev;
        
        // Получаем информацию о достижении
        const achievement = prev.achievements.find(a => a.id === achievementId);
        
        // Проверяем, достигнут ли порог для разблокировки
        if (achievement && achievement.targetProgress && 
            progress >= achievement.targetProgress && 
            !prev.unlockedAchievements.includes(achievementId)) {
          // Если достижение разблокировано, добавляем его в список разблокированных
          return {
            ...prev,
            progress: {
              ...prev.progress,
              [achievementId]: progress
            },
            unlockedAchievements: [...prev.unlockedAchievements, achievementId],
            unlockedDates: {
              ...prev.unlockedDates,
              [achievementId]: new Date().toISOString()
            }
          };
        }
        
        // Обычное обновление прогресса
        return {
          ...prev,
          progress: {
            ...prev.progress,
            [achievementId]: progress
          }
        };
      });
    };
    
    // Функция для отображения уведомлений о достижениях
    const showAchievementNotification = (achievementId: string) => {
      if (!userAchievements) return;
      
      const achievement = userAchievements.achievements.find(a => a.id === achievementId);
      if (!achievement) return;
      
      // Здесь можно использовать любую библиотеку уведомлений
      // Например, toast из react-toastify или собственный компонент
      const notification = {
        title: 'Новое достижение!',
        message: `Вы получили достижение "${achievement.title}"`,
        type: 'success',
        icon: achievement.icon || '🏆',
        duration: 5000 // 5 секунд
      };
      
      // Добавляем уведомление в очередь
      // Это можно реализовать через состояние или через внешний менеджер уведомлений
      window.dispatchEvent(new CustomEvent('achievement-notification', { 
        detail: notification 
      }));
    };
    
    // Регистрируем обработчики
    achievementService.addAchievementUnlockedListener(handleAchievementUnlocked);
    achievementService.addProgressUpdatedListener(handleProgressUpdated);
    
    // Отписываемся при размонтировании
    return () => {
      achievementService.removeAchievementUnlockedListener(handleAchievementUnlocked);
      achievementService.removeProgressUpdatedListener(handleProgressUpdated);
    };
  }, []);

  // Функция для отправки игрового события
  const dispatchGameEvent = (event: AchievementEvent) => {
    achievementService.dispatchGameEvent(event);
  };

  return (
    <AchievementContext.Provider
      value={{
        userAchievements,
        isLoading,
        dispatchGameEvent
      }}
    >
      {children}
      
      {/* Компонент для отображения уведомлений о достижениях */}
      {userAchievements && (
        <AchievementNotificationManager
          userAchievements={userAchievements}
          onAchievementUnlocked={(achievementId) => {
            // Этот колбэк уже обрабатывается через слушателей сервиса
          }}
          onProgressUpdated={(achievementId, progress) => {
            // Этот колбэк уже обрабатывается через слушателей сервиса
          }}
        />
      )}
    </AchievementContext.Provider>
  );
};