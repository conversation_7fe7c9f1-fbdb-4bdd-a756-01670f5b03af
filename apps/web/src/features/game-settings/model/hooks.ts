/**
 * Хуки для работы с настройками игры
 */
import { useState, useEffect } from 'react';
import { GameProfile, getGameProfile, createCustomProfile } from '@/entities/game/config/profiles';

interface UseGameSettingsOptions {
  defaultProfile?: string;
  storageKey?: string;
  onChange?: (settings: GameProfile) => void;
}

interface UseGameSettingsResult {
  settings: GameProfile;
  profileName: string;
  isCustomProfile: boolean;
  updateSettings: (newSettings: Partial<GameProfile>) => void;
  resetToProfile: (profileName: string) => void;
  saveSettings: () => void;
}

/**
 * Хук для работы с настройками игры
 * 
 * @param options Параметры настроек
 * @returns Методы и состояние для работы с настройками
 */
export const useGameSettings = (options: UseGameSettingsOptions = {}): UseGameSettingsResult => {
  const {
    defaultProfile = 'classic',
    storageKey = 'kozyr-master-game-settings',
    onChange,
  } = options;

  // Загрузка сохраненных настроек из localStorage
  const loadSavedSettings = (): { profileName: string; settings: GameProfile; isCustom: boolean } => {
    try {
      const savedData = localStorage.getItem(storageKey);
      if (savedData) {
        const { profileName, settings, isCustom } = JSON.parse(savedData);
        return { profileName, settings, isCustom };
      }
    } catch (error) {
      console.error('Ошибка при загрузке настроек:', error);
    }
    
    // Возвращаем настройки по умолчанию, если сохраненных нет
    return {
      profileName: defaultProfile,
      settings: getGameProfile(defaultProfile),
      isCustom: false,
    };
  };

  // Инициализация состояния
  const [profileData, setProfileData] = useState(() => loadSavedSettings());
  const { profileName, settings, isCustom: isCustomProfile } = profileData;

  // Обновление настроек
  const updateSettings = (newSettings: Partial<GameProfile>) => {
    setProfileData((prev) => {
      const updatedSettings = {
        ...prev.settings,
        ...newSettings,
        rules: {
          ...prev.settings.rules,
          ...(newSettings.rules || {}),
        },
        ui: {
          ...prev.settings.ui,
          ...(newSettings.ui || {}),
        },
      };

      // Вызываем обработчик изменений, если он предоставлен
      if (onChange) {
        onChange(updatedSettings);
      }

      return {
        profileName: prev.profileName,
        settings: updatedSettings,
        isCustom: true, // При любом изменении настроек считаем их пользовательскими
      };
    });
  };

  // Сброс настроек к выбранному профилю
  const resetToProfile = (newProfileName: string) => {
    const newSettings = getGameProfile(newProfileName);
    setProfileData({
      profileName: newProfileName,
      settings: newSettings,
      isCustom: false,
    });

    // Вызываем обработчик изменений, если он предоставлен
    if (onChange) {
      onChange(newSettings);
    }
  };

  // Сохранение настроек в localStorage
  const saveSettings = () => {
    try {
      localStorage.setItem(
        storageKey,
        JSON.stringify({
          profileName,
          settings,
          isCustom: isCustomProfile,
        })
      );
    } catch (error) {
      console.error('Ошибка при сохранении настроек:', error);
    }
  };

  // Автоматическое сохранение настроек при изменении
  useEffect(() => {
    saveSettings();
  }, [settings, profileName, isCustomProfile]);

  return {
    settings,
    profileName,
    isCustomProfile,
    updateSettings,
    resetToProfile,
    saveSettings,
  };
};

/**
 * Хук для работы с настройками многопользовательского режима
 * 
 * @returns Методы и состояние для работы с настройками многопользовательского режима
 */
export const useMultiplayerSettings = () => {
  return useGameSettings({
    defaultProfile: 'multiplayer',
    storageKey: 'kozyr-master-multiplayer-settings',
  });
};