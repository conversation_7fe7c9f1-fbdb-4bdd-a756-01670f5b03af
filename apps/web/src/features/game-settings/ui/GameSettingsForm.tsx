/**
 * Компонент формы настроек игры
 */
import React, { useState, useEffect } from 'react';
import { gameProfiles, GameProfile, getGameProfile } from '@/entities/game/config/profiles';

interface GameSettingsFormProps {
  initialProfile?: string;
  onSave?: (settings: GameProfile) => void;
  onCancel?: () => void;
  allowCustomSettings?: boolean;
}

/**
 * Форма настроек игры
 */
export const GameSettingsForm: React.FC<GameSettingsFormProps> = ({
  initialProfile = 'classic',
  onSave,
  onCancel,
  allowCustomSettings = true,
}) => {
  // Текущий профиль
  const [selectedProfileName, setSelectedProfileName] = useState(initialProfile);
  const [currentProfile, setCurrentProfile] = useState<GameProfile>(getGameProfile(initialProfile));
  const [isCustomMode, setIsCustomMode] = useState(false);

  // Обновление профиля при изменении выбранного профиля
  useEffect(() => {
    if (!isCustomMode) {
      setCurrentProfile(getGameProfile(selectedProfileName));
    }
  }, [selectedProfileName, isCustomMode]);

  // Обработчик изменения профиля
  const handleProfileChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const profileName = e.target.value;
    setSelectedProfileName(profileName);
    setIsCustomMode(profileName === 'custom');
  };

  // Обработчик изменения правил
  const handleRuleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const isCheckbox = type === 'checkbox';
    const path = name.split('.');
    const section = path[0] as 'rules' | 'ui';
    const field = path[1];

    setCurrentProfile((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: isCheckbox
          ? (e.target as HTMLInputElement).checked
          : type === 'number'
          ? Number(value)
          : value,
      },
    }));
  };

  // Обработчик сохранения настроек
  const handleSave = () => {
    if (onSave) {
      onSave(currentProfile);
    }
  };

  return (
    <div className="game-settings-form">
      <h2>Настройки игры</h2>

      <div className="form-group">
        <label htmlFor="profile">Профиль настроек:</label>
        <select
          id="profile"
          value={selectedProfileName}
          onChange={handleProfileChange}
          className="form-control"
        >
          {Object.entries(gameProfiles).map(([key, profile]) => (
            <option key={key} value={key}>
              {profile.name}
            </option>
          ))}
        </select>
        <p className="profile-description">{currentProfile.description}</p>
      </div>

      {(isCustomMode || allowCustomSettings) && (
        <div className="custom-settings">
          <h3>Правила игры</h3>
          <div className="form-group">
            <label htmlFor="rules.initialCardsCount">Начальное количество карт:</label>
            <input
              type="number"
              id="rules.initialCardsCount"
              name="rules.initialCardsCount"
              value={currentProfile.rules.initialCardsCount}
              onChange={handleRuleChange}
              min={1}
              max={10}
              className="form-control"
            />
          </div>

          <div className="form-group">
            <label htmlFor="rules.timePerMove">Время на ход (сек):</label>
            <input
              type="number"
              id="rules.timePerMove"
              name="rules.timePerMove"
              value={currentProfile.rules.timePerMove}
              onChange={handleRuleChange}
              min={5}
              max={120}
              className="form-control"
            />
          </div>

          <div className="form-group">
            <label htmlFor="rules.winScore">Очки для победы:</label>
            <input
              type="number"
              id="rules.winScore"
              name="rules.winScore"
              value={currentProfile.rules.winScore}
              onChange={handleRuleChange}
              min={10}
              max={500}
              className="form-control"
            />
          </div>

          <div className="form-group">
            <label htmlFor="rules.deckType">Тип колоды:</label>
            <select
              id="rules.deckType"
              name="rules.deckType"
              value={currentProfile.rules.deckType}
              onChange={handleRuleChange}
              className="form-control"
            >
              <option value="standard36">Стандартная (36 карт)</option>
              <option value="standard52">Полная (52 карты)</option>
              <option value="custom">Пользовательская</option>
            </select>
          </div>

          <div className="form-group checkbox">
            <label>
              <input
                type="checkbox"
                name="rules.allowTaking"
                checked={currentProfile.rules.allowTaking}
                onChange={handleRuleChange}
              />
              Разрешить подкидывать карты
            </label>
          </div>

          <div className="form-group checkbox">
            <label>
              <input
                type="checkbox"
                name="rules.allowDefending"
                checked={currentProfile.rules.allowDefending}
                onChange={handleRuleChange}
              />
              Разрешить отбиваться
            </label>
          </div>

          <div className="form-group checkbox">
            <label>
              <input
                type="checkbox"
                name="rules.allowPassing"
                checked={currentProfile.rules.allowPassing}
                onChange={handleRuleChange}
              />
              Разрешить пасовать
            </label>
          </div>

          <h3>Настройки интерфейса</h3>
          <div className="form-group">
            <label htmlFor="ui.theme">Тема оформления:</label>
            <select
              id="ui.theme"
              name="ui.theme"
              value={currentProfile.ui.theme}
              onChange={handleRuleChange}
              className="form-control"
            >
              <option value="classic">Классическая</option>
              <option value="modern">Современная</option>
              <option value="dark">Темная</option>
            </select>
          </div>

          <div className="form-group checkbox">
            <label>
              <input
                type="checkbox"
                name="ui.animations"
                checked={currentProfile.ui.animations}
                onChange={handleRuleChange}
              />
              Включить анимации
            </label>
          </div>

          <div className="form-group checkbox">
            <label>
              <input
                type="checkbox"
                name="ui.sounds"
                checked={currentProfile.ui.sounds}
                onChange={handleRuleChange}
              />
              Включить звуки
            </label>
          </div>

          <div className="form-group checkbox">
            <label>
              <input
                type="checkbox"
                name="ui.showTimer"
                checked={currentProfile.ui.showTimer}
                onChange={handleRuleChange}
              />
              Показывать таймер
            </label>
          </div>
        </div>
      )}

      <div className="form-actions">
        <button type="button" className="btn btn-primary" onClick={handleSave}>
          Сохранить
        </button>
        {onCancel && (
          <button type="button" className="btn btn-secondary" onClick={onCancel}>
            Отмена
          </button>
        )}
      </div>
    </div>
  );
};