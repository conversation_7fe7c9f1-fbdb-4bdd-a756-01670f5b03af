# 🧪 **РУКОВОДСТВО ПО ТЕСТИРОВАНИЮ КОЗЫРЬ МАСТЕР 4.0**

## 📋 **СОДЕРЖАНИЕ**
- [Быстрый старт](#быстрый-старт)
- [Типы тестов](#типы-тестов)
- [Команды запуска](#команды-запуска)
- [Структура тестов](#структура-тестов)
- [Написание тестов](#написание-тестов)
- [Отладка тестов](#отладка-тестов)
- [CI/CD интеграция](#cicd-интеграция)

---

## 🚀 **БЫСТРЫЙ СТАРТ**

### **Установка зависимостей:**
```bash
npm install
```

### **Запуск всех тестов:**
```bash
npm run test:all
```

### **Запуск в режиме разработки:**
```bash
npm run test:watch
```

---

## 🎯 **ТИПЫ ТЕСТОВ**

### **1. Unit Tests (Модульные тесты)**
- **Цель**: Тестирование отдельных функций и хуков
- **Расположение**: `src/**/__tests__/`
- **Инструменты**: Jest + React Testing Library

```bash
npm run test:unit
```

### **2. Integration Tests (Интеграционные тесты)**
- **Цель**: Тестирование взаимодействия компонентов
- **Расположение**: `src/__tests__/integration/`
- **Инструменты**: Jest + RTL + Mocks

```bash
npm run test:integration
```

### **3. E2E Tests (End-to-End тесты)**
- **Цель**: Тестирование пользовательских сценариев
- **Расположение**: `e2e/`
- **Инструменты**: Playwright

```bash
npm run test:e2e
```

---

## ⚡ **КОМАНДЫ ЗАПУСКА**

### **Основные команды:**
```bash
# Все unit тесты
npm run test:unit

# Интеграционные тесты
npm run test:integration

# E2E тесты
npm run test:e2e

# E2E с UI
npm run test:e2e:ui

# E2E в headed режиме
npm run test:e2e:headed

# Все тесты подряд
npm run test:all

# CI pipeline
npm run test:ci
```

### **Режимы разработки:**
```bash
# Watch режим для unit тестов
npm run test:watch

# Coverage отчёт
npm run test:coverage

# Только изменённые файлы
npm test -- --onlyChanged
```

---

## 📁 **СТРУКТУРА ТЕСТОВ**

```
apps/web/
├── src/
│   ├── components/
│   │   └── __tests__/
│   │       ├── Navigation.test.tsx
│   │       ├── Hero3D.test.tsx
│   │       └── MetaversePreview.test.tsx
│   ├── hooks/
│   │   └── __tests__/
│   │       ├── useQuantumRandom.test.ts
│   │       ├── useEmotionalAI.test.ts
│   │       └── useWeb3.test.ts
│   └── __tests__/
│       └── integration/
│           └── HomePage.test.tsx
├── e2e/
│   ├── homepage.spec.ts
│   ├── games.spec.ts
│   └── wallet.spec.ts
├── jest.config.js
├── jest.setup.js
├── playwright.config.ts
└── TEST_REPORT.md
```

---

## ✍️ **НАПИСАНИЕ ТЕСТОВ**

### **Unit Test Example:**
```typescript
import { renderHook, act } from '@testing-library/react';
import { useQuantumRandom } from '../useQuantumRandom';

describe('useQuantumRandom', () => {
  it('должен генерировать квантовое число', async () => {
    const { result } = renderHook(() => useQuantumRandom());
    
    let quantumSeed: number;
    await act(async () => {
      quantumSeed = await result.current.generateQuantumSeed();
    });
    
    expect(quantumSeed!).toBeGreaterThanOrEqual(0);
    expect(quantumSeed!).toBeLessThan(1000000);
  });
});
```

### **Component Test Example:**
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { Navigation } from '../Navigation';

describe('Navigation', () => {
  it('должен обрабатывать клики', () => {
    render(<Navigation />);
    
    fireEvent.click(screen.getByText('Игры'));
    expect(mockRouter.push).toHaveBeenCalledWith('/games');
  });
});
```

### **E2E Test Example:**
```typescript
import { test, expect } from '@playwright/test';

test('должен загружать главную страницу', async ({ page }) => {
  await page.goto('/');
  await expect(page.locator('text=Козырь Мастер')).toBeVisible();
});
```

---

## 🔧 **КОНФИГУРАЦИЯ**

### **Jest Configuration (jest.config.js):**
```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
  ],
  transformIgnorePatterns: [
    'node_modules/(?!(three|@react-three|framer-motion)/)',
  ],
};
```

### **Playwright Configuration (playwright.config.ts):**
```typescript
export default defineConfig({
  testDir: './e2e',
  use: {
    baseURL: 'http://localhost:3001',
    trace: 'on-first-retry',
  },
  projects: [
    { name: 'chromium', use: { ...devices['Desktop Chrome'] } },
    { name: 'firefox', use: { ...devices['Desktop Firefox'] } },
    { name: 'webkit', use: { ...devices['Desktop Safari'] } },
  ],
});
```

---

## 🐛 **ОТЛАДКА ТЕСТОВ**

### **Общие проблемы:**

#### **1. JSX Parsing Error:**
```bash
SyntaxError: Unexpected token '<'
```
**Решение**: Проверить конфигурацию Babel/TypeScript

#### **2. Async/Await Timeout:**
```bash
Exceeded timeout of 5000 ms
```
**Решение**: Увеличить timeout или улучшить мокирование

#### **3. Module Not Found:**
```bash
Cannot resolve module
```
**Решение**: Проверить moduleNameMapper в jest.config.js

### **Полезные команды для отладки:**
```bash
# Запуск одного теста
npm test -- --testNamePattern="должен генерировать"

# Запуск с подробным выводом
npm test -- --verbose

# Запуск без cache
npm test -- --no-cache

# Debug режим
npm test -- --detectOpenHandles
```

---

## 📊 **COVERAGE ОТЧЁТЫ**

### **Генерация отчёта:**
```bash
npm run test:coverage
```

### **Просмотр HTML отчёта:**
```bash
open coverage/lcov-report/index.html
```

### **Цели покрытия:**
- **Statements**: 85%
- **Branches**: 80%
- **Functions**: 85%
- **Lines**: 85%

---

## 🤖 **CI/CD ИНТЕГРАЦИЯ**

### **GitHub Actions Example:**
```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm run test:ci
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

---

## 📝 **BEST PRACTICES**

### **Именование тестов:**
```typescript
// ✅ Хорошо
it('должен генерировать квантовое число от 0 до 1000000', () => {});

// ❌ Плохо  
it('test quantum', () => {});
```

### **Структура тестов:**
```typescript
describe('ComponentName', () => {
  beforeEach(() => {
    // Подготовка
  });

  describe('when condition', () => {
    it('should do something', () => {
      // Arrange
      // Act  
      // Assert
    });
  });
});
```

### **Мокирование:**
```typescript
// ✅ Правильное мокирование
jest.mock('../api', () => ({
  fetchData: jest.fn().mockResolvedValue(mockData),
}));

// ❌ Неправильное мокирование
jest.mock('../api');
```

---

## 🎯 **ЧЕКЛИСТ КАЧЕСТВА**

### **Перед коммитом:**
- [ ] Все тесты проходят
- [ ] Coverage не упал
- [ ] Нет console.error в тестах
- [ ] Новые функции покрыты тестами

### **Перед релизом:**
- [ ] E2E тесты проходят
- [ ] Performance тесты в норме
- [ ] Cross-browser тестирование
- [ ] Mobile тестирование

---

## 🆘 **ПОЛУЧЕНИЕ ПОМОЩИ**

### **Документация:**
- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Playwright Documentation](https://playwright.dev/docs/intro)

### **Полезные ресурсы:**
- [Testing Best Practices](https://github.com/goldbergyoni/javascript-testing-best-practices)
- [Common Testing Mistakes](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)

---

## 🎉 **ЗАКЛЮЧЕНИЕ**

Тестирование - это **инвестиция в качество** и **стабильность** проекта. 

**Помните**: 
- 🎯 **Качество важнее количества** тестов
- 🚀 **TDD** ускоряет разработку
- 🔧 **Хорошие тесты** - лучшая документация
- 📈 **Coverage** - метрика, а не цель

**Удачного тестирования! 🧪✨**
