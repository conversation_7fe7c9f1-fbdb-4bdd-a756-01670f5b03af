{"name": "kozyr-master-web", "private": true, "version": "0.1.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=__tests__", "test:integration": "jest --testPathPattern=integration", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "test:ci": "npm run lint && npm run test:coverage && npm run build && npm run test:e2e"}, "dependencies": {"@kozyr-master/core": "file:../../packages/core", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@reduxjs/toolkit": "^2.0.1", "framer-motion": "^12.16.0", "i18next": "^23.7.11", "next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^14.0.0", "react-redux": "^9.0.4", "socket.io-client": "^4.8.1", "styled-components": "^6.1.18", "three": "^0.177.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/styled-components": "^5.1.34", "@types/three": "^0.177.0", "babel-jest": "^29.7.0", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "jest-environment-jsdom": "^29.7.0", "typescript": "^5.3.3", "vitest": "^1.1.0"}}