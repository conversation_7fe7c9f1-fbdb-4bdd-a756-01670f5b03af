{"name": "kozyr-master-web", "private": true, "version": "0.1.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@reduxjs/toolkit": "^2.0.1", "i18next": "^23.7.11", "next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^14.0.0", "react-redux": "^9.0.4", "socket.io-client": "^4.7.2", "styled-components": "^6.1.1", "@kozyr-master/core": "file:../../packages/core"}, "devDependencies": {"@testing-library/react": "^14.1.2", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "typescript": "^5.3.3", "vitest": "^1.1.0"}}