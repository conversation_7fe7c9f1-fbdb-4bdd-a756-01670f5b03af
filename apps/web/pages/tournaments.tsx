import Head from "next/head";
import { useState, useEffect } from "react";
import styled from "styled-components";
import { useRouter } from "next/router";
import { useSocket } from "@/hooks/useSocket";

const TournamentsPage = () => {
  const router = useRouter();
  const socket = useSocket();
  const [activeTab, setActiveTab] = useState<'active' | 'all' | 'create'>('active');
  
  // Форма создания турнира
  const [tournamentForm, setTournamentForm] = useState({
    name: '',
    description: '',
    type: 'single_elimination' as const,
    maxParticipants: 8,
    autoStart: true,
    allowSpectators: true,
    timeLimit: 30,
    ratingMin: 0,
    ratingMax: 3000
  });

  const handleBackToHome = () => {
    router.push("/");
  };

  // Загружаем турниры при подключении
  useEffect(() => {
    if (socket.connected) {
      socket.getTournaments(activeTab === 'active' ? 'active' : undefined);
    }
  }, [socket.connected, activeTab]);

  const handleCreateTournament = () => {
    if (!tournamentForm.name.trim()) return;

    const settings = {
      autoStart: tournamentForm.autoStart,
      allowSpectators: tournamentForm.allowSpectators,
      timeLimit: tournamentForm.timeLimit,
      ratingRestriction: {
        min: tournamentForm.ratingMin,
        max: tournamentForm.ratingMax
      }
    };

    socket.createTournament(
      tournamentForm.name.trim(),
      tournamentForm.description.trim(),
      tournamentForm.type,
      tournamentForm.maxParticipants,
      settings
    );

    // Сброс формы
    setTournamentForm({
      name: '',
      description: '',
      type: 'single_elimination',
      maxParticipants: 8,
      autoStart: true,
      allowSpectators: true,
      timeLimit: 30,
      ratingMin: 0,
      ratingMax: 3000
    });
  };

  const handleRegisterForTournament = (tournamentId: string) => {
    socket.registerForTournament(tournamentId);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'registration': return '#4CAF50';
      case 'starting': return '#FF9800';
      case 'in_progress': return '#2196F3';
      case 'finished': return '#9E9E9E';
      default: return '#9E9E9E';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'registration': return 'Регистрация';
      case 'starting': return 'Запуск';
      case 'in_progress': return 'Идет';
      case 'finished': return 'Завершен';
      default: return status;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'single_elimination': return 'На выбывание';
      case 'double_elimination': return 'Двойное выбывание';
      case 'round_robin': return 'Круговой';
      default: return type;
    }
  };

  const canRegister = (tournament: any) => {
    if (!socket.player) return false;
    if (tournament.status !== 'registration') return false;
    if (tournament.participants.length >= tournament.maxParticipants) return false;
    if (tournament.participants.some((p: any) => p.playerId === socket.player?.id)) return false;
    
    // Проверка рейтинговых ограничений
    if (tournament.settings.ratingRestriction && socket.playerRating) {
      const { min, max } = tournament.settings.ratingRestriction;
      if (socket.playerRating.rating < min || socket.playerRating.rating > max) return false;
    }
    
    return true;
  };

  const isRegistered = (tournament: any) => {
    return tournament.participants.some((p: any) => p.playerId === socket.player?.id);
  };

  return (
    <Container>
      <Head>
        <title>Турниры - Козырь Мастер</title>
        <meta name="description" content="Участвуйте в турнирах по Дураку" />
      </Head>

      <Header>
        <BackButton onClick={handleBackToHome}>← Назад</BackButton>
        <Title>Турниры</Title>
      </Header>

      <Main>
        {!socket.connected ? (
          <NotConnected>
            <Message>Подключитесь к серверу, чтобы участвовать в турнирах</Message>
            <ConnectButton onClick={() => router.push("/multiplayer")}>
              Подключиться
            </ConnectButton>
          </NotConnected>
        ) : !socket.player ? (
          <NotConnected>
            <Message>Войдите в игру, чтобы участвовать в турнирах</Message>
            <ConnectButton onClick={() => router.push("/multiplayer")}>
              Войти в игру
            </ConnectButton>
          </NotConnected>
        ) : (
          <>
            {/* Навигация */}
            <TabNavigation>
              <Tab 
                active={activeTab === 'active'} 
                onClick={() => setActiveTab('active')}
              >
                🔥 Активные
              </Tab>
              <Tab 
                active={activeTab === 'all'} 
                onClick={() => setActiveTab('all')}
              >
                📋 Все турниры
              </Tab>
              <Tab 
                active={activeTab === 'create'} 
                onClick={() => setActiveTab('create')}
              >
                ➕ Создать
              </Tab>
            </TabNavigation>

            {/* Контент */}
            {activeTab === 'create' ? (
              <CreateTournamentSection>
                <SectionTitle>Создать турнир</SectionTitle>
                
                <TournamentForm>
                  <FormRow>
                    <FormGroup>
                      <Label>Название турнира</Label>
                      <Input
                        type="text"
                        placeholder="Введите название"
                        value={tournamentForm.name}
                        onChange={(e) => setTournamentForm(prev => ({
                          ...prev,
                          name: e.target.value
                        }))}
                      />
                    </FormGroup>
                  </FormRow>

                  <FormRow>
                    <FormGroup>
                      <Label>Описание</Label>
                      <TextArea
                        placeholder="Описание турнира (необязательно)"
                        value={tournamentForm.description}
                        onChange={(e) => setTournamentForm(prev => ({
                          ...prev,
                          description: e.target.value
                        }))}
                      />
                    </FormGroup>
                  </FormRow>

                  <FormRow>
                    <FormGroup>
                      <Label>Тип турнира</Label>
                      <Select
                        value={tournamentForm.type}
                        onChange={(e) => setTournamentForm(prev => ({
                          ...prev,
                          type: e.target.value as any
                        }))}
                      >
                        <option value="single_elimination">На выбывание</option>
                        <option value="double_elimination">Двойное выбывание</option>
                        <option value="round_robin">Круговой</option>
                      </Select>
                    </FormGroup>

                    <FormGroup>
                      <Label>Участников</Label>
                      <Select
                        value={tournamentForm.maxParticipants}
                        onChange={(e) => setTournamentForm(prev => ({
                          ...prev,
                          maxParticipants: parseInt(e.target.value)
                        }))}
                      >
                        <option value={4}>4 игрока</option>
                        <option value={8}>8 игроков</option>
                        <option value={16}>16 игроков</option>
                        <option value={32}>32 игрока</option>
                      </Select>
                    </FormGroup>
                  </FormRow>

                  <FormActions>
                    <CreateButton 
                      onClick={handleCreateTournament}
                      disabled={!tournamentForm.name.trim()}
                    >
                      Создать турнир
                    </CreateButton>
                  </FormActions>
                </TournamentForm>
              </CreateTournamentSection>
            ) : (
              <TournamentsSection>
                <SectionHeader>
                  <SectionTitle>
                    {activeTab === 'active' ? 'Активные турниры' : 'Все турниры'} 
                    ({socket.tournaments.length})
                  </SectionTitle>
                  <RefreshButton onClick={() => socket.getTournaments(activeTab === 'active' ? 'active' : undefined)}>
                    🔄 Обновить
                  </RefreshButton>
                </SectionHeader>

                {socket.tournaments.length === 0 ? (
                  <EmptyState>
                    {activeTab === 'active' ? 
                      'Нет активных турниров' : 
                      'Турниры не найдены'
                    }
                  </EmptyState>
                ) : (
                  <TournamentsGrid>
                    {socket.tournaments.map((tournament) => (
                      <TournamentCard key={tournament.id}>
                        <TournamentHeader>
                          <TournamentName>{tournament.name}</TournamentName>
                          <StatusBadge color={getStatusColor(tournament.status)}>
                            {getStatusText(tournament.status)}
                          </StatusBadge>
                        </TournamentHeader>

                        {tournament.description && (
                          <TournamentDescription>
                            {tournament.description}
                          </TournamentDescription>
                        )}

                        <TournamentInfo>
                          <InfoItem>
                            <InfoLabel>Тип:</InfoLabel>
                            <InfoValue>{getTypeText(tournament.type)}</InfoValue>
                          </InfoItem>
                          <InfoItem>
                            <InfoLabel>Участники:</InfoLabel>
                            <InfoValue>
                              {tournament.participants.length}/{tournament.maxParticipants}
                            </InfoValue>
                          </InfoItem>
                          <InfoItem>
                            <InfoLabel>Взнос:</InfoLabel>
                            <InfoValue>{tournament.entryFee} очков</InfoValue>
                          </InfoItem>
                          <InfoItem>
                            <InfoLabel>Приз:</InfoLabel>
                            <InfoValue>{tournament.prizePool.first} очков</InfoValue>
                          </InfoItem>
                        </TournamentInfo>

                        <TournamentActions>
                          {isRegistered(tournament) ? (
                            <RegisteredButton>
                              ✓ Зарегистрирован
                            </RegisteredButton>
                          ) : canRegister(tournament) ? (
                            <RegisterButton 
                              onClick={() => handleRegisterForTournament(tournament.id)}
                            >
                              Участвовать
                            </RegisterButton>
                          ) : (
                            <DisabledButton>
                              {tournament.status !== 'registration' ? 'Регистрация закрыта' :
                               tournament.participants.length >= tournament.maxParticipants ? 'Турнир заполнен' :
                               'Недоступно'}
                            </DisabledButton>
                          )}
                        </TournamentActions>
                      </TournamentCard>
                    ))}
                  </TournamentsGrid>
                )}
              </TournamentsSection>
            )}
          </>
        )}
      </Main>
    </Container>
  );
};

// Стилизованные компоненты
const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
`;

const Header = styled.header`
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
`;

const BackButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 2rem;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const Title = styled.h1`
  font-size: 2.5rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
`;

const Main = styled.main`
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
`;

const NotConnected = styled.div`
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 3rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const Message = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
`;

const ConnectButton = styled.button`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
`;

const TabNavigation = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
`;

const Tab = styled.button<{ active: boolean }>`
  background: ${props => props.active ?
    'linear-gradient(135deg, #4CAF50, #45a049)' :
    'rgba(255, 255, 255, 0.1)'
  };
  border: 1px solid ${props => props.active ?
    'rgba(76, 175, 80, 0.3)' :
    'rgba(255, 255, 255, 0.2)'
  };
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: ${props => props.active ?
      'linear-gradient(135deg, #45a049, #4CAF50)' :
      'rgba(255, 255, 255, 0.2)'
    };
  }
`;

const CreateTournamentSection = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 2rem;
`;

const TournamentsSection = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 2rem;
`;

const SectionTitle = styled.h2`
  margin: 0 0 2rem 0;
  color: #FFD700;
  font-size: 1.8rem;
`;

const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const RefreshButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const TournamentForm = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const FormRow = styled.div`
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  min-width: 200px;
`;

const Label = styled.label`
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
`;

const Input = styled.input`
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;

  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  &:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  }
`;

const TextArea = styled.textarea`
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  min-height: 80px;
  resize: vertical;

  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  &:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  }
`;

const Select = styled.select`
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  }

  option {
    background: #333;
    color: white;
  }
`;

const FormActions = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 1rem;
`;

const CreateButton = styled.button<{ disabled?: boolean }>`
  background: ${props => props.disabled ?
    'rgba(255, 255, 255, 0.1)' :
    'linear-gradient(135deg, #4CAF50, #45a049)'
  };
  border: none;
  color: ${props => props.disabled ? 'rgba(255, 255, 255, 0.5)' : 'white'};
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.3s ease;

  &:hover {
    ${props => !props.disabled && `
      background: linear-gradient(135deg, #45a049, #4CAF50);
      transform: translateY(-2px);
    `}
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.2rem;
`;

const TournamentsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
`;

const TournamentCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }
`;

const TournamentHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
`;

const TournamentName = styled.h3`
  margin: 0;
  color: white;
  font-size: 1.3rem;
  flex: 1;
  margin-right: 1rem;
`;

const StatusBadge = styled.div<{ color: string }>`
  background: ${props => props.color}20;
  border: 1px solid ${props => props.color}40;
  color: ${props => props.color};
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  white-space: nowrap;
`;

const TournamentDescription = styled.p`
  margin: 0 0 1rem 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  line-height: 1.4;
`;

const TournamentInfo = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
`;

const InfoItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const InfoLabel = styled.span`
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
`;

const InfoValue = styled.span`
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
`;

const TournamentActions = styled.div`
  display: flex;
  justify-content: center;
`;

const RegisterButton = styled.button`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;

  &:hover {
    background: linear-gradient(135deg, #45a049, #4CAF50);
    transform: translateY(-1px);
  }
`;

const RegisteredButton = styled.button`
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #4CAF50;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: default;
  width: 100%;
`;

const DisabledButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.5);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: not-allowed;
  width: 100%;
`;

export default TournamentsPage;
