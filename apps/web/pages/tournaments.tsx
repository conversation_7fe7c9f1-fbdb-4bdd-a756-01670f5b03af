import Head from "next/head";
import styled from "styled-components";
import { useRouter } from "next/router";

const TournamentsPage = () => {
  const router = useRouter();

  const handleBackToHome = () => {
    router.push("/");
  };

  return (
    <Container>
      <Head>
        <title>Турниры - Козырь Мастер</title>
        <meta name="description" content="Участвуйте в турнирах по карточным играм" />
      </Head>

      <Header>
        <BackButton onClick={handleBackToHome}>← Назад</BackButton>
        <Title>Турниры</Title>
      </Header>

      <Main>
        <ComingSoon>
          <Icon>🏆</Icon>
          <Message>Турниры скоро появятся!</Message>
          <Description>
            Мы работаем над системой турниров, где вы сможете соревноваться 
            с другими игроками и выигрывать призы.
          </Description>
          <Features>
            <Feature>🎯 Ежедневные турниры</Feature>
            <Feature>💰 Призовые фонды</Feature>
            <Feature>🏅 Рейтинговая система</Feature>
            <Feature>👥 Командные соревнования</Feature>
          </Features>
        </ComingSoon>
      </Main>
    </Container>
  );
};

const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
`;

const Header = styled.header`
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
`;

const BackButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 2rem;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const Title = styled.h1`
  font-size: 2.5rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
`;

const Main = styled.main`
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 100px);
  padding: 2rem;
`;

const ComingSoon = styled.div`
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 3rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 600px;
`;

const Icon = styled.div`
  font-size: 4rem;
  margin-bottom: 1rem;
`;

const Message = styled.h2`
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #FFD700;
`;

const Description = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
`;

const Features = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
`;

const Feature = styled.div`
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 10px;
  font-size: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

export default TournamentsPage;
