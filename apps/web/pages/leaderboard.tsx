import Head from "next/head";
import { useState, useEffect } from "react";
import styled from "styled-components";
import { useRouter } from "next/router";
import { useSocket } from "@/hooks/useSocket";

const LeaderboardPage = () => {
  const router = useRouter();
  const socket = useSocket();
  const [activeCategory, setActiveCategory] = useState<'rating' | 'wins' | 'winRate' | 'streak'>('rating');

  const handleBackToHome = () => {
    router.push("/");
  };

  // Загружаем данные при подключении
  useEffect(() => {
    if (socket.connected) {
      socket.getLeaderboard(100);
      socket.getTopPlayers();
    }
  }, [socket.connected]);

  const getRankIcon = (position: number) => {
    switch (position) {
      case 1: return '🥇';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return `#${position}`;
    }
  };

  const getRatingCategory = (rating: number) => {
    if (rating >= 2000) return { name: 'Гроссмейстер', color: '#FFD700' };
    if (rating >= 1800) return { name: 'Мастер', color: '#C0C0C0' };
    if (rating >= 1600) return { name: 'Эксперт', color: '#CD7F32' };
    if (rating >= 1400) return { name: 'Продвинутый', color: '#4CAF50' };
    if (rating >= 1200) return { name: 'Средний', color: '#2196F3' };
    if (rating >= 1000) return { name: 'Начинающий', color: '#FF9800' };
    return { name: 'Новичок', color: '#9E9E9E' };
  };

  const getCurrentData = () => {
    if (!socket.topPlayers) return [];
    
    switch (activeCategory) {
      case 'rating':
        return socket.topPlayers.byRating;
      case 'wins':
        return socket.topPlayers.byWins;
      case 'winRate':
        return socket.topPlayers.byWinRate;
      case 'streak':
        return socket.topPlayers.byStreak;
      default:
        return socket.topPlayers.byRating;
    }
  };

  const getValueForCategory = (player: any, category: string) => {
    switch (category) {
      case 'rating':
        return player.rating;
      case 'wins':
        return player.wins;
      case 'winRate':
        return `${player.winRate.toFixed(1)}%`;
      case 'streak':
        return player.longestWinStreak;
      default:
        return player.rating;
    }
  };

  const getCategoryTitle = (category: string) => {
    switch (category) {
      case 'rating': return 'По рейтингу';
      case 'wins': return 'По победам';
      case 'winRate': return 'По проценту побед';
      case 'streak': return 'По лучшей серии';
      default: return 'По рейтингу';
    }
  };

  return (
    <Container>
      <Head>
        <title>Таблица лидеров - Козырь Мастер</title>
        <meta name="description" content="Лучшие игроки в Дурака" />
      </Head>

      <Header>
        <BackButton onClick={handleBackToHome}>← Назад</BackButton>
        <Title>Таблица лидеров</Title>
      </Header>

      <Main>
        {!socket.connected ? (
          <NotConnected>
            <Message>Подключитесь к серверу, чтобы просмотреть таблицу лидеров</Message>
            <ConnectButton onClick={() => router.push("/multiplayer")}>
              Подключиться
            </ConnectButton>
          </NotConnected>
        ) : (
          <>
            {/* Категории */}
            <CategoryTabs>
              <CategoryTab 
                active={activeCategory === 'rating'} 
                onClick={() => setActiveCategory('rating')}
              >
                📊 Рейтинг
              </CategoryTab>
              <CategoryTab 
                active={activeCategory === 'wins'} 
                onClick={() => setActiveCategory('wins')}
              >
                🏆 Победы
              </CategoryTab>
              <CategoryTab 
                active={activeCategory === 'winRate'} 
                onClick={() => setActiveCategory('winRate')}
              >
                📈 Винрейт
              </CategoryTab>
              <CategoryTab 
                active={activeCategory === 'streak'} 
                onClick={() => setActiveCategory('streak')}
              >
                ⚡ Серии
              </CategoryTab>
            </CategoryTabs>

            {/* Таблица лидеров */}
            <LeaderboardCard>
              <LeaderboardTitle>
                {getCategoryTitle(activeCategory)}
              </LeaderboardTitle>

              {getCurrentData().length > 0 ? (
                <LeaderboardTable>
                  <TableHeader>
                    <HeaderCell>Место</HeaderCell>
                    <HeaderCell>Игрок</HeaderCell>
                    <HeaderCell>Категория</HeaderCell>
                    <HeaderCell>Значение</HeaderCell>
                    <HeaderCell>Игр</HeaderCell>
                    <HeaderCell>Винрейт</HeaderCell>
                  </TableHeader>
                  <TableBody>
                    {getCurrentData().map((player, index) => {
                      const category = getRatingCategory(player.rating);
                      return (
                        <TableRow key={player.playerId} rank={index + 1}>
                          <TableCell>
                            <RankDisplay rank={index + 1}>
                              {getRankIcon(index + 1)}
                            </RankDisplay>
                          </TableCell>
                          <TableCell>
                            <PlayerName>{player.playerName}</PlayerName>
                          </TableCell>
                          <TableCell>
                            <CategoryBadge color={category.color}>
                              {category.name}
                            </CategoryBadge>
                          </TableCell>
                          <TableCell>
                            <ValueDisplay category={activeCategory}>
                              {getValueForCategory(player, activeCategory)}
                            </ValueDisplay>
                          </TableCell>
                          <TableCell>{player.gamesPlayed}</TableCell>
                          <TableCell>{player.winRate.toFixed(1)}%</TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </LeaderboardTable>
              ) : (
                <EmptyState>
                  {socket.topPlayers ? 
                    'Нет данных для отображения' : 
                    'Загрузка таблицы лидеров...'
                  }
                </EmptyState>
              )}
            </LeaderboardCard>

            {/* Полная таблица лидеров */}
            {socket.leaderboard.length > 0 && (
              <FullLeaderboardCard>
                <LeaderboardTitle>Полная таблица лидеров</LeaderboardTitle>
                <FullLeaderboardGrid>
                  {socket.leaderboard.slice(0, 50).map((player, index) => {
                    const category = getRatingCategory(player.rating);
                    return (
                      <PlayerCard key={player.playerId} rank={index + 1}>
                        <PlayerRank rank={index + 1}>
                          {getRankIcon(index + 1)}
                        </PlayerRank>
                        <PlayerInfo>
                          <PlayerName>{player.playerName}</PlayerName>
                          <CategoryBadge color={category.color}>
                            {category.name}
                          </CategoryBadge>
                        </PlayerInfo>
                        <PlayerStats>
                          <StatValue>{player.rating}</StatValue>
                          <StatLabel>рейтинг</StatLabel>
                        </PlayerStats>
                        <PlayerStats>
                          <StatValue>{player.wins}</StatValue>
                          <StatLabel>побед</StatLabel>
                        </PlayerStats>
                        <PlayerStats>
                          <StatValue>{player.winRate.toFixed(1)}%</StatValue>
                          <StatLabel>винрейт</StatLabel>
                        </PlayerStats>
                      </PlayerCard>
                    );
                  })}
                </FullLeaderboardGrid>
              </FullLeaderboardCard>
            )}
          </>
        )}
      </Main>
    </Container>
  );
};

// Стилизованные компоненты
const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
`;

const Header = styled.header`
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
`;

const BackButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 2rem;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const Title = styled.h1`
  font-size: 2.5rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
`;

const Main = styled.main`
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
`;

const NotConnected = styled.div`
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 3rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const Message = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
`;

const ConnectButton = styled.button`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
`;

const CategoryTabs = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
`;

const CategoryTab = styled.button<{ active: boolean }>`
  background: ${props => props.active ?
    'linear-gradient(135deg, #4CAF50, #45a049)' :
    'rgba(255, 255, 255, 0.1)'
  };
  border: 1px solid ${props => props.active ?
    'rgba(76, 175, 80, 0.3)' :
    'rgba(255, 255, 255, 0.2)'
  };
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: ${props => props.active ?
      'linear-gradient(135deg, #45a049, #4CAF50)' :
      'rgba(255, 255, 255, 0.2)'
    };
  }
`;

const LeaderboardCard = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 2rem;
  margin-bottom: 2rem;
`;

const LeaderboardTitle = styled.h2`
  margin: 0 0 2rem 0;
  color: #FFD700;
  font-size: 1.8rem;
  text-align: center;
`;

const LeaderboardTable = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.thead`
  background: rgba(255, 255, 255, 0.1);
`;

const HeaderCell = styled.th`
  padding: 1rem;
  text-align: left;
  color: #FFD700;
  font-weight: 600;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr<{ rank: number }>`
  background: ${props => {
    if (props.rank === 1) return 'rgba(255, 215, 0, 0.1)';
    if (props.rank === 2) return 'rgba(192, 192, 192, 0.1)';
    if (props.rank === 3) return 'rgba(205, 127, 50, 0.1)';
    return 'transparent';
  }};
  transition: background 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const TableCell = styled.td`
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
`;

const RankDisplay = styled.div<{ rank: number }>`
  font-size: 1.2rem;
  font-weight: bold;
  color: ${props => {
    if (props.rank === 1) return '#FFD700';
    if (props.rank === 2) return '#C0C0C0';
    if (props.rank === 3) return '#CD7F32';
    return 'white';
  }};
`;

const PlayerName = styled.div`
  font-weight: 600;
  color: white;
`;

const CategoryBadge = styled.div<{ color: string }>`
  background: ${props => props.color}20;
  border: 1px solid ${props => props.color}40;
  color: ${props => props.color};
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  display: inline-block;
`;

const ValueDisplay = styled.div<{ category: string }>`
  font-weight: bold;
  font-size: 1.1rem;
  color: ${props => {
    switch (props.category) {
      case 'rating': return '#FFD700';
      case 'wins': return '#4CAF50';
      case 'winRate': return '#2196F3';
      case 'streak': return '#FF9800';
      default: return 'white';
    }
  }};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.2rem;
`;

const FullLeaderboardCard = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 2rem;
`;

const FullLeaderboardGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
`;

const PlayerCard = styled.div<{ rank: number }>`
  background: ${props => {
    if (props.rank === 1) return 'rgba(255, 215, 0, 0.1)';
    if (props.rank === 2) return 'rgba(192, 192, 192, 0.1)';
    if (props.rank === 3) return 'rgba(205, 127, 50, 0.1)';
    return 'rgba(255, 255, 255, 0.05)';
  }};
  border: 1px solid ${props => {
    if (props.rank === 1) return 'rgba(255, 215, 0, 0.3)';
    if (props.rank === 2) return 'rgba(192, 192, 192, 0.3)';
    if (props.rank === 3) return 'rgba(205, 127, 50, 0.3)';
    return 'rgba(255, 255, 255, 0.1)';
  }};
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }
`;

const PlayerRank = styled.div<{ rank: number }>`
  font-size: 1.5rem;
  font-weight: bold;
  color: ${props => {
    if (props.rank === 1) return '#FFD700';
    if (props.rank === 2) return '#C0C0C0';
    if (props.rank === 3) return '#CD7F32';
    return 'white';
  }};
  min-width: 40px;
  text-align: center;
`;

const PlayerInfo = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const PlayerStats = styled.div`
  text-align: center;
  min-width: 60px;
`;

const StatValue = styled.div`
  font-weight: bold;
  color: #FFD700;
  font-size: 1.1rem;
`;

const StatLabel = styled.div`
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
`;

export default LeaderboardPage;
