import Head from "next/head";
import { useState, useEffect } from "react";
import styled from "styled-components";
import { useRouter } from "next/router";
import { useSocket } from "@/hooks/useSocket";

const ProfilePage = () => {
  const router = useRouter();
  const socket = useSocket();
  const [activeTab, setActiveTab] = useState<'stats' | 'achievements'>('stats');

  const handleBackToHome = () => {
    router.push("/");
  };

  // Загружаем данные при подключении
  useEffect(() => {
    if (socket.connected && socket.player) {
      socket.getPlayerRating();
      socket.getPlayerAchievements();
    }
  }, [socket.connected, socket.player]);

  if (!socket.player) {
    return (
      <Container>
        <Head>
          <title>Профиль - Козырь Мастер</title>
        </Head>
        <Header>
          <BackButton onClick={handleBackToHome}>← Назад</BackButton>
          <Title>Профиль</Title>
        </Header>
        <Main>
          <NotConnected>
            <Message>Подключитесь к серверу, чтобы просмотреть профиль</Message>
            <ConnectButton onClick={() => router.push("/multiplayer")}>
              Подключиться
            </ConnectButton>
          </NotConnected>
        </Main>
      </Container>
    );
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return '#9E9E9E';
      case 'rare': return '#2196F3';
      case 'epic': return '#9C27B0';
      case 'legendary': return '#FF9800';
      default: return '#9E9E9E';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'games': return '🎮';
      case 'wins': return '🏆';
      case 'rating': return '📈';
      case 'streaks': return '⚡';
      case 'special': return '🌟';
      default: return '🏅';
    }
  };

  return (
    <Container>
      <Head>
        <title>Профиль - {socket.player.name}</title>
        <meta name="description" content="Профиль игрока с рейтингом и достижениями" />
      </Head>

      <Header>
        <BackButton onClick={handleBackToHome}>← Назад</BackButton>
        <Title>Профиль</Title>
      </Header>

      <Main>
        {/* Информация об игроке */}
        <PlayerCard>
          <PlayerAvatar>
            {socket.player.name.charAt(0).toUpperCase()}
          </PlayerAvatar>
          <PlayerInfo>
            <PlayerName>{socket.player.name}</PlayerName>
            {socket.ratingCategory && (
              <RatingBadge color={socket.ratingCategory.color}>
                {socket.ratingCategory.name}
              </RatingBadge>
            )}
            {socket.playerRating && (
              <RatingValue>{socket.playerRating.rating}</RatingValue>
            )}
          </PlayerInfo>
        </PlayerCard>

        {/* Навигация по табам */}
        <TabNavigation>
          <Tab 
            active={activeTab === 'stats'} 
            onClick={() => setActiveTab('stats')}
          >
            📊 Статистика
          </Tab>
          <Tab 
            active={activeTab === 'achievements'} 
            onClick={() => setActiveTab('achievements')}
          >
            🏅 Достижения
          </Tab>
        </TabNavigation>

        {/* Контент табов */}
        {activeTab === 'stats' && (
          <TabContent>
            {socket.playerRating ? (
              <StatsGrid>
                <StatCard>
                  <StatIcon>🎮</StatIcon>
                  <StatValue>{socket.playerRating.gamesPlayed}</StatValue>
                  <StatLabel>Игр сыграно</StatLabel>
                </StatCard>

                <StatCard>
                  <StatIcon>🏆</StatIcon>
                  <StatValue>{socket.playerRating.wins}</StatValue>
                  <StatLabel>Побед</StatLabel>
                </StatCard>

                <StatCard>
                  <StatIcon>💔</StatIcon>
                  <StatValue>{socket.playerRating.losses}</StatValue>
                  <StatLabel>Поражений</StatLabel>
                </StatCard>

                <StatCard>
                  <StatIcon>📈</StatIcon>
                  <StatValue>{socket.playerRating.winRate.toFixed(1)}%</StatValue>
                  <StatLabel>Процент побед</StatLabel>
                </StatCard>

                <StatCard>
                  <StatIcon>🔥</StatIcon>
                  <StatValue>{socket.playerRating.currentStreak}</StatValue>
                  <StatLabel>Текущая серия</StatLabel>
                </StatCard>

                <StatCard>
                  <StatIcon>⭐</StatIcon>
                  <StatValue>{socket.playerRating.longestWinStreak}</StatValue>
                  <StatLabel>Лучшая серия</StatLabel>
                </StatCard>

                <StatCard>
                  <StatIcon>👑</StatIcon>
                  <StatValue>{socket.playerRating.highestRating}</StatValue>
                  <StatLabel>Лучший рейтинг</StatLabel>
                </StatCard>

                <StatCard>
                  <StatIcon>⏱️</StatIcon>
                  <StatValue>{Math.round(socket.playerRating.averageGameDuration / 60)}м</StatValue>
                  <StatLabel>Средняя игра</StatLabel>
                </StatCard>
              </StatsGrid>
            ) : (
              <LoadingMessage>Загрузка статистики...</LoadingMessage>
            )}
          </TabContent>
        )}

        {activeTab === 'achievements' && (
          <TabContent>
            {socket.playerAchievements ? (
              <>
                <AchievementProgress>
                  <ProgressTitle>
                    Прогресс достижений: {socket.playerAchievements.progress.unlocked} / {socket.playerAchievements.progress.total}
                  </ProgressTitle>
                  <ProgressBar>
                    <ProgressFill width={socket.playerAchievements.progress.percentage} />
                  </ProgressBar>
                  <ProgressText>{socket.playerAchievements.progress.percentage}%</ProgressText>
                </AchievementProgress>

                {socket.playerAchievements.unlocked.length > 0 && (
                  <AchievementSection>
                    <SectionTitle>🏆 Разблокированные ({socket.playerAchievements.unlocked.length})</SectionTitle>
                    <AchievementGrid>
                      {socket.playerAchievements.unlocked.map((achievement) => (
                        <AchievementCard key={achievement.id} rarity={achievement.rarity}>
                          <AchievementIcon>{achievement.icon}</AchievementIcon>
                          <AchievementName>{achievement.name}</AchievementName>
                          <AchievementDescription>{achievement.description}</AchievementDescription>
                          <AchievementMeta>
                            <CategoryBadge>{getCategoryIcon(achievement.category)} {achievement.category}</CategoryBadge>
                            <RarityBadge color={getRarityColor(achievement.rarity)}>
                              {achievement.rarity}
                            </RarityBadge>
                          </AchievementMeta>
                          <UnlockedDate>
                            Получено: {new Date(achievement.unlockedAt).toLocaleDateString()}
                          </UnlockedDate>
                        </AchievementCard>
                      ))}
                    </AchievementGrid>
                  </AchievementSection>
                )}

                {socket.playerAchievements.locked.length > 0 && (
                  <AchievementSection>
                    <SectionTitle>🔒 Заблокированные ({socket.playerAchievements.locked.length})</SectionTitle>
                    <AchievementGrid>
                      {socket.playerAchievements.locked.slice(0, 12).map((achievement) => (
                        <AchievementCard key={achievement.id} rarity={achievement.rarity} locked>
                          <AchievementIcon>🔒</AchievementIcon>
                          <AchievementName>{achievement.name}</AchievementName>
                          <AchievementDescription>{achievement.description}</AchievementDescription>
                          <AchievementMeta>
                            <CategoryBadge>{getCategoryIcon(achievement.category)} {achievement.category}</CategoryBadge>
                            <RarityBadge color={getRarityColor(achievement.rarity)}>
                              {achievement.rarity}
                            </RarityBadge>
                          </AchievementMeta>
                        </AchievementCard>
                      ))}
                    </AchievementGrid>
                  </AchievementSection>
                )}
              </>
            ) : (
              <LoadingMessage>Загрузка достижений...</LoadingMessage>
            )}
          </TabContent>
        )}
      </Main>
    </Container>
  );
};

// Стилизованные компоненты
const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
`;

const Header = styled.header`
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
`;

const BackButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 2rem;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const Title = styled.h1`
  font-size: 2.5rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
`;

const Main = styled.main`
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const NotConnected = styled.div`
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 3rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const Message = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
`;

const ConnectButton = styled.button`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
`;

const PlayerCard = styled.div`
  display: flex;
  align-items: center;
  gap: 2rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 2rem;
`;

const PlayerAvatar = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF6B35, #F7931E);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
`;

const PlayerInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const PlayerName = styled.h2`
  font-size: 2rem;
  margin: 0;
  color: white;
`;

const RatingBadge = styled.div<{ color: string }>`
  background: ${props => props.color}20;
  border: 1px solid ${props => props.color}40;
  color: ${props => props.color};
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  display: inline-block;
  width: fit-content;
`;

const RatingValue = styled.div`
  font-size: 1.5rem;
  font-weight: bold;
  color: #FFD700;
`;

const TabNavigation = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
`;

const Tab = styled.button<{ active: boolean }>`
  background: ${props => props.active ?
    'linear-gradient(135deg, #4CAF50, #45a049)' :
    'rgba(255, 255, 255, 0.1)'
  };
  border: 1px solid ${props => props.active ?
    'rgba(76, 175, 80, 0.3)' :
    'rgba(255, 255, 255, 0.2)'
  };
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: ${props => props.active ?
      'linear-gradient(135deg, #45a049, #4CAF50)' :
      'rgba(255, 255, 255, 0.2)'
    };
  }
`;

const TabContent = styled.div`
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const LoadingMessage = styled.div`
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
`;

const StatCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }
`;

const StatIcon = styled.div`
  font-size: 2rem;
  margin-bottom: 0.5rem;
`;

const StatValue = styled.div`
  font-size: 2rem;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
`;

const AchievementProgress = styled.div`
  margin-bottom: 2rem;
  text-align: center;
`;

const ProgressTitle = styled.h3`
  margin: 0 0 1rem 0;
  color: #FFD700;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 0.5rem;
`;

const ProgressFill = styled.div<{ width: number }>`
  width: ${props => props.width}%;
  height: 100%;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  transition: width 0.3s ease;
`;

const ProgressText = styled.div`
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
`;

const AchievementSection = styled.div`
  margin-bottom: 3rem;
`;

const SectionTitle = styled.h3`
  margin: 0 0 1.5rem 0;
  color: #FFD700;
  font-size: 1.3rem;
`;

const AchievementGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
`;

const AchievementCard = styled.div<{ rarity: string; locked?: boolean }>`
  background: ${props => props.locked ?
    'rgba(255, 255, 255, 0.05)' :
    'rgba(255, 255, 255, 0.1)'
  };
  border: 2px solid ${props => {
    if (props.locked) return 'rgba(255, 255, 255, 0.1)';
    switch (props.rarity) {
      case 'common': return '#9E9E9E40';
      case 'rare': return '#2196F340';
      case 'epic': return '#9C27B040';
      case 'legendary': return '#FF980040';
      default: return 'rgba(255, 255, 255, 0.2)';
    }
  }};
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  opacity: ${props => props.locked ? 0.6 : 1};

  &:hover {
    transform: translateY(-2px);
    background: ${props => props.locked ?
      'rgba(255, 255, 255, 0.08)' :
      'rgba(255, 255, 255, 0.15)'
    };
  }
`;

const AchievementIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const AchievementName = styled.h4`
  margin: 0 0 0.5rem 0;
  color: white;
  font-size: 1.1rem;
`;

const AchievementDescription = styled.p`
  margin: 0 0 1rem 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  line-height: 1.4;
`;

const AchievementMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
`;

const CategoryBadge = styled.span`
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.9);
`;

const RarityBadge = styled.span<{ color: string }>`
  background: ${props => props.color}20;
  border: 1px solid ${props => props.color}40;
  color: ${props => props.color};
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: capitalize;
`;

const UnlockedDate = styled.div`
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
`;

export default ProfilePage;
