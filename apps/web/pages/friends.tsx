import Head from "next/head";
import { useState, useEffect } from "react";
import styled from "styled-components";
import { useRouter } from "next/router";
import { useSocket } from "@/hooks/useSocket";

const FriendsPage = () => {
  const router = useRouter();
  const socket = useSocket();
  const [activeTab, setActiveTab] = useState<'friends' | 'requests' | 'invitations' | 'notifications'>('friends');
  const [searchQuery, setSearchQuery] = useState("");

  const handleBackToHome = () => {
    router.push("/");
  };

  // Загружаем данные при подключении
  useEffect(() => {
    if (socket.connected && socket.player) {
      socket.getFriends();
      socket.getFriendRequests();
      socket.getGameInvitations();
      socket.getNotifications();
    }
  }, [socket.connected, socket.player]);

  const handleAcceptFriendRequest = (requestId: string) => {
    socket.respondToFriendRequest(requestId, true);
    // Обновляем список после ответа
    setTimeout(() => {
      socket.getFriendRequests();
      socket.getFriends();
    }, 500);
  };

  const handleDeclineFriendRequest = (requestId: string) => {
    socket.respondToFriendRequest(requestId, false);
    setTimeout(() => {
      socket.getFriendRequests();
    }, 500);
  };

  const handleAcceptGameInvitation = (invitationId: string, invitation: any) => {
    socket.respondToGameInvitation(invitationId, true);
    
    // Если есть roomId, переходим в комнату
    if (invitation.roomId) {
      router.push(`/multiplayer?joinRoom=${invitation.roomId}`);
    }
    
    setTimeout(() => {
      socket.getGameInvitations();
    }, 500);
  };

  const handleDeclineGameInvitation = (invitationId: string) => {
    socket.respondToGameInvitation(invitationId, false);
    setTimeout(() => {
      socket.getGameInvitations();
    }, 500);
  };

  const handleRemoveFriend = (friendId: string) => {
    if (confirm('Вы уверены, что хотите удалить этого друга?')) {
      socket.removeFriend(friendId);
      setTimeout(() => {
        socket.getFriends();
      }, 500);
    }
  };

  const handleInviteToGame = (friendId: string, friendName: string) => {
    // Простое приглашение в дурака
    socket.sendGameInvitation(friendId, friendName, 'durak', undefined, undefined, 'Хочешь сыграть в дурака?');
    setTimeout(() => {
      socket.getGameInvitations();
    }, 500);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return '🟢';
      case 'in_game': return '🎮';
      case 'in_room': return '🏠';
      case 'spectating': return '👁️';
      case 'offline': return '⚫';
      default: return '❓';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online': return 'В сети';
      case 'in_game': return 'В игре';
      case 'in_room': return 'В комнате';
      case 'spectating': return 'Наблюдает';
      case 'offline': return 'Не в сети';
      default: return 'Неизвестно';
    }
  };

  const filteredFriends = socket.friends.filter(friend => 
    friend.playerName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const unreadNotifications = socket.notifications.filter(n => !n.read);

  return (
    <Container>
      <Head>
        <title>Друзья - Козырь Мастер</title>
        <meta name="description" content="Управление друзьями и социальными функциями" />
      </Head>

      <Header>
        <BackButton onClick={handleBackToHome}>← Назад</BackButton>
        <Title>Друзья</Title>
      </Header>

      <Main>
        {!socket.connected ? (
          <NotConnected>
            <Message>Подключитесь к серверу, чтобы управлять друзьями</Message>
            <ConnectButton onClick={() => router.push("/multiplayer")}>
              Подключиться
            </ConnectButton>
          </NotConnected>
        ) : !socket.player ? (
          <NotConnected>
            <Message>Войдите в игру, чтобы управлять друзьями</Message>
            <ConnectButton onClick={() => router.push("/multiplayer")}>
              Войти в игру
            </ConnectButton>
          </NotConnected>
        ) : (
          <>
            <TabNavigation>
              <Tab 
                $active={activeTab === 'friends'} 
                onClick={() => setActiveTab('friends')}
              >
                👥 Друзья ({socket.friends.length})
              </Tab>
              <Tab 
                $active={activeTab === 'requests'} 
                onClick={() => setActiveTab('requests')}
              >
                📨 Запросы ({socket.friendRequests.length})
              </Tab>
              <Tab 
                $active={activeTab === 'invitations'} 
                onClick={() => setActiveTab('invitations')}
              >
                🎮 Приглашения ({socket.gameInvitations.length})
              </Tab>
              <Tab 
                $active={activeTab === 'notifications'} 
                onClick={() => setActiveTab('notifications')}
              >
                🔔 Уведомления ({unreadNotifications.length})
              </Tab>
            </TabNavigation>

            <ContentArea>
              {activeTab === 'friends' && (
                <FriendsSection>
                  <SectionHeader>
                    <SectionTitle>Мои друзья</SectionTitle>
                    <SearchInput
                      type="text"
                      placeholder="Поиск друзей..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </SectionHeader>

                  {filteredFriends.length === 0 ? (
                    <EmptyState>
                      {searchQuery ? 'Друзья не найдены' : 'У вас пока нет друзей'}
                    </EmptyState>
                  ) : (
                    <FriendsList>
                      {filteredFriends.map((friend) => (
                        <FriendCard key={friend.playerId}>
                          <FriendInfo>
                            <FriendName>{friend.playerName}</FriendName>
                            <FriendStatus>
                              {getStatusIcon(friend.status)} {getStatusText(friend.status)}
                            </FriendStatus>
                            {friend.currentActivity && (
                              <FriendActivity>
                                {friend.currentActivity.name || friend.currentActivity.id}
                              </FriendActivity>
                            )}
                          </FriendInfo>
                          <FriendActions>
                            <InviteButton 
                              onClick={() => handleInviteToGame(friend.playerId, friend.playerName)}
                              disabled={friend.status === 'offline'}
                            >
                              Пригласить
                            </InviteButton>
                            <RemoveButton onClick={() => handleRemoveFriend(friend.playerId)}>
                              Удалить
                            </RemoveButton>
                          </FriendActions>
                        </FriendCard>
                      ))}
                    </FriendsList>
                  )}
                </FriendsSection>
              )}

              {activeTab === 'requests' && (
                <RequestsSection>
                  <SectionTitle>Запросы в друзья</SectionTitle>
                  
                  {socket.friendRequests.length === 0 ? (
                    <EmptyState>Нет новых запросов в друзья</EmptyState>
                  ) : (
                    <RequestsList>
                      {socket.friendRequests.map((request) => (
                        <RequestCard key={request.id}>
                          <RequestInfo>
                            <RequestName>{request.fromPlayerName}</RequestName>
                            <RequestDate>
                              {new Date(request.createdAt).toLocaleDateString()}
                            </RequestDate>
                          </RequestInfo>
                          <RequestActions>
                            <AcceptButton onClick={() => handleAcceptFriendRequest(request.id)}>
                              Принять
                            </AcceptButton>
                            <DeclineButton onClick={() => handleDeclineFriendRequest(request.id)}>
                              Отклонить
                            </DeclineButton>
                          </RequestActions>
                        </RequestCard>
                      ))}
                    </RequestsList>
                  )}
                </RequestsSection>
              )}

              {activeTab === 'invitations' && (
                <InvitationsSection>
                  <SectionTitle>Приглашения в игры</SectionTitle>
                  
                  {socket.gameInvitations.length === 0 ? (
                    <EmptyState>Нет новых приглашений в игры</EmptyState>
                  ) : (
                    <InvitationsList>
                      {socket.gameInvitations.map((invitation) => (
                        <InvitationCard key={invitation.id}>
                          <InvitationInfo>
                            <InvitationFrom>{invitation.fromPlayerName}</InvitationFrom>
                            <InvitationType>
                              {invitation.gameType === 'durak' ? '🃏 Дурак' : '🏆 Турнир'}
                            </InvitationType>
                            {invitation.roomName && (
                              <InvitationRoom>Комната: {invitation.roomName}</InvitationRoom>
                            )}
                            {invitation.message && (
                              <InvitationMessage>"{invitation.message}"</InvitationMessage>
                            )}
                            <InvitationDate>
                              {new Date(invitation.createdAt).toLocaleString()}
                            </InvitationDate>
                          </InvitationInfo>
                          <InvitationActions>
                            <AcceptButton onClick={() => handleAcceptGameInvitation(invitation.id, invitation)}>
                              Принять
                            </AcceptButton>
                            <DeclineButton onClick={() => handleDeclineGameInvitation(invitation.id)}>
                              Отклонить
                            </DeclineButton>
                          </InvitationActions>
                        </InvitationCard>
                      ))}
                    </InvitationsList>
                  )}
                </InvitationsSection>
              )}

              {activeTab === 'notifications' && (
                <NotificationsSection>
                  <SectionHeader>
                    <SectionTitle>Уведомления</SectionTitle>
                    {unreadNotifications.length > 0 && (
                      <MarkAllReadButton onClick={() => socket.markAllNotificationsAsRead()}>
                        Отметить все как прочитанные
                      </MarkAllReadButton>
                    )}
                  </SectionHeader>
                  
                  {socket.notifications.length === 0 ? (
                    <EmptyState>Нет уведомлений</EmptyState>
                  ) : (
                    <NotificationsList>
                      {socket.notifications.map((notification) => (
                        <NotificationCard 
                          key={notification.id} 
                          $read={notification.read}
                          onClick={() => !notification.read && socket.markNotificationAsRead(notification.id)}
                        >
                          <NotificationHeader>
                            <NotificationTitle>{notification.title}</NotificationTitle>
                            <NotificationDate>
                              {new Date(notification.createdAt).toLocaleString()}
                            </NotificationDate>
                          </NotificationHeader>
                          <NotificationMessage>{notification.message}</NotificationMessage>
                          {!notification.read && <UnreadIndicator />}
                        </NotificationCard>
                      ))}
                    </NotificationsList>
                  )}
                </NotificationsSection>
              )}
            </ContentArea>
          </>
        )}
      </Main>
    </Container>
  );
};

// Стилизованные компоненты
const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
`;

const Header = styled.header`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
`;

const BackButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const Title = styled.h1`
  font-size: 2rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
`;

const Main = styled.main`
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const NotConnected = styled.div`
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 3rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const Message = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
`;

const ConnectButton = styled.button`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
`;

const TabNavigation = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
`;

const Tab = styled.button<{ $active: boolean }>`
  flex: 1;
  padding: 1rem;
  border: none;
  border-radius: 8px;
  background: ${props => props.$active ?
    'linear-gradient(135deg, #4CAF50, #45a049)' :
    'transparent'
  };
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: ${props => props.$active ?
      'linear-gradient(135deg, #45a049, #4CAF50)' :
      'rgba(255, 255, 255, 0.1)'
    };
  }
`;

const ContentArea = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 2rem;
  min-height: 400px;
`;

const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const SectionTitle = styled.h2`
  margin: 0;
  color: #FFD700;
  font-size: 1.8rem;
`;

const SearchInput = styled.input`
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  width: 250px;

  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  &:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.2rem;
`;

const FriendsSection = styled.div``;

const FriendsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FriendCard = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }
`;

const FriendInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const FriendName = styled.h3`
  margin: 0;
  color: white;
  font-size: 1.3rem;
`;

const FriendStatus = styled.div`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
`;

const FriendActivity = styled.div`
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
  font-style: italic;
`;

const FriendActions = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const InviteButton = styled.button<{ disabled?: boolean }>`
  background: ${props => props.disabled ?
    'rgba(255, 255, 255, 0.1)' :
    'linear-gradient(135deg, #2196F3, #1976D2)'
  };
  border: none;
  color: ${props => props.disabled ? 'rgba(255, 255, 255, 0.5)' : 'white'};
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.3s ease;

  &:hover {
    ${props => !props.disabled && `
      background: linear-gradient(135deg, #1976D2, #2196F3);
    `}
  }
`;

const RemoveButton = styled.button`
  background: linear-gradient(135deg, #f44336, #d32f2f);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #d32f2f, #f44336);
  }
`;

const RequestsSection = styled.div``;
const RequestsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const RequestCard = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
`;

const RequestInfo = styled.div``;
const RequestName = styled.h3`
  margin: 0 0 0.5rem 0;
  color: white;
`;

const RequestDate = styled.div`
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
`;

const RequestActions = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const AcceptButton = styled.button`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #45a049, #4CAF50);
  }
`;

const DeclineButton = styled.button`
  background: linear-gradient(135deg, #f44336, #d32f2f);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #d32f2f, #f44336);
  }
`;

const InvitationsSection = styled.div``;
const InvitationsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const InvitationCard = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
`;

const InvitationInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const InvitationFrom = styled.h3`
  margin: 0;
  color: white;
`;

const InvitationType = styled.div`
  color: #FFD700;
  font-weight: 600;
`;

const InvitationRoom = styled.div`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
`;

const InvitationMessage = styled.div`
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
  font-size: 0.9rem;
`;

const InvitationDate = styled.div`
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
`;

const InvitationActions = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
`;

const NotificationsSection = styled.div``;
const NotificationsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const NotificationCard = styled.div<{ $read: boolean }>`
  background: ${props => props.$read ?
    'rgba(255, 255, 255, 0.05)' :
    'rgba(76, 175, 80, 0.1)'
  };
  border: 1px solid ${props => props.$read ?
    'rgba(255, 255, 255, 0.1)' :
    'rgba(76, 175, 80, 0.3)'
  };
  border-radius: 12px;
  padding: 1.5rem;
  cursor: ${props => props.$read ? 'default' : 'pointer'};
  position: relative;
  transition: all 0.3s ease;

  &:hover {
    ${props => !props.$read && `
      background: rgba(76, 175, 80, 0.15);
    `}
  }
`;

const NotificationHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
`;

const NotificationTitle = styled.h3`
  margin: 0;
  color: white;
  font-size: 1.1rem;
`;

const NotificationDate = styled.div`
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
`;

const NotificationMessage = styled.div`
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
`;

const UnreadIndicator = styled.div`
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 8px;
  height: 8px;
  background: #4CAF50;
  border-radius: 50%;
`;

const MarkAllReadButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

export default FriendsPage;
