import Head from "next/head";
import { useState, useEffect } from "react";
import styled from "styled-components";
import { useRouter } from "next/router";
import { useSocket } from "@/hooks/useSocket";

interface PokerRoom {
  id: string;
  name: string;
  playerCount: number;
  maxPlayers: number;
  smallBlind: number;
  bigBlind: number;
  status: 'waiting' | 'playing' | 'finished';
  isPrivate: boolean;
  players: Array<{ id: string; name: string }>;
}

interface PokerGameState {
  id: string;
  players: any[];
  communityCards: any[];
  pot: number;
  currentPlayerIndex: number;
  phase: 'preflop' | 'flop' | 'turn' | 'river' | 'showdown' | 'finished';
  currentBet: number;
  smallBlind: number;
  bigBlind: number;
}

const PokerPage = () => {
  const router = useRouter();
  const socket = useSocket();
  const [activeTab, setActiveTab] = useState<'lobby' | 'game'>('lobby');
  const [rooms, setRooms] = useState<PokerRoom[]>([]);
  const [currentRoom, setCurrentRoom] = useState<PokerRoom | null>(null);
  const [gameState, setGameState] = useState<PokerGameState | null>(null);
  const [showCreateRoom, setShowCreateRoom] = useState(false);
  const [availableActions, setAvailableActions] = useState<string[]>([]);
  const [suggestedBets, setSuggestedBets] = useState<number[]>([]);

  // Форма создания комнаты
  const [roomForm, setRoomForm] = useState({
    name: '',
    smallBlind: 5,
    bigBlind: 10,
    maxPlayers: 9,
    isPrivate: false,
    password: ''
  });

  const handleBackToHome = () => {
    router.push("/");
  };

  // Загружаем список комнат при подключении
  useEffect(() => {
    if (socket.connected) {
      socket.socketRef?.current?.emit('get_poker_rooms');
    }
  }, [socket.connected]);

  // Обработчики событий покера
  useEffect(() => {
    if (!socket.socketRef?.current) return;

    const handleRoomsList = (roomsList: PokerRoom[]) => {
      setRooms(roomsList);
    };

    const handleRoomCreated = (room: PokerRoom) => {
      setCurrentRoom(room);
      setActiveTab('game');
      setShowCreateRoom(false);
    };

    const handleRoomJoined = (room: PokerRoom) => {
      setCurrentRoom(room);
      setActiveTab('game');
    };

    const handleGameStarted = (state: PokerGameState) => {
      setGameState(state);
    };

    const handleGameUpdated = (state: PokerGameState) => {
      setGameState(state);
      // Обновляем доступные действия
      if (socket.player && currentRoom) {
        socket.socketRef?.current?.emit('get_poker_available_actions', { roomId: currentRoom.id });
      }
    };

    const handleAvailableActions = (data: { actions: string[]; suggestedBets: number[] }) => {
      setAvailableActions(data.actions);
      setSuggestedBets(data.suggestedBets);
    };

    const handlePokerError = (error: { message: string }) => {
      alert(`Ошибка покера: ${error.message}`);
    };

    socket.socketRef.current.on('poker_rooms_list', handleRoomsList);
    socket.socketRef.current.on('poker_room_created', handleRoomCreated);
    socket.socketRef.current.on('poker_room_joined', handleRoomJoined);
    socket.socketRef.current.on('poker_game_started', handleGameStarted);
    socket.socketRef.current.on('poker_game_updated', handleGameUpdated);
    socket.socketRef.current.on('poker_available_actions', handleAvailableActions);
    socket.socketRef.current.on('poker_error', handlePokerError);

    return () => {
      socket.socketRef.current?.off('poker_rooms_list', handleRoomsList);
      socket.socketRef.current?.off('poker_room_created', handleRoomCreated);
      socket.socketRef.current?.off('poker_room_joined', handleRoomJoined);
      socket.socketRef.current?.off('poker_game_started', handleGameStarted);
      socket.socketRef.current?.off('poker_game_updated', handleGameUpdated);
      socket.socketRef.current?.off('poker_available_actions', handleAvailableActions);
      socket.socketRef.current?.off('poker_error', handlePokerError);
    };
  }, [socket.socketRef, currentRoom, socket.player]);

  const handleCreateRoom = () => {
    if (!socket.connected || !socket.player) {
      alert('Подключитесь к серверу');
      return;
    }

    socket.socketRef?.current?.emit('create_poker_room', roomForm);
  };

  const handleJoinRoom = (roomId: string) => {
    if (!socket.connected || !socket.player) {
      alert('Подключитесь к серверу');
      return;
    }

    socket.socketRef?.current?.emit('join_poker_room', { roomId });
  };

  const handleStartGame = () => {
    if (!currentRoom) return;
    socket.socketRef?.current?.emit('start_poker_game', { roomId: currentRoom.id });
  };

  const handlePokerAction = (actionType: string, amount?: number) => {
    if (!currentRoom) return;
    
    const action = {
      type: actionType,
      amount,
      timestamp: new Date()
    };

    socket.socketRef?.current?.emit('poker_action', {
      roomId: currentRoom.id,
      action
    });
  };

  const getCardDisplay = (card: any) => {
    if (card.suit === 'hidden') return '🂠';
    
    const suitSymbols = {
      hearts: '♥️',
      diamonds: '♦️',
      clubs: '♣️',
      spades: '♠️'
    };
    
    return `${card.rank}${suitSymbols[card.suit as keyof typeof suitSymbols] || ''}`;
  };

  const getCurrentPlayer = () => {
    if (!gameState || !gameState.players) return null;
    return gameState.players[gameState.currentPlayerIndex];
  };

  const isMyTurn = () => {
    const currentPlayer = getCurrentPlayer();
    return currentPlayer && currentPlayer.id === socket.player?.id;
  };

  return (
    <Container>
      <Head>
        <title>Покер - Козырь Мастер</title>
        <meta name="description" content="Играйте в Texas Hold'em покер онлайн" />
      </Head>

      <Header>
        <BackButton onClick={handleBackToHome}>← Назад</BackButton>
        <Title>Покер (Texas Hold'em)</Title>
      </Header>

      <Main>
        {!socket.connected ? (
          <NotConnected>
            <Message>Подключитесь к серверу, чтобы играть в покер</Message>
            <ConnectButton onClick={() => router.push("/multiplayer")}>
              Подключиться
            </ConnectButton>
          </NotConnected>
        ) : !socket.player ? (
          <NotConnected>
            <Message>Войдите в игру, чтобы играть в покер</Message>
            <ConnectButton onClick={() => router.push("/multiplayer")}>
              Войти в игру
            </ConnectButton>
          </NotConnected>
        ) : (
          <>
            <TabNavigation>
              <Tab 
                $active={activeTab === 'lobby'} 
                onClick={() => setActiveTab('lobby')}
              >
                🏛️ Лобби
              </Tab>
              <Tab 
                $active={activeTab === 'game'} 
                onClick={() => setActiveTab('game')}
                disabled={!currentRoom}
              >
                🃏 Игра
              </Tab>
            </TabNavigation>

            {activeTab === 'lobby' && (
              <LobbySection>
                <LobbyHeader>
                  <SectionTitle>Покерные комнаты</SectionTitle>
                  <CreateRoomButton onClick={() => setShowCreateRoom(true)}>
                    Создать комнату
                  </CreateRoomButton>
                </LobbyHeader>

                {showCreateRoom && (
                  <CreateRoomForm>
                    <FormTitle>Создание покерной комнаты</FormTitle>
                    <FormRow>
                      <FormField>
                        <label>Название комнаты:</label>
                        <input
                          type="text"
                          value={roomForm.name}
                          onChange={(e) => setRoomForm({...roomForm, name: e.target.value})}
                          placeholder="Моя покерная комната"
                        />
                      </FormField>
                    </FormRow>
                    <FormRow>
                      <FormField>
                        <label>Малый блайнд:</label>
                        <input
                          type="number"
                          value={roomForm.smallBlind}
                          onChange={(e) => setRoomForm({...roomForm, smallBlind: parseInt(e.target.value)})}
                          min="1"
                        />
                      </FormField>
                      <FormField>
                        <label>Большой блайнд:</label>
                        <input
                          type="number"
                          value={roomForm.bigBlind}
                          onChange={(e) => setRoomForm({...roomForm, bigBlind: parseInt(e.target.value)})}
                          min="2"
                        />
                      </FormField>
                    </FormRow>
                    <FormRow>
                      <FormField>
                        <label>Максимум игроков:</label>
                        <select
                          value={roomForm.maxPlayers}
                          onChange={(e) => setRoomForm({...roomForm, maxPlayers: parseInt(e.target.value)})}
                        >
                          <option value={2}>2</option>
                          <option value={6}>6</option>
                          <option value={9}>9</option>
                        </select>
                      </FormField>
                    </FormRow>
                    <FormActions>
                      <CancelButton onClick={() => setShowCreateRoom(false)}>
                        Отмена
                      </CancelButton>
                      <CreateButton onClick={handleCreateRoom}>
                        Создать
                      </CreateButton>
                    </FormActions>
                  </CreateRoomForm>
                )}

                <RoomsList>
                  {rooms.length === 0 ? (
                    <EmptyState>
                      <EmptyIcon>🃏</EmptyIcon>
                      <EmptyText>Нет доступных покерных комнат</EmptyText>
                      <EmptySubtext>Создайте новую комнату, чтобы начать играть</EmptySubtext>
                    </EmptyState>
                  ) : (
                    rooms.map((room) => (
                      <RoomCard key={room.id}>
                        <RoomHeader>
                          <RoomName>{room.name}</RoomName>
                          <RoomStatus $status={room.status}>
                            {room.status === 'waiting' ? 'Ожидание' : 
                             room.status === 'playing' ? 'Игра' : 'Завершена'}
                          </RoomStatus>
                        </RoomHeader>
                        <RoomInfo>
                          <InfoItem>
                            <InfoLabel>Игроки:</InfoLabel>
                            <InfoValue>{room.playerCount}/{room.maxPlayers}</InfoValue>
                          </InfoItem>
                          <InfoItem>
                            <InfoLabel>Блайнды:</InfoLabel>
                            <InfoValue>{room.smallBlind}/{room.bigBlind}</InfoValue>
                          </InfoItem>
                        </RoomInfo>
                        <RoomPlayers>
                          {room.players.map((player, index) => (
                            <PlayerChip key={player.id}>
                              {player.name}
                            </PlayerChip>
                          ))}
                        </RoomPlayers>
                        <JoinButton 
                          onClick={() => handleJoinRoom(room.id)}
                          disabled={room.playerCount >= room.maxPlayers || room.status !== 'waiting'}
                        >
                          {room.status === 'waiting' ? 'Присоединиться' : 'Наблюдать'}
                        </JoinButton>
                      </RoomCard>
                    ))
                  )}
                </RoomsList>
              </LobbySection>
            )}

            {activeTab === 'game' && currentRoom && (
              <GameSection>
                <GameHeader>
                  <GameTitle>{currentRoom.name}</GameTitle>
                  {currentRoom.status === 'waiting' && (
                    <StartGameButton onClick={handleStartGame}>
                      Начать игру
                    </StartGameButton>
                  )}
                </GameHeader>

                {gameState ? (
                  <PokerTable>
                    <CommunityCards>
                      <CardsTitle>Общие карты</CardsTitle>
                      <Cards>
                        {gameState.communityCards.map((card, index) => (
                          <Card key={index}>
                            {getCardDisplay(card)}
                          </Card>
                        ))}
                      </Cards>
                    </CommunityCards>

                    <PotInfo>
                      <PotLabel>Банк:</PotLabel>
                      <PotAmount>{gameState.pot}</PotAmount>
                    </PotInfo>

                    <PlayersArea>
                      {gameState.players.map((player, index) => (
                        <PlayerCard 
                          key={player.id} 
                          $isActive={index === gameState.currentPlayerIndex}
                          $isMe={player.id === socket.player?.id}
                        >
                          <PlayerName>{player.name}</PlayerName>
                          <PlayerChips>Фишки: {player.chips}</PlayerChips>
                          <PlayerBet>Ставка: {player.currentBet}</PlayerBet>
                          <PlayerCards>
                            {player.holeCards.map((card: any, cardIndex: number) => (
                              <PlayerCard key={cardIndex}>
                                {getCardDisplay(card)}
                              </PlayerCard>
                            ))}
                          </PlayerCards>
                          {player.lastAction && (
                            <PlayerAction>{player.lastAction.type}</PlayerAction>
                          )}
                        </PlayerCard>
                      ))}
                    </PlayersArea>

                    {isMyTurn() && (
                      <ActionPanel>
                        <ActionsTitle>Ваш ход</ActionsTitle>
                        <Actions>
                          {availableActions.map((action) => (
                            <ActionButton 
                              key={action}
                              onClick={() => handlePokerAction(action)}
                            >
                              {action === 'fold' ? 'Сброс' :
                               action === 'check' ? 'Чек' :
                               action === 'call' ? 'Колл' :
                               action === 'bet' ? 'Ставка' :
                               action === 'raise' ? 'Рейз' :
                               action === 'all_in' ? 'Ва-банк' : action}
                            </ActionButton>
                          ))}
                        </Actions>
                        {suggestedBets.length > 0 && (
                          <SuggestedBets>
                            <BetsTitle>Рекомендуемые ставки:</BetsTitle>
                            <BetButtons>
                              {suggestedBets.map((amount) => (
                                <BetButton 
                                  key={amount}
                                  onClick={() => handlePokerAction('bet', amount)}
                                >
                                  {amount}
                                </BetButton>
                              ))}
                            </BetButtons>
                          </SuggestedBets>
                        )}
                      </ActionPanel>
                    )}
                  </PokerTable>
                ) : (
                  <WaitingArea>
                    <WaitingMessage>
                      Ожидание начала игры...
                    </WaitingMessage>
                    <PlayersList>
                      {currentRoom.players.map((player) => (
                        <WaitingPlayer key={player.id}>
                          {player.name}
                        </WaitingPlayer>
                      ))}
                    </PlayersList>
                  </WaitingArea>
                )}
              </GameSection>
            )}
          </>
        )}
      </Main>
    </Container>
  );
};

// Стилизованные компоненты
const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%);
  color: white;
`;

const Header = styled.header`
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
`;

const BackButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 2rem;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const Title = styled.h1`
  font-size: 2.5rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
`;

const Main = styled.main`
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
`;

const NotConnected = styled.div`
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 3rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const Message = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
`;

const ConnectButton = styled.button`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
`;

const TabNavigation = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
`;

const Tab = styled.button<{ $active: boolean; disabled?: boolean }>`
  background: ${props => props.$active ?
    'linear-gradient(135deg, #4CAF50, #45a049)' :
    'rgba(255, 255, 255, 0.1)'
  };
  border: 1px solid ${props => props.$active ?
    'rgba(76, 175, 80, 0.3)' :
    'rgba(255, 255, 255, 0.2)'
  };
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  opacity: ${props => props.disabled ? 0.5 : 1};
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background: ${props => props.$active ?
      'linear-gradient(135deg, #45a049, #4CAF50)' :
      'rgba(255, 255, 255, 0.2)'
    };
  }
`;

const LobbySection = styled.div`
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const LobbyHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.8rem;
  margin: 0;
  color: #FFD700;
`;

const CreateRoomButton = styled.button`
  background: linear-gradient(135deg, #FF6B35, #F7931E);
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
`;

const CreateRoomForm = styled.div`
  background: rgba(0, 0, 0, 0.2);
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const FormTitle = styled.h3`
  margin: 0 0 1.5rem 0;
  color: #FFD700;
`;

const FormRow = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
`;

const FormField = styled.div`
  flex: 1;

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  input, select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;

    &::placeholder {
      color: rgba(255, 255, 255, 0.5);
    }
  }
`;

const FormActions = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
`;

const CancelButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const CreateButton = styled.button`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
`;

const RoomsList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
`;

const EmptyState = styled.div`
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
`;

const EmptyIcon = styled.div`
  font-size: 4rem;
  margin-bottom: 1rem;
`;

const EmptyText = styled.h3`
  margin: 0 0 0.5rem 0;
  color: rgba(255, 255, 255, 0.8);
`;

const EmptySubtext = styled.p`
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
`;

const RoomCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }
`;

const RoomHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

const RoomName = styled.h3`
  margin: 0;
  color: white;
  font-size: 1.2rem;
`;

const RoomStatus = styled.span<{ $status: string }>`
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  background: ${props =>
    props.$status === 'waiting' ? '#4CAF50' :
    props.$status === 'playing' ? '#FF9800' : '#9E9E9E'
  };
  color: white;
`;

const RoomInfo = styled.div`
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
`;

const InfoItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
`;

const InfoLabel = styled.span`
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
`;

const InfoValue = styled.span`
  font-weight: 600;
  color: #FFD700;
`;

const RoomPlayers = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
`;

const PlayerChip = styled.span`
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  color: white;
`;

const JoinButton = styled.button`
  width: 100%;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 0.75rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
  }

  &:disabled {
    background: rgba(255, 255, 255, 0.1);
    cursor: not-allowed;
    opacity: 0.5;
  }
`;

const GameSection = styled.div`
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const GameHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const GameTitle = styled.h2`
  margin: 0;
  color: #FFD700;
`;

const StartGameButton = styled.button`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
`;

const PokerTable = styled.div`
  background: radial-gradient(ellipse at center, #2d5a3d 0%, #1a4a2e 100%);
  border: 8px solid #8B4513;
  border-radius: 50%;
  padding: 3rem;
  min-height: 600px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

const CommunityCards = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`;

const CardsTitle = styled.h3`
  margin: 0 0 1rem 0;
  color: #FFD700;
`;

const Cards = styled.div`
  display: flex;
  gap: 0.5rem;
  justify-content: center;
`;

const Card = styled.div`
  background: white;
  color: black;
  padding: 1rem 0.75rem;
  border-radius: 8px;
  font-weight: bold;
  font-size: 1.2rem;
  min-width: 60px;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
`;

const PotInfo = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`;

const PotLabel = styled.span`
  color: rgba(255, 255, 255, 0.8);
  margin-right: 0.5rem;
`;

const PotAmount = styled.span`
  color: #FFD700;
  font-size: 1.5rem;
  font-weight: bold;
`;

const PlayersArea = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  width: 100%;
  margin-bottom: 2rem;
`;

const PlayerCard = styled.div<{ $isActive?: boolean; $isMe?: boolean }>`
  background: ${props =>
    props.$isMe ? 'rgba(76, 175, 80, 0.2)' :
    props.$isActive ? 'rgba(255, 215, 0, 0.2)' :
    'rgba(255, 255, 255, 0.1)'
  };
  border: 2px solid ${props =>
    props.$isMe ? '#4CAF50' :
    props.$isActive ? '#FFD700' :
    'rgba(255, 255, 255, 0.2)'
  };
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
`;

const PlayerName = styled.div`
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: white;
`;

const PlayerChips = styled.div`
  color: #FFD700;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
`;

const PlayerBet = styled.div`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
`;

const PlayerCards = styled.div`
  display: flex;
  gap: 0.25rem;
  justify-content: center;
  margin-bottom: 0.5rem;
`;

const PlayerAction = styled.div`
  background: rgba(0, 0, 0, 0.3);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  color: #FFD700;
`;

const ActionPanel = styled.div`
  background: rgba(0, 0, 0, 0.3);
  padding: 1.5rem;
  border-radius: 12px;
  border: 2px solid #4CAF50;
`;

const ActionsTitle = styled.h3`
  margin: 0 0 1rem 0;
  color: #4CAF50;
  text-align: center;
`;

const Actions = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1rem;
`;

const ActionButton = styled.button`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
`;

const SuggestedBets = styled.div`
  text-align: center;
`;

const BetsTitle = styled.h4`
  margin: 0 0 0.5rem 0;
  color: rgba(255, 255, 255, 0.8);
`;

const BetButtons = styled.div`
  display: flex;
  gap: 0.5rem;
  justify-content: center;
`;

const BetButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const WaitingArea = styled.div`
  text-align: center;
  padding: 3rem;
`;

const WaitingMessage = styled.h3`
  margin: 0 0 2rem 0;
  color: rgba(255, 255, 255, 0.8);
`;

const PlayersList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
`;

const WaitingPlayer = styled.div`
  background: rgba(255, 255, 255, 0.1);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  color: white;
`;

export default PokerPage;
