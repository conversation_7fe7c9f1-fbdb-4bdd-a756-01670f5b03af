import Head from "next/head";
import { useState, useEffect } from "react";
import styled from "styled-components";
import { useRouter } from "next/router";
import { useSocket } from "@/hooks/useSocket";

const MultiplayerPage = () => {
  const router = useRouter();
  const socket = useSocket();
  const [playerName, setPlayerName] = useState("");
  const [roomName, setRoomName] = useState("");
  const [chatMessage, setChatMessage] = useState("");
  const [showCreateRoom, setShowCreateRoom] = useState(false);

  const handleBackToHome = () => {
    socket.disconnect();
    router.push("/");
  };

  const handleRegisterPlayer = () => {
    if (playerName.trim()) {
      socket.registerPlayer(playerName.trim());
    }
  };

  const handleCreateRoom = () => {
    if (roomName.trim()) {
      socket.createRoom(roomName.trim(), 2);
      setShowCreateRoom(false);
      setRoomName("");
    }
  };

  const handleJoinRoom = (roomId: string) => {
    socket.joinRoom(roomId);
  };

  const handleLeaveRoom = () => {
    if (socket.currentRoom) {
      socket.leaveRoom(socket.currentRoom.id);
    }
  };

  const handleStartGame = () => {
    if (socket.currentRoom) {
      socket.startGame(socket.currentRoom.id);
    }
  };

  const handleSendMessage = () => {
    if (chatMessage.trim() && socket.currentRoom) {
      socket.sendChatMessage(socket.currentRoom.id, chatMessage.trim());
      setChatMessage("");
    }
  };

  const handleRefreshRooms = () => {
    socket.getRooms();
  };

  // Автоматически обновляем список комнат
  useEffect(() => {
    if (socket.connected && socket.player) {
      socket.getRooms();
    }
  }, [socket.connected, socket.player]);

  return (
    <Container>
      <Head>
        <title>Многопользовательская игра - Козырь Мастер</title>
        <meta name="description" content="Играйте в Дурака с другими игроками онлайн" />
      </Head>

      <Header>
        <BackButton onClick={handleBackToHome}>← Назад</BackButton>
        <Title>Многопользовательская игра</Title>
        <ConnectionStatus $connected={socket.connected}>
          {socket.connected ? "🟢 Подключен" : "🔴 Отключен"}
        </ConnectionStatus>
      </Header>

      <Main>
        {socket.error && (
          <ErrorMessage>
            {socket.error}
            <CloseButton onClick={socket.clearError}>×</CloseButton>
          </ErrorMessage>
        )}

        {!socket.player ? (
          // Регистрация игрока
          <Section>
            <SectionTitle>Войти в игру</SectionTitle>
            <InputGroup>
              <Input
                type="text"
                placeholder="Введите ваше имя"
                value={playerName}
                onChange={(e) => setPlayerName(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleRegisterPlayer()}
              />
              <Button onClick={handleRegisterPlayer} disabled={!playerName.trim()}>
                Войти
              </Button>
            </InputGroup>
          </Section>
        ) : !socket.currentRoom ? (
          // Список комнат
          <TwoColumnLayout>
            <Section>
              <SectionHeader>
                <SectionTitle>Комнаты ({socket.rooms.length})</SectionTitle>
                <ButtonGroup>
                  <SmallButton onClick={handleRefreshRooms}>🔄</SmallButton>
                  <Button onClick={() => setShowCreateRoom(true)}>
                    Создать комнату
                  </Button>
                </ButtonGroup>
              </SectionHeader>

              {showCreateRoom && (
                <CreateRoomForm>
                  <Input
                    type="text"
                    placeholder="Название комнаты"
                    value={roomName}
                    onChange={(e) => setRoomName(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleCreateRoom()}
                  />
                  <ButtonGroup>
                    <Button onClick={handleCreateRoom} disabled={!roomName.trim()}>
                      Создать
                    </Button>
                    <SecondaryButton onClick={() => setShowCreateRoom(false)}>
                      Отмена
                    </SecondaryButton>
                  </ButtonGroup>
                </CreateRoomForm>
              )}

              <RoomsList>
                {socket.rooms.length === 0 ? (
                  <EmptyState>Нет доступных комнат</EmptyState>
                ) : (
                  socket.rooms.map((room) => (
                    <RoomCard key={room.id}>
                      <RoomInfo>
                        <RoomName>{room.name}</RoomName>
                        <RoomDetails>
                          Владелец: {room.ownerName} |
                          Игроки: {room.playerCount}/{room.maxPlayers} |
                          Статус: {room.status === 'waiting' ? 'Ожидание' :
                                   room.status === 'playing' ? 'Играют' : 'Завершена'}
                        </RoomDetails>
                      </RoomInfo>
                      <JoinButton
                        onClick={() => handleJoinRoom(room.id)}
                        disabled={room.status !== 'waiting' || room.playerCount >= room.maxPlayers}
                      >
                        {room.status !== 'waiting' ? 'Недоступна' :
                         room.playerCount >= room.maxPlayers ? 'Полная' : 'Войти'}
                      </JoinButton>
                    </RoomCard>
                  ))
                )}
              </RoomsList>
            </Section>

            <Section>
              <SectionTitle>Добро пожаловать, {socket.player.name}!</SectionTitle>
              <InfoText>
                Выберите комнату из списка или создайте новую, чтобы начать игру.
              </InfoText>
              <InfoText>
                В многопользовательском режиме вы можете играть с другими игроками в реальном времени.
              </InfoText>
            </Section>
          </TwoColumnLayout>
        ) : (
          // В комнате
          <TwoColumnLayout>
            <Section>
              <SectionHeader>
                <SectionTitle>Комната: {socket.currentRoom.name}</SectionTitle>
                <ButtonGroup>
                  {socket.currentRoom.ownerName === socket.player.name &&
                   socket.currentRoom.status === 'waiting' && (
                    <Button onClick={handleStartGame}>
                      Начать игру
                    </Button>
                  )}
                  <SecondaryButton onClick={handleLeaveRoom}>
                    Покинуть
                  </SecondaryButton>
                </ButtonGroup>
              </SectionHeader>

              <RoomStatus>
                <StatusItem>
                  <strong>Статус:</strong> {
                    socket.currentRoom.status === 'waiting' ? 'Ожидание игроков' :
                    socket.currentRoom.status === 'playing' ? 'Игра идет' : 'Игра завершена'
                  }
                </StatusItem>
                <StatusItem>
                  <strong>Игроки:</strong> {socket.currentRoom.playerCount}/{socket.currentRoom.maxPlayers}
                </StatusItem>
                <StatusItem>
                  <strong>Владелец:</strong> {socket.currentRoom.ownerName}
                </StatusItem>
              </RoomStatus>

              {socket.gameState && (
                <GameInfo>
                  <h4>Состояние игры</h4>
                  <pre>{JSON.stringify(socket.gameState, null, 2)}</pre>
                </GameInfo>
              )}
            </Section>

            <Section>
              <SectionTitle>Чат</SectionTitle>
              <ChatContainer>
                <ChatMessages>
                  {socket.chatMessages.map((msg) => (
                    <ChatMessage key={msg.id} $type={msg.type}>
                      <MessageHeader>
                        <MessageAuthor $type={msg.type}>
                          {msg.playerName}
                        </MessageAuthor>
                        <MessageTime>
                          {new Date(msg.timestamp).toLocaleTimeString()}
                        </MessageTime>
                      </MessageHeader>
                      <MessageText>{msg.message}</MessageText>
                    </ChatMessage>
                  ))}
                </ChatMessages>
                <ChatInput>
                  <Input
                    type="text"
                    placeholder="Введите сообщение..."
                    value={chatMessage}
                    onChange={(e) => setChatMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  />
                  <Button onClick={handleSendMessage} disabled={!chatMessage.trim()}>
                    Отправить
                  </Button>
                </ChatInput>
              </ChatContainer>
            </Section>
          </TwoColumnLayout>
        )}
      </Main>
    </Container>
  );
};

// Стилизованные компоненты
const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  display: flex;
  flex-direction: column;
`;

const Header = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
`;

const BackButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const Title = styled.h1`
  font-size: 2rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
`;

const ConnectionStatus = styled.div<{ $connected: boolean }>`
  padding: 0.5rem 1rem;
  border-radius: 8px;
  background: ${props => props.$connected ?
    'rgba(76, 175, 80, 0.2)' :
    'rgba(244, 67, 54, 0.2)'
  };
  border: 1px solid ${props => props.$connected ?
    'rgba(76, 175, 80, 0.3)' :
    'rgba(244, 67, 54, 0.3)'
  };
  font-weight: 600;
`;

const Main = styled.main`
  flex: 1;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const ErrorMessage = styled.div`
  background: rgba(244, 67, 54, 0.2);
  border: 1px solid rgba(244, 67, 54, 0.3);
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const Section = styled.section`
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  margin: 0 0 1rem 0;
  color: #FFD700;
`;

const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
`;

const TwoColumnLayout = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
`;

const InputGroup = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
`;

const Input = styled.input`
  flex: 1;
  min-width: 200px;
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;

  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  &:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  }
`;

const Button = styled.button<{ disabled?: boolean }>`
  background: ${props => props.disabled ?
    'rgba(255, 255, 255, 0.1)' :
    'linear-gradient(135deg, #4CAF50, #45a049)'
  };
  color: ${props => props.disabled ? 'rgba(255, 255, 255, 0.5)' : 'white'};
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.3s ease;
  white-space: nowrap;

  &:hover {
    ${props => !props.disabled && `
      background: linear-gradient(135deg, #45a049, #4CAF50);
      transform: translateY(-2px);
    `}
  }
`;

const SecondaryButton = styled(Button)`
  background: linear-gradient(135deg, #2196F3, #1976D2);

  &:hover {
    background: linear-gradient(135deg, #1976D2, #2196F3);
  }
`;

const SmallButton = styled(Button)`
  padding: 0.5rem;
  min-width: auto;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
`;

const CreateRoomForm = styled.div`
  background: rgba(0, 0, 0, 0.2);
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const RoomsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 2rem;
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
`;

const RoomCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }
`;

const RoomInfo = styled.div`
  flex: 1;
`;

const RoomName = styled.h3`
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
`;

const RoomDetails = styled.p`
  margin: 0;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
`;

const JoinButton = styled(Button)`
  margin-left: 1rem;
  padding: 0.5rem 1rem;
`;

const InfoText = styled.p`
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 1rem;
`;

const RoomStatus = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
`;

const StatusItem = styled.div`
  color: rgba(255, 255, 255, 0.9);
`;

const GameInfo = styled.div`
  background: rgba(0, 0, 0, 0.2);
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;

  h4 {
    margin: 0 0 1rem 0;
    color: #FFD700;
  }

  pre {
    background: rgba(0, 0, 0, 0.3);
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
  }
`;

const ChatContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 400px;
`;

const ChatMessages = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const ChatMessage = styled.div<{ $type: string }>`
  padding: 0.5rem;
  border-radius: 4px;
  background: ${props => {
    switch (props.$type) {
      case 'system': return 'rgba(255, 193, 7, 0.1)';
      case 'game': return 'rgba(76, 175, 80, 0.1)';
      default: return 'rgba(255, 255, 255, 0.05)';
    }
  }};
  border-left: 3px solid ${props => {
    switch (props.$type) {
      case 'system': return '#FFC107';
      case 'game': return '#4CAF50';
      default: return 'rgba(255, 255, 255, 0.2)';
    }
  }};
`;

const MessageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
`;

const MessageAuthor = styled.span<{ $type: string }>`
  font-weight: 600;
  font-size: 0.9rem;
  color: ${props => {
    switch (props.$type) {
      case 'system': return '#FFC107';
      case 'game': return '#4CAF50';
      default: return '#2196F3';
    }
  }};
`;

const MessageTime = styled.span`
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
`;

const MessageText = styled.div`
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
`;

const ChatInput = styled.div`
  display: flex;
  gap: 0.5rem;
`;

export default MultiplayerPage;