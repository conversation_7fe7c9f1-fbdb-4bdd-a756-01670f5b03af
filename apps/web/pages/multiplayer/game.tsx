import Head from "next/head";
import { useState, useEffect } from "react";
import styled from "styled-components";
import { useRouter } from "next/router";
import { useSocket } from "@/hooks/useSocket";
import AnimatedCard from "@/components/Card/AnimatedCard";

const MultiplayerGamePage = () => {
  const router = useRouter();
  const socket = useSocket();
  const [selectedCard, setSelectedCard] = useState<number | null>(null);
  const [chatMessage, setChatMessage] = useState("");

  const handleBackToLobby = () => {
    if (socket.currentRoom) {
      socket.leaveRoom(socket.currentRoom.id);
    }
    router.push("/multiplayer");
  };

  const handleCardClick = (cardIndex: number) => {
    if (!socket.gameState || !socket.isGameActive) return;
    
    const currentPlayer = socket.gameState.gameState.players[socket.gameState.gameState.currentPlayerIndex];
    if (currentPlayer.id !== socket.player?.id) return;

    setSelectedCard(cardIndex);
  };

  const handleAttack = () => {
    if (selectedCard !== null && socket.currentRoom) {
      socket.makeMove(socket.currentRoom.id, 'attack', selectedCard);
      setSelectedCard(null);
    }
  };

  const handleDefend = () => {
    if (selectedCard !== null && socket.currentRoom) {
      socket.makeMove(socket.currentRoom.id, 'defend', selectedCard);
      setSelectedCard(null);
    }
  };

  const handleTake = () => {
    if (socket.currentRoom) {
      socket.makeMove(socket.currentRoom.id, 'take');
      setSelectedCard(null);
    }
  };

  const handlePass = () => {
    if (socket.currentRoom) {
      socket.makeMove(socket.currentRoom.id, 'pass');
      setSelectedCard(null);
    }
  };

  const handleSendMessage = () => {
    if (chatMessage.trim() && socket.currentRoom) {
      socket.sendChatMessage(socket.currentRoom.id, chatMessage.trim());
      setChatMessage("");
    }
  };

  // Если нет активной игры, перенаправляем в лобби
  useEffect(() => {
    if (!socket.currentRoom || !socket.gameState) {
      router.push("/multiplayer");
    }
  }, [socket.currentRoom, socket.gameState, router]);

  if (!socket.currentRoom || !socket.gameState || !socket.player) {
    return <LoadingContainer>Загрузка игры...</LoadingContainer>;
  }

  const gameState = socket.gameState.gameState;
  const humanPlayer = gameState.players.find((p: any) => p.id === socket.player?.id);
  const otherPlayers = gameState.players.filter((p: any) => p.id !== socket.player?.id);
  const isHumanTurn = gameState.players[gameState.currentPlayerIndex]?.id === socket.player?.id;

  return (
    <Container>
      <Head>
        <title>Многопользовательская игра - Дурак</title>
        <meta name="description" content="Играйте в Дурака онлайн" />
      </Head>

      <GameHeader>
        <BackButton onClick={handleBackToLobby}>← В лобби</BackButton>
        <GameTitle>Комната: {socket.currentRoom.name}</GameTitle>
        <GameStatus>
          {socket.gameState.status === 'playing' ? 
            (isHumanTurn ? "Ваш ход" : "Ход противника") : 
            "Игра завершена"
          }
        </GameStatus>
      </GameHeader>

      <GameLayout>
        <GameBoard>
          {/* Карты противников */}
          <OpponentsArea>
            {otherPlayers.map((player: any, index: number) => (
              <OpponentSection key={player.id}>
                <PlayerInfo>
                  <PlayerName>{player.name}</PlayerName>
                  <CardCount>Карт: {player.handSize || player.hand?.length || 0}</CardCount>
                </PlayerInfo>
                <OpponentCards>
                  {Array.from({ length: player.handSize || player.hand?.length || 0 }).map((_, cardIndex) => (
                    <AnimatedCard
                      key={cardIndex}
                      rank="?"
                      suit="?"
                      isFlipped={true}
                      size="small"
                      disabled={true}
                    />
                  ))}
                </OpponentCards>
              </OpponentSection>
            ))}
          </OpponentsArea>

          {/* Центральная область */}
          <CenterArea>
            <GameInfo>
              <InfoItem>
                <strong>Козырь:</strong> {gameState.trumpCard ? 
                  `${gameState.trumpCard.rank} ${gameState.trumpCard.suit}` : 
                  "Неизвестно"
                }
              </InfoItem>
              <InfoItem>
                <strong>Колода:</strong> {gameState.deck?.length || 0}
              </InfoItem>
              <InfoItem>
                <strong>Ходы:</strong> {socket.gameState.moveCount || 0}
              </InfoItem>
            </GameInfo>

            {/* Стол */}
            <TableArea>
              <TableTitle>Стол</TableTitle>
              <TableCards>
                {gameState.tableCards?.map((pair: any[], pairIndex: number) => (
                  <CardPair key={pairIndex}>
                    {pair.map((card: any, cardIndex: number) => (
                      <TableCardWrapper key={cardIndex} $isDefense={cardIndex === 1}>
                        <AnimatedCard
                          rank={card.rank}
                          suit={card.suit}
                          size="medium"
                          animationType="slide"
                          disabled={true}
                        />
                      </TableCardWrapper>
                    ))}
                  </CardPair>
                ))}
                {(!gameState.tableCards || gameState.tableCards.length === 0) && (
                  <EmptyTable>Стол пуст</EmptyTable>
                )}
              </TableCards>
            </TableArea>
          </CenterArea>

          {/* Карты игрока */}
          <PlayerArea>
            <PlayerInfo>
              <PlayerName>{humanPlayer?.name}</PlayerName>
              <CardCount>Карт: {humanPlayer?.hand?.length || 0}</CardCount>
            </PlayerInfo>
            <PlayerCards>
              {humanPlayer?.hand?.map((card: any, index: number) => (
                <AnimatedCard
                  key={index}
                  rank={card.rank}
                  suit={card.suit}
                  isSelected={selectedCard === index}
                  onClick={() => handleCardClick(index)}
                  animationType={selectedCard === index ? 'glow' : 'none'}
                  size="medium"
                  disabled={!isHumanTurn}
                />
              ))}
            </PlayerCards>
          </PlayerArea>

          {/* Панель управления */}
          <ControlPanel>
            {socket.gameState.status === 'playing' ? (
              <ActionButtons>
                <ActionButton 
                  onClick={handleAttack} 
                  disabled={!isHumanTurn || selectedCard === null}
                >
                  Атаковать
                </ActionButton>
                <ActionButton 
                  onClick={handleDefend} 
                  disabled={!isHumanTurn || selectedCard === null}
                >
                  Защищаться
                </ActionButton>
                <ActionButton 
                  onClick={handleTake} 
                  disabled={!isHumanTurn}
                >
                  Взять
                </ActionButton>
                <ActionButton 
                  onClick={handlePass} 
                  disabled={!isHumanTurn}
                >
                  Пас
                </ActionButton>
              </ActionButtons>
            ) : (
              <GameEndedInfo>
                {socket.gameState.winner ? 
                  `Победитель: ${socket.gameState.winner.name}` : 
                  "Игра завершена"
                }
              </GameEndedInfo>
            )}
          </ControlPanel>
        </GameBoard>

        {/* Чат */}
        <ChatSidebar>
          <ChatTitle>Чат</ChatTitle>
          <ChatMessages>
            {socket.chatMessages.map((msg) => (
              <ChatMessage key={msg.id} $type={msg.type}>
                <MessageHeader>
                  <MessageAuthor $type={msg.type}>
                    {msg.playerName}
                  </MessageAuthor>
                  <MessageTime>
                    {new Date(msg.timestamp).toLocaleTimeString()}
                  </MessageTime>
                </MessageHeader>
                <MessageText>{msg.message}</MessageText>
              </ChatMessage>
            ))}
          </ChatMessages>
          <ChatInput>
            <Input
              type="text"
              placeholder="Введите сообщение..."
              value={chatMessage}
              onChange={(e) => setChatMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            />
            <SendButton onClick={handleSendMessage} disabled={!chatMessage.trim()}>
              ➤
            </SendButton>
          </ChatInput>
        </ChatSidebar>
      </GameLayout>
    </Container>
  );
};

// Стилизованные компоненты
const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  display: flex;
  flex-direction: column;
`;

const LoadingContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  font-size: 1.5rem;
`;

const GameHeader = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
`;

const BackButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const GameTitle = styled.h1`
  font-size: 1.5rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
`;

const GameStatus = styled.div`
  background: rgba(76, 175, 80, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 1px solid rgba(76, 175, 80, 0.3);
  font-weight: 600;
`;

const GameLayout = styled.div`
  flex: 1;
  display: flex;
  gap: 1rem;
  padding: 1rem;
`;

const GameBoard = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const OpponentsArea = styled.div`
  display: flex;
  justify-content: space-around;
  gap: 2rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
`;

const OpponentSection = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
`;

const PlayerInfo = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
`;

const PlayerName = styled.h3`
  margin: 0;
  font-size: 1.1rem;
`;

const CardCount = styled.span`
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
`;

const OpponentCards = styled.div`
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
  justify-content: center;
`;

const CenterArea = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
`;

const GameInfo = styled.div`
  display: flex;
  gap: 2rem;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
`;

const InfoItem = styled.div`
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
`;

const TableArea = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  min-height: 200px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  padding: 1rem;
`;

const TableTitle = styled.h3`
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
`;

const TableCards = styled.div`
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  flex: 1;
`;

const CardPair = styled.div`
  display: flex;
  gap: 0.25rem;
  position: relative;
`;

const TableCardWrapper = styled.div<{ $isDefense?: boolean }>`
  transform: ${props => props.$isDefense ? 'rotate(15deg) translateX(-20px)' : 'none'};
  z-index: ${props => props.$isDefense ? 2 : 1};
  transition: transform 0.3s ease;
`;

const EmptyTable = styled.div`
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
  font-size: 1.1rem;
`;

const PlayerArea = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
`;

const PlayerCards = styled.div`
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
  max-width: 100%;
`;

const ControlPanel = styled.div`
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  display: flex;
  justify-content: center;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
`;

const ActionButton = styled.button<{ disabled?: boolean }>`
  background: ${props => props.disabled ?
    'rgba(255, 255, 255, 0.1)' :
    'linear-gradient(135deg, #2196F3, #1976D2)'
  };
  border: none;
  color: ${props => props.disabled ? 'rgba(255, 255, 255, 0.5)' : 'white'};
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.3s ease;
  box-shadow: ${props => props.disabled ? 'none' : '0 4px 12px rgba(33, 150, 243, 0.3)'};

  &:hover {
    ${props => !props.disabled && `
      background: linear-gradient(135deg, #1976D2, #2196F3);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(33, 150, 243, 0.4);
    `}
  }
`;

const GameEndedInfo = styled.div`
  background: rgba(255, 193, 7, 0.2);
  border: 1px solid rgba(255, 193, 7, 0.3);
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.2rem;
  font-weight: 600;
  color: #FFD700;
`;

const ChatSidebar = styled.div`
  width: 300px;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  @media (max-width: 1024px) {
    width: 250px;
  }

  @media (max-width: 768px) {
    display: none;
  }
`;

const ChatTitle = styled.h3`
  margin: 0;
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #FFD700;
`;

const ChatMessages = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 400px;
`;

const ChatMessage = styled.div<{ $type: string }>`
  padding: 0.5rem;
  border-radius: 4px;
  background: ${props => {
    switch (props.$type) {
      case 'system': return 'rgba(255, 193, 7, 0.1)';
      case 'game': return 'rgba(76, 175, 80, 0.1)';
      default: return 'rgba(255, 255, 255, 0.05)';
    }
  }};
  border-left: 3px solid ${props => {
    switch (props.$type) {
      case 'system': return '#FFC107';
      case 'game': return '#4CAF50';
      default: return 'rgba(255, 255, 255, 0.2)';
    }
  }};
`;

const MessageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
`;

const MessageAuthor = styled.span<{ $type: string }>`
  font-weight: 600;
  font-size: 0.9rem;
  color: ${props => {
    switch (props.$type) {
      case 'system': return '#FFC107';
      case 'game': return '#4CAF50';
      default: return '#2196F3';
    }
  }};
`;

const MessageTime = styled.span`
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
`;

const MessageText = styled.div`
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  font-size: 0.9rem;
`;

const ChatInput = styled.div`
  display: flex;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
`;

const Input = styled.input`
  flex: 1;
  padding: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.9rem;

  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  &:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  }
`;

const SendButton = styled.button<{ disabled?: boolean }>`
  background: ${props => props.disabled ?
    'rgba(255, 255, 255, 0.1)' :
    'linear-gradient(135deg, #4CAF50, #45a049)'
  };
  color: ${props => props.disabled ? 'rgba(255, 255, 255, 0.5)' : 'white'};
  border: none;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.3s ease;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    ${props => !props.disabled && `
      background: linear-gradient(135deg, #45a049, #4CAF50);
    `}
  }
`;

export default MultiplayerGamePage;
