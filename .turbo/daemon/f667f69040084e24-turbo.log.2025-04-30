2025-04-30T12:08:34.884054Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("turbo.json")}
2025-04-30T12:08:34.888019Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(All)
2025-04-30T12:08:35.555680Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("turbo.json")}
2025-04-30T12:08:35.555724Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(All)
2025-04-30T12:08:35.923452Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("turbo.json")}
2025-04-30T12:08:35.923486Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(All)
2025-04-30T12:39:14.483709Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/1.cookie")}
2025-04-30T12:39:14.483771Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-30T12:39:14.867723Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/.turbo/turbo-test.log"), AnchoredSystemPathBuf("packages/core/.turbo"), AnchoredSystemPathBuf(".turbo/cookies/2.cookie")}
2025-04-30T12:39:14.867752Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-30T12:39:25.667137Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png")}
2025-04-30T12:39:25.667176Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T12:39:25.766751Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/.turbo/turbo-test.log")}
2025-04-30T12:39:25.766762Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T12:40:44.965191Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js")}
2025-04-30T12:40:44.965221Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T12:41:06.865699Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html")}
2025-04-30T12:41:06.865772Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T12:42:19.265041Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml")}
2025-04-30T12:42:19.265110Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T12:43:31.666779Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml")}
2025-04-30T12:43:31.666823Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T12:44:19.564111Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T12:44:19.564142Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T12:44:20.063503Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T12:44:20.063521Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T12:44:20.066437Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T13:00:59.733635Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html")}
2025-04-30T13:00:59.734509Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:01:46.614506Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info")}
2025-04-30T13:01:46.614563Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:01:46.673781Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html")}
2025-04-30T13:01:46.673797Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:01:46.673828Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T13:02:08.545413Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html")}
2025-04-30T13:02:08.545435Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:02:32.246615Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html")}
2025-04-30T13:02:32.246681Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:03:01.551534Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info")}
2025-04-30T13:03:01.552350Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:03:44.420835Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json")}
2025-04-30T13:03:44.441856Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:03:44.562014Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info")}
2025-04-30T13:03:44.562057Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:03:44.562322Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T13:03:44.647912Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html")}
2025-04-30T13:03:44.647928Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:03:44.748694Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html")}
2025-04-30T13:03:44.748712Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:04:14.758157Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png")}
2025-04-30T13:04:14.759433Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:04:53.980360Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json")}
2025-04-30T13:04:53.980419Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:04:54.049972Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js")}
2025-04-30T13:04:54.049989Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:04:54.050017Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T13:05:14.148861Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css")}
2025-04-30T13:05:14.148897Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:05:31.649103Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png")}
2025-04-30T13:05:31.649126Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:05:48.649481Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js")}
2025-04-30T13:05:48.649510Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:06:07.849316Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json")}
2025-04-30T13:06:07.849340Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:06:07.977689Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js")}
2025-04-30T13:06:07.981561Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:06:08.004420Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T13:06:24.850643Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js")}
2025-04-30T13:06:24.850665Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:06:36.349769Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css")}
2025-04-30T13:06:36.349793Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:06:48.551120Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info")}
2025-04-30T13:06:48.551139Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:13:04.961771Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css")}
2025-04-30T13:13:04.962755Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:29:46.905036Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info")}
2025-04-30T13:29:46.906239Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:29:46.998332Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html")}
2025-04-30T13:29:46.998340Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:29:46.998360Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T13:29:59.099465Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css")}
2025-04-30T13:29:59.099492Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:53:00.047700Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T13:53:00.048981Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:53:00.332469Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T13:53:00.332485Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:53:00.332517Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T13:54:23.439605Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html")}
2025-04-30T13:54:23.440323Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:55:08.343764Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T13:55:08.343789Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:55:08.539483Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T13:55:08.539495Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:55:08.539607Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T13:55:24.135216Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js")}
2025-04-30T13:55:24.135236Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:55:51.736488Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T13:55:51.736497Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:55:51.937790Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T13:55:51.937802Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:55:51.937821Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T13:56:43.636668Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T13:56:43.636683Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:56:43.837862Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T13:56:43.837870Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:56:43.843133Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T13:56:52.737586Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T13:56:52.737605Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:56:53.038789Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T13:56:53.038799Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:56:53.045851Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T13:57:18.637247Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/package.json")}
2025-04-30T13:57:18.637272Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:57:18.837860Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/package.json")}
2025-04-30T13:57:18.837872Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:57:18.837894Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T13:57:52.345049Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js")}
2025-04-30T13:57:52.345754Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:58:36.348364Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T13:58:36.349287Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:58:36.643187Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T13:58:36.643198Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T13:58:36.643218Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T13:59:59.346501Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png")}
2025-04-30T13:59:59.347127Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T14:14:23.587998Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T14:14:23.589345Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T14:14:23.878708Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T14:14:23.878718Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T14:14:23.878736Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T14:14:34.678718Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js")}
2025-04-30T14:14:34.678743Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T14:14:49.578773Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T14:14:49.578782Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T14:14:49.980409Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T14:14:49.980419Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T14:14:49.980441Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T14:15:06.579216Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js")}
2025-04-30T14:15:06.579237Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T14:57:02.924634Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T14:57:02.925855Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T14:57:03.314765Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T14:57:03.314775Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T14:57:03.314796Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T14:57:23.916580Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/3.cookie")}
2025-04-30T14:57:23.917250Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-30T14:57:24.015927Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/4.cookie"), AnchoredSystemPathBuf("packages/core/.turbo/turbo-test.log")}
2025-04-30T14:57:24.015938Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-30T14:57:28.715817Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/.turbo/turbo-test.log"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html")}
2025-04-30T14:57:28.715836Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T14:57:45.215794Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png")}
2025-04-30T14:57:45.215816Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:00:51.522362Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T15:00:51.522930Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:00:53.016327Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T15:00:53.016340Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:00:53.016627Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T15:01:11.421210Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js")}
2025-04-30T15:01:11.421804Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:01:45.517081Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T15:01:45.517106Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:01:47.217407Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T15:01:47.217417Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:01:47.217441Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T15:02:03.318491Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html")}
2025-04-30T15:02:03.318513Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:02:36.618633Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T15:02:36.619779Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:02:37.017619Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T15:02:37.017629Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:02:37.017648Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T15:02:51.616941Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js")}
2025-04-30T15:02:51.616973Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:03:04.517510Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T15:03:04.517524Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:03:04.817692Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T15:03:04.817704Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:03:04.817724Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T15:03:35.250280Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html")}
2025-04-30T15:03:35.250884Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:03:52.386442Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T15:03:52.386533Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:03:52.544368Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T15:03:52.544386Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:03:52.544417Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T15:04:08.244702Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js")}
2025-04-30T15:04:08.244736Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:04:28.545054Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js")}
2025-04-30T15:04:28.545090Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:04:47.045213Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html")}
2025-04-30T15:04:47.045247Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:18:47.045821Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T15:18:47.046804Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:18:47.239245Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T15:18:47.239254Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:18:47.239270Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T15:19:06.046524Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css")}
2025-04-30T15:19:06.047149Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:21:33.044862Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/types.ts.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css")}
2025-04-30T15:21:33.045586Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:22:21.338212Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T15:22:21.338245Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:22:21.538707Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T15:22:21.538716Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:22:21.538738Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T15:22:32.638433Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png")}
2025-04-30T15:22:32.638470Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:22:32.737429Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml")}
2025-04-30T15:22:32.737448Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:22:32.737483Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T15:22:54.439091Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.setup.js")}
2025-04-30T15:22:54.439128Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:22:54.562767Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.setup.js")}
2025-04-30T15:22:54.562788Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:22:54.563670Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T15:23:12.837822Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T15:23:12.837847Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:23:12.937982Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T15:23:12.937995Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:23:12.938019Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T15:23:25.538069Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info")}
2025-04-30T15:23:25.538096Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:23:44.138218Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T15:23:44.138242Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:23:44.237530Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T15:23:44.237543Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:23:44.237567Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T15:23:56.338141Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/package.json")}
2025-04-30T15:23:56.338167Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:23:56.438390Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/package.json")}
2025-04-30T15:23:56.438401Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:23:56.438438Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T15:24:44.938106Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png")}
2025-04-30T15:24:44.938139Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:25:04.337221Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T15:25:04.337253Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:25:04.638314Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T15:25:04.638325Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:25:04.638377Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T15:25:17.038286Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json")}
2025-04-30T15:25:17.038311Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:25:30.938106Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T15:25:30.938126Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:25:31.237664Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T15:25:31.237675Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T15:25:31.237697Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T15:25:44.138868Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css")}
2025-04-30T15:25:44.138890Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:49:12.342213Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T16:49:12.344531Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:49:12.623387Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T16:49:12.623397Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:49:12.632180Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T16:49:33.624957Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html")}
2025-04-30T16:49:33.624987Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:50:05.523757Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css")}
2025-04-30T16:50:05.523817Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:50:18.223106Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js")}
2025-04-30T16:50:18.223140Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:51:11.422923Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T16:51:11.422949Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:51:12.523352Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T16:51:12.523367Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:51:12.523388Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T16:51:25.668953Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js")}
2025-04-30T16:51:25.669531Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:54:06.920231Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-04-30T16:54:06.920265Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:54:07.423161Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-04-30T16:54:07.423178Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:54:07.423206Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T16:54:24.331796Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png")}
2025-04-30T16:54:24.331826Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:54:49.121209Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-04-30T16:54:49.121528Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:54:49.520403Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-04-30T16:54:49.520418Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:54:49.523713Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T16:55:01.118694Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png")}
2025-04-30T16:55:01.118717Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:56:54.425842Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-04-30T16:56:54.425877Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:56:55.016739Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-04-30T16:56:55.016761Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T16:56:55.016782Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T18:35:35.562438Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json")}
2025-04-30T18:35:35.563159Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T18:35:47.357473Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js")}
2025-04-30T18:35:47.357507Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T18:37:26.057934Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T18:37:26.057954Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T18:37:26.253684Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T18:37:26.253695Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T18:37:26.254347Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T18:37:46.853401Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js")}
2025-04-30T18:37:46.853429Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T18:49:45.563045Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-04-30T18:49:45.563819Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T18:49:46.257556Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-04-30T18:49:46.257567Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T18:49:46.257587Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T18:49:56.564178Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png")}
2025-04-30T18:49:56.564213Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T18:49:56.663479Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/clover.xml")}
2025-04-30T18:49:56.663488Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T18:49:56.663510Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T18:50:35.170488Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js")}
2025-04-30T18:50:35.170516Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T18:51:32.671188Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-04-30T18:51:32.671238Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T18:51:33.471719Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-04-30T18:51:33.471729Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T18:51:33.471751Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T18:51:45.071246Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css")}
2025-04-30T18:51:45.071275Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:31:06.330721Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/.turbo-cookie"), AnchoredSystemPathBuf(".turbo/cookies/1.cookie")}
2025-04-30T20:31:06.330733Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-30T20:31:06.732047Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/2.cookie"), AnchoredSystemPathBuf("packages/core/.turbo/turbo-test.log")}
2025-04-30T20:31:06.732099Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-30T20:31:08.930109Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css")}
2025-04-30T20:31:08.930152Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:31:09.031139Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/.turbo/turbo-test.log"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml")}
2025-04-30T20:31:09.031151Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:33:36.302774Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-04-30T20:33:36.303251Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:33:36.929276Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-04-30T20:33:36.929298Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:33:36.929597Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T20:33:48.630822Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/3.cookie")}
2025-04-30T20:33:48.631443Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-30T20:33:48.728883Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/4.cookie")}
2025-04-30T20:33:48.728893Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-30T20:33:48.828637Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/.turbo/turbo-test.log")}
2025-04-30T20:33:48.828649Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:33:51.228758Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/.turbo/turbo-test.log"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html")}
2025-04-30T20:33:51.228785Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:39:21.834021Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T20:39:21.835817Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:39:21.924782Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T20:39:21.924816Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:39:21.924909Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T20:40:25.637695Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T20:40:25.639005Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:40:41.535301Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png")}
2025-04-30T20:40:41.537342Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:41:26.725486Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-04-30T20:41:26.725531Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:41:27.331373Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-04-30T20:41:27.331421Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:41:27.347445Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T20:42:09.936448Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T20:42:09.937245Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:42:10.023641Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T20:42:10.023800Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:42:10.024011Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T20:56:19.359244Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js")}
2025-04-30T20:56:19.360803Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:56:44.860266Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T20:56:44.861818Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:56:45.747113Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T20:56:45.747158Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:56:45.749286Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T20:57:16.138451Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js")}
2025-04-30T20:57:16.138480Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:57:36.240515Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T20:57:36.240809Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:57:36.643304Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T20:57:36.643438Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:57:36.651664Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T20:59:12.337643Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js")}
2025-04-30T20:59:12.337681Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T20:59:56.047008Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info")}
2025-04-30T20:59:56.047826Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:00:10.846079Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css")}
2025-04-30T21:00:10.846123Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:00:47.237647Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js")}
2025-04-30T21:00:47.237699Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:02:16.048112Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T21:02:16.049126Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:02:16.337605Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T21:02:16.337616Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:02:16.337640Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T21:02:25.744207Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js")}
2025-04-30T21:02:25.744241Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:02:36.835992Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T21:02:36.836014Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:02:37.135925Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/jest.config.js")}
2025-04-30T21:02:37.135933Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:02:37.135945Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T21:02:58.635776Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T21:02:58.635857Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:02:58.935576Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-04-30T21:02:58.935584Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:02:58.935600Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T21:03:14.336083Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html")}
2025-04-30T21:03:14.336110Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:04:18.336022Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/package.json")}
2025-04-30T21:04:18.336759Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:04:18.535715Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/package.json")}
2025-04-30T21:04:18.535759Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:04:18.535817Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-30T21:10:03.038737Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png")}
2025-04-30T21:10:03.039211Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:11:10.326192Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml")}
2025-04-30T21:11:10.326871Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:11:53.019000Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png")}
2025-04-30T21:11:53.019026Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:12:15.218476Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css")}
2025-04-30T21:12:15.218519Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:13:24.416864Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js")}
2025-04-30T21:13:24.416889Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:13:41.517932Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js")}
2025-04-30T21:13:41.517954Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:13:57.517075Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info")}
2025-04-30T21:13:57.517099Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:14:35.716650Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js")}
2025-04-30T21:14:35.716692Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:14:50.917487Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info")}
2025-04-30T21:14:50.917509Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:15:04.715993Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css")}
2025-04-30T21:15:04.716018Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:15:17.916530Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png")}
2025-04-30T21:15:17.916560Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:15:29.816217Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json")}
2025-04-30T21:15:29.816337Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:15:40.216504Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png")}
2025-04-30T21:15:40.216561Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:15:54.516805Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css")}
2025-04-30T21:15:54.516876Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:16:13.828901Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js")}
2025-04-30T21:16:13.832752Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:16:50.740300Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js")}
2025-04-30T21:16:50.741231Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-04-30T21:17:03.815054Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/coverage/lcov-report/favicon.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.js"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/base.css"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/prettify.css"), AnchoredSystemPathBuf("packages/core/coverage/clover.xml"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/index.html"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sort-arrow-sprite.png"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/block-navigation.js"), AnchoredSystemPathBuf("packages/core/coverage/coverage-final.json"), AnchoredSystemPathBuf("packages/core/coverage/lcov.info"), AnchoredSystemPathBuf("packages/core/coverage/lcov-report/sorter.js")}
2025-04-30T21:17:03.815088Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
