2025-05-01T09:05:16.174222Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/1.cookie"), AnchoredSystemPathBuf(".turbo/cookies/.turbo-cookie")}
2025-05-01T09:05:16.174233Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-05-01T09:05:16.380074Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.turbo"), AnchoredSystemPathBuf("apps/web/.turbo/turbo-lint.log"), AnchoredSystemPathBuf("packages/core/.turbo/turbo-lint.log"), AnchoredSystemPathBuf(".turbo/cookies/2.cookie"), AnchoredSystemPathBuf(".turbo/cookies/3.cookie")}
2025-05-01T09:05:16.380088Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }, WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T09:05:18.575617Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/.turbo/turbo-lint.log")}
2025-05-01T09:05:18.575645Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T09:05:18.675616Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.turbo/turbo-lint.log")}
2025-05-01T09:05:18.675628Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-05-01T09:06:20.786526Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".eslintrc.cjs")}
2025-05-01T09:06:20.788697Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-05-01T09:06:21.078086Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".eslintrc.cjs")}
2025-05-01T09:06:21.078108Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-05-01T09:06:21.373973Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".eslintrc.cjs")}
2025-05-01T09:06:21.374063Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-05-01T09:06:21.474503Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".eslintrc.cjs")}
2025-05-01T09:06:21.474514Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-05-01T09:06:55.681605Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/4.cookie")}
2025-05-01T09:06:55.681631Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-05-01T09:06:55.773746Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/6.cookie"), AnchoredSystemPathBuf(".turbo/cookies/5.cookie")}
2025-05-01T09:06:55.773757Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-05-01T09:06:55.873614Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.turbo/turbo-lint.log"), AnchoredSystemPathBuf("packages/core/.turbo/turbo-lint.log")}
2025-05-01T09:06:55.873635Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }, WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T09:06:57.572819Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/.turbo/turbo-lint.log"), AnchoredSystemPathBuf("apps/web/.turbo/turbo-lint.log")}
2025-05-01T09:06:57.572897Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }, WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-05-01T09:11:15.308434Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-05-01T09:11:15.314659Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T09:11:16.271821Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-05-01T09:11:16.271866Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T09:11:16.280678Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-05-01T09:12:50.887857Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-05-01T09:12:50.888637Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T09:12:51.467073Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-05-01T09:12:51.467106Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T09:12:51.467269Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-05-01T09:13:45.784855Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-05-01T09:13:45.793321Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T09:13:46.575894Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-05-01T09:13:46.576005Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T09:13:46.592962Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-05-01T09:14:30.527950Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-05-01T09:14:30.530917Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T09:14:31.586340Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-05-01T09:14:31.586377Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T09:14:31.621509Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-05-01T09:18:33.085190Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-05-01T09:18:33.086765Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T09:18:33.165197Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-05-01T09:18:33.165209Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T09:18:33.165248Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-05-01T09:18:34.464455Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-05-01T09:18:34.464484Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T09:18:34.464527Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-05-01T09:52:30.968609Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-05-01T09:52:30.972020Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T09:52:31.056109Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-05-01T09:52:31.056119Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T09:52:31.056190Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-05-01T09:52:32.458575Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-05-01T09:52:32.461603Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T10:17:26.440450Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types.ts")}
2025-05-01T10:17:26.442630Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T10:17:26.627912Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types.ts")}
2025-05-01T10:17:26.627921Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T10:17:26.639674Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-05-01T10:20:38.442051Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-05-01T10:20:38.444364Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T10:20:39.027889Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-05-01T10:20:39.027917Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T10:20:39.039472Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-05-01T10:23:29.230107Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-05-01T10:23:29.230142Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T10:23:29.929753Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-05-01T10:23:29.929763Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T10:23:29.929784Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-05-01T10:23:44.630070Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-05-01T10:23:44.630089Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T10:23:45.230287Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.ts")}
2025-05-01T10:23:45.230296Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T10:23:45.230315Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-05-01T11:08:16.497765Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-05-01T11:08:16.502754Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T11:08:16.562828Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-05-01T11:08:16.562894Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T11:08:16.562947Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-05-01T11:08:18.267100Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/durak/index.test.ts")}
2025-05-01T11:08:18.268076Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@kozyr-master/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-01T11:08:18.295034Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-05-01T12:58:50.806068Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/game/model/durakSlice.ts")}
2025-05-01T12:58:50.813563Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-05-01T12:58:51.775190Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/game/model/durakSlice.ts")}
2025-05-01T12:58:51.775246Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-05-01T12:58:52.992375Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/game/model/durakSlice.ts")}
2025-05-01T12:58:52.993825Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-05-01T12:58:53.277982Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-05-01T12:59:16.375167Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/store/index.ts")}
2025-05-01T12:59:16.375309Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-05-01T12:59:16.775475Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/store/index.ts")}
2025-05-01T12:59:16.775503Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-05-01T12:59:16.883085Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
