2025-04-29T05:55:44.844840Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("node_modules/next/.DS_Store"), AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T05:55:44.844896Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T05:55:44.998528Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("node_modules/.DS_Store"), AnchoredSystemPathBuf(".turbo/.DS_Store")}
2025-04-29T05:55:44.998643Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T05:55:59.929454Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/next-env.d.ts"), AnchoredSystemPathBuf("apps/web/tsconfig.json")}
2025-04-29T05:55:59.929649Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:56:00.328905Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store"), AnchoredSystemPathBuf("node_modules/.DS_Store"), AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T05:56:00.328919Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T05:56:00.630337Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next"), AnchoredSystemPathBuf("apps/web/.next/package.json")}
2025-04-29T05:56:00.630352Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:56:05.338619Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store"), AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T05:56:05.340020Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T05:56:06.133461Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks"), AnchoredSystemPathBuf("apps/web/.next/static/development"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/polyfills.js"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static")}
2025-04-29T05:56:06.133517Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:56:06.229732Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/polyfills.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/types"), AnchoredSystemPathBuf("apps/web/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/web/.next/types/package.json")}
2025-04-29T05:56:06.229753Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:56:06.328865Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/trace")}
2025-04-29T05:56:06.328881Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:56:06.529521Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.js")}
2025-04-29T05:56:06.529540Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:56:06.533296Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T05:56:06.629362Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/pages-manifest.json")}
2025-04-29T05:56:06.629379Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:56:07.828813Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/types/package.json")}
2025-04-29T05:56:07.828865Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:56:07.927492Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/pages-manifest.json")}
2025-04-29T05:56:07.927507Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:56:07.927560Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T05:56:08.729284Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.js")}
2025-04-29T05:56:08.729325Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:56:08.934054Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/types/package.json")}
2025-04-29T05:56:08.934097Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:56:09.027607Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/types/package.json"), AnchoredSystemPathBuf("apps/web/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.js")}
2025-04-29T05:56:09.027625Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:56:09.233193Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/0.pack.gz_")}
2025-04-29T05:56:09.233215Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:56:09.728179Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/index.pack.gz"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/0.pack.gz"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/index.pack.gz_")}
2025-04-29T05:56:09.728228Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:56:59.061687Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/pages/card-examples.tsx")}
2025-04-29T05:56:59.061747Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:57:01.443185Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/pages/card-examples.tsx")}
2025-04-29T05:57:01.443900Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:57:01.967502Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store"), AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T05:57:01.967542Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T05:57:02.076743Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T05:57:02.076764Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T05:57:03.630977Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/pages/card-examples.tsx")}
2025-04-29T05:57:03.631023Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T05:57:07.060872Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store"), AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T05:57:07.069936Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T06:30:34.832944Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("ADVANCED_PLAN.md")}
2025-04-29T06:30:34.843935Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T06:30:35.709654Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("ADVANCED_PLAN.md")}
2025-04-29T06:30:35.709664Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T06:30:36.111804Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("ADVANCED_PLAN.md")}
2025-04-29T06:30:36.111964Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T06:32:12.412306Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/ui/CardExampleAdvanced.tsx")}
2025-04-29T06:32:12.412626Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T06:32:12.612177Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/ui/CardExampleAdvanced.tsx")}
2025-04-29T06:32:12.612230Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T06:32:12.944570Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T06:32:12.944583Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T06:32:13.102045Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/ui/CardExampleAdvanced.tsx")}
2025-04-29T06:32:13.102075Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T06:32:13.225749Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/ui/CardExampleAdvanced.tsx")}
2025-04-29T06:32:13.225773Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T06:32:13.225807Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T06:32:18.012431Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store"), AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T06:32:18.012460Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T06:32:26.012413Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/pages/card-examples.tsx")}
2025-04-29T06:32:26.012440Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T06:32:26.141330Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/pages/card-examples.tsx")}
2025-04-29T06:32:26.141549Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T06:32:26.141577Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T06:32:26.411342Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T06:32:26.411431Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T06:33:05.615170Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/CARD_COMPONENTS.md")}
2025-04-29T06:33:05.617067Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T06:33:05.813193Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/CARD_COMPONENTS.md")}
2025-04-29T06:33:05.813211Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T06:33:06.113622Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store"), AnchoredSystemPathBuf("docs/CARD_COMPONENTS.md")}
2025-04-29T06:33:06.113638Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T06:33:06.213164Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/CARD_COMPONENTS.md")}
2025-04-29T06:33:06.213174Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T06:33:11.114066Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T06:33:11.114094Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T07:37:27.910340Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/trace")}
2025-04-29T07:37:27.911906Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T07:37:28.010511Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T08:22:39.127026Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("ADVANCED_PLAN.md")}
2025-04-29T08:22:39.129592Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T08:22:40.907924Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("ADVANCED_PLAN.md")}
2025-04-29T08:22:40.908274Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T09:14:10.651924Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/model/types.ts")}
2025-04-29T09:14:10.654558Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T09:14:11.018294Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/model/types.ts")}
2025-04-29T09:14:11.018311Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T09:14:11.030182Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T09:14:11.118105Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T09:14:11.118306Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T09:15:06.522896Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T09:15:06.523066Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T09:19:26.346299Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/ui/Card.tsx")}
2025-04-29T09:19:26.350582Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T09:19:26.628170Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T09:19:26.628209Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T09:19:26.727429Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T09:19:26.727450Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T09:19:27.827822Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/ui/Card.tsx")}
2025-04-29T09:19:27.828385Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T09:19:27.932472Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/ui/Card.tsx")}
2025-04-29T09:19:27.932502Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T09:19:27.932617Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T09:19:31.841556Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store"), AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T09:19:31.841605Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T10:11:29.695458Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/.turbo-cookie")}
2025-04-29T10:11:29.695471Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T10:11:29.796415Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/1.cookie")}
2025-04-29T10:11:29.796434Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T10:12:06.307988Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/trace"), AnchoredSystemPathBuf("apps/web/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks"), AnchoredSystemPathBuf("apps/web/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/types"), AnchoredSystemPathBuf("apps/web/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/package.json"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/web/.next/static"), AnchoredSystemPathBuf("apps/web/.next/types/package.json"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/polyfills.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/server"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/development"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages-manifest.json")}
2025-04-29T10:12:06.310798Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:12:06.524419Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store"), AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T10:12:06.524430Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T10:12:06.697818Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server"), AnchoredSystemPathBuf("apps/web/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/polyfills.js"), AnchoredSystemPathBuf("apps/web/.next/trace"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/development"), AnchoredSystemPathBuf("apps/web/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/web/.next/types/package.json"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks"), AnchoredSystemPathBuf("apps/web/.next/types")}
2025-04-29T10:12:06.697832Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:12:06.897530Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js")}
2025-04-29T10:12:06.897542Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:12:06.997756Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/types/package.json"), AnchoredSystemPathBuf("apps/web/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/app-paths-manifest.json")}
2025-04-29T10:12:06.997766Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:12:11.505012Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store"), AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T10:12:11.505072Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T10:13:38.405523Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/ui/CardExampleAdvanced.tsx")}
2025-04-29T10:13:38.406376Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:13:39.141580Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/ui/CardExampleAdvanced.tsx")}
2025-04-29T10:13:39.141767Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:13:39.182619Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T10:13:39.202511Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T10:13:39.202521Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T10:14:05.708316Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T10:14:05.708353Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T10:14:47.816405Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/swc/plugins"), AnchoredSystemPathBuf("apps/web/.next/cache/swc"), AnchoredSystemPathBuf("apps/web/.next/cache/swc/plugins/v7_macos_aarch64_0.106.15")}
2025-04-29T10:14:47.819153Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:14:47.821256Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T10:14:48.502867Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store"), AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T10:14:48.502877Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T10:14:50.204210Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/web/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/pages"), AnchoredSystemPathBuf("apps/web/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/webpack")}
2025-04-29T10:14:50.204296Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:14:50.305298Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/react-refresh.js"), AnchoredSystemPathBuf("apps/web/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/pages/_error.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/main.js"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/633457081244afec._.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/pages/_app.js"), AnchoredSystemPathBuf("apps/web/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/pages/index.js")}
2025-04-29T10:14:50.305313Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:14:50.305345Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T10:14:50.703944Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/pages/_app.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/pages/index.js"), AnchoredSystemPathBuf("apps/web/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/@swc.js"), AnchoredSystemPathBuf("apps/web/.next/types/package.json"), AnchoredSystemPathBuf("apps/web/.next/server/pages/_error.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages"), AnchoredSystemPathBuf("apps/web/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages/_document.js"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/next.js"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.js")}
2025-04-29T10:14:50.703966Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:14:53.602710Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store"), AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T10:14:53.603007Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T10:14:56.602838Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/1.pack.gz_")}
2025-04-29T10:14:56.602865Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:14:56.707157Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development")}
2025-04-29T10:14:56.707185Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:14:57.101725Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/index.pack.gz_")}
2025-04-29T10:14:57.101740Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:14:57.203877Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/index.pack.gz_")}
2025-04-29T10:14:57.203889Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:14:57.403594Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/0.pack.gz"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/index.pack.gz_")}
2025-04-29T10:14:57.403609Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:14:57.903469Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/1.pack.gz"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/index.pack.gz")}
2025-04-29T10:14:57.903492Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:14:59.003103Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/web/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app")}
2025-04-29T10:14:59.003216Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:14:59.149188Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/main-app.js"), AnchoredSystemPathBuf("apps/web/.next/server/app"), AnchoredSystemPathBuf("apps/web/.next/server/app/_not-found/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/_not-found"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/c719fafb9bd2a7b9.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/app/_not-found"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/webpack.c719fafb9bd2a7b9.hot-update.js")}
2025-04-29T10:14:59.149198Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:14:59.149223Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T10:14:59.603094Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store"), AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T10:14:59.603103Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T10:14:59.803261Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/@swc.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/next.js"), AnchoredSystemPathBuf("apps/web/.next/types/package.json")}
2025-04-29T10:14:59.803280Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:14:59.924418Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/app/_not-found/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app-pages-internals.js"), AnchoredSystemPathBuf("apps/web/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/bd37f6f0f995bbfa.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/webpack.bd37f6f0f995bbfa.hot-update.js"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/webpack.js")}
2025-04-29T10:14:59.924442Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:14:59.924494Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T10:15:04.703481Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store"), AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T10:15:04.703508Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T10:15:06.502651Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/862b87d5707d6556.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/server/app/_not-found/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/pages/games.js"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/webpack.862b87d5707d6556.hot-update.js")}
2025-04-29T10:15:06.502706Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:15:06.803025Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/types/package.json"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages/games.js"), AnchoredSystemPathBuf("apps/web/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages-manifest.json")}
2025-04-29T10:15:06.803039Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:15:22.212225Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/react-refresh.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/amp.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/main.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/webpack.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback"), AnchoredSystemPathBuf("apps/web/.next/fallback-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/pages"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/pages/_app.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/pages/_error.js")}
2025-04-29T10:15:22.212386Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:15:22.903458Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T10:15:22.903483Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T10:15:23.007425Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T10:15:23.007450Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T10:15:24.603048Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development-fallback")}
2025-04-29T10:15:24.603239Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:15:24.907109Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development-fallback/0.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development-fallback/index.pack.gz_")}
2025-04-29T10:15:24.907296Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:15:25.081012Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development-fallback/index.pack.gz_")}
2025-04-29T10:15:25.081024Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:15:25.081052Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T10:15:25.503423Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development-fallback/index.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development-fallback/0.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development-fallback/index.pack.gz"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development-fallback/0.pack.gz")}
2025-04-29T10:15:25.503540Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:15:28.003615Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store"), AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T10:15:28.003634Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T10:16:23.718066Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/2.pack.gz_")}
2025-04-29T10:16:23.721027Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:16:23.905481Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/1.pack.gz_")}
2025-04-29T10:16:23.905492Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:16:24.160003Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T10:16:24.160015Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T10:16:24.305750Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/2.pack.gz_")}
2025-04-29T10:16:24.305771Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:16:24.811195Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/0.pack.gz"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/2.pack.gz"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/1.pack.gz"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/server-development/1.pack.gz_")}
2025-04-29T10:16:24.811215Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:16:24.811246Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T10:16:24.906014Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/1.pack.gz_")}
2025-04-29T10:16:24.906046Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:16:25.105303Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/3.pack.gz"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/1.pack.gz"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/index.pack.gz"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/2.pack.gz")}
2025-04-29T10:16:25.105320Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:16:30.605990Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/trace")}
2025-04-29T10:16:30.606012Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T10:16:30.620407Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T10:36:58.257010Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store"), AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T10:36:58.258740Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T13:30:25.284794Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/themes/index.ts"), AnchoredSystemPathBuf("apps/web/src/entities/card/themes")}
2025-04-29T13:30:25.287836Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T13:30:26.369237Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/themes/index.ts")}
2025-04-29T13:30:26.369269Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T13:30:26.625631Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T13:30:26.625644Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T13:30:26.968489Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/themes/index.ts")}
2025-04-29T13:30:26.968501Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T13:30:38.570322Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store"), AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T13:30:38.570357Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T13:30:52.172265Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/themes/ThemeProvider.tsx")}
2025-04-29T13:30:52.172471Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T13:30:54.220991Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/themes/ThemeProvider.tsx"), AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T13:30:54.221175Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T13:30:54.221243Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T13:30:59.668755Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store"), AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T13:30:59.668795Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T13:31:13.170204Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/ui/Card.tsx")}
2025-04-29T13:31:13.170277Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T13:31:14.006857Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store"), AnchoredSystemPathBuf("apps/web/src/entities/card/ui/Card.tsx")}
2025-04-29T13:31:14.006892Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T13:31:14.007058Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T13:31:14.568934Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/ui/Card.tsx")}
2025-04-29T13:31:14.568951Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T13:31:15.199335Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T13:31:30.870521Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/ui/Card.tsx")}
2025-04-29T13:31:30.870569Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T13:31:34.648117Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store"), AnchoredSystemPathBuf("apps/.DS_Store"), AnchoredSystemPathBuf("apps/web/src/entities/card/ui/Card.tsx")}
2025-04-29T13:31:34.648171Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T13:31:34.648275Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T13:31:50.470623Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/ui/Card.tsx")}
2025-04-29T13:31:50.470672Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T13:31:50.869014Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/ui/Card.tsx")}
2025-04-29T13:31:50.869029Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T13:31:51.036151Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T13:31:51.036260Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T13:31:51.036267Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T13:31:59.969249Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T13:31:59.969296Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T13:32:13.670050Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/themes/ThemeSelector.tsx")}
2025-04-29T13:32:13.670068Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T13:32:14.069468Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/themes/ThemeSelector.tsx")}
2025-04-29T13:32:14.069487Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T13:32:14.450878Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/themes/ThemeSelector.tsx"), AnchoredSystemPathBuf("apps/.DS_Store")}
2025-04-29T13:32:14.450902Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T13:32:14.450982Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T13:32:19.468806Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T13:32:19.468850Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T13:33:10.535139Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/entities/card/themes/index.ts")}
2025-04-29T13:33:10.541500Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T13:33:11.437230Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store"), AnchoredSystemPathBuf("apps/web/src/entities/card/themes/index.ts")}
2025-04-29T13:33:11.437279Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T13:33:11.437424Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T13:33:15.769495Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store"), AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T13:33:15.769569Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T16:44:00.889962Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/.turbo-cookie")}
2025-04-29T16:44:00.889972Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T16:44:00.991250Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/1.cookie")}
2025-04-29T16:44:00.991264Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-04-29T16:44:29.600229Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/web/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/pages/_error.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/pages/_error.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/react-refresh.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages/games.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/862b87d5707d6556.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/server/app/_not-found"), AnchoredSystemPathBuf("apps/web/.next/package.json"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/@swc.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/next.js"), AnchoredSystemPathBuf("apps/web/.next/static"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/polyfills.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/webpack.js"), AnchoredSystemPathBuf("apps/web/.next/types"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/_not-found"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/amp.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/main.js"), AnchoredSystemPathBuf("apps/web/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks"), AnchoredSystemPathBuf("apps/web/.next/server/pages"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/c719fafb9bd2a7b9.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/main.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/pages/games.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages/_app.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/pages/index.js"), AnchoredSystemPathBuf("apps/web/.next/fallback-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/633457081244afec._.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/server/pages/index.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/pages/_app.js"), AnchoredSystemPathBuf("apps/web/.next/server"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/pages"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/pages"), AnchoredSystemPathBuf("apps/web/.next/static/webpack"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/webpack.c719fafb9bd2a7b9.hot-update.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages/_error.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/pages/_app.js"), AnchoredSystemPathBuf("apps/web/.next/static/development"), AnchoredSystemPathBuf("apps/web/.next/server/pages/_document.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app-pages-internals.js"), AnchoredSystemPathBuf("apps/web/.next/trace"), AnchoredSystemPathBuf("apps/web/.next/types/package.json"), AnchoredSystemPathBuf("apps/web/.next/server/app/_not-found/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/bd37f6f0f995bbfa.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/server/app"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/webpack.bd37f6f0f995bbfa.hot-update.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/react-refresh.js"), AnchoredSystemPathBuf("apps/web/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app"), AnchoredSystemPathBuf("apps/web/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/main-app.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/webpack.862b87d5707d6556.hot-update.js")}
2025-04-29T16:44:29.603532Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T16:44:30.289920Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/static"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/types/package.json"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/development"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server"), AnchoredSystemPathBuf("apps/web/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks"), AnchoredSystemPathBuf("apps/web/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/trace"), AnchoredSystemPathBuf("apps/web/.next/types"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/polyfills.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/interception-route-rewrite-manifest.js")}
2025-04-29T16:44:30.289931Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T16:44:30.492368Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js")}
2025-04-29T16:44:30.492388Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T16:44:30.783632Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/types/package.json"), AnchoredSystemPathBuf("apps/web/.next/server/app-paths-manifest.json")}
2025-04-29T16:44:30.783653Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T16:44:30.784560Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T16:44:31.491467Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/index.pack.gz"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/web/.next/cache/webpack/client-development/0.pack.gz")}
2025-04-29T16:44:31.491480Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T17:17:27.409389Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/trace")}
2025-04-29T17:17:27.410644Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("kozyr-master-web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-04-29T17:17:27.545930Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-04-29T17:18:00.693512Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store"), AnchoredSystemPathBuf(".DS_Store")}
2025-04-29T17:18:00.693565Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
