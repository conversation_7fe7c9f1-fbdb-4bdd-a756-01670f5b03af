# Основные настройки
NODE_ENV=development
PORT=3000

# База данных PostgreSQL
POSTGRES_USERNAME=postgres
POSTGRES_PASSWORD=your_secure_password
DB_HOST=localhost
DB_PORT=5432
DB_NAME=kozyr_master

# MongoDB (для игровых данных)
MONGO_USERNAME=admin
MONGO_PASSWORD=your_secure_password
MONGODB_URL=mongodb://localhost:27017/kozyr-master

# Redis
REDIS_PASSWORD=your_redis_password
REDIS_URL=redis://localhost:6379

# JWT секреты
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production
JWT_REFRESH_SECRET=your_super_secret_refresh_key_change_this_in_production

# CORS
CORS_ORIGIN=http://localhost:3000
FRONTEND_URL=http://localhost:3000

# Email настройки
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>

# Мониторинг
GRAFANA_USERNAME=admin
GRAFANA_PASSWORD=admin

# Backup
BACKUP_S3_BUCKET=kozyr-master-backups
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key

# Firebase (для push уведомлений)
FIREBASE_PROJECT_ID=your_firebase_project
FIREBASE_PRIVATE_KEY=your_firebase_private_key

# Логирование
LOG_LEVEL=info
