import axios from 'axios';
import { EventEmitter } from 'events';
import WebSocket from 'ws';
import { logger } from './utils/logger';

export interface TwitchUser {
  id: string;
  login: string;
  display_name: string;
  type: string;
  broadcaster_type: string;
  description: string;
  profile_image_url: string;
  offline_image_url: string;
  view_count: number;
  created_at: string;
}

export interface TwitchStream {
  id: string;
  user_id: string;
  user_login: string;
  user_name: string;
  game_id: string;
  game_name: string;
  type: 'live' | '';
  title: string;
  viewer_count: number;
  started_at: string;
  language: string;
  thumbnail_url: string;
  tag_ids: string[];
  is_mature: boolean;
}

export interface StreamerSettings {
  userId: string;
  twitchUserId: string;
  twitchUsername: string;
  accessToken: string;
  refreshToken: string;
  isStreamingEnabled: boolean;
  autoStartStream: boolean;
  streamTitle: string;
  streamCategory: string;
  overlayEnabled: boolean;
  chatIntegration: boolean;
  donationAlerts: boolean;
  followerAlerts: boolean;
  gameSpecificSettings: {
    [gameType: string]: {
      title: string;
      category: string;
      tags: string[];
    };
  };
}

export class TwitchIntegration extends EventEmitter {
  private clientId: string;
  private clientSecret: string;
  private redirectUri: string;
  private apiBaseUrl = 'https://api.twitch.tv/helix';
  private authBaseUrl = 'https://id.twitch.tv/oauth2';
  private eventSubUrl = 'wss://eventsub.wss.twitch.tv/ws';
  
  private streamers: Map<string, StreamerSettings> = new Map();
  private eventSubConnections: Map<string, WebSocket> = new Map();

  constructor() {
    super();
    this.clientId = process.env.TWITCH_CLIENT_ID!;
    this.clientSecret = process.env.TWITCH_CLIENT_SECRET!;
    this.redirectUri = process.env.TWITCH_REDIRECT_URI!;
  }

  // Авторизация стримера
  public getAuthUrl(state: string): string {
    const scopes = [
      'channel:manage:broadcast',
      'channel:read:stream_key',
      'channel:edit:commercial',
      'chat:read',
      'chat:edit',
      'channel:moderate',
      'channel:read:subscriptions',
      'bits:read',
      'channel:read:redemptions',
      'moderator:read:followers'
    ].join(' ');

    const params = new URLSearchParams({
      client_id: this.clientId,
      redirect_uri: this.redirectUri,
      response_type: 'code',
      scope: scopes,
      state
    });

    return `${this.authBaseUrl}/authorize?${params.toString()}`;
  }

  // Обмен кода на токены
  public async exchangeCodeForTokens(code: string): Promise<{
    access_token: string;
    refresh_token: string;
    expires_in: number;
  }> {
    try {
      const response = await axios.post(`${this.authBaseUrl}/token`, {
        client_id: this.clientId,
        client_secret: this.clientSecret,
        code,
        grant_type: 'authorization_code',
        redirect_uri: this.redirectUri
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to exchange code for tokens:', error);
      throw new Error('Failed to get Twitch tokens');
    }
  }

  // Обновление токена
  public async refreshAccessToken(refreshToken: string): Promise<{
    access_token: string;
    refresh_token: string;
    expires_in: number;
  }> {
    try {
      const response = await axios.post(`${this.authBaseUrl}/token`, {
        client_id: this.clientId,
        client_secret: this.clientSecret,
        refresh_token: refreshToken,
        grant_type: 'refresh_token'
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to refresh access token:', error);
      throw new Error('Failed to refresh Twitch token');
    }
  }

  // Получение информации о пользователе
  public async getUserInfo(accessToken: string): Promise<TwitchUser> {
    try {
      const response = await axios.get(`${this.apiBaseUrl}/users`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Client-Id': this.clientId
        }
      });

      return response.data.data[0];
    } catch (error) {
      logger.error('Failed to get user info:', error);
      throw new Error('Failed to get Twitch user info');
    }
  }

  // Получение информации о стриме
  public async getStreamInfo(userId: string): Promise<TwitchStream | null> {
    const streamer = this.streamers.get(userId);
    if (!streamer) {
      throw new Error('Streamer not found');
    }

    try {
      const response = await axios.get(`${this.apiBaseUrl}/streams`, {
        headers: {
          'Authorization': `Bearer ${streamer.accessToken}`,
          'Client-Id': this.clientId
        },
        params: {
          user_id: streamer.twitchUserId
        }
      });

      return response.data.data[0] || null;
    } catch (error) {
      logger.error('Failed to get stream info:', error);
      return null;
    }
  }

  // Обновление информации о стриме
  public async updateStreamInfo(userId: string, title: string, categoryId: string): Promise<boolean> {
    const streamer = this.streamers.get(userId);
    if (!streamer) {
      throw new Error('Streamer not found');
    }

    try {
      await axios.patch(`${this.apiBaseUrl}/channels`, {
        broadcaster_id: streamer.twitchUserId,
        title,
        game_id: categoryId
      }, {
        headers: {
          'Authorization': `Bearer ${streamer.accessToken}`,
          'Client-Id': this.clientId,
          'Content-Type': 'application/json'
        }
      });

      logger.info(`Stream info updated for ${streamer.twitchUsername}`, { title, categoryId });
      return true;
    } catch (error) {
      logger.error('Failed to update stream info:', error);
      return false;
    }
  }

  // Поиск категорий игр
  public async searchCategories(query: string): Promise<Array<{ id: string; name: string }>> {
    try {
      const response = await axios.get(`${this.apiBaseUrl}/games`, {
        headers: {
          'Authorization': `Bearer ${await this.getAppAccessToken()}`,
          'Client-Id': this.clientId
        },
        params: {
          name: query
        }
      });

      return response.data.data.map((game: any) => ({
        id: game.id,
        name: game.name
      }));
    } catch (error) {
      logger.error('Failed to search categories:', error);
      return [];
    }
  }

  // Регистрация стримера
  public async registerStreamer(settings: StreamerSettings): Promise<void> {
    this.streamers.set(settings.userId, settings);
    
    // Подключаемся к EventSub для получения событий
    if (settings.chatIntegration || settings.donationAlerts || settings.followerAlerts) {
      await this.setupEventSub(settings);
    }

    logger.info(`Streamer registered: ${settings.twitchUsername}`);
  }

  // Удаление стримера
  public async unregisterStreamer(userId: string): Promise<void> {
    const streamer = this.streamers.get(userId);
    if (streamer) {
      // Закрываем EventSub соединение
      const ws = this.eventSubConnections.get(userId);
      if (ws) {
        ws.close();
        this.eventSubConnections.delete(userId);
      }

      this.streamers.delete(userId);
      logger.info(`Streamer unregistered: ${streamer.twitchUsername}`);
    }
  }

  // Автоматическое обновление стрима при начале игры
  public async onGameStarted(userId: string, gameType: string, gameData: any): Promise<void> {
    const streamer = this.streamers.get(userId);
    if (!streamer || !streamer.isStreamingEnabled || !streamer.autoStartStream) {
      return;
    }

    const gameSettings = streamer.gameSpecificSettings[gameType];
    if (!gameSettings) {
      return;
    }

    // Ищем категорию игры
    const categories = await this.searchCategories(gameSettings.category);
    const categoryId = categories[0]?.id;

    if (categoryId) {
      const title = this.formatStreamTitle(gameSettings.title, gameData);
      await this.updateStreamInfo(userId, title, categoryId);
      
      this.emit('streamUpdated', {
        userId,
        gameType,
        title,
        category: gameSettings.category
      });
    }
  }

  // Отправка сообщения в чат
  public async sendChatMessage(userId: string, message: string): Promise<boolean> {
    const streamer = this.streamers.get(userId);
    if (!streamer || !streamer.chatIntegration) {
      return false;
    }

    try {
      await axios.post(`${this.apiBaseUrl}/chat/messages`, {
        broadcaster_id: streamer.twitchUserId,
        sender_id: streamer.twitchUserId,
        message
      }, {
        headers: {
          'Authorization': `Bearer ${streamer.accessToken}`,
          'Client-Id': this.clientId,
          'Content-Type': 'application/json'
        }
      });

      return true;
    } catch (error) {
      logger.error('Failed to send chat message:', error);
      return false;
    }
  }

  // Настройка EventSub для получения событий
  private async setupEventSub(streamer: StreamerSettings): Promise<void> {
    try {
      const ws = new WebSocket(this.eventSubUrl);
      
      ws.on('open', () => {
        logger.info(`EventSub connected for ${streamer.twitchUsername}`);
      });

      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleEventSubMessage(streamer.userId, message);
        } catch (error) {
          logger.error('Failed to parse EventSub message:', error);
        }
      });

      ws.on('close', () => {
        logger.info(`EventSub disconnected for ${streamer.twitchUsername}`);
        // Переподключение через 5 секунд
        setTimeout(() => {
          if (this.streamers.has(streamer.userId)) {
            this.setupEventSub(streamer);
          }
        }, 5000);
      });

      ws.on('error', (error) => {
        logger.error('EventSub WebSocket error:', error);
      });

      this.eventSubConnections.set(streamer.userId, ws);
    } catch (error) {
      logger.error('Failed to setup EventSub:', error);
    }
  }

  // Обработка сообщений EventSub
  private handleEventSubMessage(userId: string, message: any): void {
    const streamer = this.streamers.get(userId);
    if (!streamer) return;

    switch (message.metadata?.message_type) {
      case 'session_welcome':
        // Подписываемся на события
        this.subscribeToEvents(streamer, message.payload.session.id);
        break;

      case 'notification':
        this.handleNotification(userId, message.payload);
        break;

      case 'session_keepalive':
        // Keepalive сообщение, ничего не делаем
        break;

      default:
        logger.debug('Unknown EventSub message type:', message.metadata?.message_type);
    }
  }

  // Подписка на события
  private async subscribeToEvents(streamer: StreamerSettings, sessionId: string): Promise<void> {
    const events = [];

    if (streamer.followerAlerts) {
      events.push('channel.follow');
    }

    if (streamer.donationAlerts) {
      events.push('channel.cheer');
      events.push('channel.subscribe');
      events.push('channel.subscription.gift');
    }

    if (streamer.chatIntegration) {
      events.push('channel.chat.message');
    }

    for (const eventType of events) {
      try {
        await axios.post(`${this.apiBaseUrl}/eventsub/subscriptions`, {
          type: eventType,
          version: '1',
          condition: {
            broadcaster_user_id: streamer.twitchUserId
          },
          transport: {
            method: 'websocket',
            session_id: sessionId
          }
        }, {
          headers: {
            'Authorization': `Bearer ${streamer.accessToken}`,
            'Client-Id': this.clientId,
            'Content-Type': 'application/json'
          }
        });

        logger.info(`Subscribed to ${eventType} for ${streamer.twitchUsername}`);
      } catch (error) {
        logger.error(`Failed to subscribe to ${eventType}:`, error);
      }
    }
  }

  // Обработка уведомлений
  private handleNotification(userId: string, payload: any): void {
    const eventType = payload.subscription.type;
    const eventData = payload.event;

    this.emit('twitchEvent', {
      userId,
      eventType,
      data: eventData
    });

    // Специфическая обработка событий
    switch (eventType) {
      case 'channel.follow':
        this.emit('newFollower', {
          userId,
          follower: {
            id: eventData.user_id,
            name: eventData.user_name,
            displayName: eventData.user_display_name
          }
        });
        break;

      case 'channel.cheer':
        this.emit('newCheer', {
          userId,
          cheer: {
            userId: eventData.user_id,
            userName: eventData.user_name,
            bits: eventData.bits,
            message: eventData.message
          }
        });
        break;

      case 'channel.subscribe':
        this.emit('newSubscriber', {
          userId,
          subscriber: {
            userId: eventData.user_id,
            userName: eventData.user_name,
            tier: eventData.tier,
            isGift: eventData.is_gift
          }
        });
        break;
    }
  }

  // Форматирование заголовка стрима
  private formatStreamTitle(template: string, gameData: any): string {
    return template
      .replace('{game}', gameData.gameType || 'Card Game')
      .replace('{rating}', gameData.rating || 'Unranked')
      .replace('{rank}', gameData.rank || 'Beginner')
      .replace('{tournament}', gameData.tournament || '');
  }

  // Получение токена приложения
  private async getAppAccessToken(): Promise<string> {
    try {
      const response = await axios.post(`${this.authBaseUrl}/token`, {
        client_id: this.clientId,
        client_secret: this.clientSecret,
        grant_type: 'client_credentials'
      });

      return response.data.access_token;
    } catch (error) {
      logger.error('Failed to get app access token:', error);
      throw new Error('Failed to get app access token');
    }
  }

  // Получение статистики стримера
  public async getStreamerStats(userId: string): Promise<any> {
    const streamer = this.streamers.get(userId);
    if (!streamer) {
      throw new Error('Streamer not found');
    }

    try {
      const [userInfo, streamInfo] = await Promise.all([
        this.getUserInfo(streamer.accessToken),
        this.getStreamInfo(userId)
      ]);

      return {
        user: userInfo,
        stream: streamInfo,
        isLive: !!streamInfo,
        viewerCount: streamInfo?.viewer_count || 0,
        followerCount: userInfo.view_count
      };
    } catch (error) {
      logger.error('Failed to get streamer stats:', error);
      return null;
    }
  }
}
