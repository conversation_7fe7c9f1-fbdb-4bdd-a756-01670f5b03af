import { OpenAI } from 'openai';
import { TensorFlow } from '@tensorflow/tfjs-node';

export interface AIPersonality {
  id: string;
  name: string;
  description: string;
  traits: {
    helpfulness: number;    // 0-1
    playfulness: number;    // 0-1
    competitiveness: number; // 0-1
    patience: number;       // 0-1
    expertise: number;      // 0-1
  };
  specialties: string[];
  language: string;
  avatar: string;
}

export interface PlayerAnalysis {
  playerId: string;
  skillLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert' | 'master';
  playStyle: 'aggressive' | 'conservative' | 'balanced' | 'unpredictable';
  strengths: string[];
  weaknesses: string[];
  preferredGames: string[];
  learningGoals: string[];
  emotionalState: 'frustrated' | 'confident' | 'neutral' | 'excited' | 'focused';
  lastAnalyzed: Date;
}

export interface GameAdvice {
  type: 'move_suggestion' | 'strategy_tip' | 'rule_explanation' | 'encouragement' | 'warning';
  priority: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  reasoning: string;
  confidence: number; // 0-1
  gameContext: any;
  timestamp: Date;
}

export interface LearningPath {
  id: string;
  playerId: string;
  gameType: string;
  currentLevel: number;
  targetLevel: number;
  lessons: LearningLesson[];
  progress: number; // 0-100
  estimatedCompletion: Date;
  adaptiveAdjustments: string[];
}

export interface LearningLesson {
  id: string;
  title: string;
  description: string;
  type: 'tutorial' | 'practice' | 'challenge' | 'theory' | 'analysis';
  difficulty: number; // 1-10
  prerequisites: string[];
  objectives: string[];
  content: LessonContent;
  isCompleted: boolean;
  score?: number;
  timeSpent: number;
}

export interface LessonContent {
  text?: string;
  video?: string;
  interactive?: any;
  quiz?: QuizQuestion[];
  practiceScenarios?: GameScenario[];
}

export interface QuizQuestion {
  question: string;
  options: string[];
  correctAnswer: number;
  explanation: string;
}

export interface GameScenario {
  id: string;
  description: string;
  gameState: any;
  correctMoves: any[];
  hints: string[];
}

export class AIAssistant {
  private openai: OpenAI;
  private personalities: Map<string, AIPersonality> = new Map();
  private playerAnalyses: Map<string, PlayerAnalysis> = new Map();
  private learningPaths: Map<string, LearningPath> = new Map();
  private mlModels: Map<string, any> = new Map();

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    this.initializePersonalities();
    this.loadMLModels();
  }

  /**
   * Анализирует игрока и предоставляет персонализированные советы
   */
  async analyzePlayer(playerId: string, gameHistory: any[], currentGame?: any): Promise<PlayerAnalysis> {
    const existingAnalysis = this.playerAnalyses.get(playerId);
    
    // Анализируем игровую историю с помощью ML
    const skillLevel = await this.assessSkillLevel(gameHistory);
    const playStyle = await this.identifyPlayStyle(gameHistory);
    const strengths = await this.identifyStrengths(gameHistory);
    const weaknesses = await this.identifyWeaknesses(gameHistory);
    const emotionalState = await this.assessEmotionalState(currentGame, gameHistory);

    const analysis: PlayerAnalysis = {
      playerId,
      skillLevel,
      playStyle,
      strengths,
      weaknesses,
      preferredGames: this.getPreferredGames(gameHistory),
      learningGoals: await this.generateLearningGoals(skillLevel, weaknesses),
      emotionalState,
      lastAnalyzed: new Date()
    };

    this.playerAnalyses.set(playerId, analysis);
    
    // Обновляем или создаем персонализированный путь обучения
    await this.updateLearningPath(playerId, analysis);

    return analysis;
  }

  /**
   * Предоставляет советы в реальном времени во время игры
   */
  async provideGameAdvice(
    playerId: string, 
    gameState: any, 
    gameType: string,
    personalityId: string = 'default'
  ): Promise<GameAdvice | null> {
    const analysis = this.playerAnalyses.get(playerId);
    const personality = this.personalities.get(personalityId);
    
    if (!analysis || !personality) return null;

    // Анализируем текущую ситуацию в игре
    const moveAnalysis = await this.analyzeGameSituation(gameState, gameType, analysis);
    
    if (!moveAnalysis.needsAdvice) return null;

    // Генерируем совет с учетом личности ИИ
    const advice = await this.generatePersonalizedAdvice(
      moveAnalysis,
      analysis,
      personality,
      gameType
    );

    return advice;
  }

  /**
   * Создает персонализированный путь обучения
   */
  async createLearningPath(
    playerId: string, 
    gameType: string, 
    targetSkillLevel: string
  ): Promise<LearningPath> {
    const analysis = this.playerAnalyses.get(playerId);
    if (!analysis) {
      throw new Error('Player analysis required');
    }

    const pathId = this.generatePathId();
    const lessons = await this.generateAdaptiveLessons(analysis, gameType, targetSkillLevel);
    
    const learningPath: LearningPath = {
      id: pathId,
      playerId,
      gameType,
      currentLevel: this.skillLevelToNumber(analysis.skillLevel),
      targetLevel: this.skillLevelToNumber(targetSkillLevel as any),
      lessons,
      progress: 0,
      estimatedCompletion: this.calculateEstimatedCompletion(lessons),
      adaptiveAdjustments: []
    };

    this.learningPaths.set(pathId, learningPath);
    return learningPath;
  }

  /**
   * Обновляет прогресс обучения и адаптирует путь
   */
  async updateLearningProgress(
    pathId: string, 
    lessonId: string, 
    score: number, 
    timeSpent: number
  ): Promise<void> {
    const path = this.learningPaths.get(pathId);
    if (!path) return;

    const lesson = path.lessons.find(l => l.id === lessonId);
    if (!lesson) return;

    lesson.isCompleted = score >= 70; // 70% для прохождения
    lesson.score = score;
    lesson.timeSpent += timeSpent;

    // Адаптивная корректировка сложности
    if (score < 50) {
      // Слишком сложно - добавляем дополнительные уроки
      await this.addSupportiveLessons(path, lesson);
    } else if (score > 90 && timeSpent < lesson.difficulty * 60) {
      // Слишком легко - увеличиваем сложность следующих уроков
      await this.increaseDifficulty(path, lesson);
    }

    // Обновляем общий прогресс
    const completedLessons = path.lessons.filter(l => l.isCompleted).length;
    path.progress = (completedLessons / path.lessons.length) * 100;

    // Переанализируем игрока для обновления пути
    const analysis = this.playerAnalyses.get(path.playerId);
    if (analysis) {
      await this.adaptLearningPath(path, analysis);
    }
  }

  /**
   * Генерирует интерактивные обучающие сценарии
   */
  async generateTrainingScenario(
    gameType: string, 
    skillLevel: string, 
    focusArea: string
  ): Promise<GameScenario> {
    const prompt = `
    Создай обучающий сценарий для игры ${gameType} 
    для игрока уровня ${skillLevel} 
    с фокусом на ${focusArea}.
    
    Сценарий должен включать:
    1. Описание ситуации
    2. Текущее состояние игры
    3. Несколько возможных ходов
    4. Правильное решение с объяснением
    5. Подсказки для игрока
    `;

    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.7
    });

    const scenarioData = JSON.parse(response.choices[0].message.content || '{}');
    
    return {
      id: this.generateScenarioId(),
      description: scenarioData.description,
      gameState: scenarioData.gameState,
      correctMoves: scenarioData.correctMoves,
      hints: scenarioData.hints
    };
  }

  /**
   * Анализирует эмоциональное состояние игрока
   */
  private async assessEmotionalState(
    currentGame: any, 
    gameHistory: any[]
  ): Promise<PlayerAnalysis['emotionalState']> {
    // Анализируем паттерны поведения
    const recentPerformance = gameHistory.slice(-10);
    const winRate = recentPerformance.filter(g => g.won).length / recentPerformance.length;
    
    if (currentGame) {
      const gameTime = Date.now() - new Date(currentGame.startTime).getTime();
      const averageGameTime = gameHistory.reduce((sum, g) => sum + g.duration, 0) / gameHistory.length;
      
      // Быстрые ходы могут указывать на фрустрацию или уверенность
      if (gameTime < averageGameTime * 0.5) {
        return winRate > 0.7 ? 'confident' : 'frustrated';
      }
      
      // Медленные ходы могут указывать на концентрацию
      if (gameTime > averageGameTime * 1.5) {
        return 'focused';
      }
    }

    if (winRate > 0.7) return 'confident';
    if (winRate < 0.3) return 'frustrated';
    if (winRate > 0.5) return 'excited';
    
    return 'neutral';
  }

  /**
   * Оценивает уровень навыков игрока
   */
  private async assessSkillLevel(gameHistory: any[]): Promise<PlayerAnalysis['skillLevel']> {
    if (gameHistory.length < 10) return 'beginner';
    
    const winRate = gameHistory.filter(g => g.won).length / gameHistory.length;
    const averageGameDuration = gameHistory.reduce((sum, g) => sum + g.duration, 0) / gameHistory.length;
    const complexMoves = gameHistory.reduce((sum, g) => sum + (g.complexMoves || 0), 0);
    
    // Используем ML модель для более точной оценки
    const features = [winRate, averageGameDuration, complexMoves, gameHistory.length];
    const prediction = await this.predictSkillLevel(features);
    
    return prediction;
  }

  /**
   * Определяет стиль игры
   */
  private async identifyPlayStyle(gameHistory: any[]): Promise<PlayerAnalysis['playStyle']> {
    const aggressiveActions = gameHistory.reduce((sum, g) => sum + (g.aggressiveActions || 0), 0);
    const conservativeActions = gameHistory.reduce((sum, g) => sum + (g.conservativeActions || 0), 0);
    const totalActions = gameHistory.reduce((sum, g) => sum + (g.totalActions || 1), 0);
    
    const aggressiveRatio = aggressiveActions / totalActions;
    const conservativeRatio = conservativeActions / totalActions;
    
    if (aggressiveRatio > 0.6) return 'aggressive';
    if (conservativeRatio > 0.6) return 'conservative';
    if (Math.abs(aggressiveRatio - conservativeRatio) < 0.2) return 'balanced';
    
    return 'unpredictable';
  }

  /**
   * Генерирует персонализированный совет
   */
  private async generatePersonalizedAdvice(
    moveAnalysis: any,
    playerAnalysis: PlayerAnalysis,
    personality: AIPersonality,
    gameType: string
  ): Promise<GameAdvice> {
    const prompt = `
    Ты ${personality.name} - ${personality.description}.
    
    Игрок: ${playerAnalysis.skillLevel} уровня, стиль игры: ${playerAnalysis.playStyle}
    Эмоциональное состояние: ${playerAnalysis.emotionalState}
    Слабые стороны: ${playerAnalysis.weaknesses.join(', ')}
    
    Ситуация в игре ${gameType}: ${JSON.stringify(moveAnalysis.situation)}
    
    Дай совет в своем стиле, учитывая личность и состояние игрока.
    Будь ${personality.traits.helpfulness > 0.7 ? 'очень полезным' : 'умеренно полезным'} и 
    ${personality.traits.playfulness > 0.7 ? 'игривым' : 'серьезным'}.
    `;

    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [{ role: "user", content: prompt }],
      temperature: personality.traits.playfulness,
      max_tokens: 200
    });

    return {
      type: moveAnalysis.adviceType,
      priority: moveAnalysis.priority,
      message: response.choices[0].message.content || '',
      reasoning: moveAnalysis.reasoning,
      confidence: moveAnalysis.confidence,
      gameContext: moveAnalysis.situation,
      timestamp: new Date()
    };
  }

  /**
   * Инициализирует личности ИИ
   */
  private initializePersonalities(): void {
    const personalities: AIPersonality[] = [
      {
        id: 'mentor',
        name: 'Мастер Карт',
        description: 'Мудрый наставник с многолетним опытом',
        traits: {
          helpfulness: 0.9,
          playfulness: 0.3,
          competitiveness: 0.6,
          patience: 0.9,
          expertise: 0.95
        },
        specialties: ['strategy', 'advanced_tactics', 'psychology'],
        language: 'ru',
        avatar: '🎩'
      },
      {
        id: 'friend',
        name: 'Веселый Друг',
        description: 'Дружелюбный компаньон для игр',
        traits: {
          helpfulness: 0.8,
          playfulness: 0.9,
          competitiveness: 0.4,
          patience: 0.7,
          expertise: 0.6
        },
        specialties: ['encouragement', 'fun_facts', 'casual_tips'],
        language: 'ru',
        avatar: '😊'
      },
      {
        id: 'coach',
        name: 'Тренер Про',
        description: 'Строгий тренер для серьезных игроков',
        traits: {
          helpfulness: 0.85,
          playfulness: 0.2,
          competitiveness: 0.95,
          patience: 0.5,
          expertise: 0.9
        },
        specialties: ['competitive_play', 'tournament_prep', 'analysis'],
        language: 'ru',
        avatar: '💪'
      }
    ];

    personalities.forEach(p => this.personalities.set(p.id, p));
  }

  /**
   * Загружает ML модели
   */
  private async loadMLModels(): Promise<void> {
    // Здесь загружаются предобученные модели TensorFlow
    // Модель для предсказания уровня навыков
    // Модель для анализа эмоционального состояния
    // Модель для предсказания следующего хода
    console.log('Loading ML models...');
  }

  // Вспомогательные методы
  private async predictSkillLevel(features: number[]): Promise<PlayerAnalysis['skillLevel']> {
    // Заглушка для ML предсказания
    const score = features[0] * 0.4 + features[2] * 0.3 + Math.min(features[3] / 100, 1) * 0.3;
    
    if (score < 0.3) return 'beginner';
    if (score < 0.5) return 'intermediate';
    if (score < 0.7) return 'advanced';
    if (score < 0.9) return 'expert';
    return 'master';
  }

  private skillLevelToNumber(level: PlayerAnalysis['skillLevel']): number {
    const levels = { beginner: 1, intermediate: 2, advanced: 3, expert: 4, master: 5 };
    return levels[level];
  }

  private getPreferredGames(gameHistory: any[]): string[] {
    const gameCounts = gameHistory.reduce((acc, game) => {
      acc[game.type] = (acc[game.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(gameCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([game]) => game);
  }

  private async generateLearningGoals(
    skillLevel: PlayerAnalysis['skillLevel'], 
    weaknesses: string[]
  ): Promise<string[]> {
    const goals = [];
    
    if (weaknesses.includes('card_counting')) {
      goals.push('Улучшить навыки подсчета карт');
    }
    
    if (weaknesses.includes('bluffing')) {
      goals.push('Освоить техники блефа');
    }
    
    if (skillLevel === 'beginner') {
      goals.push('Изучить основные правила всех игр');
    }
    
    return goals;
  }

  private async analyzeGameSituation(
    gameState: any, 
    gameType: string, 
    analysis: PlayerAnalysis
  ): Promise<any> {
    // Анализ текущей игровой ситуации
    return {
      needsAdvice: Math.random() > 0.7, // Заглушка
      adviceType: 'move_suggestion',
      priority: 'medium',
      situation: gameState,
      reasoning: 'Анализ ситуации',
      confidence: 0.8
    };
  }

  private async updateLearningPath(playerId: string, analysis: PlayerAnalysis): Promise<void> {
    // Обновление пути обучения на основе анализа
  }

  private async generateAdaptiveLessons(
    analysis: PlayerAnalysis, 
    gameType: string, 
    targetLevel: string
  ): Promise<LearningLesson[]> {
    // Генерация адаптивных уроков
    return [];
  }

  private calculateEstimatedCompletion(lessons: LearningLesson[]): Date {
    const totalTime = lessons.reduce((sum, lesson) => sum + lesson.difficulty * 30, 0);
    return new Date(Date.now() + totalTime * 60 * 1000);
  }

  private async addSupportiveLessons(path: LearningPath, lesson: LearningLesson): Promise<void> {
    // Добавление поддерживающих уроков
  }

  private async increaseDifficulty(path: LearningPath, lesson: LearningLesson): Promise<void> {
    // Увеличение сложности
  }

  private async adaptLearningPath(path: LearningPath, analysis: PlayerAnalysis): Promise<void> {
    // Адаптация пути обучения
  }

  private generatePathId(): string {
    return 'path_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generateScenarioId(): string {
    return 'scenario_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}
