import { EventEmitter } from 'events';
import OpenAI from 'openai';
import { TensorFlow } from '@tensorflow/tfjs-node';
import { logger } from './utils/logger';

export interface AIPersonality {
  id: string;
  name: string;
  description: string;
  playStyle: 'aggressive' | 'conservative' | 'balanced' | 'unpredictable';
  difficulty: number; // 1-10
  specialties: string[];
  avatar: string;
  voiceId: string;
  catchphrases: string[];
}

export interface GameAnalysis {
  moveQuality: number; // 0-100
  suggestedMove: any;
  reasoning: string;
  riskAssessment: number;
  winProbability: number;
  alternativeMoves: Array<{
    move: any;
    quality: number;
    reasoning: string;
  }>;
}

export interface PlayerProfile {
  userId: string;
  skillLevel: number;
  playStyle: string;
  strengths: string[];
  weaknesses: string[];
  learningGoals: string[];
  preferredDifficulty: number;
  gameHistory: any[];
  improvementAreas: string[];
}

export class GameAI extends EventEmitter {
  private openai: OpenAI;
  private models: Map<string, any> = new Map();
  private personalities: Map<string, AIPersonality> = new Map();
  private playerProfiles: Map<string, PlayerProfile> = new Map();

  constructor() {
    super();
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    this.initializePersonalities();
    this.loadModels();
  }

  private initializePersonalities(): void {
    const personalities: AIPersonality[] = [
      {
        id: 'grandmaster_ivan',
        name: 'Гроссмейстер Иван',
        description: 'Легендарный мастер карточных игр с 50-летним опытом',
        playStyle: 'conservative',
        difficulty: 10,
        specialties: ['durak', 'preferans', 'strategic_thinking'],
        avatar: '👴🏻',
        voiceId: 'ivan_voice',
        catchphrases: [
          'В моё время карты были настоящими!',
          'Терпение - ключ к победе',
          'Каждая карта имеет свою историю'
        ]
      },
      {
        id: 'poker_shark_anna',
        name: 'Покерная Акула Анна',
        description: 'Молодая и агрессивная покеристка из Лас-Вегаса',
        playStyle: 'aggressive',
        difficulty: 9,
        specialties: ['poker', 'bluffing', 'psychology'],
        avatar: '🦈',
        voiceId: 'anna_voice',
        catchphrases: [
          'Всё или ничего!',
          'Читаю тебя как открытую книгу',
          'Удача любит смелых'
        ]
      },
      {
        id: 'ai_professor',
        name: 'Профессор Алгоритм',
        description: 'ИИ-сущность, изучающая человеческое поведение через игры',
        playStyle: 'unpredictable',
        difficulty: 8,
        specialties: ['pattern_recognition', 'adaptation', 'learning'],
        avatar: '🤖',
        voiceId: 'ai_voice',
        catchphrases: [
          'Анализирую вероятности...',
          'Интересный паттерн поведения',
          'Обновляю стратегию в реальном времени'
        ]
      },
      {
        id: 'lucky_dmitri',
        name: 'Везунчик Дмитрий',
        description: 'Оптимистичный игрок, который верит в удачу',
        playStyle: 'balanced',
        difficulty: 6,
        specialties: ['intuition', 'risk_taking', 'entertainment'],
        avatar: '🍀',
        voiceId: 'dmitri_voice',
        catchphrases: [
          'Сегодня мой день!',
          'Чувствую, что повезёт',
          'Главное - получать удовольствие'
        ]
      },
      {
        id: 'strategic_elena',
        name: 'Стратег Елена',
        description: 'Математик и аналитик, просчитывающая каждый ход',
        playStyle: 'conservative',
        difficulty: 9,
        specialties: ['mathematics', 'probability', 'long_term_planning'],
        avatar: '📊',
        voiceId: 'elena_voice',
        catchphrases: [
          'Математика не лжёт',
          'Просчитываю все варианты',
          'Долгосрочная стратегия важнее'
        ]
      }
    ];

    personalities.forEach(p => this.personalities.set(p.id, p));
  }

  private async loadModels(): Promise<void> {
    try {
      // Загружаем предобученные модели для каждой игры
      const gameTypes = ['durak', 'poker', 'preferans', 'blackjack'];
      
      for (const gameType of gameTypes) {
        const modelPath = `./models/${gameType}_model.json`;
        try {
          const model = await TensorFlow.loadLayersModel(`file://${modelPath}`);
          this.models.set(gameType, model);
          logger.info(`Loaded AI model for ${gameType}`);
        } catch (error) {
          logger.warn(`Could not load model for ${gameType}, using fallback`);
          // Создаём простую модель как fallback
          this.models.set(gameType, this.createFallbackModel(gameType));
        }
      }
    } catch (error) {
      logger.error('Error loading AI models:', error);
    }
  }

  private createFallbackModel(gameType: string): any {
    // Создаём простую нейронную сеть как fallback
    const model = TensorFlow.sequential({
      layers: [
        TensorFlow.layers.dense({ inputShape: [52], units: 128, activation: 'relu' }),
        TensorFlow.layers.dropout({ rate: 0.2 }),
        TensorFlow.layers.dense({ units: 64, activation: 'relu' }),
        TensorFlow.layers.dense({ units: 32, activation: 'relu' }),
        TensorFlow.layers.dense({ units: 10, activation: 'softmax' })
      ]
    });

    model.compile({
      optimizer: 'adam',
      loss: 'categoricalCrossentropy',
      metrics: ['accuracy']
    });

    return model;
  }

  // Анализ игры и предложение ходов
  public async analyzeGame(gameState: any, gameType: string, userId: string): Promise<GameAnalysis> {
    try {
      const model = this.models.get(gameType);
      const playerProfile = this.playerProfiles.get(userId);

      // Преобразуем состояние игры в тензор
      const gameVector = this.gameStateToVector(gameState, gameType);
      
      // Получаем предсказания от модели
      const predictions = model ? await model.predict(gameVector).data() : null;
      
      // Анализируем с помощью GPT для более детального объяснения
      const gptAnalysis = await this.getGPTAnalysis(gameState, gameType, playerProfile);
      
      // Комбинируем результаты
      const analysis: GameAnalysis = {
        moveQuality: this.calculateMoveQuality(gameState, predictions),
        suggestedMove: this.getBestMove(gameState, predictions, gameType),
        reasoning: gptAnalysis.reasoning,
        riskAssessment: gptAnalysis.riskAssessment,
        winProbability: this.calculateWinProbability(gameState, predictions),
        alternativeMoves: await this.getAlternativeMoves(gameState, gameType, 3)
      };

      // Обновляем профиль игрока
      this.updatePlayerProfile(userId, gameState, analysis);

      return analysis;
    } catch (error) {
      logger.error('Error analyzing game:', error);
      return this.getFallbackAnalysis(gameState, gameType);
    }
  }

  private async getGPTAnalysis(gameState: any, gameType: string, playerProfile?: PlayerProfile): Promise<any> {
    const prompt = `
Проанализируй текущую ситуацию в игре ${gameType}:

Состояние игры: ${JSON.stringify(gameState, null, 2)}
${playerProfile ? `Профиль игрока: Уровень навыка ${playerProfile.skillLevel}, стиль игры: ${playerProfile.playStyle}` : ''}

Дай подробный анализ:
1. Оценка текущей позиции (1-100)
2. Рекомендуемый ход и почему
3. Уровень риска (1-10)
4. Вероятность победы (%)
5. Что нужно учесть в данной ситуации
6. Обучающие комментарии для улучшения игры

Ответь в формате JSON.
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.7,
        max_tokens: 1000
      });

      return JSON.parse(response.choices[0].message.content || '{}');
    } catch (error) {
      logger.error('GPT analysis error:', error);
      return {
        reasoning: 'Анализ недоступен',
        riskAssessment: 5,
        winProbability: 50
      };
    }
  }

  // Персонализированное обучение
  public async createLearningPlan(userId: string): Promise<any> {
    const profile = this.playerProfiles.get(userId);
    if (!profile) {
      return this.createDefaultLearningPlan();
    }

    const prompt = `
Создай персональный план обучения для игрока:

Профиль: ${JSON.stringify(profile, null, 2)}

План должен включать:
1. Текущий уровень и оценка навыков
2. Приоритетные области для улучшения
3. Конкретные упражнения и задания
4. Рекомендуемые игры и режимы
5. Цели на ближайшие недели
6. Мотивационные элементы

Ответь в формате JSON.
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.8,
        max_tokens: 1500
      });

      return JSON.parse(response.choices[0].message.content || '{}');
    } catch (error) {
      logger.error('Learning plan creation error:', error);
      return this.createDefaultLearningPlan();
    }
  }

  // Адаптивный ИИ-противник
  public async getAIMove(gameState: any, gameType: string, personalityId: string, opponentProfile?: PlayerProfile): Promise<any> {
    const personality = this.personalities.get(personalityId);
    if (!personality) {
      throw new Error('Personality not found');
    }

    // Адаптируем сложность под уровень игрока
    const adaptedDifficulty = this.adaptDifficulty(personality.difficulty, opponentProfile);
    
    // Получаем базовый ход от модели
    const model = this.models.get(gameType);
    const gameVector = this.gameStateToVector(gameState, gameType);
    const predictions = model ? await model.predict(gameVector).data() : null;
    
    // Применяем стиль личности
    const move = this.applyPersonalityToMove(
      this.getBestMove(gameState, predictions, gameType),
      personality,
      adaptedDifficulty,
      gameState
    );

    // Генерируем комментарий от ИИ
    const comment = await this.generateAIComment(personality, gameState, move);

    return {
      move,
      comment,
      personality: personality.name,
      confidence: this.calculateConfidence(predictions, adaptedDifficulty)
    };
  }

  private adaptDifficulty(baseDifficulty: number, opponentProfile?: PlayerProfile): number {
    if (!opponentProfile) return baseDifficulty;

    // Адаптируем сложность под уровень игрока
    const playerSkill = opponentProfile.skillLevel;
    const targetDifficulty = Math.max(1, Math.min(10, playerSkill + 1));
    
    // Плавно адаптируем к целевой сложности
    return baseDifficulty * 0.7 + targetDifficulty * 0.3;
  }

  private applyPersonalityToMove(baseMove: any, personality: AIPersonality, difficulty: number, gameState: any): any {
    // Применяем стиль игры личности
    switch (personality.playStyle) {
      case 'aggressive':
        return this.makeMoreAggressive(baseMove, difficulty);
      case 'conservative':
        return this.makeMoreConservative(baseMove, difficulty);
      case 'unpredictable':
        return this.addUnpredictability(baseMove, difficulty);
      default:
        return baseMove;
    }
  }

  private async generateAIComment(personality: AIPersonality, gameState: any, move: any): Promise<string> {
    // Случайно выбираем между заготовленными фразами и генерацией новых
    if (Math.random() < 0.3) {
      return personality.catchphrases[Math.floor(Math.random() * personality.catchphrases.length)];
    }

    const prompt = `
Ты играешь роль ${personality.name}: ${personality.description}
Стиль игры: ${personality.playStyle}

Сделай короткий комментарий (1-2 предложения) о своём ходе в характере персонажа.
Ход: ${JSON.stringify(move)}
Состояние игры: ${JSON.stringify(gameState)}

Комментарий должен быть в характере персонажа и не более 100 символов.
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.9,
        max_tokens: 50
      });

      return response.choices[0].message.content || personality.catchphrases[0];
    } catch (error) {
      return personality.catchphrases[Math.floor(Math.random() * personality.catchphrases.length)];
    }
  }

  // Анализ эмоционального состояния игрока
  public async analyzePlayerEmotion(chatMessages: string[], gameActions: any[]): Promise<any> {
    const prompt = `
Проанализируй эмоциональное состояние игрока на основе:

Сообщения в чате: ${JSON.stringify(chatMessages)}
Игровые действия: ${JSON.stringify(gameActions)}

Определи:
1. Текущее настроение (радость, фрустрация, концентрация, усталость)
2. Уровень стресса (1-10)
3. Мотивация к игре (1-10)
4. Рекомендации по адаптации игрового процесса
5. Нужна ли поддержка или перерыв

Ответь в формате JSON.
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.6,
        max_tokens: 500
      });

      return JSON.parse(response.choices[0].message.content || '{}');
    } catch (error) {
      logger.error('Emotion analysis error:', error);
      return {
        mood: 'neutral',
        stress: 5,
        motivation: 7,
        recommendations: ['Продолжайте играть в своём темпе']
      };
    }
  }

  // Вспомогательные методы
  private gameStateToVector(gameState: any, gameType: string): any {
    // Преобразуем состояние игры в числовой вектор для нейросети
    // Это упрощённая версия - в реальности нужна более сложная обработка
    const vector = new Array(52).fill(0);
    
    // Заполняем вектор на основе карт и состояния игры
    if (gameState.hand) {
      gameState.hand.forEach((card: any, index: number) => {
        if (index < 52) {
          vector[index] = this.cardToNumber(card);
        }
      });
    }

    return TensorFlow.tensor2d([vector]);
  }

  private cardToNumber(card: any): number {
    // Преобразуем карту в число для нейросети
    const suits = { 'hearts': 0, 'diamonds': 1, 'clubs': 2, 'spades': 3 };
    const ranks = { '6': 6, '7': 7, '8': 8, '9': 9, '10': 10, 'J': 11, 'Q': 12, 'K': 13, 'A': 14 };
    
    return (suits[card.suit] || 0) * 15 + (ranks[card.rank] || 0);
  }

  private calculateMoveQuality(gameState: any, predictions: any): number {
    // Оцениваем качество хода на основе предсказаний модели
    if (!predictions) return 75; // Базовая оценка
    
    const maxPrediction = Math.max(...Array.from(predictions));
    return Math.round(maxPrediction * 100);
  }

  private getBestMove(gameState: any, predictions: any, gameType: string): any {
    // Возвращаем лучший ход на основе предсказаний
    if (!predictions) {
      return this.getRandomValidMove(gameState, gameType);
    }

    const bestIndex = Array.from(predictions).indexOf(Math.max(...Array.from(predictions)));
    return this.indexToMove(bestIndex, gameState, gameType);
  }

  private calculateWinProbability(gameState: any, predictions: any): number {
    // Рассчитываем вероятность победы
    if (!predictions) return 50;
    
    // Упрощённый расчёт на основе силы позиции
    const avgPrediction = Array.from(predictions).reduce((a: number, b: number) => a + b, 0) / predictions.length;
    return Math.round(avgPrediction * 100);
  }

  private async getAlternativeMoves(gameState: any, gameType: string, count: number): Promise<any[]> {
    // Возвращаем альтернативные ходы с оценками
    const alternatives = [];
    
    for (let i = 0; i < count; i++) {
      alternatives.push({
        move: this.getRandomValidMove(gameState, gameType),
        quality: Math.floor(Math.random() * 40) + 40, // 40-80
        reasoning: `Альтернативный вариант ${i + 1}`
      });
    }

    return alternatives;
  }

  private updatePlayerProfile(userId: string, gameState: any, analysis: GameAnalysis): void {
    let profile = this.playerProfiles.get(userId);
    
    if (!profile) {
      profile = {
        userId,
        skillLevel: 5,
        playStyle: 'balanced',
        strengths: [],
        weaknesses: [],
        learningGoals: [],
        preferredDifficulty: 5,
        gameHistory: [],
        improvementAreas: []
      };
    }

    // Обновляем профиль на основе анализа
    profile.gameHistory.push({
      timestamp: new Date(),
      gameState,
      analysis,
      moveQuality: analysis.moveQuality
    });

    // Адаптируем уровень навыка
    const recentQuality = profile.gameHistory.slice(-10).reduce((sum, game) => sum + game.moveQuality, 0) / Math.min(10, profile.gameHistory.length);
    profile.skillLevel = Math.max(1, Math.min(10, Math.round(recentQuality / 10)));

    this.playerProfiles.set(userId, profile);
  }

  private getFallbackAnalysis(gameState: any, gameType: string): GameAnalysis {
    return {
      moveQuality: 60,
      suggestedMove: this.getRandomValidMove(gameState, gameType),
      reasoning: 'Базовый анализ недоступен',
      riskAssessment: 5,
      winProbability: 50,
      alternativeMoves: []
    };
  }

  private createDefaultLearningPlan(): any {
    return {
      currentLevel: 'Начинающий',
      priorities: ['Изучение базовых правил', 'Практика простых ситуаций'],
      exercises: ['Игра против ИИ на лёгком уровне', 'Изучение обучающих материалов'],
      goals: ['Достичь 70% побед против лёгкого ИИ'],
      motivation: 'Каждая игра делает вас лучше!'
    };
  }

  private getRandomValidMove(gameState: any, gameType: string): any {
    // Возвращаем случайный валидный ход
    return { type: 'random', gameType };
  }

  private indexToMove(index: number, gameState: any, gameType: string): any {
    // Преобразуем индекс предсказания в игровой ход
    return { type: 'predicted', index, gameType };
  }

  private makeMoreAggressive(move: any, difficulty: number): any {
    // Делаем ход более агрессивным
    return { ...move, style: 'aggressive', risk: Math.min(10, difficulty + 2) };
  }

  private makeMoreConservative(move: any, difficulty: number): any {
    // Делаем ход более консервативным
    return { ...move, style: 'conservative', risk: Math.max(1, difficulty - 2) };
  }

  private addUnpredictability(move: any, difficulty: number): any {
    // Добавляем непредсказуемость
    const randomFactor = Math.random() * 0.3; // 30% случайности
    return { ...move, style: 'unpredictable', randomness: randomFactor };
  }

  private calculateConfidence(predictions: any, difficulty: number): number {
    if (!predictions) return 70;
    
    const maxPrediction = Math.max(...Array.from(predictions));
    return Math.round(maxPrediction * difficulty * 10);
  }

  // Публичные методы для получения данных
  public getPersonalities(): AIPersonality[] {
    return Array.from(this.personalities.values());
  }

  public getPlayerProfile(userId: string): PlayerProfile | undefined {
    return this.playerProfiles.get(userId);
  }

  public async trainModel(gameType: string, trainingData: any[]): Promise<void> {
    // Дообучение модели на новых данных
    const model = this.models.get(gameType);
    if (!model || !trainingData.length) return;

    try {
      // Подготавливаем данные для обучения
      const xs = trainingData.map(data => this.gameStateToVector(data.gameState, gameType));
      const ys = trainingData.map(data => data.outcome);

      // Дообучаем модель
      await model.fit(TensorFlow.stack(xs), TensorFlow.tensor2d(ys), {
        epochs: 10,
        batchSize: 32,
        validationSplit: 0.2
      });

      logger.info(`Model retrained for ${gameType} with ${trainingData.length} samples`);
    } catch (error) {
      logger.error(`Error training model for ${gameType}:`, error);
    }
  }
}
