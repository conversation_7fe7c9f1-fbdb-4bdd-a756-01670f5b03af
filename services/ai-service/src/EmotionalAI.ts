import { EventEmitter } from 'events';
import OpenAI from 'openai';
import { TensorFlow } from '@tensorflow/tfjs-node';
import { logger } from './utils/logger';

export interface EmotionalState {
  happiness: number;      // 0-1
  frustration: number;    // 0-1
  excitement: number;     // 0-1
  confidence: number;     // 0-1
  stress: number;         // 0-1
  focus: number;          // 0-1
  motivation: number;     // 0-1
  fatigue: number;        // 0-1
}

export interface PlayerPersonality {
  userId: string;
  traits: {
    openness: number;       // 0-1
    conscientiousness: number; // 0-1
    extraversion: number;   // 0-1
    agreeableness: number;  // 0-1
    neuroticism: number;    // 0-1
  };
  preferences: {
    riskTolerance: number;  // 0-1
    competitiveness: number; // 0-1
    socialness: number;     // 0-1
    learningStyle: 'visual' | 'auditory' | 'kinesthetic' | 'mixed';
    gameComplexity: 'simple' | 'medium' | 'complex' | 'expert';
  };
  emotionalProfile: {
    baseline: EmotionalState;
    volatility: number;     // 0-1
    resilience: number;     // 0-1
    empathy: number;        // 0-1
  };
}

export interface EmotionalResponse {
  message: string;
  tone: 'supportive' | 'encouraging' | 'challenging' | 'calming' | 'energizing';
  intensity: number;      // 0-1
  personalizedElements: string[];
  suggestedActions: string[];
  emotionalImpact: EmotionalState;
}

export interface LearningPath {
  userId: string;
  currentLevel: number;
  targetLevel: number;
  strengths: string[];
  weaknesses: string[];
  personalizedLessons: Lesson[];
  adaptiveSchedule: ScheduleItem[];
  motivationalElements: MotivationalElement[];
}

export interface Lesson {
  id: string;
  title: string;
  description: string;
  difficulty: number;
  estimatedTime: number;
  prerequisites: string[];
  content: LessonContent;
  adaptiveElements: AdaptiveElement[];
}

export interface LessonContent {
  theory: string;
  examples: GameExample[];
  exercises: Exercise[];
  assessments: Assessment[];
  multimedia: MultimediaContent[];
}

export interface AdaptiveElement {
  type: 'difficulty' | 'pace' | 'style' | 'content';
  condition: string;
  modification: any;
}

export class EmotionalAI extends EventEmitter {
  private openai: OpenAI;
  private emotionModel: any;
  private personalityModel: any;
  private playerProfiles: Map<string, PlayerPersonality> = new Map();
  private emotionalStates: Map<string, EmotionalState> = new Map();
  private learningPaths: Map<string, LearningPath> = new Map();
  private conversationHistory: Map<string, any[]> = new Map();

  constructor() {
    super();
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    this.initializeModels();
    this.startEmotionalMonitoring();
  }

  private async initializeModels(): Promise<void> {
    try {
      // Загружаем предобученные модели для анализа эмоций
      this.emotionModel = await TensorFlow.loadLayersModel('./models/emotion_recognition.json');
      this.personalityModel = await TensorFlow.loadLayersModel('./models/personality_analysis.json');

      logger.info('Emotional AI models loaded successfully');
    } catch (error) {
      logger.error('Error loading emotional AI models:', error);
      // Создаём fallback модели
      this.createFallbackModels();
    }
  }

  private createFallbackModels(): void {
    // Простые модели как fallback
    this.emotionModel = TensorFlow.sequential({
      layers: [
        TensorFlow.layers.dense({ inputShape: [100], units: 64, activation: 'relu' }),
        TensorFlow.layers.dropout({ rate: 0.3 }),
        TensorFlow.layers.dense({ units: 32, activation: 'relu' }),
        TensorFlow.layers.dense({ units: 8, activation: 'sigmoid' }) // 8 эмоций
      ]
    });

    this.personalityModel = TensorFlow.sequential({
      layers: [
        TensorFlow.layers.dense({ inputShape: [50], units: 32, activation: 'relu' }),
        TensorFlow.layers.dense({ units: 16, activation: 'relu' }),
        TensorFlow.layers.dense({ units: 5, activation: 'sigmoid' }) // Big Five
      ]
    });
  }

  private startEmotionalMonitoring(): void {
    // Мониторинг эмоционального состояния каждые 30 секунд
    setInterval(() => {
      this.updateEmotionalStates();
    }, 30000);

    // Адаптация обучающих материалов каждые 5 минут
    setInterval(() => {
      this.adaptLearningPaths();
    }, 300000);
  }

  // Анализ эмоционального состояния игрока
  public async analyzeEmotionalState(userId: string, gameData: any): Promise<EmotionalState> {
    try {
      const features = this.extractEmotionalFeatures(gameData);
      const predictions = await this.emotionModel.predict(features).data();

      const emotionalState: EmotionalState = {
        happiness: predictions[0],
        frustration: predictions[1],
        excitement: predictions[2],
        confidence: predictions[3],
        stress: predictions[4],
        focus: predictions[5],
        motivation: predictions[6],
        fatigue: predictions[7]
      };

      // Сохраняем состояние
      this.emotionalStates.set(userId, emotionalState);

      // Анализируем с помощью GPT для более глубокого понимания
      const contextualAnalysis = await this.getContextualEmotionalAnalysis(userId, gameData, emotionalState);

      this.emit('emotionalStateAnalyzed', {
        userId,
        state: emotionalState,
        context: contextualAnalysis
      });

      return emotionalState;
    } catch (error) {
      logger.error('Error analyzing emotional state:', error);
      return this.getDefaultEmotionalState();
    }
  }

  private extractEmotionalFeatures(gameData: any): any {
    // Извлекаем признаки для анализа эмоций
    const features = [
      gameData.reactionTime || 0,
      gameData.decisionConfidence || 0.5,
      gameData.chatSentiment || 0,
      gameData.gamePerformance || 0.5,
      gameData.streakLength || 0,
      gameData.timeSpent || 0,
      gameData.mistakeCount || 0,
      gameData.helpRequests || 0,
      // ... добавляем больше признаков
    ];

    // Дополняем до 100 признаков
    while (features.length < 100) {
      features.push(0);
    }

    return TensorFlow.tensor2d([features]);
  }

  private async getContextualEmotionalAnalysis(userId: string, gameData: any, emotionalState: EmotionalState): Promise<any> {
    const profile = this.playerProfiles.get(userId);
    const history = this.conversationHistory.get(userId) || [];

    const prompt = `
Проанализируй эмоциональное состояние игрока:

Текущие эмоции:
- Счастье: ${emotionalState.happiness.toFixed(2)}
- Фрустрация: ${emotionalState.frustration.toFixed(2)}
- Возбуждение: ${emotionalState.excitement.toFixed(2)}
- Уверенность: ${emotionalState.confidence.toFixed(2)}
- Стресс: ${emotionalState.stress.toFixed(2)}
- Фокус: ${emotionalState.focus.toFixed(2)}
- Мотивация: ${emotionalState.motivation.toFixed(2)}
- Усталость: ${emotionalState.fatigue.toFixed(2)}

Игровые данные: ${JSON.stringify(gameData)}
${profile ? `Профиль личности: ${JSON.stringify(profile.traits)}` : ''}

Дай рекомендации:
1. Как поддержать игрока
2. Какие изменения внести в игровой процесс
3. Персонализированные сообщения
4. Предупреждения о рисках (тильт, выгорание)

Ответь в формате JSON.
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.7,
        max_tokens: 1000
      });

      return JSON.parse(response.choices[0].message.content || '{}');
    } catch (error) {
      logger.error('Error in contextual emotional analysis:', error);
      return { recommendations: [], warnings: [] };
    }
  }

  // Анализ личности игрока
  public async analyzePersonality(userId: string, behaviorData: any[]): Promise<PlayerPersonality> {
    try {
      const features = this.extractPersonalityFeatures(behaviorData);
      const predictions = await this.personalityModel.predict(features).data();

      const personality: PlayerPersonality = {
        userId,
        traits: {
          openness: predictions[0],
          conscientiousness: predictions[1],
          extraversion: predictions[2],
          agreeableness: predictions[3],
          neuroticism: predictions[4]
        },
        preferences: await this.inferPreferences(userId, behaviorData),
        emotionalProfile: {
          baseline: await this.calculateEmotionalBaseline(userId),
          volatility: this.calculateEmotionalVolatility(userId),
          resilience: this.calculateResilience(userId),
          empathy: this.calculateEmpathy(userId)
        }
      };

      this.playerProfiles.set(userId, personality);

      // Создаём персонализированный путь обучения
      await this.createPersonalizedLearningPath(userId, personality);

      this.emit('personalityAnalyzed', { userId, personality });

      return personality;
    } catch (error) {
      logger.error('Error analyzing personality:', error);
      return this.getDefaultPersonality(userId);
    }
  }

  private extractPersonalityFeatures(behaviorData: any[]): any {
    // Извлекаем признаки личности из поведенческих данных
    const features = [
      this.calculateRiskTaking(behaviorData),
      this.calculateSocialInteraction(behaviorData),
      this.calculateLearningSpeed(behaviorData),
      this.calculatePersistence(behaviorData),
      this.calculateEmotionalStability(behaviorData),
      // ... добавляем больше признаков
    ];

    // Дополняем до 50 признаков
    while (features.length < 50) {
      features.push(0);
    }

    return TensorFlow.tensor2d([features]);
  }

  // Генерация эмоционально адаптивного ответа
  public async generateEmotionalResponse(userId: string, context: string, intent: string): Promise<EmotionalResponse> {
    const emotionalState = this.emotionalStates.get(userId) || this.getDefaultEmotionalState();
    const personality = this.playerProfiles.get(userId);
    const history = this.conversationHistory.get(userId) || [];

    const prompt = `
Ты эмоционально интеллектуальный ИИ-тренер для карточных игр.

Контекст: ${context}
Намерение: ${intent}

Эмоциональное состояние игрока:
${JSON.stringify(emotionalState)}

${personality ? `Личность игрока: ${JSON.stringify(personality.traits)}` : ''}

История общения (последние 3 сообщения):
${JSON.stringify(history.slice(-3))}

Создай персонализированный ответ, который:
1. Учитывает текущее эмоциональное состояние
2. Соответствует личности игрока
3. Мотивирует и поддерживает
4. Даёт конкретные советы
5. Использует подходящий тон и стиль

Ответь в формате JSON с полями:
- message: основное сообщение
- tone: тон сообщения
- intensity: интенсивность (0-1)
- personalizedElements: персонализированные элементы
- suggestedActions: предлагаемые действия
- emotionalImpact: ожидаемое влияние на эмоции
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.8,
        max_tokens: 800
      });

      const emotionalResponse = JSON.parse(response.choices[0].message.content || '{}');

      // Сохраняем в историю
      if (!this.conversationHistory.has(userId)) {
        this.conversationHistory.set(userId, []);
      }
      this.conversationHistory.get(userId)!.push({
        timestamp: Date.now(),
        context,
        intent,
        response: emotionalResponse,
        emotionalState: { ...emotionalState }
      });

      return emotionalResponse;
    } catch (error) {
      logger.error('Error generating emotional response:', error);
      return this.getDefaultEmotionalResponse();
    }
  }

  // Создание персонализированного пути обучения
  public async createPersonalizedLearningPath(userId: string, personality: PlayerPersonality): Promise<LearningPath> {
    const currentSkills = await this.assessCurrentSkills(userId);
    const learningGoals = await this.identifyLearningGoals(userId, personality);

    const learningPath: LearningPath = {
      userId,
      currentLevel: currentSkills.overallLevel,
      targetLevel: learningGoals.targetLevel,
      strengths: currentSkills.strengths,
      weaknesses: currentSkills.weaknesses,
      personalizedLessons: await this.generatePersonalizedLessons(userId, personality, currentSkills),
      adaptiveSchedule: await this.createAdaptiveSchedule(userId, personality),
      motivationalElements: await this.createMotivationalElements(userId, personality)
    };

    this.learningPaths.set(userId, learningPath);

    this.emit('learningPathCreated', { userId, learningPath });

    return learningPath;
  }

  private async generatePersonalizedLessons(userId: string, personality: PlayerPersonality, skills: any): Promise<Lesson[]> {
    const lessons: Lesson[] = [];

    // Генерируем уроки на основе личности и навыков
    const lessonTopics = this.selectLessonTopics(personality, skills);

    for (const topic of lessonTopics) {
      const lesson = await this.createAdaptiveLesson(topic, personality, skills);
      lessons.push(lesson);
    }

    return lessons;
  }

  private async createAdaptiveLesson(topic: string, personality: PlayerPersonality, skills: any): Promise<Lesson> {
    const prompt = `
Создай персонализированный урок по теме "${topic}" для игрока с характеристиками:

Личность: ${JSON.stringify(personality.traits)}
Предпочтения: ${JSON.stringify(personality.preferences)}
Текущие навыки: ${JSON.stringify(skills)}

Урок должен включать:
1. Теоретическую часть (адаптированную под стиль обучения)
2. Практические примеры
3. Интерактивные упражнения
4. Систему оценки прогресса
5. Мотивационные элементы

Ответь в формате JSON.
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.7,
        max_tokens: 1500
      });

      const lessonData = JSON.parse(response.choices[0].message.content || '{}');

      return {
        id: `lesson_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        title: lessonData.title || topic,
        description: lessonData.description || '',
        difficulty: lessonData.difficulty || 5,
        estimatedTime: lessonData.estimatedTime || 30,
        prerequisites: lessonData.prerequisites || [],
        content: lessonData.content || {},
        adaptiveElements: lessonData.adaptiveElements || []
      };
    } catch (error) {
      logger.error('Error creating adaptive lesson:', error);
      return this.getDefaultLesson(topic);
    }
  }

  // Адаптивная система мотивации
  public async generateMotivationalContent(userId: string, context: 'victory' | 'defeat' | 'learning' | 'challenge'): Promise<any> {
    const emotionalState = this.emotionalStates.get(userId) || this.getDefaultEmotionalState();
    const personality = this.playerProfiles.get(userId);

    const motivationalStrategies = this.selectMotivationalStrategies(personality, emotionalState, context);

    const content = {
      message: await this.generateMotivationalMessage(userId, context, motivationalStrategies),
      visualElements: await this.generateVisualMotivation(personality, context),
      audioElements: await this.generateAudioMotivation(personality, context),
      gameplayModifications: await this.suggestGameplayModifications(userId, context),
      rewards: await this.generatePersonalizedRewards(userId, context)
    };

    return content;
  }

  // Предиктивная аналитика эмоций
  public async predictEmotionalTrajectory(userId: string, plannedActions: any[]): Promise<any> {
    const currentState = this.emotionalStates.get(userId) || this.getDefaultEmotionalState();
    const personality = this.playerProfiles.get(userId);
    const history = this.getEmotionalHistory(userId);

    // Используем модель для предсказания эмоциональной траектории
    const predictions = [];

    for (const action of plannedActions) {
      const predictedState = await this.predictEmotionalResponse(currentState, action, personality, history);
      predictions.push({
        action,
        predictedState,
        confidence: this.calculatePredictionConfidence(history, action),
        recommendations: await this.generatePreventiveRecommendations(predictedState)
      });
    }

    return {
      currentState,
      predictions,
      overallTrend: this.analyzeTrend(predictions),
      riskFactors: this.identifyRiskFactors(predictions),
      interventions: await this.suggestInterventions(predictions)
    };
  }

  // Вспомогательные методы
  private getDefaultEmotionalState(): EmotionalState {
    return {
      happiness: 0.5,
      frustration: 0.3,
      excitement: 0.4,
      confidence: 0.5,
      stress: 0.3,
      focus: 0.6,
      motivation: 0.7,
      fatigue: 0.2
    };
  }

  private getDefaultPersonality(userId: string): PlayerPersonality {
    return {
      userId,
      traits: {
        openness: 0.5,
        conscientiousness: 0.5,
        extraversion: 0.5,
        agreeableness: 0.5,
        neuroticism: 0.5
      },
      preferences: {
        riskTolerance: 0.5,
        competitiveness: 0.5,
        socialness: 0.5,
        learningStyle: 'mixed',
        gameComplexity: 'medium'
      },
      emotionalProfile: {
        baseline: this.getDefaultEmotionalState(),
        volatility: 0.3,
        resilience: 0.7,
        empathy: 0.6
      }
    };
  }

  private getDefaultEmotionalResponse(): EmotionalResponse {
    return {
      message: "Продолжайте играть! У вас всё получится!",
      tone: 'encouraging',
      intensity: 0.6,
      personalizedElements: [],
      suggestedActions: ['Сделайте перерыв', 'Попробуйте новую стратегию'],
      emotionalImpact: this.getDefaultEmotionalState()
    };
  }

  // Заглушки для методов (в реальности нужна полная реализация)
  private calculateRiskTaking(data: any[]): number { return 0.5; }
  private calculateSocialInteraction(data: any[]): number { return 0.5; }
  private calculateLearningSpeed(data: any[]): number { return 0.5; }
  private calculatePersistence(data: any[]): number { return 0.5; }
  private calculateEmotionalStability(data: any[]): number { return 0.5; }
  private async inferPreferences(userId: string, data: any[]): Promise<any> { return {}; }
  private async calculateEmotionalBaseline(userId: string): Promise<EmotionalState> { return this.getDefaultEmotionalState(); }
  private calculateEmotionalVolatility(userId: string): number { return 0.3; }
  private calculateResilience(userId: string): number { return 0.7; }
  private calculateEmpathy(userId: string): number { return 0.6; }
  private async assessCurrentSkills(userId: string): Promise<any> { return { overallLevel: 5, strengths: [], weaknesses: [] }; }
  private async identifyLearningGoals(userId: string, personality: PlayerPersonality): Promise<any> { return { targetLevel: 8 }; }
  private selectLessonTopics(personality: PlayerPersonality, skills: any): string[] { return ['Базовая стратегия', 'Психология игры']; }
  private async createAdaptiveSchedule(userId: string, personality: PlayerPersonality): Promise<any[]> { return []; }
  private async createMotivationalElements(userId: string, personality: PlayerPersonality): Promise<any[]> { return []; }
  private getDefaultLesson(topic: string): Lesson { return { id: '', title: topic, description: '', difficulty: 5, estimatedTime: 30, prerequisites: [], content: {} as any, adaptiveElements: [] }; }
  private updateEmotionalStates(): void { /* Реализация */ }
  private adaptLearningPaths(): void { /* Реализация */ }
  private selectMotivationalStrategies(personality: any, state: EmotionalState, context: string): any[] { return []; }
  private async generateMotivationalMessage(userId: string, context: string, strategies: any[]): Promise<string> { return "Мотивационное сообщение"; }
  private async generateVisualMotivation(personality: any, context: string): Promise<any> { return {}; }
  private async generateAudioMotivation(personality: any, context: string): Promise<any> { return {}; }
  private async suggestGameplayModifications(userId: string, context: string): Promise<any> { return {}; }
  private async generatePersonalizedRewards(userId: string, context: string): Promise<any> { return {}; }
  private getEmotionalHistory(userId: string): any[] { return []; }
  private async predictEmotionalResponse(current: EmotionalState, action: any, personality: any, history: any[]): Promise<EmotionalState> { return current; }
  private calculatePredictionConfidence(history: any[], action: any): number { return 0.8; }
  private async generatePreventiveRecommendations(state: EmotionalState): Promise<string[]> { return []; }
  private analyzeTrend(predictions: any[]): string { return 'stable'; }
  private identifyRiskFactors(predictions: any[]): string[] { return []; }
  private async suggestInterventions(predictions: any[]): Promise<any[]> { return []; }
}