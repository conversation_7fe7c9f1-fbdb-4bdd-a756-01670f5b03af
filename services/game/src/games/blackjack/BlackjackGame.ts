import { GameCore } from '../../GameCore';
import { Player } from '../../Player';
import { Card, Rank } from '../../Card';
import { Deck } from '../../Deck';

export interface BlackjackPlayer extends Player {
  hands: BlackjackHand[];
  chips: number;
  totalWinnings: number;
  insurance?: number;
  isActive: boolean;
  hasPlayed: boolean;
}

export interface BlackjackHand {
  cards: Card[];
  bet: number;
  status: 'playing' | 'stand' | 'bust' | 'blackjack' | 'surrender';
  value: number;
  softAce: boolean;
  doubled: boolean;
  split: boolean;
  insurance?: number;
}

export interface BlackjackDealer {
  cards: Card[];
  value: number;
  softAce: boolean;
  holeCard?: Card;
  revealed: boolean;
}

export enum BlackjackPhase {
  BETTING = 'betting',
  DEALING = 'dealing',
  INSURANCE = 'insurance',
  PLAYING = 'playing',
  DEALER_TURN = 'dealer_turn',
  PAYOUT = 'payout',
  FINISHED = 'finished'
}

export class BlackjackGame extends GameCore {
  private players: BlackjackPlayer[] = [];
  private dealer: BlackjackDealer;
  private deck: Deck;
  private currentPlayerIndex: number = 0;
  private currentHandIndex: number = 0;
  private phase: BlackjackPhase = BlackjackPhase.BETTING;
  private minBet: number = 10;
  private maxBet: number = 1000;
  private roundNumber: number = 1;
  private insuranceOffered: boolean = false;

  constructor(minBet: number = 10, maxBet: number = 1000) {
    super();
    this.minBet = minBet;
    this.maxBet = maxBet;
    this.deck = new Deck();
    this.dealer = {
      cards: [],
      value: 0,
      softAce: false,
      revealed: false
    };
    this.maxPlayers = 7;
    this.minPlayers = 1;
  }

  public addPlayer(player: Player, chips: number = 1000): boolean {
    if (this.players.length >= this.maxPlayers) {
      return false;
    }

    const blackjackPlayer: BlackjackPlayer = {
      ...player,
      hands: [],
      chips,
      totalWinnings: 0,
      isActive: true,
      hasPlayed: false
    };

    this.players.push(blackjackPlayer);
    
    this.emit('player:joined', {
      player: {
        id: blackjackPlayer.id,
        name: blackjackPlayer.name,
        chips: blackjackPlayer.chips,
        position: this.players.length - 1
      },
      totalPlayers: this.players.length
    });

    return true;
  }

  public startRound(): void {
    if (this.players.length === 0) return;

    this.phase = BlackjackPhase.BETTING;
    this.currentPlayerIndex = 0;
    this.currentHandIndex = 0;
    this.insuranceOffered = false;

    // Сброс состояния игроков
    this.players.forEach(player => {
      player.hands = [];
      player.hasPlayed = false;
      player.insurance = undefined;
    });

    // Сброс дилера
    this.dealer = {
      cards: [],
      value: 0,
      softAce: false,
      revealed: false
    };

    // Перетасовка колоды если осталось мало карт
    if (this.deck.remainingCards() < 20) {
      this.deck = new Deck();
      this.deck.shuffle();
    }

    this.emit('round:started', {
      roundNumber: this.roundNumber,
      phase: this.phase,
      minBet: this.minBet,
      maxBet: this.maxBet,
      players: this.players.map(p => ({
        id: p.id,
        name: p.name,
        chips: p.chips
      }))
    });
  }

  public placeBet(playerId: string, amount: number): boolean {
    if (this.phase !== BlackjackPhase.BETTING) return false;
    
    const player = this.players.find(p => p.id === playerId);
    if (!player || amount < this.minBet || amount > this.maxBet || amount > player.chips) {
      return false;
    }

    player.chips -= amount;
    player.hands = [{
      cards: [],
      bet: amount,
      status: 'playing',
      value: 0,
      softAce: false,
      doubled: false,
      split: false
    }];

    this.emit('bet:placed', {
      playerId,
      amount,
      remainingChips: player.chips
    });

    // Проверяем, все ли сделали ставки
    const allBetsPlaced = this.players.every(p => p.hands.length > 0);
    if (allBetsPlaced) {
      this.dealInitialCards();
    }

    return true;
  }

  private dealInitialCards(): void {
    this.phase = BlackjackPhase.DEALING;

    // Раздаем по 2 карты каждому игроку и дилеру
    for (let round = 0; round < 2; round++) {
      // Игрокам
      this.players.forEach(player => {
        if (player.hands.length > 0) {
          const card = this.deck.dealCard()!;
          player.hands[0].cards.push(card);
        }
      });

      // Дилеру
      const dealerCard = this.deck.dealCard()!;
      this.dealer.cards.push(dealerCard);
      
      // Вторая карта дилера - закрытая
      if (round === 1) {
        this.dealer.holeCard = dealerCard;
      }
    }

    // Подсчитываем значения рук
    this.players.forEach(player => {
      if (player.hands.length > 0) {
        this.updateHandValue(player.hands[0]);
        
        // Проверяем блэкджек
        if (player.hands[0].value === 21) {
          player.hands[0].status = 'blackjack';
        }
      }
    });

    this.updateDealerValue();

    this.emit('cards:dealt', {
      players: this.players.map(p => ({
        id: p.id,
        hands: p.hands.map(h => ({
          cards: h.cards,
          value: h.value,
          status: h.status
        }))
      })),
      dealer: {
        cards: [this.dealer.cards[0]], // Показываем только первую карту
        value: this.getCardValue(this.dealer.cards[0])
      }
    });

    // Проверяем страховку
    if (this.getCardValue(this.dealer.cards[0]) === 11) {
      this.offerInsurance();
    } else {
      this.startPlayerTurns();
    }
  }

  private offerInsurance(): void {
    this.phase = BlackjackPhase.INSURANCE;
    this.insuranceOffered = true;

    this.emit('insurance:offered', {
      dealerUpCard: this.dealer.cards[0]
    });

    // Автоматически переходим к игре через некоторое время
    setTimeout(() => {
      this.startPlayerTurns();
    }, 10000);
  }

  public buyInsurance(playerId: string): boolean {
    if (this.phase !== BlackjackPhase.INSURANCE) return false;
    
    const player = this.players.find(p => p.id === playerId);
    if (!player || player.hands.length === 0) return false;

    const insuranceAmount = Math.floor(player.hands[0].bet / 2);
    if (player.chips < insuranceAmount) return false;

    player.chips -= insuranceAmount;
    player.insurance = insuranceAmount;

    this.emit('insurance:bought', {
      playerId,
      amount: insuranceAmount,
      remainingChips: player.chips
    });

    return true;
  }

  private startPlayerTurns(): void {
    this.phase = BlackjackPhase.PLAYING;
    this.currentPlayerIndex = 0;
    this.currentHandIndex = 0;

    // Проверяем блэкджек дилера
    if (this.dealer.value === 21) {
      this.revealDealerCards();
      this.payoutInsurance();
      this.finishRound();
      return;
    }

    // Находим первого активного игрока
    this.findNextActivePlayer();

    this.emit('player:turn_started', {
      currentPlayer: this.getCurrentPlayer(),
      currentHand: this.getCurrentHand(),
      availableActions: this.getAvailableActions()
    });
  }

  public hit(playerId: string, handIndex: number = 0): boolean {
    if (!this.isCurrentPlayer(playerId, handIndex)) return false;

    const player = this.players.find(p => p.id === playerId)!;
    const hand = player.hands[handIndex];

    const card = this.deck.dealCard()!;
    hand.cards.push(card);
    this.updateHandValue(hand);

    this.emit('card:dealt', {
      playerId,
      handIndex,
      card,
      handValue: hand.value,
      handStatus: hand.status
    });

    // Проверяем перебор
    if (hand.value > 21) {
      hand.status = 'bust';
      this.nextPlayer();
    }

    return true;
  }

  public stand(playerId: string, handIndex: number = 0): boolean {
    if (!this.isCurrentPlayer(playerId, handIndex)) return false;

    const player = this.players.find(p => p.id === playerId)!;
    const hand = player.hands[handIndex];

    hand.status = 'stand';

    this.emit('player:stand', {
      playerId,
      handIndex,
      handValue: hand.value
    });

    this.nextPlayer();
    return true;
  }

  public doubleDown(playerId: string, handIndex: number = 0): boolean {
    if (!this.isCurrentPlayer(playerId, handIndex)) return false;

    const player = this.players.find(p => p.id === playerId)!;
    const hand = player.hands[handIndex];

    if (hand.cards.length !== 2 || player.chips < hand.bet || hand.doubled) {
      return false;
    }

    player.chips -= hand.bet;
    hand.bet *= 2;
    hand.doubled = true;

    // Берем одну карту
    const card = this.deck.dealCard()!;
    hand.cards.push(card);
    this.updateHandValue(hand);

    if (hand.value > 21) {
      hand.status = 'bust';
    } else {
      hand.status = 'stand';
    }

    this.emit('player:doubled', {
      playerId,
      handIndex,
      card,
      newBet: hand.bet,
      handValue: hand.value,
      handStatus: hand.status,
      remainingChips: player.chips
    });

    this.nextPlayer();
    return true;
  }

  public split(playerId: string, handIndex: number = 0): boolean {
    if (!this.isCurrentPlayer(playerId, handIndex)) return false;

    const player = this.players.find(p => p.id === playerId)!;
    const hand = player.hands[handIndex];

    if (hand.cards.length !== 2 || 
        this.getCardValue(hand.cards[0]) !== this.getCardValue(hand.cards[1]) ||
        player.chips < hand.bet ||
        player.hands.length >= 4) {
      return false;
    }

    // Создаем новую руку
    const newHand: BlackjackHand = {
      cards: [hand.cards.pop()!],
      bet: hand.bet,
      status: 'playing',
      value: 0,
      softAce: false,
      doubled: false,
      split: true
    };

    player.chips -= hand.bet;
    player.hands.push(newHand);

    // Добавляем по карте в каждую руку
    hand.cards.push(this.deck.dealCard()!);
    newHand.cards.push(this.deck.dealCard()!);

    this.updateHandValue(hand);
    this.updateHandValue(newHand);

    this.emit('player:split', {
      playerId,
      hands: player.hands.map(h => ({
        cards: h.cards,
        value: h.value,
        bet: h.bet
      })),
      remainingChips: player.chips
    });

    return true;
  }

  public surrender(playerId: string, handIndex: number = 0): boolean {
    if (!this.isCurrentPlayer(playerId, handIndex)) return false;

    const player = this.players.find(p => p.id === playerId)!;
    const hand = player.hands[handIndex];

    if (hand.cards.length !== 2) return false;

    hand.status = 'surrender';
    player.chips += Math.floor(hand.bet / 2); // Возвращаем половину ставки

    this.emit('player:surrendered', {
      playerId,
      handIndex,
      refund: Math.floor(hand.bet / 2),
      remainingChips: player.chips
    });

    this.nextPlayer();
    return true;
  }

  private nextPlayer(): void {
    // Переходим к следующей руке текущего игрока
    this.currentHandIndex++;
    
    const currentPlayer = this.players[this.currentPlayerIndex];
    if (this.currentHandIndex < currentPlayer.hands.length) {
      const currentHand = currentPlayer.hands[this.currentHandIndex];
      if (currentHand.status === 'playing') {
        this.emit('player:turn_started', {
          currentPlayer: this.getCurrentPlayer(),
          currentHand: this.getCurrentHand(),
          availableActions: this.getAvailableActions()
        });
        return;
      }
    }

    // Переходим к следующему игроку
    this.currentPlayerIndex++;
    this.currentHandIndex = 0;

    if (!this.findNextActivePlayer()) {
      this.dealerTurn();
    }
  }

  private findNextActivePlayer(): boolean {
    while (this.currentPlayerIndex < this.players.length) {
      const player = this.players[this.currentPlayerIndex];
      
      // Находим первую активную руку
      for (let i = this.currentHandIndex; i < player.hands.length; i++) {
        if (player.hands[i].status === 'playing') {
          this.currentHandIndex = i;
          this.emit('player:turn_started', {
            currentPlayer: this.getCurrentPlayer(),
            currentHand: this.getCurrentHand(),
            availableActions: this.getAvailableActions()
          });
          return true;
        }
      }

      this.currentPlayerIndex++;
      this.currentHandIndex = 0;
    }

    return false;
  }

  private dealerTurn(): void {
    this.phase = BlackjackPhase.DEALER_TURN;
    this.revealDealerCards();

    // Дилер берет карты до 17
    while (this.dealer.value < 17 || (this.dealer.value === 17 && this.dealer.softAce)) {
      const card = this.deck.dealCard()!;
      this.dealer.cards.push(card);
      this.updateDealerValue();

      this.emit('dealer:card_dealt', {
        card,
        dealerValue: this.dealer.value,
        dealerCards: this.dealer.cards
      });
    }

    this.finishRound();
  }

  private revealDealerCards(): void {
    this.dealer.revealed = true;
    this.updateDealerValue();

    this.emit('dealer:cards_revealed', {
      cards: this.dealer.cards,
      value: this.dealer.value
    });
  }

  private payoutInsurance(): void {
    if (!this.insuranceOffered) return;

    const dealerHasBlackjack = this.dealer.value === 21;

    this.players.forEach(player => {
      if (player.insurance) {
        if (dealerHasBlackjack) {
          // Страховка выигрывает 2:1
          const payout = player.insurance * 3;
          player.chips += payout;
          player.totalWinnings += player.insurance * 2;
        }
        // Если дилер не имеет блэкджека, страховка просто теряется
      }
    });
  }

  private finishRound(): void {
    this.phase = BlackjackPhase.PAYOUT;
    
    const results = this.players.map(player => {
      const playerResults = player.hands.map(hand => {
        let payout = 0;
        let result = '';

        if (hand.status === 'surrender') {
          result = 'surrender';
        } else if (hand.status === 'bust') {
          result = 'bust';
        } else if (hand.status === 'blackjack' && this.dealer.value !== 21) {
          payout = Math.floor(hand.bet * 2.5); // Блэкджек платит 3:2
          result = 'blackjack';
        } else if (this.dealer.value > 21) {
          payout = hand.bet * 2;
          result = 'dealer_bust';
        } else if (hand.value > this.dealer.value) {
          payout = hand.bet * 2;
          result = 'win';
        } else if (hand.value === this.dealer.value) {
          payout = hand.bet; // Возврат ставки
          result = 'push';
        } else {
          result = 'lose';
        }

        player.chips += payout;
        if (payout > hand.bet) {
          player.totalWinnings += (payout - hand.bet);
        } else if (payout < hand.bet) {
          player.totalWinnings -= (hand.bet - payout);
        }

        return {
          handIndex: player.hands.indexOf(hand),
          cards: hand.cards,
          value: hand.value,
          bet: hand.bet,
          payout,
          result
        };
      });

      return {
        playerId: player.id,
        name: player.name,
        hands: playerResults,
        chips: player.chips,
        totalWinnings: player.totalWinnings
      };
    });

    this.emit('round:finished', {
      results,
      dealer: {
        cards: this.dealer.cards,
        value: this.dealer.value
      },
      roundNumber: this.roundNumber
    });

    this.roundNumber++;

    // Автоматически начинаем новый раунд через некоторое время
    setTimeout(() => {
      this.startRound();
    }, 5000);
  }

  private updateHandValue(hand: BlackjackHand): void {
    let value = 0;
    let aces = 0;

    hand.cards.forEach(card => {
      const cardValue = this.getCardValue(card);
      if (cardValue === 11) {
        aces++;
      }
      value += cardValue;
    });

    // Обрабатываем тузы
    while (value > 21 && aces > 0) {
      value -= 10;
      aces--;
    }

    hand.value = value;
    hand.softAce = aces > 0;
  }

  private updateDealerValue(): void {
    let value = 0;
    let aces = 0;

    this.dealer.cards.forEach(card => {
      const cardValue = this.getCardValue(card);
      if (cardValue === 11) {
        aces++;
      }
      value += cardValue;
    });

    while (value > 21 && aces > 0) {
      value -= 10;
      aces--;
    }

    this.dealer.value = value;
    this.dealer.softAce = aces > 0;
  }

  private getCardValue(card: Card): number {
    if (card.rank === 'A') return 11;
    if (['K', 'Q', 'J'].includes(card.rank)) return 10;
    return parseInt(card.rank);
  }

  private isCurrentPlayer(playerId: string, handIndex: number): boolean {
    if (this.phase !== BlackjackPhase.PLAYING) return false;
    
    const currentPlayer = this.players[this.currentPlayerIndex];
    return currentPlayer?.id === playerId && this.currentHandIndex === handIndex;
  }

  private getCurrentPlayer() {
    return this.players[this.currentPlayerIndex];
  }

  private getCurrentHand() {
    const player = this.getCurrentPlayer();
    return player?.hands[this.currentHandIndex];
  }

  private getAvailableActions(): string[] {
    const actions = ['hit', 'stand'];
    const hand = this.getCurrentHand();
    const player = this.getCurrentPlayer();

    if (!hand || !player) return actions;

    // Double down
    if (hand.cards.length === 2 && player.chips >= hand.bet && !hand.doubled) {
      actions.push('double');
    }

    // Split
    if (hand.cards.length === 2 && 
        this.getCardValue(hand.cards[0]) === this.getCardValue(hand.cards[1]) &&
        player.chips >= hand.bet &&
        player.hands.length < 4) {
      actions.push('split');
    }

    // Surrender
    if (hand.cards.length === 2 && !hand.split) {
      actions.push('surrender');
    }

    return actions;
  }

  public getGameState() {
    return {
      phase: this.phase,
      roundNumber: this.roundNumber,
      currentPlayer: this.currentPlayerIndex,
      currentHand: this.currentHandIndex,
      minBet: this.minBet,
      maxBet: this.maxBet,
      players: this.players.map(p => ({
        id: p.id,
        name: p.name,
        chips: p.chips,
        hands: p.hands,
        totalWinnings: p.totalWinnings,
        insurance: p.insurance
      })),
      dealer: {
        cards: this.dealer.revealed ? this.dealer.cards : [this.dealer.cards[0]],
        value: this.dealer.revealed ? this.dealer.value : this.getCardValue(this.dealer.cards[0]),
        revealed: this.dealer.revealed
      },
      availableActions: this.phase === BlackjackPhase.PLAYING ? this.getAvailableActions() : []
    };
  }
}
