import { GameCore } from '../../GameCore';
import { Player } from '../../Player';
import { Card, Suit, Rank } from '../../Card';
import { Deck } from '../../Deck';

export interface PreferansPlayer extends Player {
  hand: Card[];
  tricks: Card[][];
  bid?: PreferansBid;
  score: number;
  totalScore: number;
  isDealer: boolean;
  position: 'left' | 'middle' | 'right';
}

export interface PreferansBid {
  suit: Suit | 'no_trump' | 'all_trump';
  level: number; // 6-10 (6 = 6 tricks, 10 = 10 tricks)
  player: string;
  points: number;
}

export interface PreferansContract {
  declarer: string;
  bid: PreferansBid;
  defenders: string[];
  result: 'made' | 'failed' | 'pending';
  tricksWon: number;
  penalty?: number;
  bonus?: number;
}

export enum PreferansPhase {
  DEALING = 'dealing',
  BIDDING = 'bidding',
  WIDOW_EXCHANGE = 'widow_exchange',
  PLAYING = 'playing',
  SCORING = 'scoring',
  FINISHED = 'finished'
}

export class PreferansGame extends GameCore {
  private players: PreferansPlayer[] = [];
  private deck: Deck;
  private widow: Card[] = []; // 2 карты в прикупе
  private currentTrick: Card[] = [];
  private currentPlayer: number = 0;
  private dealer: number = 0;
  private phase: PreferansPhase = PreferansPhase.DEALING;
  private bids: PreferansBid[] = [];
  private contract?: PreferansContract;
  private tricks: Card[][] = [];
  private trickWinner?: string;
  private gameNumber: number = 1;

  constructor() {
    super();
    this.deck = new Deck();
    this.maxPlayers = 3;
    this.minPlayers = 3;
  }

  public addPlayer(player: Player): boolean {
    if (this.players.length >= this.maxPlayers) {
      return false;
    }

    const positions: ('left' | 'middle' | 'right')[] = ['left', 'middle', 'right'];
    const preferansPlayer: PreferansPlayer = {
      ...player,
      hand: [],
      tricks: [],
      score: 0,
      totalScore: 0,
      isDealer: this.players.length === 0, // Первый игрок - дилер
      position: positions[this.players.length]
    };

    this.players.push(preferansPlayer);
    
    if (this.players.length === this.maxPlayers) {
      this.startGame();
    }

    return true;
  }

  public startGame(): void {
    this.phase = PreferansPhase.DEALING;
    this.dealCards();
    this.phase = PreferansPhase.BIDDING;
    this.currentPlayer = (this.dealer + 1) % 3; // Торговля начинается слева от дилера
    
    this.emit('game:started', {
      players: this.players.map(p => ({
        id: p.id,
        name: p.name,
        position: p.position,
        isDealer: p.isDealer,
        handSize: p.hand.length
      })),
      phase: this.phase,
      currentPlayer: this.currentPlayer,
      gameNumber: this.gameNumber
    });
  }

  private dealCards(): void {
    this.deck.shuffle();
    
    // Раздача карт: каждому игроку по 10 карт, 2 карты в прикуп
    for (let round = 0; round < 10; round++) {
      for (let i = 0; i < 3; i++) {
        const playerIndex = (this.dealer + 1 + i) % 3;
        this.players[playerIndex].hand.push(this.deck.dealCard()!);
      }
    }

    // 2 карты в прикуп
    this.widow = [this.deck.dealCard()!, this.deck.dealCard()!];

    // Сортируем карты в руках
    this.players.forEach(player => {
      player.hand.sort(this.compareCards);
    });
  }

  public makeBid(playerId: string, bid: PreferansBid | 'pass'): boolean {
    const player = this.players.find(p => p.id === playerId);
    if (!player || this.phase !== PreferansPhase.BIDDING) {
      return false;
    }

    if (this.players[this.currentPlayer].id !== playerId) {
      return false;
    }

    if (bid !== 'pass') {
      // Проверяем, что ставка выше предыдущих
      const lastBid = this.bids[this.bids.length - 1];
      if (lastBid && !this.isBidHigher(bid, lastBid)) {
        return false;
      }

      this.bids.push(bid);
      player.bid = bid;
    }

    this.emit('player:bid', {
      playerId,
      bid,
      bids: this.bids
    });

    // Переходим к следующему игроку
    this.currentPlayer = (this.currentPlayer + 1) % 3;

    // Проверяем окончание торговли
    if (this.isBiddingFinished()) {
      this.finishBidding();
    }

    return true;
  }

  private isBidHigher(newBid: PreferansBid, lastBid: PreferansBid): boolean {
    if (newBid.level > lastBid.level) return true;
    if (newBid.level < lastBid.level) return false;
    
    // При одинаковом уровне сравниваем масти
    const suitOrder = ['clubs', 'diamonds', 'hearts', 'spades', 'no_trump', 'all_trump'];
    const newSuitIndex = suitOrder.indexOf(newBid.suit as string);
    const lastSuitIndex = suitOrder.indexOf(lastBid.suit as string);
    
    return newSuitIndex > lastSuitIndex;
  }

  private isBiddingFinished(): boolean {
    // Торговля заканчивается, когда есть хотя бы одна ставка и два паса подряд
    if (this.bids.length === 0) return false;
    
    // Простая логика: если все сделали ход и есть хотя бы одна ставка
    const activeBids = this.bids.filter(bid => bid !== 'pass');
    return activeBids.length > 0 && this.bids.length >= 3;
  }

  private finishBidding(): void {
    const winningBid = this.bids[this.bids.length - 1];
    if (winningBid === 'pass' || typeof winningBid === 'string') {
      // Все спасовали - переходим к следующей игре
      this.nextGame();
      return;
    }

    const declarer = this.players.find(p => p.id === winningBid.player)!;
    const defenders = this.players.filter(p => p.id !== winningBid.player);

    this.contract = {
      declarer: declarer.id,
      bid: winningBid,
      defenders: defenders.map(d => d.id),
      result: 'pending',
      tricksWon: 0
    };

    this.phase = PreferansPhase.WIDOW_EXCHANGE;
    this.currentPlayer = this.players.findIndex(p => p.id === declarer.id);

    this.emit('bidding:finished', {
      contract: this.contract,
      widow: this.widow,
      phase: this.phase
    });
  }

  public exchangeWidow(playerId: string, cardsToDiscard: Card[]): boolean {
    if (this.phase !== PreferansPhase.WIDOW_EXCHANGE) return false;
    if (!this.contract || this.contract.declarer !== playerId) return false;
    if (cardsToDiscard.length !== 2) return false;

    const player = this.players.find(p => p.id === playerId)!;
    
    // Добавляем прикуп в руку
    player.hand.push(...this.widow);
    
    // Убираем выбранные карты
    cardsToDiscard.forEach(card => {
      const index = player.hand.findIndex(c => 
        c.suit === card.suit && c.rank === card.rank
      );
      if (index !== -1) {
        player.hand.splice(index, 1);
      }
    });

    // Сортируем карты
    player.hand.sort(this.compareCards);

    this.phase = PreferansPhase.PLAYING;
    this.currentPlayer = (this.dealer + 1) % 3; // Ход начинается слева от дилера

    this.emit('widow:exchanged', {
      phase: this.phase,
      currentPlayer: this.currentPlayer
    });

    return true;
  }

  public playCard(playerId: string, card: Card): boolean {
    if (this.phase !== PreferansPhase.PLAYING) return false;
    
    const player = this.players.find(p => p.id === playerId);
    if (!player || this.players[this.currentPlayer].id !== playerId) {
      return false;
    }

    // Проверяем, что карта есть в руке
    const cardIndex = player.hand.findIndex(c => 
      c.suit === card.suit && c.rank === card.rank
    );
    if (cardIndex === -1) return false;

    // Проверяем правила хода
    if (!this.isValidPlay(card, player)) {
      return false;
    }

    // Убираем карту из руки
    player.hand.splice(cardIndex, 1);
    
    // Добавляем карту в текущую взятку
    this.currentTrick.push({ ...card, playerId });

    this.emit('card:played', {
      playerId,
      card,
      currentTrick: this.currentTrick,
      remainingCards: player.hand.length
    });

    // Если взятка завершена
    if (this.currentTrick.length === 3) {
      this.finishTrick();
    } else {
      this.currentPlayer = (this.currentPlayer + 1) % 3;
    }

    return true;
  }

  private isValidPlay(card: Card, player: PreferansPlayer): boolean {
    if (this.currentTrick.length === 0) {
      // Первый ход - любая карта
      return true;
    }

    const leadSuit = this.currentTrick[0].suit;
    const hasSuit = player.hand.some(c => c.suit === leadSuit);

    if (hasSuit && card.suit !== leadSuit) {
      // Должен ходить в масть, если она есть
      return false;
    }

    return true;
  }

  private finishTrick(): void {
    const winner = this.getTrickWinner();
    const winnerPlayer = this.players.find(p => p.id === winner)!;
    
    winnerPlayer.tricks.push([...this.currentTrick]);
    this.tricks.push([...this.currentTrick]);

    if (this.contract && winner === this.contract.declarer) {
      this.contract.tricksWon++;
    }

    this.emit('trick:finished', {
      winner,
      trick: this.currentTrick,
      tricksWon: winnerPlayer.tricks.length
    });

    this.currentTrick = [];
    this.currentPlayer = this.players.findIndex(p => p.id === winner);

    // Проверяем окончание игры
    if (this.players[0].hand.length === 0) {
      this.finishHand();
    }
  }

  private getTrickWinner(): string {
    if (!this.contract) return this.currentTrick[0].playerId;

    const trump = this.contract.bid.suit;
    let winningCard = this.currentTrick[0];
    let winner = winningCard.playerId;

    for (let i = 1; i < this.currentTrick.length; i++) {
      const card = this.currentTrick[i];
      
      if (this.isCardHigher(card, winningCard, trump)) {
        winningCard = card;
        winner = card.playerId;
      }
    }

    return winner;
  }

  private isCardHigher(card1: Card, card2: Card, trump: string): boolean {
    // Логика сравнения карт с учетом козыря
    if (trump === 'no_trump') {
      // Без козыря - старше карта той же масти
      if (card1.suit !== card2.suit) return false;
      return this.getCardValue(card1) > this.getCardValue(card2);
    }

    if (trump === 'all_trump') {
      // Все козыри - сравниваем по старшинству
      return this.getCardValue(card1) > this.getCardValue(card2);
    }

    // Обычный козырь
    const card1IsTrump = card1.suit === trump;
    const card2IsTrump = card2.suit === trump;

    if (card1IsTrump && !card2IsTrump) return true;
    if (!card1IsTrump && card2IsTrump) return false;
    if (card1IsTrump && card2IsTrump) {
      return this.getCardValue(card1) > this.getCardValue(card2);
    }

    // Обе не козыри - сравниваем только одной масти
    if (card1.suit !== card2.suit) return false;
    return this.getCardValue(card1) > this.getCardValue(card2);
  }

  private getCardValue(card: Card): number {
    const values: { [key in Rank]: number } = {
      '7': 7, '8': 8, '9': 9, '10': 10,
      'J': 11, 'Q': 12, 'K': 13, 'A': 14
    };
    return values[card.rank];
  }

  private finishHand(): void {
    this.phase = PreferansPhase.SCORING;
    
    if (this.contract) {
      const contractMade = this.contract.tricksWon >= (this.contract.bid.level + 4);
      this.contract.result = contractMade ? 'made' : 'failed';
      
      this.calculateScores();
    }

    this.emit('hand:finished', {
      contract: this.contract,
      scores: this.players.map(p => ({
        id: p.id,
        name: p.name,
        score: p.score,
        totalScore: p.totalScore,
        tricks: p.tricks.length
      }))
    });

    // Переходим к следующей игре
    setTimeout(() => {
      this.nextGame();
    }, 5000);
  }

  private calculateScores(): void {
    if (!this.contract) return;

    const declarer = this.players.find(p => p.id === this.contract!.declarer)!;
    const defenders = this.players.filter(p => p.id !== this.contract!.declarer);

    if (this.contract.result === 'made') {
      // Контракт выполнен
      const baseScore = this.contract.bid.level * 10;
      declarer.score += baseScore;
      declarer.totalScore += baseScore;
    } else {
      // Контракт провален
      const penalty = (this.contract.bid.level + 4 - this.contract.tricksWon) * 10;
      declarer.score -= penalty;
      declarer.totalScore -= penalty;
      
      // Защитники получают очки
      defenders.forEach(defender => {
        const bonus = Math.floor(penalty / 2);
        defender.score += bonus;
        defender.totalScore += bonus;
      });
    }
  }

  private nextGame(): void {
    // Сброс для новой игры
    this.players.forEach(player => {
      player.hand = [];
      player.tricks = [];
      player.bid = undefined;
      player.isDealer = false;
    });

    this.dealer = (this.dealer + 1) % 3;
    this.players[this.dealer].isDealer = true;
    this.bids = [];
    this.contract = undefined;
    this.tricks = [];
    this.currentTrick = [];
    this.widow = [];
    this.gameNumber++;

    this.deck = new Deck(); // Новая колода

    this.startGame();
  }

  private compareCards(a: Card, b: Card): number {
    const suitOrder = ['clubs', 'diamonds', 'hearts', 'spades'];
    const rankOrder = ['7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    
    const suitDiff = suitOrder.indexOf(a.suit) - suitOrder.indexOf(b.suit);
    if (suitDiff !== 0) return suitDiff;
    
    return rankOrder.indexOf(a.rank) - rankOrder.indexOf(b.rank);
  }

  public getGameState() {
    return {
      players: this.players.map(p => ({
        id: p.id,
        name: p.name,
        position: p.position,
        handSize: p.hand.length,
        tricksWon: p.tricks.length,
        score: p.score,
        totalScore: p.totalScore,
        isDealer: p.isDealer,
        bid: p.bid
      })),
      phase: this.phase,
      currentPlayer: this.currentPlayer,
      currentTrick: this.currentTrick,
      bids: this.bids,
      contract: this.contract,
      gameNumber: this.gameNumber,
      widow: this.phase === PreferansPhase.WIDOW_EXCHANGE ? this.widow : undefined
    };
  }
}
