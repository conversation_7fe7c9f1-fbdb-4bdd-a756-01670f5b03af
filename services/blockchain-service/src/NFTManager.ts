import { ethers } from 'ethers';
import { Web3Storage } from 'web3.storage';
import { EventEmitter } from 'events';
import { logger } from './utils/logger';

export interface NFTCard {
  tokenId: string;
  name: string;
  description: string;
  image: string;
  attributes: Array<{
    trait_type: string;
    value: string | number;
  }>;
  rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
  gameType: string;
  power: number;
  special_abilities: string[];
  creator: string;
  owner: string;
  price?: string;
  isForSale: boolean;
  createdAt: Date;
  lastSale?: {
    price: string;
    buyer: string;
    seller: string;
    timestamp: Date;
  };
}

export interface GameToken {
  symbol: string;
  name: string;
  totalSupply: string;
  decimals: number;
  contractAddress: string;
  price: number; // в USD
  stakingRewards: number; // % годовых
  utilityFeatures: string[];
}

export interface Tournament {
  id: string;
  name: string;
  entryFee: string; // в токенах
  prizePool: string;
  maxParticipants: number;
  currentParticipants: number;
  startTime: Date;
  endTime: Date;
  nftRewards: NFTCard[];
  isActive: boolean;
}

export class NFTManager extends EventEmitter {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private nftContract: ethers.Contract;
  private tokenContract: ethers.Contract;
  private marketplaceContract: ethers.Contract;
  private web3Storage: Web3Storage;

  // Contract ABIs (упрощённые)
  private nftABI = [
    "function mint(address to, string memory tokenURI) public returns (uint256)",
    "function ownerOf(uint256 tokenId) public view returns (address)",
    "function tokenURI(uint256 tokenId) public view returns (string)",
    "function approve(address to, uint256 tokenId) public",
    "function transferFrom(address from, address to, uint256 tokenId) public",
    "function balanceOf(address owner) public view returns (uint256)",
    "function tokenOfOwnerByIndex(address owner, uint256 index) public view returns (uint256)",
    "event Transfer(address indexed from, address indexed to, uint256 indexed tokenId)"
  ];

  private tokenABI = [
    "function transfer(address to, uint256 amount) public returns (bool)",
    "function balanceOf(address account) public view returns (uint256)",
    "function approve(address spender, uint256 amount) public returns (bool)",
    "function mint(address to, uint256 amount) public",
    "function burn(uint256 amount) public",
    "function stake(uint256 amount) public",
    "function unstake(uint256 amount) public",
    "function getStakingRewards(address account) public view returns (uint256)"
  ];

  private marketplaceABI = [
    "function listNFT(address nftContract, uint256 tokenId, uint256 price) public",
    "function buyNFT(address nftContract, uint256 tokenId) public payable",
    "function cancelListing(address nftContract, uint256 tokenId) public",
    "function getListingPrice(address nftContract, uint256 tokenId) public view returns (uint256)",
    "function isListed(address nftContract, uint256 tokenId) public view returns (bool)"
  ];

  constructor() {
    super();
    this.initializeContracts();
    this.setupEventListeners();
  }

  private async initializeContracts(): Promise<void> {
    try {
      // Подключение к блокчейну (Polygon для низких комиссий)
      this.provider = new ethers.JsonRpcProvider(process.env.POLYGON_RPC_URL);
      this.wallet = new ethers.Wallet(process.env.PRIVATE_KEY!, this.provider);

      // Инициализация контрактов
      this.nftContract = new ethers.Contract(
        process.env.NFT_CONTRACT_ADDRESS!,
        this.nftABI,
        this.wallet
      );

      this.tokenContract = new ethers.Contract(
        process.env.TOKEN_CONTRACT_ADDRESS!,
        this.tokenABI,
        this.wallet
      );

      this.marketplaceContract = new ethers.Contract(
        process.env.MARKETPLACE_CONTRACT_ADDRESS!,
        this.marketplaceABI,
        this.wallet
      );

      // Web3.Storage для хранения метаданных NFT
      this.web3Storage = new Web3Storage({ 
        token: process.env.WEB3_STORAGE_TOKEN! 
      });

      logger.info('Blockchain contracts initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize blockchain contracts:', error);
    }
  }

  private setupEventListeners(): void {
    // Слушаем события трансферов NFT
    this.nftContract.on('Transfer', (from, to, tokenId) => {
      this.emit('nftTransfer', { from, to, tokenId: tokenId.toString() });
    });

    // Слушаем события маркетплейса
    this.marketplaceContract.on('NFTListed', (seller, nftContract, tokenId, price) => {
      this.emit('nftListed', { seller, tokenId: tokenId.toString(), price: price.toString() });
    });

    this.marketplaceContract.on('NFTSold', (buyer, seller, nftContract, tokenId, price) => {
      this.emit('nftSold', { buyer, seller, tokenId: tokenId.toString(), price: price.toString() });
    });
  }

  // Создание NFT карты
  public async createNFTCard(cardData: Partial<NFTCard>, recipientAddress: string): Promise<NFTCard> {
    try {
      // Генерируем уникальные атрибуты карты
      const attributes = this.generateCardAttributes(cardData);
      
      // Создаём изображение карты
      const imageUrl = await this.generateCardImage(cardData, attributes);
      
      // Подготавливаем метаданные
      const metadata = {
        name: cardData.name || `Kozyr Card #${Date.now()}`,
        description: cardData.description || 'Unique card from Kozyr Master',
        image: imageUrl,
        attributes,
        external_url: `https://kozyr-master.com/nft/${cardData.tokenId}`,
        animation_url: await this.generateCardAnimation(cardData),
        properties: {
          game_type: cardData.gameType || 'universal',
          power: attributes.find(a => a.trait_type === 'Power')?.value || 50,
          rarity: cardData.rarity || 'common',
          creator: 'Kozyr Master',
          created_at: new Date().toISOString()
        }
      };

      // Загружаем метаданные в IPFS
      const metadataFile = new File([JSON.stringify(metadata)], 'metadata.json', {
        type: 'application/json'
      });
      const metadataCid = await this.web3Storage.put([metadataFile]);
      const tokenURI = `https://${metadataCid}.ipfs.w3s.link/metadata.json`;

      // Минтим NFT
      const tx = await this.nftContract.mint(recipientAddress, tokenURI);
      const receipt = await tx.wait();
      
      // Получаем tokenId из события
      const transferEvent = receipt.logs.find((log: any) => 
        log.topics[0] === ethers.id('Transfer(address,address,uint256)')
      );
      const tokenId = ethers.AbiCoder.defaultAbiCoder().decode(['uint256'], transferEvent.topics[3])[0].toString();

      const nftCard: NFTCard = {
        tokenId,
        name: metadata.name,
        description: metadata.description,
        image: imageUrl,
        attributes,
        rarity: cardData.rarity || 'common',
        gameType: cardData.gameType || 'universal',
        power: metadata.properties.power as number,
        special_abilities: this.generateSpecialAbilities(cardData.rarity || 'common'),
        creator: 'Kozyr Master',
        owner: recipientAddress,
        isForSale: false,
        createdAt: new Date()
      };

      this.emit('nftCreated', nftCard);
      logger.info(`NFT card created: ${tokenId}`);

      return nftCard;
    } catch (error) {
      logger.error('Error creating NFT card:', error);
      throw error;
    }
  }

  // Генерация атрибутов карты
  private generateCardAttributes(cardData: Partial<NFTCard>): Array<{ trait_type: string; value: string | number }> {
    const rarity = cardData.rarity || 'common';
    const gameType = cardData.gameType || 'universal';
    
    // Базовые атрибуты
    const attributes = [
      { trait_type: 'Rarity', value: rarity },
      { trait_type: 'Game Type', value: gameType },
      { trait_type: 'Power', value: this.calculatePower(rarity) },
      { trait_type: 'Generation', value: 1 },
      { trait_type: 'Element', value: this.getRandomElement() }
    ];

    // Добавляем специальные атрибуты в зависимости от редкости
    if (rarity !== 'common') {
      attributes.push({ trait_type: 'Special Ability', value: this.getRandomAbility(rarity) });
    }

    if (rarity === 'legendary' || rarity === 'mythic') {
      attributes.push({ trait_type: 'Legendary Trait', value: this.getLegendaryTrait() });
    }

    // Игровые атрибуты
    attributes.push(
      { trait_type: 'Attack', value: Math.floor(Math.random() * 50) + 25 },
      { trait_type: 'Defense', value: Math.floor(Math.random() * 50) + 25 },
      { trait_type: 'Speed', value: Math.floor(Math.random() * 50) + 25 },
      { trait_type: 'Luck', value: Math.floor(Math.random() * 100) + 1 }
    );

    return attributes;
  }

  private calculatePower(rarity: string): number {
    const basePower = {
      common: 30,
      rare: 50,
      epic: 70,
      legendary: 90,
      mythic: 100
    };
    
    const base = basePower[rarity] || 30;
    return base + Math.floor(Math.random() * 20) - 10; // ±10 вариация
  }

  private getRandomElement(): string {
    const elements = ['Fire', 'Water', 'Earth', 'Air', 'Light', 'Dark', 'Neutral'];
    return elements[Math.floor(Math.random() * elements.length)];
  }

  private getRandomAbility(rarity: string): string {
    const abilities = {
      rare: ['Double Strike', 'Shield', 'Regeneration', 'Quick Draw'],
      epic: ['Triple Strike', 'Immunity', 'Life Steal', 'Card Draw', 'Mana Boost'],
      legendary: ['Ultimate Strike', 'Invulnerability', 'Time Manipulation', 'Reality Bend'],
      mythic: ['Omnipotence', 'Universe Control', 'Infinite Power', 'God Mode']
    };
    
    const abilityList = abilities[rarity] || abilities.rare;
    return abilityList[Math.floor(Math.random() * abilityList.length)];
  }

  private getLegendaryTrait(): string {
    const traits = [
      'First Edition', 'Creator Signed', 'Tournament Winner', 
      'Streamer Special', 'Community Choice', 'Developer Favorite'
    ];
    return traits[Math.floor(Math.random() * traits.length)];
  }

  private generateSpecialAbilities(rarity: string): string[] {
    const abilityCount = {
      common: 0,
      rare: 1,
      epic: 2,
      legendary: 3,
      mythic: 5
    };

    const abilities = [];
    const count = abilityCount[rarity] || 0;
    
    for (let i = 0; i < count; i++) {
      abilities.push(this.getRandomAbility(rarity));
    }

    return abilities;
  }

  // Генерация изображения карты
  private async generateCardImage(cardData: Partial<NFTCard>, attributes: any[]): Promise<string> {
    try {
      // Здесь должна быть логика генерации изображения
      // Можно использовать AI для генерации или шаблоны
      
      // Пока возвращаем placeholder
      const imageUrl = `https://api.kozyr-master.com/generate-card-image?rarity=${cardData.rarity}&game=${cardData.gameType}&power=${attributes.find(a => a.trait_type === 'Power')?.value}`;
      
      return imageUrl;
    } catch (error) {
      logger.error('Error generating card image:', error);
      return 'https://kozyr-master.com/default-card.png';
    }
  }

  // Генерация анимации карты
  private async generateCardAnimation(cardData: Partial<NFTCard>): Promise<string> {
    if (cardData.rarity === 'legendary' || cardData.rarity === 'mythic') {
      return `https://api.kozyr-master.com/generate-card-animation?rarity=${cardData.rarity}`;
    }
    return '';
  }

  // Маркетплейс функции
  public async listNFTForSale(tokenId: string, price: string, sellerAddress: string): Promise<void> {
    try {
      // Проверяем владение NFT
      const owner = await this.nftContract.ownerOf(tokenId);
      if (owner.toLowerCase() !== sellerAddress.toLowerCase()) {
        throw new Error('Not the owner of this NFT');
      }

      // Одобряем маркетплейс для трансфера
      const approveTx = await this.nftContract.approve(this.marketplaceContract.target, tokenId);
      await approveTx.wait();

      // Выставляем на продажу
      const priceInWei = ethers.parseEther(price);
      const listTx = await this.marketplaceContract.listNFT(this.nftContract.target, tokenId, priceInWei);
      await listTx.wait();

      this.emit('nftListedForSale', { tokenId, price, seller: sellerAddress });
      logger.info(`NFT ${tokenId} listed for sale at ${price} ETH`);
    } catch (error) {
      logger.error('Error listing NFT for sale:', error);
      throw error;
    }
  }

  public async buyNFT(tokenId: string, buyerAddress: string): Promise<void> {
    try {
      // Получаем цену
      const price = await this.marketplaceContract.getListingPrice(this.nftContract.target, tokenId);
      
      // Покупаем NFT
      const buyTx = await this.marketplaceContract.buyNFT(this.nftContract.target, tokenId, {
        value: price
      });
      await buyTx.wait();

      this.emit('nftPurchased', { tokenId, buyer: buyerAddress, price: price.toString() });
      logger.info(`NFT ${tokenId} purchased by ${buyerAddress}`);
    } catch (error) {
      logger.error('Error buying NFT:', error);
      throw error;
    }
  }

  // Токен функции
  public async mintTokens(recipientAddress: string, amount: string): Promise<void> {
    try {
      const amountInWei = ethers.parseEther(amount);
      const tx = await this.tokenContract.mint(recipientAddress, amountInWei);
      await tx.wait();

      this.emit('tokensMinted', { recipient: recipientAddress, amount });
      logger.info(`${amount} tokens minted to ${recipientAddress}`);
    } catch (error) {
      logger.error('Error minting tokens:', error);
      throw error;
    }
  }

  public async getTokenBalance(address: string): Promise<string> {
    try {
      const balance = await this.tokenContract.balanceOf(address);
      return ethers.formatEther(balance);
    } catch (error) {
      logger.error('Error getting token balance:', error);
      return '0';
    }
  }

  public async stakeTokens(userAddress: string, amount: string): Promise<void> {
    try {
      const amountInWei = ethers.parseEther(amount);
      const tx = await this.tokenContract.stake(amountInWei);
      await tx.wait();

      this.emit('tokensStaked', { user: userAddress, amount });
      logger.info(`${amount} tokens staked by ${userAddress}`);
    } catch (error) {
      logger.error('Error staking tokens:', error);
      throw error;
    }
  }

  public async getStakingRewards(address: string): Promise<string> {
    try {
      const rewards = await this.tokenContract.getStakingRewards(address);
      return ethers.formatEther(rewards);
    } catch (error) {
      logger.error('Error getting staking rewards:', error);
      return '0';
    }
  }

  // NFT коллекции и редкие события
  public async createLimitedEditionCard(eventName: string, maxSupply: number): Promise<void> {
    try {
      // Создаём лимитированную серию карт для специального события
      const cardData: Partial<NFTCard> = {
        name: `${eventName} Limited Edition`,
        description: `Exclusive card from ${eventName} event. Only ${maxSupply} ever created!`,
        rarity: 'legendary',
        gameType: 'special_event'
      };

      // Минтим ограниченное количество
      for (let i = 0; i < maxSupply; i++) {
        // Здесь должна быть логика распределения между игроками
        // Например, победители турнира, топ игроки и т.д.
      }

      this.emit('limitedEditionCreated', { eventName, maxSupply });
    } catch (error) {
      logger.error('Error creating limited edition cards:', error);
      throw error;
    }
  }

  // Игровая интеграция
  public async useNFTInGame(tokenId: string, gameId: string, userAddress: string): Promise<any> {
    try {
      // Проверяем владение NFT
      const owner = await this.nftContract.ownerOf(tokenId);
      if (owner.toLowerCase() !== userAddress.toLowerCase()) {
        throw new Error('Not the owner of this NFT');
      }

      // Получаем метаданные NFT
      const tokenURI = await this.nftContract.tokenURI(tokenId);
      const metadata = await this.fetchMetadata(tokenURI);

      // Применяем бонусы от NFT в игре
      const gameBonuses = this.calculateGameBonuses(metadata);

      this.emit('nftUsedInGame', { tokenId, gameId, userAddress, bonuses: gameBonuses });

      return gameBonuses;
    } catch (error) {
      logger.error('Error using NFT in game:', error);
      throw error;
    }
  }

  private async fetchMetadata(tokenURI: string): Promise<any> {
    try {
      const response = await fetch(tokenURI);
      return await response.json();
    } catch (error) {
      logger.error('Error fetching NFT metadata:', error);
      return {};
    }
  }

  private calculateGameBonuses(metadata: any): any {
    const attributes = metadata.attributes || [];
    const bonuses = {
      powerBoost: 0,
      luckBoost: 0,
      experienceMultiplier: 1,
      specialAbilities: []
    };

    // Рассчитываем бонусы на основе атрибутов NFT
    attributes.forEach((attr: any) => {
      switch (attr.trait_type) {
        case 'Power':
          bonuses.powerBoost = Math.floor(attr.value / 10);
          break;
        case 'Luck':
          bonuses.luckBoost = Math.floor(attr.value / 20);
          break;
        case 'Rarity':
          const multipliers = { common: 1, rare: 1.1, epic: 1.25, legendary: 1.5, mythic: 2 };
          bonuses.experienceMultiplier = multipliers[attr.value] || 1;
          break;
        case 'Special Ability':
          bonuses.specialAbilities.push(attr.value);
          break;
      }
    });

    return bonuses;
  }

  // Аналитика и статистика
  public async getNFTCollectionStats(): Promise<any> {
    try {
      // Здесь должна быть логика получения статистики из блокчейна
      return {
        totalSupply: 1000,
        totalOwners: 500,
        floorPrice: '0.1',
        volume24h: '50.5',
        averagePrice: '0.25',
        rarityDistribution: {
          common: 60,
          rare: 25,
          epic: 10,
          legendary: 4,
          mythic: 1
        }
      };
    } catch (error) {
      logger.error('Error getting collection stats:', error);
      return {};
    }
  }

  public async getUserNFTs(userAddress: string): Promise<NFTCard[]> {
    try {
      const balance = await this.nftContract.balanceOf(userAddress);
      const nfts: NFTCard[] = [];

      for (let i = 0; i < balance; i++) {
        const tokenId = await this.nftContract.tokenOfOwnerByIndex(userAddress, i);
        const tokenURI = await this.nftContract.tokenURI(tokenId);
        const metadata = await this.fetchMetadata(tokenURI);

        nfts.push({
          tokenId: tokenId.toString(),
          name: metadata.name,
          description: metadata.description,
          image: metadata.image,
          attributes: metadata.attributes,
          rarity: metadata.properties?.rarity || 'common',
          gameType: metadata.properties?.game_type || 'universal',
          power: metadata.properties?.power || 50,
          special_abilities: metadata.properties?.special_abilities || [],
          creator: metadata.properties?.creator || 'Unknown',
          owner: userAddress,
          isForSale: await this.marketplaceContract.isListed(this.nftContract.target, tokenId),
          createdAt: new Date(metadata.properties?.created_at || Date.now())
        });
      }

      return nfts;
    } catch (error) {
      logger.error('Error getting user NFTs:', error);
      return [];
    }
  }
}
