import { ethers } from 'ethers';
import { Web3Storage } from 'web3.storage';

export interface NFTCard {
  tokenId: string;
  contractAddress: string;
  owner: string;
  metadata: NFTMetadata;
  rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
  gameStats: CardGameStats;
  tradingHistory: TradeRecord[];
  isLocked: boolean; // Заблокирована в игре
  createdAt: Date;
  lastTransfer: Date;
}

export interface NFTMetadata {
  name: string;
  description: string;
  image: string;
  animationUrl?: string;
  attributes: NFTAttribute[];
  externalUrl: string;
  backgroundColor?: string;
}

export interface NFTAttribute {
  traitType: string;
  value: string | number;
  displayType?: 'boost_number' | 'boost_percentage' | 'number' | 'date';
  maxValue?: number;
}

export interface CardGameStats {
  gamesPlayed: number;
  gamesWon: number;
  winRate: number;
  powerLevel: number;
  specialAbilities: string[];
  evolutionLevel: number;
  experience: number;
}

export interface TradeRecord {
  from: string;
  to: string;
  price: string; // В wei
  currency: 'ETH' | 'MATIC' | 'KZM'; // KZM - наш токен
  timestamp: Date;
  transactionHash: string;
  marketplaceId?: string;
}

export interface GameToken {
  symbol: string;
  name: string;
  contractAddress: string;
  decimals: number;
  totalSupply: string;
  circulatingSupply: string;
  holders: number;
  price: TokenPrice;
}

export interface TokenPrice {
  usd: number;
  eth: number;
  change24h: number;
  marketCap: number;
  volume24h: number;
  lastUpdated: Date;
}

export interface StakingPool {
  id: string;
  name: string;
  tokenAddress: string;
  rewardToken: string;
  apy: number;
  totalStaked: string;
  minStakeAmount: string;
  lockPeriod: number; // в днях
  participants: number;
  isActive: boolean;
}

export interface PlayerWallet {
  address: string;
  playerId: string;
  nftCards: NFTCard[];
  tokenBalances: Map<string, string>;
  stakingPositions: StakingPosition[];
  tradingHistory: TradeRecord[];
  totalValue: number; // в USD
}

export interface StakingPosition {
  poolId: string;
  amount: string;
  startDate: Date;
  endDate: Date;
  rewardsEarned: string;
  isActive: boolean;
}

export interface Marketplace {
  id: string;
  name: string;
  contractAddress: string;
  feePercentage: number;
  supportedTokens: string[];
  listings: MarketplaceListing[];
  volume24h: number;
  totalTrades: number;
}

export interface MarketplaceListing {
  id: string;
  tokenId: string;
  seller: string;
  price: string;
  currency: string;
  isAuction: boolean;
  auctionEndTime?: Date;
  highestBid?: string;
  highestBidder?: string;
  status: 'active' | 'sold' | 'cancelled' | 'expired';
  createdAt: Date;
}

export interface TournamentReward {
  tournamentId: string;
  rewardType: 'nft' | 'token' | 'both';
  nftRewards: NFTReward[];
  tokenRewards: TokenReward[];
  distribution: RewardDistribution[];
}

export interface NFTReward {
  rarity: NFTCard['rarity'];
  quantity: number;
  metadata: Partial<NFTMetadata>;
  specialAttributes: string[];
}

export interface TokenReward {
  tokenAddress: string;
  amount: string;
  vestingPeriod?: number;
}

export interface RewardDistribution {
  place: number;
  percentage: number;
  nftCount: number;
  tokenAmount: string;
}

export class BlockchainManager {
  private provider: ethers.Provider;
  private signer: ethers.Signer;
  private web3Storage: Web3Storage;
  private contracts: Map<string, ethers.Contract> = new Map();
  private nftCards: Map<string, NFTCard> = new Map();
  private marketplaces: Map<string, Marketplace> = new Map();
  private stakingPools: Map<string, StakingPool> = new Map();

  constructor() {
    this.initializeProvider();
    this.initializeContracts();
    this.initializeIPFS();
  }

  /**
   * Создает новую NFT карту
   */
  async mintNFTCard(
    recipient: string,
    metadata: NFTMetadata,
    rarity: NFTCard['rarity'],
    gameStats: CardGameStats
  ): Promise<NFTCard> {
    const nftContract = this.contracts.get('nft_cards');
    if (!nftContract) throw new Error('NFT contract not found');

    // Загружаем метаданные в IPFS
    const metadataUri = await this.uploadToIPFS(metadata);

    // Минтим NFT
    const tx = await nftContract.mint(recipient, metadataUri);
    const receipt = await tx.wait();
    
    const tokenId = receipt.logs[0].args.tokenId.toString();

    const nftCard: NFTCard = {
      tokenId,
      contractAddress: await nftContract.getAddress(),
      owner: recipient,
      metadata,
      rarity,
      gameStats,
      tradingHistory: [],
      isLocked: false,
      createdAt: new Date(),
      lastTransfer: new Date()
    };

    this.nftCards.set(tokenId, nftCard);
    return nftCard;
  }

  /**
   * Создает специальную турнирную NFT карту
   */
  async mintTournamentReward(
    tournamentId: string,
    winner: string,
    place: number,
    rewardConfig: NFTReward
  ): Promise<NFTCard> {
    const specialMetadata: NFTMetadata = {
      name: `Tournament Champion #${place}`,
      description: `Exclusive NFT card for ${place} place in tournament ${tournamentId}`,
      image: `https://api.kozyr-master.com/nft/tournament/${tournamentId}/${place}.png`,
      attributes: [
        { traitType: 'Tournament', value: tournamentId },
        { traitType: 'Place', value: place },
        { traitType: 'Rarity', value: rewardConfig.rarity },
        { traitType: 'Year', value: new Date().getFullYear() },
        ...rewardConfig.specialAttributes.map(attr => ({
          traitType: 'Special', value: attr
        }))
      ],
      externalUrl: `https://kozyr-master.com/tournaments/${tournamentId}`
    };

    const gameStats: CardGameStats = {
      gamesPlayed: 0,
      gamesWon: 0,
      winRate: 0,
      powerLevel: this.calculatePowerLevel(rewardConfig.rarity, place),
      specialAbilities: rewardConfig.specialAttributes,
      evolutionLevel: 1,
      experience: 0
    };

    return await this.mintNFTCard(winner, specialMetadata, rewardConfig.rarity, gameStats);
  }

  /**
   * Выставляет NFT на продажу
   */
  async listNFTForSale(
    tokenId: string,
    price: string,
    currency: string,
    isAuction: boolean = false,
    auctionDuration?: number
  ): Promise<MarketplaceListing> {
    const marketplace = this.marketplaces.get('main');
    if (!marketplace) throw new Error('Marketplace not found');

    const nftCard = this.nftCards.get(tokenId);
    if (!nftCard) throw new Error('NFT not found');

    if (nftCard.isLocked) {
      throw new Error('NFT is locked in game');
    }

    const marketplaceContract = this.contracts.get('marketplace');
    if (!marketplaceContract) throw new Error('Marketplace contract not found');

    const listingId = this.generateListingId();
    
    const tx = await marketplaceContract.listItem(
      nftCard.contractAddress,
      tokenId,
      ethers.parseEther(price),
      currency,
      isAuction,
      auctionDuration || 0
    );

    await tx.wait();

    const listing: MarketplaceListing = {
      id: listingId,
      tokenId,
      seller: nftCard.owner,
      price,
      currency,
      isAuction,
      auctionEndTime: isAuction ? new Date(Date.now() + (auctionDuration! * 1000)) : undefined,
      status: 'active',
      createdAt: new Date()
    };

    marketplace.listings.push(listing);
    return listing;
  }

  /**
   * Покупает NFT с маркетплейса
   */
  async buyNFT(listingId: string, buyer: string): Promise<TradeRecord> {
    const marketplace = this.marketplaces.get('main');
    if (!marketplace) throw new Error('Marketplace not found');

    const listing = marketplace.listings.find(l => l.id === listingId);
    if (!listing || listing.status !== 'active') {
      throw new Error('Listing not available');
    }

    const marketplaceContract = this.contracts.get('marketplace');
    if (!marketplaceContract) throw new Error('Marketplace contract not found');

    const tx = await marketplaceContract.buyItem(listingId, {
      value: ethers.parseEther(listing.price)
    });

    const receipt = await tx.wait();

    // Обновляем владельца NFT
    const nftCard = this.nftCards.get(listing.tokenId);
    if (nftCard) {
      nftCard.owner = buyer;
      nftCard.lastTransfer = new Date();
    }

    // Создаем запись о торговле
    const tradeRecord: TradeRecord = {
      from: listing.seller,
      to: buyer,
      price: listing.price,
      currency: listing.currency as any,
      timestamp: new Date(),
      transactionHash: receipt.hash,
      marketplaceId: marketplace.id
    };

    if (nftCard) {
      nftCard.tradingHistory.push(tradeRecord);
    }

    // Обновляем статус листинга
    listing.status = 'sold';

    return tradeRecord;
  }

  /**
   * Создает стейкинг пул
   */
  async createStakingPool(
    name: string,
    tokenAddress: string,
    rewardToken: string,
    apy: number,
    minStakeAmount: string,
    lockPeriod: number
  ): Promise<StakingPool> {
    const poolId = this.generatePoolId();
    
    const stakingContract = this.contracts.get('staking');
    if (!stakingContract) throw new Error('Staking contract not found');

    const tx = await stakingContract.createPool(
      tokenAddress,
      rewardToken,
      Math.floor(apy * 100), // APY в базисных пунктах
      ethers.parseEther(minStakeAmount),
      lockPeriod * 24 * 60 * 60 // в секундах
    );

    await tx.wait();

    const pool: StakingPool = {
      id: poolId,
      name,
      tokenAddress,
      rewardToken,
      apy,
      totalStaked: '0',
      minStakeAmount,
      lockPeriod,
      participants: 0,
      isActive: true
    };

    this.stakingPools.set(poolId, pool);
    return pool;
  }

  /**
   * Стейкает токены в пул
   */
  async stakeTokens(
    poolId: string,
    amount: string,
    staker: string
  ): Promise<StakingPosition> {
    const pool = this.stakingPools.get(poolId);
    if (!pool || !pool.isActive) {
      throw new Error('Pool not available');
    }

    const stakingContract = this.contracts.get('staking');
    if (!stakingContract) throw new Error('Staking contract not found');

    const tx = await stakingContract.stake(poolId, ethers.parseEther(amount));
    await tx.wait();

    const position: StakingPosition = {
      poolId,
      amount,
      startDate: new Date(),
      endDate: new Date(Date.now() + pool.lockPeriod * 24 * 60 * 60 * 1000),
      rewardsEarned: '0',
      isActive: true
    };

    // Обновляем статистику пула
    pool.totalStaked = (parseFloat(pool.totalStaked) + parseFloat(amount)).toString();
    pool.participants++;

    return position;
  }

  /**
   * Эволюционирует NFT карту
   */
  async evolveNFTCard(tokenId: string, experienceGained: number): Promise<NFTCard> {
    const nftCard = this.nftCards.get(tokenId);
    if (!nftCard) throw new Error('NFT not found');

    nftCard.gameStats.experience += experienceGained;
    
    // Проверяем возможность эволюции
    const requiredExp = nftCard.gameStats.evolutionLevel * 1000;
    if (nftCard.gameStats.experience >= requiredExp) {
      nftCard.gameStats.evolutionLevel++;
      nftCard.gameStats.powerLevel += 10;
      
      // Обновляем метаданные
      nftCard.metadata.attributes.push({
        traitType: 'Evolution Level',
        value: nftCard.gameStats.evolutionLevel
      });

      // Обновляем метаданные в блокчейне
      const newMetadataUri = await this.uploadToIPFS(nftCard.metadata);
      const nftContract = this.contracts.get('nft_cards');
      if (nftContract) {
        const tx = await nftContract.setTokenURI(tokenId, newMetadataUri);
        await tx.wait();
      }
    }

    return nftCard;
  }

  /**
   * Получает портфель игрока
   */
  async getPlayerWallet(playerId: string, address: string): Promise<PlayerWallet> {
    const nftCards = Array.from(this.nftCards.values())
      .filter(card => card.owner.toLowerCase() === address.toLowerCase());

    const tokenBalances = new Map<string, string>();
    
    // Получаем балансы токенов
    for (const [symbol, contract] of this.contracts.entries()) {
      if (symbol.includes('token')) {
        try {
          const balance = await contract.balanceOf(address);
          tokenBalances.set(symbol, ethers.formatEther(balance));
        } catch (error) {
          console.error(`Error getting balance for ${symbol}:`, error);
        }
      }
    }

    // Получаем стейкинг позиции
    const stakingPositions: StakingPosition[] = [];
    // Здесь должна быть логика получения стейкинг позиций

    // Вычисляем общую стоимость
    const totalValue = await this.calculatePortfolioValue(nftCards, tokenBalances);

    return {
      address,
      playerId,
      nftCards,
      tokenBalances,
      stakingPositions,
      tradingHistory: this.getPlayerTradingHistory(address),
      totalValue
    };
  }

  /**
   * Получает статистику маркетплейса
   */
  getMarketplaceStats(marketplaceId: string): any {
    const marketplace = this.marketplaces.get(marketplaceId);
    if (!marketplace) return null;

    const activeListings = marketplace.listings.filter(l => l.status === 'active').length;
    const soldListings = marketplace.listings.filter(l => l.status === 'sold').length;
    
    const averagePrice = marketplace.listings
      .filter(l => l.status === 'sold')
      .reduce((sum, l) => sum + parseFloat(l.price), 0) / soldListings || 0;

    return {
      totalListings: marketplace.listings.length,
      activeListings,
      soldListings,
      volume24h: marketplace.volume24h,
      totalTrades: marketplace.totalTrades,
      averagePrice,
      floorPrice: this.getFloorPrice(marketplace),
      topSale: this.getTopSale(marketplace)
    };
  }

  // Приватные методы
  private initializeProvider(): void {
    // Подключение к Ethereum/Polygon сети
    this.provider = new ethers.JsonRpcProvider(process.env.RPC_URL);
    this.signer = new ethers.Wallet(process.env.PRIVATE_KEY!, this.provider);
  }

  private initializeContracts(): void {
    // Инициализация смарт-контрактов
    const contracts = {
      nft_cards: process.env.NFT_CONTRACT_ADDRESS!,
      marketplace: process.env.MARKETPLACE_CONTRACT_ADDRESS!,
      staking: process.env.STAKING_CONTRACT_ADDRESS!,
      game_token: process.env.GAME_TOKEN_ADDRESS!
    };

    for (const [name, address] of Object.entries(contracts)) {
      // Здесь должны быть ABI контрактов
      const abi = this.getContractABI(name);
      this.contracts.set(name, new ethers.Contract(address, abi, this.signer));
    }
  }

  private initializeIPFS(): void {
    this.web3Storage = new Web3Storage({ 
      token: process.env.WEB3_STORAGE_TOKEN! 
    });
  }

  private async uploadToIPFS(metadata: any): Promise<string> {
    const blob = new Blob([JSON.stringify(metadata)], { type: 'application/json' });
    const files = [new File([blob], 'metadata.json')];
    const cid = await this.web3Storage.put(files);
    return `https://${cid}.ipfs.w3s.link/metadata.json`;
  }

  private calculatePowerLevel(rarity: NFTCard['rarity'], place: number): number {
    const rarityMultiplier = {
      common: 1,
      rare: 2,
      epic: 3,
      legendary: 5,
      mythic: 8
    };

    const placeBonus = Math.max(10 - place, 1);
    return rarityMultiplier[rarity] * 10 + placeBonus;
  }

  private async calculatePortfolioValue(
    nftCards: NFTCard[], 
    tokenBalances: Map<string, string>
  ): Promise<number> {
    let totalValue = 0;

    // Стоимость NFT (на основе последних продаж)
    for (const card of nftCards) {
      const lastTrade = card.tradingHistory[card.tradingHistory.length - 1];
      if (lastTrade) {
        totalValue += parseFloat(lastTrade.price) * 2000; // ETH to USD примерно
      } else {
        // Базовая стоимость по редкости
        const baseValues = { common: 10, rare: 50, epic: 200, legendary: 1000, mythic: 5000 };
        totalValue += baseValues[card.rarity];
      }
    }

    // Стоимость токенов
    for (const [token, balance] of tokenBalances.entries()) {
      const price = await this.getTokenPrice(token);
      totalValue += parseFloat(balance) * price;
    }

    return totalValue;
  }

  private getPlayerTradingHistory(address: string): TradeRecord[] {
    const history: TradeRecord[] = [];
    
    for (const card of this.nftCards.values()) {
      history.push(...card.tradingHistory.filter(
        trade => trade.from === address || trade.to === address
      ));
    }

    return history.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  private getFloorPrice(marketplace: Marketplace): number {
    const activePrices = marketplace.listings
      .filter(l => l.status === 'active' && !l.isAuction)
      .map(l => parseFloat(l.price));
    
    return activePrices.length > 0 ? Math.min(...activePrices) : 0;
  }

  private getTopSale(marketplace: Marketplace): number {
    const soldPrices = marketplace.listings
      .filter(l => l.status === 'sold')
      .map(l => parseFloat(l.price));
    
    return soldPrices.length > 0 ? Math.max(...soldPrices) : 0;
  }

  private async getTokenPrice(token: string): Promise<number> {
    // Интеграция с CoinGecko или другим API для получения цен
    return 1; // Заглушка
  }

  private getContractABI(contractName: string): any[] {
    // Здесь должны быть ABI смарт-контрактов
    return [];
  }

  private generateListingId(): string {
    return 'listing_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generatePoolId(): string {
    return 'pool_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}
