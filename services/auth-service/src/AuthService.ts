import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { Pool } from 'pg';
import crypto from 'crypto';
import nodemailer from 'nodemailer';

export interface User {
  id: string;
  username: string;
  email: string;
  displayName?: string;
  avatarUrl?: string;
  isActive: boolean;
  isVerified: boolean;
  createdAt: Date;
  lastLogin?: Date;
  preferences: Record<string, any>;
  metadata: Record<string, any>;
}

export interface PlayerProfile {
  id: string;
  userId: string;
  rating: number;
  seasonRating?: number;
  highestRating: number;
  gamesPlayed: number;
  gamesWon: number;
  winRate: number;
  currentWinStreak: number;
  longestWinStreak: number;
  rank: string;
  rankProgress: number;
  totalPlaytime: number;
  favoriteGame?: string;
  skillLevel: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface LoginCredentials {
  username: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  displayName?: string;
  acceptTerms: boolean;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordReset {
  token: string;
  newPassword: string;
}

export interface EmailVerification {
  token: string;
}

export interface UserUpdate {
  displayName?: string;
  avatarUrl?: string;
  preferences?: Record<string, any>;
}

export interface AuthResult {
  success: boolean;
  user?: User;
  profile?: PlayerProfile;
  tokens?: AuthTokens;
  error?: string;
  requiresVerification?: boolean;
}

export interface SessionInfo {
  userId: string;
  username: string;
  email: string;
  isActive: boolean;
  lastActivity: Date;
  ipAddress: string;
  userAgent: string;
}

export class AuthService {
  private db: Pool;
  private jwtSecret: string;
  private jwtRefreshSecret: string;
  private emailTransporter: nodemailer.Transporter;
  private activeSessions: Map<string, SessionInfo> = new Map();

  constructor() {
    this.db = new Pool({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      database: process.env.DB_NAME || 'kozyr_master',
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'password',
    });

    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    this.jwtRefreshSecret = process.env.JWT_REFRESH_SECRET || 'your-refresh-secret';

    this.setupEmailTransporter();
  }

  /**
   * Регистрация нового пользователя
   */
  async register(data: RegisterData): Promise<AuthResult> {
    try {
      // Валидация данных
      const validation = this.validateRegistrationData(data);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      // Проверка уникальности username и email
      const existingUser = await this.checkUserExists(data.username, data.email);
      if (existingUser.exists) {
        return { success: false, error: existingUser.error };
      }

      // Хеширование пароля
      const passwordHash = await bcrypt.hash(data.password, 12);

      // Генерация токена верификации
      const verificationToken = crypto.randomBytes(32).toString('hex');

      const client = await this.db.connect();
      
      try {
        await client.query('BEGIN');

        // Создание пользователя
        const userResult = await client.query(`
          INSERT INTO users (username, email, password_hash, display_name, verification_token)
          VALUES ($1, $2, $3, $4, $5)
          RETURNING id, username, email, display_name, created_at, is_verified
        `, [data.username, data.email, passwordHash, data.displayName || data.username, verificationToken]);

        const user = userResult.rows[0];

        // Создание профиля игрока
        const profileResult = await client.query(`
          INSERT INTO player_profiles (user_id)
          VALUES ($1)
          RETURNING *
        `, [user.id]);

        const profile = this.mapPlayerProfile(profileResult.rows[0]);

        // Создание настроек пользователя
        await client.query(`
          INSERT INTO user_settings (user_id)
          VALUES ($1)
        `, [user.id]);

        await client.query('COMMIT');

        // Отправка email верификации
        await this.sendVerificationEmail(user.email, user.username, verificationToken);

        return {
          success: true,
          user: this.mapUser(user),
          profile,
          requiresVerification: true
        };

      } catch (error) {
        await client.query('ROLLBACK');
        throw error;
      } finally {
        client.release();
      }

    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: 'Ошибка при регистрации' };
    }
  }

  /**
   * Вход в систему
   */
  async login(credentials: LoginCredentials, ipAddress: string, userAgent: string): Promise<AuthResult> {
    try {
      // Поиск пользователя
      const userResult = await this.db.query(`
        SELECT u.*, pp.* FROM users u
        LEFT JOIN player_profiles pp ON u.id = pp.user_id
        WHERE u.username = $1 OR u.email = $1
      `, [credentials.username]);

      if (userResult.rows.length === 0) {
        return { success: false, error: 'Неверные учетные данные' };
      }

      const userData = userResult.rows[0];

      // Проверка активности аккаунта
      if (!userData.is_active) {
        return { success: false, error: 'Аккаунт заблокирован' };
      }

      // Проверка пароля
      const passwordValid = await bcrypt.compare(credentials.password, userData.password_hash);
      if (!passwordValid) {
        return { success: false, error: 'Неверные учетные данные' };
      }

      // Обновление времени последнего входа
      await this.db.query(`
        UPDATE users SET last_login = NOW() WHERE id = $1
      `, [userData.id]);

      // Генерация токенов
      const tokens = this.generateTokens(userData.id, credentials.rememberMe);

      // Создание сессии
      const sessionInfo: SessionInfo = {
        userId: userData.id,
        username: userData.username,
        email: userData.email,
        isActive: true,
        lastActivity: new Date(),
        ipAddress,
        userAgent
      };

      this.activeSessions.set(userData.id, sessionInfo);

      return {
        success: true,
        user: this.mapUser(userData),
        profile: this.mapPlayerProfile(userData),
        tokens
      };

    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Ошибка при входе в систему' };
    }
  }

  /**
   * Выход из системы
   */
  async logout(userId: string, refreshToken: string): Promise<{ success: boolean }> {
    try {
      // Удаление сессии
      this.activeSessions.delete(userId);

      // В реальной системе здесь бы был blacklist для refresh токенов
      // Для простоты просто удаляем из активных сессий

      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false };
    }
  }

  /**
   * Обновление access токена
   */
  async refreshToken(refreshToken: string): Promise<AuthResult> {
    try {
      const decoded = jwt.verify(refreshToken, this.jwtRefreshSecret) as any;
      
      // Проверка активности пользователя
      const userResult = await this.db.query(`
        SELECT id, username, email, is_active FROM users WHERE id = $1
      `, [decoded.userId]);

      if (userResult.rows.length === 0 || !userResult.rows[0].is_active) {
        return { success: false, error: 'Недействительный токен' };
      }

      // Генерация новых токенов
      const tokens = this.generateTokens(decoded.userId, true);

      return {
        success: true,
        tokens
      };

    } catch (error) {
      return { success: false, error: 'Недействительный refresh токен' };
    }
  }

  /**
   * Верификация email
   */
  async verifyEmail(verification: EmailVerification): Promise<AuthResult> {
    try {
      const result = await this.db.query(`
        UPDATE users 
        SET is_verified = true, verification_token = NULL
        WHERE verification_token = $1 AND is_verified = false
        RETURNING id, username, email
      `, [verification.token]);

      if (result.rows.length === 0) {
        return { success: false, error: 'Недействительный токен верификации' };
      }

      return { success: true };

    } catch (error) {
      console.error('Email verification error:', error);
      return { success: false, error: 'Ошибка при верификации email' };
    }
  }

  /**
   * Запрос сброса пароля
   */
  async requestPasswordReset(request: PasswordResetRequest): Promise<{ success: boolean; error?: string }> {
    try {
      const resetToken = crypto.randomBytes(32).toString('hex');
      const expiresAt = new Date(Date.now() + 3600000); // 1 час

      const result = await this.db.query(`
        UPDATE users 
        SET reset_token = $1, reset_token_expires = $2
        WHERE email = $3 AND is_active = true
        RETURNING username, email
      `, [resetToken, expiresAt, request.email]);

      if (result.rows.length > 0) {
        await this.sendPasswordResetEmail(
          result.rows[0].email,
          result.rows[0].username,
          resetToken
        );
      }

      // Всегда возвращаем success для безопасности
      return { success: true };

    } catch (error) {
      console.error('Password reset request error:', error);
      return { success: false, error: 'Ошибка при запросе сброса пароля' };
    }
  }

  /**
   * Сброс пароля
   */
  async resetPassword(reset: PasswordReset): Promise<{ success: boolean; error?: string }> {
    try {
      const passwordHash = await bcrypt.hash(reset.newPassword, 12);

      const result = await this.db.query(`
        UPDATE users 
        SET password_hash = $1, reset_token = NULL, reset_token_expires = NULL
        WHERE reset_token = $2 AND reset_token_expires > NOW()
        RETURNING id
      `, [passwordHash, reset.token]);

      if (result.rows.length === 0) {
        return { success: false, error: 'Недействительный или истекший токен' };
      }

      return { success: true };

    } catch (error) {
      console.error('Password reset error:', error);
      return { success: false, error: 'Ошибка при сбросе пароля' };
    }
  }

  /**
   * Получение информации о пользователе
   */
  async getUserInfo(userId: string): Promise<{ user?: User; profile?: PlayerProfile; error?: string }> {
    try {
      const result = await this.db.query(`
        SELECT u.*, pp.* FROM users u
        LEFT JOIN player_profiles pp ON u.id = pp.user_id
        WHERE u.id = $1 AND u.is_active = true
      `, [userId]);

      if (result.rows.length === 0) {
        return { error: 'Пользователь не найден' };
      }

      const userData = result.rows[0];

      return {
        user: this.mapUser(userData),
        profile: this.mapPlayerProfile(userData)
      };

    } catch (error) {
      console.error('Get user info error:', error);
      return { error: 'Ошибка при получении информации о пользователе' };
    }
  }

  /**
   * Обновление профиля пользователя
   */
  async updateUser(userId: string, update: UserUpdate): Promise<AuthResult> {
    try {
      const result = await this.db.query(`
        UPDATE users 
        SET display_name = COALESCE($2, display_name),
            avatar_url = COALESCE($3, avatar_url),
            preferences = COALESCE($4, preferences),
            updated_at = NOW()
        WHERE id = $1 AND is_active = true
        RETURNING *
      `, [userId, update.displayName, update.avatarUrl, JSON.stringify(update.preferences)]);

      if (result.rows.length === 0) {
        return { success: false, error: 'Пользователь не найден' };
      }

      return {
        success: true,
        user: this.mapUser(result.rows[0])
      };

    } catch (error) {
      console.error('Update user error:', error);
      return { success: false, error: 'Ошибка при обновлении профиля' };
    }
  }

  /**
   * Проверка валидности токена
   */
  async validateToken(token: string): Promise<{ valid: boolean; userId?: string; error?: string }> {
    try {
      const decoded = jwt.verify(token, this.jwtSecret) as any;
      
      // Проверка активности сессии
      const session = this.activeSessions.get(decoded.userId);
      if (!session || !session.isActive) {
        return { valid: false, error: 'Сессия не активна' };
      }

      // Обновление времени последней активности
      session.lastActivity = new Date();

      return { valid: true, userId: decoded.userId };

    } catch (error) {
      return { valid: false, error: 'Недействительный токен' };
    }
  }

  // Приватные методы
  private validateRegistrationData(data: RegisterData): { valid: boolean; error?: string } {
    if (!data.username || data.username.length < 3 || data.username.length > 50) {
      return { valid: false, error: 'Имя пользователя должно быть от 3 до 50 символов' };
    }

    if (!/^[a-zA-Z0-9_]+$/.test(data.username)) {
      return { valid: false, error: 'Имя пользователя может содержать только буквы, цифры и подчеркивания' };
    }

    if (!data.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      return { valid: false, error: 'Некорректный email адрес' };
    }

    if (!data.password || data.password.length < 8) {
      return { valid: false, error: 'Пароль должен быть не менее 8 символов' };
    }

    if (!data.acceptTerms) {
      return { valid: false, error: 'Необходимо принять условия использования' };
    }

    return { valid: true };
  }

  private async checkUserExists(username: string, email: string): Promise<{ exists: boolean; error?: string }> {
    const result = await this.db.query(`
      SELECT username, email FROM users WHERE username = $1 OR email = $2
    `, [username, email]);

    if (result.rows.length > 0) {
      const existing = result.rows[0];
      if (existing.username === username) {
        return { exists: true, error: 'Пользователь с таким именем уже существует' };
      }
      if (existing.email === email) {
        return { exists: true, error: 'Пользователь с таким email уже существует' };
      }
    }

    return { exists: false };
  }

  private generateTokens(userId: string, rememberMe: boolean = false): AuthTokens {
    const accessTokenExpiry = rememberMe ? '7d' : '1h';
    const refreshTokenExpiry = rememberMe ? '30d' : '7d';

    const accessToken = jwt.sign(
      { userId, type: 'access' },
      this.jwtSecret,
      { expiresIn: accessTokenExpiry }
    );

    const refreshToken = jwt.sign(
      { userId, type: 'refresh' },
      this.jwtRefreshSecret,
      { expiresIn: refreshTokenExpiry }
    );

    return {
      accessToken,
      refreshToken,
      expiresIn: rememberMe ? 7 * 24 * 60 * 60 : 60 * 60 // в секундах
    };
  }

  private mapUser(userData: any): User {
    return {
      id: userData.id,
      username: userData.username,
      email: userData.email,
      displayName: userData.display_name,
      avatarUrl: userData.avatar_url,
      isActive: userData.is_active,
      isVerified: userData.is_verified,
      createdAt: userData.created_at,
      lastLogin: userData.last_login,
      preferences: userData.preferences || {},
      metadata: userData.metadata || {}
    };
  }

  private mapPlayerProfile(profileData: any): PlayerProfile {
    return {
      id: profileData.id,
      userId: profileData.user_id,
      rating: profileData.rating || 1000,
      seasonRating: profileData.season_rating,
      highestRating: profileData.highest_rating || 1000,
      gamesPlayed: profileData.games_played || 0,
      gamesWon: profileData.games_won || 0,
      winRate: parseFloat(profileData.win_rate) || 0,
      currentWinStreak: profileData.current_win_streak || 0,
      longestWinStreak: profileData.longest_win_streak || 0,
      rank: profileData.rank || 'Новичок I',
      rankProgress: profileData.rank_progress || 0,
      totalPlaytime: profileData.total_playtime || 0,
      favoriteGame: profileData.favorite_game,
      skillLevel: profileData.skill_level || 'beginner'
    };
  }

  private setupEmailTransporter(): void {
    this.emailTransporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
  }

  private async sendVerificationEmail(email: string, username: string, token: string): Promise<void> {
    const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;
    
    await this.emailTransporter.sendMail({
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: email,
      subject: 'Подтверждение регистрации - Козырь Мастер',
      html: `
        <h2>Добро пожаловать в Козырь Мастер, ${username}!</h2>
        <p>Для завершения регистрации подтвердите ваш email адрес:</p>
        <a href="${verificationUrl}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
          Подтвердить email
        </a>
        <p>Если кнопка не работает, скопируйте эту ссылку: ${verificationUrl}</p>
      `
    });
  }

  private async sendPasswordResetEmail(email: string, username: string, token: string): Promise<void> {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${token}`;
    
    await this.emailTransporter.sendMail({
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: email,
      subject: 'Сброс пароля - Козырь Мастер',
      html: `
        <h2>Сброс пароля</h2>
        <p>Здравствуйте, ${username}!</p>
        <p>Вы запросили сброс пароля. Нажмите на кнопку ниже для создания нового пароля:</p>
        <a href="${resetUrl}" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
          Сбросить пароль
        </a>
        <p>Ссылка действительна в течение 1 часа.</p>
        <p>Если вы не запрашивали сброс пароля, проигнорируйте это письмо.</p>
      `
    });
  }
}
