import winston from 'winston';

const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { service: 'auth-service' },
  transports: [
    new winston.transports.File({ 
      filename: '/app/logs/auth-error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: '/app/logs/auth-combined.log' 
    }),
  ],
});

// В development режиме также логируем в консоль
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}
