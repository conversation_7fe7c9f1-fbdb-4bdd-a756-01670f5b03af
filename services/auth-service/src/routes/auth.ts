import { Router, Request, Response } from 'express';
import Jo<PERSON> from 'joi';
import { AuthService } from '../AuthService';
import { validateRequest } from '../middleware/validation';
import { logger } from '../utils/logger';

export function authRoutes(authService: AuthService): Router {
  const router = Router();

  // Схемы валидации
  const registerSchema = Joi.object({
    username: Joi.string().alphanum().min(3).max(50).required(),
    email: Joi.string().email().required(),
    password: Joi.string().min(8).max(128).required(),
    displayName: Joi.string().min(1).max(100).optional(),
    acceptTerms: Joi.boolean().valid(true).required()
  });

  const loginSchema = Joi.object({
    username: Joi.string().required(),
    password: Joi.string().required(),
    rememberMe: Joi.boolean().optional()
  });

  const refreshTokenSchema = Joi.object({
    refreshToken: Joi.string().required()
  });

  const verifyEmailSchema = Joi.object({
    token: Joi.string().required()
  });

  const passwordResetRequestSchema = Joi.object({
    email: Joi.string().email().required()
  });

  const passwordResetSchema = Joi.object({
    token: Joi.string().required(),
    newPassword: Joi.string().min(8).max(128).required()
  });

  /**
   * POST /api/auth/register
   * Регистрация нового пользователя
   */
  router.post('/register', validateRequest(registerSchema), async (req: Request, res: Response) => {
    try {
      const result = await authService.register(req.body);

      if (!result.success) {
        return res.status(400).json({
          error: result.error
        });
      }

      logger.info('User registered successfully', {
        userId: result.user?.id,
        username: result.user?.username
      });

      res.status(201).json({
        success: true,
        user: result.user,
        profile: result.profile,
        requiresVerification: result.requiresVerification
      });

    } catch (error) {
      logger.error('Registration error:', error);
      res.status(500).json({
        error: 'Внутренняя ошибка сервера'
      });
    }
  });

  /**
   * POST /api/auth/login
   * Вход в систему
   */
  router.post('/login', validateRequest(loginSchema), async (req: Request, res: Response) => {
    try {
      const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent') || 'unknown';

      const result = await authService.login(req.body, ipAddress, userAgent);

      if (!result.success) {
        return res.status(401).json({
          error: result.error
        });
      }

      // Устанавливаем refresh token в httpOnly cookie
      if (result.tokens) {
        res.cookie('refreshToken', result.tokens.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          maxAge: result.tokens.expiresIn * 1000
        });
      }

      logger.info('User logged in successfully', {
        userId: result.user?.id,
        username: result.user?.username,
        ip: ipAddress
      });

      res.json({
        success: true,
        user: result.user,
        profile: result.profile,
        accessToken: result.tokens?.accessToken,
        expiresIn: result.tokens?.expiresIn
      });

    } catch (error) {
      logger.error('Login error:', error);
      res.status(500).json({
        error: 'Внутренняя ошибка сервера'
      });
    }
  });

  /**
   * POST /api/auth/logout
   * Выход из системы
   */
  router.post('/logout', async (req: Request, res: Response) => {
    try {
      const refreshToken = req.cookies.refreshToken || req.body.refreshToken;
      const userId = req.body.userId;

      if (userId && refreshToken) {
        await authService.logout(userId, refreshToken);
      }

      // Очищаем cookie
      res.clearCookie('refreshToken');

      logger.info('User logged out', { userId });

      res.json({
        success: true,
        message: 'Выход выполнен успешно'
      });

    } catch (error) {
      logger.error('Logout error:', error);
      res.status(500).json({
        error: 'Внутренняя ошибка сервера'
      });
    }
  });

  /**
   * POST /api/auth/refresh
   * Обновление access token
   */
  router.post('/refresh', async (req: Request, res: Response) => {
    try {
      const refreshToken = req.cookies.refreshToken || req.body.refreshToken;

      if (!refreshToken) {
        return res.status(401).json({
          error: 'Refresh token не предоставлен'
        });
      }

      const result = await authService.refreshToken(refreshToken);

      if (!result.success) {
        res.clearCookie('refreshToken');
        return res.status(401).json({
          error: result.error
        });
      }

      // Обновляем refresh token в cookie
      if (result.tokens) {
        res.cookie('refreshToken', result.tokens.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          maxAge: result.tokens.expiresIn * 1000
        });
      }

      res.json({
        success: true,
        accessToken: result.tokens?.accessToken,
        expiresIn: result.tokens?.expiresIn
      });

    } catch (error) {
      logger.error('Token refresh error:', error);
      res.status(500).json({
        error: 'Внутренняя ошибка сервера'
      });
    }
  });

  /**
   * POST /api/auth/verify-email
   * Верификация email адреса
   */
  router.post('/verify-email', validateRequest(verifyEmailSchema), async (req: Request, res: Response) => {
    try {
      const result = await authService.verifyEmail(req.body);

      if (!result.success) {
        return res.status(400).json({
          error: result.error
        });
      }

      logger.info('Email verified successfully', {
        token: req.body.token
      });

      res.json({
        success: true,
        message: 'Email успешно подтвержден'
      });

    } catch (error) {
      logger.error('Email verification error:', error);
      res.status(500).json({
        error: 'Внутренняя ошибка сервера'
      });
    }
  });

  /**
   * POST /api/auth/forgot-password
   * Запрос сброса пароля
   */
  router.post('/forgot-password', validateRequest(passwordResetRequestSchema), async (req: Request, res: Response) => {
    try {
      const result = await authService.requestPasswordReset(req.body);

      // Всегда возвращаем успех для безопасности
      res.json({
        success: true,
        message: 'Если email существует, инструкции по сбросу пароля отправлены'
      });

    } catch (error) {
      logger.error('Password reset request error:', error);
      res.status(500).json({
        error: 'Внутренняя ошибка сервера'
      });
    }
  });

  /**
   * POST /api/auth/reset-password
   * Сброс пароля
   */
  router.post('/reset-password', validateRequest(passwordResetSchema), async (req: Request, res: Response) => {
    try {
      const result = await authService.resetPassword(req.body);

      if (!result.success) {
        return res.status(400).json({
          error: result.error
        });
      }

      logger.info('Password reset successfully');

      res.json({
        success: true,
        message: 'Пароль успешно изменен'
      });

    } catch (error) {
      logger.error('Password reset error:', error);
      res.status(500).json({
        error: 'Внутренняя ошибка сервера'
      });
    }
  });

  /**
   * GET /api/auth/validate
   * Проверка валидности токена
   */
  router.get('/validate', async (req: Request, res: Response) => {
    try {
      const token = req.headers.authorization?.replace('Bearer ', '');

      if (!token) {
        return res.status(401).json({
          valid: false,
          error: 'Токен не предоставлен'
        });
      }

      const result = await authService.validateToken(token);

      if (!result.valid) {
        return res.status(401).json({
          valid: false,
          error: result.error
        });
      }

      res.json({
        valid: true,
        userId: result.userId
      });

    } catch (error) {
      logger.error('Token validation error:', error);
      res.status(500).json({
        valid: false,
        error: 'Внутренняя ошибка сервера'
      });
    }
  });

  return router;
}
