import { Router, Request, Response } from 'express';
import <PERSON><PERSON> from 'joi';
import { AuthService } from '../AuthService';
import { validateRequest } from '../middleware/validation';
import { logger } from '../utils/logger';

export function userRoutes(authService: AuthService): Router {
  const router = Router();

  // Схемы валидации
  const updateUserSchema = Joi.object({
    displayName: Joi.string().min(1).max(100).optional(),
    avatarUrl: Joi.string().uri().optional(),
    preferences: Joi.object().optional()
  });

  /**
   * GET /api/user/profile
   * Получение профиля текущего пользователя
   */
  router.get('/profile', async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      
      const result = await authService.getUserInfo(userId);

      if (result.error) {
        return res.status(404).json({
          error: result.error
        });
      }

      res.json({
        success: true,
        user: result.user,
        profile: result.profile
      });

    } catch (error) {
      logger.error('Get profile error:', error);
      res.status(500).json({
        error: 'Внутренняя ошибка сервера'
      });
    }
  });

  /**
   * PUT /api/user/profile
   * Обновление профиля пользователя
   */
  router.put('/profile', validateRequest(updateUserSchema), async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      
      const result = await authService.updateUser(userId, req.body);

      if (!result.success) {
        return res.status(400).json({
          error: result.error
        });
      }

      logger.info('User profile updated', {
        userId,
        changes: Object.keys(req.body)
      });

      res.json({
        success: true,
        user: result.user
      });

    } catch (error) {
      logger.error('Update profile error:', error);
      res.status(500).json({
        error: 'Внутренняя ошибка сервера'
      });
    }
  });

  /**
   * GET /api/user/stats
   * Получение статистики пользователя
   */
  router.get('/stats', async (req: Request, res: Response) => {
    try {
      const userId = req.user!.id;
      
      // Здесь будет интеграция с игровым сервером для получения статистики
      // Пока возвращаем заглушку
      
      res.json({
        success: true,
        stats: {
          gamesPlayed: 0,
          gamesWon: 0,
          winRate: 0,
          rating: 1000,
          rank: 'Новичок I',
          achievements: []
        }
      });

    } catch (error) {
      logger.error('Get stats error:', error);
      res.status(500).json({
        error: 'Внутренняя ошибка сервера'
      });
    }
  });

  return router;
}
