import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../AuthService';
import { logger } from '../utils/logger';

// Расширяем интерфейс Request для добавления пользователя
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        username: string;
        email: string;
      };
    }
  }
}

export function authMiddleware(authService: AuthService) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
          error: 'Токен доступа не предоставлен'
        });
      }

      const token = authHeader.substring(7); // Убираем "Bearer "

      const validation = await authService.validateToken(token);

      if (!validation.valid) {
        return res.status(401).json({
          error: validation.error || 'Недействительный токен'
        });
      }

      // Получаем информацию о пользователе
      const userInfo = await authService.getUserInfo(validation.userId!);

      if (userInfo.error || !userInfo.user) {
        return res.status(401).json({
          error: 'Пользователь не найден'
        });
      }

      // Добавляем пользователя в request
      req.user = {
        id: userInfo.user.id,
        username: userInfo.user.username,
        email: userInfo.user.email
      };

      next();

    } catch (error) {
      logger.error('Auth middleware error:', error);
      res.status(500).json({
        error: 'Внутренняя ошибка сервера'
      });
    }
  };
}

export function optionalAuthMiddleware(authService: AuthService) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return next(); // Продолжаем без аутентификации
      }

      const token = authHeader.substring(7);
      const validation = await authService.validateToken(token);

      if (validation.valid && validation.userId) {
        const userInfo = await authService.getUserInfo(validation.userId);
        
        if (userInfo.user) {
          req.user = {
            id: userInfo.user.id,
            username: userInfo.user.username,
            email: userInfo.user.email
          };
        }
      }

      next();

    } catch (error) {
      logger.error('Optional auth middleware error:', error);
      next(); // Продолжаем даже при ошибке
    }
  };
}
