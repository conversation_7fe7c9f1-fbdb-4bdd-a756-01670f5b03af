import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { logger } from '../utils/logger';

export function validateRequest(schema: Joi.ObjectSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      logger.warn('Validation error', {
        path: req.path,
        errors: errorDetails
      });

      return res.status(400).json({
        error: 'Ошибка валидации данных',
        details: errorDetails
      });
    }

    req.body = value;
    next();
  };
}
