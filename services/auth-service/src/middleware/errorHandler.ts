import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.error('Unhandled error:', {
    error: error.message,
    stack: error.stack,
    path: req.path,
    method: req.method,
    ip: req.ip
  });

  // Не раскрываем детали ошибок в продакшене
  const isDevelopment = process.env.NODE_ENV === 'development';

  res.status(500).json({
    error: 'Внутренняя ошибка сервера',
    ...(isDevelopment && {
      details: error.message,
      stack: error.stack
    })
  });
};
