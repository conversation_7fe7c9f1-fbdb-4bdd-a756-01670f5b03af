import { Request, Response, NextFunction } from 'express';
import { RateLimiterRedis } from 'rate-limiter-flexible';
import Redis from 'redis';

const redis = Redis.createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379'
});

redis.connect().catch(console.error);

// Общий лимит для API
const rateLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'auth_api_limit',
  points: 100, // Количество запросов
  duration: 60, // За 60 секунд
});

// Строгий лимит для аутентификации
const authRateLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'auth_strict_limit',
  points: 5, // Только 5 попыток
  duration: 300, // За 5 минут
  blockDuration: 900, // Блокировка на 15 минут
});

// Лимит для регистрации
const registerRateLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'register_limit',
  points: 3, // 3 регистрации
  duration: 3600, // За час
});

export const rateLimitMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const key = req.ip || 'unknown';
    
    // Применяем общий лимит
    await rateLimiter.consume(key);
    
    // Применяем специфичные лимиты
    if (req.path === '/api/auth/login' || req.path === '/api/auth/refresh') {
      await authRateLimiter.consume(key);
    }
    
    if (req.path === '/api/auth/register') {
      await registerRateLimiter.consume(key);
    }
    
    next();
    
  } catch (rejRes: any) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    
    res.set('Retry-After', String(secs));
    res.status(429).json({
      error: 'Слишком много запросов',
      retryAfter: secs
    });
  }
};
