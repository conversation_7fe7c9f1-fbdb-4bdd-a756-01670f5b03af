import { Request, Response } from 'express';
import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import { Strategy as FacebookStrategy } from 'passport-facebook';
import { Strategy as DiscordStrategy } from 'passport-discord';
import { Strategy as TwitchStrategy } from 'passport-twitch-new';
import { Strategy as VKStrategy } from 'passport-vkontakte';
import { AuthService } from '../AuthService';
import { logger } from '../utils/logger';

export class OAuthProviders {
  private authService: AuthService;

  constructor(authService: AuthService) {
    this.authService = authService;
    this.initializeStrategies();
  }

  private initializeStrategies(): void {
    // Google OAuth
    if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
      passport.use(new GoogleStrategy({
        clientID: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        callbackURL: `${process.env.AUTH_SERVICE_URL}/auth/google/callback`
      }, this.handleOAuthCallback.bind(this, 'google')));
    }

    // Facebook OAuth
    if (process.env.FACEBOOK_APP_ID && process.env.FACEBOOK_APP_SECRET) {
      passport.use(new FacebookStrategy({
        clientID: process.env.FACEBOOK_APP_ID,
        clientSecret: process.env.FACEBOOK_APP_SECRET,
        callbackURL: `${process.env.AUTH_SERVICE_URL}/auth/facebook/callback`,
        profileFields: ['id', 'emails', 'name', 'picture']
      }, this.handleOAuthCallback.bind(this, 'facebook')));
    }

    // Discord OAuth
    if (process.env.DISCORD_CLIENT_ID && process.env.DISCORD_CLIENT_SECRET) {
      passport.use(new DiscordStrategy({
        clientID: process.env.DISCORD_CLIENT_ID,
        clientSecret: process.env.DISCORD_CLIENT_SECRET,
        callbackURL: `${process.env.AUTH_SERVICE_URL}/auth/discord/callback`,
        scope: ['identify', 'email']
      }, this.handleOAuthCallback.bind(this, 'discord')));
    }

    // Twitch OAuth
    if (process.env.TWITCH_CLIENT_ID && process.env.TWITCH_CLIENT_SECRET) {
      passport.use(new TwitchStrategy({
        clientID: process.env.TWITCH_CLIENT_ID,
        clientSecret: process.env.TWITCH_CLIENT_SECRET,
        callbackURL: `${process.env.AUTH_SERVICE_URL}/auth/twitch/callback`,
        scope: ['user:read:email']
      }, this.handleOAuthCallback.bind(this, 'twitch')));
    }

    // VK OAuth
    if (process.env.VK_APP_ID && process.env.VK_APP_SECRET) {
      passport.use(new VKStrategy({
        clientID: process.env.VK_APP_ID,
        clientSecret: process.env.VK_APP_SECRET,
        callbackURL: `${process.env.AUTH_SERVICE_URL}/auth/vk/callback`
      }, this.handleOAuthCallback.bind(this, 'vk')));
    }

    // Сериализация пользователя для сессий
    passport.serializeUser((user: any, done) => {
      done(null, user.id);
    });

    passport.deserializeUser(async (id: string, done) => {
      try {
        const result = await this.authService.getUserInfo(id);
        if (result.user) {
          done(null, result.user);
        } else {
          done(new Error('User not found'), null);
        }
      } catch (error) {
        done(error, null);
      }
    });
  }

  private async handleOAuthCallback(
    provider: string,
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: any
  ): Promise<void> {
    try {
      logger.info(`OAuth callback from ${provider}`, {
        providerId: profile.id,
        email: profile.emails?.[0]?.value
      });

      // Извлекаем данные профиля
      const profileData = this.extractProfileData(provider, profile);
      
      // Проверяем, существует ли пользователь с таким OAuth ID
      let user = await this.findUserByOAuthId(provider, profile.id);
      
      if (user) {
        // Обновляем токены
        await this.updateOAuthTokens(user.id, provider, accessToken, refreshToken);
        return done(null, user);
      }

      // Проверяем, существует ли пользователь с таким email
      if (profileData.email) {
        user = await this.findUserByEmail(profileData.email);
        
        if (user) {
          // Связываем существующий аккаунт с OAuth провайдером
          await this.linkOAuthAccount(user.id, provider, profile.id, accessToken, refreshToken);
          return done(null, user);
        }
      }

      // Создаем новый аккаунт
      const newUser = await this.createUserFromOAuth(provider, profileData, accessToken, refreshToken);
      return done(null, newUser);

    } catch (error) {
      logger.error(`OAuth callback error for ${provider}:`, error);
      return done(error, null);
    }
  }

  private extractProfileData(provider: string, profile: any): any {
    const baseData = {
      providerId: profile.id,
      email: null,
      displayName: profile.displayName || profile.username,
      avatarUrl: null
    };

    switch (provider) {
      case 'google':
        return {
          ...baseData,
          email: profile.emails?.[0]?.value,
          displayName: profile.displayName,
          avatarUrl: profile.photos?.[0]?.value
        };

      case 'facebook':
        return {
          ...baseData,
          email: profile.emails?.[0]?.value,
          displayName: `${profile.name?.givenName} ${profile.name?.familyName}`.trim(),
          avatarUrl: profile.photos?.[0]?.value
        };

      case 'discord':
        return {
          ...baseData,
          email: profile.email,
          displayName: `${profile.username}#${profile.discriminator}`,
          avatarUrl: profile.avatar ? 
            `https://cdn.discordapp.com/avatars/${profile.id}/${profile.avatar}.png` : null
        };

      case 'twitch':
        return {
          ...baseData,
          email: profile.email,
          displayName: profile.display_name || profile.login,
          avatarUrl: profile.profile_image_url
        };

      case 'vk':
        return {
          ...baseData,
          email: profile.emails?.[0]?.value,
          displayName: `${profile.name?.givenName} ${profile.name?.familyName}`.trim(),
          avatarUrl: profile.photos?.[0]?.value
        };

      default:
        return baseData;
    }
  }

  private async findUserByOAuthId(provider: string, providerId: string): Promise<any> {
    // Здесь должен быть запрос к базе данных
    // Пока возвращаем null
    return null;
  }

  private async findUserByEmail(email: string): Promise<any> {
    const result = await this.authService.getUserByEmail(email);
    return result.user || null;
  }

  private async updateOAuthTokens(
    userId: string, 
    provider: string, 
    accessToken: string, 
    refreshToken: string
  ): Promise<void> {
    // Обновляем токены OAuth в базе данных
    logger.info(`Updating OAuth tokens for user ${userId}, provider ${provider}`);
  }

  private async linkOAuthAccount(
    userId: string,
    provider: string,
    providerId: string,
    accessToken: string,
    refreshToken: string
  ): Promise<void> {
    // Связываем существующий аккаунт с OAuth провайдером
    logger.info(`Linking OAuth account for user ${userId}, provider ${provider}`);
  }

  private async createUserFromOAuth(
    provider: string,
    profileData: any,
    accessToken: string,
    refreshToken: string
  ): Promise<any> {
    // Создаем нового пользователя из OAuth данных
    const username = this.generateUsername(profileData.displayName, profileData.providerId);
    
    const userData = {
      username,
      email: profileData.email,
      displayName: profileData.displayName,
      avatarUrl: profileData.avatarUrl,
      isEmailVerified: !!profileData.email,
      oauthProvider: provider,
      oauthId: profileData.providerId
    };

    const result = await this.authService.createUserFromOAuth(userData);
    
    if (result.success && result.user) {
      // Сохраняем OAuth токены
      await this.saveOAuthTokens(result.user.id, provider, accessToken, refreshToken);
      return result.user;
    }

    throw new Error('Failed to create user from OAuth');
  }

  private generateUsername(displayName: string, providerId: string): string {
    // Генерируем уникальное имя пользователя
    const baseName = displayName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .substring(0, 10);
    
    const suffix = providerId.substring(0, 4);
    return `${baseName}${suffix}`;
  }

  private async saveOAuthTokens(
    userId: string,
    provider: string,
    accessToken: string,
    refreshToken: string
  ): Promise<void> {
    // Сохраняем OAuth токены в базе данных
    logger.info(`Saving OAuth tokens for user ${userId}, provider ${provider}`);
  }

  // Методы для маршрутов
  public getGoogleAuth() {
    return passport.authenticate('google', { scope: ['profile', 'email'] });
  }

  public getFacebookAuth() {
    return passport.authenticate('facebook', { scope: ['email'] });
  }

  public getDiscordAuth() {
    return passport.authenticate('discord');
  }

  public getTwitchAuth() {
    return passport.authenticate('twitch');
  }

  public getVKAuth() {
    return passport.authenticate('vkontakte');
  }

  public getOAuthCallback(provider: string) {
    return passport.authenticate(provider, {
      successRedirect: `${process.env.FRONTEND_URL}/auth/success`,
      failureRedirect: `${process.env.FRONTEND_URL}/auth/error`
    });
  }

  // Отключение OAuth аккаунта
  public async unlinkOAuthAccount(userId: string, provider: string): Promise<boolean> {
    try {
      // Проверяем, что у пользователя есть другие способы входа
      const user = await this.authService.getUserInfo(userId);
      
      if (!user.user) {
        return false;
      }

      // Проверяем, что у пользователя есть пароль или другие OAuth аккаунты
      const hasPassword = user.user.hasPassword;
      const oauthAccounts = await this.getUserOAuthAccounts(userId);
      
      if (!hasPassword && oauthAccounts.length <= 1) {
        throw new Error('Cannot unlink the only authentication method');
      }

      // Удаляем OAuth связь
      await this.removeOAuthLink(userId, provider);
      
      logger.info(`OAuth account unlinked`, { userId, provider });
      return true;

    } catch (error) {
      logger.error(`Failed to unlink OAuth account:`, error);
      return false;
    }
  }

  private async getUserOAuthAccounts(userId: string): Promise<string[]> {
    // Получаем список OAuth аккаунтов пользователя
    // Пока возвращаем пустой массив
    return [];
  }

  private async removeOAuthLink(userId: string, provider: string): Promise<void> {
    // Удаляем OAuth связь из базы данных
    logger.info(`Removing OAuth link for user ${userId}, provider ${provider}`);
  }

  // Получение информации о связанных аккаунтах
  public async getLinkedAccounts(userId: string): Promise<any[]> {
    try {
      const accounts = await this.getUserOAuthAccounts(userId);
      
      return accounts.map(provider => ({
        provider,
        isLinked: true,
        canUnlink: accounts.length > 1 // Можно отключить, если есть другие способы входа
      }));

    } catch (error) {
      logger.error(`Failed to get linked accounts:`, error);
      return [];
    }
  }

  // Обновление OAuth токенов
  public async refreshOAuthToken(userId: string, provider: string): Promise<string | null> {
    try {
      // Получаем refresh token из базы данных
      const refreshToken = await this.getStoredRefreshToken(userId, provider);
      
      if (!refreshToken) {
        return null;
      }

      // Обновляем токен в зависимости от провайдера
      const newAccessToken = await this.refreshTokenForProvider(provider, refreshToken);
      
      if (newAccessToken) {
        await this.updateOAuthTokens(userId, provider, newAccessToken, refreshToken);
        return newAccessToken;
      }

      return null;

    } catch (error) {
      logger.error(`Failed to refresh OAuth token:`, error);
      return null;
    }
  }

  private async getStoredRefreshToken(userId: string, provider: string): Promise<string | null> {
    // Получаем refresh token из базы данных
    return null;
  }

  private async refreshTokenForProvider(provider: string, refreshToken: string): Promise<string | null> {
    // Обновляем токен для конкретного провайдера
    // Здесь должна быть логика для каждого провайдера
    return null;
  }
}
