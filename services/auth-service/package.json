{"name": "@kozyr-master/auth-service", "version": "1.0.0", "description": "Authentication service for Kozyr Master", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node src/index.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "redis": "^4.6.8", "nodemailer": "^6.9.4", "joi": "^17.9.2", "rate-limiter-flexible": "^2.4.2", "winston": "^3.10.0", "dotenv": "^16.3.1", "crypto": "^1.0.1"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/pg": "^8.10.2", "@types/nodemailer": "^6.4.9", "@types/node": "^20.4.5", "typescript": "^5.1.6", "ts-node": "^10.9.1", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "eslint": "^8.45.0", "jest": "^29.6.1", "@types/jest": "^29.5.3", "ts-jest": "^29.1.1"}, "keywords": ["authentication", "jwt", "nodejs", "typescript", "kozyr-master"], "author": "Kozyr Master Team", "license": "MIT"}