-- Расширение схемы базы данных для монетизации

-- Таблица продуктов и подписок
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id VARCHAR(100) UNIQUE NOT NULL, -- ID для платежных систем
    name VARCHAR(200) NOT NULL,
    description TEXT,
    product_type VARCHAR(50) NOT NULL, -- premium_subscription, coins, cosmetic, tournament_entry, special_offer
    
    -- Цены
    price_usd DECIMAL(10,2) NOT NULL,
    price_rub DECIMAL(10,2),
    price_eur DECIMAL(10,2),
    
    -- Настройки продукта
    is_subscription BOOLEAN DEFAULT false,
    subscription_duration_days INTEGER, -- для подписок
    coins_amount INTEGER, -- количество монет
    bonus_coins INTEGER DEFAULT 0, -- бонусные монеты
    
    -- Доступность
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    available_from TIMESTAMP WITH TIME ZONE,
    available_until TIMESTAMP WITH TIME ZONE,
    
    -- Ограничения
    max_purchases_per_user INTEGER, -- ограничение покупок на пользователя
    requires_premium BOOLEAN DEFAULT false,
    min_level INTEGER DEFAULT 1,
    
    -- Метаданные
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Таблица покупок пользователей
CREATE TABLE user_purchases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id),
    
    -- Информация о покупке
    transaction_id VARCHAR(200) UNIQUE NOT NULL,
    payment_provider VARCHAR(50) NOT NULL, -- stripe, apple, google, paypal
    provider_transaction_id VARCHAR(200),
    
    -- Сумма и валюта
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    
    -- Статус покупки
    status VARCHAR(20) DEFAULT 'pending', -- pending, completed, failed, refunded, cancelled
    
    -- Время покупки
    purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    
    -- Подписки
    subscription_start TIMESTAMP WITH TIME ZONE,
    subscription_end TIMESTAMP WITH TIME ZONE,
    is_auto_renewal BOOLEAN DEFAULT false,
    
    -- Дополнительная информация
    receipt_data TEXT,
    refund_reason TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Таблица премиум подписок
CREATE TABLE premium_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    
    -- Статус подписки
    is_active BOOLEAN DEFAULT false,
    subscription_type VARCHAR(50) NOT NULL, -- basic, premium, vip
    
    -- Время подписки
    started_at TIMESTAMP WITH TIME ZONE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_payment_at TIMESTAMP WITH TIME ZONE,
    next_payment_at TIMESTAMP WITH TIME ZONE,
    
    -- Настройки
    is_auto_renewal BOOLEAN DEFAULT true,
    payment_provider VARCHAR(50),
    provider_subscription_id VARCHAR(200),
    
    -- Статистика
    total_payments INTEGER DEFAULT 0,
    total_amount_paid DECIMAL(10,2) DEFAULT 0.00,
    
    -- Льготы
    benefits JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Таблица виртуальной валюты (монеты)
CREATE TABLE user_coins (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    
    -- Баланс монет
    balance INTEGER DEFAULT 0,
    total_earned INTEGER DEFAULT 0,
    total_spent INTEGER DEFAULT 0,
    
    -- Ограничения
    daily_earned_today INTEGER DEFAULT 0,
    daily_earned_date DATE DEFAULT CURRENT_DATE,
    weekly_earned INTEGER DEFAULT 0,
    weekly_earned_reset DATE DEFAULT CURRENT_DATE,
    
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Таблица транзакций монет
CREATE TABLE coin_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Транзакция
    amount INTEGER NOT NULL, -- положительное для получения, отрицательное для трат
    transaction_type VARCHAR(50) NOT NULL, -- purchase, reward, game_win, daily_bonus, tournament_prize, spend_cosmetic, spend_entry
    description TEXT,
    
    -- Связанные объекты
    related_purchase_id UUID REFERENCES user_purchases(id),
    related_game_id UUID REFERENCES game_sessions(id),
    related_tournament_id UUID,
    
    -- Баланс после транзакции
    balance_after INTEGER NOT NULL,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    INDEX idx_coin_transactions_user_id (user_id),
    INDEX idx_coin_transactions_type (transaction_type),
    INDEX idx_coin_transactions_created_at (created_at)
);

-- Таблица косметических предметов
CREATE TABLE cosmetic_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    item_id VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- Тип предмета
    item_type VARCHAR(50) NOT NULL, -- avatar, card_back, table_theme, emote, title, badge
    category VARCHAR(50),
    
    -- Цена
    price_coins INTEGER,
    price_usd DECIMAL(10,2),
    is_premium_only BOOLEAN DEFAULT false,
    
    -- Редкость
    rarity VARCHAR(20) NOT NULL, -- common, rare, epic, legendary, exclusive
    
    -- Доступность
    is_active BOOLEAN DEFAULT true,
    is_limited BOOLEAN DEFAULT false,
    available_from TIMESTAMP WITH TIME ZONE,
    available_until TIMESTAMP WITH TIME ZONE,
    
    -- Требования
    required_level INTEGER DEFAULT 1,
    required_achievement VARCHAR(50),
    required_rank VARCHAR(50),
    
    -- Ресурсы
    image_url TEXT,
    animation_url TEXT,
    preview_url TEXT,
    
    -- Метаданные
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Таблица косметических предметов пользователей
CREATE TABLE user_cosmetics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    cosmetic_id UUID REFERENCES cosmetic_items(id) ON DELETE CASCADE,
    
    -- Информация о получении
    acquired_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    acquisition_method VARCHAR(50) NOT NULL, -- purchase, reward, achievement, gift, event
    
    -- Статус
    is_equipped BOOLEAN DEFAULT false,
    
    UNIQUE(user_id, cosmetic_id)
);

-- Таблица специальных предложений
CREATE TABLE special_offers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- Тип предложения
    offer_type VARCHAR(50) NOT NULL, -- first_purchase, weekend_deal, level_up, comeback, seasonal
    
    -- Скидка
    discount_percent INTEGER DEFAULT 0,
    bonus_coins INTEGER DEFAULT 0,
    bonus_items JSONB DEFAULT '[]',
    
    -- Время действия
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Ограничения
    max_uses_total INTEGER,
    max_uses_per_user INTEGER DEFAULT 1,
    current_uses INTEGER DEFAULT 0,
    
    -- Условия показа
    target_audience JSONB DEFAULT '{}', -- условия для показа предложения
    
    -- Настройки
    is_active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Таблица использования специальных предложений
CREATE TABLE user_offer_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    offer_id UUID REFERENCES special_offers(id) ON DELETE CASCADE,
    purchase_id UUID REFERENCES user_purchases(id),
    
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, offer_id)
);

-- Таблица турнирных взносов
CREATE TABLE tournament_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tournament_id UUID, -- ссылка на турнир
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Взнос
    entry_fee_coins INTEGER DEFAULT 0,
    entry_fee_usd DECIMAL(10,2) DEFAULT 0.00,
    payment_method VARCHAR(50), -- coins, purchase, premium_free
    
    -- Статус
    is_paid BOOLEAN DEFAULT false,
    paid_at TIMESTAMP WITH TIME ZONE,
    
    -- Возврат средств
    is_refunded BOOLEAN DEFAULT false,
    refund_reason TEXT,
    refunded_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(tournament_id, user_id)
);

-- Таблица реферальной программы
CREATE TABLE referral_program (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    referrer_id UUID REFERENCES users(id) ON DELETE CASCADE,
    referred_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Статус
    is_active BOOLEAN DEFAULT true,
    is_completed BOOLEAN DEFAULT false, -- выполнены ли условия для награды
    
    -- Награды
    referrer_reward_coins INTEGER DEFAULT 0,
    referred_reward_coins INTEGER DEFAULT 0,
    referrer_reward_premium_days INTEGER DEFAULT 0,
    
    -- Условия выполнения
    required_games INTEGER DEFAULT 10,
    required_purchases INTEGER DEFAULT 1,
    games_played INTEGER DEFAULT 0,
    purchases_made INTEGER DEFAULT 0,
    
    -- Время
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(referred_id) -- один пользователь может быть приглашен только один раз
);

-- Таблица ежедневных наград
CREATE TABLE daily_rewards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Прогресс
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    last_claim_date DATE,
    
    -- Настройки
    streak_multiplier DECIMAL(3,2) DEFAULT 1.00,
    premium_bonus DECIMAL(3,2) DEFAULT 1.00,
    
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id)
);

-- Индексы для оптимизации
CREATE INDEX idx_products_product_type ON products(product_type);
CREATE INDEX idx_products_is_active ON products(is_active);
CREATE INDEX idx_products_is_featured ON products(is_featured);

CREATE INDEX idx_user_purchases_user_id ON user_purchases(user_id);
CREATE INDEX idx_user_purchases_status ON user_purchases(status);
CREATE INDEX idx_user_purchases_purchased_at ON user_purchases(purchased_at);

CREATE INDEX idx_premium_subscriptions_user_id ON premium_subscriptions(user_id);
CREATE INDEX idx_premium_subscriptions_is_active ON premium_subscriptions(is_active);
CREATE INDEX idx_premium_subscriptions_expires_at ON premium_subscriptions(expires_at);

CREATE INDEX idx_cosmetic_items_item_type ON cosmetic_items(item_type);
CREATE INDEX idx_cosmetic_items_rarity ON cosmetic_items(rarity);
CREATE INDEX idx_cosmetic_items_is_active ON cosmetic_items(is_active);

CREATE INDEX idx_user_cosmetics_user_id ON user_cosmetics(user_id);
CREATE INDEX idx_user_cosmetics_is_equipped ON user_cosmetics(is_equipped);

CREATE INDEX idx_special_offers_offer_type ON special_offers(offer_type);
CREATE INDEX idx_special_offers_is_active ON special_offers(is_active);
CREATE INDEX idx_special_offers_time ON special_offers(start_time, end_time);

-- Триггеры для автоматического обновления
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_premium_subscriptions_updated_at BEFORE UPDATE ON premium_subscriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cosmetic_items_updated_at BEFORE UPDATE ON cosmetic_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_special_offers_updated_at BEFORE UPDATE ON special_offers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Функция для обновления баланса монет
CREATE OR REPLACE FUNCTION update_user_coins_balance()
RETURNS TRIGGER AS $$
BEGIN
    -- Обновляем баланс монет при добавлении транзакции
    IF TG_OP = 'INSERT' THEN
        INSERT INTO user_coins (user_id, balance, total_earned, total_spent)
        VALUES (NEW.user_id, NEW.amount, 
                CASE WHEN NEW.amount > 0 THEN NEW.amount ELSE 0 END,
                CASE WHEN NEW.amount < 0 THEN ABS(NEW.amount) ELSE 0 END)
        ON CONFLICT (user_id) DO UPDATE SET
            balance = user_coins.balance + NEW.amount,
            total_earned = user_coins.total_earned + CASE WHEN NEW.amount > 0 THEN NEW.amount ELSE 0 END,
            total_spent = user_coins.total_spent + CASE WHEN NEW.amount < 0 THEN ABS(NEW.amount) ELSE 0 END,
            updated_at = NOW();
            
        -- Обновляем balance_after в транзакции
        UPDATE coin_transactions 
        SET balance_after = (SELECT balance FROM user_coins WHERE user_id = NEW.user_id)
        WHERE id = NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Триггер для автоматического обновления баланса монет
CREATE TRIGGER update_coins_on_transaction
    AFTER INSERT ON coin_transactions
    FOR EACH ROW EXECUTE FUNCTION update_user_coins_balance();

-- Функция для проверки и обновления премиум подписок
CREATE OR REPLACE FUNCTION check_premium_subscriptions()
RETURNS void AS $$
BEGIN
    -- Деактивируем истекшие подписки
    UPDATE premium_subscriptions 
    SET is_active = false
    WHERE is_active = true AND expires_at < NOW();
    
    -- Обновляем статус пользователей
    UPDATE users 
    SET is_premium = EXISTS(
        SELECT 1 FROM premium_subscriptions 
        WHERE user_id = users.id AND is_active = true
    );
END;
$$ LANGUAGE plpgsql;

-- Создаем задачу для периодической проверки подписок (нужно настроить в cron)
-- SELECT cron.schedule('check-premium-subscriptions', '0 * * * *', 'SELECT check_premium_subscriptions();');
