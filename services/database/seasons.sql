-- Расширение схемы базы данных для рейтинговых сезонов

-- Таблица сезонов
CREATE TABLE rating_seasons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    season_number INTEGER NOT NULL,
    
    -- Время сезона
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    registration_start TIMESTAMP WITH TIME ZONE,
    registration_end TIMESTAMP WITH TIME ZONE,
    
    -- Настройки сезона
    initial_rating INTEGER DEFAULT 1000,
    rating_decay_enabled BOOLEAN DEFAULT true,
    rating_decay_days INTEGER DEFAULT 14,
    rating_decay_amount INTEGER DEFAULT 25,
    
    -- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ьные требования
    min_games_for_ranking INTEGER DEFAULT 10,
    min_games_for_rewards INTEGER DEFAULT 25,
    
    -- Статус сезона
    status VARCHAR(20) DEFAULT 'upcoming', -- upcoming, active, finished, cancelled
    
    -- Призы и награды
    total_prize_pool INTEGER DEFAULT 0,
    reward_distribution JSONB DEFAULT '{}',
    
    -- Метаданные
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_season_dates CHECK (start_date < end_date),
    CONSTRAINT unique_season_number UNIQUE (season_number)
);

-- Таблица участников сезона
CREATE TABLE season_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    season_id UUID REFERENCES rating_seasons(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Рейтинги
    starting_rating INTEGER NOT NULL,
    current_rating INTEGER NOT NULL,
    peak_rating INTEGER NOT NULL,
    final_rating INTEGER,
    
    -- Статистика игр
    games_played INTEGER DEFAULT 0,
    games_won INTEGER DEFAULT 0,
    games_lost INTEGER DEFAULT 0,
    games_drawn INTEGER DEFAULT 0,
    
    -- Статистика по играм
    durak_games INTEGER DEFAULT 0,
    durak_wins INTEGER DEFAULT 0,
    poker_games INTEGER DEFAULT 0,
    poker_wins INTEGER DEFAULT 0,
    preferans_games INTEGER DEFAULT 0,
    preferans_wins INTEGER DEFAULT 0,
    blackjack_games INTEGER DEFAULT 0,
    blackjack_wins INTEGER DEFAULT 0,
    
    -- Достижения сезона
    longest_win_streak INTEGER DEFAULT 0,
    current_win_streak INTEGER DEFAULT 0,
    longest_loss_streak INTEGER DEFAULT 0,
    current_loss_streak INTEGER DEFAULT 0,
    
    -- Активность
    first_game_at TIMESTAMP WITH TIME ZONE,
    last_game_at TIMESTAMP WITH TIME ZONE,
    total_playtime INTEGER DEFAULT 0, -- в секундах
    days_active INTEGER DEFAULT 0,
    
    -- Ранг и позиция
    current_rank VARCHAR(50),
    final_rank VARCHAR(50),
    leaderboard_position INTEGER,
    final_position INTEGER,
    
    -- Награды
    rewards_earned JSONB DEFAULT '[]',
    reward_points INTEGER DEFAULT 0,
    
    -- Статус участника
    is_active BOOLEAN DEFAULT true,
    is_qualified BOOLEAN DEFAULT false, -- квалифицирован для наград
    
    -- Временные метки
    registered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_decay_at TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(season_id, user_id)
);

-- Таблица истории рейтинга в сезоне
CREATE TABLE season_rating_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    season_id UUID REFERENCES rating_seasons(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Изменение рейтинга
    old_rating INTEGER NOT NULL,
    new_rating INTEGER NOT NULL,
    rating_change INTEGER NOT NULL,
    
    -- Причина изменения
    change_reason VARCHAR(50) NOT NULL, -- game_win, game_loss, decay, adjustment, bonus
    game_type VARCHAR(50),
    opponent_id UUID REFERENCES users(id),
    opponent_rating INTEGER,
    
    -- Дополнительная информация
    game_session_id UUID REFERENCES game_sessions(id),
    multiplier DECIMAL(3,2) DEFAULT 1.00,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    INDEX idx_season_rating_history_season_user (season_id, user_id),
    INDEX idx_season_rating_history_created_at (created_at)
);

-- Таблица лидербордов сезона
CREATE TABLE season_leaderboards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    season_id UUID REFERENCES rating_seasons(id) ON DELETE CASCADE,
    leaderboard_type VARCHAR(50) NOT NULL, -- overall, durak, poker, preferans, blackjack
    
    -- Настройки лидерборда
    min_games INTEGER DEFAULT 10,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Топ игроки (денормализованные данные для быстрого доступа)
    top_players JSONB DEFAULT '[]',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(season_id, leaderboard_type)
);

-- Таблица наград сезона
CREATE TABLE season_rewards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    season_id UUID REFERENCES rating_seasons(id) ON DELETE CASCADE,
    
    -- Тип награды
    reward_type VARCHAR(50) NOT NULL, -- rank_reward, position_reward, achievement_reward, participation_reward
    
    -- Условия получения
    condition_type VARCHAR(50) NOT NULL, -- rank, position, games_played, rating_threshold
    condition_value VARCHAR(100) NOT NULL,
    
    -- Награда
    reward_name VARCHAR(200) NOT NULL,
    reward_description TEXT,
    reward_data JSONB DEFAULT '{}', -- детали награды (очки, предметы, титулы)
    
    -- Настройки
    is_active BOOLEAN DEFAULT true,
    max_recipients INTEGER, -- ограничение количества получателей
    current_recipients INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Таблица полученных наград
CREATE TABLE season_reward_claims (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    season_id UUID REFERENCES rating_seasons(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    reward_id UUID REFERENCES season_rewards(id) ON DELETE CASCADE,
    
    -- Информация о получении
    claimed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    final_position INTEGER,
    final_rating INTEGER,
    final_rank VARCHAR(50),
    
    -- Статус
    is_claimed BOOLEAN DEFAULT false,
    claimed_by_user_at TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(season_id, user_id, reward_id)
);

-- Таблица событий сезона
CREATE TABLE season_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    season_id UUID REFERENCES rating_seasons(id) ON DELETE CASCADE,
    
    -- Информация о событии
    event_type VARCHAR(50) NOT NULL, -- double_rating, bonus_weekend, special_tournament
    name VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- Время события
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Настройки события
    rating_multiplier DECIMAL(3,2) DEFAULT 1.00,
    bonus_points INTEGER DEFAULT 0,
    affected_games VARCHAR(100), -- какие игры затронуты
    
    -- Условия участия
    min_rating INTEGER,
    max_rating INTEGER,
    min_games INTEGER,
    
    -- Статус
    is_active BOOLEAN DEFAULT true,
    
    -- Метаданные
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Таблица достижений сезона
CREATE TABLE season_achievements (
    id VARCHAR(50) PRIMARY KEY,
    season_id UUID REFERENCES rating_seasons(id) ON DELETE CASCADE,
    
    -- Информация о достижении
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(50) NOT NULL,
    rarity VARCHAR(20) NOT NULL, -- common, rare, epic, legendary, seasonal
    icon VARCHAR(10),
    
    -- Условия получения
    condition_type VARCHAR(50) NOT NULL,
    condition_value INTEGER NOT NULL,
    condition_data JSONB DEFAULT '{}',
    
    -- Награды
    reward_points INTEGER DEFAULT 0,
    reward_title VARCHAR(100),
    reward_badge VARCHAR(100),
    
    -- Статистика
    total_earned INTEGER DEFAULT 0,
    
    -- Настройки
    is_active BOOLEAN DEFAULT true,
    is_hidden BOOLEAN DEFAULT false, -- скрытые достижения
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Таблица прогресса достижений сезона
CREATE TABLE season_achievement_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    season_id UUID REFERENCES rating_seasons(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    achievement_id VARCHAR(50) REFERENCES season_achievements(id),
    
    -- Прогресс
    current_progress INTEGER DEFAULT 0,
    is_completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Метаданные прогресса
    progress_data JSONB DEFAULT '{}',
    
    UNIQUE(season_id, user_id, achievement_id)
);

-- Индексы для оптимизации
CREATE INDEX idx_rating_seasons_status ON rating_seasons(status);
CREATE INDEX idx_rating_seasons_dates ON rating_seasons(start_date, end_date);

CREATE INDEX idx_season_participants_season_id ON season_participants(season_id);
CREATE INDEX idx_season_participants_user_id ON season_participants(user_id);
CREATE INDEX idx_season_participants_current_rating ON season_participants(current_rating DESC);
CREATE INDEX idx_season_participants_final_position ON season_participants(final_position);
CREATE INDEX idx_season_participants_is_qualified ON season_participants(is_qualified);

CREATE INDEX idx_season_rating_history_season_user ON season_rating_history(season_id, user_id);
CREATE INDEX idx_season_rating_history_created_at ON season_rating_history(created_at);

CREATE INDEX idx_season_leaderboards_season_type ON season_leaderboards(season_id, leaderboard_type);

CREATE INDEX idx_season_rewards_season_id ON season_rewards(season_id);
CREATE INDEX idx_season_rewards_condition ON season_rewards(condition_type, condition_value);

CREATE INDEX idx_season_reward_claims_season_user ON season_reward_claims(season_id, user_id);
CREATE INDEX idx_season_reward_claims_is_claimed ON season_reward_claims(is_claimed);

CREATE INDEX idx_season_events_season_id ON season_events(season_id);
CREATE INDEX idx_season_events_time ON season_events(start_time, end_time);
CREATE INDEX idx_season_events_is_active ON season_events(is_active);

CREATE INDEX idx_season_achievements_season_id ON season_achievements(season_id);
CREATE INDEX idx_season_achievements_category ON season_achievements(category);

CREATE INDEX idx_season_achievement_progress_season_user ON season_achievement_progress(season_id, user_id);
CREATE INDEX idx_season_achievement_progress_completed ON season_achievement_progress(is_completed);

-- Триггеры для автоматического обновления
CREATE TRIGGER update_rating_seasons_updated_at BEFORE UPDATE ON rating_seasons FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_season_leaderboards_updated_at BEFORE UPDATE ON season_leaderboards FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_season_events_updated_at BEFORE UPDATE ON season_events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Функция для обновления статистики участника сезона
CREATE OR REPLACE FUNCTION update_season_participant_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Обновляем статистику при добавлении новой записи в историю рейтинга
    IF TG_OP = 'INSERT' THEN
        UPDATE season_participants 
        SET 
            current_rating = NEW.new_rating,
            peak_rating = GREATEST(peak_rating, NEW.new_rating),
            last_game_at = NEW.created_at
        WHERE season_id = NEW.season_id AND user_id = NEW.user_id;
        
        -- Обновляем статистику игр если это результат игры
        IF NEW.change_reason IN ('game_win', 'game_loss') THEN
            UPDATE season_participants 
            SET 
                games_played = games_played + 1,
                games_won = CASE WHEN NEW.change_reason = 'game_win' THEN games_won + 1 ELSE games_won END,
                games_lost = CASE WHEN NEW.change_reason = 'game_loss' THEN games_lost + 1 ELSE games_lost END
            WHERE season_id = NEW.season_id AND user_id = NEW.user_id;
            
            -- Обновляем статистику по типу игры
            IF NEW.game_type = 'durak' THEN
                UPDATE season_participants 
                SET 
                    durak_games = durak_games + 1,
                    durak_wins = CASE WHEN NEW.change_reason = 'game_win' THEN durak_wins + 1 ELSE durak_wins END
                WHERE season_id = NEW.season_id AND user_id = NEW.user_id;
            ELSIF NEW.game_type = 'poker' THEN
                UPDATE season_participants 
                SET 
                    poker_games = poker_games + 1,
                    poker_wins = CASE WHEN NEW.change_reason = 'game_win' THEN poker_wins + 1 ELSE poker_wins END
                WHERE season_id = NEW.season_id AND user_id = NEW.user_id;
            END IF;
        END IF;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Триггер для автоматического обновления статистики участника
CREATE TRIGGER update_season_stats_on_rating_change
    AFTER INSERT ON season_rating_history
    FOR EACH ROW EXECUTE FUNCTION update_season_participant_stats();

-- Функция для создания нового сезона
CREATE OR REPLACE FUNCTION create_new_season(
    p_name VARCHAR(100),
    p_description TEXT,
    p_start_date TIMESTAMP WITH TIME ZONE,
    p_end_date TIMESTAMP WITH TIME ZONE,
    p_initial_rating INTEGER DEFAULT 1000
)
RETURNS UUID AS $$
DECLARE
    v_season_id UUID;
    v_season_number INTEGER;
BEGIN
    -- Получаем следующий номер сезона
    SELECT COALESCE(MAX(season_number), 0) + 1 INTO v_season_number FROM rating_seasons;
    
    -- Создаем новый сезон
    INSERT INTO rating_seasons (
        name, description, season_number, start_date, end_date, initial_rating
    ) VALUES (
        p_name, p_description, v_season_number, p_start_date, p_end_date, p_initial_rating
    ) RETURNING id INTO v_season_id;
    
    -- Создаем лидерборды для нового сезона
    INSERT INTO season_leaderboards (season_id, leaderboard_type) VALUES
        (v_season_id, 'overall'),
        (v_season_id, 'durak'),
        (v_season_id, 'poker'),
        (v_season_id, 'preferans'),
        (v_season_id, 'blackjack');
    
    RETURN v_season_id;
END;
$$ LANGUAGE plpgsql;
