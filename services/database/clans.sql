-- Расширение схемы базы данных для кланов и гильдий

-- Таблица кланов
CREATE TABLE clans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) UNIQUE NOT NULL,
    tag VARCHAR(10) UNIQUE NOT NULL,
    description TEXT,
    logo_url TEXT,
    banner_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Настройки клана
    is_public BOOLEAN DEFAULT true,
    max_members INTEGER DEFAULT 50,
    min_rating INTEGER DEFAULT 0,
    required_games INTEGER DEFAULT 0,
    
    -- Статистика клана
    total_members INTEGER DEFAULT 0,
    average_rating DECIMAL(8,2) DEFAULT 0.00,
    total_games_played INTEGER DEFAULT 0,
    total_games_won INTEGER DEFAULT 0,
    clan_rating INTEGER DEFAULT 1000,
    
    -- <PERSON><PERSON><PERSON><PERSON><PERSON> клана
    leader_id UUID REFERENCES users(id) ON DELETE SET NULL,
    
    -- Статус клана
    status VARCHAR(20) DEFAULT 'active', -- active, disbanded, suspended
    
    -- Метаданные
    metadata JSONB DEFAULT '{}',
    
    CONSTRAINT valid_clan_name CHECK (LENGTH(name) >= 3),
    CONSTRAINT valid_clan_tag CHECK (LENGTH(tag) >= 2)
);

-- Таблица участников кланов
CREATE TABLE clan_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    clan_id UUID REFERENCES clans(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(20) DEFAULT 'member', -- leader, officer, veteran, member, recruit
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Вклад участника
    contribution_points INTEGER DEFAULT 0,
    games_played_for_clan INTEGER DEFAULT 0,
    games_won_for_clan INTEGER DEFAULT 0,
    
    -- Статус участника
    is_active BOOLEAN DEFAULT true,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Права участника
    permissions JSONB DEFAULT '{}',
    
    UNIQUE(clan_id, user_id)
);

-- Таблица заявок в кланы
CREATE TABLE clan_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    clan_id UUID REFERENCES clans(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    message TEXT,
    status VARCHAR(20) DEFAULT 'pending', -- pending, accepted, rejected, withdrawn
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by UUID REFERENCES users(id),
    review_message TEXT,
    
    UNIQUE(clan_id, user_id, status) DEFERRABLE INITIALLY DEFERRED
);

-- Таблица приглашений в кланы
CREATE TABLE clan_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    clan_id UUID REFERENCES clans(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    invited_by UUID REFERENCES users(id) ON DELETE CASCADE,
    message TEXT,
    status VARCHAR(20) DEFAULT 'pending', -- pending, accepted, rejected, expired
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
    responded_at TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(clan_id, user_id, status) DEFERRABLE INITIALLY DEFERRED
);

-- Таблица событий клана
CREATE TABLE clan_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    clan_id UUID REFERENCES clans(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    event_type VARCHAR(50) NOT NULL, -- tournament, meeting, training, social
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    max_participants INTEGER,
    current_participants INTEGER DEFAULT 0,
    
    -- Настройки события
    is_public BOOLEAN DEFAULT false,
    requires_approval BOOLEAN DEFAULT false,
    min_rating INTEGER,
    
    -- Организатор
    organizer_id UUID REFERENCES users(id) ON DELETE SET NULL,
    
    -- Статус события
    status VARCHAR(20) DEFAULT 'upcoming', -- upcoming, active, finished, cancelled
    
    -- Метаданные
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Таблица участников событий клана
CREATE TABLE clan_event_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID REFERENCES clan_events(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'registered', -- registered, attended, no_show, cancelled
    registered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(event_id, user_id)
);

-- Таблица достижений кланов
CREATE TABLE clan_achievements (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(50) NOT NULL,
    rarity VARCHAR(20) NOT NULL, -- common, rare, epic, legendary
    icon VARCHAR(10),
    condition_type VARCHAR(50) NOT NULL,
    condition_value INTEGER NOT NULL,
    reward_type VARCHAR(20),
    reward_value VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Таблица достижений кланов
CREATE TABLE clan_achievement_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    clan_id UUID REFERENCES clans(id) ON DELETE CASCADE,
    achievement_id VARCHAR(50) REFERENCES clan_achievements(id),
    progress INTEGER DEFAULT 0,
    is_completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(clan_id, achievement_id)
);

-- Таблица войн кланов
CREATE TABLE clan_wars (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    clan1_id UUID REFERENCES clans(id) ON DELETE CASCADE,
    clan2_id UUID REFERENCES clans(id) ON DELETE CASCADE,
    name VARCHAR(200),
    description TEXT,
    
    -- Настройки войны
    max_participants_per_clan INTEGER DEFAULT 10,
    games_per_match INTEGER DEFAULT 3,
    war_type VARCHAR(50) DEFAULT 'best_of_series', -- best_of_series, points_based, elimination
    
    -- Время проведения
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    registration_deadline TIMESTAMP WITH TIME ZONE,
    
    -- Статус войны
    status VARCHAR(20) DEFAULT 'upcoming', -- upcoming, registration, active, finished, cancelled
    
    -- Результаты
    clan1_score INTEGER DEFAULT 0,
    clan2_score INTEGER DEFAULT 0,
    winner_clan_id UUID REFERENCES clans(id),
    
    -- Призы
    prize_pool INTEGER DEFAULT 0,
    winner_prize INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT different_clans CHECK (clan1_id != clan2_id)
);

-- Таблица участников войн кланов
CREATE TABLE clan_war_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    war_id UUID REFERENCES clan_wars(id) ON DELETE CASCADE,
    clan_id UUID REFERENCES clans(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    registered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Статистика участника в войне
    games_played INTEGER DEFAULT 0,
    games_won INTEGER DEFAULT 0,
    points_earned INTEGER DEFAULT 0,
    
    UNIQUE(war_id, user_id)
);

-- Таблица матчей войн кланов
CREATE TABLE clan_war_matches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    war_id UUID REFERENCES clan_wars(id) ON DELETE CASCADE,
    player1_id UUID REFERENCES users(id) ON DELETE CASCADE,
    player2_id UUID REFERENCES users(id) ON DELETE CASCADE,
    game_session_id UUID REFERENCES game_sessions(id),
    
    -- Результат матча
    winner_id UUID REFERENCES users(id),
    status VARCHAR(20) DEFAULT 'pending', -- pending, active, finished, forfeit
    
    -- Время матча
    scheduled_time TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    finished_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Таблица сообщений клана
CREATE TABLE clan_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    clan_id UUID REFERENCES clans(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES users(id) ON DELETE CASCADE,
    message_type VARCHAR(20) DEFAULT 'general', -- general, announcement, officer, leader
    title VARCHAR(200),
    content TEXT NOT NULL,
    
    -- Настройки сообщения
    is_pinned BOOLEAN DEFAULT false,
    is_important BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Таблица настроек кланов
CREATE TABLE clan_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    clan_id UUID REFERENCES clans(id) ON DELETE CASCADE UNIQUE,
    
    -- Настройки приватности
    auto_accept_applications BOOLEAN DEFAULT false,
    allow_member_invites BOOLEAN DEFAULT true,
    public_statistics BOOLEAN DEFAULT true,
    
    -- Настройки активности
    activity_requirement_days INTEGER DEFAULT 30,
    min_games_per_month INTEGER DEFAULT 10,
    auto_kick_inactive BOOLEAN DEFAULT false,
    
    -- Настройки войн
    auto_accept_wars BOOLEAN DEFAULT false,
    min_rating_for_wars INTEGER DEFAULT 1000,
    
    -- Настройки чата
    chat_moderation_enabled BOOLEAN DEFAULT true,
    allow_external_links BOOLEAN DEFAULT false,
    
    -- Кастомные настройки
    custom_settings JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Индексы для оптимизации
CREATE INDEX idx_clans_name ON clans(name);
CREATE INDEX idx_clans_tag ON clans(tag);
CREATE INDEX idx_clans_leader_id ON clans(leader_id);
CREATE INDEX idx_clans_status ON clans(status);
CREATE INDEX idx_clans_clan_rating ON clans(clan_rating DESC);

CREATE INDEX idx_clan_members_clan_id ON clan_members(clan_id);
CREATE INDEX idx_clan_members_user_id ON clan_members(user_id);
CREATE INDEX idx_clan_members_role ON clan_members(role);
CREATE INDEX idx_clan_members_is_active ON clan_members(is_active);

CREATE INDEX idx_clan_applications_clan_id ON clan_applications(clan_id);
CREATE INDEX idx_clan_applications_user_id ON clan_applications(user_id);
CREATE INDEX idx_clan_applications_status ON clan_applications(status);

CREATE INDEX idx_clan_invitations_clan_id ON clan_invitations(clan_id);
CREATE INDEX idx_clan_invitations_user_id ON clan_invitations(user_id);
CREATE INDEX idx_clan_invitations_status ON clan_invitations(status);
CREATE INDEX idx_clan_invitations_expires_at ON clan_invitations(expires_at);

CREATE INDEX idx_clan_events_clan_id ON clan_events(clan_id);
CREATE INDEX idx_clan_events_start_time ON clan_events(start_time);
CREATE INDEX idx_clan_events_status ON clan_events(status);

CREATE INDEX idx_clan_wars_clan1_id ON clan_wars(clan1_id);
CREATE INDEX idx_clan_wars_clan2_id ON clan_wars(clan2_id);
CREATE INDEX idx_clan_wars_status ON clan_wars(status);
CREATE INDEX idx_clan_wars_start_time ON clan_wars(start_time);

-- Триггеры для автоматического обновления
CREATE TRIGGER update_clans_updated_at BEFORE UPDATE ON clans FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_clan_events_updated_at BEFORE UPDATE ON clan_events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_clan_wars_updated_at BEFORE UPDATE ON clan_wars FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_clan_messages_updated_at BEFORE UPDATE ON clan_messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_clan_settings_updated_at BEFORE UPDATE ON clan_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Функция для обновления статистики клана
CREATE OR REPLACE FUNCTION update_clan_statistics()
RETURNS TRIGGER AS $$
BEGIN
    -- Обновляем количество участников
    UPDATE clans 
    SET total_members = (
        SELECT COUNT(*) 
        FROM clan_members 
        WHERE clan_id = COALESCE(NEW.clan_id, OLD.clan_id) AND is_active = true
    )
    WHERE id = COALESCE(NEW.clan_id, OLD.clan_id);
    
    -- Обновляем средний рейтинг
    UPDATE clans 
    SET average_rating = (
        SELECT COALESCE(AVG(pp.rating), 0)
        FROM clan_members cm
        JOIN player_profiles pp ON cm.user_id = pp.user_id
        WHERE cm.clan_id = COALESCE(NEW.clan_id, OLD.clan_id) AND cm.is_active = true
    )
    WHERE id = COALESCE(NEW.clan_id, OLD.clan_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Триггер для автоматического обновления статистики клана
CREATE TRIGGER update_clan_stats_on_member_change
    AFTER INSERT OR UPDATE OR DELETE ON clan_members
    FOR EACH ROW EXECUTE FUNCTION update_clan_statistics();
