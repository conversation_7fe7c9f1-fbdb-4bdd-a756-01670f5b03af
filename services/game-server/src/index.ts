/**
 * Game Server - WebSocket сервер для многопользовательской игры
 */

import express from 'express';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import { GameManager, getGamePublicState } from './game/GameManager';
import { RoomManager, getRoomPublicInfo, isRoomOwner } from './rooms/RoomManager';
import { ChatManager } from './chat/ChatManager';
import { PlayerManager, getPlayerPublicInfo } from './players/PlayerManager';
import { RatingManager } from './rating/RatingManager';
import { AchievementManager } from './achievements/AchievementManager';
import { TournamentManager } from './tournaments/TournamentManager';

const app = express();
const server = createServer(app);

// Настройка CORS
app.use(cors({
  origin: ["http://localhost:3000", "http://localhost:3001"],
  credentials: true
}));

// Socket.IO сервер
const io = new SocketIOServer(server, {
  cors: {
    origin: ["http://localhost:3000", "http://localhost:3001"],
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Менеджеры
const gameManager = new GameManager();
const roomManager = new RoomManager();
const chatManager = new ChatManager();
const playerManager = new PlayerManager();
const ratingManager = new RatingManager();
const achievementManager = new AchievementManager();
const tournamentManager = new TournamentManager(gameManager, ratingManager);

// Базовые маршруты
app.get('/', (req, res) => {
  res.json({
    message: 'Kozyr Master Game Server',
    version: '0.1.0',
    status: 'running',
    rooms: roomManager.getRoomsCount(),
    players: playerManager.getPlayersCount()
  });
});

app.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

app.get('/stats', (req, res) => {
  res.json({
    rooms: roomManager.getStats(),
    players: playerManager.getStats(),
    games: gameManager.getStats(),
    ratings: ratingManager.getSystemStats(),
    achievements: achievementManager.getAchievementStats()
  });
});

// API для рейтингов
app.get('/leaderboard', (req, res) => {
  const limit = parseInt(req.query.limit as string) || 50;
  res.json(ratingManager.getLeaderboard(limit));
});

app.get('/top-players', (req, res) => {
  res.json(ratingManager.getTopPlayers());
});

app.get('/player/:playerId/rating', (req, res) => {
  const { playerId } = req.params;
  const rating = ratingManager.getPlayerStats(playerId);

  if (!rating) {
    return res.status(404).json({ error: 'Player not found' });
  }

  res.json(rating);
});

app.get('/player/:playerId/history', (req, res) => {
  const { playerId } = req.params;
  const limit = parseInt(req.query.limit as string) || 20;
  const history = ratingManager.getPlayerGameHistory(playerId, limit);
  res.json(history);
});

// API для достижений
app.get('/achievements', (req, res) => {
  res.json(achievementManager.getAchievementsByCategory());
});

app.get('/player/:playerId/achievements', (req, res) => {
  const { playerId } = req.params;
  const achievements = achievementManager.getPlayerAchievements(playerId);
  res.json(achievements);
});

app.get('/achievements/rare', (req, res) => {
  res.json(achievementManager.getRareAchievements());
});

// API для турниров
app.get('/tournaments', (req, res) => {
  const status = req.query.status as string;
  let tournaments;

  if (status === 'active') {
    tournaments = tournamentManager.getActiveTournaments();
  } else {
    tournaments = tournamentManager.getAllTournaments();
  }

  res.json(tournaments);
});

app.get('/tournaments/:tournamentId', (req, res) => {
  const { tournamentId } = req.params;
  const tournament = tournamentManager.getTournament(tournamentId);

  if (!tournament) {
    return res.status(404).json({ error: 'Tournament not found' });
  }

  res.json(tournament);
});

app.get('/player/:playerId/tournaments', (req, res) => {
  const { playerId } = req.params;
  const tournaments = tournamentManager.getPlayerTournaments(playerId);
  res.json(tournaments);
});

// WebSocket соединения
io.on('connection', (socket) => {
  console.log(`Player connected: ${socket.id}`);

  // Регистрация игрока
  socket.on('register_player', (data: { name: string }) => {
    try {
      const player = playerManager.registerPlayer(socket.id, data.name, socket);
      socket.emit('player_registered', { 
        playerId: player.id, 
        name: player.name 
      });
      
      // Отправляем список доступных комнат
      socket.emit('rooms_list', roomManager.getPublicRooms());
      
      console.log(`Player registered: ${player.name} (${player.id})`);
    } catch (error) {
      socket.emit('error', { message: 'Failed to register player' });
    }
  });

  // Создание комнаты
  socket.on('create_room', (data: { name: string, maxPlayers?: number }) => {
    try {
      const player = playerManager.getPlayer(socket.id);
      if (!player) {
        socket.emit('error', { message: 'Player not registered' });
        return;
      }

      const room = roomManager.createRoom(data.name, player, data.maxPlayers);
      socket.join(room.id);
      
      socket.emit('room_created', {
        roomId: room.id,
        room: getRoomPublicInfo(room)
      });

      // Уведомляем всех о новой комнате
      socket.broadcast.emit('room_added', getRoomPublicInfo(room));
      
      console.log(`Room created: ${room.name} by ${player.name}`);
    } catch (error) {
      socket.emit('error', { message: 'Failed to create room' });
    }
  });

  // Присоединение к комнате
  socket.on('join_room', (data: { roomId: string }) => {
    try {
      const player = playerManager.getPlayer(socket.id);
      if (!player) {
        socket.emit('error', { message: 'Player not registered' });
        return;
      }

      const room = roomManager.joinRoom(data.roomId, player);
      socket.join(room.id);
      
      socket.emit('room_joined', {
        roomId: room.id,
        room: getRoomPublicInfo(room)
      });

      // Уведомляем других игроков в комнате
      socket.to(room.id).emit('player_joined', {
        player: getPlayerPublicInfo(player),
        room: getRoomPublicInfo(room)
      });
      
      console.log(`Player ${player.name} joined room ${room.name}`);
    } catch (error) {
      socket.emit('error', { message: error instanceof Error ? error.message : 'Failed to join room' });
    }
  });

  // Покидание комнаты
  socket.on('leave_room', (data: { roomId: string }) => {
    try {
      const player = playerManager.getPlayer(socket.id);
      if (!player) return;

      const room = roomManager.leaveRoom(data.roomId, player);
      socket.leave(data.roomId);
      
      socket.emit('room_left', { roomId: data.roomId });
      
      if (room) {
        // Уведомляем других игроков
        socket.to(room.id).emit('player_left', {
          player: getPlayerPublicInfo(player),
          room: getRoomPublicInfo(room)
        });
      }
      
      console.log(`Player ${player.name} left room`);
    } catch (error) {
      socket.emit('error', { message: 'Failed to leave room' });
    }
  });

  // Запуск игры
  socket.on('start_game', (data: { roomId: string }) => {
    try {
      const player = playerManager.getPlayer(socket.id);
      const room = roomManager.getRoom(data.roomId);
      
      if (!player || !room) {
        socket.emit('error', { message: 'Invalid player or room' });
        return;
      }

      if (!isRoomOwner(room, player)) {
        socket.emit('error', { message: 'Only room owner can start the game' });
        return;
      }

      const game = gameManager.startGame(room);
      
      // Уведомляем всех игроков в комнате
      io.to(room.id).emit('game_started', {
        gameId: game.id,
        gameState: getGamePublicState(game)
      });
      
      console.log(`Game started in room ${room.name}`);
    } catch (error) {
      socket.emit('error', { message: error instanceof Error ? error.message : 'Failed to start game' });
    }
  });

  // Ход в игре
  socket.on('game_move', (data: { roomId: string, action: string, cardIndex?: number }) => {
    try {
      const player = playerManager.getPlayer(socket.id);
      const room = roomManager.getRoom(data.roomId);
      
      if (!player || !room) {
        socket.emit('error', { message: 'Invalid player or room' });
        return;
      }

      const game = gameManager.getGameByRoom(room.id);
      if (!game) {
        socket.emit('error', { message: 'No active game in this room' });
        return;
      }

      const success = gameManager.makeMove(game.id, player.id, data.action, data.cardIndex);
      
      if (success) {
        const updatedGame = gameManager.getGame(game.id);
        const publicState = getGamePublicState(updatedGame!);

        // Отправляем обновленное состояние всем игрокам
        io.to(room.id).emit('game_updated', {
          gameState: publicState
        });

        // Проверяем, завершилась ли игра
        if (updatedGame!.status === 'finished' && updatedGame!.winner) {
          const winner = updatedGame!.winner;
          const loser = updatedGame!.players.find(p => p.id !== winner.id);

          if (loser) {
            // Обновляем рейтинги
            const gameDuration = Math.round((Date.now() - updatedGame!.startedAt.getTime()) / 1000);
            const moveCount = updatedGame!.moves.length;

            const { winnerRating, loserRating } = ratingManager.updateRatingsAfterGame(
              winner.id, winner.name,
              loser.id, loser.name,
              updatedGame!.id,
              gameDuration,
              moveCount
            );

            // Проверяем достижения
            const winnerHistory = ratingManager.getPlayerGameHistory(winner.id);
            const loserHistory = ratingManager.getPlayerGameHistory(loser.id);

            const winnerAchievements = achievementManager.checkAchievements(winner.id, winnerRating, winnerHistory);
            const loserAchievements = achievementManager.checkAchievements(loser.id, loserRating, loserHistory);

            // Отправляем обновления рейтингов и достижений
            io.to(room.id).emit('game_finished', {
              winner: {
                player: winner,
                rating: winnerRating,
                newAchievements: winnerAchievements
              },
              loser: {
                player: loser,
                rating: loserRating,
                newAchievements: loserAchievements
              },
              gameStats: {
                duration: gameDuration,
                moves: moveCount
              }
            });
          }
        }
      } else {
        socket.emit('error', { message: 'Invalid move' });
      }
      
    } catch (error) {
      socket.emit('error', { message: error instanceof Error ? error.message : 'Failed to make move' });
    }
  });

  // Чат
  socket.on('chat_message', (data: { roomId: string, message: string }) => {
    try {
      const player = playerManager.getPlayer(socket.id);
      if (!player) return;

      const chatMessage = chatManager.addMessage(data.roomId, player, data.message);
      
      // Отправляем сообщение всем в комнате
      io.to(data.roomId).emit('chat_message', chatMessage);
      
    } catch (error) {
      socket.emit('error', { message: 'Failed to send message' });
    }
  });

  // Запрос списка комнат
  socket.on('get_rooms', () => {
    socket.emit('rooms_list', roomManager.getPublicRooms());
  });

  // Запрос рейтинга игрока
  socket.on('get_player_rating', () => {
    const player = playerManager.getPlayer(socket.id);
    if (!player) return;

    const rating = ratingManager.getPlayerStats(player.id);
    const category = rating ? ratingManager.getRatingCategory(rating.rating) : null;

    socket.emit('player_rating', {
      rating,
      category
    });
  });

  // Запрос достижений игрока
  socket.on('get_player_achievements', () => {
    const player = playerManager.getPlayer(socket.id);
    if (!player) return;

    const achievements = achievementManager.getPlayerAchievements(player.id);
    socket.emit('player_achievements', achievements);
  });

  // Запрос таблицы лидеров
  socket.on('get_leaderboard', (data: { limit?: number } = {}) => {
    const limit = data.limit || 50;
    const leaderboard = ratingManager.getLeaderboard(limit);
    socket.emit('leaderboard', leaderboard);
  });

  // Запрос топ игроков
  socket.on('get_top_players', () => {
    const topPlayers = ratingManager.getTopPlayers();
    socket.emit('top_players', topPlayers);
  });

  // Турниры
  socket.on('create_tournament', (data: {
    name: string;
    description: string;
    type: 'single_elimination' | 'double_elimination' | 'round_robin';
    maxParticipants: number;
    settings?: any;
  }) => {
    try {
      const player = playerManager.getPlayer(socket.id);
      if (!player) {
        socket.emit('error', { message: 'Player not registered' });
        return;
      }

      const tournament = tournamentManager.createTournament(
        data.name,
        data.description,
        data.type,
        data.maxParticipants,
        player.id,
        data.settings
      );

      socket.emit('tournament_created', tournament);
      socket.broadcast.emit('tournament_added', tournament);

      console.log(`Tournament created: ${tournament.name} by ${player.name}`);
    } catch (error) {
      socket.emit('error', {
        message: error instanceof Error ? error.message : 'Failed to create tournament'
      });
    }
  });

  socket.on('register_for_tournament', (data: { tournamentId: string }) => {
    try {
      const player = playerManager.getPlayer(socket.id);
      if (!player) {
        socket.emit('error', { message: 'Player not registered' });
        return;
      }

      const participant = tournamentManager.registerPlayer(data.tournamentId, player);
      const tournament = tournamentManager.getTournament(data.tournamentId);

      socket.emit('tournament_registered', { tournament, participant });
      socket.broadcast.emit('tournament_updated', tournament);

      console.log(`Player ${player.name} registered for tournament ${tournament?.name}`);
    } catch (error) {
      socket.emit('error', {
        message: error instanceof Error ? error.message : 'Failed to register for tournament'
      });
    }
  });

  socket.on('get_tournaments', (data: { status?: string } = {}) => {
    let tournaments;

    if (data.status === 'active') {
      tournaments = tournamentManager.getActiveTournaments();
    } else {
      tournaments = tournamentManager.getAllTournaments();
    }

    socket.emit('tournaments_list', tournaments);
  });

  socket.on('get_tournament', (data: { tournamentId: string }) => {
    const tournament = tournamentManager.getTournament(data.tournamentId);
    if (tournament) {
      socket.emit('tournament_details', tournament);
    } else {
      socket.emit('error', { message: 'Tournament not found' });
    }
  });

  // Отключение
  socket.on('disconnect', () => {
    try {
      const player = playerManager.getPlayer(socket.id);
      if (player) {
        // Покидаем все комнаты
        const rooms = roomManager.getPlayerRooms(player);
        rooms.forEach(room => {
          roomManager.leaveRoom(room.id, player);
          socket.to(room.id).emit('player_left', {
            player: getPlayerPublicInfo(player),
            room: getRoomPublicInfo(room)
          });
        });
        
        playerManager.unregisterPlayer(socket.id);
        console.log(`Player disconnected: ${player.name} (${player.id})`);
      }
    } catch (error) {
      console.error('Error handling disconnect:', error);
    }
  });
});

const PORT = process.env.PORT || 3002;

server.listen(PORT, () => {
  console.log(`🎮 Game Server running on port ${PORT}`);
  console.log(`📡 WebSocket endpoint: ws://localhost:${PORT}`);
  console.log(`🌐 HTTP endpoint: http://localhost:${PORT}`);
});

export { io, gameManager, roomManager, chatManager, playerManager, ratingManager, achievementManager, tournamentManager };
