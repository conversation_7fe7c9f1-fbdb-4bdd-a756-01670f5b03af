export interface EsportsEvent {
  id: string;
  name: string;
  description: string;
  type: 'tournament' | 'league' | 'championship' | 'exhibition';
  gameMode: 'durak' | 'poker' | 'preferans' | 'mixed';
  format: 'single_elimination' | 'double_elimination' | 'round_robin' | 'swiss';
  prizePool: {
    total: number;
    currency: 'coins' | 'gems' | 'real_money';
    distribution: { place: number; amount: number; percentage: number }[];
  };
  schedule: {
    registrationStart: Date;
    registrationEnd: Date;
    eventStart: Date;
    eventEnd: Date;
  };
  requirements: {
    minRating: number;
    minLevel: number;
    maxParticipants: number;
    entryFee?: number;
    inviteOnly?: boolean;
    regionRestriction?: string[];
  };
  participants: EsportsParticipant[];
  matches: EsportsMatch[];
  brackets: EsportsBracket[];
  status: 'upcoming' | 'registration' | 'active' | 'completed' | 'cancelled';
  streaming: StreamingInfo;
  sponsors: Sponsor[];
  officials: Official[];
  rules: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface EsportsParticipant {
  playerId: string;
  playerName: string;
  teamId?: string;
  teamName?: string;
  rating: number;
  seed: number;
  registeredAt: Date;
  status: 'registered' | 'confirmed' | 'disqualified' | 'eliminated' | 'active';
  stats: ParticipantStats;
}

export interface ParticipantStats {
  gamesPlayed: number;
  gamesWon: number;
  gamesLost: number;
  winRate: number;
  averageGameDuration: number;
  totalPrizeWon: number;
  achievements: string[];
}

export interface EsportsMatch {
  id: string;
  eventId: string;
  round: number;
  matchNumber: number;
  participant1: string;
  participant2: string;
  winner?: string;
  score: { player1: number; player2: number };
  games: GameResult[];
  scheduledTime: Date;
  startTime?: Date;
  endTime?: Date;
  status: 'scheduled' | 'live' | 'completed' | 'postponed' | 'cancelled';
  streaming: MatchStreaming;
  officials: string[];
  chatLog: ChatMessage[];
}

export interface GameResult {
  gameId: string;
  winner: string;
  duration: number;
  moves: number;
  endReason: 'normal' | 'timeout' | 'forfeit' | 'disqualification';
  replay?: string;
}

export interface EsportsBracket {
  id: string;
  eventId: string;
  type: 'winners' | 'losers' | 'finals';
  rounds: BracketRound[];
  participants: string[];
}

export interface BracketRound {
  roundNumber: number;
  matches: string[];
  isCompleted: boolean;
}

export interface StreamingInfo {
  isLive: boolean;
  streamUrl?: string;
  streamKey?: string;
  platform: 'twitch' | 'youtube' | 'internal' | 'multiple';
  viewers: number;
  maxViewers: number;
  streamers: Streamer[];
  chatEnabled: boolean;
  recordingEnabled: boolean;
  highlights: StreamHighlight[];
}

export interface Streamer {
  id: string;
  name: string;
  platform: string;
  channelUrl: string;
  isOfficial: boolean;
  viewerCount: number;
  language: string;
}

export interface StreamHighlight {
  id: string;
  title: string;
  timestamp: Date;
  duration: number;
  clipUrl?: string;
  description: string;
  tags: string[];
}

export interface MatchStreaming {
  isStreamed: boolean;
  streamers: string[];
  viewers: number;
  chatMessages: number;
  highlights: string[];
}

export interface Sponsor {
  id: string;
  name: string;
  logo: string;
  website: string;
  tier: 'title' | 'presenting' | 'official' | 'supporting';
  contribution: number;
}

export interface Official {
  id: string;
  name: string;
  role: 'admin' | 'referee' | 'observer' | 'commentator';
  permissions: string[];
  assignedMatches: string[];
}

export interface ChatMessage {
  id: string;
  userId: string;
  username: string;
  message: string;
  timestamp: Date;
  type: 'chat' | 'system' | 'highlight';
  moderated: boolean;
}

export interface EsportsLeague {
  id: string;
  name: string;
  season: number;
  divisions: Division[];
  schedule: LeagueSchedule;
  standings: LeagueStandings[];
  playoffs: EsportsEvent;
  status: 'upcoming' | 'active' | 'playoffs' | 'completed';
}

export interface Division {
  id: string;
  name: string;
  tier: number;
  teams: string[];
  promotionSpots: number;
  relegationSpots: number;
}

export interface LeagueSchedule {
  regularSeason: {
    start: Date;
    end: Date;
    matchesPerWeek: number;
  };
  playoffs: {
    start: Date;
    end: Date;
    format: string;
  };
}

export interface LeagueStandings {
  teamId: string;
  teamName: string;
  division: string;
  gamesPlayed: number;
  wins: number;
  losses: number;
  winRate: number;
  points: number;
  rank: number;
}

export class EsportsManager {
  private events: Map<string, EsportsEvent> = new Map();
  private leagues: Map<string, EsportsLeague> = new Map();
  private participants: Map<string, EsportsParticipant> = new Map();
  private streamingData: Map<string, StreamingInfo> = new Map();

  /**
   * Создает киберспортивное событие
   */
  createEvent(
    name: string,
    description: string,
    type: EsportsEvent['type'],
    gameMode: EsportsEvent['gameMode'],
    format: EsportsEvent['format'],
    prizePool: EsportsEvent['prizePool'],
    schedule: EsportsEvent['schedule'],
    requirements: EsportsEvent['requirements']
  ): EsportsEvent {
    const eventId = this.generateEventId();
    
    const event: EsportsEvent = {
      id: eventId,
      name,
      description,
      type,
      gameMode,
      format,
      prizePool,
      schedule,
      requirements,
      participants: [],
      matches: [],
      brackets: [],
      status: 'upcoming',
      streaming: {
        isLive: false,
        platform: 'internal',
        viewers: 0,
        maxViewers: 0,
        streamers: [],
        chatEnabled: true,
        recordingEnabled: true,
        highlights: []
      },
      sponsors: [],
      officials: [],
      rules: this.getDefaultRules(gameMode),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.events.set(eventId, event);
    return event;
  }

  /**
   * Регистрирует участника в событии
   */
  registerParticipant(
    eventId: string,
    playerId: string,
    playerName: string,
    rating: number,
    teamId?: string,
    teamName?: string
  ): boolean {
    const event = this.events.get(eventId);
    if (!event) return false;

    // Проверяем статус события
    if (event.status !== 'registration') return false;

    // Проверяем требования
    if (rating < event.requirements.minRating) return false;
    if (event.participants.length >= event.requirements.maxParticipants) return false;

    // Проверяем, не зарегистрирован ли уже
    if (event.participants.some(p => p.playerId === playerId)) return false;

    const participant: EsportsParticipant = {
      playerId,
      playerName,
      teamId,
      teamName,
      rating,
      seed: event.participants.length + 1,
      registeredAt: new Date(),
      status: 'registered',
      stats: {
        gamesPlayed: 0,
        gamesWon: 0,
        gamesLost: 0,
        winRate: 0,
        averageGameDuration: 0,
        totalPrizeWon: 0,
        achievements: []
      }
    };

    event.participants.push(participant);
    this.participants.set(playerId, participant);
    event.updatedAt = new Date();

    return true;
  }

  /**
   * Начинает событие
   */
  startEvent(eventId: string): boolean {
    const event = this.events.get(eventId);
    if (!event || event.status !== 'registration') return false;

    if (event.participants.length < 2) return false;

    // Генерируем сетку
    this.generateBrackets(event);
    
    // Создаем первые матчи
    this.createInitialMatches(event);

    event.status = 'active';
    event.updatedAt = new Date();

    // Начинаем стриминг
    this.startEventStreaming(event);

    return true;
  }

  /**
   * Создает матч
   */
  createMatch(
    eventId: string,
    round: number,
    participant1: string,
    participant2: string,
    scheduledTime: Date
  ): EsportsMatch {
    const matchId = this.generateMatchId();
    
    const match: EsportsMatch = {
      id: matchId,
      eventId,
      round,
      matchNumber: 0,
      participant1,
      participant2,
      score: { player1: 0, player2: 0 },
      games: [],
      scheduledTime,
      status: 'scheduled',
      streaming: {
        isStreamed: true,
        streamers: [],
        viewers: 0,
        chatMessages: 0,
        highlights: []
      },
      officials: [],
      chatLog: []
    };

    const event = this.events.get(eventId);
    if (event) {
      event.matches.push(match);
      match.matchNumber = event.matches.length;
    }

    return match;
  }

  /**
   * Завершает матч
   */
  completeMatch(matchId: string, winner: string, games: GameResult[]): boolean {
    const match = this.findMatch(matchId);
    if (!match || match.status !== 'live') return false;

    match.winner = winner;
    match.games = games;
    match.endTime = new Date();
    match.status = 'completed';

    // Обновляем счет
    const player1Wins = games.filter(g => g.winner === match.participant1).length;
    const player2Wins = games.filter(g => g.winner === match.participant2).length;
    
    match.score = { player1: player1Wins, player2: player2Wins };

    // Обновляем статистику участников
    this.updateParticipantStats(match.participant1, games);
    this.updateParticipantStats(match.participant2, games);

    // Продвигаем в следующий раунд
    this.advanceWinner(match);

    return true;
  }

  /**
   * Начинает стриминг события
   */
  startEventStreaming(event: EsportsEvent): void {
    event.streaming.isLive = true;
    event.streaming.viewers = 0;
    
    // Создаем основной стрим
    const mainStreamer: Streamer = {
      id: 'official_stream',
      name: 'Официальный стрим',
      platform: 'internal',
      channelUrl: `/stream/${event.id}`,
      isOfficial: true,
      viewerCount: 0,
      language: 'ru'
    };

    event.streaming.streamers.push(mainStreamer);
    this.streamingData.set(event.id, event.streaming);
  }

  /**
   * Добавляет стримера к событию
   */
  addStreamer(eventId: string, streamer: Streamer): boolean {
    const event = this.events.get(eventId);
    if (!event) return false;

    event.streaming.streamers.push(streamer);
    return true;
  }

  /**
   * Обновляет количество зрителей
   */
  updateViewerCount(eventId: string, streamerId: string, viewers: number): void {
    const event = this.events.get(eventId);
    if (!event) return;

    const streamer = event.streaming.streamers.find(s => s.id === streamerId);
    if (streamer) {
      streamer.viewerCount = viewers;
      
      // Обновляем общее количество зрителей
      event.streaming.viewers = event.streaming.streamers.reduce((sum, s) => sum + s.viewerCount, 0);
      
      if (event.streaming.viewers > event.streaming.maxViewers) {
        event.streaming.maxViewers = event.streaming.viewers;
      }
    }
  }

  /**
   * Создает хайлайт
   */
  createHighlight(
    eventId: string,
    title: string,
    description: string,
    duration: number,
    tags: string[]
  ): StreamHighlight {
    const highlight: StreamHighlight = {
      id: this.generateHighlightId(),
      title,
      timestamp: new Date(),
      duration,
      description,
      tags
    };

    const event = this.events.get(eventId);
    if (event) {
      event.streaming.highlights.push(highlight);
    }

    return highlight;
  }

  /**
   * Добавляет сообщение в чат матча
   */
  addChatMessage(matchId: string, userId: string, username: string, message: string): void {
    const match = this.findMatch(matchId);
    if (!match) return;

    const chatMessage: ChatMessage = {
      id: this.generateChatMessageId(),
      userId,
      username,
      message,
      timestamp: new Date(),
      type: 'chat',
      moderated: false
    };

    match.chatLog.push(chatMessage);
    match.streaming.chatMessages++;
  }

  /**
   * Получает активные события
   */
  getActiveEvents(): EsportsEvent[] {
    return Array.from(this.events.values())
      .filter(event => event.status === 'active' || event.status === 'registration')
      .sort((a, b) => a.schedule.eventStart.getTime() - b.schedule.eventStart.getTime());
  }

  /**
   * Получает статистику киберспорта
   */
  getEsportsStats() {
    const totalEvents = this.events.size;
    const activeEvents = Array.from(this.events.values()).filter(e => e.status === 'active').length;
    const totalParticipants = Array.from(this.events.values())
      .reduce((sum, event) => sum + event.participants.length, 0);
    
    const totalPrizePool = Array.from(this.events.values())
      .reduce((sum, event) => sum + event.prizePool.total, 0);

    const totalViewers = Array.from(this.events.values())
      .reduce((sum, event) => sum + event.streaming.viewers, 0);

    const eventsByGame = Array.from(this.events.values()).reduce((acc, event) => {
      acc[event.gameMode] = (acc[event.gameMode] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalEvents,
      activeEvents,
      totalParticipants,
      totalPrizePool,
      totalViewers,
      eventsByGame
    };
  }

  // Приватные методы
  private generateBrackets(event: EsportsEvent): void {
    // Генерируем сетку в зависимости от формата
    switch (event.format) {
      case 'single_elimination':
        this.generateSingleEliminationBracket(event);
        break;
      case 'double_elimination':
        this.generateDoubleEliminationBracket(event);
        break;
      case 'round_robin':
        this.generateRoundRobinBracket(event);
        break;
    }
  }

  private generateSingleEliminationBracket(event: EsportsEvent): void {
    const participants = [...event.participants];
    
    // Сортируем по рейтингу для сидинга
    participants.sort((a, b) => b.rating - a.rating);
    
    const bracket: EsportsBracket = {
      id: this.generateBracketId(),
      eventId: event.id,
      type: 'winners',
      rounds: [],
      participants: participants.map(p => p.playerId)
    };

    // Вычисляем количество раундов
    const rounds = Math.ceil(Math.log2(participants.length));
    
    for (let i = 0; i < rounds; i++) {
      bracket.rounds.push({
        roundNumber: i + 1,
        matches: [],
        isCompleted: false
      });
    }

    event.brackets.push(bracket);
  }

  private generateDoubleEliminationBracket(event: EsportsEvent): void {
    // Создаем winners и losers bracket
    this.generateSingleEliminationBracket(event);
    
    const losersBracket: EsportsBracket = {
      id: this.generateBracketId(),
      eventId: event.id,
      type: 'losers',
      rounds: [],
      participants: []
    };

    event.brackets.push(losersBracket);
  }

  private generateRoundRobinBracket(event: EsportsEvent): void {
    // Каждый играет с каждым
    const participants = event.participants;
    
    for (let i = 0; i < participants.length; i++) {
      for (let j = i + 1; j < participants.length; j++) {
        // Создаем матч между участниками i и j
      }
    }
  }

  private createInitialMatches(event: EsportsEvent): void {
    const bracket = event.brackets[0];
    if (!bracket) return;

    const participants = event.participants;
    const firstRound = bracket.rounds[0];
    
    // Создаем матчи первого раунда
    for (let i = 0; i < participants.length; i += 2) {
      if (i + 1 < participants.length) {
        const match = this.createMatch(
          event.id,
          1,
          participants[i].playerId,
          participants[i + 1].playerId,
          new Date(Date.now() + i * 60 * 60 * 1000) // Каждый час
        );
        
        firstRound.matches.push(match.id);
      }
    }
  }

  private updateParticipantStats(participantId: string, games: GameResult[]): void {
    const participant = this.participants.get(participantId);
    if (!participant) return;

    const wins = games.filter(g => g.winner === participantId).length;
    const losses = games.length - wins;

    participant.stats.gamesPlayed += games.length;
    participant.stats.gamesWon += wins;
    participant.stats.gamesLost += losses;
    participant.stats.winRate = (participant.stats.gamesWon / participant.stats.gamesPlayed) * 100;
    
    const totalDuration = games.reduce((sum, g) => sum + g.duration, 0);
    participant.stats.averageGameDuration = totalDuration / games.length;
  }

  private advanceWinner(match: EsportsMatch): void {
    // Логика продвижения победителя в следующий раунд
    // Зависит от формата турнира
  }

  private findMatch(matchId: string): EsportsMatch | undefined {
    for (const event of this.events.values()) {
      const match = event.matches.find(m => m.id === matchId);
      if (match) return match;
    }
    return undefined;
  }

  private getDefaultRules(gameMode: string): string[] {
    const commonRules = [
      'Запрещено использование внешних программ',
      'Неспортивное поведение наказывается дисквалификацией',
      'Решения судей окончательны',
      'Участники должны быть готовы к игре в назначенное время'
    ];

    switch (gameMode) {
      case 'durak':
        return [...commonRules, 'Игра до полной победы', 'Время на ход: 60 секунд'];
      case 'poker':
        return [...commonRules, 'Стартовый стек: 1000 фишек', 'Блайнды увеличиваются каждые 10 минут'];
      case 'preferans':
        return [...commonRules, 'Игра до 10 пуль', 'Время на торговлю: 30 секунд'];
      default:
        return commonRules;
    }
  }

  // Генераторы ID
  private generateEventId(): string {
    return 'event_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generateMatchId(): string {
    return 'match_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generateBracketId(): string {
    return 'bracket_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generateHighlightId(): string {
    return 'highlight_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generateChatMessageId(): string {
    return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}
