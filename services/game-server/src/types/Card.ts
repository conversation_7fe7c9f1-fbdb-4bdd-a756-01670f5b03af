export type Suit = 'hearts' | 'diamonds' | 'clubs' | 'spades';
export type Rank = '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | 'J' | 'Q' | 'K' | 'A';

export interface Card {
  suit: Suit;
  rank: Rank;
}

export class CardUtils {
  /**
   * Создает стандартную колоду из 52 карт
   */
  static createDeck(): Card[] {
    const deck: Card[] = [];
    const suits: Suit[] = ['hearts', 'diamonds', 'clubs', 'spades'];
    const ranks: Rank[] = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];

    for (const suit of suits) {
      for (const rank of ranks) {
        deck.push({ suit, rank });
      }
    }

    return deck;
  }

  /**
   * Перемешивает колоду
   */
  static shuffleDeck(deck: Card[]): Card[] {
    const shuffled = [...deck];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  /**
   * Получает числовое значение карты
   */
  static getCardValue(rank: Rank): number {
    switch (rank) {
      case '2': return 2;
      case '3': return 3;
      case '4': return 4;
      case '5': return 5;
      case '6': return 6;
      case '7': return 7;
      case '8': return 8;
      case '9': return 9;
      case '10': return 10;
      case 'J': return 11;
      case 'Q': return 12;
      case 'K': return 13;
      case 'A': return 14;
      default: return 0;
    }
  }

  /**
   * Получает цвет масти
   */
  static getSuitColor(suit: Suit): 'red' | 'black' {
    return suit === 'hearts' || suit === 'diamonds' ? 'red' : 'black';
  }

  /**
   * Получает символ масти
   */
  static getSuitSymbol(suit: Suit): string {
    switch (suit) {
      case 'hearts': return '♥';
      case 'diamonds': return '♦';
      case 'clubs': return '♣';
      case 'spades': return '♠';
      default: return '';
    }
  }

  /**
   * Получает эмодзи масти
   */
  static getSuitEmoji(suit: Suit): string {
    switch (suit) {
      case 'hearts': return '♥️';
      case 'diamonds': return '♦️';
      case 'clubs': return '♣️';
      case 'spades': return '♠️';
      default: return '';
    }
  }

  /**
   * Форматирует карту для отображения
   */
  static formatCard(card: Card): string {
    return `${card.rank}${CardUtils.getSuitSymbol(card.suit)}`;
  }

  /**
   * Сравнивает карты по рангу
   */
  static compareCards(a: Card, b: Card): number {
    return CardUtils.getCardValue(a.rank) - CardUtils.getCardValue(b.rank);
  }

  /**
   * Проверяет, является ли карта тузом
   */
  static isAce(card: Card): boolean {
    return card.rank === 'A';
  }

  /**
   * Проверяет, является ли карта фигурой
   */
  static isFaceCard(card: Card): boolean {
    return card.rank === 'J' || card.rank === 'Q' || card.rank === 'K';
  }

  /**
   * Проверяет, одинаковые ли масти у карт
   */
  static sameSuit(cards: Card[]): boolean {
    if (cards.length === 0) return true;
    const firstSuit = cards[0].suit;
    return cards.every(card => card.suit === firstSuit);
  }

  /**
   * Проверяет, одинаковые ли ранги у карт
   */
  static sameRank(cards: Card[]): boolean {
    if (cards.length === 0) return true;
    const firstRank = cards[0].rank;
    return cards.every(card => card.rank === firstRank);
  }

  /**
   * Группирует карты по рангу
   */
  static groupByRank(cards: Card[]): Map<Rank, Card[]> {
    const groups = new Map<Rank, Card[]>();
    
    for (const card of cards) {
      if (!groups.has(card.rank)) {
        groups.set(card.rank, []);
      }
      groups.get(card.rank)!.push(card);
    }
    
    return groups;
  }

  /**
   * Группирует карты по масти
   */
  static groupBySuit(cards: Card[]): Map<Suit, Card[]> {
    const groups = new Map<Suit, Card[]>();
    
    for (const card of cards) {
      if (!groups.has(card.suit)) {
        groups.set(card.suit, []);
      }
      groups.get(card.suit)!.push(card);
    }
    
    return groups;
  }

  /**
   * Сортирует карты по рангу (по убыванию)
   */
  static sortByRank(cards: Card[], ascending: boolean = false): Card[] {
    return [...cards].sort((a, b) => {
      const diff = CardUtils.getCardValue(a.rank) - CardUtils.getCardValue(b.rank);
      return ascending ? diff : -diff;
    });
  }

  /**
   * Сортирует карты по масти
   */
  static sortBySuit(cards: Card[]): Card[] {
    const suitOrder: Suit[] = ['spades', 'hearts', 'diamonds', 'clubs'];
    return [...cards].sort((a, b) => {
      const aSuitIndex = suitOrder.indexOf(a.suit);
      const bSuitIndex = suitOrder.indexOf(b.suit);
      if (aSuitIndex !== bSuitIndex) {
        return aSuitIndex - bSuitIndex;
      }
      return CardUtils.getCardValue(a.rank) - CardUtils.getCardValue(b.rank);
    });
  }

  /**
   * Проверяет, является ли последовательность стритом
   */
  static isStraight(cards: Card[]): boolean {
    if (cards.length < 5) return false;
    
    const sorted = CardUtils.sortByRank(cards, true);
    const values = sorted.map(card => CardUtils.getCardValue(card.rank));
    const uniqueValues = [...new Set(values)];
    
    if (uniqueValues.length !== 5) return false;
    
    // Проверяем обычный стрит
    for (let i = 0; i < 4; i++) {
      if (uniqueValues[i + 1] - uniqueValues[i] !== 1) {
        // Проверяем A-2-3-4-5 стрит
        if (uniqueValues[0] === 2 && uniqueValues[1] === 3 && 
            uniqueValues[2] === 4 && uniqueValues[3] === 5 && 
            uniqueValues[4] === 14) {
          return true;
        }
        return false;
      }
    }
    
    return true;
  }

  /**
   * Проверяет, является ли набор карт флешем
   */
  static isFlush(cards: Card[]): boolean {
    return cards.length >= 5 && CardUtils.sameSuit(cards);
  }

  /**
   * Создает копию карты
   */
  static cloneCard(card: Card): Card {
    return { suit: card.suit, rank: card.rank };
  }

  /**
   * Создает копию массива карт
   */
  static cloneCards(cards: Card[]): Card[] {
    return cards.map(card => CardUtils.cloneCard(card));
  }
}
