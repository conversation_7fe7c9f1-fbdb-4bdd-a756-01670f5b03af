export interface SeasonalEvent {
  id: string;
  name: string;
  description: string;
  type: 'tournament' | 'challenge' | 'special_game' | 'collection' | 'leaderboard';
  theme: 'new_year' | 'valentine' | 'easter' | 'summer' | 'halloween' | 'christmas' | 'anniversary' | 'special';
  startDate: Date;
  endDate: Date;
  status: 'upcoming' | 'active' | 'completed' | 'cancelled';
  requirements: {
    minLevel: number;
    minRating: number;
    maxParticipants?: number;
    clanMembersOnly?: boolean;
  };
  rewards: SeasonalReward[];
  leaderboard: SeasonalLeaderboardEntry[];
  participants: string[];
  progress: Map<string, SeasonalProgress>; // playerId -> progress
  rules: {
    gameMode: string;
    specialRules?: string[];
    pointsSystem: PointsRule[];
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface SeasonalReward {
  id: string;
  name: string;
  description: string;
  type: 'title' | 'achievement' | 'experience' | 'currency' | 'cosmetic' | 'special';
  rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
  icon: string;
  value: number;
  isLimited: boolean;
  requirement: {
    type: 'rank' | 'points' | 'participation' | 'completion';
    value: number;
  };
}

export interface SeasonalLeaderboardEntry {
  playerId: string;
  playerName: string;
  clanTag?: string;
  points: number;
  rank: number;
  gamesPlayed: number;
  lastUpdated: Date;
}

export interface SeasonalProgress {
  playerId: string;
  eventId: string;
  points: number;
  gamesPlayed: number;
  achievements: string[];
  milestones: SeasonalMilestone[];
  joinedAt: Date;
  lastActiveAt: Date;
}

export interface SeasonalMilestone {
  id: string;
  name: string;
  description: string;
  requirement: number;
  reward: SeasonalReward;
  isCompleted: boolean;
  completedAt?: Date;
}

export interface PointsRule {
  action: 'win' | 'lose' | 'play' | 'streak' | 'special';
  points: number;
  multiplier?: number;
  condition?: string;
}

export interface Season {
  id: string;
  name: string;
  number: number;
  startDate: Date;
  endDate: Date;
  theme: string;
  description: string;
  events: string[];
  rewards: SeasonalReward[];
  leaderboard: SeasonalLeaderboardEntry[];
  status: 'upcoming' | 'active' | 'completed';
}

export class SeasonalEventManager {
  private events: Map<string, SeasonalEvent> = new Map();
  private seasons: Map<string, Season> = new Map();
  private currentSeason: Season | null = null;

  constructor() {
    this.initializeDefaultEvents();
  }

  /**
   * Создает новое сезонное событие
   */
  createEvent(
    name: string,
    description: string,
    type: SeasonalEvent['type'],
    theme: SeasonalEvent['theme'],
    startDate: Date,
    endDate: Date,
    requirements: SeasonalEvent['requirements'],
    rewards: SeasonalReward[],
    rules: SeasonalEvent['rules']
  ): SeasonalEvent {
    const eventId = this.generateEventId();
    
    const event: SeasonalEvent = {
      id: eventId,
      name,
      description,
      type,
      theme,
      startDate,
      endDate,
      status: 'upcoming',
      requirements,
      rewards,
      leaderboard: [],
      participants: [],
      progress: new Map(),
      rules,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.events.set(eventId, event);
    return event;
  }

  /**
   * Регистрирует игрока на событие
   */
  registerPlayer(eventId: string, playerId: string, playerName: string, playerLevel: number, playerRating: number): boolean {
    const event = this.events.get(eventId);
    if (!event) {
      throw new Error('Event not found');
    }

    if (event.status !== 'active' && event.status !== 'upcoming') {
      throw new Error('Event registration is closed');
    }

    // Проверяем требования
    if (playerLevel < event.requirements.minLevel) {
      throw new Error('Player level too low');
    }

    if (playerRating < event.requirements.minRating) {
      throw new Error('Player rating too low');
    }

    if (event.requirements.maxParticipants && event.participants.length >= event.requirements.maxParticipants) {
      throw new Error('Event is full');
    }

    if (event.participants.includes(playerId)) {
      throw new Error('Player already registered');
    }

    // Регистрируем игрока
    event.participants.push(playerId);
    
    const progress: SeasonalProgress = {
      playerId,
      eventId,
      points: 0,
      gamesPlayed: 0,
      achievements: [],
      milestones: this.generateMilestones(event),
      joinedAt: new Date(),
      lastActiveAt: new Date()
    };

    event.progress.set(playerId, progress);
    event.updatedAt = new Date();

    return true;
  }

  /**
   * Обновляет прогресс игрока в событии
   */
  updatePlayerProgress(
    eventId: string, 
    playerId: string, 
    action: 'win' | 'lose' | 'play' | 'streak' | 'special',
    gameData?: any
  ): SeasonalProgress | null {
    const event = this.events.get(eventId);
    if (!event || event.status !== 'active') {
      return null;
    }

    const progress = event.progress.get(playerId);
    if (!progress) {
      return null;
    }

    // Вычисляем очки по правилам события
    let pointsEarned = 0;
    for (const rule of event.rules.pointsSystem) {
      if (rule.action === action) {
        pointsEarned += rule.points;
        if (rule.multiplier && gameData?.multiplier) {
          pointsEarned *= rule.multiplier;
        }
      }
    }

    progress.points += pointsEarned;
    progress.gamesPlayed++;
    progress.lastActiveAt = new Date();

    // Проверяем достижение вех
    this.checkMilestones(progress);

    // Обновляем лидерборд
    this.updateLeaderboard(event, playerId, progress);

    event.updatedAt = new Date();
    return progress;
  }

  /**
   * Получает активные события
   */
  getActiveEvents(): SeasonalEvent[] {
    const now = new Date();
    return Array.from(this.events.values())
      .filter(event => event.status === 'active' || 
        (event.status === 'upcoming' && event.startDate <= now))
      .sort((a, b) => a.startDate.getTime() - b.startDate.getTime());
  }

  /**
   * Получает события по теме
   */
  getEventsByTheme(theme: SeasonalEvent['theme']): SeasonalEvent[] {
    return Array.from(this.events.values())
      .filter(event => event.theme === theme)
      .sort((a, b) => b.startDate.getTime() - a.startDate.getTime());
  }

  /**
   * Получает прогресс игрока в событии
   */
  getPlayerProgress(eventId: string, playerId: string): SeasonalProgress | null {
    const event = this.events.get(eventId);
    return event ? event.progress.get(playerId) || null : null;
  }

  /**
   * Получает лидерборд события
   */
  getEventLeaderboard(eventId: string, limit: number = 100): SeasonalLeaderboardEntry[] {
    const event = this.events.get(eventId);
    if (!event) {
      return [];
    }

    return event.leaderboard
      .sort((a, b) => b.points - a.points)
      .slice(0, limit);
  }

  /**
   * Создает новый сезон
   */
  createSeason(
    name: string,
    number: number,
    startDate: Date,
    endDate: Date,
    theme: string,
    description: string
  ): Season {
    const seasonId = this.generateSeasonId();
    
    const season: Season = {
      id: seasonId,
      name,
      number,
      startDate,
      endDate,
      theme,
      description,
      events: [],
      rewards: this.generateSeasonRewards(number),
      leaderboard: [],
      status: 'upcoming'
    };

    this.seasons.set(seasonId, season);
    
    // Если это первый сезон или текущий сезон завершился
    if (!this.currentSeason || this.currentSeason.status === 'completed') {
      this.currentSeason = season;
    }

    return season;
  }

  /**
   * Получает текущий сезон
   */
  getCurrentSeason(): Season | null {
    return this.currentSeason;
  }

  /**
   * Получает статистику событий
   */
  getStats() {
    const totalEvents = this.events.size;
    const activeEvents = Array.from(this.events.values()).filter(e => e.status === 'active').length;
    const totalParticipants = Array.from(this.events.values())
      .reduce((sum, event) => sum + event.participants.length, 0);
    
    const eventsByTheme = Array.from(this.events.values()).reduce((acc, event) => {
      acc[event.theme] = (acc[event.theme] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalEvents,
      activeEvents,
      totalParticipants,
      eventsByTheme,
      currentSeason: this.currentSeason?.name || 'None'
    };
  }

  /**
   * Обновляет статус событий
   */
  updateEventStatuses(): void {
    const now = new Date();
    
    for (const event of this.events.values()) {
      if (event.status === 'upcoming' && event.startDate <= now) {
        event.status = 'active';
      } else if (event.status === 'active' && event.endDate <= now) {
        event.status = 'completed';
        this.finalizeEvent(event);
      }
    }

    // Обновляем статус сезонов
    for (const season of this.seasons.values()) {
      if (season.status === 'upcoming' && season.startDate <= now) {
        season.status = 'active';
        if (this.currentSeason?.status === 'completed') {
          this.currentSeason = season;
        }
      } else if (season.status === 'active' && season.endDate <= now) {
        season.status = 'completed';
      }
    }
  }

  // Приватные методы
  private initializeDefaultEvents(): void {
    // Создаем базовые сезонные события
    const now = new Date();
    const nextMonth = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

    // Новогодний турнир
    this.createEvent(
      'Новогодний турнир',
      'Специальный турнир в честь Нового года с уникальными наградами',
      'tournament',
      'new_year',
      new Date(now.getFullYear(), 11, 25), // 25 декабря
      new Date(now.getFullYear() + 1, 0, 10), // 10 января
      { minLevel: 5, minRating: 1000 },
      this.generateEventRewards('new_year'),
      {
        gameMode: 'durak',
        pointsSystem: [
          { action: 'win', points: 100 },
          { action: 'lose', points: 25 },
          { action: 'streak', points: 50, multiplier: 2 }
        ]
      }
    );

    // Летний челлендж
    this.createEvent(
      'Летний челлендж',
      'Испытание на выносливость в жаркие летние дни',
      'challenge',
      'summer',
      new Date(now.getFullYear(), 5, 1), // 1 июня
      new Date(now.getFullYear(), 7, 31), // 31 августа
      { minLevel: 1, minRating: 800 },
      this.generateEventRewards('summer'),
      {
        gameMode: 'any',
        pointsSystem: [
          { action: 'play', points: 10 },
          { action: 'win', points: 50 }
        ]
      }
    );
  }

  private generateEventRewards(theme: string): SeasonalReward[] {
    const rewards: SeasonalReward[] = [];
    
    switch (theme) {
      case 'new_year':
        rewards.push(
          {
            id: 'ny_title_champion',
            name: 'Новогодний чемпион',
            description: 'Титул победителя новогоднего турнира',
            type: 'title',
            rarity: 'legendary',
            icon: '🎄',
            value: 0,
            isLimited: true,
            requirement: { type: 'rank', value: 1 }
          },
          {
            id: 'ny_experience',
            name: 'Новогодний бонус',
            description: 'Дополнительный опыт за участие',
            type: 'experience',
            rarity: 'common',
            icon: '⭐',
            value: 1000,
            isLimited: false,
            requirement: { type: 'participation', value: 1 }
          }
        );
        break;
      
      case 'summer':
        rewards.push(
          {
            id: 'summer_title',
            name: 'Летний воин',
            description: 'Титул за активность летом',
            type: 'title',
            rarity: 'epic',
            icon: '☀️',
            value: 0,
            isLimited: true,
            requirement: { type: 'points', value: 5000 }
          }
        );
        break;
    }

    return rewards;
  }

  private generateSeasonRewards(seasonNumber: number): SeasonalReward[] {
    return [
      {
        id: `season_${seasonNumber}_title`,
        name: `Ветеран ${seasonNumber} сезона`,
        description: `Титул за участие в ${seasonNumber} сезоне`,
        type: 'title',
        rarity: 'rare',
        icon: '🏆',
        value: 0,
        isLimited: true,
        requirement: { type: 'participation', value: 1 }
      }
    ];
  }

  private generateMilestones(event: SeasonalEvent): SeasonalMilestone[] {
    return [
      {
        id: 'milestone_1',
        name: 'Первые шаги',
        description: 'Сыграйте 5 игр',
        requirement: 5,
        reward: {
          id: 'milestone_exp_1',
          name: 'Бонусный опыт',
          description: '100 опыта',
          type: 'experience',
          rarity: 'common',
          icon: '⭐',
          value: 100,
          isLimited: false,
          requirement: { type: 'completion', value: 1 }
        },
        isCompleted: false
      },
      {
        id: 'milestone_2',
        name: 'Активный участник',
        description: 'Наберите 1000 очков',
        requirement: 1000,
        reward: {
          id: 'milestone_title_1',
          name: 'Активист',
          description: 'Титул активного участника',
          type: 'title',
          rarity: 'rare',
          icon: '🎯',
          value: 0,
          isLimited: true,
          requirement: { type: 'completion', value: 1 }
        },
        isCompleted: false
      }
    ];
  }

  private checkMilestones(progress: SeasonalProgress): void {
    for (const milestone of progress.milestones) {
      if (!milestone.isCompleted) {
        let currentValue = 0;
        
        if (milestone.name.includes('игр')) {
          currentValue = progress.gamesPlayed;
        } else if (milestone.name.includes('очков')) {
          currentValue = progress.points;
        }
        
        if (currentValue >= milestone.requirement) {
          milestone.isCompleted = true;
          milestone.completedAt = new Date();
          progress.achievements.push(milestone.reward.id);
        }
      }
    }
  }

  private updateLeaderboard(event: SeasonalEvent, playerId: string, progress: SeasonalProgress): void {
    const existingEntry = event.leaderboard.find(entry => entry.playerId === playerId);
    
    if (existingEntry) {
      existingEntry.points = progress.points;
      existingEntry.gamesPlayed = progress.gamesPlayed;
      existingEntry.lastUpdated = new Date();
    } else {
      event.leaderboard.push({
        playerId,
        playerName: '', // Должно заполняться из PlayerManager
        points: progress.points,
        rank: 0,
        gamesPlayed: progress.gamesPlayed,
        lastUpdated: new Date()
      });
    }

    // Пересчитываем ранги
    event.leaderboard.sort((a, b) => b.points - a.points);
    event.leaderboard.forEach((entry, index) => {
      entry.rank = index + 1;
    });
  }

  private finalizeEvent(event: SeasonalEvent): void {
    // Выдаем награды по итогам события
    for (const reward of event.rewards) {
      const eligiblePlayers = event.leaderboard.filter(entry => {
        switch (reward.requirement.type) {
          case 'rank':
            return entry.rank <= reward.requirement.value;
          case 'points':
            return entry.points >= reward.requirement.value;
          case 'participation':
            return true;
          default:
            return false;
        }
      });

      // Здесь должна быть логика выдачи наград игрокам
      // eligiblePlayers.forEach(player => giveReward(player.playerId, reward));
    }
  }

  private generateEventId(): string {
    return 'event_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generateSeasonId(): string {
    return 'season_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}
