import { Socket } from 'socket.io';
import { v4 as uuidv4 } from 'uuid';

export interface Player {
  id: string;
  socketId: string;
  name: string;
  socket: Socket;
  joinedAt: Date;
  isOnline: boolean;
}

export class PlayerManager {
  private players: Map<string, Player> = new Map(); // socketId -> Player
  private playersByName: Map<string, Player> = new Map(); // name -> Player

  /**
   * Регистрирует нового игрока
   */
  registerPlayer(socketId: string, name: string, socket: Socket): Player {
    // Проверяем, не занято ли имя
    if (this.playersByName.has(name)) {
      throw new Error(`Player name "${name}" is already taken`);
    }

    // Создаем игрока
    const player: Player = {
      id: uuidv4(),
      socketId,
      name,
      socket,
      joinedAt: new Date(),
      isOnline: true
    };

    this.players.set(socketId, player);
    this.playersByName.set(name, player);

    return player;
  }

  /**
   * Отменяет регистрацию игрока
   */
  unregisterPlayer(socketId: string): boolean {
    const player = this.players.get(socketId);
    if (!player) return false;

    this.players.delete(socketId);
    this.playersByName.delete(player.name);

    return true;
  }

  /**
   * Получает игрока по socket ID
   */
  getPlayer(socketId: string): Player | undefined {
    return this.players.get(socketId);
  }

  /**
   * Получает игрока по имени
   */
  getPlayerByName(name: string): Player | undefined {
    return this.playersByName.get(name);
  }

  /**
   * Получает игрока по ID
   */
  getPlayerById(playerId: string): Player | undefined {
    for (const player of this.players.values()) {
      if (player.id === playerId) {
        return player;
      }
    }
    return undefined;
  }

  /**
   * Получает socket игрока по ID
   */
  getPlayerSocket(playerId: string): Socket | undefined {
    const player = this.getPlayerById(playerId);
    return player?.socket;
  }

  /**
   * Получает всех игроков
   */
  getAllPlayers(): Player[] {
    return Array.from(this.players.values());
  }

  /**
   * Получает количество игроков
   */
  getPlayersCount(): number {
    return this.players.size;
  }

  /**
   * Получает онлайн игроков
   */
  getOnlinePlayers(): Player[] {
    return this.getAllPlayers().filter(player => player.isOnline);
  }

  /**
   * Устанавливает статус игрока
   */
  setPlayerStatus(socketId: string, isOnline: boolean): boolean {
    const player = this.players.get(socketId);
    if (!player) return false;

    player.isOnline = isOnline;
    return true;
  }

  /**
   * Проверяет, доступно ли имя
   */
  isNameAvailable(name: string): boolean {
    return !this.playersByName.has(name);
  }

  /**
   * Получает статистику
   */
  getStats() {
    const players = this.getAllPlayers();
    const onlinePlayers = players.filter(p => p.isOnline);

    return {
      total: players.length,
      online: onlinePlayers.length,
      offline: players.length - onlinePlayers.length,
      averageSessionTime: this.calculateAverageSessionTime(players)
    };
  }

  /**
   * Вычисляет среднее время сессии
   */
  private calculateAverageSessionTime(players: Player[]): number {
    if (players.length === 0) return 0;

    const now = new Date();
    const totalTime = players.reduce((sum, player) => {
      const sessionTime = now.getTime() - player.joinedAt.getTime();
      return sum + sessionTime;
    }, 0);

    return Math.round(totalTime / players.length / 1000); // в секундах
  }

  /**
   * Очищает неактивных игроков
   */
  cleanupInactivePlayers(maxInactiveTime: number = 30 * 60 * 1000): number { // 30 минут
    const now = new Date();
    let cleaned = 0;

    for (const [socketId, player] of this.players.entries()) {
      if (!player.isOnline) {
        const inactiveTime = now.getTime() - player.joinedAt.getTime();
        if (inactiveTime > maxInactiveTime) {
          this.unregisterPlayer(socketId);
          cleaned++;
        }
      }
    }

    return cleaned;
  }
}

// Расширение интерфейса Player для публичной информации
export interface PublicPlayerInfo {
  id: string;
  name: string;
  isOnline: boolean;
  joinedAt: Date;
}

// Добавляем метод для получения публичной информации
export function getPlayerPublicInfo(player: Player): PublicPlayerInfo {
  return {
    id: player.id,
    name: player.name,
    isOnline: player.isOnline,
    joinedAt: player.joinedAt
  };
}
