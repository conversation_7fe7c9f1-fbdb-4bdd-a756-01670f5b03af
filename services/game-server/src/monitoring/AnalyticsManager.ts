export interface GameEvent {
  id: string;
  eventType: 'game_start' | 'game_end' | 'player_action' | 'purchase' | 'achievement' | 'social' | 'error';
  playerId: string;
  sessionId: string;
  gameType?: string;
  roomId?: string;
  data: Record<string, any>;
  timestamp: Date;
  userAgent?: string;
  ipAddress?: string;
  platform: 'web' | 'mobile' | 'desktop';
  version: string;
}

export interface PlayerSession {
  sessionId: string;
  playerId: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  platform: string;
  events: GameEvent[];
  gamesPlayed: number;
  achievements: number;
  purchases: number;
  socialInteractions: number;
  errors: number;
}

export interface PerformanceMetrics {
  timestamp: Date;
  serverLoad: {
    cpu: number;
    memory: number;
    activeConnections: number;
    requestsPerSecond: number;
  };
  gameMetrics: {
    activeGames: number;
    averageGameDuration: number;
    gamesPerHour: number;
    playerConcurrency: number;
  };
  errorMetrics: {
    errorRate: number;
    criticalErrors: number;
    warnings: number;
    responseTime: number;
  };
  businessMetrics: {
    revenue: number;
    newPlayers: number;
    retention: {
      day1: number;
      day7: number;
      day30: number;
    };
    conversionRate: number;
  };
}

export interface PlayerBehaviorAnalysis {
  playerId: string;
  playerSegment: 'new' | 'casual' | 'core' | 'whale' | 'churned';
  lifetimeValue: number;
  churnRisk: number;
  preferredGames: string[];
  playPatterns: {
    averageSessionDuration: number;
    sessionsPerWeek: number;
    peakPlayTime: string;
    preferredPlatform: string;
  };
  socialBehavior: {
    friendsCount: number;
    clanParticipation: boolean;
    chatActivity: number;
    invitesSent: number;
  };
  monetization: {
    totalSpent: number;
    averagePurchase: number;
    lastPurchase?: Date;
    preferredItems: string[];
  };
  predictions: {
    nextPurchaseProbability: number;
    churnProbability: number;
    lifetimeValuePrediction: number;
  };
}

export interface A_BTestConfig {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'active' | 'paused' | 'completed';
  startDate: Date;
  endDate?: Date;
  targetAudience: {
    platform?: string[];
    playerLevel?: { min: number; max: number };
    playerSegment?: string[];
    percentage: number;
  };
  variants: A_BVariant[];
  metrics: string[];
  results?: A_BTestResults;
}

export interface A_BVariant {
  id: string;
  name: string;
  description: string;
  config: Record<string, any>;
  trafficPercentage: number;
  participants: number;
}

export interface A_BTestResults {
  totalParticipants: number;
  conversionRate: Record<string, number>;
  statisticalSignificance: number;
  winningVariant?: string;
  metrics: Record<string, any>;
}

export class AnalyticsManager {
  private events: GameEvent[] = [];
  private sessions: Map<string, PlayerSession> = new Map();
  private performanceHistory: PerformanceMetrics[] = [];
  private playerAnalytics: Map<string, PlayerBehaviorAnalysis> = new Map();
  private abTests: Map<string, A_BTestConfig> = new Map();
  private realTimeMetrics: Map<string, number> = new Map();

  /**
   * Записывает игровое событие
   */
  trackEvent(
    eventType: GameEvent['eventType'],
    playerId: string,
    sessionId: string,
    data: Record<string, any>,
    platform: GameEvent['platform'] = 'web',
    version: string = '1.0.0'
  ): void {
    const event: GameEvent = {
      id: this.generateEventId(),
      eventType,
      playerId,
      sessionId,
      data,
      timestamp: new Date(),
      platform,
      version
    };

    this.events.push(event);
    this.updateSession(sessionId, event);
    this.updateRealTimeMetrics(event);
    this.processEventForAnalytics(event);

    // Отправляем в внешние системы аналитики
    this.sendToExternalAnalytics(event);
  }

  /**
   * Начинает новую сессию игрока
   */
  startSession(playerId: string, platform: string): string {
    const sessionId = this.generateSessionId();
    
    const session: PlayerSession = {
      sessionId,
      playerId,
      startTime: new Date(),
      platform,
      events: [],
      gamesPlayed: 0,
      achievements: 0,
      purchases: 0,
      socialInteractions: 0,
      errors: 0
    };

    this.sessions.set(sessionId, session);
    
    this.trackEvent('game_start', playerId, sessionId, {
      action: 'session_start',
      platform
    }, platform as GameEvent['platform']);

    return sessionId;
  }

  /**
   * Завершает сессию игрока
   */
  endSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.endTime = new Date();
    session.duration = session.endTime.getTime() - session.startTime.getTime();

    this.trackEvent('game_end', session.playerId, sessionId, {
      action: 'session_end',
      duration: session.duration,
      gamesPlayed: session.gamesPlayed,
      achievements: session.achievements,
      purchases: session.purchases
    });

    // Анализируем сессию для обновления поведенческой аналитики
    this.analyzeSession(session);
  }

  /**
   * Записывает метрики производительности
   */
  recordPerformanceMetrics(metrics: PerformanceMetrics): void {
    this.performanceHistory.push(metrics);
    
    // Храним только последние 24 часа
    const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000);
    this.performanceHistory = this.performanceHistory.filter(m => m.timestamp > cutoff);

    // Обновляем real-time метрики
    this.realTimeMetrics.set('server_cpu', metrics.serverLoad.cpu);
    this.realTimeMetrics.set('server_memory', metrics.serverLoad.memory);
    this.realTimeMetrics.set('active_connections', metrics.serverLoad.activeConnections);
    this.realTimeMetrics.set('active_games', metrics.gameMetrics.activeGames);
    this.realTimeMetrics.set('player_concurrency', metrics.gameMetrics.playerConcurrency);

    // Проверяем алерты
    this.checkPerformanceAlerts(metrics);
  }

  /**
   * Создает A/B тест
   */
  createABTest(config: Omit<A_BTestConfig, 'id' | 'status'>): A_BTestConfig {
    const testId = this.generateABTestId();
    
    const abTest: A_BTestConfig = {
      ...config,
      id: testId,
      status: 'draft'
    };

    this.abTests.set(testId, abTest);
    return abTest;
  }

  /**
   * Запускает A/B тест
   */
  startABTest(testId: string): boolean {
    const test = this.abTests.get(testId);
    if (!test || test.status !== 'draft') return false;

    test.status = 'active';
    test.startDate = new Date();

    // Инициализируем участников
    test.variants.forEach(variant => {
      variant.participants = 0;
    });

    return true;
  }

  /**
   * Получает вариант A/B теста для игрока
   */
  getABTestVariant(testId: string, playerId: string): A_BVariant | null {
    const test = this.abTests.get(testId);
    if (!test || test.status !== 'active') return null;

    // Проверяем, подходит ли игрок под целевую аудиторию
    if (!this.isPlayerInTargetAudience(playerId, test.targetAudience)) {
      return null;
    }

    // Определяем вариант на основе хеша ID игрока
    const hash = this.hashString(playerId + testId);
    const percentage = hash % 100;
    
    let cumulativePercentage = 0;
    for (const variant of test.variants) {
      cumulativePercentage += variant.trafficPercentage;
      if (percentage < cumulativePercentage) {
        variant.participants++;
        
        // Записываем участие в тесте
        this.trackEvent('social', playerId, '', {
          action: 'ab_test_assignment',
          testId,
          variantId: variant.id
        });
        
        return variant;
      }
    }

    return null;
  }

  /**
   * Анализирует поведение игрока
   */
  analyzePlayerBehavior(playerId: string): PlayerBehaviorAnalysis {
    let analysis = this.playerAnalytics.get(playerId);
    
    if (!analysis) {
      analysis = this.createInitialPlayerAnalysis(playerId);
      this.playerAnalytics.set(playerId, analysis);
    }

    // Обновляем анализ на основе последних событий
    this.updatePlayerAnalysis(analysis);
    
    return analysis;
  }

  /**
   * Получает дашборд аналитики
   */
  getAnalyticsDashboard(timeRange: 'hour' | 'day' | 'week' | 'month' = 'day'): any {
    const now = new Date();
    const timeRanges = {
      hour: 60 * 60 * 1000,
      day: 24 * 60 * 60 * 1000,
      week: 7 * 24 * 60 * 60 * 1000,
      month: 30 * 24 * 60 * 60 * 1000
    };
    
    const cutoff = new Date(now.getTime() - timeRanges[timeRange]);
    const recentEvents = this.events.filter(e => e.timestamp > cutoff);

    return {
      overview: {
        totalEvents: recentEvents.length,
        uniquePlayers: new Set(recentEvents.map(e => e.playerId)).size,
        activeSessions: this.sessions.size,
        realTimeMetrics: Object.fromEntries(this.realTimeMetrics)
      },
      gameMetrics: this.calculateGameMetrics(recentEvents),
      playerMetrics: this.calculatePlayerMetrics(recentEvents),
      revenueMetrics: this.calculateRevenueMetrics(recentEvents),
      performanceMetrics: this.getLatestPerformanceMetrics(),
      topEvents: this.getTopEvents(recentEvents),
      playerSegmentation: this.getPlayerSegmentation(),
      abTestResults: this.getABTestResults()
    };
  }

  /**
   * Получает отчет по удержанию игроков
   */
  getRetentionReport(): any {
    const cohorts = this.calculateCohortAnalysis();
    const churnAnalysis = this.calculateChurnAnalysis();
    
    return {
      cohorts,
      churnAnalysis,
      retentionBySegment: this.getRetentionBySegment(),
      recommendations: this.generateRetentionRecommendations()
    };
  }

  /**
   * Экспортирует данные для внешнего анализа
   */
  exportData(format: 'json' | 'csv', timeRange: Date, endTime: Date): string {
    const events = this.events.filter(e => 
      e.timestamp >= timeRange && e.timestamp <= endTime
    );

    if (format === 'json') {
      return JSON.stringify(events, null, 2);
    } else {
      return this.convertToCSV(events);
    }
  }

  // Приватные методы
  private updateSession(sessionId: string, event: GameEvent): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.events.push(event);

    switch (event.eventType) {
      case 'game_start':
        if (event.data.action === 'game_start') session.gamesPlayed++;
        break;
      case 'achievement':
        session.achievements++;
        break;
      case 'purchase':
        session.purchases++;
        break;
      case 'social':
        session.socialInteractions++;
        break;
      case 'error':
        session.errors++;
        break;
    }
  }

  private updateRealTimeMetrics(event: GameEvent): void {
    const metricKey = `events_${event.eventType}`;
    const current = this.realTimeMetrics.get(metricKey) || 0;
    this.realTimeMetrics.set(metricKey, current + 1);

    // Обновляем метрики за последний час
    const hourKey = `${metricKey}_hour`;
    const hourlyEvents = this.events.filter(e => 
      e.eventType === event.eventType && 
      e.timestamp > new Date(Date.now() - 60 * 60 * 1000)
    ).length;
    this.realTimeMetrics.set(hourKey, hourlyEvents);
  }

  private processEventForAnalytics(event: GameEvent): void {
    // Обновляем поведенческую аналитику игрока
    const analysis = this.playerAnalytics.get(event.playerId);
    if (analysis) {
      this.updatePlayerAnalysisFromEvent(analysis, event);
    }

    // Обновляем результаты A/B тестов
    this.updateABTestResults(event);
  }

  private sendToExternalAnalytics(event: GameEvent): void {
    // Интеграция с Google Analytics, Mixpanel, etc.
    // Здесь должна быть отправка во внешние системы
    console.log(`Analytics: ${event.eventType} from ${event.playerId}`);
  }

  private analyzeSession(session: PlayerSession): void {
    const analysis = this.analyzePlayerBehavior(session.playerId);
    
    // Обновляем паттерны игры
    analysis.playPatterns.averageSessionDuration = 
      (analysis.playPatterns.averageSessionDuration + (session.duration || 0)) / 2;
    
    // Обновляем предпочтения по платформе
    if (session.platform === analysis.playPatterns.preferredPlatform) {
      // Увеличиваем счетчик
    } else {
      analysis.playPatterns.preferredPlatform = session.platform;
    }
  }

  private createInitialPlayerAnalysis(playerId: string): PlayerBehaviorAnalysis {
    return {
      playerId,
      playerSegment: 'new',
      lifetimeValue: 0,
      churnRisk: 0.1,
      preferredGames: [],
      playPatterns: {
        averageSessionDuration: 0,
        sessionsPerWeek: 0,
        peakPlayTime: '20:00',
        preferredPlatform: 'web'
      },
      socialBehavior: {
        friendsCount: 0,
        clanParticipation: false,
        chatActivity: 0,
        invitesSent: 0
      },
      monetization: {
        totalSpent: 0,
        averagePurchase: 0,
        preferredItems: []
      },
      predictions: {
        nextPurchaseProbability: 0.05,
        churnProbability: 0.1,
        lifetimeValuePrediction: 0
      }
    };
  }

  private updatePlayerAnalysis(analysis: PlayerBehaviorAnalysis): void {
    // Обновляем сегмент игрока
    analysis.playerSegment = this.calculatePlayerSegment(analysis);
    
    // Обновляем риск оттока
    analysis.churnRisk = this.calculateChurnRisk(analysis);
    
    // Обновляем предсказания
    analysis.predictions = this.calculatePredictions(analysis);
  }

  private updatePlayerAnalysisFromEvent(analysis: PlayerBehaviorAnalysis, event: GameEvent): void {
    switch (event.eventType) {
      case 'purchase':
        analysis.monetization.totalSpent += event.data.amount || 0;
        analysis.monetization.lastPurchase = event.timestamp;
        break;
      case 'social':
        analysis.socialBehavior.chatActivity++;
        break;
      case 'game_start':
        if (event.data.gameType) {
          const gameIndex = analysis.preferredGames.indexOf(event.data.gameType);
          if (gameIndex === -1) {
            analysis.preferredGames.push(event.data.gameType);
          }
        }
        break;
    }
  }

  private calculatePlayerSegment(analysis: PlayerBehaviorAnalysis): PlayerBehaviorAnalysis['playerSegment'] {
    if (analysis.monetization.totalSpent > 100) return 'whale';
    if (analysis.monetization.totalSpent > 10) return 'core';
    if (analysis.playPatterns.sessionsPerWeek > 5) return 'core';
    if (analysis.playPatterns.sessionsPerWeek < 1) return 'churned';
    return 'casual';
  }

  private calculateChurnRisk(analysis: PlayerBehaviorAnalysis): number {
    let risk = 0.1;
    
    // Увеличиваем риск если давно не играл
    const daysSinceLastSession = 7; // Должно браться из реальных данных
    risk += daysSinceLastSession * 0.02;
    
    // Уменьшаем риск для активных игроков
    if (analysis.playPatterns.sessionsPerWeek > 3) risk -= 0.05;
    if (analysis.socialBehavior.friendsCount > 5) risk -= 0.03;
    if (analysis.socialBehavior.clanParticipation) risk -= 0.02;
    
    return Math.max(0, Math.min(1, risk));
  }

  private calculatePredictions(analysis: PlayerBehaviorAnalysis): PlayerBehaviorAnalysis['predictions'] {
    return {
      nextPurchaseProbability: analysis.monetization.totalSpent > 0 ? 0.15 : 0.05,
      churnProbability: analysis.churnRisk,
      lifetimeValuePrediction: analysis.lifetimeValue * 1.5
    };
  }

  private isPlayerInTargetAudience(playerId: string, audience: A_BTestConfig['targetAudience']): boolean {
    // Здесь должна быть проверка критериев аудитории
    return Math.random() < (audience.percentage / 100);
  }

  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash);
  }

  private checkPerformanceAlerts(metrics: PerformanceMetrics): void {
    const alerts = [];
    
    if (metrics.serverLoad.cpu > 80) {
      alerts.push('High CPU usage detected');
    }
    
    if (metrics.serverLoad.memory > 85) {
      alerts.push('High memory usage detected');
    }
    
    if (metrics.errorMetrics.errorRate > 5) {
      alerts.push('High error rate detected');
    }
    
    if (alerts.length > 0) {
      this.sendAlerts(alerts);
    }
  }

  private sendAlerts(alerts: string[]): void {
    // Отправка алертов в Slack, email, etc.
    console.log('ALERTS:', alerts);
  }

  private calculateGameMetrics(events: GameEvent[]): any {
    const gameEvents = events.filter(e => e.eventType === 'game_start' || e.eventType === 'game_end');
    const gamesByType = gameEvents.reduce((acc, event) => {
      const gameType = event.data.gameType || 'unknown';
      acc[gameType] = (acc[gameType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalGames: gameEvents.length,
      gamesByType,
      averageGameDuration: this.calculateAverageGameDuration(events)
    };
  }

  private calculatePlayerMetrics(events: GameEvent[]): any {
    const uniquePlayers = new Set(events.map(e => e.playerId)).size;
    const newPlayers = events.filter(e => e.eventType === 'game_start' && e.data.action === 'first_game').length;
    
    return {
      uniquePlayers,
      newPlayers,
      returningPlayers: uniquePlayers - newPlayers
    };
  }

  private calculateRevenueMetrics(events: GameEvent[]): any {
    const purchaseEvents = events.filter(e => e.eventType === 'purchase');
    const totalRevenue = purchaseEvents.reduce((sum, e) => sum + (e.data.amount || 0), 0);
    
    return {
      totalRevenue,
      totalPurchases: purchaseEvents.length,
      averagePurchase: purchaseEvents.length > 0 ? totalRevenue / purchaseEvents.length : 0,
      payingUsers: new Set(purchaseEvents.map(e => e.playerId)).size
    };
  }

  private calculateAverageGameDuration(events: GameEvent[]): number {
    // Логика расчета средней продолжительности игры
    return 300; // 5 минут в секундах
  }

  private getLatestPerformanceMetrics(): PerformanceMetrics | null {
    return this.performanceHistory[this.performanceHistory.length - 1] || null;
  }

  private getTopEvents(events: GameEvent[]): any[] {
    const eventCounts = events.reduce((acc, event) => {
      acc[event.eventType] = (acc[event.eventType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(eventCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([event, count]) => ({ event, count }));
  }

  private getPlayerSegmentation(): any {
    const segments = Array.from(this.playerAnalytics.values()).reduce((acc, analysis) => {
      acc[analysis.playerSegment] = (acc[analysis.playerSegment] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return segments;
  }

  private getABTestResults(): any[] {
    return Array.from(this.abTests.values())
      .filter(test => test.status === 'active')
      .map(test => ({
        id: test.id,
        name: test.name,
        participants: test.variants.reduce((sum, v) => sum + v.participants, 0),
        status: test.status
      }));
  }

  private calculateCohortAnalysis(): any {
    // Логика когортного анализа
    return {};
  }

  private calculateChurnAnalysis(): any {
    // Логика анализа оттока
    return {};
  }

  private getRetentionBySegment(): any {
    // Логика удержания по сегментам
    return {};
  }

  private generateRetentionRecommendations(): string[] {
    return [
      'Увеличить частоту ежедневных наград',
      'Добавить больше социальных функций',
      'Улучшить onboarding для новых игроков'
    ];
  }

  private updateABTestResults(event: GameEvent): void {
    // Обновление результатов A/B тестов на основе событий
  }

  private convertToCSV(events: GameEvent[]): string {
    const headers = ['id', 'eventType', 'playerId', 'timestamp', 'platform', 'data'];
    const rows = events.map(event => [
      event.id,
      event.eventType,
      event.playerId,
      event.timestamp.toISOString(),
      event.platform,
      JSON.stringify(event.data)
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  // Генераторы ID
  private generateEventId(): string {
    return 'evt_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generateSessionId(): string {
    return 'ses_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generateABTestId(): string {
    return 'ab_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}
