import { PlayerRating } from '../rating/RatingManager';
import { FriendsManager } from '../friends/FriendsManager';

export interface PlayerProgress {
  playerId: string;
  level: number;
  experience: number;
  experienceToNextLevel: number;
  totalExperience: number;
  currentTitle?: string;
  availableTitles: string[];
  dailyTasksCompleted: number;
  weeklyTasksCompleted: number;
  lastDailyReset: Date;
  lastWeeklyReset: Date;
  playStreak: number; // Дни подряд игры
  lastPlayDate: Date;
  lifetimeStats: {
    gamesPlayed: number;
    gamesWon: number;
    tournamentsWon: number;
    friendsAdded: number;
    achievementsUnlocked: number;
    spectatedGames: number;
  };
}

export interface DailyTask {
  id: string;
  name: string;
  description: string;
  icon: string;
  requirement: {
    type: 'play_games' | 'win_games' | 'play_with_friends' | 'spectate_games' | 'send_friend_requests' | 'login_streak';
    value: number;
  };
  experienceReward: number;
  progress: number;
  completed: boolean;
  date: string; // YYYY-MM-DD
}

export interface WeeklyChallenge {
  id: string;
  name: string;
  description: string;
  icon: string;
  requirement: {
    type: 'win_streak' | 'rating_gain' | 'tournament_participation' | 'social_activity' | 'perfect_games';
    value: number;
  };
  experienceReward: number;
  specialReward?: {
    type: 'title' | 'badge' | 'points';
    value: string | number;
  };
  progress: number;
  completed: boolean;
  weekStart: string; // YYYY-MM-DD
}

export interface PlayerTitle {
  id: string;
  name: string;
  description: string;
  icon: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
  requirement: string;
  color: string;
  unlockCondition: (progress: PlayerProgress, rating: PlayerRating) => boolean;
}

export interface ExperienceGain {
  source: 'game_win' | 'game_played' | 'daily_task' | 'weekly_challenge' | 'achievement' | 'tournament' | 'social';
  amount: number;
  description: string;
  timestamp: Date;
}

export class ProgressManager {
  private playerProgress: Map<string, PlayerProgress> = new Map(); // playerId -> progress
  private dailyTasks: Map<string, DailyTask[]> = new Map(); // playerId -> tasks
  private weeklyChallenges: Map<string, WeeklyChallenge[]> = new Map(); // playerId -> challenges
  private availableTitles: Map<string, PlayerTitle> = new Map(); // titleId -> title
  private experienceHistory: Map<string, ExperienceGain[]> = new Map(); // playerId -> history

  constructor() {
    this.initializeTitles();
  }

  /**
   * Инициализирует доступные титулы
   */
  private initializeTitles(): void {
    const titles: PlayerTitle[] = [
      // Базовые титулы
      {
        id: 'novice',
        name: 'Новичок',
        description: 'Начинающий игрок',
        icon: '🌱',
        rarity: 'common',
        requirement: 'Достигните 1 уровня',
        color: '#4CAF50',
        unlockCondition: (progress) => progress.level >= 1
      },
      {
        id: 'apprentice',
        name: 'Ученик',
        description: 'Изучающий основы',
        icon: '📚',
        rarity: 'common',
        requirement: 'Достигните 5 уровня',
        color: '#2196F3',
        unlockCondition: (progress) => progress.level >= 5
      },
      {
        id: 'skilled',
        name: 'Умелый',
        description: 'Опытный игрок',
        icon: '⚔️',
        rarity: 'rare',
        requirement: 'Достигните 10 уровня',
        color: '#FF9800',
        unlockCondition: (progress) => progress.level >= 10
      },
      {
        id: 'expert',
        name: 'Эксперт',
        description: 'Мастер своего дела',
        icon: '💎',
        rarity: 'epic',
        requirement: 'Достигните 20 уровня',
        color: '#9C27B0',
        unlockCondition: (progress) => progress.level >= 20
      },
      {
        id: 'master',
        name: 'Мастер',
        description: 'Истинный профессионал',
        icon: '👑',
        rarity: 'legendary',
        requirement: 'Достигните 35 уровня',
        color: '#FFD700',
        unlockCondition: (progress) => progress.level >= 35
      },
      {
        id: 'grandmaster',
        name: 'Гроссмейстер',
        description: 'Легенда игры',
        icon: '🌟',
        rarity: 'mythic',
        requirement: 'Достигните 50 уровня',
        color: '#FF6B35',
        unlockCondition: (progress) => progress.level >= 50
      },

      // Специальные титулы
      {
        id: 'social_butterfly',
        name: 'Душа компании',
        description: 'Активный в социальных функциях',
        icon: '🦋',
        rarity: 'rare',
        requirement: 'Добавьте 10 друзей',
        color: '#E91E63',
        unlockCondition: (progress) => progress.lifetimeStats.friendsAdded >= 10
      },
      {
        id: 'tournament_champion',
        name: 'Чемпион турниров',
        description: 'Победитель турниров',
        icon: '🏆',
        rarity: 'epic',
        requirement: 'Выиграйте 5 турниров',
        color: '#FFD700',
        unlockCondition: (progress) => progress.lifetimeStats.tournamentsWon >= 5
      },
      {
        id: 'observer',
        name: 'Наблюдатель',
        description: 'Любитель смотреть игры',
        icon: '👁️',
        rarity: 'rare',
        requirement: 'Понаблюдайте за 50 играми',
        color: '#607D8B',
        unlockCondition: (progress) => progress.lifetimeStats.spectatedGames >= 50
      },
      {
        id: 'achiever',
        name: 'Коллекционер',
        description: 'Собиратель достижений',
        icon: '🎖️',
        rarity: 'epic',
        requirement: 'Разблокируйте 25 достижений',
        color: '#795548',
        unlockCondition: (progress) => progress.lifetimeStats.achievementsUnlocked >= 25
      },
      {
        id: 'dedicated',
        name: 'Преданный',
        description: 'Играет каждый день',
        icon: '🔥',
        rarity: 'legendary',
        requirement: 'Играйте 30 дней подряд',
        color: '#FF5722',
        unlockCondition: (progress) => progress.playStreak >= 30
      }
    ];

    titles.forEach(title => {
      this.availableTitles.set(title.id, title);
    });
  }

  /**
   * Получает или создает прогресс игрока
   */
  getPlayerProgress(playerId: string): PlayerProgress {
    if (!this.playerProgress.has(playerId)) {
      const newProgress: PlayerProgress = {
        playerId,
        level: 1,
        experience: 0,
        experienceToNextLevel: this.getExperienceForLevel(2),
        totalExperience: 0,
        currentTitle: 'novice',
        availableTitles: ['novice'],
        dailyTasksCompleted: 0,
        weeklyTasksCompleted: 0,
        lastDailyReset: new Date(),
        lastWeeklyReset: this.getWeekStart(new Date()),
        playStreak: 0,
        lastPlayDate: new Date(),
        lifetimeStats: {
          gamesPlayed: 0,
          gamesWon: 0,
          tournamentsWon: 0,
          friendsAdded: 0,
          achievementsUnlocked: 0,
          spectatedGames: 0
        }
      };
      this.playerProgress.set(playerId, newProgress);
    }
    return this.playerProgress.get(playerId)!;
  }

  /**
   * Добавляет опыт игроку
   */
  addExperience(playerId: string, gain: Omit<ExperienceGain, 'timestamp'>): { 
    leveledUp: boolean; 
    newLevel?: number; 
    newTitles?: PlayerTitle[] 
  } {
    const progress = this.getPlayerProgress(playerId);
    const oldLevel = progress.level;

    // Добавляем опыт
    progress.experience += gain.amount;
    progress.totalExperience += gain.amount;

    // Записываем в историю
    const experienceGain: ExperienceGain = {
      ...gain,
      timestamp: new Date()
    };
    
    if (!this.experienceHistory.has(playerId)) {
      this.experienceHistory.set(playerId, []);
    }
    this.experienceHistory.get(playerId)!.push(experienceGain);

    // Проверяем повышение уровня
    let leveledUp = false;
    let newTitles: PlayerTitle[] = [];

    while (progress.experience >= progress.experienceToNextLevel) {
      progress.experience -= progress.experienceToNextLevel;
      progress.level++;
      leveledUp = true;
      
      // Обновляем опыт для следующего уровня
      progress.experienceToNextLevel = this.getExperienceForLevel(progress.level + 1);
    }

    // Проверяем новые титулы
    if (leveledUp) {
      newTitles = this.checkNewTitles(playerId);
    }

    return {
      leveledUp,
      newLevel: leveledUp ? progress.level : undefined,
      newTitles: newTitles.length > 0 ? newTitles : undefined
    };
  }

  /**
   * Обновляет статистику игрока
   */
  updatePlayerStats(playerId: string, stats: Partial<PlayerProgress['lifetimeStats']>): void {
    const progress = this.getPlayerProgress(playerId);
    
    Object.assign(progress.lifetimeStats, stats);
    
    // Обновляем дату последней игры и серию
    const today = new Date();
    const lastPlay = progress.lastPlayDate;
    const daysDiff = Math.floor((today.getTime() - lastPlay.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDiff === 1) {
      // Продолжаем серию
      progress.playStreak++;
    } else if (daysDiff > 1) {
      // Серия прервана
      progress.playStreak = 1;
    }
    // Если daysDiff === 0, то игрок уже играл сегодня
    
    progress.lastPlayDate = today;
  }

  /**
   * Проверяет новые доступные титулы
   */
  private checkNewTitles(playerId: string): PlayerTitle[] {
    const progress = this.getPlayerProgress(playerId);
    const playerRating = { rating: 1200 } as PlayerRating; // Заглушка, нужно получать реальный рейтинг
    
    const newTitles: PlayerTitle[] = [];
    
    for (const title of this.availableTitles.values()) {
      if (!progress.availableTitles.includes(title.id) && 
          title.unlockCondition(progress, playerRating)) {
        progress.availableTitles.push(title.id);
        newTitles.push(title);
      }
    }
    
    return newTitles;
  }

  /**
   * Генерирует ежедневные задания
   */
  generateDailyTasks(playerId: string): DailyTask[] {
    const today = new Date().toISOString().split('T')[0];
    const progress = this.getPlayerProgress(playerId);
    
    // Проверяем, нужно ли обновить задания
    const lastReset = progress.lastDailyReset.toISOString().split('T')[0];
    if (lastReset === today) {
      return this.dailyTasks.get(playerId) || [];
    }

    // Генерируем новые задания
    const taskTemplates = [
      {
        id: 'play_games',
        name: 'Активная игра',
        description: 'Сыграйте 3 игры',
        icon: '🎮',
        type: 'play_games' as const,
        value: 3,
        experience: 100
      },
      {
        id: 'win_games',
        name: 'Путь к победе',
        description: 'Выиграйте 2 игры',
        icon: '🏆',
        type: 'win_games' as const,
        value: 2,
        experience: 150
      },
      {
        id: 'play_with_friends',
        name: 'Дружеская игра',
        description: 'Сыграйте 1 игру с другом',
        icon: '👥',
        type: 'play_with_friends' as const,
        value: 1,
        experience: 120
      },
      {
        id: 'spectate_games',
        name: 'Наблюдатель',
        description: 'Понаблюдайте за 2 играми',
        icon: '👁️',
        type: 'spectate_games' as const,
        value: 2,
        experience: 80
      },
      {
        id: 'send_friend_requests',
        name: 'Социальная активность',
        description: 'Отправьте 1 запрос в друзья',
        icon: '📨',
        type: 'send_friend_requests' as const,
        value: 1,
        experience: 50
      }
    ];

    // Выбираем 3 случайных задания
    const shuffled = taskTemplates.sort(() => 0.5 - Math.random());
    const selectedTasks = shuffled.slice(0, 3);

    const dailyTasks: DailyTask[] = selectedTasks.map(template => ({
      id: `${today}_${template.id}`,
      name: template.name,
      description: template.description,
      icon: template.icon,
      requirement: {
        type: template.type,
        value: template.value
      },
      experienceReward: template.experience,
      progress: 0,
      completed: false,
      date: today
    }));

    this.dailyTasks.set(playerId, dailyTasks);
    progress.lastDailyReset = new Date();
    progress.dailyTasksCompleted = 0;

    return dailyTasks;
  }

  /**
   * Обновляет прогресс ежедневного задания
   */
  updateDailyTaskProgress(playerId: string, taskType: DailyTask['requirement']['type'], amount: number = 1): DailyTask[] {
    const tasks = this.generateDailyTasks(playerId); // Получаем актуальные задания
    const progress = this.getPlayerProgress(playerId);
    
    let completedNewTasks = 0;
    
    tasks.forEach(task => {
      if (task.requirement.type === taskType && !task.completed) {
        task.progress = Math.min(task.progress + amount, task.requirement.value);
        
        if (task.progress >= task.requirement.value && !task.completed) {
          task.completed = true;
          completedNewTasks++;
          
          // Добавляем опыт за выполнение задания
          this.addExperience(playerId, {
            source: 'daily_task',
            amount: task.experienceReward,
            description: `Выполнено ежедневное задание: ${task.name}`
          });
        }
      }
    });
    
    progress.dailyTasksCompleted += completedNewTasks;
    return tasks;
  }

  /**
   * Вычисляет опыт, необходимый для достижения уровня
   */
  private getExperienceForLevel(level: number): number {
    // Экспоненциальная формула: базовый опыт * (уровень^1.5)
    const baseExperience = 100;
    return Math.floor(baseExperience * Math.pow(level, 1.5));
  }

  /**
   * Получает начало недели
   */
  private getWeekStart(date: Date): Date {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Понедельник как начало недели
    return new Date(d.setDate(diff));
  }

  /**
   * Получает историю опыта игрока
   */
  getExperienceHistory(playerId: string, limit: number = 50): ExperienceGain[] {
    const history = this.experienceHistory.get(playerId) || [];
    return history.slice(-limit).reverse();
  }

  /**
   * Получает доступные титулы игрока
   */
  getPlayerTitles(playerId: string): { current?: PlayerTitle; available: PlayerTitle[] } {
    const progress = this.getPlayerProgress(playerId);
    
    const available = progress.availableTitles
      .map(titleId => this.availableTitles.get(titleId))
      .filter((title): title is PlayerTitle => title !== undefined);
    
    const current = progress.currentTitle ? 
      this.availableTitles.get(progress.currentTitle) : 
      undefined;
    
    return { current, available };
  }

  /**
   * Устанавливает активный титул игрока
   */
  setPlayerTitle(playerId: string, titleId: string): boolean {
    const progress = this.getPlayerProgress(playerId);
    
    if (progress.availableTitles.includes(titleId)) {
      progress.currentTitle = titleId;
      return true;
    }
    
    return false;
  }

  /**
   * Получает статистику прогресса
   */
  getProgressStats() {
    const totalPlayers = this.playerProgress.size;
    let totalExperience = 0;
    let maxLevel = 0;
    let totalTasks = 0;
    
    for (const progress of this.playerProgress.values()) {
      totalExperience += progress.totalExperience;
      maxLevel = Math.max(maxLevel, progress.level);
      totalTasks += progress.dailyTasksCompleted;
    }
    
    return {
      totalPlayers,
      totalExperience,
      maxLevel,
      totalTasksCompleted: totalTasks,
      averageLevel: totalPlayers > 0 ? Math.round(totalExperience / totalPlayers / 100) : 0
    };
  }
}
