export interface Clan {
  id: string;
  name: string;
  tag: string; // 3-4 символа
  description: string;
  leaderId: string;
  leaderName: string;
  members: ClanMember[];
  level: number;
  experience: number;
  experienceToNextLevel: number;
  totalRating: number;
  averageRating: number;
  totalWins: number;
  totalGames: number;
  winRate: number;
  treasury: number;
  isPublic: boolean;
  requirements: {
    minRating: number;
    minLevel: number;
    applicationRequired: boolean;
  };
  perks: {
    experienceBonus: number;
    dailyTaskBonus: number;
    tournamentBonus: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface ClanMember {
  playerId: string;
  playerName: string;
  role: 'leader' | 'officer' | 'member';
  joinedAt: Date;
  contributedExperience: number;
  contributedTreasury: number;
  lastActiveAt: Date;
  permissions: {
    canInvite: boolean;
    canKick: boolean;
    canManageEvents: boolean;
    canManageTreasury: boolean;
  };
}

export interface ClanApplication {
  id: string;
  clanId: string;
  playerId: string;
  playerName: string;
  playerRating: number;
  playerLevel: number;
  message: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
}

export interface ClanEvent {
  id: string;
  clanId: string;
  type: 'tournament' | 'challenge' | 'meeting' | 'training';
  title: string;
  description: string;
  startTime: Date;
  endTime: Date;
  maxParticipants: number;
  participants: string[];
  rewards: {
    experience: number;
    treasury: number;
    items?: string[];
  };
  status: 'scheduled' | 'active' | 'completed' | 'cancelled';
  createdBy: string;
  createdAt: Date;
}

export interface ClanWar {
  id: string;
  attackingClanId: string;
  defendingClanId: string;
  startTime: Date;
  endTime: Date;
  status: 'preparation' | 'active' | 'completed';
  battles: ClanBattle[];
  score: {
    attacking: number;
    defending: number;
  };
  rewards: {
    winner: { experience: number; treasury: number };
    loser: { experience: number; treasury: number };
  };
}

export interface ClanBattle {
  id: string;
  attackingPlayerId: string;
  defendingPlayerId: string;
  gameId: string;
  winnerId?: string;
  completedAt?: Date;
}

export class ClanManager {
  private clans: Map<string, Clan> = new Map();
  private playerClans: Map<string, string> = new Map(); // playerId -> clanId
  private applications: Map<string, ClanApplication> = new Map();
  private events: Map<string, ClanEvent> = new Map();
  private wars: Map<string, ClanWar> = new Map();

  /**
   * Создает новый клан
   */
  createClan(
    leaderId: string,
    leaderName: string,
    clanName: string,
    tag: string,
    description: string = '',
    isPublic: boolean = true
  ): Clan {
    // Проверяем, что игрок не состоит в клане
    if (this.playerClans.has(leaderId)) {
      throw new Error('Player already in a clan');
    }

    // Проверяем уникальность названия и тега
    if (this.isClanNameTaken(clanName)) {
      throw new Error('Clan name already taken');
    }

    if (this.isClanTagTaken(tag)) {
      throw new Error('Clan tag already taken');
    }

    // Валидация тега
    if (tag.length < 3 || tag.length > 4 || !/^[A-Z0-9]+$/.test(tag)) {
      throw new Error('Invalid clan tag format');
    }

    const clanId = this.generateClanId();
    const leader: ClanMember = {
      playerId: leaderId,
      playerName: leaderName,
      role: 'leader',
      joinedAt: new Date(),
      contributedExperience: 0,
      contributedTreasury: 0,
      lastActiveAt: new Date(),
      permissions: {
        canInvite: true,
        canKick: true,
        canManageEvents: true,
        canManageTreasury: true
      }
    };

    const clan: Clan = {
      id: clanId,
      name: clanName,
      tag,
      description,
      leaderId,
      leaderName,
      members: [leader],
      level: 1,
      experience: 0,
      experienceToNextLevel: 1000,
      totalRating: 0,
      averageRating: 0,
      totalWins: 0,
      totalGames: 0,
      winRate: 0,
      treasury: 0,
      isPublic,
      requirements: {
        minRating: 1000,
        minLevel: 1,
        applicationRequired: false
      },
      perks: {
        experienceBonus: 5, // 5% бонус к опыту
        dailyTaskBonus: 10, // 10% бонус к ежедневным заданиям
        tournamentBonus: 15 // 15% бонус к турнирным наградам
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.clans.set(clanId, clan);
    this.playerClans.set(leaderId, clanId);

    return clan;
  }

  /**
   * Подает заявку на вступление в клан
   */
  applyToClan(
    clanId: string,
    playerId: string,
    playerName: string,
    playerRating: number,
    playerLevel: number,
    message: string = ''
  ): ClanApplication {
    const clan = this.clans.get(clanId);
    if (!clan) {
      throw new Error('Clan not found');
    }

    if (this.playerClans.has(playerId)) {
      throw new Error('Player already in a clan');
    }

    // Проверяем требования клана
    if (playerRating < clan.requirements.minRating) {
      throw new Error('Player rating too low');
    }

    if (playerLevel < clan.requirements.minLevel) {
      throw new Error('Player level too low');
    }

    // Проверяем, нет ли уже заявки
    const existingApplication = Array.from(this.applications.values())
      .find(app => app.clanId === clanId && app.playerId === playerId && app.status === 'pending');
    
    if (existingApplication) {
      throw new Error('Application already exists');
    }

    const applicationId = this.generateApplicationId();
    const application: ClanApplication = {
      id: applicationId,
      clanId,
      playerId,
      playerName,
      playerRating,
      playerLevel,
      message,
      status: 'pending',
      createdAt: new Date()
    };

    this.applications.set(applicationId, application);

    // Если клан публичный и не требует заявок, автоматически принимаем
    if (clan.isPublic && !clan.requirements.applicationRequired) {
      this.approveApplication(applicationId, clan.leaderId);
    }

    return application;
  }

  /**
   * Одобряет заявку на вступление
   */
  approveApplication(applicationId: string, reviewerId: string): boolean {
    const application = this.applications.get(applicationId);
    if (!application || application.status !== 'pending') {
      return false;
    }

    const clan = this.clans.get(application.clanId);
    if (!clan) {
      return false;
    }

    // Проверяем права на одобрение
    const reviewer = clan.members.find(m => m.playerId === reviewerId);
    if (!reviewer || (!reviewer.permissions.canInvite && reviewer.role !== 'leader')) {
      throw new Error('No permission to approve applications');
    }

    // Добавляем игрока в клан
    const newMember: ClanMember = {
      playerId: application.playerId,
      playerName: application.playerName,
      role: 'member',
      joinedAt: new Date(),
      contributedExperience: 0,
      contributedTreasury: 0,
      lastActiveAt: new Date(),
      permissions: {
        canInvite: false,
        canKick: false,
        canManageEvents: false,
        canManageTreasury: false
      }
    };

    clan.members.push(newMember);
    this.playerClans.set(application.playerId, application.clanId);

    // Обновляем заявку
    application.status = 'approved';
    application.reviewedAt = new Date();
    application.reviewedBy = reviewerId;

    // Обновляем статистику клана
    this.updateClanStats(clan);

    return true;
  }

  /**
   * Отклоняет заявку на вступление
   */
  rejectApplication(applicationId: string, reviewerId: string): boolean {
    const application = this.applications.get(applicationId);
    if (!application || application.status !== 'pending') {
      return false;
    }

    const clan = this.clans.get(application.clanId);
    if (!clan) {
      return false;
    }

    // Проверяем права на отклонение
    const reviewer = clan.members.find(m => m.playerId === reviewerId);
    if (!reviewer || (!reviewer.permissions.canInvite && reviewer.role !== 'leader')) {
      throw new Error('No permission to reject applications');
    }

    application.status = 'rejected';
    application.reviewedAt = new Date();
    application.reviewedBy = reviewerId;

    return true;
  }

  /**
   * Исключает игрока из клана
   */
  kickMember(clanId: string, kickerId: string, targetId: string): boolean {
    const clan = this.clans.get(clanId);
    if (!clan) {
      return false;
    }

    const kicker = clan.members.find(m => m.playerId === kickerId);
    const target = clan.members.find(m => m.playerId === targetId);

    if (!kicker || !target) {
      return false;
    }

    // Проверяем права на исключение
    if (!kicker.permissions.canKick && kicker.role !== 'leader') {
      throw new Error('No permission to kick members');
    }

    // Нельзя исключить лидера
    if (target.role === 'leader') {
      throw new Error('Cannot kick clan leader');
    }

    // Офицеры могут исключать только обычных участников
    if (kicker.role === 'officer' && target.role === 'officer') {
      throw new Error('Officers cannot kick other officers');
    }

    // Удаляем участника
    clan.members = clan.members.filter(m => m.playerId !== targetId);
    this.playerClans.delete(targetId);

    this.updateClanStats(clan);
    return true;
  }

  /**
   * Покидает клан
   */
  leaveClan(playerId: string): boolean {
    const clanId = this.playerClans.get(playerId);
    if (!clanId) {
      return false;
    }

    const clan = this.clans.get(clanId);
    if (!clan) {
      return false;
    }

    const member = clan.members.find(m => m.playerId === playerId);
    if (!member) {
      return false;
    }

    // Лидер не может покинуть клан, пока не передаст лидерство
    if (member.role === 'leader' && clan.members.length > 1) {
      throw new Error('Leader must transfer leadership before leaving');
    }

    // Удаляем участника
    clan.members = clan.members.filter(m => m.playerId !== playerId);
    this.playerClans.delete(playerId);

    // Если клан остался без участников, удаляем его
    if (clan.members.length === 0) {
      this.clans.delete(clanId);
    } else {
      this.updateClanStats(clan);
    }

    return true;
  }

  /**
   * Передает лидерство
   */
  transferLeadership(clanId: string, currentLeaderId: string, newLeaderId: string): boolean {
    const clan = this.clans.get(clanId);
    if (!clan || clan.leaderId !== currentLeaderId) {
      return false;
    }

    const newLeader = clan.members.find(m => m.playerId === newLeaderId);
    if (!newLeader) {
      return false;
    }

    // Обновляем роли
    const currentLeader = clan.members.find(m => m.playerId === currentLeaderId)!;
    currentLeader.role = 'officer';
    currentLeader.permissions = {
      canInvite: true,
      canKick: true,
      canManageEvents: true,
      canManageTreasury: false
    };

    newLeader.role = 'leader';
    newLeader.permissions = {
      canInvite: true,
      canKick: true,
      canManageEvents: true,
      canManageTreasury: true
    };

    clan.leaderId = newLeaderId;
    clan.leaderName = newLeader.playerName;
    clan.updatedAt = new Date();

    return true;
  }

  /**
   * Добавляет опыт клану
   */
  addClanExperience(playerId: string, amount: number): boolean {
    const clanId = this.playerClans.get(playerId);
    if (!clanId) {
      return false;
    }

    const clan = this.clans.get(clanId);
    if (!clan) {
      return false;
    }

    const member = clan.members.find(m => m.playerId === playerId);
    if (!member) {
      return false;
    }

    // Добавляем опыт с учетом бонуса клана
    const bonusAmount = Math.floor(amount * (clan.perks.experienceBonus / 100));
    const totalAmount = amount + bonusAmount;

    clan.experience += totalAmount;
    member.contributedExperience += totalAmount;

    // Проверяем повышение уровня
    while (clan.experience >= clan.experienceToNextLevel) {
      clan.experience -= clan.experienceToNextLevel;
      clan.level++;
      clan.experienceToNextLevel = this.calculateExperienceForLevel(clan.level + 1);
      
      // Улучшаем перки при повышении уровня
      this.updateClanPerks(clan);
    }

    clan.updatedAt = new Date();
    return true;
  }

  /**
   * Получает информацию о клане
   */
  getClan(clanId: string): Clan | undefined {
    return this.clans.get(clanId);
  }

  /**
   * Получает клан игрока
   */
  getPlayerClan(playerId: string): Clan | undefined {
    const clanId = this.playerClans.get(playerId);
    return clanId ? this.clans.get(clanId) : undefined;
  }

  /**
   * Получает список кланов
   */
  getClans(limit: number = 50, sortBy: 'level' | 'rating' | 'members' = 'level'): Clan[] {
    const clans = Array.from(this.clans.values());
    
    clans.sort((a, b) => {
      switch (sortBy) {
        case 'level':
          return b.level - a.level;
        case 'rating':
          return b.averageRating - a.averageRating;
        case 'members':
          return b.members.length - a.members.length;
        default:
          return b.level - a.level;
      }
    });

    return clans.slice(0, limit);
  }

  /**
   * Получает заявки клана
   */
  getClanApplications(clanId: string): ClanApplication[] {
    return Array.from(this.applications.values())
      .filter(app => app.clanId === clanId && app.status === 'pending')
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  /**
   * Получает статистику кланов
   */
  getStats() {
    const totalClans = this.clans.size;
    const totalMembers = Array.from(this.clans.values()).reduce((sum, clan) => sum + clan.members.length, 0);
    const averageClanSize = totalClans > 0 ? Math.round(totalMembers / totalClans) : 0;
    const pendingApplications = Array.from(this.applications.values()).filter(app => app.status === 'pending').length;

    const levelDistribution = Array.from(this.clans.values()).reduce((acc, clan) => {
      acc[clan.level] = (acc[clan.level] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    return {
      totalClans,
      totalMembers,
      averageClanSize,
      pendingApplications,
      levelDistribution
    };
  }

  // Утилиты
  private generateClanId(): string {
    return 'clan_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generateApplicationId(): string {
    return 'app_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private isClanNameTaken(name: string): boolean {
    return Array.from(this.clans.values()).some(clan => 
      clan.name.toLowerCase() === name.toLowerCase()
    );
  }

  private isClanTagTaken(tag: string): boolean {
    return Array.from(this.clans.values()).some(clan => 
      clan.tag.toLowerCase() === tag.toLowerCase()
    );
  }

  private updateClanStats(clan: Clan): void {
    if (clan.members.length === 0) return;

    // Здесь должна быть логика обновления статистики на основе данных участников
    // Пока оставляем заглушку
    clan.updatedAt = new Date();
  }

  private calculateExperienceForLevel(level: number): number {
    return Math.floor(1000 * Math.pow(level, 1.5));
  }

  private updateClanPerks(clan: Clan): void {
    // Улучшаем перки в зависимости от уровня
    clan.perks.experienceBonus = Math.min(5 + clan.level, 25); // Максимум 25%
    clan.perks.dailyTaskBonus = Math.min(10 + clan.level * 2, 50); // Максимум 50%
    clan.perks.tournamentBonus = Math.min(15 + clan.level * 3, 75); // Максимум 75%
  }

  /**
   * Очищает старые данные
   */
  cleanup(): number {
    let cleaned = 0;
    const now = Date.now();
    const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 дней

    // Удаляем старые отклоненные заявки
    for (const [appId, application] of this.applications.entries()) {
      if (application.status === 'rejected' &&
          now - application.createdAt.getTime() > maxAge) {
        this.applications.delete(appId);
        cleaned++;
      }
    }

    return cleaned;
  }
}
