import { v4 as uuidv4 } from 'uuid';
import { Room, RoomStatus } from '../rooms/RoomManager';
import { Player } from '../players/PlayerManager';

// Импортируем настоящее игровое ядро
import {
  DurakGame,
  BotFactory,
  BotDifficulty,
  GameStatus,
  PlayerAction,
  DurakVariant,
  GameEventData,
  GameEvent
} from '@kozyr-master/core';

// Интерфейс для игрового состояния
interface GameState {
  gameStatus: GameStatus;
  players: any[];
  currentPlayerIndex: number;
  attackerIndex: number;
  defenderIndex: number;
  tableCards: any[];
  deck: any[];
  trumpCard: any;
  trumpSuit: string;
  winner?: any;
}

interface GameMove {
  playerId: string;
  action: string;
  cardIndex?: number;
  timestamp: Date;
}

export interface MultiplayerGame {
  id: string;
  roomId: string;
  players: Player[];
  durakGame: DurakGame; // Настоящая игра
  gameState: GameState;
  moves: GameMove[];
  startedAt: Date;
  finishedAt?: Date;
  winner?: Player;
  status: 'waiting' | 'playing' | 'finished';
  eventHandlers: Map<string, (eventData: GameEventData) => void>;
}

export class GameManager {
  private games: Map<string, MultiplayerGame> = new Map(); // gameId -> Game
  private roomGames: Map<string, string> = new Map(); // roomId -> gameId

  /**
   * Запускает новую игру в комнате
   */
  startGame(room: Room): MultiplayerGame {
    // Проверяем, что в комнате достаточно игроков
    if (room.players.length < 2) {
      throw new Error('Not enough players to start the game');
    }

    // Проверяем, что игра еще не запущена
    if (this.roomGames.has(room.id)) {
      throw new Error('Game is already running in this room');
    }

    // Создаем игроков для DurakGame
    const durakPlayers = room.players.map(player => ({
      id: player.id,
      name: player.name,
      hand: [],
      isActive: false
    }));

    // Настройки игры
    const rules = {
      variant: DurakVariant.CLASSIC,
      numberOfPlayers: room.players.length,
      initialHandSize: 6,
      attackLimit: 6,
    };

    // Создаем настоящую игру
    const durakGame = new DurakGame(durakPlayers, rules);

    // Создаем мультиплеерную обертку
    const game: MultiplayerGame = {
      id: uuidv4(),
      roomId: room.id,
      players: [...room.players],
      durakGame,
      gameState: this.convertDurakStateToGameState(durakGame.getState()),
      moves: [],
      startedAt: new Date(),
      status: 'playing',
      eventHandlers: new Map()
    };

    // Настраиваем обработчики событий
    this.setupGameEventHandlers(game);

    // Запускаем игру
    durakGame.startGame();

    this.games.set(game.id, game);
    this.roomGames.set(room.id, game.id);

    // Обновляем статус комнаты
    room.status = RoomStatus.PLAYING;
    room.gameId = game.id;

    return game;
  }

  /**
   * Делает ход в игре
   */
  makeMove(gameId: string, playerId: string, action: string, cardIndex?: number): boolean {
    const game = this.games.get(gameId);
    if (!game) return false;

    // Проверяем, что игра активна
    if (game.status !== 'playing') return false;

    // Проверяем, что игрок участвует в игре
    const player = game.players.find(p => p.id === playerId);
    if (!player) return false;

    // Преобразуем строковое действие в PlayerAction
    let playerAction: PlayerAction;
    switch (action.toLowerCase()) {
      case 'attack':
        playerAction = PlayerAction.ATTACK;
        break;
      case 'defend':
        playerAction = PlayerAction.DEFEND;
        break;
      case 'take':
        playerAction = PlayerAction.TAKE;
        break;
      case 'pass':
        playerAction = PlayerAction.PASS;
        break;
      default:
        return false; // Неизвестное действие
    }

    // Делаем ход в настоящей игре
    const success = game.durakGame.makeMove(playerId, playerAction, cardIndex);

    if (success) {
      // Создаем запись хода
      const move: GameMove = {
        playerId,
        action,
        cardIndex,
        timestamp: new Date()
      };

      // Добавляем ход в историю
      game.moves.push(move);

      // Обновляем состояние
      game.gameState = this.convertDurakStateToGameState(game.durakGame.getState());
    }

    return success;
  }

  /**
   * Получает игру по ID
   */
  getGame(gameId: string): MultiplayerGame | undefined {
    return this.games.get(gameId);
  }

  /**
   * Получает игру по комнате
   */
  getGameByRoom(roomId: string): MultiplayerGame | undefined {
    const gameId = this.roomGames.get(roomId);
    return gameId ? this.games.get(gameId) : undefined;
  }

  /**
   * Завершает игру
   */
  finishGame(gameId: string, winner?: Player): boolean {
    const game = this.games.get(gameId);
    if (!game) return false;

    game.status = 'finished';
    game.finishedAt = new Date();
    game.winner = winner;

    return true;
  }

  /**
   * Получает все игры
   */
  getAllGames(): MultiplayerGame[] {
    return Array.from(this.games.values());
  }

  /**
   * Получает активные игры
   */
  getActiveGames(): MultiplayerGame[] {
    return this.getAllGames().filter(game => game.status === 'playing');
  }

  /**
   * Получает статистику
   */
  getStats() {
    const games = this.getAllGames();
    const activeGames = games.filter(g => g.status === 'playing');
    const finishedGames = games.filter(g => g.status === 'finished');

    return {
      total: games.length,
      active: activeGames.length,
      finished: finishedGames.length,
      averageGameDuration: this.calculateAverageGameDuration(finishedGames)
    };
  }

  /**
   * Настраивает обработчики событий игры
   */
  private setupGameEventHandlers(game: MultiplayerGame): void {
    const eventHandler = (eventData: GameEventData) => {
      // Обновляем состояние игры
      game.gameState = this.convertDurakStateToGameState(eventData.gameState);

      // Обрабатываем разные типы событий
      switch (eventData.type) {
        case GameEvent.GAME_ENDED:
          game.status = 'finished';
          game.finishedAt = new Date();
          if (eventData.gameState.winner && eventData.gameState.winner.id) {
            game.winner = game.players.find(p => p.id === eventData.gameState.winner!.id);
          }
          break;
      }
    };

    game.durakGame.addEventListener(eventHandler);
    game.eventHandlers.set('main', eventHandler);
  }

  /**
   * Конвертирует состояние DurakGame в GameState
   */
  private convertDurakStateToGameState(durakState: any): GameState {
    return {
      gameStatus: durakState.gameStatus,
      players: durakState.players,
      currentPlayerIndex: durakState.currentPlayerIndex,
      attackerIndex: durakState.attackerIndex,
      defenderIndex: durakState.defenderIndex,
      tableCards: durakState.tableCards,
      deck: durakState.deck,
      trumpCard: durakState.trumpCard,
      trumpSuit: durakState.trumpSuit,
      winner: durakState.winner
    };
  }



  /**
   * Вычисляет среднюю продолжительность игры
   */
  private calculateAverageGameDuration(games: MultiplayerGame[]): number {
    if (games.length === 0) return 0;

    const totalDuration = games.reduce((sum, game) => {
      if (game.finishedAt) {
        return sum + (game.finishedAt.getTime() - game.startedAt.getTime());
      }
      return sum;
    }, 0);

    return Math.round(totalDuration / games.length / 1000); // в секундах
  }

  /**
   * Очищает старые завершенные игры
   */
  cleanupFinishedGames(maxAge: number = 24 * 60 * 60 * 1000): number { // 24 часа
    const now = new Date();
    let cleaned = 0;

    for (const [gameId, game] of this.games.entries()) {
      if (game.status === 'finished' && game.finishedAt) {
        const age = now.getTime() - game.finishedAt.getTime();
        if (age > maxAge) {
          this.games.delete(gameId);
          this.roomGames.delete(game.roomId);
          cleaned++;
        }
      }
    }

    return cleaned;
  }
}

// Функция для получения публичного состояния игры
export function getGamePublicState(game: MultiplayerGame): any {
  // Создаем безопасную копию состояния игры для отправки клиентам
  const publicGameState = {
    ...game.gameState,
    players: game.gameState.players.map((player: any, index: number) => {
      // Для каждого игрока показываем только его собственные карты
      return {
        id: player.id,
        name: player.name,
        handSize: player.hand.length, // Количество карт вместо самих карт
        hand: player.hand, // Карты будут фильтроваться на клиенте
        isActive: player.isActive
      };
    })
  };

  return {
    id: game.id,
    roomId: game.roomId,
    players: game.players.map(p => ({
      id: p.id,
      name: p.name
    })),
    gameState: publicGameState,
    status: game.status,
    startedAt: game.startedAt,
    winner: game.winner ? {
      id: game.winner.id,
      name: game.winner.name
    } : undefined,
    moveCount: game.moves.length
  };
}
