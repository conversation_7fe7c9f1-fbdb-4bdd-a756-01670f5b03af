import { v4 as uuidv4 } from 'uuid';
import { Room, RoomStatus } from '../rooms/RoomManager';
import { Player } from '../players/PlayerManager';

// Временные заглушки для игрового ядра (пока не интегрируем настоящее)
interface GameState {
  gameStatus: string;
  players: any[];
  currentPlayerIndex: number;
  tableCards: any[];
  deck: any[];
  trumpCard: any;
  trumpSuit: string;
}

interface GameMove {
  playerId: string;
  action: string;
  cardIndex?: number;
  timestamp: Date;
}

export interface MultiplayerGame {
  id: string;
  roomId: string;
  players: Player[];
  gameState: GameState;
  moves: GameMove[];
  startedAt: Date;
  finishedAt?: Date;
  winner?: Player;
  status: 'waiting' | 'playing' | 'finished';
}

export class GameManager {
  private games: Map<string, MultiplayerGame> = new Map(); // gameId -> Game
  private roomGames: Map<string, string> = new Map(); // roomId -> gameId

  /**
   * Запускает новую игру в комнате
   */
  startGame(room: Room): MultiplayerGame {
    // Проверяем, что в комнате достаточно игроков
    if (room.players.length < 2) {
      throw new Error('Not enough players to start the game');
    }

    // Проверяем, что игра еще не запущена
    if (this.roomGames.has(room.id)) {
      throw new Error('Game is already running in this room');
    }

    // Создаем новую игру
    const game: MultiplayerGame = {
      id: uuidv4(),
      roomId: room.id,
      players: [...room.players],
      gameState: this.createInitialGameState(room.players),
      moves: [],
      startedAt: new Date(),
      status: 'playing'
    };

    this.games.set(game.id, game);
    this.roomGames.set(room.id, game.id);

    // Обновляем статус комнаты
    room.status = RoomStatus.PLAYING;
    room.gameId = game.id;

    return game;
  }

  /**
   * Делает ход в игре
   */
  makeMove(gameId: string, playerId: string, action: string, cardIndex?: number): boolean {
    const game = this.games.get(gameId);
    if (!game) return false;

    // Проверяем, что игра активна
    if (game.status !== 'playing') return false;

    // Проверяем, что игрок участвует в игре
    const player = game.players.find(p => p.id === playerId);
    if (!player) return false;

    // Создаем ход
    const move: GameMove = {
      playerId,
      action,
      cardIndex,
      timestamp: new Date()
    };

    // Добавляем ход в историю
    game.moves.push(move);

    // Обновляем игровое состояние (заглушка)
    this.updateGameState(game, move);

    return true;
  }

  /**
   * Получает игру по ID
   */
  getGame(gameId: string): MultiplayerGame | undefined {
    return this.games.get(gameId);
  }

  /**
   * Получает игру по комнате
   */
  getGameByRoom(roomId: string): MultiplayerGame | undefined {
    const gameId = this.roomGames.get(roomId);
    return gameId ? this.games.get(gameId) : undefined;
  }

  /**
   * Завершает игру
   */
  finishGame(gameId: string, winner?: Player): boolean {
    const game = this.games.get(gameId);
    if (!game) return false;

    game.status = 'finished';
    game.finishedAt = new Date();
    game.winner = winner;

    return true;
  }

  /**
   * Получает все игры
   */
  getAllGames(): MultiplayerGame[] {
    return Array.from(this.games.values());
  }

  /**
   * Получает активные игры
   */
  getActiveGames(): MultiplayerGame[] {
    return this.getAllGames().filter(game => game.status === 'playing');
  }

  /**
   * Получает статистику
   */
  getStats() {
    const games = this.getAllGames();
    const activeGames = games.filter(g => g.status === 'playing');
    const finishedGames = games.filter(g => g.status === 'finished');

    return {
      total: games.length,
      active: activeGames.length,
      finished: finishedGames.length,
      averageGameDuration: this.calculateAverageGameDuration(finishedGames)
    };
  }

  /**
   * Создает начальное состояние игры
   */
  private createInitialGameState(players: Player[]): GameState {
    // Заглушка для начального состояния
    return {
      gameStatus: 'playing',
      players: players.map((player, index) => ({
        id: player.id,
        name: player.name,
        hand: this.generateInitialHand(),
        isActive: index === 0
      })),
      currentPlayerIndex: 0,
      tableCards: [],
      deck: this.generateDeck(),
      trumpCard: { rank: '6', suit: '♠' },
      trumpSuit: '♠'
    };
  }

  /**
   * Обновляет состояние игры после хода
   */
  private updateGameState(game: MultiplayerGame, move: GameMove): void {
    // Заглушка для обновления состояния
    const { gameState } = game;
    
    switch (move.action) {
      case 'attack':
        if (move.cardIndex !== undefined) {
          const player = gameState.players.find(p => p.id === move.playerId);
          if (player && player.hand[move.cardIndex]) {
            const card = player.hand.splice(move.cardIndex, 1)[0];
            gameState.tableCards.push([card]);
          }
        }
        break;
      
      case 'defend':
        // Логика защиты
        break;
      
      case 'take':
        // Логика взятия карт
        break;
      
      case 'pass':
        // Логика паса
        break;
    }

    // Переключаем ход
    gameState.currentPlayerIndex = (gameState.currentPlayerIndex + 1) % gameState.players.length;

    // Проверяем условия окончания игры
    this.checkGameEnd(game);
  }

  /**
   * Проверяет условия окончания игры
   */
  private checkGameEnd(game: MultiplayerGame): void {
    const { gameState } = game;
    
    // Проверяем, есть ли игрок без карт
    const playersWithoutCards = gameState.players.filter(p => p.hand.length === 0);
    
    if (playersWithoutCards.length > 0) {
      // Игра окончена
      game.status = 'finished';
      game.finishedAt = new Date();
      game.winner = game.players.find(p => p.id === playersWithoutCards[0].id);
      gameState.gameStatus = 'finished';
    }
  }

  /**
   * Генерирует начальную руку
   */
  private generateInitialHand(): any[] {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    const hand = [];

    for (let i = 0; i < 6; i++) {
      hand.push({
        rank: ranks[Math.floor(Math.random() * ranks.length)],
        suit: suits[Math.floor(Math.random() * suits.length)]
      });
    }

    return hand;
  }

  /**
   * Генерирует колоду
   */
  private generateDeck(): any[] {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    const deck = [];

    for (const suit of suits) {
      for (const rank of ranks) {
        deck.push({ rank, suit });
      }
    }

    return deck.slice(12); // Убираем розданные карты
  }

  /**
   * Вычисляет среднюю продолжительность игры
   */
  private calculateAverageGameDuration(games: MultiplayerGame[]): number {
    if (games.length === 0) return 0;

    const totalDuration = games.reduce((sum, game) => {
      if (game.finishedAt) {
        return sum + (game.finishedAt.getTime() - game.startedAt.getTime());
      }
      return sum;
    }, 0);

    return Math.round(totalDuration / games.length / 1000); // в секундах
  }

  /**
   * Очищает старые завершенные игры
   */
  cleanupFinishedGames(maxAge: number = 24 * 60 * 60 * 1000): number { // 24 часа
    const now = new Date();
    let cleaned = 0;

    for (const [gameId, game] of this.games.entries()) {
      if (game.status === 'finished' && game.finishedAt) {
        const age = now.getTime() - game.finishedAt.getTime();
        if (age > maxAge) {
          this.games.delete(gameId);
          this.roomGames.delete(game.roomId);
          cleaned++;
        }
      }
    }

    return cleaned;
  }
}

// Функция для получения публичного состояния игры
export function getGamePublicState(game: MultiplayerGame): any {
  return {
    id: game.id,
    roomId: game.roomId,
    players: game.players.map(p => ({
      id: p.id,
      name: p.name
    })),
    gameState: game.gameState,
    status: game.status,
    startedAt: game.startedAt,
    winner: game.winner ? {
      id: game.winner.id,
      name: game.winner.name
    } : undefined
  };
}
