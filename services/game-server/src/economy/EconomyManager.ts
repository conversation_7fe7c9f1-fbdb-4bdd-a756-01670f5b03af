export interface Currency {
  coins: number;        // Основная валюта (зарабатывается в играх)
  gems: number;         // Премиум валюта (покупается за реальные деньги)
  tokens: number;       // Турнирная валюта (за участие в турнирах)
  seasonPoints: number; // Сезонные очки (за события)
}

export interface PlayerWallet {
  playerId: string;
  playerName: string;
  currency: Currency;
  totalEarned: Currency;
  totalSpent: Currency;
  transactions: Transaction[];
  lastUpdated: Date;
}

export interface Transaction {
  id: string;
  type: 'earn' | 'spend' | 'transfer' | 'purchase' | 'reward';
  currency: keyof Currency;
  amount: number;
  reason: string;
  source?: string;
  targetPlayer?: string;
  metadata?: any;
  timestamp: Date;
}

export interface ShopItem {
  id: string;
  name: string;
  description: string;
  category: 'cosmetic' | 'gameplay' | 'premium' | 'bundle' | 'seasonal';
  type: 'card_back' | 'avatar' | 'title' | 'emote' | 'boost' | 'chest' | 'subscription';
  rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
  price: Partial<Currency>;
  icon: string;
  preview?: string;
  isLimited: boolean;
  limitedQuantity?: number;
  soldQuantity: number;
  availableFrom?: Date;
  availableUntil?: Date;
  requirements?: {
    minLevel?: number;
    minRating?: number;
    achievements?: string[];
    clanMember?: boolean;
  };
  effects?: {
    experienceBoost?: number;
    coinBoost?: number;
    duration?: number; // в часах
  };
  bundle?: {
    items: string[];
    discount: number; // процент скидки
  };
  isActive: boolean;
  createdAt: Date;
}

export interface PlayerInventory {
  playerId: string;
  items: InventoryItem[];
  activeItems: {
    cardBack?: string;
    avatar?: string;
    title?: string;
  };
  lastUpdated: Date;
}

export interface InventoryItem {
  itemId: string;
  quantity: number;
  acquiredAt: Date;
  expiresAt?: Date;
  isActive: boolean;
  metadata?: any;
}

export interface DailyReward {
  day: number;
  reward: Partial<Currency>;
  item?: string;
  claimed: boolean;
  claimedAt?: Date;
}

export interface PlayerDailyRewards {
  playerId: string;
  currentStreak: number;
  longestStreak: number;
  lastClaimDate?: Date;
  rewards: DailyReward[];
  nextResetDate: Date;
}

export class EconomyManager {
  private wallets: Map<string, PlayerWallet> = new Map();
  private inventories: Map<string, PlayerInventory> = new Map();
  private shopItems: Map<string, ShopItem> = new Map();
  private dailyRewards: Map<string, PlayerDailyRewards> = new Map();
  private transactionCounter = 0;

  constructor() {
    this.initializeShop();
  }

  /**
   * Получает кошелек игрока
   */
  getPlayerWallet(playerId: string, playerName: string): PlayerWallet {
    let wallet = this.wallets.get(playerId);
    
    if (!wallet) {
      wallet = {
        playerId,
        playerName,
        currency: { coins: 100, gems: 0, tokens: 0, seasonPoints: 0 }, // Стартовая валюта
        totalEarned: { coins: 100, gems: 0, tokens: 0, seasonPoints: 0 },
        totalSpent: { coins: 0, gems: 0, tokens: 0, seasonPoints: 0 },
        transactions: [],
        lastUpdated: new Date()
      };
      this.wallets.set(playerId, wallet);
    }
    
    return wallet;
  }

  /**
   * Добавляет валюту игроку
   */
  addCurrency(
    playerId: string, 
    playerName: string,
    currency: keyof Currency, 
    amount: number, 
    reason: string,
    source?: string
  ): boolean {
    if (amount <= 0) return false;

    const wallet = this.getPlayerWallet(playerId, playerName);
    
    wallet.currency[currency] += amount;
    wallet.totalEarned[currency] += amount;
    wallet.lastUpdated = new Date();

    // Записываем транзакцию
    const transaction: Transaction = {
      id: this.generateTransactionId(),
      type: 'earn',
      currency,
      amount,
      reason,
      source,
      timestamp: new Date()
    };
    
    wallet.transactions.push(transaction);
    
    return true;
  }

  /**
   * Тратит валюту игрока
   */
  spendCurrency(
    playerId: string,
    currency: keyof Currency,
    amount: number,
    reason: string,
    targetPlayer?: string
  ): boolean {
    const wallet = this.wallets.get(playerId);
    if (!wallet) return false;

    if (wallet.currency[currency] < amount) {
      return false; // Недостаточно средств
    }

    wallet.currency[currency] -= amount;
    wallet.totalSpent[currency] += amount;
    wallet.lastUpdated = new Date();

    // Записываем транзакцию
    const transaction: Transaction = {
      id: this.generateTransactionId(),
      type: 'spend',
      currency,
      amount,
      reason,
      targetPlayer,
      timestamp: new Date()
    };
    
    wallet.transactions.push(transaction);
    
    return true;
  }

  /**
   * Переводит валюту между игроками
   */
  transferCurrency(
    fromPlayerId: string,
    toPlayerId: string,
    toPlayerName: string,
    currency: keyof Currency,
    amount: number
  ): boolean {
    // Проверяем возможность перевода (только монеты можно переводить)
    if (currency !== 'coins') {
      return false;
    }

    const fromWallet = this.wallets.get(fromPlayerId);
    if (!fromWallet || fromWallet.currency[currency] < amount) {
      return false;
    }

    const toWallet = this.getPlayerWallet(toPlayerId, toPlayerName);

    // Выполняем перевод
    fromWallet.currency[currency] -= amount;
    toWallet.currency[currency] += amount;

    const now = new Date();
    fromWallet.lastUpdated = now;
    toWallet.lastUpdated = now;

    // Записываем транзакции
    const transferId = this.generateTransactionId();
    
    fromWallet.transactions.push({
      id: transferId + '_out',
      type: 'transfer',
      currency,
      amount: -amount,
      reason: `Перевод игроку ${toPlayerName}`,
      targetPlayer: toPlayerId,
      timestamp: now
    });

    toWallet.transactions.push({
      id: transferId + '_in',
      type: 'transfer',
      currency,
      amount,
      reason: `Перевод от игрока ${fromWallet.playerName}`,
      targetPlayer: fromPlayerId,
      timestamp: now
    });

    return true;
  }

  /**
   * Покупает предмет в магазине
   */
  purchaseItem(playerId: string, itemId: string): { success: boolean; error?: string } {
    const wallet = this.wallets.get(playerId);
    const item = this.shopItems.get(itemId);

    if (!wallet) {
      return { success: false, error: 'Player wallet not found' };
    }

    if (!item || !item.isActive) {
      return { success: false, error: 'Item not available' };
    }

    // Проверяем требования
    if (!this.checkItemRequirements(playerId, item)) {
      return { success: false, error: 'Requirements not met' };
    }

    // Проверяем наличие средств
    for (const [currency, price] of Object.entries(item.price)) {
      if (price && wallet.currency[currency as keyof Currency] < price) {
        return { success: false, error: `Insufficient ${currency}` };
      }
    }

    // Проверяем лимиты
    if (item.isLimited && item.limitedQuantity && item.soldQuantity >= item.limitedQuantity) {
      return { success: false, error: 'Item sold out' };
    }

    // Выполняем покупку
    for (const [currency, price] of Object.entries(item.price)) {
      if (price) {
        wallet.currency[currency as keyof Currency] -= price;
        wallet.totalSpent[currency as keyof Currency] += price;
      }
    }

    // Добавляем предмет в инвентарь
    this.addItemToInventory(playerId, itemId);

    // Обновляем статистику продаж
    item.soldQuantity++;

    // Записываем транзакцию
    wallet.transactions.push({
      id: this.generateTransactionId(),
      type: 'purchase',
      currency: 'coins', // Основная валюта для записи
      amount: item.price.coins || 0,
      reason: `Покупка: ${item.name}`,
      metadata: { itemId, itemName: item.name },
      timestamp: new Date()
    });

    wallet.lastUpdated = new Date();

    return { success: true };
  }

  /**
   * Получает инвентарь игрока
   */
  getPlayerInventory(playerId: string): PlayerInventory {
    let inventory = this.inventories.get(playerId);
    
    if (!inventory) {
      inventory = {
        playerId,
        items: [],
        activeItems: {},
        lastUpdated: new Date()
      };
      this.inventories.set(playerId, inventory);
    }
    
    return inventory;
  }

  /**
   * Добавляет предмет в инвентарь
   */
  private addItemToInventory(playerId: string, itemId: string, quantity: number = 1): void {
    const inventory = this.getPlayerInventory(playerId);
    const existingItem = inventory.items.find(item => item.itemId === itemId);

    if (existingItem) {
      existingItem.quantity += quantity;
    } else {
      inventory.items.push({
        itemId,
        quantity,
        acquiredAt: new Date(),
        isActive: false
      });
    }

    inventory.lastUpdated = new Date();
  }

  /**
   * Активирует предмет
   */
  activateItem(playerId: string, itemId: string): boolean {
    const inventory = this.getPlayerInventory(playerId);
    const item = this.shopItems.get(itemId);
    const inventoryItem = inventory.items.find(item => item.itemId === itemId);

    if (!item || !inventoryItem || inventoryItem.quantity <= 0) {
      return false;
    }

    // Деактивируем предыдущий предмет того же типа
    if (item.type === 'card_back') {
      inventory.activeItems.cardBack = itemId;
    } else if (item.type === 'avatar') {
      inventory.activeItems.avatar = itemId;
    } else if (item.type === 'title') {
      inventory.activeItems.title = itemId;
    }

    inventoryItem.isActive = true;
    inventory.lastUpdated = new Date();

    return true;
  }

  /**
   * Получает ежедневные награды игрока
   */
  getDailyRewards(playerId: string): PlayerDailyRewards {
    let dailyRewards = this.dailyRewards.get(playerId);
    
    if (!dailyRewards) {
      dailyRewards = {
        playerId,
        currentStreak: 0,
        longestStreak: 0,
        rewards: this.generateDailyRewards(),
        nextResetDate: this.getNextResetDate()
      };
      this.dailyRewards.set(playerId, dailyRewards);
    }
    
    return dailyRewards;
  }

  /**
   * Забирает ежедневную награду
   */
  claimDailyReward(playerId: string, playerName: string, day: number): boolean {
    const dailyRewards = this.getDailyRewards(playerId);
    const today = new Date();
    
    // Проверяем, можно ли забрать награду
    if (dailyRewards.lastClaimDate) {
      const lastClaim = new Date(dailyRewards.lastClaimDate);
      const daysDiff = Math.floor((today.getTime() - lastClaim.getTime()) / (1000 * 60 * 60 * 24));
      
      if (daysDiff === 0) {
        return false; // Уже забрал сегодня
      }
      
      if (daysDiff > 1) {
        // Серия прервана
        dailyRewards.currentStreak = 0;
      }
    }

    const reward = dailyRewards.rewards[day - 1];
    if (!reward || reward.claimed) {
      return false;
    }

    // Выдаем награду
    for (const [currency, amount] of Object.entries(reward.reward)) {
      if (amount) {
        this.addCurrency(playerId, playerName, currency as keyof Currency, amount, `Ежедневная награда день ${day}`);
      }
    }

    if (reward.item) {
      this.addItemToInventory(playerId, reward.item);
    }

    // Обновляем статистику
    reward.claimed = true;
    reward.claimedAt = today;
    dailyRewards.lastClaimDate = today;
    dailyRewards.currentStreak++;
    
    if (dailyRewards.currentStreak > dailyRewards.longestStreak) {
      dailyRewards.longestStreak = dailyRewards.currentStreak;
    }

    return true;
  }

  /**
   * Награждает игрока за игровые действия
   */
  rewardGameAction(playerId: string, playerName: string, action: string, metadata?: any): void {
    const rewards = this.getGameActionRewards(action, metadata);
    
    for (const [currency, amount] of Object.entries(rewards)) {
      if (amount > 0) {
        this.addCurrency(playerId, playerName, currency as keyof Currency, amount, `Награда за: ${action}`);
      }
    }
  }

  /**
   * Получает награды за игровые действия
   */
  private getGameActionRewards(action: string, metadata?: any): Partial<Currency> {
    const rewards: Partial<Currency> = {};

    switch (action) {
      case 'game_win':
        rewards.coins = 10;
        if (metadata?.gameType === 'poker') rewards.coins = 15;
        if (metadata?.gameType === 'preferans') rewards.coins = 20;
        break;
      
      case 'game_loss':
        rewards.coins = 3;
        break;
      
      case 'tournament_win':
        rewards.coins = 100;
        rewards.tokens = 10;
        break;
      
      case 'tournament_participation':
        rewards.tokens = 2;
        break;
      
      case 'achievement_unlock':
        rewards.coins = metadata?.rarity === 'legendary' ? 50 : 
                       metadata?.rarity === 'epic' ? 30 : 
                       metadata?.rarity === 'rare' ? 20 : 10;
        break;
      
      case 'level_up':
        rewards.coins = (metadata?.newLevel || 1) * 5;
        break;
      
      case 'daily_login':
        rewards.coins = 5;
        break;
      
      case 'clan_contribution':
        rewards.coins = 15;
        break;
    }

    return rewards;
  }

  /**
   * Инициализирует магазин
   */
  private initializeShop(): void {
    const items: ShopItem[] = [
      // Косметические предметы
      {
        id: 'card_back_royal',
        name: 'Королевская рубашка',
        description: 'Элегантная рубашка карт с золотым узором',
        category: 'cosmetic',
        type: 'card_back',
        rarity: 'epic',
        price: { coins: 500 },
        icon: '🃏',
        isLimited: false,
        soldQuantity: 0,
        isActive: true,
        createdAt: new Date()
      },
      {
        id: 'avatar_crown',
        name: 'Корона',
        description: 'Аватар с золотой короной',
        category: 'cosmetic',
        type: 'avatar',
        rarity: 'legendary',
        price: { gems: 100 },
        icon: '👑',
        isLimited: false,
        soldQuantity: 0,
        isActive: true,
        createdAt: new Date()
      },
      
      // Игровые усиления
      {
        id: 'exp_boost_2x',
        name: 'Удвоение опыта',
        description: 'Удваивает получаемый опыт на 24 часа',
        category: 'gameplay',
        type: 'boost',
        rarity: 'rare',
        price: { gems: 50 },
        icon: '⚡',
        isLimited: false,
        soldQuantity: 0,
        effects: { experienceBoost: 100, duration: 24 },
        isActive: true,
        createdAt: new Date()
      },
      
      // Премиум подписка
      {
        id: 'premium_month',
        name: 'Премиум на месяц',
        description: 'Премиум статус на 30 дней с бонусами',
        category: 'premium',
        type: 'subscription',
        rarity: 'legendary',
        price: { gems: 500 },
        icon: '💎',
        isLimited: false,
        soldQuantity: 0,
        effects: { experienceBoost: 50, coinBoost: 25, duration: 720 },
        isActive: true,
        createdAt: new Date()
      }
    ];

    items.forEach(item => {
      this.shopItems.set(item.id, item);
    });
  }

  /**
   * Проверяет требования для покупки предмета
   */
  private checkItemRequirements(playerId: string, item: ShopItem): boolean {
    if (!item.requirements) return true;

    // Здесь должна быть проверка требований через другие менеджеры
    // Пока возвращаем true
    return true;
  }

  /**
   * Генерирует ежедневные награды
   */
  private generateDailyRewards(): DailyReward[] {
    const rewards: DailyReward[] = [];
    
    for (let day = 1; day <= 7; day++) {
      rewards.push({
        day,
        reward: {
          coins: day * 10,
          gems: day === 7 ? 10 : 0
        },
        item: day === 7 ? 'card_back_royal' : undefined,
        claimed: false
      });
    }
    
    return rewards;
  }

  /**
   * Получает дату следующего сброса
   */
  private getNextResetDate(): Date {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    return tomorrow;
  }

  /**
   * Генерирует ID транзакции
   */
  private generateTransactionId(): string {
    return `tx_${Date.now()}_${++this.transactionCounter}`;
  }

  /**
   * Получает статистику экономики
   */
  getEconomyStats() {
    const totalPlayers = this.wallets.size;
    const totalCurrency = Array.from(this.wallets.values()).reduce((acc, wallet) => {
      acc.coins += wallet.currency.coins;
      acc.gems += wallet.currency.gems;
      acc.tokens += wallet.currency.tokens;
      acc.seasonPoints += wallet.currency.seasonPoints;
      return acc;
    }, { coins: 0, gems: 0, tokens: 0, seasonPoints: 0 });

    const totalTransactions = Array.from(this.wallets.values())
      .reduce((sum, wallet) => sum + wallet.transactions.length, 0);

    const shopStats = Array.from(this.shopItems.values()).reduce((acc, item) => {
      acc.totalItems++;
      acc.totalSold += item.soldQuantity;
      return acc;
    }, { totalItems: 0, totalSold: 0 });

    return {
      totalPlayers,
      totalCurrency,
      totalTransactions,
      shopStats
    };
  }

  /**
   * Получает список предметов магазина
   */
  getShopItems(category?: string): ShopItem[] {
    const items = Array.from(this.shopItems.values()).filter(item => item.isActive);
    
    if (category) {
      return items.filter(item => item.category === category);
    }
    
    return items;
  }
}
