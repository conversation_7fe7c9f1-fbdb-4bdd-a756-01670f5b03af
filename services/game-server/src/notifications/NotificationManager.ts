export interface GameNotification {
  id: string;
  playerId: string;
  type: 'achievement' | 'level_up' | 'friend_request' | 'game_invitation' | 'tournament' | 'system' | 'daily_task' | 'title_unlocked';
  title: string;
  message: string;
  icon: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'game' | 'social' | 'progress' | 'system';
  data?: any;
  read: boolean;
  dismissed: boolean;
  createdAt: Date;
  expiresAt?: Date;
  actionRequired?: boolean;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  id: string;
  label: string;
  type: 'primary' | 'secondary' | 'danger';
  action: string;
  data?: any;
}

export interface NotificationSettings {
  playerId: string;
  enabledTypes: string[];
  soundEnabled: boolean;
  desktopEnabled: boolean;
  emailEnabled: boolean;
  quietHours: {
    enabled: boolean;
    start: string; // HH:MM
    end: string;   // HH:MM
  };
  categories: {
    game: boolean;
    social: boolean;
    progress: boolean;
    system: boolean;
  };
}

export class NotificationManager {
  private notifications: Map<string, GameNotification[]> = new Map(); // playerId -> notifications
  private settings: Map<string, NotificationSettings> = new Map(); // playerId -> settings
  private globalNotifications: GameNotification[] = []; // Системные уведомления для всех

  /**
   * Создает уведомление для игрока
   */
  createNotification(notification: Omit<GameNotification, 'id' | 'createdAt' | 'read' | 'dismissed'>): GameNotification {
    const fullNotification: GameNotification = {
      id: this.generateNotificationId(),
      createdAt: new Date(),
      read: false,
      dismissed: false,
      ...notification
    };

    // Проверяем настройки игрока
    const settings = this.getPlayerSettings(notification.playerId);
    if (!this.shouldSendNotification(fullNotification, settings)) {
      return fullNotification; // Создаем, но не отправляем
    }

    // Добавляем в список уведомлений игрока
    if (!this.notifications.has(notification.playerId)) {
      this.notifications.set(notification.playerId, []);
    }

    const playerNotifications = this.notifications.get(notification.playerId)!;
    playerNotifications.unshift(fullNotification); // Добавляем в начало

    // Ограничиваем количество уведомлений
    if (playerNotifications.length > 100) {
      playerNotifications.splice(100);
    }

    return fullNotification;
  }

  /**
   * Создает системное уведомление для всех игроков
   */
  createGlobalNotification(notification: Omit<GameNotification, 'id' | 'playerId' | 'createdAt' | 'read' | 'dismissed'>): GameNotification {
    const globalNotification: GameNotification = {
      id: this.generateNotificationId(),
      playerId: 'global',
      createdAt: new Date(),
      read: false,
      dismissed: false,
      ...notification
    };

    this.globalNotifications.unshift(globalNotification);

    // Ограничиваем количество глобальных уведомлений
    if (this.globalNotifications.length > 50) {
      this.globalNotifications.splice(50);
    }

    return globalNotification;
  }

  /**
   * Получает уведомления игрока
   */
  getPlayerNotifications(playerId: string, options: {
    unreadOnly?: boolean;
    category?: string;
    limit?: number;
    includeGlobal?: boolean;
  } = {}): GameNotification[] {
    
    const playerNotifications = this.notifications.get(playerId) || [];
    let allNotifications = [...playerNotifications];

    // Добавляем глобальные уведомления
    if (options.includeGlobal !== false) {
      allNotifications = [...this.globalNotifications, ...allNotifications];
    }

    // Фильтруем по прочитанности
    if (options.unreadOnly) {
      allNotifications = allNotifications.filter(n => !n.read);
    }

    // Фильтруем по категории
    if (options.category) {
      allNotifications = allNotifications.filter(n => n.category === options.category);
    }

    // Фильтруем истекшие
    const now = new Date();
    allNotifications = allNotifications.filter(n => !n.expiresAt || n.expiresAt > now);

    // Сортируем по приоритету и времени
    allNotifications.sort((a, b) => {
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
      const aPriority = priorityOrder[a.priority];
      const bPriority = priorityOrder[b.priority];
      
      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }
      
      return b.createdAt.getTime() - a.createdAt.getTime();
    });

    // Ограничиваем количество
    if (options.limit) {
      allNotifications = allNotifications.slice(0, options.limit);
    }

    return allNotifications;
  }

  /**
   * Помечает уведомление как прочитанное
   */
  markAsRead(playerId: string, notificationId: string): boolean {
    const notifications = this.notifications.get(playerId);
    if (!notifications) return false;

    const notification = notifications.find(n => n.id === notificationId);
    if (!notification) {
      // Проверяем глобальные уведомления
      const globalNotification = this.globalNotifications.find(n => n.id === notificationId);
      if (globalNotification) {
        globalNotification.read = true;
        return true;
      }
      return false;
    }

    notification.read = true;
    return true;
  }

  /**
   * Помечает уведомление как отклоненное
   */
  dismissNotification(playerId: string, notificationId: string): boolean {
    const notifications = this.notifications.get(playerId);
    if (!notifications) return false;

    const notification = notifications.find(n => n.id === notificationId);
    if (!notification) return false;

    notification.dismissed = true;
    return true;
  }

  /**
   * Помечает все уведомления как прочитанные
   */
  markAllAsRead(playerId: string): number {
    const notifications = this.notifications.get(playerId) || [];
    let count = 0;

    notifications.forEach(notification => {
      if (!notification.read) {
        notification.read = true;
        count++;
      }
    });

    // Также помечаем глобальные как прочитанные для этого игрока
    this.globalNotifications.forEach(notification => {
      if (!notification.read) {
        notification.read = true;
        count++;
      }
    });

    return count;
  }

  /**
   * Получает настройки уведомлений игрока
   */
  getPlayerSettings(playerId: string): NotificationSettings {
    if (!this.settings.has(playerId)) {
      const defaultSettings: NotificationSettings = {
        playerId,
        enabledTypes: ['achievement', 'level_up', 'friend_request', 'game_invitation', 'tournament', 'daily_task', 'title_unlocked'],
        soundEnabled: true,
        desktopEnabled: true,
        emailEnabled: false,
        quietHours: {
          enabled: false,
          start: '22:00',
          end: '08:00'
        },
        categories: {
          game: true,
          social: true,
          progress: true,
          system: true
        }
      };
      this.settings.set(playerId, defaultSettings);
    }
    return this.settings.get(playerId)!;
  }

  /**
   * Обновляет настройки уведомлений игрока
   */
  updatePlayerSettings(playerId: string, updates: Partial<NotificationSettings>): NotificationSettings {
    const currentSettings = this.getPlayerSettings(playerId);
    const newSettings = { ...currentSettings, ...updates };
    this.settings.set(playerId, newSettings);
    return newSettings;
  }

  /**
   * Проверяет, нужно ли отправлять уведомление
   */
  private shouldSendNotification(notification: GameNotification, settings: NotificationSettings): boolean {
    // Проверяем, включен ли тип уведомления
    if (!settings.enabledTypes.includes(notification.type)) {
      return false;
    }

    // Проверяем, включена ли категория
    if (!settings.categories[notification.category]) {
      return false;
    }

    // Проверяем тихие часы
    if (settings.quietHours.enabled && this.isQuietHours(settings.quietHours)) {
      // Отправляем только срочные уведомления в тихие часы
      return notification.priority === 'urgent';
    }

    return true;
  }

  /**
   * Проверяет, сейчас ли тихие часы
   */
  private isQuietHours(quietHours: NotificationSettings['quietHours']): boolean {
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    const [startHour, startMin] = quietHours.start.split(':').map(Number);
    const [endHour, endMin] = quietHours.end.split(':').map(Number);
    
    const startTime = startHour * 60 + startMin;
    const endTime = endHour * 60 + endMin;

    if (startTime <= endTime) {
      // Обычный случай (например, 22:00 - 08:00 следующего дня)
      return currentTime >= startTime && currentTime <= endTime;
    } else {
      // Переход через полночь (например, 22:00 - 08:00)
      return currentTime >= startTime || currentTime <= endTime;
    }
  }

  /**
   * Создает уведомление о достижении
   */
  createAchievementNotification(playerId: string, achievementName: string, achievementIcon: string): GameNotification {
    return this.createNotification({
      playerId,
      type: 'achievement',
      title: 'Достижение разблокировано!',
      message: `Вы получили достижение "${achievementName}"`,
      icon: achievementIcon,
      priority: 'high',
      category: 'progress',
      data: { achievementName }
    });
  }

  /**
   * Создает уведомление о повышении уровня
   */
  createLevelUpNotification(playerId: string, newLevel: number, newTitles?: string[]): GameNotification {
    let message = `Поздравляем! Вы достигли ${newLevel} уровня!`;
    if (newTitles && newTitles.length > 0) {
      message += ` Разблокированы новые титулы: ${newTitles.join(', ')}`;
    }

    return this.createNotification({
      playerId,
      type: 'level_up',
      title: 'Повышение уровня!',
      message,
      icon: '🎉',
      priority: 'high',
      category: 'progress',
      data: { newLevel, newTitles }
    });
  }

  /**
   * Создает уведомление о выполнении ежедневного задания
   */
  createDailyTaskNotification(playerId: string, taskName: string, experienceGained: number): GameNotification {
    return this.createNotification({
      playerId,
      type: 'daily_task',
      title: 'Задание выполнено!',
      message: `Выполнено: "${taskName}". Получено ${experienceGained} опыта.`,
      icon: '✅',
      priority: 'medium',
      category: 'progress',
      data: { taskName, experienceGained }
    });
  }

  /**
   * Создает уведомление о разблокировке титула
   */
  createTitleUnlockedNotification(playerId: string, titleName: string, titleIcon: string): GameNotification {
    return this.createNotification({
      playerId,
      type: 'title_unlocked',
      title: 'Новый титул!',
      message: `Разблокирован титул "${titleName}"`,
      icon: titleIcon,
      priority: 'high',
      category: 'progress',
      data: { titleName }
    });
  }

  /**
   * Получает статистику уведомлений
   */
  getNotificationStats() {
    let totalNotifications = 0;
    let unreadCount = 0;
    let byType: Record<string, number> = {};
    let byCategory: Record<string, number> = {};

    // Подсчитываем уведомления игроков
    for (const notifications of this.notifications.values()) {
      totalNotifications += notifications.length;
      
      notifications.forEach(notification => {
        if (!notification.read) unreadCount++;
        
        byType[notification.type] = (byType[notification.type] || 0) + 1;
        byCategory[notification.category] = (byCategory[notification.category] || 0) + 1;
      });
    }

    // Добавляем глобальные уведомления
    totalNotifications += this.globalNotifications.length;
    this.globalNotifications.forEach(notification => {
      if (!notification.read) unreadCount++;
      
      byType[notification.type] = (byType[notification.type] || 0) + 1;
      byCategory[notification.category] = (byCategory[notification.category] || 0) + 1;
    });

    return {
      totalNotifications,
      unreadCount,
      totalPlayers: this.notifications.size,
      byType,
      byCategory,
      globalNotifications: this.globalNotifications.length
    };
  }

  /**
   * Очищает старые уведомления
   */
  cleanup(): number {
    let cleaned = 0;
    const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 дней
    const now = Date.now();

    // Очищаем уведомления игроков
    for (const [playerId, notifications] of this.notifications.entries()) {
      const originalLength = notifications.length;
      
      // Удаляем старые и отклоненные уведомления
      const filtered = notifications.filter(notification => {
        const age = now - notification.createdAt.getTime();
        return age < maxAge && !notification.dismissed;
      });
      
      this.notifications.set(playerId, filtered);
      cleaned += originalLength - filtered.length;
    }

    // Очищаем глобальные уведомления
    const originalGlobalLength = this.globalNotifications.length;
    this.globalNotifications = this.globalNotifications.filter(notification => {
      const age = now - notification.createdAt.getTime();
      return age < maxAge && !notification.dismissed;
    });
    cleaned += originalGlobalLength - this.globalNotifications.length;

    return cleaned;
  }

  // Утилиты
  private generateNotificationId(): string {
    return 'notif_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}
