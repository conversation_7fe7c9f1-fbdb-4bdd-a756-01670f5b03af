import { Card, Suit, Rank, CardUtils } from '../../types/Card';
import { DurakGame, DurakPlayer, DurakGameState, AttackCard, DefenseCard } from '../durak/DurakGame';

export interface TransferablePlayer extends DurakPlayer {
  canTransfer: boolean;
  transferredCards: Card[];
}

export interface TransferableGameState extends DurakGameState {
  players: TransferablePlayer[];
  transferPhase: boolean;
  transferTarget?: string;
  originalAttacker?: string;
  transferHistory: TransferMove[];
}

export interface TransferMove {
  fromPlayer: string;
  toPlayer: string;
  cards: Card[];
  timestamp: Date;
}

export class DurakTransferableGame extends DurakGame {
  protected gameState: TransferableGameState;

  constructor(gameId: string) {
    super(gameId);
    
    // Преобразуем состояние для переводного дурака
    this.gameState = {
      ...this.gameState,
      transferPhase: false,
      transferHistory: []
    } as TransferableGameState;
  }

  /**
   * Добавляет игрока в игру (переопределяем для TransferablePlayer)
   */
  addPlayer(playerId: string, playerName: string): boolean {
    if (this.gameState.players.length >= 6) {
      return false;
    }

    if (this.gameState.players.some(p => p.id === playerId)) {
      return false;
    }

    const player: TransferablePlayer = {
      id: playerId,
      name: playerName,
      hand: [],
      isActive: true,
      isDefending: false,
      isAttacking: false,
      hasPassedTurn: false,
      canTransfer: false,
      transferredCards: []
    };

    this.gameState.players.push(player);
    return true;
  }

  /**
   * Начинает новую игру (переопределяем для сброса переводных полей)
   */
  startNewGame(): void {
    super.startNewGame();
    
    // Сбрасываем переводные поля
    this.gameState.transferPhase = false;
    this.gameState.transferTarget = undefined;
    this.gameState.originalAttacker = undefined;
    this.gameState.transferHistory = [];
    
    this.gameState.players.forEach(player => {
      (player as TransferablePlayer).canTransfer = false;
      (player as TransferablePlayer).transferredCards = [];
    });
  }

  /**
   * Начинает новый раунд (переопределяем для переводного дурака)
   */
  protected startNewRound(): void {
    super.startNewRound();
    
    // Сбрасываем переводные поля для нового раунда
    this.gameState.transferPhase = false;
    this.gameState.transferTarget = undefined;
    this.gameState.originalAttacker = undefined;
    
    this.gameState.players.forEach(player => {
      (player as TransferablePlayer).canTransfer = false;
      (player as TransferablePlayer).transferredCards = [];
    });

    // В переводном дураке защищающийся может переводить
    const defender = this.getDefender();
    if (defender) {
      (defender as TransferablePlayer).canTransfer = true;
    }
  }

  /**
   * Атакует картами (переопределяем для поддержки перевода)
   */
  attack(playerId: string, cards: Card[]): boolean {
    // Если идет фаза перевода, атака невозможна
    if (this.gameState.transferPhase) {
      return false;
    }

    return super.attack(playerId, cards);
  }

  /**
   * Защищается картами (переопределяем для поддержки перевода)
   */
  defend(playerId: string, defenses: DefenseCard[]): boolean {
    // Если идет фаза перевода, защита невозможна
    if (this.gameState.transferPhase) {
      return false;
    }

    return super.defend(playerId, defenses);
  }

  /**
   * Переводит карты на следующего игрока
   */
  transfer(playerId: string, cards: Card[]): boolean {
    const player = this.gameState.players.find(p => p.id === playerId) as TransferablePlayer;
    if (!player) {
      return false;
    }

    // Проверяем, может ли игрок переводить
    if (!player.canTransfer) {
      return false;
    }

    // Проверяем, что игрок защищается
    if (!player.isDefending) {
      return false;
    }

    // Проверяем валидность перевода
    if (!this.isValidTransfer(cards)) {
      return false;
    }

    // Проверяем, что у игрока есть эти карты
    for (const card of cards) {
      if (!this.hasCard(player, card)) {
        return false;
      }
    }

    // Определяем цель перевода (следующий игрок по часовой стрелке)
    const transferTarget = this.getNextActivePlayer(player);
    if (!transferTarget) {
      return false;
    }

    // Проверяем, что у цели достаточно карт для приема перевода
    const totalCardsToDefend = this.gameState.attackCards.length + cards.length;
    if (transferTarget.hand.length < totalCardsToDefend) {
      return false;
    }

    // Выполняем перевод
    this.executeTransfer(player, transferTarget, cards);

    return true;
  }

  /**
   * Проверяет валидность перевода
   */
  private isValidTransfer(cards: Card[]): boolean {
    // Можно переводить только картами того же ранга, что и атакующие
    const attackRanks = this.gameState.attackCards.map(ac => ac.rank);
    
    for (const card of cards) {
      if (!attackRanks.includes(card.rank)) {
        return false;
      }
    }

    // Количество переводимых карт не должно превышать количество атакующих
    if (cards.length > this.gameState.attackCards.length) {
      return false;
    }

    return true;
  }

  /**
   * Выполняет перевод карт
   */
  private executeTransfer(fromPlayer: TransferablePlayer, toPlayer: TransferablePlayer, cards: Card[]): void {
    // Убираем карты из руки переводящего
    for (const card of cards) {
      const cardIndex = fromPlayer.hand.findIndex(c => 
        c.suit === card.suit && c.rank === card.rank
      );
      if (cardIndex !== -1) {
        fromPlayer.hand.splice(cardIndex, 1);
      }
    }

    // Добавляем переведенные карты к атакующим
    for (const card of cards) {
      this.gameState.attackCards.push({
        suit: card.suit,
        rank: card.rank,
        playerId: fromPlayer.id
      });
    }

    // Сохраняем информацию о переводе
    fromPlayer.transferredCards.push(...cards);
    
    const transferMove: TransferMove = {
      fromPlayer: fromPlayer.id,
      toPlayer: toPlayer.id,
      cards: [...cards],
      timestamp: new Date()
    };
    this.gameState.transferHistory.push(transferMove);

    // Меняем роли игроков
    fromPlayer.isDefending = false;
    fromPlayer.canTransfer = false;
    
    toPlayer.isDefending = true;
    (toPlayer as TransferablePlayer).canTransfer = true;

    // Устанавливаем фазу перевода
    this.gameState.transferPhase = true;
    this.gameState.transferTarget = toPlayer.id;
    
    if (!this.gameState.originalAttacker) {
      this.gameState.originalAttacker = this.getAttacker()?.id;
    }

    // Обновляем текущего игрока
    this.gameState.currentPlayer = toPlayer.id;

    // Логируем перевод
    this.gameState.gameLog.push({
      type: 'transfer',
      playerId: fromPlayer.id,
      playerName: fromPlayer.name,
      cards: [...cards],
      targetPlayer: toPlayer.name,
      timestamp: new Date()
    });
  }

  /**
   * Получает следующего активного игрока
   */
  private getNextActivePlayer(currentPlayer: TransferablePlayer): TransferablePlayer | null {
    const currentIndex = this.gameState.players.indexOf(currentPlayer);
    const activePlayers = this.gameState.players.filter(p => p.isActive);
    
    for (let i = 1; i < activePlayers.length; i++) {
      const nextIndex = (currentIndex + i) % this.gameState.players.length;
      const nextPlayer = this.gameState.players[nextIndex] as TransferablePlayer;
      
      if (nextPlayer.isActive && !nextPlayer.isAttacking) {
        return nextPlayer;
      }
    }
    
    return null;
  }

  /**
   * Берет карты (переопределяем для обработки перевода)
   */
  takeCards(playerId: string): boolean {
    const player = this.gameState.players.find(p => p.id === playerId) as TransferablePlayer;
    if (!player) {
      return false;
    }

    // Если игрок может переводить, сначала нужно решить - переводить или брать
    if (player.canTransfer && this.gameState.attackCards.length > 0) {
      // Игрок выбрал взять карты вместо перевода
      player.canTransfer = false;
    }

    // Выполняем стандартное взятие карт
    const result = super.takeCards(playerId);
    
    if (result) {
      // Сбрасываем состояние перевода
      this.gameState.transferPhase = false;
      this.gameState.transferTarget = undefined;
      this.gameState.originalAttacker = undefined;
      
      this.gameState.players.forEach(p => {
        (p as TransferablePlayer).canTransfer = false;
        (p as TransferablePlayer).transferredCards = [];
      });
    }

    return result;
  }

  /**
   * Завершает ход (переопределяем для обработки перевода)
   */
  endTurn(playerId: string): boolean {
    // Если идет фаза перевода, завершение хода означает отказ от перевода
    if (this.gameState.transferPhase) {
      const player = this.gameState.players.find(p => p.id === playerId) as TransferablePlayer;
      if (player && player.canTransfer) {
        // Игрок отказался от перевода, теперь должен защищаться обычным способом
        player.canTransfer = false;
        this.gameState.transferPhase = false;
        return true;
      }
    }

    return super.endTurn(playerId);
  }

  /**
   * Получает доступные действия для игрока (переопределяем)
   */
  getAvailableActions(playerId: string): string[] {
    const baseActions = super.getAvailableActions(playerId);
    const player = this.gameState.players.find(p => p.id === playerId) as TransferablePlayer;
    
    if (!player) {
      return baseActions;
    }

    // Если игрок может переводить, добавляем действие перевода
    if (player.canTransfer && this.gameState.attackCards.length > 0) {
      return ['transfer', 'defend', 'take_cards'];
    }

    return baseActions;
  }

  /**
   * Получает карты, которые можно использовать для перевода
   */
  getTransferableCards(playerId: string): Card[] {
    const player = this.gameState.players.find(p => p.id === playerId) as TransferablePlayer;
    if (!player || !player.canTransfer) {
      return [];
    }

    const attackRanks = this.gameState.attackCards.map(ac => ac.rank);
    return player.hand.filter(card => attackRanks.includes(card.rank));
  }

  /**
   * Проверяет, может ли игрок переводить
   */
  canPlayerTransfer(playerId: string): boolean {
    const player = this.gameState.players.find(p => p.id === playerId) as TransferablePlayer;
    return player ? player.canTransfer : false;
  }

  /**
   * Получает историю переводов в текущем раунде
   */
  getTransferHistory(): TransferMove[] {
    return [...this.gameState.transferHistory];
  }

  /**
   * Получает публичное состояние игры (переопределяем)
   */
  getPublicGameState(playerId?: string): any {
    const publicState = super.getPublicGameState(playerId);
    
    return {
      ...publicState,
      transferPhase: this.gameState.transferPhase,
      transferTarget: this.gameState.transferTarget,
      transferHistory: this.gameState.transferHistory,
      players: this.gameState.players.map(player => {
        const transferablePlayer = player as TransferablePlayer;
        return {
          ...player,
          hand: player.id === playerId ? player.hand : player.hand.map(() => ({ suit: 'hidden', rank: 'hidden' })),
          canTransfer: transferablePlayer.canTransfer,
          transferredCards: transferablePlayer.transferredCards
        };
      })
    };
  }

  /**
   * Получает статистику переводов
   */
  getTransferStats(): any {
    const stats = {
      totalTransfers: this.gameState.transferHistory.length,
      transfersByPlayer: {} as Record<string, number>,
      mostTransferredRank: null as Rank | null,
      averageTransferSize: 0
    };

    // Подсчитываем переводы по игрокам
    for (const transfer of this.gameState.transferHistory) {
      stats.transfersByPlayer[transfer.fromPlayer] = 
        (stats.transfersByPlayer[transfer.fromPlayer] || 0) + 1;
    }

    // Находим самый часто переводимый ранг
    const rankCounts: Record<Rank, number> = {} as Record<Rank, number>;
    let totalCards = 0;

    for (const transfer of this.gameState.transferHistory) {
      for (const card of transfer.cards) {
        rankCounts[card.rank] = (rankCounts[card.rank] || 0) + 1;
        totalCards++;
      }
    }

    if (totalCards > 0) {
      stats.averageTransferSize = totalCards / this.gameState.transferHistory.length;
      
      let maxCount = 0;
      for (const [rank, count] of Object.entries(rankCounts)) {
        if (count > maxCount) {
          maxCount = count;
          stats.mostTransferredRank = rank as Rank;
        }
      }
    }

    return stats;
  }
}
