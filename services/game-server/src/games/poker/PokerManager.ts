import { PokerGame, PokerGameState, PokerAction } from './PokerGame';
import { Player } from '../../players/PlayerManager';

export interface PokerRoom {
  id: string;
  name: string;
  game: PokerGame;
  players: Player[];
  maxPlayers: number;
  smallBlind: number;
  bigBlind: number;
  isPrivate: boolean;
  password?: string;
  status: 'waiting' | 'playing' | 'finished';
  createdAt: Date;
  createdBy: string;
}

export class PokerManager {
  private rooms: Map<string, PokerRoom> = new Map();
  private playerRooms: Map<string, string> = new Map(); // playerId -> roomId

  /**
   * Создает новую покерную комнату
   */
  createRoom(
    creator: Player, 
    roomName: string, 
    smallBlind: number = 5, 
    bigBlind: number = 10,
    maxPlayers: number = 9,
    isPrivate: boolean = false,
    password?: string
  ): PokerRoom {
    const roomId = this.generateRoomId();
    const game = new PokerGame(roomId, smallBlind, bigBlind);

    const room: PokerRoom = {
      id: roomId,
      name: roomN<PERSON>,
      game,
      players: [],
      maxPlayers,
      smallBlind,
      bigBlind,
      isPrivate,
      password,
      status: 'waiting',
      createdAt: new Date(),
      createdBy: creator.id
    };

    this.rooms.set(roomId, room);
    
    // Автоматически добавляем создателя в комнату
    this.joinRoom(roomId, creator);

    return room;
  }

  /**
   * Присоединяется к покерной комнате
   */
  joinRoom(roomId: string, player: Player, password?: string): boolean {
    const room = this.rooms.get(roomId);
    if (!room) {
      throw new Error('Room not found');
    }

    if (room.status !== 'waiting') {
      throw new Error('Game already in progress');
    }

    if (room.players.length >= room.maxPlayers) {
      throw new Error('Room is full');
    }

    if (room.isPrivate && room.password !== password) {
      throw new Error('Invalid password');
    }

    if (room.players.some(p => p.id === player.id)) {
      throw new Error('Player already in room');
    }

    // Проверяем, не находится ли игрок в другой комнате
    const currentRoom = this.playerRooms.get(player.id);
    if (currentRoom) {
      this.leaveRoom(currentRoom, player);
    }

    room.players.push(player);
    this.playerRooms.set(player.id, roomId);
    
    // Добавляем игрока в покерную игру
    room.game.addPlayer(player.id, player.name);

    return true;
  }

  /**
   * Покидает покерную комнату
   */
  leaveRoom(roomId: string, player: Player): boolean {
    const room = this.rooms.get(roomId);
    if (!room) {
      return false;
    }

    const playerIndex = room.players.findIndex(p => p.id === player.id);
    if (playerIndex === -1) {
      return false;
    }

    room.players.splice(playerIndex, 1);
    this.playerRooms.delete(player.id);

    // Если комната пустая, удаляем её
    if (room.players.length === 0) {
      this.rooms.delete(roomId);
    }

    return true;
  }

  /**
   * Начинает покерную игру
   */
  startGame(roomId: string, playerId: string): PokerGameState {
    const room = this.rooms.get(roomId);
    if (!room) {
      throw new Error('Room not found');
    }

    if (room.createdBy !== playerId) {
      throw new Error('Only room creator can start the game');
    }

    if (room.players.length < 2) {
      throw new Error('Need at least 2 players to start');
    }

    if (room.status !== 'waiting') {
      throw new Error('Game already started');
    }

    room.status = 'playing';
    room.game.startNewHand();

    return room.game.getGameState();
  }

  /**
   * Выполняет действие в покерной игре
   */
  makeAction(roomId: string, playerId: string, action: PokerAction): PokerGameState {
    const room = this.rooms.get(roomId);
    if (!room) {
      throw new Error('Room not found');
    }

    if (room.status !== 'playing') {
      throw new Error('Game not in progress');
    }

    const success = room.game.makeAction(playerId, action);
    if (!success) {
      throw new Error('Invalid action');
    }

    const gameState = room.game.getGameState();
    
    // Проверяем, завершилась ли игра
    if (gameState.phase === 'finished') {
      room.status = 'finished';
      
      // Можно автоматически начать новую руку
      setTimeout(() => {
        if (room.players.length >= 2) {
          room.status = 'playing';
          room.game.startNewHand();
        } else {
          room.status = 'waiting';
        }
      }, 5000); // 5 секунд между руками
    }

    return gameState;
  }

  /**
   * Получает состояние покерной игры
   */
  getGameState(roomId: string, playerId?: string): PokerGameState | null {
    const room = this.rooms.get(roomId);
    if (!room) {
      return null;
    }

    return room.game.getPublicGameState(playerId);
  }

  /**
   * Получает информацию о комнате
   */
  getRoom(roomId: string): PokerRoom | undefined {
    return this.rooms.get(roomId);
  }

  /**
   * Получает список публичных комнат
   */
  getPublicRooms(): PokerRoom[] {
    return Array.from(this.rooms.values())
      .filter(room => !room.isPrivate)
      .map(room => ({
        ...room,
        game: undefined as any // Не передаем игровое состояние в списке
      }));
  }

  /**
   * Получает комнаты игрока
   */
  getPlayerRooms(playerId: string): PokerRoom[] {
    const roomId = this.playerRooms.get(playerId);
    if (!roomId) {
      return [];
    }

    const room = this.rooms.get(roomId);
    return room ? [room] : [];
  }

  /**
   * Получает статистику покерных игр
   */
  getStats() {
    const totalRooms = this.rooms.size;
    const activeGames = Array.from(this.rooms.values()).filter(room => room.status === 'playing').length;
    const waitingRooms = Array.from(this.rooms.values()).filter(room => room.status === 'waiting').length;
    const totalPlayers = Array.from(this.rooms.values()).reduce((sum, room) => sum + room.players.length, 0);

    const blindLevels = Array.from(this.rooms.values()).reduce((acc, room) => {
      const key = `${room.smallBlind}/${room.bigBlind}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalRooms,
      activeGames,
      waitingRooms,
      totalPlayers,
      blindLevels
    };
  }

  /**
   * Очищает завершенные игры
   */
  cleanup(): number {
    let cleaned = 0;
    const now = Date.now();
    const maxAge = 60 * 60 * 1000; // 1 час

    for (const [roomId, room] of this.rooms.entries()) {
      // Удаляем старые завершенные игры без игроков
      if (room.status === 'finished' && 
          room.players.length === 0 && 
          now - room.createdAt.getTime() > maxAge) {
        this.rooms.delete(roomId);
        cleaned++;
      }
    }

    return cleaned;
  }

  /**
   * Получает публичную информацию о комнате
   */
  getRoomPublicInfo(room: PokerRoom): any {
    return {
      id: room.id,
      name: room.name,
      playerCount: room.players.length,
      maxPlayers: room.maxPlayers,
      smallBlind: room.smallBlind,
      bigBlind: room.bigBlind,
      status: room.status,
      isPrivate: room.isPrivate,
      createdAt: room.createdAt,
      players: room.players.map(player => ({
        id: player.id,
        name: player.name
      }))
    };
  }

  /**
   * Проверяет, может ли игрок выполнить действие
   */
  canPlayerAct(roomId: string, playerId: string): boolean {
    const room = this.rooms.get(roomId);
    if (!room || room.status !== 'playing') {
      return false;
    }

    const gameState = room.game.getGameState();
    const currentPlayer = gameState.players[gameState.currentPlayerIndex];
    
    return currentPlayer && currentPlayer.id === playerId && !currentPlayer.isFolded && !currentPlayer.isAllIn;
  }

  /**
   * Получает доступные действия для игрока
   */
  getAvailableActions(roomId: string, playerId: string): string[] {
    const room = this.rooms.get(roomId);
    if (!room || room.status !== 'playing') {
      return [];
    }

    const gameState = room.game.getGameState();
    const player = gameState.players.find(p => p.id === playerId);
    
    if (!player || player.isFolded || player.isAllIn) {
      return [];
    }

    const actions: string[] = ['fold'];
    const callAmount = gameState.currentBet - player.currentBet;

    if (callAmount === 0) {
      actions.push('check');
      if (gameState.currentBet === 0) {
        actions.push('bet');
      }
    } else {
      if (player.chips >= callAmount) {
        actions.push('call');
      }
      if (player.chips >= callAmount + gameState.minRaise) {
        actions.push('raise');
      }
    }

    if (player.chips > 0) {
      actions.push('all_in');
    }

    return actions;
  }

  /**
   * Получает рекомендуемые размеры ставок
   */
  getSuggestedBets(roomId: string, playerId: string): number[] {
    const room = this.rooms.get(roomId);
    if (!room) {
      return [];
    }

    const gameState = room.game.getGameState();
    const player = gameState.players.find(p => p.id === playerId);
    
    if (!player) {
      return [];
    }

    const pot = gameState.pot;
    const suggestions: number[] = [];

    // Минимальная ставка/рейз
    if (gameState.currentBet === 0) {
      suggestions.push(gameState.bigBlind);
    } else {
      suggestions.push(gameState.currentBet + gameState.minRaise);
    }

    // Размеры относительно банка
    const potSizes = [0.5, 0.75, 1, 1.5, 2];
    potSizes.forEach(size => {
      const amount = Math.round(pot * size);
      if (amount > suggestions[suggestions.length - 1] && amount <= player.chips) {
        suggestions.push(amount);
      }
    });

    // Олл-ин
    if (player.chips > suggestions[suggestions.length - 1]) {
      suggestions.push(player.chips);
    }

    return suggestions.slice(0, 5); // Максимум 5 предложений
  }

  // Утилиты
  private generateRoomId(): string {
    return 'poker_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}
