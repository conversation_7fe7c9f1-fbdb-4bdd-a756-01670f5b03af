import { Player } from '../players/PlayerManager';

export interface FriendRequest {
  id: string;
  fromPlayerId: string;
  fromPlayerName: string;
  toPlayerId: string;
  toPlayerName: string;
  status: 'pending' | 'accepted' | 'declined';
  createdAt: Date;
  respondedAt?: Date;
}

export interface Friendship {
  id: string;
  player1Id: string;
  player1Name: string;
  player2Id: string;
  player2Name: string;
  createdAt: Date;
  lastInteraction?: Date;
}

export interface GameInvitation {
  id: string;
  fromPlayerId: string;
  fromPlayerName: string;
  toPlayerId: string;
  toPlayerName: string;
  roomId?: string;
  roomName?: string;
  gameType: 'durak' | 'tournament';
  message?: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  createdAt: Date;
  expiresAt: Date;
  respondedAt?: Date;
}

export interface PlayerStatus {
  playerId: string;
  playerName: string;
  status: 'online' | 'in_game' | 'in_room' | 'spectating' | 'offline';
  lastSeen: Date;
  currentActivity?: {
    type: 'game' | 'room' | 'spectating';
    id: string;
    name?: string;
  };
}

export interface SocialNotification {
  id: string;
  playerId: string;
  type: 'friend_request' | 'friend_accepted' | 'game_invitation' | 'game_started' | 'achievement_unlocked';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: Date;
}

export class FriendsManager {
  private friendRequests: Map<string, FriendRequest> = new Map(); // requestId -> FriendRequest
  private friendships: Map<string, Friendship> = new Map(); // friendshipId -> Friendship
  private gameInvitations: Map<string, GameInvitation> = new Map(); // invitationId -> GameInvitation
  private playerStatuses: Map<string, PlayerStatus> = new Map(); // playerId -> PlayerStatus
  private notifications: Map<string, SocialNotification[]> = new Map(); // playerId -> notifications
  private playerFriends: Map<string, Set<string>> = new Map(); // playerId -> friendIds
  private playerRequests: Map<string, Set<string>> = new Map(); // playerId -> requestIds

  /**
   * Отправляет запрос на добавление в друзья
   */
  sendFriendRequest(fromPlayer: Player, toPlayerId: string, toPlayerName: string): FriendRequest {
    // Проверяем, что игрок не отправляет запрос самому себе
    if (fromPlayer.id === toPlayerId) {
      throw new Error('Cannot send friend request to yourself');
    }

    // Проверяем, не являются ли уже друзьями
    if (this.areFriends(fromPlayer.id, toPlayerId)) {
      throw new Error('Players are already friends');
    }

    // Проверяем, нет ли уже активного запроса
    const existingRequest = this.findPendingRequest(fromPlayer.id, toPlayerId);
    if (existingRequest) {
      throw new Error('Friend request already exists');
    }

    const request: FriendRequest = {
      id: this.generateRequestId(),
      fromPlayerId: fromPlayer.id,
      fromPlayerName: fromPlayer.name,
      toPlayerId,
      toPlayerName,
      status: 'pending',
      createdAt: new Date()
    };

    this.friendRequests.set(request.id, request);

    // Добавляем в индексы
    if (!this.playerRequests.has(toPlayerId)) {
      this.playerRequests.set(toPlayerId, new Set());
    }
    this.playerRequests.get(toPlayerId)!.add(request.id);

    // Создаем уведомление
    this.addNotification(toPlayerId, {
      type: 'friend_request',
      title: 'Новый запрос в друзья',
      message: `${fromPlayer.name} хочет добавить вас в друзья`,
      data: { requestId: request.id }
    });

    return request;
  }

  /**
   * Отвечает на запрос в друзья
   */
  respondToFriendRequest(requestId: string, playerId: string, accept: boolean): { request: FriendRequest; friendship?: Friendship } {
    const request = this.friendRequests.get(requestId);
    if (!request) {
      throw new Error('Friend request not found');
    }

    if (request.toPlayerId !== playerId) {
      throw new Error('Not authorized to respond to this request');
    }

    if (request.status !== 'pending') {
      throw new Error('Request already responded to');
    }

    request.status = accept ? 'accepted' : 'declined';
    request.respondedAt = new Date();

    // Удаляем из индекса запросов
    const playerRequests = this.playerRequests.get(playerId);
    if (playerRequests) {
      playerRequests.delete(requestId);
    }

    let friendship: Friendship | undefined;

    if (accept) {
      // Создаем дружбу
      friendship = this.createFriendship(request.fromPlayerId, request.fromPlayerName, request.toPlayerId, request.toPlayerName);

      // Уведомляем отправителя
      this.addNotification(request.fromPlayerId, {
        type: 'friend_accepted',
        title: 'Запрос принят',
        message: `${request.toPlayerName} принял ваш запрос в друзья`,
        data: { friendshipId: friendship.id }
      });
    }

    return { request, friendship };
  }

  /**
   * Создает дружбу между игроками
   */
  private createFriendship(player1Id: string, player1Name: string, player2Id: string, player2Name: string): Friendship {
    const friendship: Friendship = {
      id: this.generateFriendshipId(),
      player1Id,
      player1Name,
      player2Id,
      player2Name,
      createdAt: new Date()
    };

    this.friendships.set(friendship.id, friendship);

    // Добавляем в индексы друзей
    if (!this.playerFriends.has(player1Id)) {
      this.playerFriends.set(player1Id, new Set());
    }
    if (!this.playerFriends.has(player2Id)) {
      this.playerFriends.set(player2Id, new Set());
    }

    this.playerFriends.get(player1Id)!.add(player2Id);
    this.playerFriends.get(player2Id)!.add(player1Id);

    return friendship;
  }

  /**
   * Удаляет друга
   */
  removeFriend(playerId: string, friendId: string): boolean {
    if (!this.areFriends(playerId, friendId)) {
      return false;
    }

    // Находим дружбу
    const friendship = Array.from(this.friendships.values()).find(f => 
      (f.player1Id === playerId && f.player2Id === friendId) ||
      (f.player1Id === friendId && f.player2Id === playerId)
    );

    if (!friendship) {
      return false;
    }

    // Удаляем дружбу
    this.friendships.delete(friendship.id);

    // Удаляем из индексов
    this.playerFriends.get(playerId)?.delete(friendId);
    this.playerFriends.get(friendId)?.delete(playerId);

    return true;
  }

  /**
   * Отправляет приглашение в игру
   */
  sendGameInvitation(fromPlayer: Player, toPlayerId: string, toPlayerName: string, gameType: GameInvitation['gameType'], roomId?: string, roomName?: string, message?: string): GameInvitation {
    // Проверяем, что игроки друзья (опционально)
    // if (!this.areFriends(fromPlayer.id, toPlayerId)) {
    //   throw new Error('Can only invite friends to games');
    // }

    const invitation: GameInvitation = {
      id: this.generateInvitationId(),
      fromPlayerId: fromPlayer.id,
      fromPlayerName: fromPlayer.name,
      toPlayerId,
      toPlayerName,
      roomId,
      roomName,
      gameType,
      message,
      status: 'pending',
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 5 * 60 * 1000) // 5 минут
    };

    this.gameInvitations.set(invitation.id, invitation);

    // Создаем уведомление
    this.addNotification(toPlayerId, {
      type: 'game_invitation',
      title: 'Приглашение в игру',
      message: `${fromPlayer.name} приглашает вас в игру${roomName ? ` "${roomName}"` : ''}`,
      data: { invitationId: invitation.id }
    });

    return invitation;
  }

  /**
   * Отвечает на приглашение в игру
   */
  respondToGameInvitation(invitationId: string, playerId: string, accept: boolean): GameInvitation {
    const invitation = this.gameInvitations.get(invitationId);
    if (!invitation) {
      throw new Error('Game invitation not found');
    }

    if (invitation.toPlayerId !== playerId) {
      throw new Error('Not authorized to respond to this invitation');
    }

    if (invitation.status !== 'pending') {
      throw new Error('Invitation already responded to');
    }

    if (new Date() > invitation.expiresAt) {
      invitation.status = 'expired';
      throw new Error('Invitation has expired');
    }

    invitation.status = accept ? 'accepted' : 'declined';
    invitation.respondedAt = new Date();

    return invitation;
  }

  /**
   * Обновляет статус игрока
   */
  updatePlayerStatus(playerId: string, playerName: string, status: PlayerStatus['status'], activity?: PlayerStatus['currentActivity']): void {
    const playerStatus: PlayerStatus = {
      playerId,
      playerName,
      status,
      lastSeen: new Date(),
      currentActivity: activity
    };

    this.playerStatuses.set(playerId, playerStatus);
  }

  /**
   * Добавляет уведомление
   */
  addNotification(playerId: string, notification: Omit<SocialNotification, 'id' | 'playerId' | 'read' | 'createdAt'>): SocialNotification {
    const fullNotification: SocialNotification = {
      id: this.generateNotificationId(),
      playerId,
      read: false,
      createdAt: new Date(),
      ...notification
    };

    if (!this.notifications.has(playerId)) {
      this.notifications.set(playerId, []);
    }

    const playerNotifications = this.notifications.get(playerId)!;
    playerNotifications.push(fullNotification);

    // Ограничиваем количество уведомлений
    if (playerNotifications.length > 50) {
      playerNotifications.splice(0, playerNotifications.length - 50);
    }

    return fullNotification;
  }

  /**
   * Помечает уведомление как прочитанное
   */
  markNotificationAsRead(playerId: string, notificationId: string): boolean {
    const notifications = this.notifications.get(playerId);
    if (!notifications) return false;

    const notification = notifications.find(n => n.id === notificationId);
    if (!notification) return false;

    notification.read = true;
    return true;
  }

  /**
   * Помечает все уведомления как прочитанные
   */
  markAllNotificationsAsRead(playerId: string): number {
    const notifications = this.notifications.get(playerId);
    if (!notifications) return 0;

    let count = 0;
    notifications.forEach(notification => {
      if (!notification.read) {
        notification.read = true;
        count++;
      }
    });

    return count;
  }

  // Геттеры
  getFriends(playerId: string): PlayerStatus[] {
    const friendIds = this.playerFriends.get(playerId) || new Set();
    return Array.from(friendIds)
      .map(friendId => this.playerStatuses.get(friendId))
      .filter((status): status is PlayerStatus => status !== undefined);
  }

  getFriendRequests(playerId: string): FriendRequest[] {
    const requestIds = this.playerRequests.get(playerId) || new Set();
    return Array.from(requestIds)
      .map(requestId => this.friendRequests.get(requestId))
      .filter((request): request is FriendRequest => request !== undefined && request.status === 'pending');
  }

  getGameInvitations(playerId: string): GameInvitation[] {
    return Array.from(this.gameInvitations.values())
      .filter(invitation => invitation.toPlayerId === playerId && invitation.status === 'pending' && new Date() <= invitation.expiresAt);
  }

  getNotifications(playerId: string, unreadOnly: boolean = false): SocialNotification[] {
    const notifications = this.notifications.get(playerId) || [];
    return unreadOnly ? notifications.filter(n => !n.read) : notifications;
  }

  getPlayerStatus(playerId: string): PlayerStatus | undefined {
    return this.playerStatuses.get(playerId);
  }

  areFriends(playerId1: string, playerId2: string): boolean {
    const friends = this.playerFriends.get(playerId1);
    return friends ? friends.has(playerId2) : false;
  }

  private findPendingRequest(fromPlayerId: string, toPlayerId: string): FriendRequest | undefined {
    return Array.from(this.friendRequests.values()).find(request => 
      request.status === 'pending' && 
      ((request.fromPlayerId === fromPlayerId && request.toPlayerId === toPlayerId) ||
       (request.fromPlayerId === toPlayerId && request.toPlayerId === fromPlayerId))
    );
  }

  // Утилиты
  private generateRequestId(): string {
    return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generateFriendshipId(): string {
    return 'friend_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generateInvitationId(): string {
    return 'inv_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generateNotificationId(): string {
    return 'notif_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Очищает устаревшие данные
   */
  cleanup(): number {
    let cleaned = 0;
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 часа

    // Удаляем устаревшие приглашения
    for (const [id, invitation] of this.gameInvitations.entries()) {
      if (invitation.status === 'pending' && now > invitation.expiresAt.getTime()) {
        invitation.status = 'expired';
      }
      
      if (invitation.status !== 'pending' && now - invitation.createdAt.getTime() > maxAge) {
        this.gameInvitations.delete(id);
        cleaned++;
      }
    }

    // Удаляем старые запросы в друзья
    for (const [id, request] of this.friendRequests.entries()) {
      if (request.status !== 'pending' && now - request.createdAt.getTime() > maxAge) {
        this.friendRequests.delete(id);
        cleaned++;
      }
    }

    return cleaned;
  }

  /**
   * Получает статистику системы друзей
   */
  getStats() {
    const totalFriendships = this.friendships.size;
    const pendingRequests = Array.from(this.friendRequests.values()).filter(r => r.status === 'pending').length;
    const activeInvitations = Array.from(this.gameInvitations.values()).filter(i => i.status === 'pending').length;
    const onlinePlayers = Array.from(this.playerStatuses.values()).filter(s => s.status !== 'offline').length;

    return {
      totalFriendships,
      pendingRequests,
      activeInvitations,
      onlinePlayers,
      totalNotifications: Array.from(this.notifications.values()).reduce((sum, notifications) => sum + notifications.length, 0)
    };
  }
}
