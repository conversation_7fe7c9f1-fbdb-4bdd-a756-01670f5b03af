import { Player } from '../players/PlayerManager';
import { GameManager } from '../game/GameManager';
import { RatingManager } from '../rating/RatingManager';

export interface Tournament {
  id: string;
  name: string;
  description: string;
  type: 'single_elimination' | 'double_elimination' | 'round_robin';
  status: 'registration' | 'starting' | 'in_progress' | 'finished';
  maxParticipants: number;
  minParticipants: number;
  entryFee: number; // Рейтинговые очки для входа
  prizePool: {
    first: number;
    second: number;
    third: number;
  };
  participants: TournamentParticipant[];
  bracket: TournamentBracket;
  currentRound: number;
  totalRounds: number;
  createdAt: Date;
  startedAt?: Date;
  finishedAt?: Date;
  createdBy: string;
  settings: TournamentSettings;
}

export interface TournamentParticipant {
  playerId: string;
  playerName: string;
  rating: number;
  seed: number; // Позиция в турнирной сетке
  registeredAt: Date;
  status: 'registered' | 'active' | 'eliminated' | 'winner';
  currentMatch?: string;
  wins: number;
  losses: number;
}

export interface TournamentMatch {
  id: string;
  tournamentId: string;
  round: number;
  position: number; // Позиция в раунде
  player1?: TournamentParticipant;
  player2?: TournamentParticipant;
  winner?: TournamentParticipant;
  gameId?: string;
  status: 'pending' | 'ready' | 'in_progress' | 'finished';
  startedAt?: Date;
  finishedAt?: Date;
  nextMatchId?: string; // Следующий матч для победителя
}

export interface TournamentBracket {
  matches: Map<string, TournamentMatch>; // matchId -> match
  rounds: Map<number, string[]>; // round -> matchIds
}

export interface TournamentSettings {
  autoStart: boolean; // Автоматический старт при заполнении
  allowSpectators: boolean;
  timeLimit: number; // Лимит времени на игру в минутах
  ratingRestriction?: {
    min: number;
    max: number;
  };
}

export class TournamentManager {
  private tournaments: Map<string, Tournament> = new Map();
  private gameManager: GameManager;
  private ratingManager: RatingManager;

  constructor(gameManager: GameManager, ratingManager: RatingManager) {
    this.gameManager = gameManager;
    this.ratingManager = ratingManager;
  }

  /**
   * Создает новый турнир
   */
  createTournament(
    name: string,
    description: string,
    type: Tournament['type'],
    maxParticipants: number,
    createdBy: string,
    settings: Partial<TournamentSettings> = {}
  ): Tournament {
    
    const tournamentId = this.generateTournamentId();
    
    // Валидация размера турнира (должен быть степенью 2 для elimination)
    if ((type === 'single_elimination' || type === 'double_elimination') && 
        !this.isPowerOfTwo(maxParticipants)) {
      throw new Error('Tournament size must be a power of 2 for elimination tournaments');
    }

    const tournament: Tournament = {
      id: tournamentId,
      name,
      description,
      type,
      status: 'registration',
      maxParticipants,
      minParticipants: Math.max(2, Math.floor(maxParticipants / 4)),
      entryFee: this.calculateEntryFee(maxParticipants),
      prizePool: this.calculatePrizePool(maxParticipants),
      participants: [],
      bracket: {
        matches: new Map(),
        rounds: new Map()
      },
      currentRound: 0,
      totalRounds: this.calculateTotalRounds(type, maxParticipants),
      createdAt: new Date(),
      createdBy,
      settings: {
        autoStart: true,
        allowSpectators: true,
        timeLimit: 30,
        ...settings
      }
    };

    this.tournaments.set(tournamentId, tournament);
    return tournament;
  }

  /**
   * Регистрирует игрока на турнир
   */
  registerPlayer(tournamentId: string, player: Player): TournamentParticipant {
    const tournament = this.tournaments.get(tournamentId);
    if (!tournament) {
      throw new Error('Tournament not found');
    }

    if (tournament.status !== 'registration') {
      throw new Error('Tournament registration is closed');
    }

    if (tournament.participants.length >= tournament.maxParticipants) {
      throw new Error('Tournament is full');
    }

    // Проверяем, не зарегистрирован ли уже игрок
    if (tournament.participants.some(p => p.playerId === player.id)) {
      throw new Error('Player already registered');
    }

    // Проверяем рейтинговые ограничения
    const playerRating = this.ratingManager.getPlayerStats(player.id);
    if (tournament.settings.ratingRestriction && playerRating) {
      const { min, max } = tournament.settings.ratingRestriction;
      if (playerRating.rating < min || playerRating.rating > max) {
        throw new Error(`Player rating must be between ${min} and ${max}`);
      }
    }

    // Проверяем, хватает ли рейтинговых очков для входа
    if (playerRating && playerRating.rating < tournament.entryFee) {
      throw new Error(`Insufficient rating points. Required: ${tournament.entryFee}`);
    }

    const participant: TournamentParticipant = {
      playerId: player.id,
      playerName: player.name,
      rating: playerRating?.rating || 1200,
      seed: tournament.participants.length + 1,
      registeredAt: new Date(),
      status: 'registered',
      wins: 0,
      losses: 0
    };

    tournament.participants.push(participant);

    // Автоматический старт если турнир заполнен
    if (tournament.settings.autoStart && 
        tournament.participants.length === tournament.maxParticipants) {
      this.startTournament(tournamentId);
    }

    return participant;
  }

  /**
   * Запускает турнир
   */
  startTournament(tournamentId: string): Tournament {
    const tournament = this.tournaments.get(tournamentId);
    if (!tournament) {
      throw new Error('Tournament not found');
    }

    if (tournament.status !== 'registration') {
      throw new Error('Tournament cannot be started');
    }

    if (tournament.participants.length < tournament.minParticipants) {
      throw new Error(`Not enough participants. Minimum: ${tournament.minParticipants}`);
    }

    // Сортируем участников по рейтингу для сидинга
    tournament.participants.sort((a, b) => b.rating - a.rating);
    tournament.participants.forEach((participant, index) => {
      participant.seed = index + 1;
      participant.status = 'active';
    });

    // Создаем турнирную сетку
    this.generateBracket(tournament);

    tournament.status = 'starting';
    tournament.startedAt = new Date();
    tournament.currentRound = 1;

    // Запускаем первый раунд
    this.startRound(tournament, 1);

    return tournament;
  }

  /**
   * Генерирует турнирную сетку
   */
  private generateBracket(tournament: Tournament): void {
    switch (tournament.type) {
      case 'single_elimination':
        this.generateSingleEliminationBracket(tournament);
        break;
      case 'double_elimination':
        this.generateDoubleEliminationBracket(tournament);
        break;
      case 'round_robin':
        this.generateRoundRobinBracket(tournament);
        break;
    }
  }

  /**
   * Генерирует сетку на выбывание
   */
  private generateSingleEliminationBracket(tournament: Tournament): void {
    const participants = [...tournament.participants];
    const totalRounds = Math.log2(participants.length);
    
    // Создаем матчи для первого раунда
    const firstRoundMatches: TournamentMatch[] = [];
    
    for (let i = 0; i < participants.length; i += 2) {
      const matchId = this.generateMatchId();
      const match: TournamentMatch = {
        id: matchId,
        tournamentId: tournament.id,
        round: 1,
        position: Math.floor(i / 2) + 1,
        player1: participants[i],
        player2: participants[i + 1],
        status: 'pending'
      };
      
      firstRoundMatches.push(match);
      tournament.bracket.matches.set(matchId, match);
    }

    tournament.bracket.rounds.set(1, firstRoundMatches.map(m => m.id));

    // Создаем пустые матчи для следующих раундов
    for (let round = 2; round <= totalRounds; round++) {
      const roundMatches: string[] = [];
      const matchesInRound = Math.pow(2, totalRounds - round);
      
      for (let i = 0; i < matchesInRound; i++) {
        const matchId = this.generateMatchId();
        const match: TournamentMatch = {
          id: matchId,
          tournamentId: tournament.id,
          round,
          position: i + 1,
          status: 'pending'
        };
        
        tournament.bracket.matches.set(matchId, match);
        roundMatches.push(matchId);
      }
      
      tournament.bracket.rounds.set(round, roundMatches);
    }

    // Связываем матчи
    this.linkMatches(tournament);
  }

  /**
   * Связывает матчи в турнирной сетке
   */
  private linkMatches(tournament: Tournament): void {
    for (let round = 1; round < tournament.totalRounds; round++) {
      const currentRoundMatches = tournament.bracket.rounds.get(round) || [];
      const nextRoundMatches = tournament.bracket.rounds.get(round + 1) || [];
      
      for (let i = 0; i < currentRoundMatches.length; i += 2) {
        const match1 = tournament.bracket.matches.get(currentRoundMatches[i]);
        const match2 = tournament.bracket.matches.get(currentRoundMatches[i + 1]);
        const nextMatch = tournament.bracket.matches.get(nextRoundMatches[Math.floor(i / 2)]);
        
        if (match1 && nextMatch) {
          match1.nextMatchId = nextMatch.id;
        }
        if (match2 && nextMatch) {
          match2.nextMatchId = nextMatch.id;
        }
      }
    }
  }

  /**
   * Запускает раунд турнира
   */
  private startRound(tournament: Tournament, round: number): void {
    const roundMatches = tournament.bracket.rounds.get(round) || [];
    
    for (const matchId of roundMatches) {
      const match = tournament.bracket.matches.get(matchId);
      if (match && match.player1 && match.player2) {
        match.status = 'ready';
        // Здесь можно автоматически создавать игры
        // this.createGameForMatch(tournament, match);
      }
    }
    
    tournament.status = 'in_progress';
  }

  /**
   * Обрабатывает результат матча
   */
  processMatchResult(tournamentId: string, matchId: string, winnerId: string): void {
    const tournament = this.tournaments.get(tournamentId);
    if (!tournament) {
      throw new Error('Tournament not found');
    }

    const match = tournament.bracket.matches.get(matchId);
    if (!match) {
      throw new Error('Match not found');
    }

    if (match.status !== 'in_progress') {
      throw new Error('Match is not in progress');
    }

    // Определяем победителя и проигравшего
    let winner: TournamentParticipant;
    let loser: TournamentParticipant;

    if (match.player1?.playerId === winnerId) {
      winner = match.player1;
      loser = match.player2!;
    } else if (match.player2?.playerId === winnerId) {
      winner = match.player2;
      loser = match.player1!;
    } else {
      throw new Error('Invalid winner ID');
    }

    // Обновляем статистику
    winner.wins++;
    loser.losses++;
    
    match.winner = winner;
    match.status = 'finished';
    match.finishedAt = new Date();

    // Проигравший выбывает (для single elimination)
    if (tournament.type === 'single_elimination') {
      loser.status = 'eliminated';
    }

    // Продвигаем победителя в следующий раунд
    if (match.nextMatchId) {
      const nextMatch = tournament.bracket.matches.get(match.nextMatchId);
      if (nextMatch) {
        if (!nextMatch.player1) {
          nextMatch.player1 = winner;
        } else if (!nextMatch.player2) {
          nextMatch.player2 = winner;
          nextMatch.status = 'ready';
        }
      }
    } else {
      // Это финальный матч
      winner.status = 'winner';
      this.finishTournament(tournament);
    }

    // Проверяем, завершен ли текущий раунд
    this.checkRoundCompletion(tournament);
  }

  /**
   * Проверяет завершение раунда
   */
  private checkRoundCompletion(tournament: Tournament): void {
    const currentRoundMatches = tournament.bracket.rounds.get(tournament.currentRound) || [];
    const allFinished = currentRoundMatches.every(matchId => {
      const match = tournament.bracket.matches.get(matchId);
      return match?.status === 'finished';
    });

    if (allFinished && tournament.currentRound < tournament.totalRounds) {
      tournament.currentRound++;
      this.startRound(tournament, tournament.currentRound);
    }
  }

  /**
   * Завершает турнир
   */
  private finishTournament(tournament: Tournament): void {
    tournament.status = 'finished';
    tournament.finishedAt = new Date();

    // Распределяем призы
    this.distributePrizes(tournament);
  }

  /**
   * Распределяет призы
   */
  private distributePrizes(tournament: Tournament): void {
    const winner = tournament.participants.find(p => p.status === 'winner');
    if (winner) {
      // Добавляем рейтинговые очки победителю
      const winnerRating = this.ratingManager.getPlayerStats(winner.playerId);
      if (winnerRating) {
        // Здесь можно добавить специальную логику для турнирных призов
        console.log(`Tournament winner: ${winner.playerName}, prize: ${tournament.prizePool.first} rating points`);
      }
    }
  }

  // Утилиты
  private generateTournamentId(): string {
    return 'tournament_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generateMatchId(): string {
    return 'match_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private isPowerOfTwo(n: number): boolean {
    return n > 0 && (n & (n - 1)) === 0;
  }

  private calculateTotalRounds(type: Tournament['type'], participants: number): number {
    switch (type) {
      case 'single_elimination':
        return Math.log2(participants);
      case 'double_elimination':
        return Math.log2(participants) * 2 - 1;
      case 'round_robin':
        return participants - 1;
      default:
        return 1;
    }
  }

  private calculateEntryFee(participants: number): number {
    return Math.max(50, participants * 10);
  }

  private calculatePrizePool(participants: number): Tournament['prizePool'] {
    const total = participants * this.calculateEntryFee(participants);
    return {
      first: Math.floor(total * 0.5),
      second: Math.floor(total * 0.3),
      third: Math.floor(total * 0.2)
    };
  }

  // Геттеры
  getTournament(id: string): Tournament | undefined {
    return this.tournaments.get(id);
  }

  getAllTournaments(): Tournament[] {
    return Array.from(this.tournaments.values());
  }

  getActiveTournaments(): Tournament[] {
    return Array.from(this.tournaments.values())
      .filter(t => t.status === 'registration' || t.status === 'in_progress');
  }

  getPlayerTournaments(playerId: string): Tournament[] {
    return Array.from(this.tournaments.values())
      .filter(t => t.participants.some(p => p.playerId === playerId));
  }

  // Заглушки для дополнительных методов
  private generateDoubleEliminationBracket(tournament: Tournament): void {
    // TODO: Реализовать double elimination
    this.generateSingleEliminationBracket(tournament);
  }

  private generateRoundRobinBracket(tournament: Tournament): void {
    // TODO: Реализовать round robin
    this.generateSingleEliminationBracket(tournament);
  }
}
