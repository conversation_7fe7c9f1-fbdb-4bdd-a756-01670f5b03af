import { Player } from '../players/PlayerManager';
export interface FriendRequest {
    id: string;
    fromPlayerId: string;
    fromPlayerName: string;
    toPlayerId: string;
    toPlayerName: string;
    status: 'pending' | 'accepted' | 'declined';
    createdAt: Date;
    respondedAt?: Date;
}
export interface Friendship {
    id: string;
    player1Id: string;
    player1Name: string;
    player2Id: string;
    player2Name: string;
    createdAt: Date;
    lastInteraction?: Date;
}
export interface GameInvitation {
    id: string;
    fromPlayerId: string;
    fromPlayerName: string;
    toPlayerId: string;
    toPlayerName: string;
    roomId?: string;
    roomName?: string;
    gameType: 'durak' | 'tournament';
    message?: string;
    status: 'pending' | 'accepted' | 'declined' | 'expired';
    createdAt: Date;
    expiresAt: Date;
    respondedAt?: Date;
}
export interface PlayerStatus {
    playerId: string;
    playerName: string;
    status: 'online' | 'in_game' | 'in_room' | 'spectating' | 'offline';
    lastSeen: Date;
    currentActivity?: {
        type: 'game' | 'room' | 'spectating';
        id: string;
        name?: string;
    };
}
export interface SocialNotification {
    id: string;
    playerId: string;
    type: 'friend_request' | 'friend_accepted' | 'game_invitation' | 'game_started' | 'achievement_unlocked';
    title: string;
    message: string;
    data?: any;
    read: boolean;
    createdAt: Date;
}
export declare class FriendsManager {
    private friendRequests;
    private friendships;
    private gameInvitations;
    private playerStatuses;
    private notifications;
    private playerFriends;
    private playerRequests;
    /**
     * Отправляет запрос на добавление в друзья
     */
    sendFriendRequest(fromPlayer: Player, toPlayerId: string, toPlayerName: string): FriendRequest;
    /**
     * Отвечает на запрос в друзья
     */
    respondToFriendRequest(requestId: string, playerId: string, accept: boolean): {
        request: FriendRequest;
        friendship?: Friendship;
    };
    /**
     * Создает дружбу между игроками
     */
    private createFriendship;
    /**
     * Удаляет друга
     */
    removeFriend(playerId: string, friendId: string): boolean;
    /**
     * Отправляет приглашение в игру
     */
    sendGameInvitation(fromPlayer: Player, toPlayerId: string, toPlayerName: string, gameType: GameInvitation['gameType'], roomId?: string, roomName?: string, message?: string): GameInvitation;
    /**
     * Отвечает на приглашение в игру
     */
    respondToGameInvitation(invitationId: string, playerId: string, accept: boolean): GameInvitation;
    /**
     * Обновляет статус игрока
     */
    updatePlayerStatus(playerId: string, playerName: string, status: PlayerStatus['status'], activity?: PlayerStatus['currentActivity']): void;
    /**
     * Добавляет уведомление
     */
    addNotification(playerId: string, notification: Omit<SocialNotification, 'id' | 'playerId' | 'read' | 'createdAt'>): SocialNotification;
    /**
     * Помечает уведомление как прочитанное
     */
    markNotificationAsRead(playerId: string, notificationId: string): boolean;
    /**
     * Помечает все уведомления как прочитанные
     */
    markAllNotificationsAsRead(playerId: string): number;
    getFriends(playerId: string): PlayerStatus[];
    getFriendRequests(playerId: string): FriendRequest[];
    getGameInvitations(playerId: string): GameInvitation[];
    getNotifications(playerId: string, unreadOnly?: boolean): SocialNotification[];
    getPlayerStatus(playerId: string): PlayerStatus | undefined;
    areFriends(playerId1: string, playerId2: string): boolean;
    private findPendingRequest;
    private generateRequestId;
    private generateFriendshipId;
    private generateInvitationId;
    private generateNotificationId;
    /**
     * Очищает устаревшие данные
     */
    cleanup(): number;
    /**
     * Получает статистику системы друзей
     */
    getStats(): {
        totalFriendships: number;
        pendingRequests: number;
        activeInvitations: number;
        onlinePlayers: number;
        totalNotifications: number;
    };
}
//# sourceMappingURL=FriendsManager.d.ts.map