"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FriendsManager = void 0;
class FriendsManager {
    constructor() {
        this.friendRequests = new Map(); // requestId -> FriendRequest
        this.friendships = new Map(); // friendshipId -> Friendship
        this.gameInvitations = new Map(); // invitationId -> GameInvitation
        this.playerStatuses = new Map(); // playerId -> PlayerStatus
        this.notifications = new Map(); // playerId -> notifications
        this.playerFriends = new Map(); // playerId -> friendIds
        this.playerRequests = new Map(); // playerId -> requestIds
    }
    /**
     * Отправляет запрос на добавление в друзья
     */
    sendFriendRequest(fromPlayer, toPlayerId, toPlayerName) {
        // Проверяем, что игрок не отправляет запрос самому себе
        if (fromPlayer.id === toPlayerId) {
            throw new Error('Cannot send friend request to yourself');
        }
        // Проверяем, не являются ли уже друзьями
        if (this.areFriends(fromPlayer.id, toPlayerId)) {
            throw new Error('Players are already friends');
        }
        // Проверяем, нет ли уже активного запроса
        const existingRequest = this.findPendingRequest(fromPlayer.id, toPlayerId);
        if (existingRequest) {
            throw new Error('Friend request already exists');
        }
        const request = {
            id: this.generateRequestId(),
            fromPlayerId: fromPlayer.id,
            fromPlayerName: fromPlayer.name,
            toPlayerId,
            toPlayerName,
            status: 'pending',
            createdAt: new Date()
        };
        this.friendRequests.set(request.id, request);
        // Добавляем в индексы
        if (!this.playerRequests.has(toPlayerId)) {
            this.playerRequests.set(toPlayerId, new Set());
        }
        this.playerRequests.get(toPlayerId).add(request.id);
        // Создаем уведомление
        this.addNotification(toPlayerId, {
            type: 'friend_request',
            title: 'Новый запрос в друзья',
            message: `${fromPlayer.name} хочет добавить вас в друзья`,
            data: { requestId: request.id }
        });
        return request;
    }
    /**
     * Отвечает на запрос в друзья
     */
    respondToFriendRequest(requestId, playerId, accept) {
        const request = this.friendRequests.get(requestId);
        if (!request) {
            throw new Error('Friend request not found');
        }
        if (request.toPlayerId !== playerId) {
            throw new Error('Not authorized to respond to this request');
        }
        if (request.status !== 'pending') {
            throw new Error('Request already responded to');
        }
        request.status = accept ? 'accepted' : 'declined';
        request.respondedAt = new Date();
        // Удаляем из индекса запросов
        const playerRequests = this.playerRequests.get(playerId);
        if (playerRequests) {
            playerRequests.delete(requestId);
        }
        let friendship;
        if (accept) {
            // Создаем дружбу
            friendship = this.createFriendship(request.fromPlayerId, request.fromPlayerName, request.toPlayerId, request.toPlayerName);
            // Уведомляем отправителя
            this.addNotification(request.fromPlayerId, {
                type: 'friend_accepted',
                title: 'Запрос принят',
                message: `${request.toPlayerName} принял ваш запрос в друзья`,
                data: { friendshipId: friendship.id }
            });
        }
        return { request, friendship };
    }
    /**
     * Создает дружбу между игроками
     */
    createFriendship(player1Id, player1Name, player2Id, player2Name) {
        const friendship = {
            id: this.generateFriendshipId(),
            player1Id,
            player1Name,
            player2Id,
            player2Name,
            createdAt: new Date()
        };
        this.friendships.set(friendship.id, friendship);
        // Добавляем в индексы друзей
        if (!this.playerFriends.has(player1Id)) {
            this.playerFriends.set(player1Id, new Set());
        }
        if (!this.playerFriends.has(player2Id)) {
            this.playerFriends.set(player2Id, new Set());
        }
        this.playerFriends.get(player1Id).add(player2Id);
        this.playerFriends.get(player2Id).add(player1Id);
        return friendship;
    }
    /**
     * Удаляет друга
     */
    removeFriend(playerId, friendId) {
        if (!this.areFriends(playerId, friendId)) {
            return false;
        }
        // Находим дружбу
        const friendship = Array.from(this.friendships.values()).find(f => (f.player1Id === playerId && f.player2Id === friendId) ||
            (f.player1Id === friendId && f.player2Id === playerId));
        if (!friendship) {
            return false;
        }
        // Удаляем дружбу
        this.friendships.delete(friendship.id);
        // Удаляем из индексов
        this.playerFriends.get(playerId)?.delete(friendId);
        this.playerFriends.get(friendId)?.delete(playerId);
        return true;
    }
    /**
     * Отправляет приглашение в игру
     */
    sendGameInvitation(fromPlayer, toPlayerId, toPlayerName, gameType, roomId, roomName, message) {
        // Проверяем, что игроки друзья (опционально)
        // if (!this.areFriends(fromPlayer.id, toPlayerId)) {
        //   throw new Error('Can only invite friends to games');
        // }
        const invitation = {
            id: this.generateInvitationId(),
            fromPlayerId: fromPlayer.id,
            fromPlayerName: fromPlayer.name,
            toPlayerId,
            toPlayerName,
            roomId,
            roomName,
            gameType,
            message,
            status: 'pending',
            createdAt: new Date(),
            expiresAt: new Date(Date.now() + 5 * 60 * 1000) // 5 минут
        };
        this.gameInvitations.set(invitation.id, invitation);
        // Создаем уведомление
        this.addNotification(toPlayerId, {
            type: 'game_invitation',
            title: 'Приглашение в игру',
            message: `${fromPlayer.name} приглашает вас в игру${roomName ? ` "${roomName}"` : ''}`,
            data: { invitationId: invitation.id }
        });
        return invitation;
    }
    /**
     * Отвечает на приглашение в игру
     */
    respondToGameInvitation(invitationId, playerId, accept) {
        const invitation = this.gameInvitations.get(invitationId);
        if (!invitation) {
            throw new Error('Game invitation not found');
        }
        if (invitation.toPlayerId !== playerId) {
            throw new Error('Not authorized to respond to this invitation');
        }
        if (invitation.status !== 'pending') {
            throw new Error('Invitation already responded to');
        }
        if (new Date() > invitation.expiresAt) {
            invitation.status = 'expired';
            throw new Error('Invitation has expired');
        }
        invitation.status = accept ? 'accepted' : 'declined';
        invitation.respondedAt = new Date();
        return invitation;
    }
    /**
     * Обновляет статус игрока
     */
    updatePlayerStatus(playerId, playerName, status, activity) {
        const playerStatus = {
            playerId,
            playerName,
            status,
            lastSeen: new Date(),
            currentActivity: activity
        };
        this.playerStatuses.set(playerId, playerStatus);
    }
    /**
     * Добавляет уведомление
     */
    addNotification(playerId, notification) {
        const fullNotification = {
            id: this.generateNotificationId(),
            playerId,
            read: false,
            createdAt: new Date(),
            ...notification
        };
        if (!this.notifications.has(playerId)) {
            this.notifications.set(playerId, []);
        }
        const playerNotifications = this.notifications.get(playerId);
        playerNotifications.push(fullNotification);
        // Ограничиваем количество уведомлений
        if (playerNotifications.length > 50) {
            playerNotifications.splice(0, playerNotifications.length - 50);
        }
        return fullNotification;
    }
    /**
     * Помечает уведомление как прочитанное
     */
    markNotificationAsRead(playerId, notificationId) {
        const notifications = this.notifications.get(playerId);
        if (!notifications)
            return false;
        const notification = notifications.find(n => n.id === notificationId);
        if (!notification)
            return false;
        notification.read = true;
        return true;
    }
    /**
     * Помечает все уведомления как прочитанные
     */
    markAllNotificationsAsRead(playerId) {
        const notifications = this.notifications.get(playerId);
        if (!notifications)
            return 0;
        let count = 0;
        notifications.forEach(notification => {
            if (!notification.read) {
                notification.read = true;
                count++;
            }
        });
        return count;
    }
    // Геттеры
    getFriends(playerId) {
        const friendIds = this.playerFriends.get(playerId) || new Set();
        return Array.from(friendIds)
            .map(friendId => this.playerStatuses.get(friendId))
            .filter((status) => status !== undefined);
    }
    getFriendRequests(playerId) {
        const requestIds = this.playerRequests.get(playerId) || new Set();
        return Array.from(requestIds)
            .map(requestId => this.friendRequests.get(requestId))
            .filter((request) => request !== undefined && request.status === 'pending');
    }
    getGameInvitations(playerId) {
        return Array.from(this.gameInvitations.values())
            .filter(invitation => invitation.toPlayerId === playerId && invitation.status === 'pending' && new Date() <= invitation.expiresAt);
    }
    getNotifications(playerId, unreadOnly = false) {
        const notifications = this.notifications.get(playerId) || [];
        return unreadOnly ? notifications.filter(n => !n.read) : notifications;
    }
    getPlayerStatus(playerId) {
        return this.playerStatuses.get(playerId);
    }
    areFriends(playerId1, playerId2) {
        const friends = this.playerFriends.get(playerId1);
        return friends ? friends.has(playerId2) : false;
    }
    findPendingRequest(fromPlayerId, toPlayerId) {
        return Array.from(this.friendRequests.values()).find(request => request.status === 'pending' &&
            ((request.fromPlayerId === fromPlayerId && request.toPlayerId === toPlayerId) ||
                (request.fromPlayerId === toPlayerId && request.toPlayerId === fromPlayerId)));
    }
    // Утилиты
    generateRequestId() {
        return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    generateFriendshipId() {
        return 'friend_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    generateInvitationId() {
        return 'inv_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    generateNotificationId() {
        return 'notif_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    /**
     * Очищает устаревшие данные
     */
    cleanup() {
        let cleaned = 0;
        const now = Date.now();
        const maxAge = 24 * 60 * 60 * 1000; // 24 часа
        // Удаляем устаревшие приглашения
        for (const [id, invitation] of this.gameInvitations.entries()) {
            if (invitation.status === 'pending' && now > invitation.expiresAt.getTime()) {
                invitation.status = 'expired';
            }
            if (invitation.status !== 'pending' && now - invitation.createdAt.getTime() > maxAge) {
                this.gameInvitations.delete(id);
                cleaned++;
            }
        }
        // Удаляем старые запросы в друзья
        for (const [id, request] of this.friendRequests.entries()) {
            if (request.status !== 'pending' && now - request.createdAt.getTime() > maxAge) {
                this.friendRequests.delete(id);
                cleaned++;
            }
        }
        return cleaned;
    }
    /**
     * Получает статистику системы друзей
     */
    getStats() {
        const totalFriendships = this.friendships.size;
        const pendingRequests = Array.from(this.friendRequests.values()).filter(r => r.status === 'pending').length;
        const activeInvitations = Array.from(this.gameInvitations.values()).filter(i => i.status === 'pending').length;
        const onlinePlayers = Array.from(this.playerStatuses.values()).filter(s => s.status !== 'offline').length;
        return {
            totalFriendships,
            pendingRequests,
            activeInvitations,
            onlinePlayers,
            totalNotifications: Array.from(this.notifications.values()).reduce((sum, notifications) => sum + notifications.length, 0)
        };
    }
}
exports.FriendsManager = FriendsManager;
//# sourceMappingURL=FriendsManager.js.map