import { Player } from '../players/PlayerManager';
export interface ChatMessage {
    id: string;
    roomId: string;
    playerId: string;
    playerName: string;
    message: string;
    timestamp: Date;
    type: 'message' | 'system' | 'game';
}
export declare class ChatManager {
    private messages;
    private readonly maxMessagesPerRoom;
    /**
     * Добавляет сообщение в чат комнаты
     */
    addMessage(roomId: string, player: Player, message: string, type?: 'message' | 'system' | 'game'): ChatMessage;
    /**
     * Добавляет системное сообщение
     */
    addSystemMessage(roomId: string, message: string): ChatMessage;
    /**
     * Добавляет игровое сообщение
     */
    addGameMessage(roomId: string, message: string): ChatMessage;
    /**
     * Получает сообщения комнаты
     */
    getRoomMessages(roomId: string, limit?: number): ChatMessage[];
    /**
     * Получает последние сообщения комнаты
     */
    getRecentMessages(roomId: string, count?: number): ChatMessage[];
    /**
     * Очищает сообщения комнаты
     */
    clearRoomMessages(roomId: string): boolean;
    /**
     * Получает количество сообщений в комнате
     */
    getRoomMessageCount(roomId: string): number;
    /**
     * Фильтрует сообщения по типу
     */
    getMessagesByType(roomId: string, type: 'message' | 'system' | 'game'): ChatMessage[];
    /**
     * Получает сообщения игрока
     */
    getPlayerMessages(roomId: string, playerId: string): ChatMessage[];
    /**
     * Проверяет, содержит ли сообщение запрещенные слова
     */
    private containsProfanity;
    /**
     * Модерирует сообщение
     */
    moderateMessage(message: string): {
        allowed: boolean;
        reason?: string;
    };
    /**
     * Получает статистику чата
     */
    getStats(): {
        totalRooms: number;
        totalMessages: number;
        averageMessagesPerRoom: number;
    };
    /**
     * Очищает старые сообщения
     */
    cleanupOldMessages(maxAge?: number): number;
    /**
     * Экспортирует историю чата комнаты
     */
    exportRoomHistory(roomId: string): string;
}
//# sourceMappingURL=ChatManager.d.ts.map