"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatManager = void 0;
const uuid_1 = require("uuid");
class ChatManager {
    constructor() {
        this.messages = new Map(); // roomId -> messages
        this.maxMessagesPerRoom = 100;
    }
    /**
     * Добавляет сообщение в чат комнаты
     */
    addMessage(roomId, player, message, type = 'message') {
        // Валидация сообщения
        if (!message || message.trim().length === 0) {
            throw new Error('Message cannot be empty');
        }
        if (message.length > 500) {
            throw new Error('Message is too long');
        }
        // Создаем сообщение
        const chatMessage = {
            id: (0, uuid_1.v4)(),
            roomId,
            playerId: player.id,
            playerName: player.name,
            message: message.trim(),
            timestamp: new Date(),
            type
        };
        // Получаем или создаем массив сообщений для комнаты
        if (!this.messages.has(roomId)) {
            this.messages.set(roomId, []);
        }
        const roomMessages = this.messages.get(roomId);
        roomMessages.push(chatMessage);
        // Ограничиваем количество сообщений
        if (roomMessages.length > this.maxMessagesPerRoom) {
            roomMessages.splice(0, roomMessages.length - this.maxMessagesPerRoom);
        }
        return chatMessage;
    }
    /**
     * Добавляет системное сообщение
     */
    addSystemMessage(roomId, message) {
        const systemMessage = {
            id: (0, uuid_1.v4)(),
            roomId,
            playerId: 'system',
            playerName: 'Система',
            message,
            timestamp: new Date(),
            type: 'system'
        };
        if (!this.messages.has(roomId)) {
            this.messages.set(roomId, []);
        }
        const roomMessages = this.messages.get(roomId);
        roomMessages.push(systemMessage);
        if (roomMessages.length > this.maxMessagesPerRoom) {
            roomMessages.splice(0, roomMessages.length - this.maxMessagesPerRoom);
        }
        return systemMessage;
    }
    /**
     * Добавляет игровое сообщение
     */
    addGameMessage(roomId, message) {
        const gameMessage = {
            id: (0, uuid_1.v4)(),
            roomId,
            playerId: 'game',
            playerName: 'Игра',
            message,
            timestamp: new Date(),
            type: 'game'
        };
        if (!this.messages.has(roomId)) {
            this.messages.set(roomId, []);
        }
        const roomMessages = this.messages.get(roomId);
        roomMessages.push(gameMessage);
        if (roomMessages.length > this.maxMessagesPerRoom) {
            roomMessages.splice(0, roomMessages.length - this.maxMessagesPerRoom);
        }
        return gameMessage;
    }
    /**
     * Получает сообщения комнаты
     */
    getRoomMessages(roomId, limit) {
        const messages = this.messages.get(roomId) || [];
        if (limit && limit > 0) {
            return messages.slice(-limit);
        }
        return [...messages];
    }
    /**
     * Получает последние сообщения комнаты
     */
    getRecentMessages(roomId, count = 50) {
        const messages = this.messages.get(roomId) || [];
        return messages.slice(-count);
    }
    /**
     * Очищает сообщения комнаты
     */
    clearRoomMessages(roomId) {
        return this.messages.delete(roomId);
    }
    /**
     * Получает количество сообщений в комнате
     */
    getRoomMessageCount(roomId) {
        const messages = this.messages.get(roomId);
        return messages ? messages.length : 0;
    }
    /**
     * Фильтрует сообщения по типу
     */
    getMessagesByType(roomId, type) {
        const messages = this.messages.get(roomId) || [];
        return messages.filter(msg => msg.type === type);
    }
    /**
     * Получает сообщения игрока
     */
    getPlayerMessages(roomId, playerId) {
        const messages = this.messages.get(roomId) || [];
        return messages.filter(msg => msg.playerId === playerId);
    }
    /**
     * Проверяет, содержит ли сообщение запрещенные слова
     */
    containsProfanity(message) {
        // Простая проверка на запрещенные слова
        const profanityWords = ['спам', 'реклама']; // Добавьте нужные слова
        const lowerMessage = message.toLowerCase();
        return profanityWords.some(word => lowerMessage.includes(word));
    }
    /**
     * Модерирует сообщение
     */
    moderateMessage(message) {
        // Проверка длины
        if (message.length > 500) {
            return { allowed: false, reason: 'Message too long' };
        }
        // Проверка на пустое сообщение
        if (!message.trim()) {
            return { allowed: false, reason: 'Empty message' };
        }
        // Проверка на спам (слишком много одинаковых символов)
        const repeatedChars = /(.)\1{10,}/;
        if (repeatedChars.test(message)) {
            return { allowed: false, reason: 'Too many repeated characters' };
        }
        // Проверка на запрещенные слова
        if (this.containsProfanity(message)) {
            return { allowed: false, reason: 'Contains inappropriate content' };
        }
        return { allowed: true };
    }
    /**
     * Получает статистику чата
     */
    getStats() {
        let totalMessages = 0;
        let totalRooms = this.messages.size;
        for (const messages of this.messages.values()) {
            totalMessages += messages.length;
        }
        return {
            totalRooms,
            totalMessages,
            averageMessagesPerRoom: totalRooms > 0 ? Math.round(totalMessages / totalRooms) : 0
        };
    }
    /**
     * Очищает старые сообщения
     */
    cleanupOldMessages(maxAge = 24 * 60 * 60 * 1000) {
        const now = new Date();
        let cleaned = 0;
        for (const [roomId, messages] of this.messages.entries()) {
            const filteredMessages = messages.filter(msg => {
                const age = now.getTime() - msg.timestamp.getTime();
                return age <= maxAge;
            });
            if (filteredMessages.length !== messages.length) {
                cleaned += messages.length - filteredMessages.length;
                if (filteredMessages.length === 0) {
                    this.messages.delete(roomId);
                }
                else {
                    this.messages.set(roomId, filteredMessages);
                }
            }
        }
        return cleaned;
    }
    /**
     * Экспортирует историю чата комнаты
     */
    exportRoomHistory(roomId) {
        const messages = this.messages.get(roomId) || [];
        return messages.map(msg => {
            const timestamp = msg.timestamp.toISOString();
            return `[${timestamp}] ${msg.playerName}: ${msg.message}`;
        }).join('\n');
    }
}
exports.ChatManager = ChatManager;
//# sourceMappingURL=ChatManager.js.map