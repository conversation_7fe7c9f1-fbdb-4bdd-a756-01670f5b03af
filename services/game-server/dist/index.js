"use strict";
/**
 * Game Server - WebSocket сервер для многопользовательской игры
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.friendsManager = exports.spectatorManager = exports.tournamentManager = exports.achievementManager = exports.ratingManager = exports.playerManager = exports.chatManager = exports.roomManager = exports.gameManager = exports.io = void 0;
const express_1 = __importDefault(require("express"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const cors_1 = __importDefault(require("cors"));
const GameManager_1 = require("./game/GameManager");
const RoomManager_1 = require("./rooms/RoomManager");
const ChatManager_1 = require("./chat/ChatManager");
const PlayerManager_1 = require("./players/PlayerManager");
const RatingManager_1 = require("./rating/RatingManager");
const AchievementManager_1 = require("./achievements/AchievementManager");
const TournamentManager_1 = require("./tournaments/TournamentManager");
const SpectatorManager_1 = require("./spectators/SpectatorManager");
const FriendsManager_1 = require("./friends/FriendsManager");
const app = (0, express_1.default)();
const server = (0, http_1.createServer)(app);
// Настройка CORS
app.use((0, cors_1.default)({
    origin: ["http://localhost:3000", "http://localhost:3001"],
    credentials: true
}));
// Socket.IO сервер
const io = new socket_io_1.Server(server, {
    cors: {
        origin: ["http://localhost:3000", "http://localhost:3001"],
        methods: ["GET", "POST"],
        credentials: true
    }
});
exports.io = io;
// Менеджеры
const gameManager = new GameManager_1.GameManager();
exports.gameManager = gameManager;
const roomManager = new RoomManager_1.RoomManager();
exports.roomManager = roomManager;
const chatManager = new ChatManager_1.ChatManager();
exports.chatManager = chatManager;
const playerManager = new PlayerManager_1.PlayerManager();
exports.playerManager = playerManager;
const ratingManager = new RatingManager_1.RatingManager();
exports.ratingManager = ratingManager;
const achievementManager = new AchievementManager_1.AchievementManager();
exports.achievementManager = achievementManager;
const tournamentManager = new TournamentManager_1.TournamentManager(gameManager, ratingManager);
exports.tournamentManager = tournamentManager;
const spectatorManager = new SpectatorManager_1.SpectatorManager();
exports.spectatorManager = spectatorManager;
const friendsManager = new FriendsManager_1.FriendsManager();
exports.friendsManager = friendsManager;
// Базовые маршруты
app.get('/', (req, res) => {
    res.json({
        message: 'Kozyr Master Game Server',
        version: '0.1.0',
        status: 'running',
        rooms: roomManager.getRoomsCount(),
        players: playerManager.getPlayersCount()
    });
});
app.get('/health', (req, res) => {
    res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});
app.get('/stats', (req, res) => {
    res.json({
        rooms: roomManager.getStats(),
        players: playerManager.getStats(),
        games: gameManager.getStats(),
        ratings: ratingManager.getSystemStats(),
        achievements: achievementManager.getAchievementStats()
    });
});
// API для рейтингов
app.get('/leaderboard', (req, res) => {
    const limit = parseInt(req.query.limit) || 50;
    res.json(ratingManager.getLeaderboard(limit));
});
app.get('/top-players', (req, res) => {
    res.json(ratingManager.getTopPlayers());
});
app.get('/player/:playerId/rating', (req, res) => {
    const { playerId } = req.params;
    const rating = ratingManager.getPlayerStats(playerId);
    if (!rating) {
        return res.status(404).json({ error: 'Player not found' });
    }
    res.json(rating);
});
app.get('/player/:playerId/history', (req, res) => {
    const { playerId } = req.params;
    const limit = parseInt(req.query.limit) || 20;
    const history = ratingManager.getPlayerGameHistory(playerId, limit);
    res.json(history);
});
// API для достижений
app.get('/achievements', (req, res) => {
    res.json(achievementManager.getAchievementsByCategory());
});
app.get('/player/:playerId/achievements', (req, res) => {
    const { playerId } = req.params;
    const achievements = achievementManager.getPlayerAchievements(playerId);
    res.json(achievements);
});
app.get('/achievements/rare', (req, res) => {
    res.json(achievementManager.getRareAchievements());
});
// API для турниров
app.get('/tournaments', (req, res) => {
    const status = req.query.status;
    let tournaments;
    if (status === 'active') {
        tournaments = tournamentManager.getActiveTournaments();
    }
    else {
        tournaments = tournamentManager.getAllTournaments();
    }
    res.json(tournaments);
});
app.get('/tournaments/:tournamentId', (req, res) => {
    const { tournamentId } = req.params;
    const tournament = tournamentManager.getTournament(tournamentId);
    if (!tournament) {
        return res.status(404).json({ error: 'Tournament not found' });
    }
    res.json(tournament);
});
app.get('/player/:playerId/tournaments', (req, res) => {
    const { playerId } = req.params;
    const tournaments = tournamentManager.getPlayerTournaments(playerId);
    res.json(tournaments);
});
// API для спектаторов
app.get('/spectator/games', (req, res) => {
    const games = spectatorManager.getSpectatorGames();
    res.json(games);
});
app.get('/spectator/games/:gameId', (req, res) => {
    const { gameId } = req.params;
    const game = spectatorManager.getSpectatorGame(gameId);
    if (!game) {
        return res.status(404).json({ error: 'Game not found' });
    }
    res.json(game);
});
app.get('/spectator/games/:gameId/chat', (req, res) => {
    const { gameId } = req.params;
    const limit = parseInt(req.query.limit) || 50;
    const messages = spectatorManager.getSpectatorChatMessages(gameId, limit);
    res.json(messages);
});
app.get('/spectator/stats', (req, res) => {
    res.json(spectatorManager.getSpectatorStats());
});
// API для друзей
app.get('/friends/stats', (req, res) => {
    res.json(friendsManager.getStats());
});
app.get('/player/:playerId/friends', (req, res) => {
    const { playerId } = req.params;
    const friends = friendsManager.getFriends(playerId);
    res.json(friends);
});
app.get('/player/:playerId/friend-requests', (req, res) => {
    const { playerId } = req.params;
    const requests = friendsManager.getFriendRequests(playerId);
    res.json(requests);
});
app.get('/player/:playerId/game-invitations', (req, res) => {
    const { playerId } = req.params;
    const invitations = friendsManager.getGameInvitations(playerId);
    res.json(invitations);
});
app.get('/player/:playerId/notifications', (req, res) => {
    const { playerId } = req.params;
    const unreadOnly = req.query.unread === 'true';
    const notifications = friendsManager.getNotifications(playerId, unreadOnly);
    res.json(notifications);
});
// WebSocket соединения
io.on('connection', (socket) => {
    console.log(`Player connected: ${socket.id}`);
    // Регистрация игрока
    socket.on('register_player', (data) => {
        try {
            const player = playerManager.registerPlayer(socket.id, data.name, socket);
            // Обновляем статус игрока
            friendsManager.updatePlayerStatus(player.id, player.name, 'online');
            socket.emit('player_registered', {
                playerId: player.id,
                name: player.name
            });
            // Отправляем список доступных комнат
            socket.emit('rooms_list', roomManager.getPublicRooms());
            console.log(`Player registered: ${player.name} (${player.id})`);
        }
        catch (error) {
            socket.emit('error', { message: 'Failed to register player' });
        }
    });
    // Создание комнаты
    socket.on('create_room', (data) => {
        try {
            const player = playerManager.getPlayer(socket.id);
            if (!player) {
                socket.emit('error', { message: 'Player not registered' });
                return;
            }
            const room = roomManager.createRoom(data.name, player, data.maxPlayers);
            socket.join(room.id);
            socket.emit('room_created', {
                roomId: room.id,
                room: (0, RoomManager_1.getRoomPublicInfo)(room)
            });
            // Уведомляем всех о новой комнате
            socket.broadcast.emit('room_added', (0, RoomManager_1.getRoomPublicInfo)(room));
            console.log(`Room created: ${room.name} by ${player.name}`);
        }
        catch (error) {
            socket.emit('error', { message: 'Failed to create room' });
        }
    });
    // Присоединение к комнате
    socket.on('join_room', (data) => {
        try {
            const player = playerManager.getPlayer(socket.id);
            if (!player) {
                socket.emit('error', { message: 'Player not registered' });
                return;
            }
            const room = roomManager.joinRoom(data.roomId, player);
            socket.join(room.id);
            socket.emit('room_joined', {
                roomId: room.id,
                room: (0, RoomManager_1.getRoomPublicInfo)(room)
            });
            // Уведомляем других игроков в комнате
            socket.to(room.id).emit('player_joined', {
                player: (0, PlayerManager_1.getPlayerPublicInfo)(player),
                room: (0, RoomManager_1.getRoomPublicInfo)(room)
            });
            console.log(`Player ${player.name} joined room ${room.name}`);
        }
        catch (error) {
            socket.emit('error', { message: error instanceof Error ? error.message : 'Failed to join room' });
        }
    });
    // Покидание комнаты
    socket.on('leave_room', (data) => {
        try {
            const player = playerManager.getPlayer(socket.id);
            if (!player)
                return;
            const room = roomManager.leaveRoom(data.roomId, player);
            socket.leave(data.roomId);
            socket.emit('room_left', { roomId: data.roomId });
            if (room) {
                // Уведомляем других игроков
                socket.to(room.id).emit('player_left', {
                    player: (0, PlayerManager_1.getPlayerPublicInfo)(player),
                    room: (0, RoomManager_1.getRoomPublicInfo)(room)
                });
            }
            console.log(`Player ${player.name} left room`);
        }
        catch (error) {
            socket.emit('error', { message: 'Failed to leave room' });
        }
    });
    // Запуск игры
    socket.on('start_game', (data) => {
        try {
            const player = playerManager.getPlayer(socket.id);
            const room = roomManager.getRoom(data.roomId);
            if (!player || !room) {
                socket.emit('error', { message: 'Invalid player or room' });
                return;
            }
            if (!(0, RoomManager_1.isRoomOwner)(room, player)) {
                socket.emit('error', { message: 'Only room owner can start the game' });
                return;
            }
            const game = gameManager.startGame(room);
            // Добавляем игру в систему спектаторов
            spectatorManager.addGame(game, true);
            // Уведомляем всех игроков в комнате
            io.to(room.id).emit('game_started', {
                gameId: game.id,
                gameState: (0, GameManager_1.getGamePublicState)(game)
            });
            // Уведомляем о новой игре для спектаторов
            socket.broadcast.emit('spectator_game_added', {
                gameId: game.id,
                roomName: room.name,
                players: game.players.map(p => ({ id: p.id, name: p.name }))
            });
            console.log(`Game started in room ${room.name}`);
        }
        catch (error) {
            socket.emit('error', { message: error instanceof Error ? error.message : 'Failed to start game' });
        }
    });
    // Ход в игре
    socket.on('game_move', (data) => {
        try {
            const player = playerManager.getPlayer(socket.id);
            const room = roomManager.getRoom(data.roomId);
            if (!player || !room) {
                socket.emit('error', { message: 'Invalid player or room' });
                return;
            }
            const game = gameManager.getGameByRoom(room.id);
            if (!game) {
                socket.emit('error', { message: 'No active game in this room' });
                return;
            }
            const success = gameManager.makeMove(game.id, player.id, data.action, data.cardIndex);
            if (success) {
                const updatedGame = gameManager.getGame(game.id);
                const publicState = (0, GameManager_1.getGamePublicState)(updatedGame);
                // Отправляем обновленное состояние всем игрокам
                io.to(room.id).emit('game_updated', {
                    gameState: publicState
                });
                // Обновляем состояние для спектаторов
                spectatorManager.updateGameState(updatedGame);
                // Отправляем обновление спектаторам
                io.emit('spectator_game_updated', {
                    gameId: updatedGame.id,
                    gameState: publicState
                });
                // Проверяем, завершилась ли игра
                if (updatedGame.status === 'finished' && updatedGame.winner) {
                    const winner = updatedGame.winner;
                    const loser = updatedGame.players.find(p => p.id !== winner.id);
                    if (loser) {
                        // Обновляем рейтинги
                        const gameDuration = Math.round((Date.now() - updatedGame.startedAt.getTime()) / 1000);
                        const moveCount = updatedGame.moves.length;
                        const { winnerRating, loserRating } = ratingManager.updateRatingsAfterGame(winner.id, winner.name, loser.id, loser.name, updatedGame.id, gameDuration, moveCount);
                        // Проверяем достижения
                        const winnerHistory = ratingManager.getPlayerGameHistory(winner.id);
                        const loserHistory = ratingManager.getPlayerGameHistory(loser.id);
                        const winnerAchievements = achievementManager.checkAchievements(winner.id, winnerRating, winnerHistory);
                        const loserAchievements = achievementManager.checkAchievements(loser.id, loserRating, loserHistory);
                        // Отправляем обновления рейтингов и достижений
                        io.to(room.id).emit('game_finished', {
                            winner: {
                                player: winner,
                                rating: winnerRating,
                                newAchievements: winnerAchievements
                            },
                            loser: {
                                player: loser,
                                rating: loserRating,
                                newAchievements: loserAchievements
                            },
                            gameStats: {
                                duration: gameDuration,
                                moves: moveCount
                            }
                        });
                    }
                }
            }
            else {
                socket.emit('error', { message: 'Invalid move' });
            }
        }
        catch (error) {
            socket.emit('error', { message: error instanceof Error ? error.message : 'Failed to make move' });
        }
    });
    // Чат
    socket.on('chat_message', (data) => {
        try {
            const player = playerManager.getPlayer(socket.id);
            if (!player)
                return;
            const chatMessage = chatManager.addMessage(data.roomId, player, data.message);
            // Отправляем сообщение всем в комнате
            io.to(data.roomId).emit('chat_message', chatMessage);
        }
        catch (error) {
            socket.emit('error', { message: 'Failed to send message' });
        }
    });
    // Запрос списка комнат
    socket.on('get_rooms', () => {
        socket.emit('rooms_list', roomManager.getPublicRooms());
    });
    // Запрос рейтинга игрока
    socket.on('get_player_rating', () => {
        const player = playerManager.getPlayer(socket.id);
        if (!player)
            return;
        const rating = ratingManager.getPlayerStats(player.id);
        const category = rating ? ratingManager.getRatingCategory(rating.rating) : null;
        socket.emit('player_rating', {
            rating,
            category
        });
    });
    // Запрос достижений игрока
    socket.on('get_player_achievements', () => {
        const player = playerManager.getPlayer(socket.id);
        if (!player)
            return;
        const achievements = achievementManager.getPlayerAchievements(player.id);
        socket.emit('player_achievements', achievements);
    });
    // Запрос таблицы лидеров
    socket.on('get_leaderboard', (data = {}) => {
        const limit = data.limit || 50;
        const leaderboard = ratingManager.getLeaderboard(limit);
        socket.emit('leaderboard', leaderboard);
    });
    // Запрос топ игроков
    socket.on('get_top_players', () => {
        const topPlayers = ratingManager.getTopPlayers();
        socket.emit('top_players', topPlayers);
    });
    // Турниры
    socket.on('create_tournament', (data) => {
        try {
            const player = playerManager.getPlayer(socket.id);
            if (!player) {
                socket.emit('error', { message: 'Player not registered' });
                return;
            }
            const tournament = tournamentManager.createTournament(data.name, data.description, data.type, data.maxParticipants, player.id, data.settings);
            socket.emit('tournament_created', tournament);
            socket.broadcast.emit('tournament_added', tournament);
            console.log(`Tournament created: ${tournament.name} by ${player.name}`);
        }
        catch (error) {
            socket.emit('error', {
                message: error instanceof Error ? error.message : 'Failed to create tournament'
            });
        }
    });
    socket.on('register_for_tournament', (data) => {
        try {
            const player = playerManager.getPlayer(socket.id);
            if (!player) {
                socket.emit('error', { message: 'Player not registered' });
                return;
            }
            const participant = tournamentManager.registerPlayer(data.tournamentId, player);
            const tournament = tournamentManager.getTournament(data.tournamentId);
            socket.emit('tournament_registered', { tournament, participant });
            socket.broadcast.emit('tournament_updated', tournament);
            console.log(`Player ${player.name} registered for tournament ${tournament?.name}`);
        }
        catch (error) {
            socket.emit('error', {
                message: error instanceof Error ? error.message : 'Failed to register for tournament'
            });
        }
    });
    socket.on('get_tournaments', (data = {}) => {
        let tournaments;
        if (data.status === 'active') {
            tournaments = tournamentManager.getActiveTournaments();
        }
        else {
            tournaments = tournamentManager.getAllTournaments();
        }
        socket.emit('tournaments_list', tournaments);
    });
    socket.on('get_tournament', (data) => {
        const tournament = tournamentManager.getTournament(data.tournamentId);
        if (tournament) {
            socket.emit('tournament_details', tournament);
        }
        else {
            socket.emit('error', { message: 'Tournament not found' });
        }
    });
    // Спектаторы
    socket.on('get_spectator_games', () => {
        const games = spectatorManager.getSpectatorGames();
        socket.emit('spectator_games_list', games);
    });
    socket.on('join_spectator', (data) => {
        try {
            const player = playerManager.getPlayer(socket.id);
            if (!player) {
                socket.emit('error', { message: 'Player not registered' });
                return;
            }
            const spectator = spectatorManager.addSpectator(data.gameId, player);
            const game = spectatorManager.getSpectatorGame(data.gameId);
            if (spectator && game) {
                // Присоединяем к комнате спектаторов
                socket.join(`spectator_${data.gameId}`);
                socket.emit('spectator_joined', {
                    gameId: data.gameId,
                    spectator,
                    game
                });
                // Уведомляем других спектаторов
                socket.to(`spectator_${data.gameId}`).emit('spectator_added', {
                    gameId: data.gameId,
                    spectator
                });
                console.log(`Player ${player.name} started spectating game ${data.gameId}`);
            }
        }
        catch (error) {
            socket.emit('error', {
                message: error instanceof Error ? error.message : 'Failed to join as spectator'
            });
        }
    });
    socket.on('leave_spectator', (data) => {
        try {
            const player = playerManager.getPlayer(socket.id);
            if (!player)
                return;
            const success = spectatorManager.removeSpectator(data.gameId, player.id);
            if (success) {
                // Покидаем комнату спектаторов
                socket.leave(`spectator_${data.gameId}`);
                socket.emit('spectator_left', { gameId: data.gameId });
                // Уведомляем других спектаторов
                socket.to(`spectator_${data.gameId}`).emit('spectator_removed', {
                    gameId: data.gameId,
                    playerId: player.id
                });
                console.log(`Player ${player.name} stopped spectating game ${data.gameId}`);
            }
        }
        catch (error) {
            socket.emit('error', { message: 'Failed to leave spectator mode' });
        }
    });
    socket.on('spectator_chat_message', (data) => {
        try {
            const player = playerManager.getPlayer(socket.id);
            if (!player)
                return;
            // Проверяем, что игрок является зрителем этой игры
            if (!spectatorManager.isSpectating(player.id, data.gameId)) {
                socket.emit('error', { message: 'Not spectating this game' });
                return;
            }
            const chatMessage = spectatorManager.addSpectatorChatMessage(data.gameId, {
                spectatorId: player.id,
                spectatorName: player.name,
                message: data.message.trim()
            });
            // Отправляем сообщение всем спектаторам
            io.to(`spectator_${data.gameId}`).emit('spectator_chat_message', chatMessage);
        }
        catch (error) {
            socket.emit('error', { message: 'Failed to send spectator message' });
        }
    });
    // Друзья
    socket.on('send_friend_request', (data) => {
        try {
            const player = playerManager.getPlayer(socket.id);
            if (!player) {
                socket.emit('error', { message: 'Player not registered' });
                return;
            }
            const request = friendsManager.sendFriendRequest(player, data.toPlayerId, data.toPlayerName);
            socket.emit('friend_request_sent', request);
            // Уведомляем получателя
            const targetSocket = playerManager.getPlayerSocket(data.toPlayerId);
            if (targetSocket) {
                targetSocket.emit('friend_request_received', request);
            }
            console.log(`Friend request sent from ${player.name} to ${data.toPlayerName}`);
        }
        catch (error) {
            socket.emit('error', {
                message: error instanceof Error ? error.message : 'Failed to send friend request'
            });
        }
    });
    socket.on('respond_to_friend_request', (data) => {
        try {
            const player = playerManager.getPlayer(socket.id);
            if (!player) {
                socket.emit('error', { message: 'Player not registered' });
                return;
            }
            const { request, friendship } = friendsManager.respondToFriendRequest(data.requestId, player.id, data.accept);
            socket.emit('friend_request_responded', { request, friendship });
            // Уведомляем отправителя
            const senderSocket = playerManager.getPlayerSocket(request.fromPlayerId);
            if (senderSocket) {
                senderSocket.emit('friend_request_response', { request, friendship });
            }
            console.log(`Friend request ${data.accept ? 'accepted' : 'declined'} by ${player.name}`);
        }
        catch (error) {
            socket.emit('error', {
                message: error instanceof Error ? error.message : 'Failed to respond to friend request'
            });
        }
    });
    socket.on('remove_friend', (data) => {
        try {
            const player = playerManager.getPlayer(socket.id);
            if (!player) {
                socket.emit('error', { message: 'Player not registered' });
                return;
            }
            const success = friendsManager.removeFriend(player.id, data.friendId);
            if (success) {
                socket.emit('friend_removed', { friendId: data.friendId });
                // Уведомляем бывшего друга
                const friendSocket = playerManager.getPlayerSocket(data.friendId);
                if (friendSocket) {
                    friendSocket.emit('friend_removed', { friendId: player.id });
                }
                console.log(`${player.name} removed friend ${data.friendId}`);
            }
            else {
                socket.emit('error', { message: 'Failed to remove friend' });
            }
        }
        catch (error) {
            socket.emit('error', { message: 'Failed to remove friend' });
        }
    });
    socket.on('send_game_invitation', (data) => {
        try {
            const player = playerManager.getPlayer(socket.id);
            if (!player) {
                socket.emit('error', { message: 'Player not registered' });
                return;
            }
            const invitation = friendsManager.sendGameInvitation(player, data.toPlayerId, data.toPlayerName, data.gameType, data.roomId, data.roomName, data.message);
            socket.emit('game_invitation_sent', invitation);
            // Уведомляем получателя
            const targetSocket = playerManager.getPlayerSocket(data.toPlayerId);
            if (targetSocket) {
                targetSocket.emit('game_invitation_received', invitation);
            }
            console.log(`Game invitation sent from ${player.name} to ${data.toPlayerName}`);
        }
        catch (error) {
            socket.emit('error', {
                message: error instanceof Error ? error.message : 'Failed to send game invitation'
            });
        }
    });
    socket.on('respond_to_game_invitation', (data) => {
        try {
            const player = playerManager.getPlayer(socket.id);
            if (!player) {
                socket.emit('error', { message: 'Player not registered' });
                return;
            }
            const invitation = friendsManager.respondToGameInvitation(data.invitationId, player.id, data.accept);
            socket.emit('game_invitation_responded', invitation);
            // Уведомляем отправителя
            const senderSocket = playerManager.getPlayerSocket(invitation.fromPlayerId);
            if (senderSocket) {
                senderSocket.emit('game_invitation_response', invitation);
            }
            console.log(`Game invitation ${data.accept ? 'accepted' : 'declined'} by ${player.name}`);
        }
        catch (error) {
            socket.emit('error', {
                message: error instanceof Error ? error.message : 'Failed to respond to game invitation'
            });
        }
    });
    socket.on('get_friends', () => {
        const player = playerManager.getPlayer(socket.id);
        if (!player)
            return;
        const friends = friendsManager.getFriends(player.id);
        socket.emit('friends_list', friends);
    });
    socket.on('get_friend_requests', () => {
        const player = playerManager.getPlayer(socket.id);
        if (!player)
            return;
        const requests = friendsManager.getFriendRequests(player.id);
        socket.emit('friend_requests_list', requests);
    });
    socket.on('get_game_invitations', () => {
        const player = playerManager.getPlayer(socket.id);
        if (!player)
            return;
        const invitations = friendsManager.getGameInvitations(player.id);
        socket.emit('game_invitations_list', invitations);
    });
    socket.on('get_notifications', (data = {}) => {
        const player = playerManager.getPlayer(socket.id);
        if (!player)
            return;
        const notifications = friendsManager.getNotifications(player.id, data.unreadOnly);
        socket.emit('notifications_list', notifications);
    });
    socket.on('mark_notification_read', (data) => {
        const player = playerManager.getPlayer(socket.id);
        if (!player)
            return;
        const success = friendsManager.markNotificationAsRead(player.id, data.notificationId);
        if (success) {
            socket.emit('notification_marked_read', { notificationId: data.notificationId });
        }
    });
    socket.on('mark_all_notifications_read', () => {
        const player = playerManager.getPlayer(socket.id);
        if (!player)
            return;
        const count = friendsManager.markAllNotificationsAsRead(player.id);
        socket.emit('all_notifications_marked_read', { count });
    });
    // Отключение
    socket.on('disconnect', () => {
        try {
            const player = playerManager.getPlayer(socket.id);
            if (player) {
                // Покидаем все комнаты
                const rooms = roomManager.getPlayerRooms(player);
                rooms.forEach(room => {
                    roomManager.leaveRoom(room.id, player);
                    socket.to(room.id).emit('player_left', {
                        player: (0, PlayerManager_1.getPlayerPublicInfo)(player),
                        room: (0, RoomManager_1.getRoomPublicInfo)(room)
                    });
                });
                // Покидаем все игры как зритель
                const spectatorGames = spectatorManager.getPlayerSpectatorGames(player.id);
                spectatorGames.forEach(game => {
                    spectatorManager.removeSpectator(game.gameId, player.id);
                    socket.to(`spectator_${game.gameId}`).emit('spectator_removed', {
                        gameId: game.gameId,
                        playerId: player.id
                    });
                });
                // Обновляем статус на offline
                friendsManager.updatePlayerStatus(player.id, player.name, 'offline');
                playerManager.unregisterPlayer(socket.id);
                console.log(`Player disconnected: ${player.name} (${player.id})`);
            }
        }
        catch (error) {
            console.error('Error handling disconnect:', error);
        }
    });
});
const PORT = process.env.PORT || 3002;
server.listen(PORT, () => {
    console.log(`🎮 Game Server running on port ${PORT}`);
    console.log(`📡 WebSocket endpoint: ws://localhost:${PORT}`);
    console.log(`🌐 HTTP endpoint: http://localhost:${PORT}`);
});
//# sourceMappingURL=index.js.map