/**
 * Game Server - WebSocket сервер для многопользовательской игры
 */
import { Server as SocketIOServer } from 'socket.io';
import { GameManager } from './game/GameManager';
import { RoomManager } from './rooms/RoomManager';
import { ChatManager } from './chat/ChatManager';
import { PlayerManager } from './players/PlayerManager';
declare const io: SocketIOServer<import("socket.io").DefaultEventsMap, import("socket.io").DefaultEventsMap, import("socket.io").DefaultEventsMap, any>;
declare const gameManager: GameManager;
declare const roomManager: RoomManager;
declare const chatManager: ChatManager;
declare const playerManager: PlayerManager;
export { io, gameManager, roomManager, chatManager, playerManager };
//# sourceMappingURL=index.d.ts.map