/**
 * Game Server - WebSocket сервер для многопользовательской игры
 */
import { Server as SocketIOServer } from 'socket.io';
import { GameManager } from './game/GameManager';
import { RoomManager } from './rooms/RoomManager';
import { ChatManager } from './chat/ChatManager';
import { PlayerManager } from './players/PlayerManager';
import { RatingManager } from './rating/RatingManager';
import { AchievementManager } from './achievements/AchievementManager';
import { TournamentManager } from './tournaments/TournamentManager';
import { SpectatorManager } from './spectators/SpectatorManager';
import { FriendsManager } from './friends/FriendsManager';
import { ProgressManager } from './progress/ProgressManager';
import { NotificationManager } from './notifications/NotificationManager';
import { PokerManager } from './games/poker/PokerManager';
declare const io: SocketIOServer<import("socket.io").DefaultEventsMap, import("socket.io").DefaultEventsMap, import("socket.io").DefaultEventsMap, any>;
declare const gameManager: GameManager;
declare const roomManager: RoomManager;
declare const chatManager: ChatManager;
declare const playerManager: PlayerManager;
declare const ratingManager: RatingManager;
declare const achievementManager: AchievementManager;
declare const tournamentManager: TournamentManager;
declare const spectatorManager: SpectatorManager;
declare const friendsManager: FriendsManager;
declare const progressManager: ProgressManager;
declare const notificationManager: NotificationManager;
declare const pokerManager: PokerManager;
export { io, gameManager, roomManager, chatManager, playerManager, ratingManager, achievementManager, tournamentManager, spectatorManager, friendsManager, progressManager, notificationManager, pokerManager };
//# sourceMappingURL=index.d.ts.map