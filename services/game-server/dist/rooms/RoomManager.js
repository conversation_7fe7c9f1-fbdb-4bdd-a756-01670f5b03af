"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoomManager = exports.RoomStatus = void 0;
exports.getRoomPublicInfo = getRoomPublicInfo;
exports.isRoomOwner = isRoomOwner;
const uuid_1 = require("uuid");
var RoomStatus;
(function (RoomStatus) {
    RoomStatus["WAITING"] = "waiting";
    RoomStatus["PLAYING"] = "playing";
    RoomStatus["FINISHED"] = "finished";
})(RoomStatus || (exports.RoomStatus = RoomStatus = {}));
class RoomManager {
    constructor() {
        this.rooms = new Map();
        this.playerRooms = new Map(); // playerId -> roomId
    }
    /**
     * Создает новую комнату
     */
    createRoom(name, owner, maxPlayers = 2) {
        // Проверяем, не находится ли игрок уже в комнате
        if (this.playerRooms.has(owner.id)) {
            throw new Error('Player is already in a room');
        }
        const room = {
            id: (0, uuid_1.v4)(),
            name,
            owner,
            players: [owner],
            maxPlayers: Math.max(2, Math.min(maxPlayers, 6)), // от 2 до 6 игроков
            status: RoomStatus.WAITING,
            createdAt: new Date(),
            isPrivate: false
        };
        this.rooms.set(room.id, room);
        this.playerRooms.set(owner.id, room.id);
        return room;
    }
    /**
     * Присоединяется к комнате
     */
    joinRoom(roomId, player) {
        const room = this.rooms.get(roomId);
        if (!room) {
            throw new Error('Room not found');
        }
        // Проверяем, не находится ли игрок уже в комнате
        if (this.playerRooms.has(player.id)) {
            throw new Error('Player is already in a room');
        }
        // Проверяем, есть ли место
        if (room.players.length >= room.maxPlayers) {
            throw new Error('Room is full');
        }
        // Проверяем статус комнаты
        if (room.status !== RoomStatus.WAITING) {
            throw new Error('Cannot join room - game is in progress');
        }
        room.players.push(player);
        this.playerRooms.set(player.id, room.id);
        return room;
    }
    /**
     * Покидает комнату
     */
    leaveRoom(roomId, player) {
        const room = this.rooms.get(roomId);
        if (!room)
            return null;
        const playerIndex = room.players.findIndex(p => p.id === player.id);
        if (playerIndex === -1)
            return null;
        // Удаляем игрока из комнаты
        room.players.splice(playerIndex, 1);
        this.playerRooms.delete(player.id);
        // Если комната пустая, удаляем её
        if (room.players.length === 0) {
            this.rooms.delete(roomId);
            return null;
        }
        // Если владелец покинул комнату, назначаем нового
        if (room.owner.id === player.id && room.players.length > 0) {
            room.owner = room.players[0];
        }
        return room;
    }
    /**
     * Получает комнату по ID
     */
    getRoom(roomId) {
        return this.rooms.get(roomId);
    }
    /**
     * Получает все комнаты
     */
    getAllRooms() {
        return Array.from(this.rooms.values());
    }
    /**
     * Получает публичные комнаты
     */
    getPublicRooms() {
        return this.getAllRooms()
            .filter(room => !room.isPrivate)
            .map(room => getRoomPublicInfo(room));
    }
    /**
     * Получает комнаты игрока
     */
    getPlayerRooms(player) {
        const roomId = this.playerRooms.get(player.id);
        if (!roomId)
            return [];
        const room = this.rooms.get(roomId);
        return room ? [room] : [];
    }
    /**
     * Получает комнату игрока
     */
    getPlayerRoom(player) {
        const roomId = this.playerRooms.get(player.id);
        if (!roomId)
            return undefined;
        return this.rooms.get(roomId);
    }
    /**
     * Проверяет, является ли игрок владельцем комнаты
     */
    isRoomOwner(roomId, player) {
        const room = this.rooms.get(roomId);
        return room ? room.owner.id === player.id : false;
    }
    /**
     * Устанавливает статус комнаты
     */
    setRoomStatus(roomId, status) {
        const room = this.rooms.get(roomId);
        if (!room)
            return false;
        room.status = status;
        return true;
    }
    /**
     * Устанавливает ID игры для комнаты
     */
    setRoomGameId(roomId, gameId) {
        const room = this.rooms.get(roomId);
        if (!room)
            return false;
        room.gameId = gameId;
        return true;
    }
    /**
     * Получает количество комнат
     */
    getRoomsCount() {
        return this.rooms.size;
    }
    /**
     * Получает статистику
     */
    getStats() {
        const rooms = this.getAllRooms();
        const waitingRooms = rooms.filter(r => r.status === RoomStatus.WAITING);
        const playingRooms = rooms.filter(r => r.status === RoomStatus.PLAYING);
        const finishedRooms = rooms.filter(r => r.status === RoomStatus.FINISHED);
        return {
            total: rooms.length,
            waiting: waitingRooms.length,
            playing: playingRooms.length,
            finished: finishedRooms.length,
            averagePlayersPerRoom: rooms.length > 0 ?
                rooms.reduce((sum, room) => sum + room.players.length, 0) / rooms.length : 0
        };
    }
    /**
     * Очищает пустые и старые комнаты
     */
    cleanupRooms(maxAge = 60 * 60 * 1000) {
        const now = new Date();
        let cleaned = 0;
        for (const [roomId, room] of this.rooms.entries()) {
            const age = now.getTime() - room.createdAt.getTime();
            // Удаляем пустые комнаты или очень старые завершенные комнаты
            if (room.players.length === 0 ||
                (room.status === RoomStatus.FINISHED && age > maxAge)) {
                // Очищаем связи игроков с комнатой
                room.players.forEach(player => {
                    this.playerRooms.delete(player.id);
                });
                this.rooms.delete(roomId);
                cleaned++;
            }
        }
        return cleaned;
    }
}
exports.RoomManager = RoomManager;
// Функции для работы с комнатами
function getRoomPublicInfo(room) {
    return {
        id: room.id,
        name: room.name,
        ownerName: room.owner.name,
        playerCount: room.players.length,
        maxPlayers: room.maxPlayers,
        status: room.status,
        createdAt: room.createdAt,
        isPrivate: room.isPrivate
    };
}
function isRoomOwner(room, player) {
    return room.owner.id === player.id;
}
//# sourceMappingURL=RoomManager.js.map