import { Player } from '../players/PlayerManager';
export declare enum RoomStatus {
    WAITING = "waiting",
    PLAYING = "playing",
    FINISHED = "finished"
}
export interface Room {
    id: string;
    name: string;
    owner: Player;
    players: Player[];
    maxPlayers: number;
    status: RoomStatus;
    createdAt: Date;
    gameId?: string;
    isPrivate: boolean;
}
export interface PublicRoomInfo {
    id: string;
    name: string;
    ownerName: string;
    playerCount: number;
    maxPlayers: number;
    status: RoomStatus;
    createdAt: Date;
    isPrivate: boolean;
}
export declare class RoomManager {
    private rooms;
    private playerRooms;
    /**
     * Создает новую комнату
     */
    createRoom(name: string, owner: Player, maxPlayers?: number): Room;
    /**
     * Присоединяется к комнате
     */
    joinRoom(roomId: string, player: Player): Room;
    /**
     * Покидает комнату
     */
    leaveRoom(roomId: string, player: Player): Room | null;
    /**
     * Получает комнату по ID
     */
    getRoom(roomId: string): Room | undefined;
    /**
     * Получает все комнаты
     */
    getAllRooms(): Room[];
    /**
     * Получает публичные комнаты
     */
    getPublicRooms(): PublicRoomInfo[];
    /**
     * Получает комнаты игрока
     */
    getPlayerRooms(player: Player): Room[];
    /**
     * Получает комнату игрока
     */
    getPlayerRoom(player: Player): Room | undefined;
    /**
     * Проверяет, является ли игрок владельцем комнаты
     */
    isRoomOwner(roomId: string, player: Player): boolean;
    /**
     * Устанавливает статус комнаты
     */
    setRoomStatus(roomId: string, status: RoomStatus): boolean;
    /**
     * Устанавливает ID игры для комнаты
     */
    setRoomGameId(roomId: string, gameId: string): boolean;
    /**
     * Получает количество комнат
     */
    getRoomsCount(): number;
    /**
     * Получает статистику
     */
    getStats(): {
        total: number;
        waiting: number;
        playing: number;
        finished: number;
        averagePlayersPerRoom: number;
    };
    /**
     * Очищает пустые и старые комнаты
     */
    cleanupRooms(maxAge?: number): number;
}
export declare function getRoomPublicInfo(room: Room): PublicRoomInfo;
export declare function isRoomOwner(room: Room, player: Player): boolean;
//# sourceMappingURL=RoomManager.d.ts.map