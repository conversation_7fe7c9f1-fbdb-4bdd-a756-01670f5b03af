"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PokerManager = void 0;
const PokerGame_1 = require("./PokerGame");
class PokerManager {
    constructor() {
        this.rooms = new Map();
        this.playerRooms = new Map(); // playerId -> roomId
    }
    /**
     * Создает новую покерную комнату
     */
    createRoom(creator, roomName, smallBlind = 5, bigBlind = 10, maxPlayers = 9, isPrivate = false, password) {
        const roomId = this.generateRoomId();
        const game = new PokerGame_1.PokerGame(roomId, smallBlind, bigBlind);
        const room = {
            id: roomId,
            name: roomName,
            game,
            players: [],
            maxPlayers,
            smallBlind,
            bigBlind,
            isPrivate,
            password,
            status: 'waiting',
            createdAt: new Date(),
            createdBy: creator.id
        };
        this.rooms.set(roomId, room);
        // Автоматически добавляем создателя в комнату
        this.joinRoom(roomId, creator);
        return room;
    }
    /**
     * Присоединяется к покерной комнате
     */
    joinRoom(roomId, player, password) {
        const room = this.rooms.get(roomId);
        if (!room) {
            throw new Error('Room not found');
        }
        if (room.status !== 'waiting') {
            throw new Error('Game already in progress');
        }
        if (room.players.length >= room.maxPlayers) {
            throw new Error('Room is full');
        }
        if (room.isPrivate && room.password !== password) {
            throw new Error('Invalid password');
        }
        if (room.players.some(p => p.id === player.id)) {
            throw new Error('Player already in room');
        }
        // Проверяем, не находится ли игрок в другой комнате
        const currentRoom = this.playerRooms.get(player.id);
        if (currentRoom) {
            this.leaveRoom(currentRoom, player);
        }
        room.players.push(player);
        this.playerRooms.set(player.id, roomId);
        // Добавляем игрока в покерную игру
        room.game.addPlayer(player.id, player.name);
        return true;
    }
    /**
     * Покидает покерную комнату
     */
    leaveRoom(roomId, player) {
        const room = this.rooms.get(roomId);
        if (!room) {
            return false;
        }
        const playerIndex = room.players.findIndex(p => p.id === player.id);
        if (playerIndex === -1) {
            return false;
        }
        room.players.splice(playerIndex, 1);
        this.playerRooms.delete(player.id);
        // Если комната пустая, удаляем её
        if (room.players.length === 0) {
            this.rooms.delete(roomId);
        }
        return true;
    }
    /**
     * Начинает покерную игру
     */
    startGame(roomId, playerId) {
        const room = this.rooms.get(roomId);
        if (!room) {
            throw new Error('Room not found');
        }
        if (room.createdBy !== playerId) {
            throw new Error('Only room creator can start the game');
        }
        if (room.players.length < 2) {
            throw new Error('Need at least 2 players to start');
        }
        if (room.status !== 'waiting') {
            throw new Error('Game already started');
        }
        room.status = 'playing';
        room.game.startNewHand();
        return room.game.getGameState();
    }
    /**
     * Выполняет действие в покерной игре
     */
    makeAction(roomId, playerId, action) {
        const room = this.rooms.get(roomId);
        if (!room) {
            throw new Error('Room not found');
        }
        if (room.status !== 'playing') {
            throw new Error('Game not in progress');
        }
        const success = room.game.makeAction(playerId, action);
        if (!success) {
            throw new Error('Invalid action');
        }
        const gameState = room.game.getGameState();
        // Проверяем, завершилась ли игра
        if (gameState.phase === 'finished') {
            room.status = 'finished';
            // Можно автоматически начать новую руку
            setTimeout(() => {
                if (room.players.length >= 2) {
                    room.status = 'playing';
                    room.game.startNewHand();
                }
                else {
                    room.status = 'waiting';
                }
            }, 5000); // 5 секунд между руками
        }
        return gameState;
    }
    /**
     * Получает состояние покерной игры
     */
    getGameState(roomId, playerId) {
        const room = this.rooms.get(roomId);
        if (!room) {
            return null;
        }
        return room.game.getPublicGameState(playerId);
    }
    /**
     * Получает информацию о комнате
     */
    getRoom(roomId) {
        return this.rooms.get(roomId);
    }
    /**
     * Получает список публичных комнат
     */
    getPublicRooms() {
        return Array.from(this.rooms.values())
            .filter(room => !room.isPrivate)
            .map(room => ({
            ...room,
            game: undefined // Не передаем игровое состояние в списке
        }));
    }
    /**
     * Получает комнаты игрока
     */
    getPlayerRooms(playerId) {
        const roomId = this.playerRooms.get(playerId);
        if (!roomId) {
            return [];
        }
        const room = this.rooms.get(roomId);
        return room ? [room] : [];
    }
    /**
     * Получает статистику покерных игр
     */
    getStats() {
        const totalRooms = this.rooms.size;
        const activeGames = Array.from(this.rooms.values()).filter(room => room.status === 'playing').length;
        const waitingRooms = Array.from(this.rooms.values()).filter(room => room.status === 'waiting').length;
        const totalPlayers = Array.from(this.rooms.values()).reduce((sum, room) => sum + room.players.length, 0);
        const blindLevels = Array.from(this.rooms.values()).reduce((acc, room) => {
            const key = `${room.smallBlind}/${room.bigBlind}`;
            acc[key] = (acc[key] || 0) + 1;
            return acc;
        }, {});
        return {
            totalRooms,
            activeGames,
            waitingRooms,
            totalPlayers,
            blindLevels
        };
    }
    /**
     * Очищает завершенные игры
     */
    cleanup() {
        let cleaned = 0;
        const now = Date.now();
        const maxAge = 60 * 60 * 1000; // 1 час
        for (const [roomId, room] of this.rooms.entries()) {
            // Удаляем старые завершенные игры без игроков
            if (room.status === 'finished' &&
                room.players.length === 0 &&
                now - room.createdAt.getTime() > maxAge) {
                this.rooms.delete(roomId);
                cleaned++;
            }
        }
        return cleaned;
    }
    /**
     * Получает публичную информацию о комнате
     */
    getRoomPublicInfo(room) {
        return {
            id: room.id,
            name: room.name,
            playerCount: room.players.length,
            maxPlayers: room.maxPlayers,
            smallBlind: room.smallBlind,
            bigBlind: room.bigBlind,
            status: room.status,
            isPrivate: room.isPrivate,
            createdAt: room.createdAt,
            players: room.players.map(player => ({
                id: player.id,
                name: player.name
            }))
        };
    }
    /**
     * Проверяет, может ли игрок выполнить действие
     */
    canPlayerAct(roomId, playerId) {
        const room = this.rooms.get(roomId);
        if (!room || room.status !== 'playing') {
            return false;
        }
        const gameState = room.game.getGameState();
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        return currentPlayer && currentPlayer.id === playerId && !currentPlayer.isFolded && !currentPlayer.isAllIn;
    }
    /**
     * Получает доступные действия для игрока
     */
    getAvailableActions(roomId, playerId) {
        const room = this.rooms.get(roomId);
        if (!room || room.status !== 'playing') {
            return [];
        }
        const gameState = room.game.getGameState();
        const player = gameState.players.find(p => p.id === playerId);
        if (!player || player.isFolded || player.isAllIn) {
            return [];
        }
        const actions = ['fold'];
        const callAmount = gameState.currentBet - player.currentBet;
        if (callAmount === 0) {
            actions.push('check');
            if (gameState.currentBet === 0) {
                actions.push('bet');
            }
        }
        else {
            if (player.chips >= callAmount) {
                actions.push('call');
            }
            if (player.chips >= callAmount + gameState.minRaise) {
                actions.push('raise');
            }
        }
        if (player.chips > 0) {
            actions.push('all_in');
        }
        return actions;
    }
    /**
     * Получает рекомендуемые размеры ставок
     */
    getSuggestedBets(roomId, playerId) {
        const room = this.rooms.get(roomId);
        if (!room) {
            return [];
        }
        const gameState = room.game.getGameState();
        const player = gameState.players.find(p => p.id === playerId);
        if (!player) {
            return [];
        }
        const pot = gameState.pot;
        const suggestions = [];
        // Минимальная ставка/рейз
        if (gameState.currentBet === 0) {
            suggestions.push(gameState.bigBlind);
        }
        else {
            suggestions.push(gameState.currentBet + gameState.minRaise);
        }
        // Размеры относительно банка
        const potSizes = [0.5, 0.75, 1, 1.5, 2];
        potSizes.forEach(size => {
            const amount = Math.round(pot * size);
            if (amount > suggestions[suggestions.length - 1] && amount <= player.chips) {
                suggestions.push(amount);
            }
        });
        // Олл-ин
        if (player.chips > suggestions[suggestions.length - 1]) {
            suggestions.push(player.chips);
        }
        return suggestions.slice(0, 5); // Максимум 5 предложений
    }
    // Утилиты
    generateRoomId() {
        return 'poker_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
}
exports.PokerManager = PokerManager;
//# sourceMappingURL=PokerManager.js.map