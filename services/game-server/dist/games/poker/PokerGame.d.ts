import { Card } from '../../types/Card';
export interface PokerPlayer {
    id: string;
    name: string;
    chips: number;
    holeCards: Card[];
    currentBet: number;
    totalBet: number;
    isActive: boolean;
    isFolded: boolean;
    isAllIn: boolean;
    position: number;
    lastAction?: PokerAction;
}
export interface PokerAction {
    type: 'fold' | 'check' | 'call' | 'bet' | 'raise' | 'all_in';
    amount?: number;
    timestamp: Date;
}
export interface PokerHand {
    cards: Card[];
    rank: HandRank;
    value: number;
    description: string;
}
export declare enum HandRank {
    HIGH_CARD = 1,
    PAIR = 2,
    TWO_PAIR = 3,
    THREE_OF_A_KIND = 4,
    STRAIGHT = 5,
    FLUSH = 6,
    FULL_HOUSE = 7,
    FOUR_OF_A_KIND = 8,
    STRAIGHT_FLUSH = 9,
    ROYAL_FLUSH = 10
}
export interface PokerGameState {
    id: string;
    players: PokerPlayer[];
    deck: Card[];
    communityCards: Card[];
    pot: number;
    sidePots: Array<{
        amount: number;
        eligiblePlayers: string[];
    }>;
    currentPlayerIndex: number;
    dealerIndex: number;
    smallBlindIndex: number;
    bigBlindIndex: number;
    phase: 'preflop' | 'flop' | 'turn' | 'river' | 'showdown' | 'finished';
    currentBet: number;
    minRaise: number;
    smallBlind: number;
    bigBlind: number;
    actionHistory: Array<{
        playerId: string;
        action: PokerAction;
    }>;
    winners: Array<{
        playerId: string;
        hand: PokerHand;
        amount: number;
    }>;
    handNumber: number;
}
export declare class PokerGame {
    private gameState;
    private readonly maxPlayers;
    private readonly startingChips;
    constructor(gameId: string, smallBlind?: number, bigBlind?: number);
    /**
     * Добавляет игрока в игру
     */
    addPlayer(playerId: string, playerName: string): boolean;
    /**
     * Начинает новую руку
     */
    startNewHand(): void;
    /**
     * Выполняет действие игрока
     */
    makeAction(playerId: string, action: PokerAction): boolean;
    /**
     * Проверяет валидность действия
     */
    private isValidAction;
    /**
     * Выполняет действие игрока
     */
    private executeAction;
    /**
     * Добавляет ставку игрока
     */
    private addPlayerBet;
    /**
     * Определяет лучшую покерную комбинацию
     */
    getBestHand(holeCards: Card[], communityCards: Card[]): PokerHand;
    /**
     * Оценивает покерную комбинацию
     */
    private evaluateHand;
    private createDeck;
    private shuffleDeck;
    private dealHoleCards;
    private postBlinds;
    private movePositions;
    private getActivePlayers;
    private getCurrentPlayer;
    private moveToNextPlayer;
    private isBettingRoundComplete;
    private advancePhase;
    private dealCommunityCards;
    private determineWinners;
    private getCardValue;
    private isStraight;
    private getRankCounts;
    private getCombinations;
    getGameState(): PokerGameState;
    getPublicGameState(playerId?: string): any;
}
//# sourceMappingURL=PokerGame.d.ts.map