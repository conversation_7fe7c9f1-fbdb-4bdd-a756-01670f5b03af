"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PokerGame = exports.HandRank = void 0;
const Card_1 = require("../../types/Card");
var HandRank;
(function (HandRank) {
    HandRank[HandRank["HIGH_CARD"] = 1] = "HIGH_CARD";
    HandRank[HandRank["PAIR"] = 2] = "PAIR";
    HandRank[HandRank["TWO_PAIR"] = 3] = "TWO_PAIR";
    HandRank[HandRank["THREE_OF_A_KIND"] = 4] = "THREE_OF_A_KIND";
    HandRank[HandRank["STRAIGHT"] = 5] = "STRAIGHT";
    HandRank[HandRank["FLUSH"] = 6] = "FLUSH";
    HandRank[HandRank["FULL_HOUSE"] = 7] = "FULL_HOUSE";
    HandRank[HandRank["FOUR_OF_A_KIND"] = 8] = "FOUR_OF_A_KIND";
    HandRank[HandRank["STRAIGHT_FLUSH"] = 9] = "STRAIGHT_FLUSH";
    HandRank[HandRank["ROYAL_FLUSH"] = 10] = "ROYAL_FLUSH";
})(HandRank || (exports.HandRank = HandRank = {}));
class PokerGame {
    constructor(gameId, smallBlind = 5, bigBlind = 10) {
        this.maxPlayers = 9;
        this.startingChips = 1000;
        this.gameState = {
            id: gameId,
            players: [],
            deck: [],
            communityCards: [],
            pot: 0,
            sidePots: [],
            currentPlayerIndex: 0,
            dealerIndex: 0,
            smallBlindIndex: 1,
            bigBlindIndex: 2,
            phase: 'preflop',
            currentBet: 0,
            minRaise: bigBlind,
            smallBlind,
            bigBlind,
            actionHistory: [],
            winners: [],
            handNumber: 1
        };
    }
    /**
     * Добавляет игрока в игру
     */
    addPlayer(playerId, playerName) {
        if (this.gameState.players.length >= this.maxPlayers) {
            return false;
        }
        if (this.gameState.players.some(p => p.id === playerId)) {
            return false;
        }
        const player = {
            id: playerId,
            name: playerName,
            chips: this.startingChips,
            holeCards: [],
            currentBet: 0,
            totalBet: 0,
            isActive: true,
            isFolded: false,
            isAllIn: false,
            position: this.gameState.players.length
        };
        this.gameState.players.push(player);
        return true;
    }
    /**
     * Начинает новую руку
     */
    startNewHand() {
        if (this.gameState.players.length < 2) {
            throw new Error('Need at least 2 players to start');
        }
        // Сброс состояния игроков
        this.gameState.players.forEach(player => {
            player.holeCards = [];
            player.currentBet = 0;
            player.totalBet = 0;
            player.isFolded = false;
            player.isAllIn = false;
            player.lastAction = undefined;
            player.isActive = player.chips > 0;
        });
        // Сброс игрового состояния
        this.gameState.communityCards = [];
        this.gameState.pot = 0;
        this.gameState.sidePots = [];
        this.gameState.currentBet = 0;
        this.gameState.minRaise = this.gameState.bigBlind;
        this.gameState.phase = 'preflop';
        this.gameState.actionHistory = [];
        this.gameState.winners = [];
        // Перемещение позиций
        this.movePositions();
        // Создание и перемешивание колоды
        this.createDeck();
        this.shuffleDeck();
        // Постановка блайндов
        this.postBlinds();
        // Раздача карт
        this.dealHoleCards();
        // Установка текущего игрока (после большого блайнда)
        this.gameState.currentPlayerIndex = (this.gameState.bigBlindIndex + 1) % this.getActivePlayers().length;
    }
    /**
     * Выполняет действие игрока
     */
    makeAction(playerId, action) {
        const player = this.getCurrentPlayer();
        if (!player || player.id !== playerId) {
            return false;
        }
        if (!this.isValidAction(player, action)) {
            return false;
        }
        this.executeAction(player, action);
        this.gameState.actionHistory.push({ playerId, action });
        // Проверяем, завершен ли раунд ставок
        if (this.isBettingRoundComplete()) {
            this.advancePhase();
        }
        else {
            this.moveToNextPlayer();
        }
        return true;
    }
    /**
     * Проверяет валидность действия
     */
    isValidAction(player, action) {
        if (player.isFolded || player.isAllIn) {
            return false;
        }
        const callAmount = this.gameState.currentBet - player.currentBet;
        switch (action.type) {
            case 'fold':
                return true;
            case 'check':
                return callAmount === 0;
            case 'call':
                return callAmount > 0 && player.chips >= callAmount;
            case 'bet':
                return this.gameState.currentBet === 0 &&
                    action.amount !== undefined &&
                    action.amount >= this.gameState.bigBlind &&
                    player.chips >= action.amount;
            case 'raise':
                return this.gameState.currentBet > 0 &&
                    action.amount !== undefined &&
                    action.amount >= this.gameState.currentBet + this.gameState.minRaise &&
                    player.chips >= action.amount - player.currentBet;
            case 'all_in':
                return player.chips > 0;
            default:
                return false;
        }
    }
    /**
     * Выполняет действие игрока
     */
    executeAction(player, action) {
        player.lastAction = action;
        switch (action.type) {
            case 'fold':
                player.isFolded = true;
                break;
            case 'check':
                // Ничего не делаем
                break;
            case 'call':
                const callAmount = this.gameState.currentBet - player.currentBet;
                this.addPlayerBet(player, callAmount);
                break;
            case 'bet':
            case 'raise':
                if (action.amount !== undefined) {
                    const betAmount = action.amount - player.currentBet;
                    this.addPlayerBet(player, betAmount);
                    this.gameState.currentBet = action.amount;
                    this.gameState.minRaise = action.amount - this.gameState.currentBet;
                }
                break;
            case 'all_in':
                this.addPlayerBet(player, player.chips);
                player.isAllIn = true;
                if (player.currentBet > this.gameState.currentBet) {
                    this.gameState.currentBet = player.currentBet;
                }
                break;
        }
    }
    /**
     * Добавляет ставку игрока
     */
    addPlayerBet(player, amount) {
        const actualAmount = Math.min(amount, player.chips);
        player.chips -= actualAmount;
        player.currentBet += actualAmount;
        player.totalBet += actualAmount;
        this.gameState.pot += actualAmount;
        if (player.chips === 0) {
            player.isAllIn = true;
        }
    }
    /**
     * Определяет лучшую покерную комбинацию
     */
    getBestHand(holeCards, communityCards) {
        const allCards = [...holeCards, ...communityCards];
        const combinations = this.getCombinations(allCards, 5);
        let bestHand = {
            cards: combinations[0],
            rank: HandRank.HIGH_CARD,
            value: 0,
            description: 'High Card'
        };
        for (const combination of combinations) {
            const hand = this.evaluateHand(combination);
            if (hand.rank > bestHand.rank || (hand.rank === bestHand.rank && hand.value > bestHand.value)) {
                bestHand = hand;
            }
        }
        return bestHand;
    }
    /**
     * Оценивает покерную комбинацию
     */
    evaluateHand(cards) {
        const sortedCards = cards.sort((a, b) => this.getCardValue(b.rank) - this.getCardValue(a.rank));
        const ranks = sortedCards.map(card => this.getCardValue(card.rank));
        const suits = sortedCards.map(card => card.suit);
        const isFlush = suits.every(suit => suit === suits[0]);
        const isStraight = this.isStraight(ranks);
        const rankCounts = this.getRankCounts(ranks);
        // Royal Flush
        if (isFlush && isStraight && ranks[0] === 14) {
            return {
                cards: sortedCards,
                rank: HandRank.ROYAL_FLUSH,
                value: 10,
                description: 'Royal Flush'
            };
        }
        // Straight Flush
        if (isFlush && isStraight) {
            return {
                cards: sortedCards,
                rank: HandRank.STRAIGHT_FLUSH,
                value: ranks[0],
                description: 'Straight Flush'
            };
        }
        // Four of a Kind
        if (rankCounts.some(count => count === 4)) {
            const fourKind = ranks.find(rank => ranks.filter(r => r === rank).length === 4);
            return {
                cards: sortedCards,
                rank: HandRank.FOUR_OF_A_KIND,
                value: fourKind * 1000 + ranks.find(r => r !== fourKind),
                description: 'Four of a Kind'
            };
        }
        // Full House
        const threeKind = ranks.find(rank => ranks.filter(r => r === rank).length === 3);
        const pair = ranks.find(rank => ranks.filter(r => r === rank).length === 2);
        if (threeKind && pair) {
            return {
                cards: sortedCards,
                rank: HandRank.FULL_HOUSE,
                value: threeKind * 1000 + pair,
                description: 'Full House'
            };
        }
        // Flush
        if (isFlush) {
            return {
                cards: sortedCards,
                rank: HandRank.FLUSH,
                value: ranks.reduce((sum, rank, index) => sum + rank * Math.pow(100, 4 - index), 0),
                description: 'Flush'
            };
        }
        // Straight
        if (isStraight) {
            return {
                cards: sortedCards,
                rank: HandRank.STRAIGHT,
                value: ranks[0],
                description: 'Straight'
            };
        }
        // Three of a Kind
        if (threeKind) {
            const kickers = ranks.filter(r => r !== threeKind).slice(0, 2);
            return {
                cards: sortedCards,
                rank: HandRank.THREE_OF_A_KIND,
                value: threeKind * 10000 + kickers[0] * 100 + kickers[1],
                description: 'Three of a Kind'
            };
        }
        // Two Pair
        const pairs = ranks.filter(rank => ranks.filter(r => r === rank).length === 2);
        if (pairs.length >= 4) { // Two pairs
            const uniquePairs = [...new Set(pairs)].sort((a, b) => b - a);
            const kicker = ranks.find(r => !uniquePairs.includes(r));
            return {
                cards: sortedCards,
                rank: HandRank.TWO_PAIR,
                value: uniquePairs[0] * 10000 + uniquePairs[1] * 100 + kicker,
                description: 'Two Pair'
            };
        }
        // One Pair
        if (pair) {
            const kickers = ranks.filter(r => r !== pair).slice(0, 3);
            return {
                cards: sortedCards,
                rank: HandRank.PAIR,
                value: pair * 1000000 + kickers.reduce((sum, rank, index) => sum + rank * Math.pow(100, 2 - index), 0),
                description: 'Pair'
            };
        }
        // High Card
        return {
            cards: sortedCards,
            rank: HandRank.HIGH_CARD,
            value: ranks.reduce((sum, rank, index) => sum + rank * Math.pow(100, 4 - index), 0),
            description: 'High Card'
        };
    }
    // Утилиты
    createDeck() {
        this.gameState.deck = Card_1.CardUtils.createDeck();
    }
    shuffleDeck() {
        this.gameState.deck = Card_1.CardUtils.shuffleDeck(this.gameState.deck);
    }
    dealHoleCards() {
        // Раздаем по 2 карты каждому игроку
        for (let i = 0; i < 2; i++) {
            for (const player of this.getActivePlayers()) {
                if (this.gameState.deck.length > 0) {
                    player.holeCards.push(this.gameState.deck.pop());
                }
            }
        }
    }
    postBlinds() {
        const activePlayers = this.getActivePlayers();
        const smallBlindPlayer = activePlayers[this.gameState.smallBlindIndex];
        const bigBlindPlayer = activePlayers[this.gameState.bigBlindIndex];
        this.addPlayerBet(smallBlindPlayer, this.gameState.smallBlind);
        this.addPlayerBet(bigBlindPlayer, this.gameState.bigBlind);
        this.gameState.currentBet = this.gameState.bigBlind;
    }
    movePositions() {
        const activeCount = this.getActivePlayers().length;
        this.gameState.dealerIndex = (this.gameState.dealerIndex + 1) % activeCount;
        this.gameState.smallBlindIndex = (this.gameState.dealerIndex + 1) % activeCount;
        this.gameState.bigBlindIndex = (this.gameState.dealerIndex + 2) % activeCount;
    }
    getActivePlayers() {
        return this.gameState.players.filter(p => p.isActive);
    }
    getCurrentPlayer() {
        const activePlayers = this.getActivePlayers();
        return activePlayers[this.gameState.currentPlayerIndex] || null;
    }
    moveToNextPlayer() {
        const activePlayers = this.getActivePlayers();
        do {
            this.gameState.currentPlayerIndex = (this.gameState.currentPlayerIndex + 1) % activePlayers.length;
        } while (activePlayers[this.gameState.currentPlayerIndex].isFolded ||
            activePlayers[this.gameState.currentPlayerIndex].isAllIn);
    }
    isBettingRoundComplete() {
        const activePlayers = this.getActivePlayers().filter(p => !p.isFolded);
        if (activePlayers.length <= 1) {
            return true;
        }
        const playersInAction = activePlayers.filter(p => !p.isAllIn);
        if (playersInAction.length === 0) {
            return true;
        }
        return playersInAction.every(p => p.currentBet === this.gameState.currentBet && p.lastAction);
    }
    advancePhase() {
        // Сброс ставок для следующего раунда
        this.gameState.players.forEach(p => p.currentBet = 0);
        this.gameState.currentBet = 0;
        this.gameState.currentPlayerIndex = this.gameState.smallBlindIndex;
        switch (this.gameState.phase) {
            case 'preflop':
                this.gameState.phase = 'flop';
                this.dealCommunityCards(3);
                break;
            case 'flop':
                this.gameState.phase = 'turn';
                this.dealCommunityCards(1);
                break;
            case 'turn':
                this.gameState.phase = 'river';
                this.dealCommunityCards(1);
                break;
            case 'river':
                this.gameState.phase = 'showdown';
                this.determineWinners();
                break;
        }
    }
    dealCommunityCards(count) {
        // Сжигаем одну карту
        this.gameState.deck.pop();
        // Раздаем общие карты
        for (let i = 0; i < count; i++) {
            if (this.gameState.deck.length > 0) {
                this.gameState.communityCards.push(this.gameState.deck.pop());
            }
        }
    }
    determineWinners() {
        const activePlayers = this.getActivePlayers().filter(p => !p.isFolded);
        const playerHands = activePlayers.map(player => ({
            player,
            hand: this.getBestHand(player.holeCards, this.gameState.communityCards)
        }));
        // Сортируем по силе руки
        playerHands.sort((a, b) => {
            if (a.hand.rank !== b.hand.rank) {
                return b.hand.rank - a.hand.rank;
            }
            return b.hand.value - a.hand.value;
        });
        // Определяем победителей (может быть несколько при равных руках)
        const bestRank = playerHands[0].hand.rank;
        const bestValue = playerHands[0].hand.value;
        const winners = playerHands.filter(ph => ph.hand.rank === bestRank && ph.hand.value === bestValue);
        // Распределяем банк
        const winAmount = Math.floor(this.gameState.pot / winners.length);
        this.gameState.winners = winners.map(winner => ({
            playerId: winner.player.id,
            hand: winner.hand,
            amount: winAmount
        }));
        // Добавляем выигрыш к фишкам
        winners.forEach(winner => {
            winner.player.chips += winAmount;
        });
        this.gameState.phase = 'finished';
    }
    // Утилиты для оценки рук
    getCardValue(rank) {
        return Card_1.CardUtils.getCardValue(rank);
    }
    isStraight(ranks) {
        const uniqueRanks = [...new Set(ranks)].sort((a, b) => b - a);
        if (uniqueRanks.length !== 5)
            return false;
        // Проверяем обычный стрит
        for (let i = 0; i < 4; i++) {
            if (uniqueRanks[i] - uniqueRanks[i + 1] !== 1) {
                // Проверяем A-2-3-4-5 стрит
                if (uniqueRanks[0] === 14 && uniqueRanks[1] === 5 && uniqueRanks[2] === 4 &&
                    uniqueRanks[3] === 3 && uniqueRanks[4] === 2) {
                    return true;
                }
                return false;
            }
        }
        return true;
    }
    getRankCounts(ranks) {
        const counts = {};
        ranks.forEach(rank => counts[rank] = (counts[rank] || 0) + 1);
        return Object.values(counts);
    }
    getCombinations(arr, size) {
        if (size > arr.length)
            return [];
        if (size === 1)
            return arr.map(item => [item]);
        const combinations = [];
        for (let i = 0; i <= arr.length - size; i++) {
            const head = arr[i];
            const tailCombinations = this.getCombinations(arr.slice(i + 1), size - 1);
            tailCombinations.forEach(tail => combinations.push([head, ...tail]));
        }
        return combinations;
    }
    // Геттеры
    getGameState() {
        return { ...this.gameState };
    }
    getPublicGameState(playerId) {
        const publicState = {
            ...this.gameState,
            players: this.gameState.players.map(player => ({
                ...player,
                holeCards: player.id === playerId ? player.holeCards : player.holeCards.map(() => ({ suit: 'hidden', rank: 'hidden' }))
            })),
            deck: undefined // Скрываем колоду
        };
        return publicState;
    }
}
exports.PokerGame = PokerGame;
//# sourceMappingURL=PokerGame.js.map