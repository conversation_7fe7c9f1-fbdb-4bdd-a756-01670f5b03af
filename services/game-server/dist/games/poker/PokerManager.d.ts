import { PokerGame, PokerGameState, PokerAction } from './PokerGame';
import { Player } from '../../players/PlayerManager';
export interface PokerRoom {
    id: string;
    name: string;
    game: PokerGame;
    players: Player[];
    maxPlayers: number;
    smallBlind: number;
    bigBlind: number;
    isPrivate: boolean;
    password?: string;
    status: 'waiting' | 'playing' | 'finished';
    createdAt: Date;
    createdBy: string;
}
export declare class PokerManager {
    private rooms;
    private playerRooms;
    /**
     * Создает новую покерную комнату
     */
    createRoom(creator: Player, roomName: string, smallBlind?: number, bigBlind?: number, maxPlayers?: number, isPrivate?: boolean, password?: string): PokerRoom;
    /**
     * Присоединяется к покерной комнате
     */
    joinRoom(roomId: string, player: Player, password?: string): boolean;
    /**
     * Покидает покерную комнату
     */
    leaveRoom(roomId: string, player: Player): boolean;
    /**
     * Начинает покерную игру
     */
    startGame(roomId: string, playerId: string): PokerGameState;
    /**
     * Выполняет действие в покерной игре
     */
    makeAction(roomId: string, playerId: string, action: PokerAction): PokerGameState;
    /**
     * Получает состояние покерной игры
     */
    getGameState(roomId: string, playerId?: string): PokerGameState | null;
    /**
     * Получает информацию о комнате
     */
    getRoom(roomId: string): PokerRoom | undefined;
    /**
     * Получает список публичных комнат
     */
    getPublicRooms(): PokerRoom[];
    /**
     * Получает комнаты игрока
     */
    getPlayerRooms(playerId: string): PokerRoom[];
    /**
     * Получает статистику покерных игр
     */
    getStats(): {
        totalRooms: number;
        activeGames: number;
        waitingRooms: number;
        totalPlayers: number;
        blindLevels: Record<string, number>;
    };
    /**
     * Очищает завершенные игры
     */
    cleanup(): number;
    /**
     * Получает публичную информацию о комнате
     */
    getRoomPublicInfo(room: PokerRoom): any;
    /**
     * Проверяет, может ли игрок выполнить действие
     */
    canPlayerAct(roomId: string, playerId: string): boolean;
    /**
     * Получает доступные действия для игрока
     */
    getAvailableActions(roomId: string, playerId: string): string[];
    /**
     * Получает рекомендуемые размеры ставок
     */
    getSuggestedBets(roomId: string, playerId: string): number[];
    private generateRoomId;
}
//# sourceMappingURL=PokerManager.d.ts.map