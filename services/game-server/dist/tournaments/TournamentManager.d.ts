import { Player } from '../players/PlayerManager';
import { GameManager } from '../game/GameManager';
import { RatingManager } from '../rating/RatingManager';
export interface Tournament {
    id: string;
    name: string;
    description: string;
    type: 'single_elimination' | 'double_elimination' | 'round_robin';
    status: 'registration' | 'starting' | 'in_progress' | 'finished';
    maxParticipants: number;
    minParticipants: number;
    entryFee: number;
    prizePool: {
        first: number;
        second: number;
        third: number;
    };
    participants: TournamentParticipant[];
    bracket: TournamentBracket;
    currentRound: number;
    totalRounds: number;
    createdAt: Date;
    startedAt?: Date;
    finishedAt?: Date;
    createdBy: string;
    settings: TournamentSettings;
}
export interface TournamentParticipant {
    playerId: string;
    playerName: string;
    rating: number;
    seed: number;
    registeredAt: Date;
    status: 'registered' | 'active' | 'eliminated' | 'winner';
    currentMatch?: string;
    wins: number;
    losses: number;
}
export interface TournamentMatch {
    id: string;
    tournamentId: string;
    round: number;
    position: number;
    player1?: TournamentParticipant;
    player2?: TournamentParticipant;
    winner?: TournamentParticipant;
    gameId?: string;
    status: 'pending' | 'ready' | 'in_progress' | 'finished';
    startedAt?: Date;
    finishedAt?: Date;
    nextMatchId?: string;
}
export interface TournamentBracket {
    matches: Map<string, TournamentMatch>;
    rounds: Map<number, string[]>;
}
export interface TournamentSettings {
    autoStart: boolean;
    allowSpectators: boolean;
    timeLimit: number;
    ratingRestriction?: {
        min: number;
        max: number;
    };
}
export declare class TournamentManager {
    private tournaments;
    private gameManager;
    private ratingManager;
    constructor(gameManager: GameManager, ratingManager: RatingManager);
    /**
     * Создает новый турнир
     */
    createTournament(name: string, description: string, type: Tournament['type'], maxParticipants: number, createdBy: string, settings?: Partial<TournamentSettings>): Tournament;
    /**
     * Регистрирует игрока на турнир
     */
    registerPlayer(tournamentId: string, player: Player): TournamentParticipant;
    /**
     * Запускает турнир
     */
    startTournament(tournamentId: string): Tournament;
    /**
     * Генерирует турнирную сетку
     */
    private generateBracket;
    /**
     * Генерирует сетку на выбывание
     */
    private generateSingleEliminationBracket;
    /**
     * Связывает матчи в турнирной сетке
     */
    private linkMatches;
    /**
     * Запускает раунд турнира
     */
    private startRound;
    /**
     * Обрабатывает результат матча
     */
    processMatchResult(tournamentId: string, matchId: string, winnerId: string): void;
    /**
     * Проверяет завершение раунда
     */
    private checkRoundCompletion;
    /**
     * Завершает турнир
     */
    private finishTournament;
    /**
     * Распределяет призы
     */
    private distributePrizes;
    private generateTournamentId;
    private generateMatchId;
    private isPowerOfTwo;
    private calculateTotalRounds;
    private calculateEntryFee;
    private calculatePrizePool;
    getTournament(id: string): Tournament | undefined;
    getAllTournaments(): Tournament[];
    getActiveTournaments(): Tournament[];
    getPlayerTournaments(playerId: string): Tournament[];
    private generateDoubleEliminationBracket;
    private generateRoundRobinBracket;
}
//# sourceMappingURL=TournamentManager.d.ts.map