{"version": 3, "file": "TournamentManager.d.ts", "sourceRoot": "", "sources": ["../../src/tournaments/TournamentManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,0BAA0B,CAAC;AAClD,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAExD,MAAM,WAAW,UAAU;IACzB,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,oBAAoB,GAAG,oBAAoB,GAAG,aAAa,CAAC;IAClE,MAAM,EAAE,cAAc,GAAG,UAAU,GAAG,aAAa,GAAG,UAAU,CAAC;IACjE,eAAe,EAAE,MAAM,CAAC;IACxB,eAAe,EAAE,MAAM,CAAC;IACxB,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE;QACT,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,EAAE,MAAM,CAAC;QACf,KAAK,EAAE,MAAM,CAAC;KACf,CAAC;IACF,YAAY,EAAE,qBAAqB,EAAE,CAAC;IACtC,OAAO,EAAE,iBAAiB,CAAC;IAC3B,YAAY,EAAE,MAAM,CAAC;IACrB,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,CAAC,EAAE,IAAI,CAAC;IACjB,UAAU,CAAC,EAAE,IAAI,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE,kBAAkB,CAAC;CAC9B;AAED,MAAM,WAAW,qBAAqB;IACpC,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE,MAAM,CAAC;IACnB,MAAM,EAAE,MAAM,CAAC;IACf,IAAI,EAAE,MAAM,CAAC;IACb,YAAY,EAAE,IAAI,CAAC;IACnB,MAAM,EAAE,YAAY,GAAG,QAAQ,GAAG,YAAY,GAAG,QAAQ,CAAC;IAC1D,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,WAAW,eAAe;IAC9B,EAAE,EAAE,MAAM,CAAC;IACX,YAAY,EAAE,MAAM,CAAC;IACrB,KAAK,EAAE,MAAM,CAAC;IACd,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,CAAC,EAAE,qBAAqB,CAAC;IAChC,OAAO,CAAC,EAAE,qBAAqB,CAAC;IAChC,MAAM,CAAC,EAAE,qBAAqB,CAAC;IAC/B,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,SAAS,GAAG,OAAO,GAAG,aAAa,GAAG,UAAU,CAAC;IACzD,SAAS,CAAC,EAAE,IAAI,CAAC;IACjB,UAAU,CAAC,EAAE,IAAI,CAAC;IAClB,WAAW,CAAC,EAAE,MAAM,CAAC;CACtB;AAED,MAAM,WAAW,iBAAiB;IAChC,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;IACtC,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;CAC/B;AAED,MAAM,WAAW,kBAAkB;IACjC,SAAS,EAAE,OAAO,CAAC;IACnB,eAAe,EAAE,OAAO,CAAC;IACzB,SAAS,EAAE,MAAM,CAAC;IAClB,iBAAiB,CAAC,EAAE;QAClB,GAAG,EAAE,MAAM,CAAC;QACZ,GAAG,EAAE,MAAM,CAAC;KACb,CAAC;CACH;AAED,qBAAa,iBAAiB;IAC5B,OAAO,CAAC,WAAW,CAAsC;IACzD,OAAO,CAAC,WAAW,CAAc;IACjC,OAAO,CAAC,aAAa,CAAgB;gBAEzB,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa;IAKlE;;OAEG;IACH,gBAAgB,CACd,IAAI,EAAE,MAAM,EACZ,WAAW,EAAE,MAAM,EACnB,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC,EACxB,eAAe,EAAE,MAAM,EACvB,SAAS,EAAE,MAAM,EACjB,QAAQ,GAAE,OAAO,CAAC,kBAAkB,CAAM,GACzC,UAAU;IAyCb;;OAEG;IACH,cAAc,CAAC,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,qBAAqB;IAuD3E;;OAEG;IACH,eAAe,CAAC,YAAY,EAAE,MAAM,GAAG,UAAU;IAkCjD;;OAEG;IACH,OAAO,CAAC,eAAe;IAcvB;;OAEG;IACH,OAAO,CAAC,gCAAgC;IAmDxC;;OAEG;IACH,OAAO,CAAC,WAAW;IAoBnB;;OAEG;IACH,OAAO,CAAC,UAAU;IAelB;;OAEG;IACH,kBAAkB,CAAC,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI;IA+DjF;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAa5B;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAQxB;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAaxB,OAAO,CAAC,oBAAoB;IAI5B,OAAO,CAAC,eAAe;IAIvB,OAAO,CAAC,YAAY;IAIpB,OAAO,CAAC,oBAAoB;IAa5B,OAAO,CAAC,iBAAiB;IAIzB,OAAO,CAAC,kBAAkB;IAU1B,aAAa,CAAC,EAAE,EAAE,MAAM,GAAG,UAAU,GAAG,SAAS;IAIjD,iBAAiB,IAAI,UAAU,EAAE;IAIjC,oBAAoB,IAAI,UAAU,EAAE;IAKpC,oBAAoB,CAAC,QAAQ,EAAE,MAAM,GAAG,UAAU,EAAE;IAMpD,OAAO,CAAC,gCAAgC;IAKxC,OAAO,CAAC,yBAAyB;CAIlC"}