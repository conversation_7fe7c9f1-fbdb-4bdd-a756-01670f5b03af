"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TournamentManager = void 0;
class TournamentManager {
    constructor(gameManager, ratingManager) {
        this.tournaments = new Map();
        this.gameManager = gameManager;
        this.ratingManager = ratingManager;
    }
    /**
     * Создает новый турнир
     */
    createTournament(name, description, type, maxParticipants, createdBy, settings = {}) {
        const tournamentId = this.generateTournamentId();
        // Валидация размера турнира (должен быть степенью 2 для elimination)
        if ((type === 'single_elimination' || type === 'double_elimination') &&
            !this.isPowerOfTwo(maxParticipants)) {
            throw new Error('Tournament size must be a power of 2 for elimination tournaments');
        }
        const tournament = {
            id: tournamentId,
            name,
            description,
            type,
            status: 'registration',
            maxParticipants,
            minParticipants: Math.max(2, Math.floor(maxParticipants / 4)),
            entryFee: this.calculateEntryFee(maxParticipants),
            prizePool: this.calculatePrizePool(maxParticipants),
            participants: [],
            bracket: {
                matches: new Map(),
                rounds: new Map()
            },
            currentRound: 0,
            totalRounds: this.calculateTotalRounds(type, maxParticipants),
            createdAt: new Date(),
            createdBy,
            settings: {
                autoStart: true,
                allowSpectators: true,
                timeLimit: 30,
                ...settings
            }
        };
        this.tournaments.set(tournamentId, tournament);
        return tournament;
    }
    /**
     * Регистрирует игрока на турнир
     */
    registerPlayer(tournamentId, player) {
        const tournament = this.tournaments.get(tournamentId);
        if (!tournament) {
            throw new Error('Tournament not found');
        }
        if (tournament.status !== 'registration') {
            throw new Error('Tournament registration is closed');
        }
        if (tournament.participants.length >= tournament.maxParticipants) {
            throw new Error('Tournament is full');
        }
        // Проверяем, не зарегистрирован ли уже игрок
        if (tournament.participants.some(p => p.playerId === player.id)) {
            throw new Error('Player already registered');
        }
        // Проверяем рейтинговые ограничения
        const playerRating = this.ratingManager.getPlayerStats(player.id);
        if (tournament.settings.ratingRestriction && playerRating) {
            const { min, max } = tournament.settings.ratingRestriction;
            if (playerRating.rating < min || playerRating.rating > max) {
                throw new Error(`Player rating must be between ${min} and ${max}`);
            }
        }
        // Проверяем, хватает ли рейтинговых очков для входа
        if (playerRating && playerRating.rating < tournament.entryFee) {
            throw new Error(`Insufficient rating points. Required: ${tournament.entryFee}`);
        }
        const participant = {
            playerId: player.id,
            playerName: player.name,
            rating: playerRating?.rating || 1200,
            seed: tournament.participants.length + 1,
            registeredAt: new Date(),
            status: 'registered',
            wins: 0,
            losses: 0
        };
        tournament.participants.push(participant);
        // Автоматический старт если турнир заполнен
        if (tournament.settings.autoStart &&
            tournament.participants.length === tournament.maxParticipants) {
            this.startTournament(tournamentId);
        }
        return participant;
    }
    /**
     * Запускает турнир
     */
    startTournament(tournamentId) {
        const tournament = this.tournaments.get(tournamentId);
        if (!tournament) {
            throw new Error('Tournament not found');
        }
        if (tournament.status !== 'registration') {
            throw new Error('Tournament cannot be started');
        }
        if (tournament.participants.length < tournament.minParticipants) {
            throw new Error(`Not enough participants. Minimum: ${tournament.minParticipants}`);
        }
        // Сортируем участников по рейтингу для сидинга
        tournament.participants.sort((a, b) => b.rating - a.rating);
        tournament.participants.forEach((participant, index) => {
            participant.seed = index + 1;
            participant.status = 'active';
        });
        // Создаем турнирную сетку
        this.generateBracket(tournament);
        tournament.status = 'starting';
        tournament.startedAt = new Date();
        tournament.currentRound = 1;
        // Запускаем первый раунд
        this.startRound(tournament, 1);
        return tournament;
    }
    /**
     * Генерирует турнирную сетку
     */
    generateBracket(tournament) {
        switch (tournament.type) {
            case 'single_elimination':
                this.generateSingleEliminationBracket(tournament);
                break;
            case 'double_elimination':
                this.generateDoubleEliminationBracket(tournament);
                break;
            case 'round_robin':
                this.generateRoundRobinBracket(tournament);
                break;
        }
    }
    /**
     * Генерирует сетку на выбывание
     */
    generateSingleEliminationBracket(tournament) {
        const participants = [...tournament.participants];
        const totalRounds = Math.log2(participants.length);
        // Создаем матчи для первого раунда
        const firstRoundMatches = [];
        for (let i = 0; i < participants.length; i += 2) {
            const matchId = this.generateMatchId();
            const match = {
                id: matchId,
                tournamentId: tournament.id,
                round: 1,
                position: Math.floor(i / 2) + 1,
                player1: participants[i],
                player2: participants[i + 1],
                status: 'pending'
            };
            firstRoundMatches.push(match);
            tournament.bracket.matches.set(matchId, match);
        }
        tournament.bracket.rounds.set(1, firstRoundMatches.map(m => m.id));
        // Создаем пустые матчи для следующих раундов
        for (let round = 2; round <= totalRounds; round++) {
            const roundMatches = [];
            const matchesInRound = Math.pow(2, totalRounds - round);
            for (let i = 0; i < matchesInRound; i++) {
                const matchId = this.generateMatchId();
                const match = {
                    id: matchId,
                    tournamentId: tournament.id,
                    round,
                    position: i + 1,
                    status: 'pending'
                };
                tournament.bracket.matches.set(matchId, match);
                roundMatches.push(matchId);
            }
            tournament.bracket.rounds.set(round, roundMatches);
        }
        // Связываем матчи
        this.linkMatches(tournament);
    }
    /**
     * Связывает матчи в турнирной сетке
     */
    linkMatches(tournament) {
        for (let round = 1; round < tournament.totalRounds; round++) {
            const currentRoundMatches = tournament.bracket.rounds.get(round) || [];
            const nextRoundMatches = tournament.bracket.rounds.get(round + 1) || [];
            for (let i = 0; i < currentRoundMatches.length; i += 2) {
                const match1 = tournament.bracket.matches.get(currentRoundMatches[i]);
                const match2 = tournament.bracket.matches.get(currentRoundMatches[i + 1]);
                const nextMatch = tournament.bracket.matches.get(nextRoundMatches[Math.floor(i / 2)]);
                if (match1 && nextMatch) {
                    match1.nextMatchId = nextMatch.id;
                }
                if (match2 && nextMatch) {
                    match2.nextMatchId = nextMatch.id;
                }
            }
        }
    }
    /**
     * Запускает раунд турнира
     */
    startRound(tournament, round) {
        const roundMatches = tournament.bracket.rounds.get(round) || [];
        for (const matchId of roundMatches) {
            const match = tournament.bracket.matches.get(matchId);
            if (match && match.player1 && match.player2) {
                match.status = 'ready';
                // Здесь можно автоматически создавать игры
                // this.createGameForMatch(tournament, match);
            }
        }
        tournament.status = 'in_progress';
    }
    /**
     * Обрабатывает результат матча
     */
    processMatchResult(tournamentId, matchId, winnerId) {
        const tournament = this.tournaments.get(tournamentId);
        if (!tournament) {
            throw new Error('Tournament not found');
        }
        const match = tournament.bracket.matches.get(matchId);
        if (!match) {
            throw new Error('Match not found');
        }
        if (match.status !== 'in_progress') {
            throw new Error('Match is not in progress');
        }
        // Определяем победителя и проигравшего
        let winner;
        let loser;
        if (match.player1?.playerId === winnerId) {
            winner = match.player1;
            loser = match.player2;
        }
        else if (match.player2?.playerId === winnerId) {
            winner = match.player2;
            loser = match.player1;
        }
        else {
            throw new Error('Invalid winner ID');
        }
        // Обновляем статистику
        winner.wins++;
        loser.losses++;
        match.winner = winner;
        match.status = 'finished';
        match.finishedAt = new Date();
        // Проигравший выбывает (для single elimination)
        if (tournament.type === 'single_elimination') {
            loser.status = 'eliminated';
        }
        // Продвигаем победителя в следующий раунд
        if (match.nextMatchId) {
            const nextMatch = tournament.bracket.matches.get(match.nextMatchId);
            if (nextMatch) {
                if (!nextMatch.player1) {
                    nextMatch.player1 = winner;
                }
                else if (!nextMatch.player2) {
                    nextMatch.player2 = winner;
                    nextMatch.status = 'ready';
                }
            }
        }
        else {
            // Это финальный матч
            winner.status = 'winner';
            this.finishTournament(tournament);
        }
        // Проверяем, завершен ли текущий раунд
        this.checkRoundCompletion(tournament);
    }
    /**
     * Проверяет завершение раунда
     */
    checkRoundCompletion(tournament) {
        const currentRoundMatches = tournament.bracket.rounds.get(tournament.currentRound) || [];
        const allFinished = currentRoundMatches.every(matchId => {
            const match = tournament.bracket.matches.get(matchId);
            return match?.status === 'finished';
        });
        if (allFinished && tournament.currentRound < tournament.totalRounds) {
            tournament.currentRound++;
            this.startRound(tournament, tournament.currentRound);
        }
    }
    /**
     * Завершает турнир
     */
    finishTournament(tournament) {
        tournament.status = 'finished';
        tournament.finishedAt = new Date();
        // Распределяем призы
        this.distributePrizes(tournament);
    }
    /**
     * Распределяет призы
     */
    distributePrizes(tournament) {
        const winner = tournament.participants.find(p => p.status === 'winner');
        if (winner) {
            // Добавляем рейтинговые очки победителю
            const winnerRating = this.ratingManager.getPlayerStats(winner.playerId);
            if (winnerRating) {
                // Здесь можно добавить специальную логику для турнирных призов
                console.log(`Tournament winner: ${winner.playerName}, prize: ${tournament.prizePool.first} rating points`);
            }
        }
    }
    // Утилиты
    generateTournamentId() {
        return 'tournament_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    generateMatchId() {
        return 'match_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    isPowerOfTwo(n) {
        return n > 0 && (n & (n - 1)) === 0;
    }
    calculateTotalRounds(type, participants) {
        switch (type) {
            case 'single_elimination':
                return Math.log2(participants);
            case 'double_elimination':
                return Math.log2(participants) * 2 - 1;
            case 'round_robin':
                return participants - 1;
            default:
                return 1;
        }
    }
    calculateEntryFee(participants) {
        return Math.max(50, participants * 10);
    }
    calculatePrizePool(participants) {
        const total = participants * this.calculateEntryFee(participants);
        return {
            first: Math.floor(total * 0.5),
            second: Math.floor(total * 0.3),
            third: Math.floor(total * 0.2)
        };
    }
    // Геттеры
    getTournament(id) {
        return this.tournaments.get(id);
    }
    getAllTournaments() {
        return Array.from(this.tournaments.values());
    }
    getActiveTournaments() {
        return Array.from(this.tournaments.values())
            .filter(t => t.status === 'registration' || t.status === 'in_progress');
    }
    getPlayerTournaments(playerId) {
        return Array.from(this.tournaments.values())
            .filter(t => t.participants.some(p => p.playerId === playerId));
    }
    // Заглушки для дополнительных методов
    generateDoubleEliminationBracket(tournament) {
        // TODO: Реализовать double elimination
        this.generateSingleEliminationBracket(tournament);
    }
    generateRoundRobinBracket(tournament) {
        // TODO: Реализовать round robin
        this.generateSingleEliminationBracket(tournament);
    }
}
exports.TournamentManager = TournamentManager;
//# sourceMappingURL=TournamentManager.js.map