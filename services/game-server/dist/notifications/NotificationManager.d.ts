export interface GameNotification {
    id: string;
    playerId: string;
    type: 'achievement' | 'level_up' | 'friend_request' | 'game_invitation' | 'tournament' | 'system' | 'daily_task' | 'title_unlocked';
    title: string;
    message: string;
    icon: string;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    category: 'game' | 'social' | 'progress' | 'system';
    data?: any;
    read: boolean;
    dismissed: boolean;
    createdAt: Date;
    expiresAt?: Date;
    actionRequired?: boolean;
    actions?: NotificationAction[];
}
export interface NotificationAction {
    id: string;
    label: string;
    type: 'primary' | 'secondary' | 'danger';
    action: string;
    data?: any;
}
export interface NotificationSettings {
    playerId: string;
    enabledTypes: string[];
    soundEnabled: boolean;
    desktopEnabled: boolean;
    emailEnabled: boolean;
    quietHours: {
        enabled: boolean;
        start: string;
        end: string;
    };
    categories: {
        game: boolean;
        social: boolean;
        progress: boolean;
        system: boolean;
    };
}
export declare class NotificationManager {
    private notifications;
    private settings;
    private globalNotifications;
    /**
     * Создает уведомление для игрока
     */
    createNotification(notification: Omit<GameNotification, 'id' | 'createdAt' | 'read' | 'dismissed'>): GameNotification;
    /**
     * Создает системное уведомление для всех игроков
     */
    createGlobalNotification(notification: Omit<GameNotification, 'id' | 'playerId' | 'createdAt' | 'read' | 'dismissed'>): GameNotification;
    /**
     * Получает уведомления игрока
     */
    getPlayerNotifications(playerId: string, options?: {
        unreadOnly?: boolean;
        category?: string;
        limit?: number;
        includeGlobal?: boolean;
    }): GameNotification[];
    /**
     * Помечает уведомление как прочитанное
     */
    markAsRead(playerId: string, notificationId: string): boolean;
    /**
     * Помечает уведомление как отклоненное
     */
    dismissNotification(playerId: string, notificationId: string): boolean;
    /**
     * Помечает все уведомления как прочитанные
     */
    markAllAsRead(playerId: string): number;
    /**
     * Получает настройки уведомлений игрока
     */
    getPlayerSettings(playerId: string): NotificationSettings;
    /**
     * Обновляет настройки уведомлений игрока
     */
    updatePlayerSettings(playerId: string, updates: Partial<NotificationSettings>): NotificationSettings;
    /**
     * Проверяет, нужно ли отправлять уведомление
     */
    private shouldSendNotification;
    /**
     * Проверяет, сейчас ли тихие часы
     */
    private isQuietHours;
    /**
     * Создает уведомление о достижении
     */
    createAchievementNotification(playerId: string, achievementName: string, achievementIcon: string): GameNotification;
    /**
     * Создает уведомление о повышении уровня
     */
    createLevelUpNotification(playerId: string, newLevel: number, newTitles?: string[]): GameNotification;
    /**
     * Создает уведомление о выполнении ежедневного задания
     */
    createDailyTaskNotification(playerId: string, taskName: string, experienceGained: number): GameNotification;
    /**
     * Создает уведомление о разблокировке титула
     */
    createTitleUnlockedNotification(playerId: string, titleName: string, titleIcon: string): GameNotification;
    /**
     * Получает статистику уведомлений
     */
    getNotificationStats(): {
        totalNotifications: number;
        unreadCount: number;
        totalPlayers: number;
        byType: Record<string, number>;
        byCategory: Record<string, number>;
        globalNotifications: number;
    };
    /**
     * Очищает старые уведомления
     */
    cleanup(): number;
    private generateNotificationId;
}
//# sourceMappingURL=NotificationManager.d.ts.map