{"version": 3, "file": "NotificationManager.d.ts", "sourceRoot": "", "sources": ["../../src/notifications/NotificationManager.ts"], "names": [], "mappings": "AAAA,MAAM,WAAW,gBAAgB;IAC/B,EAAE,EAAE,MAAM,CAAC;IACX,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,aAAa,GAAG,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,GAAG,YAAY,GAAG,QAAQ,GAAG,YAAY,GAAG,gBAAgB,CAAC;IACpI,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,QAAQ,CAAC;IAC/C,QAAQ,EAAE,MAAM,GAAG,QAAQ,GAAG,UAAU,GAAG,QAAQ,CAAC;IACpD,IAAI,CAAC,EAAE,GAAG,CAAC;IACX,IAAI,EAAE,OAAO,CAAC;IACd,SAAS,EAAE,OAAO,CAAC;IACnB,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,CAAC,EAAE,IAAI,CAAC;IACjB,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,OAAO,CAAC,EAAE,kBAAkB,EAAE,CAAC;CAChC;AAED,MAAM,WAAW,kBAAkB;IACjC,EAAE,EAAE,MAAM,CAAC;IACX,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,SAAS,GAAG,WAAW,GAAG,QAAQ,CAAC;IACzC,MAAM,EAAE,MAAM,CAAC;IACf,IAAI,CAAC,EAAE,GAAG,CAAC;CACZ;AAED,MAAM,WAAW,oBAAoB;IACnC,QAAQ,EAAE,MAAM,CAAC;IACjB,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,YAAY,EAAE,OAAO,CAAC;IACtB,cAAc,EAAE,OAAO,CAAC;IACxB,YAAY,EAAE,OAAO,CAAC;IACtB,UAAU,EAAE;QACV,OAAO,EAAE,OAAO,CAAC;QACjB,KAAK,EAAE,MAAM,CAAC;QACd,GAAG,EAAE,MAAM,CAAC;KACb,CAAC;IACF,UAAU,EAAE;QACV,IAAI,EAAE,OAAO,CAAC;QACd,MAAM,EAAE,OAAO,CAAC;QAChB,QAAQ,EAAE,OAAO,CAAC;QAClB,MAAM,EAAE,OAAO,CAAC;KACjB,CAAC;CACH;AAED,qBAAa,mBAAmB;IAC9B,OAAO,CAAC,aAAa,CAA8C;IACnE,OAAO,CAAC,QAAQ,CAAgD;IAChE,OAAO,CAAC,mBAAmB,CAA0B;IAErD;;OAEG;IACH,kBAAkB,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,GAAG,WAAW,GAAG,MAAM,GAAG,WAAW,CAAC,GAAG,gBAAgB;IA+BrH;;OAEG;IACH,wBAAwB,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,GAAG,UAAU,GAAG,WAAW,GAAG,MAAM,GAAG,WAAW,CAAC,GAAG,gBAAgB;IAoBxI;;OAEG;IACH,sBAAsB,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,GAAE;QAChD,UAAU,CAAC,EAAE,OAAO,CAAC;QACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,aAAa,CAAC,EAAE,OAAO,CAAC;KACpB,GAAG,gBAAgB,EAAE;IA6C3B;;OAEG;IACH,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,GAAG,OAAO;IAmB7D;;OAEG;IACH,mBAAmB,CAAC,QAAQ,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,GAAG,OAAO;IAWtE;;OAEG;IACH,aAAa,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM;IAsBvC;;OAEG;IACH,iBAAiB,CAAC,QAAQ,EAAE,MAAM,GAAG,oBAAoB;IAyBzD;;OAEG;IACH,oBAAoB,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;IAOpG;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAoB9B;;OAEG;IACH,OAAO,CAAC,YAAY;IAmBpB;;OAEG;IACH,6BAA6B,CAAC,QAAQ,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,GAAG,gBAAgB;IAanH;;OAEG;IACH,yBAAyB,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE,GAAG,gBAAgB;IAkBrG;;OAEG;IACH,2BAA2B,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,GAAG,gBAAgB;IAa3G;;OAEG;IACH,+BAA+B,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,gBAAgB;IAazG;;OAEG;IACH,oBAAoB;;;;;;;;IAqCpB;;OAEG;IACH,OAAO,IAAI,MAAM;IA+BjB,OAAO,CAAC,sBAAsB;CAG/B"}