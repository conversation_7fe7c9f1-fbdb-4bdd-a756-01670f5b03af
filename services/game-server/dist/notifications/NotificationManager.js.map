{"version": 3, "file": "NotificationManager.js", "sourceRoot": "", "sources": ["../../src/notifications/NotificationManager.ts"], "names": [], "mappings": ";;;AA6CA,MAAa,mBAAmB;IAAhC;QACU,kBAAa,GAAoC,IAAI,GAAG,EAAE,CAAC,CAAC,4BAA4B;QACxF,aAAQ,GAAsC,IAAI,GAAG,EAAE,CAAC,CAAC,uBAAuB;QAChF,wBAAmB,GAAuB,EAAE,CAAC,CAAC,iCAAiC;IAkZzF,CAAC;IAhZC;;OAEG;IACH,kBAAkB,CAAC,YAA+E;QAChG,MAAM,gBAAgB,GAAqB;YACzC,EAAE,EAAE,IAAI,CAAC,sBAAsB,EAAE;YACjC,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,IAAI,EAAE,KAAK;YACX,SAAS,EAAE,KAAK;YAChB,GAAG,YAAY;SAChB,CAAC;QAEF,6BAA6B;QAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,QAAQ,CAAC,EAAE,CAAC;YAC7D,OAAO,gBAAgB,CAAC,CAAC,4BAA4B;QACvD,CAAC;QAED,wCAAwC;QACxC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAE,CAAC;QAC3E,mBAAmB,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,qBAAqB;QAEpE,sCAAsC;QACtC,IAAI,mBAAmB,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACrC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAClC,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,YAA4F;QACnH,MAAM,kBAAkB,GAAqB;YAC3C,EAAE,EAAE,IAAI,CAAC,sBAAsB,EAAE;YACjC,QAAQ,EAAE,QAAQ;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,IAAI,EAAE,KAAK;YACX,SAAS,EAAE,KAAK;YAChB,GAAG,YAAY;SAChB,CAAC;QAEF,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAErD,iDAAiD;QACjD,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACzC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACtC,CAAC;QAED,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,QAAgB,EAAE,UAKrC,EAAE;QAEJ,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACnE,IAAI,gBAAgB,GAAG,CAAC,GAAG,mBAAmB,CAAC,CAAC;QAEhD,mCAAmC;QACnC,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,EAAE,CAAC;YACpC,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC,mBAAmB,EAAE,GAAG,gBAAgB,CAAC,CAAC;QACxE,CAAC;QAED,6BAA6B;QAC7B,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC;QAED,yBAAyB;QACzB,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrB,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnF,CAAC;QAED,qBAAqB;QACrB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;QAEnF,oCAAoC;QACpC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC7B,MAAM,aAAa,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;YAChE,MAAM,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YAC5C,MAAM,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YAE5C,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC5B,OAAO,SAAS,GAAG,SAAS,CAAC;YAC/B,CAAC;YAED,OAAO,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,0BAA0B;QAC1B,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAClB,gBAAgB,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,QAAgB,EAAE,cAAsB;QACjD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACvD,IAAI,CAAC,aAAa;YAAE,OAAO,KAAK,CAAC;QAEjC,MAAM,YAAY,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,cAAc,CAAC,CAAC;QACtE,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,mCAAmC;YACnC,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,cAAc,CAAC,CAAC;YACvF,IAAI,kBAAkB,EAAE,CAAC;gBACvB,kBAAkB,CAAC,IAAI,GAAG,IAAI,CAAC;gBAC/B,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,QAAgB,EAAE,cAAsB;QAC1D,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACvD,IAAI,CAAC,aAAa;YAAE,OAAO,KAAK,CAAC;QAEjC,MAAM,YAAY,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,cAAc,CAAC,CAAC;QACtE,IAAI,CAAC,YAAY;YAAE,OAAO,KAAK,CAAC;QAEhC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,QAAgB;QAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC7D,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YACnC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;gBACvB,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;gBACzB,KAAK,EAAE,CAAC;YACV,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,6DAA6D;QAC7D,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YAC9C,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;gBACvB,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;gBACzB,KAAK,EAAE,CAAC;YACV,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,QAAgB;QAChC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjC,MAAM,eAAe,GAAyB;gBAC5C,QAAQ;gBACR,YAAY,EAAE,CAAC,aAAa,EAAE,UAAU,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,YAAY,EAAE,YAAY,EAAE,gBAAgB,CAAC;gBAC5H,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,IAAI;gBACpB,YAAY,EAAE,KAAK;gBACnB,UAAU,EAAE;oBACV,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,OAAO;oBACd,GAAG,EAAE,OAAO;iBACb;gBACD,UAAU,EAAE;oBACV,IAAI,EAAE,IAAI;oBACV,MAAM,EAAE,IAAI;oBACZ,QAAQ,EAAE,IAAI;oBACd,MAAM,EAAE,IAAI;iBACb;aACF,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;QAC/C,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,QAAgB,EAAE,OAAsC;QAC3E,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QACzD,MAAM,WAAW,GAAG,EAAE,GAAG,eAAe,EAAE,GAAG,OAAO,EAAE,CAAC;QACvD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QACzC,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,YAA8B,EAAE,QAA8B;QAC3F,wCAAwC;QACxC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,mCAAmC;QACnC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,uBAAuB;QACvB,IAAI,QAAQ,CAAC,UAAU,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1E,qDAAqD;YACrD,OAAO,YAAY,CAAC,QAAQ,KAAK,QAAQ,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,UAA8C;QACjE,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC;QAE3D,MAAM,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACtE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEhE,MAAM,SAAS,GAAG,SAAS,GAAG,EAAE,GAAG,QAAQ,CAAC;QAC5C,MAAM,OAAO,GAAG,OAAO,GAAG,EAAE,GAAG,MAAM,CAAC;QAEtC,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;YACzB,0DAA0D;YAC1D,OAAO,WAAW,IAAI,SAAS,IAAI,WAAW,IAAI,OAAO,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,kDAAkD;YAClD,OAAO,WAAW,IAAI,SAAS,IAAI,WAAW,IAAI,OAAO,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,6BAA6B,CAAC,QAAgB,EAAE,eAAuB,EAAE,eAAuB;QAC9F,OAAO,IAAI,CAAC,kBAAkB,CAAC;YAC7B,QAAQ;YACR,IAAI,EAAE,aAAa;YACnB,KAAK,EAAE,4BAA4B;YACnC,OAAO,EAAE,2BAA2B,eAAe,GAAG;YACtD,IAAI,EAAE,eAAe;YACrB,QAAQ,EAAE,MAAM;YAChB,QAAQ,EAAE,UAAU;YACpB,IAAI,EAAE,EAAE,eAAe,EAAE;SAC1B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,yBAAyB,CAAC,QAAgB,EAAE,QAAgB,EAAE,SAAoB;QAChF,IAAI,OAAO,GAAG,4BAA4B,QAAQ,UAAU,CAAC;QAC7D,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtC,OAAO,IAAI,iCAAiC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACrE,CAAC;QAED,OAAO,IAAI,CAAC,kBAAkB,CAAC;YAC7B,QAAQ;YACR,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,mBAAmB;YAC1B,OAAO;YACP,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,MAAM;YAChB,QAAQ,EAAE,UAAU;YACpB,IAAI,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE;SAC9B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,2BAA2B,CAAC,QAAgB,EAAE,QAAgB,EAAE,gBAAwB;QACtF,OAAO,IAAI,CAAC,kBAAkB,CAAC;YAC7B,QAAQ;YACR,IAAI,EAAE,YAAY;YAClB,KAAK,EAAE,oBAAoB;YAC3B,OAAO,EAAE,eAAe,QAAQ,eAAe,gBAAgB,SAAS;YACxE,IAAI,EAAE,GAAG;YACT,QAAQ,EAAE,QAAQ;YAClB,QAAQ,EAAE,UAAU;YACpB,IAAI,EAAE,EAAE,QAAQ,EAAE,gBAAgB,EAAE;SACrC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,+BAA+B,CAAC,QAAgB,EAAE,SAAiB,EAAE,SAAiB;QACpF,OAAO,IAAI,CAAC,kBAAkB,CAAC;YAC7B,QAAQ;YACR,IAAI,EAAE,gBAAgB;YACtB,KAAK,EAAE,cAAc;YACrB,OAAO,EAAE,wBAAwB,SAAS,GAAG;YAC7C,IAAI,EAAE,SAAS;YACf,QAAQ,EAAE,MAAM;YAChB,QAAQ,EAAE,UAAU;YACpB,IAAI,EAAE,EAAE,SAAS,EAAE;SACpB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,IAAI,kBAAkB,GAAG,CAAC,CAAC;QAC3B,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,MAAM,GAA2B,EAAE,CAAC;QACxC,IAAI,UAAU,GAA2B,EAAE,CAAC;QAE5C,mCAAmC;QACnC,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC;YACxD,kBAAkB,IAAI,aAAa,CAAC,MAAM,CAAC;YAE3C,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;gBACnC,IAAI,CAAC,YAAY,CAAC,IAAI;oBAAE,WAAW,EAAE,CAAC;gBAEtC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;gBACjE,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACnF,CAAC,CAAC,CAAC;QACL,CAAC;QAED,mCAAmC;QACnC,kBAAkB,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;QACtD,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YAC9C,IAAI,CAAC,YAAY,CAAC,IAAI;gBAAE,WAAW,EAAE,CAAC;YAEtC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACjE,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,kBAAkB;YAClB,WAAW;YACX,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;YACrC,MAAM;YACN,UAAU;YACV,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM;SACrD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,MAAM,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,UAAU;QACnD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,8BAA8B;QAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,aAAa,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YACrE,MAAM,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC;YAE5C,2CAA2C;YAC3C,MAAM,QAAQ,GAAG,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE;gBACnD,MAAM,GAAG,GAAG,GAAG,GAAG,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;gBACnD,OAAO,GAAG,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;YACjD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAC3C,OAAO,IAAI,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC9C,CAAC;QAED,iCAAiC;QACjC,MAAM,oBAAoB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;QAC7D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE;YACxE,MAAM,GAAG,GAAG,GAAG,GAAG,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACnD,OAAO,GAAG,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;QACjD,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,oBAAoB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;QAElE,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,UAAU;IACF,sBAAsB;QAC5B,OAAO,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/E,CAAC;CACF;AArZD,kDAqZC"}