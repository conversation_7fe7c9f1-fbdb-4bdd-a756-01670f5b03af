{"version": 3, "file": "ProgressManager.d.ts", "sourceRoot": "", "sources": ["../../src/progress/ProgressManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAGvD,MAAM,WAAW,cAAc;IAC7B,QAAQ,EAAE,MAAM,CAAC;IACjB,KAAK,EAAE,MAAM,CAAC;IACd,UAAU,EAAE,MAAM,CAAC;IACnB,qBAAqB,EAAE,MAAM,CAAC;IAC9B,eAAe,EAAE,MAAM,CAAC;IACxB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,mBAAmB,EAAE,MAAM,CAAC;IAC5B,oBAAoB,EAAE,MAAM,CAAC;IAC7B,cAAc,EAAE,IAAI,CAAC;IACrB,eAAe,EAAE,IAAI,CAAC;IACtB,UAAU,EAAE,MAAM,CAAC;IACnB,YAAY,EAAE,IAAI,CAAC;IACnB,aAAa,EAAE;QACb,WAAW,EAAE,MAAM,CAAC;QACpB,QAAQ,EAAE,MAAM,CAAC;QACjB,cAAc,EAAE,MAAM,CAAC;QACvB,YAAY,EAAE,MAAM,CAAC;QACrB,oBAAoB,EAAE,MAAM,CAAC;QAC7B,cAAc,EAAE,MAAM,CAAC;KACxB,CAAC;CACH;AAED,MAAM,WAAW,SAAS;IACxB,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE;QACX,IAAI,EAAE,YAAY,GAAG,WAAW,GAAG,mBAAmB,GAAG,gBAAgB,GAAG,sBAAsB,GAAG,cAAc,CAAC;QACpH,KAAK,EAAE,MAAM,CAAC;KACf,CAAC;IACF,gBAAgB,EAAE,MAAM,CAAC;IACzB,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,OAAO,CAAC;IACnB,IAAI,EAAE,MAAM,CAAC;CACd;AAED,MAAM,WAAW,eAAe;IAC9B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE;QACX,IAAI,EAAE,YAAY,GAAG,aAAa,GAAG,0BAA0B,GAAG,iBAAiB,GAAG,eAAe,CAAC;QACtG,KAAK,EAAE,MAAM,CAAC;KACf,CAAC;IACF,gBAAgB,EAAE,MAAM,CAAC;IACzB,aAAa,CAAC,EAAE;QACd,IAAI,EAAE,OAAO,GAAG,OAAO,GAAG,QAAQ,CAAC;QACnC,KAAK,EAAE,MAAM,GAAG,MAAM,CAAC;KACxB,CAAC;IACF,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,OAAO,CAAC;IACnB,SAAS,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,WAAW;IAC1B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG,WAAW,GAAG,QAAQ,CAAC;IAC5D,WAAW,EAAE,MAAM,CAAC;IACpB,KAAK,EAAE,MAAM,CAAC;IACd,eAAe,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,MAAM,EAAE,YAAY,KAAK,OAAO,CAAC;CAC9E;AAED,MAAM,WAAW,cAAc;IAC7B,MAAM,EAAE,UAAU,GAAG,aAAa,GAAG,YAAY,GAAG,kBAAkB,GAAG,aAAa,GAAG,YAAY,GAAG,QAAQ,CAAC;IACjH,MAAM,EAAE,MAAM,CAAC;IACf,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,EAAE,IAAI,CAAC;CACjB;AAED,qBAAa,eAAe;IAC1B,OAAO,CAAC,cAAc,CAA0C;IAChE,OAAO,CAAC,UAAU,CAAuC;IACzD,OAAO,CAAC,gBAAgB,CAA6C;IACrE,OAAO,CAAC,eAAe,CAAuC;IAC9D,OAAO,CAAC,iBAAiB,CAA4C;;IAMrE;;OAEG;IACH,OAAO,CAAC,gBAAgB;IA0HxB;;OAEG;IACH,iBAAiB,CAAC,QAAQ,EAAE,MAAM,GAAG,cAAc;IA8BnD;;OAEG;IACH,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,GAAG;QACxE,SAAS,EAAE,OAAO,CAAC;QACnB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,SAAS,CAAC,EAAE,WAAW,EAAE,CAAA;KAC1B;IA4CD;;OAEG;IACH,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,GAAG,IAAI;IAsB1F;;OAEG;IACH,OAAO,CAAC,cAAc;IAiBtB;;OAEG;IACH,kBAAkB,CAAC,QAAQ,EAAE,MAAM,GAAG,SAAS,EAAE;IAqFjD;;OAEG;IACH,uBAAuB,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,GAAE,MAAU,GAAG,SAAS,EAAE;IA4BtH;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAM7B;;OAEG;IACH,OAAO,CAAC,YAAY;IAOpB;;OAEG;IACH,oBAAoB,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,GAAE,MAAW,GAAG,cAAc,EAAE;IAK5E;;OAEG;IACH,eAAe,CAAC,QAAQ,EAAE,MAAM,GAAG;QAAE,OAAO,CAAC,EAAE,WAAW,CAAC;QAAC,SAAS,EAAE,WAAW,EAAE,CAAA;KAAE;IActF;;OAEG;IACH,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,OAAO;IAW1D;;OAEG;IACH,gBAAgB;;;;;;;CAoBjB"}