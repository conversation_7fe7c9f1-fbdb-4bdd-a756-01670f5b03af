import { PlayerRating } from '../rating/RatingManager';
export interface PlayerProgress {
    playerId: string;
    level: number;
    experience: number;
    experienceToNextLevel: number;
    totalExperience: number;
    currentTitle?: string;
    availableTitles: string[];
    dailyTasksCompleted: number;
    weeklyTasksCompleted: number;
    lastDailyReset: Date;
    lastWeeklyReset: Date;
    playStreak: number;
    lastPlayDate: Date;
    lifetimeStats: {
        gamesPlayed: number;
        gamesWon: number;
        tournamentsWon: number;
        friendsAdded: number;
        achievementsUnlocked: number;
        spectatedGames: number;
    };
}
export interface DailyTask {
    id: string;
    name: string;
    description: string;
    icon: string;
    requirement: {
        type: 'play_games' | 'win_games' | 'play_with_friends' | 'spectate_games' | 'send_friend_requests' | 'login_streak';
        value: number;
    };
    experienceReward: number;
    progress: number;
    completed: boolean;
    date: string;
}
export interface WeeklyChallenge {
    id: string;
    name: string;
    description: string;
    icon: string;
    requirement: {
        type: 'win_streak' | 'rating_gain' | 'tournament_participation' | 'social_activity' | 'perfect_games';
        value: number;
    };
    experienceReward: number;
    specialReward?: {
        type: 'title' | 'badge' | 'points';
        value: string | number;
    };
    progress: number;
    completed: boolean;
    weekStart: string;
}
export interface PlayerTitle {
    id: string;
    name: string;
    description: string;
    icon: string;
    rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
    requirement: string;
    color: string;
    unlockCondition: (progress: PlayerProgress, rating: PlayerRating) => boolean;
}
export interface ExperienceGain {
    source: 'game_win' | 'game_played' | 'daily_task' | 'weekly_challenge' | 'achievement' | 'tournament' | 'social';
    amount: number;
    description: string;
    timestamp: Date;
}
export declare class ProgressManager {
    private playerProgress;
    private dailyTasks;
    private weeklyChallenges;
    private availableTitles;
    private experienceHistory;
    constructor();
    /**
     * Инициализирует доступные титулы
     */
    private initializeTitles;
    /**
     * Получает или создает прогресс игрока
     */
    getPlayerProgress(playerId: string): PlayerProgress;
    /**
     * Добавляет опыт игроку
     */
    addExperience(playerId: string, gain: Omit<ExperienceGain, 'timestamp'>): {
        leveledUp: boolean;
        newLevel?: number;
        newTitles?: PlayerTitle[];
    };
    /**
     * Обновляет статистику игрока
     */
    updatePlayerStats(playerId: string, stats: Partial<PlayerProgress['lifetimeStats']>): void;
    /**
     * Проверяет новые доступные титулы
     */
    private checkNewTitles;
    /**
     * Генерирует ежедневные задания
     */
    generateDailyTasks(playerId: string): DailyTask[];
    /**
     * Обновляет прогресс ежедневного задания
     */
    updateDailyTaskProgress(playerId: string, taskType: DailyTask['requirement']['type'], amount?: number): DailyTask[];
    /**
     * Вычисляет опыт, необходимый для достижения уровня
     */
    private getExperienceForLevel;
    /**
     * Получает начало недели
     */
    private getWeekStart;
    /**
     * Получает историю опыта игрока
     */
    getExperienceHistory(playerId: string, limit?: number): ExperienceGain[];
    /**
     * Получает доступные титулы игрока
     */
    getPlayerTitles(playerId: string): {
        current?: PlayerTitle;
        available: PlayerTitle[];
    };
    /**
     * Устанавливает активный титул игрока
     */
    setPlayerTitle(playerId: string, titleId: string): boolean;
    /**
     * Получает статистику прогресса
     */
    getProgressStats(): {
        totalPlayers: number;
        totalExperience: number;
        maxLevel: number;
        totalTasksCompleted: number;
        averageLevel: number;
    };
}
//# sourceMappingURL=ProgressManager.d.ts.map