import { Room } from '../rooms/RoomManager';
import { Player } from '../players/PlayerManager';
import { DurakGame, GameStatus, GameEventData } from '@kozyr-master/core';
interface GameState {
    gameStatus: GameStatus;
    players: any[];
    currentPlayerIndex: number;
    attackerIndex: number;
    defenderIndex: number;
    tableCards: any[];
    deck: any[];
    trumpCard: any;
    trumpSuit: string;
    winner?: any;
}
interface GameMove {
    playerId: string;
    action: string;
    cardIndex?: number;
    timestamp: Date;
}
export interface MultiplayerGame {
    id: string;
    roomId: string;
    players: Player[];
    durakGame: DurakGame;
    gameState: GameState;
    moves: GameMove[];
    startedAt: Date;
    finishedAt?: Date;
    winner?: Player;
    status: 'waiting' | 'playing' | 'finished';
    eventHandlers: Map<string, (eventData: GameEventData) => void>;
}
export declare class GameManager {
    private games;
    private roomGames;
    /**
     * Запускает новую игру в комнате
     */
    startGame(room: Room): MultiplayerGame;
    /**
     * Делает ход в игре
     */
    makeMove(gameId: string, playerId: string, action: string, cardIndex?: number): boolean;
    /**
     * Получает игру по ID
     */
    getGame(gameId: string): MultiplayerGame | undefined;
    /**
     * Получает игру по комнате
     */
    getGameByRoom(roomId: string): MultiplayerGame | undefined;
    /**
     * Завершает игру
     */
    finishGame(gameId: string, winner?: Player): boolean;
    /**
     * Получает все игры
     */
    getAllGames(): MultiplayerGame[];
    /**
     * Получает активные игры
     */
    getActiveGames(): MultiplayerGame[];
    /**
     * Получает статистику
     */
    getStats(): {
        total: number;
        active: number;
        finished: number;
        averageGameDuration: number;
    };
    /**
     * Настраивает обработчики событий игры
     */
    private setupGameEventHandlers;
    /**
     * Конвертирует состояние DurakGame в GameState
     */
    private convertDurakStateToGameState;
    /**
     * Вычисляет среднюю продолжительность игры
     */
    private calculateAverageGameDuration;
    /**
     * Очищает старые завершенные игры
     */
    cleanupFinishedGames(maxAge?: number): number;
}
export declare function getGamePublicState(game: MultiplayerGame): any;
export {};
//# sourceMappingURL=GameManager.d.ts.map