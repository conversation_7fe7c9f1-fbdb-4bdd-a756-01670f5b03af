"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameManager = void 0;
exports.getGamePublicState = getGamePublicState;
const uuid_1 = require("uuid");
const RoomManager_1 = require("../rooms/RoomManager");
class GameManager {
    constructor() {
        this.games = new Map(); // gameId -> Game
        this.roomGames = new Map(); // roomId -> gameId
    }
    /**
     * Запускает новую игру в комнате
     */
    startGame(room) {
        // Проверяем, что в комнате достаточно игроков
        if (room.players.length < 2) {
            throw new Error('Not enough players to start the game');
        }
        // Проверяем, что игра еще не запущена
        if (this.roomGames.has(room.id)) {
            throw new Error('Game is already running in this room');
        }
        // Создаем новую игру
        const game = {
            id: (0, uuid_1.v4)(),
            roomId: room.id,
            players: [...room.players],
            gameState: this.createInitialGameState(room.players),
            moves: [],
            startedAt: new Date(),
            status: 'playing'
        };
        this.games.set(game.id, game);
        this.roomGames.set(room.id, game.id);
        // Обновляем статус комнаты
        room.status = RoomManager_1.RoomStatus.PLAYING;
        room.gameId = game.id;
        return game;
    }
    /**
     * Делает ход в игре
     */
    makeMove(gameId, playerId, action, cardIndex) {
        const game = this.games.get(gameId);
        if (!game)
            return false;
        // Проверяем, что игра активна
        if (game.status !== 'playing')
            return false;
        // Проверяем, что игрок участвует в игре
        const player = game.players.find(p => p.id === playerId);
        if (!player)
            return false;
        // Создаем ход
        const move = {
            playerId,
            action,
            cardIndex,
            timestamp: new Date()
        };
        // Добавляем ход в историю
        game.moves.push(move);
        // Обновляем игровое состояние (заглушка)
        this.updateGameState(game, move);
        return true;
    }
    /**
     * Получает игру по ID
     */
    getGame(gameId) {
        return this.games.get(gameId);
    }
    /**
     * Получает игру по комнате
     */
    getGameByRoom(roomId) {
        const gameId = this.roomGames.get(roomId);
        return gameId ? this.games.get(gameId) : undefined;
    }
    /**
     * Завершает игру
     */
    finishGame(gameId, winner) {
        const game = this.games.get(gameId);
        if (!game)
            return false;
        game.status = 'finished';
        game.finishedAt = new Date();
        game.winner = winner;
        return true;
    }
    /**
     * Получает все игры
     */
    getAllGames() {
        return Array.from(this.games.values());
    }
    /**
     * Получает активные игры
     */
    getActiveGames() {
        return this.getAllGames().filter(game => game.status === 'playing');
    }
    /**
     * Получает статистику
     */
    getStats() {
        const games = this.getAllGames();
        const activeGames = games.filter(g => g.status === 'playing');
        const finishedGames = games.filter(g => g.status === 'finished');
        return {
            total: games.length,
            active: activeGames.length,
            finished: finishedGames.length,
            averageGameDuration: this.calculateAverageGameDuration(finishedGames)
        };
    }
    /**
     * Создает начальное состояние игры
     */
    createInitialGameState(players) {
        // Заглушка для начального состояния
        return {
            gameStatus: 'playing',
            players: players.map((player, index) => ({
                id: player.id,
                name: player.name,
                hand: this.generateInitialHand(),
                isActive: index === 0
            })),
            currentPlayerIndex: 0,
            tableCards: [],
            deck: this.generateDeck(),
            trumpCard: { rank: '6', suit: '♠' },
            trumpSuit: '♠'
        };
    }
    /**
     * Обновляет состояние игры после хода
     */
    updateGameState(game, move) {
        // Заглушка для обновления состояния
        const { gameState } = game;
        switch (move.action) {
            case 'attack':
                if (move.cardIndex !== undefined) {
                    const player = gameState.players.find(p => p.id === move.playerId);
                    if (player && player.hand[move.cardIndex]) {
                        const card = player.hand.splice(move.cardIndex, 1)[0];
                        gameState.tableCards.push([card]);
                    }
                }
                break;
            case 'defend':
                // Логика защиты
                break;
            case 'take':
                // Логика взятия карт
                break;
            case 'pass':
                // Логика паса
                break;
        }
        // Переключаем ход
        gameState.currentPlayerIndex = (gameState.currentPlayerIndex + 1) % gameState.players.length;
        // Проверяем условия окончания игры
        this.checkGameEnd(game);
    }
    /**
     * Проверяет условия окончания игры
     */
    checkGameEnd(game) {
        const { gameState } = game;
        // Проверяем, есть ли игрок без карт
        const playersWithoutCards = gameState.players.filter(p => p.hand.length === 0);
        if (playersWithoutCards.length > 0) {
            // Игра окончена
            game.status = 'finished';
            game.finishedAt = new Date();
            game.winner = game.players.find(p => p.id === playersWithoutCards[0].id);
            gameState.gameStatus = 'finished';
        }
    }
    /**
     * Генерирует начальную руку
     */
    generateInitialHand() {
        const suits = ['♠', '♥', '♦', '♣'];
        const ranks = ['6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
        const hand = [];
        for (let i = 0; i < 6; i++) {
            hand.push({
                rank: ranks[Math.floor(Math.random() * ranks.length)],
                suit: suits[Math.floor(Math.random() * suits.length)]
            });
        }
        return hand;
    }
    /**
     * Генерирует колоду
     */
    generateDeck() {
        const suits = ['♠', '♥', '♦', '♣'];
        const ranks = ['6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
        const deck = [];
        for (const suit of suits) {
            for (const rank of ranks) {
                deck.push({ rank, suit });
            }
        }
        return deck.slice(12); // Убираем розданные карты
    }
    /**
     * Вычисляет среднюю продолжительность игры
     */
    calculateAverageGameDuration(games) {
        if (games.length === 0)
            return 0;
        const totalDuration = games.reduce((sum, game) => {
            if (game.finishedAt) {
                return sum + (game.finishedAt.getTime() - game.startedAt.getTime());
            }
            return sum;
        }, 0);
        return Math.round(totalDuration / games.length / 1000); // в секундах
    }
    /**
     * Очищает старые завершенные игры
     */
    cleanupFinishedGames(maxAge = 24 * 60 * 60 * 1000) {
        const now = new Date();
        let cleaned = 0;
        for (const [gameId, game] of this.games.entries()) {
            if (game.status === 'finished' && game.finishedAt) {
                const age = now.getTime() - game.finishedAt.getTime();
                if (age > maxAge) {
                    this.games.delete(gameId);
                    this.roomGames.delete(game.roomId);
                    cleaned++;
                }
            }
        }
        return cleaned;
    }
}
exports.GameManager = GameManager;
// Функция для получения публичного состояния игры
function getGamePublicState(game) {
    return {
        id: game.id,
        roomId: game.roomId,
        players: game.players.map(p => ({
            id: p.id,
            name: p.name
        })),
        gameState: game.gameState,
        status: game.status,
        startedAt: game.startedAt,
        winner: game.winner ? {
            id: game.winner.id,
            name: game.winner.name
        } : undefined
    };
}
//# sourceMappingURL=GameManager.js.map