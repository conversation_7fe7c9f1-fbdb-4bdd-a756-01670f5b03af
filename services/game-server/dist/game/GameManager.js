"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameManager = void 0;
exports.getGamePublicState = getGamePublicState;
const uuid_1 = require("uuid");
const RoomManager_1 = require("../rooms/RoomManager");
// Импортируем настоящее игровое ядро
const core_1 = require("@kozyr-master/core");
class GameManager {
    constructor() {
        this.games = new Map(); // gameId -> Game
        this.roomGames = new Map(); // roomId -> gameId
    }
    /**
     * Запускает новую игру в комнате
     */
    startGame(room) {
        // Проверяем, что в комнате достаточно игроков
        if (room.players.length < 2) {
            throw new Error('Not enough players to start the game');
        }
        // Проверяем, что игра еще не запущена
        if (this.roomGames.has(room.id)) {
            throw new Error('Game is already running in this room');
        }
        // Создаем игроков для DurakGame
        const durakPlayers = room.players.map(player => ({
            id: player.id,
            name: player.name,
            hand: [],
            isActive: false
        }));
        // Настройки игры
        const rules = {
            variant: core_1.DurakVariant.CLASSIC,
            numberOfPlayers: room.players.length,
            initialHandSize: 6,
            attackLimit: 6,
        };
        // Создаем настоящую игру
        const durakGame = new core_1.DurakGame(durakPlayers, rules);
        // Создаем мультиплеерную обертку
        const game = {
            id: (0, uuid_1.v4)(),
            roomId: room.id,
            players: [...room.players],
            durakGame,
            gameState: this.convertDurakStateToGameState(durakGame.getState()),
            moves: [],
            startedAt: new Date(),
            status: 'playing',
            eventHandlers: new Map()
        };
        // Настраиваем обработчики событий
        this.setupGameEventHandlers(game);
        // Запускаем игру
        durakGame.startGame();
        this.games.set(game.id, game);
        this.roomGames.set(room.id, game.id);
        // Обновляем статус комнаты
        room.status = RoomManager_1.RoomStatus.PLAYING;
        room.gameId = game.id;
        return game;
    }
    /**
     * Делает ход в игре
     */
    makeMove(gameId, playerId, action, cardIndex) {
        const game = this.games.get(gameId);
        if (!game)
            return false;
        // Проверяем, что игра активна
        if (game.status !== 'playing')
            return false;
        // Проверяем, что игрок участвует в игре
        const player = game.players.find(p => p.id === playerId);
        if (!player)
            return false;
        // Преобразуем строковое действие в PlayerAction
        let playerAction;
        switch (action.toLowerCase()) {
            case 'attack':
                playerAction = core_1.PlayerAction.ATTACK;
                break;
            case 'defend':
                playerAction = core_1.PlayerAction.DEFEND;
                break;
            case 'take':
                playerAction = core_1.PlayerAction.TAKE;
                break;
            case 'pass':
                playerAction = core_1.PlayerAction.PASS;
                break;
            default:
                return false; // Неизвестное действие
        }
        // Делаем ход в настоящей игре
        const success = game.durakGame.makeMove(playerId, playerAction, cardIndex);
        if (success) {
            // Создаем запись хода
            const move = {
                playerId,
                action,
                cardIndex,
                timestamp: new Date()
            };
            // Добавляем ход в историю
            game.moves.push(move);
            // Обновляем состояние
            game.gameState = this.convertDurakStateToGameState(game.durakGame.getState());
        }
        return success;
    }
    /**
     * Получает игру по ID
     */
    getGame(gameId) {
        return this.games.get(gameId);
    }
    /**
     * Получает игру по комнате
     */
    getGameByRoom(roomId) {
        const gameId = this.roomGames.get(roomId);
        return gameId ? this.games.get(gameId) : undefined;
    }
    /**
     * Завершает игру
     */
    finishGame(gameId, winner) {
        const game = this.games.get(gameId);
        if (!game)
            return false;
        game.status = 'finished';
        game.finishedAt = new Date();
        game.winner = winner;
        return true;
    }
    /**
     * Получает все игры
     */
    getAllGames() {
        return Array.from(this.games.values());
    }
    /**
     * Получает активные игры
     */
    getActiveGames() {
        return this.getAllGames().filter(game => game.status === 'playing');
    }
    /**
     * Получает статистику
     */
    getStats() {
        const games = this.getAllGames();
        const activeGames = games.filter(g => g.status === 'playing');
        const finishedGames = games.filter(g => g.status === 'finished');
        return {
            total: games.length,
            active: activeGames.length,
            finished: finishedGames.length,
            averageGameDuration: this.calculateAverageGameDuration(finishedGames)
        };
    }
    /**
     * Настраивает обработчики событий игры
     */
    setupGameEventHandlers(game) {
        const eventHandler = (eventData) => {
            // Обновляем состояние игры
            game.gameState = this.convertDurakStateToGameState(eventData.gameState);
            // Обрабатываем разные типы событий
            switch (eventData.type) {
                case core_1.GameEvent.GAME_ENDED:
                    game.status = 'finished';
                    game.finishedAt = new Date();
                    if (eventData.gameState.winner && eventData.gameState.winner.id) {
                        game.winner = game.players.find(p => p.id === eventData.gameState.winner.id);
                    }
                    break;
            }
        };
        game.durakGame.addEventListener(eventHandler);
        game.eventHandlers.set('main', eventHandler);
    }
    /**
     * Конвертирует состояние DurakGame в GameState
     */
    convertDurakStateToGameState(durakState) {
        return {
            gameStatus: durakState.gameStatus,
            players: durakState.players,
            currentPlayerIndex: durakState.currentPlayerIndex,
            attackerIndex: durakState.attackerIndex,
            defenderIndex: durakState.defenderIndex,
            tableCards: durakState.tableCards,
            deck: durakState.deck,
            trumpCard: durakState.trumpCard,
            trumpSuit: durakState.trumpSuit,
            winner: durakState.winner
        };
    }
    /**
     * Вычисляет среднюю продолжительность игры
     */
    calculateAverageGameDuration(games) {
        if (games.length === 0)
            return 0;
        const totalDuration = games.reduce((sum, game) => {
            if (game.finishedAt) {
                return sum + (game.finishedAt.getTime() - game.startedAt.getTime());
            }
            return sum;
        }, 0);
        return Math.round(totalDuration / games.length / 1000); // в секундах
    }
    /**
     * Очищает старые завершенные игры
     */
    cleanupFinishedGames(maxAge = 24 * 60 * 60 * 1000) {
        const now = new Date();
        let cleaned = 0;
        for (const [gameId, game] of this.games.entries()) {
            if (game.status === 'finished' && game.finishedAt) {
                const age = now.getTime() - game.finishedAt.getTime();
                if (age > maxAge) {
                    this.games.delete(gameId);
                    this.roomGames.delete(game.roomId);
                    cleaned++;
                }
            }
        }
        return cleaned;
    }
}
exports.GameManager = GameManager;
// Функция для получения публичного состояния игры
function getGamePublicState(game) {
    // Создаем безопасную копию состояния игры для отправки клиентам
    const publicGameState = {
        ...game.gameState,
        players: game.gameState.players.map((player, index) => {
            // Для каждого игрока показываем только его собственные карты
            return {
                id: player.id,
                name: player.name,
                handSize: player.hand.length, // Количество карт вместо самих карт
                hand: player.hand, // Карты будут фильтроваться на клиенте
                isActive: player.isActive
            };
        })
    };
    return {
        id: game.id,
        roomId: game.roomId,
        players: game.players.map(p => ({
            id: p.id,
            name: p.name
        })),
        gameState: publicGameState,
        status: game.status,
        startedAt: game.startedAt,
        winner: game.winner ? {
            id: game.winner.id,
            name: game.winner.name
        } : undefined,
        moveCount: game.moves.length
    };
}
//# sourceMappingURL=GameManager.js.map