{"version": 3, "file": "GameManager.js", "sourceRoot": "", "sources": ["../../src/game/GameManager.ts"], "names": [], "mappings": ";;;AA0TA,gDAgCC;AA1VD,+BAAoC;AACpC,sDAAwD;AAGxD,qCAAqC;AACrC,6CAS4B;AAqC5B,MAAa,WAAW;IAAxB;QACU,UAAK,GAAiC,IAAI,GAAG,EAAE,CAAC,CAAC,iBAAiB;QAClE,cAAS,GAAwB,IAAI,GAAG,EAAE,CAAC,CAAC,mBAAmB;IAkQzE,CAAC;IAhQC;;OAEG;IACH,SAAS,CAAC,IAAU;QAClB,8CAA8C;QAC9C,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,sCAAsC;QACtC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,gCAAgC;QAChC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC/C,EAAE,EAAE,MAAM,CAAC,EAAE;YACb,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,IAAI,EAAE,EAAE;YACR,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC,CAAC;QAEJ,iBAAiB;QACjB,MAAM,KAAK,GAAG;YACZ,OAAO,EAAE,mBAAY,CAAC,OAAO;YAC7B,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;YACpC,eAAe,EAAE,CAAC;YAClB,WAAW,EAAE,CAAC;SACf,CAAC;QAEF,yBAAyB;QACzB,MAAM,SAAS,GAAG,IAAI,gBAAS,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QAErD,iCAAiC;QACjC,MAAM,IAAI,GAAoB;YAC5B,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;YAC1B,SAAS;YACT,SAAS,EAAE,IAAI,CAAC,4BAA4B,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAClE,KAAK,EAAE,EAAE;YACT,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM,EAAE,SAAS;YACjB,aAAa,EAAE,IAAI,GAAG,EAAE;SACzB,CAAC;QAEF,kCAAkC;QAClC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAElC,iBAAiB;QACjB,SAAS,CAAC,SAAS,EAAE,CAAC;QAEtB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAErC,2BAA2B;QAC3B,IAAI,CAAC,MAAM,GAAG,wBAAU,CAAC,OAAO,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;QAEtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,MAAc,EAAE,QAAgB,EAAE,MAAc,EAAE,SAAkB;QAC3E,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAC;QAExB,8BAA8B;QAC9B,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS;YAAE,OAAO,KAAK,CAAC;QAE5C,wCAAwC;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;QACzD,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAE1B,gDAAgD;QAChD,IAAI,YAA0B,CAAC;QAC/B,QAAQ,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;YAC7B,KAAK,QAAQ;gBACX,YAAY,GAAG,mBAAY,CAAC,MAAM,CAAC;gBACnC,MAAM;YACR,KAAK,QAAQ;gBACX,YAAY,GAAG,mBAAY,CAAC,MAAM,CAAC;gBACnC,MAAM;YACR,KAAK,MAAM;gBACT,YAAY,GAAG,mBAAY,CAAC,IAAI,CAAC;gBACjC,MAAM;YACR,KAAK,MAAM;gBACT,YAAY,GAAG,mBAAY,CAAC,IAAI,CAAC;gBACjC,MAAM;YACR;gBACE,OAAO,KAAK,CAAC,CAAC,uBAAuB;QACzC,CAAC;QAED,8BAA8B;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;QAE3E,IAAI,OAAO,EAAE,CAAC;YACZ,sBAAsB;YACtB,MAAM,IAAI,GAAa;gBACrB,QAAQ;gBACR,MAAM;gBACN,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,0BAA0B;YAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEtB,sBAAsB;YACtB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;QAChF,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,MAAc;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAAc;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1C,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,MAAc,EAAE,MAAe;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAC;QAExB,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACjC,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC;QAC9D,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC;QAEjE,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,MAAM;YACnB,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,QAAQ,EAAE,aAAa,CAAC,MAAM;YAC9B,mBAAmB,EAAE,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAC;SACtE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,IAAqB;QAClD,MAAM,YAAY,GAAG,CAAC,SAAwB,EAAE,EAAE;YAChD,2BAA2B;YAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,4BAA4B,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAExE,mCAAmC;YACnC,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;gBACvB,KAAK,gBAAS,CAAC,UAAU;oBACvB,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;oBACzB,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;oBAC7B,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;wBAChE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,SAAS,CAAC,MAAO,CAAC,EAAE,CAAC,CAAC;oBAChF,CAAC;oBACD,MAAM;YACV,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAC9C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,4BAA4B,CAAC,UAAe;QAClD,OAAO;YACL,UAAU,EAAE,UAAU,CAAC,UAAU;YACjC,OAAO,EAAE,UAAU,CAAC,OAAO;YAC3B,kBAAkB,EAAE,UAAU,CAAC,kBAAkB;YACjD,aAAa,EAAE,UAAU,CAAC,aAAa;YACvC,aAAa,EAAE,UAAU,CAAC,aAAa;YACvC,UAAU,EAAE,UAAU,CAAC,UAAU;YACjC,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,SAAS,EAAE,UAAU,CAAC,SAAS;YAC/B,SAAS,EAAE,UAAU,CAAC,SAAS;YAC/B,MAAM,EAAE,UAAU,CAAC,MAAM;SAC1B,CAAC;IACJ,CAAC;IAID;;OAEG;IACK,4BAA4B,CAAC,KAAwB;QAC3D,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEjC,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YAC/C,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACpB,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;YACtE,CAAC;YACD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,aAAa;IACvE,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,SAAiB,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QACvD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,OAAO,GAAG,CAAC,CAAC;QAEhB,KAAK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAClD,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClD,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACtD,IAAI,GAAG,GAAG,MAAM,EAAE,CAAC;oBACjB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAC1B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBACnC,OAAO,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AApQD,kCAoQC;AAED,kDAAkD;AAClD,SAAgB,kBAAkB,CAAC,IAAqB;IACtD,gEAAgE;IAChE,MAAM,eAAe,GAAG;QACtB,GAAG,IAAI,CAAC,SAAS;QACjB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,KAAa,EAAE,EAAE;YACjE,6DAA6D;YAC7D,OAAO;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE;gBACb,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,oCAAoC;gBAClE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,uCAAuC;gBAC1D,QAAQ,EAAE,MAAM,CAAC,QAAQ;aAC1B,CAAC;QACJ,CAAC,CAAC;KACH,CAAC;IAEF,OAAO;QACL,EAAE,EAAE,IAAI,CAAC,EAAE;QACX,MAAM,EAAE,IAAI,CAAC,MAAM;QACnB,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9B,EAAE,EAAE,CAAC,CAAC,EAAE;YACR,IAAI,EAAE,CAAC,CAAC,IAAI;SACb,CAAC,CAAC;QACH,SAAS,EAAE,eAAe;QAC1B,MAAM,EAAE,IAAI,CAAC,MAAM;QACnB,SAAS,EAAE,IAAI,CAAC,SAAS;QACzB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YACpB,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;YAClB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;SACvB,CAAC,CAAC,CAAC,SAAS;QACb,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;KAC7B,CAAC;AACJ,CAAC"}