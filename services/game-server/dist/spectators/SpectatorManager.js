"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpectatorManager = void 0;
class SpectatorManager {
    constructor() {
        this.spectatorGames = new Map(); // gameId -> SpectatorGame
        this.spectatorsByGame = new Map(); // gameId -> spectatorId -> Spectator
        this.spectatorChatMessages = new Map(); // gameId -> messages
        this.playerToGames = new Map(); // playerId -> gameIds (watching)
    }
    /**
     * Добавляет игру для наблюдения
     */
    addGame(game, allowSpectators = true) {
        const spectatorGame = {
            gameId: game.id,
            roomId: game.roomId,
            roomName: `Игра ${game.id.slice(-6)}`,
            players: game.players.map(p => ({
                id: p.id,
                name: p.name,
                rating: undefined // Можно добавить рейтинг позже
            })),
            spectators: [],
            gameState: this.getPublicGameState(game),
            status: game.status,
            startedAt: game.startedAt,
            isPublic: true,
            allowSpectators,
            maxSpectators: 50,
            tournamentId: undefined
        };
        this.spectatorGames.set(game.id, spectatorGame);
        this.spectatorsByGame.set(game.id, new Map());
        this.spectatorChatMessages.set(game.id, []);
        return spectatorGame;
    }
    /**
     * Добавляет зрителя к игре
     */
    addSpectator(gameId, player) {
        const game = this.spectatorGames.get(gameId);
        if (!game) {
            throw new Error('Game not found');
        }
        if (!game.allowSpectators) {
            throw new Error('Spectators not allowed for this game');
        }
        if (game.status === 'finished') {
            throw new Error('Cannot spectate finished game');
        }
        // Проверяем, не является ли игрок участником игры
        if (game.players.some(p => p.id === player.id)) {
            throw new Error('Players cannot spectate their own game');
        }
        const spectators = this.spectatorsByGame.get(gameId);
        // Проверяем, не наблюдает ли уже
        if (spectators.has(player.id)) {
            throw new Error('Already spectating this game');
        }
        // Проверяем лимит зрителей
        if (spectators.size >= game.maxSpectators) {
            throw new Error('Maximum spectators reached');
        }
        const spectator = {
            id: player.id,
            playerId: player.id,
            playerName: player.name,
            joinedAt: new Date(),
            isActive: true
        };
        spectators.set(player.id, spectator);
        game.spectators.push(spectator);
        // Добавляем в индекс игрока
        if (!this.playerToGames.has(player.id)) {
            this.playerToGames.set(player.id, new Set());
        }
        this.playerToGames.get(player.id).add(gameId);
        // Добавляем системное сообщение
        this.addSpectatorChatMessage(gameId, {
            spectatorId: 'system',
            spectatorName: 'Система',
            message: `${player.name} присоединился к просмотру`,
            type: 'system'
        });
        return spectator;
    }
    /**
     * Удаляет зрителя из игры
     */
    removeSpectator(gameId, playerId) {
        const game = this.spectatorGames.get(gameId);
        const spectators = this.spectatorsByGame.get(gameId);
        if (!game || !spectators) {
            return false;
        }
        const spectator = spectators.get(playerId);
        if (!spectator) {
            return false;
        }
        // Удаляем из всех структур данных
        spectators.delete(playerId);
        game.spectators = game.spectators.filter(s => s.playerId !== playerId);
        // Удаляем из индекса игрока
        const playerGames = this.playerToGames.get(playerId);
        if (playerGames) {
            playerGames.delete(gameId);
            if (playerGames.size === 0) {
                this.playerToGames.delete(playerId);
            }
        }
        // Добавляем системное сообщение
        this.addSpectatorChatMessage(gameId, {
            spectatorId: 'system',
            spectatorName: 'Система',
            message: `${spectator.playerName} покинул просмотр`,
            type: 'system'
        });
        return true;
    }
    /**
     * Обновляет состояние игры для зрителей
     */
    updateGameState(game) {
        const spectatorGame = this.spectatorGames.get(game.id);
        if (!spectatorGame) {
            return;
        }
        spectatorGame.gameState = this.getPublicGameState(game);
        spectatorGame.status = game.status;
        // Если игра завершилась, помечаем её как завершенную
        if (game.status === 'finished') {
            spectatorGame.status = 'finished';
            // Добавляем системное сообщение о завершении
            this.addSpectatorChatMessage(game.id, {
                spectatorId: 'system',
                spectatorName: 'Система',
                message: `Игра завершена. Победитель: ${game.winner?.name || 'Неизвестно'}`,
                type: 'system'
            });
            // Через некоторое время удаляем игру из списка
            setTimeout(() => {
                this.removeGame(game.id);
            }, 5 * 60 * 1000); // 5 минут
        }
    }
    /**
     * Удаляет игру из системы спектаторов
     */
    removeGame(gameId) {
        const game = this.spectatorGames.get(gameId);
        if (!game) {
            return;
        }
        // Удаляем всех зрителей
        const spectators = this.spectatorsByGame.get(gameId);
        if (spectators) {
            for (const spectator of spectators.values()) {
                const playerGames = this.playerToGames.get(spectator.playerId);
                if (playerGames) {
                    playerGames.delete(gameId);
                    if (playerGames.size === 0) {
                        this.playerToGames.delete(spectator.playerId);
                    }
                }
            }
        }
        // Удаляем все данные игры
        this.spectatorGames.delete(gameId);
        this.spectatorsByGame.delete(gameId);
        this.spectatorChatMessages.delete(gameId);
    }
    /**
     * Добавляет сообщение в чат зрителей
     */
    addSpectatorChatMessage(gameId, messageData) {
        const messages = this.spectatorChatMessages.get(gameId);
        if (!messages) {
            throw new Error('Game not found');
        }
        const message = {
            id: this.generateMessageId(),
            gameId,
            spectatorId: messageData.spectatorId,
            spectatorName: messageData.spectatorName,
            message: messageData.message,
            timestamp: new Date(),
            type: messageData.type || 'spectator'
        };
        messages.push(message);
        // Ограничиваем количество сообщений
        if (messages.length > 100) {
            messages.splice(0, messages.length - 100);
        }
        return message;
    }
    /**
     * Получает публичное состояние игры (без скрытой информации)
     */
    getPublicGameState(game) {
        if (!game.gameState) {
            return null;
        }
        // Создаем копию состояния игры без скрытой информации
        const publicState = {
            ...game.gameState,
            // Скрываем карты игроков, показываем только количество
            players: game.gameState.players?.map((player) => ({
                ...player,
                hand: undefined,
                handSize: player.hand ? player.hand.length : 0
            })) || []
        };
        return publicState;
    }
    /**
     * Получает список игр доступных для просмотра
     */
    getSpectatorGames() {
        return Array.from(this.spectatorGames.values())
            .filter(game => game.isPublic && game.allowSpectators && game.status !== 'finished')
            .sort((a, b) => {
            // Сортируем по количеству зрителей (популярные сначала)
            if (a.spectators.length !== b.spectators.length) {
                return b.spectators.length - a.spectators.length;
            }
            // Затем по времени начала (новые сначала)
            const aTime = a.startedAt?.getTime() || 0;
            const bTime = b.startedAt?.getTime() || 0;
            return bTime - aTime;
        });
    }
    /**
     * Получает игру для зрителя
     */
    getSpectatorGame(gameId) {
        return this.spectatorGames.get(gameId);
    }
    /**
     * Получает зрителей игры
     */
    getGameSpectators(gameId) {
        const spectators = this.spectatorsByGame.get(gameId);
        return spectators ? Array.from(spectators.values()) : [];
    }
    /**
     * Получает сообщения чата зрителей
     */
    getSpectatorChatMessages(gameId, limit = 50) {
        const messages = this.spectatorChatMessages.get(gameId);
        if (!messages) {
            return [];
        }
        return messages.slice(-limit);
    }
    /**
     * Получает игры, которые смотрит игрок
     */
    getPlayerSpectatorGames(playerId) {
        const gameIds = this.playerToGames.get(playerId);
        if (!gameIds) {
            return [];
        }
        return Array.from(gameIds)
            .map(gameId => this.spectatorGames.get(gameId))
            .filter((game) => game !== undefined);
    }
    /**
     * Проверяет, наблюдает ли игрок за игрой
     */
    isSpectating(playerId, gameId) {
        const spectators = this.spectatorsByGame.get(gameId);
        return spectators ? spectators.has(playerId) : false;
    }
    /**
     * Получает статистику спектаторов
     */
    getSpectatorStats() {
        const totalGames = this.spectatorGames.size;
        const activeGames = Array.from(this.spectatorGames.values())
            .filter(game => game.status === 'playing').length;
        const totalSpectators = Array.from(this.spectatorsByGame.values())
            .reduce((sum, spectators) => sum + spectators.size, 0);
        const mostWatchedGame = Array.from(this.spectatorGames.values())
            .reduce((max, game) => game.spectators.length > (max?.spectators.length || 0) ? game : max, null);
        return {
            totalGames,
            activeGames,
            totalSpectators,
            mostWatchedGame: mostWatchedGame ? {
                gameId: mostWatchedGame.gameId,
                roomName: mostWatchedGame.roomName,
                spectatorCount: mostWatchedGame.spectators.length
            } : null
        };
    }
    /**
     * Очищает неактивные игры и зрителей
     */
    cleanup() {
        let cleaned = 0;
        const now = Date.now();
        const maxAge = 2 * 60 * 60 * 1000; // 2 часа
        for (const [gameId, game] of this.spectatorGames.entries()) {
            // Удаляем старые завершенные игры
            if (game.status === 'finished') {
                const finishedTime = game.startedAt ? game.startedAt.getTime() : now;
                if (now - finishedTime > maxAge) {
                    this.removeGame(gameId);
                    cleaned++;
                }
            }
        }
        return cleaned;
    }
    // Утилиты
    generateMessageId() {
        return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
}
exports.SpectatorManager = SpectatorManager;
//# sourceMappingURL=SpectatorManager.js.map