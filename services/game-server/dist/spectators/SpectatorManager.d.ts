import { Player } from '../players/PlayerManager';
import { MultiplayerGame } from '../game/GameManager';
export interface Spectator {
    id: string;
    playerId: string;
    playerName: string;
    joinedAt: Date;
    isActive: boolean;
}
export interface SpectatorGame {
    gameId: string;
    roomId: string;
    roomName: string;
    players: Array<{
        id: string;
        name: string;
        rating?: number;
    }>;
    spectators: Spectator[];
    gameState: any;
    status: 'waiting' | 'playing' | 'finished';
    startedAt?: Date;
    isPublic: boolean;
    allowSpectators: boolean;
    maxSpectators: number;
    tournamentId?: string;
}
export interface SpectatorChatMessage {
    id: string;
    gameId: string;
    spectatorId: string;
    spectatorName: string;
    message: string;
    timestamp: Date;
    type: 'spectator' | 'system';
}
export declare class SpectatorManager {
    private spectatorGames;
    private spectatorsByGame;
    private spectatorChatMessages;
    private playerToGames;
    /**
     * Добавляет игру для наблюдения
     */
    addGame(game: MultiplayerGame, allowSpectators?: boolean): SpectatorGame;
    /**
     * Добавляет зрителя к игре
     */
    addSpectator(gameId: string, player: Player): Spectator | null;
    /**
     * Удаляет зрителя из игры
     */
    removeSpectator(gameId: string, playerId: string): boolean;
    /**
     * Обновляет состояние игры для зрителей
     */
    updateGameState(game: MultiplayerGame): void;
    /**
     * Удаляет игру из системы спектаторов
     */
    removeGame(gameId: string): void;
    /**
     * Добавляет сообщение в чат зрителей
     */
    addSpectatorChatMessage(gameId: string, messageData: {
        spectatorId: string;
        spectatorName: string;
        message: string;
        type?: 'spectator' | 'system';
    }): SpectatorChatMessage;
    /**
     * Получает публичное состояние игры (без скрытой информации)
     */
    private getPublicGameState;
    /**
     * Получает список игр доступных для просмотра
     */
    getSpectatorGames(): SpectatorGame[];
    /**
     * Получает игру для зрителя
     */
    getSpectatorGame(gameId: string): SpectatorGame | undefined;
    /**
     * Получает зрителей игры
     */
    getGameSpectators(gameId: string): Spectator[];
    /**
     * Получает сообщения чата зрителей
     */
    getSpectatorChatMessages(gameId: string, limit?: number): SpectatorChatMessage[];
    /**
     * Получает игры, которые смотрит игрок
     */
    getPlayerSpectatorGames(playerId: string): SpectatorGame[];
    /**
     * Проверяет, наблюдает ли игрок за игрой
     */
    isSpectating(playerId: string, gameId: string): boolean;
    /**
     * Получает статистику спектаторов
     */
    getSpectatorStats(): {
        totalGames: number;
        activeGames: number;
        totalSpectators: number;
        mostWatchedGame: {
            gameId: string;
            roomName: string;
            spectatorCount: number;
        } | null;
    };
    /**
     * Очищает неактивные игры и зрителей
     */
    cleanup(): number;
    private generateMessageId;
}
//# sourceMappingURL=SpectatorManager.d.ts.map