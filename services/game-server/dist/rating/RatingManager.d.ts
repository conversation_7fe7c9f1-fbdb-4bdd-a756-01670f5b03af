export interface PlayerRating {
    playerId: string;
    playerName: string;
    rating: number;
    gamesPlayed: number;
    wins: number;
    losses: number;
    winRate: number;
    highestRating: number;
    currentStreak: number;
    longestWinStreak: number;
    averageGameDuration: number;
    lastGameAt: Date;
    createdAt: Date;
    updatedAt: Date;
}
export interface GameResult {
    gameId: string;
    winnerId: string;
    loserId: string;
    gameDuration: number;
    moveCount: number;
    timestamp: Date;
}
export declare class RatingManager {
    private ratings;
    private gameResults;
    private readonly K_FACTOR;
    private readonly INITIAL_RATING;
    /**
     * Получает рейтинг игрока или создает новый
     */
    getPlayerRating(playerId: string, playerName: string): PlayerRating;
    /**
     * Создает начальный рейтинг для нового игрока
     */
    private createInitialRating;
    /**
     * Обновляет рейтинги после игры
     */
    updateRatingsAfterGame(winnerId: string, winnerName: string, loserId: string, loserName: string, gameId: string, gameDuration: number, moveCount: number): {
        winnerRating: PlayerRating;
        loserRating: PlayerRating;
    };
    /**
     * Вычисляет ожидаемый результат по формуле ELO
     */
    private getExpectedScore;
    /**
     * Обновляет статистику игрока
     */
    private updatePlayerStats;
    /**
     * Получает таблицу лидеров
     */
    getLeaderboard(limit?: number): PlayerRating[];
    /**
     * Получает топ игроков по различным критериям
     */
    getTopPlayers(): {
        byRating: PlayerRating[];
        byWins: PlayerRating[];
        byWinRate: PlayerRating[];
        byStreak: PlayerRating[];
    };
    /**
     * Получает статистику игрока
     */
    getPlayerStats(playerId: string): PlayerRating | undefined;
    /**
     * Получает историю игр игрока
     */
    getPlayerGameHistory(playerId: string, limit?: number): GameResult[];
    /**
     * Получает общую статистику системы рейтингов
     */
    getSystemStats(): {
        totalPlayers: number;
        totalGames: number;
        averageRating: number;
        highestRating: number;
        mostActivePlayer: null;
        recentGames: number;
    } | {
        totalPlayers: number;
        totalGames: number;
        averageRating: number;
        highestRating: number;
        mostActivePlayer: {
            name: string;
            gamesPlayed: number;
        };
        recentGames: number;
    };
    /**
     * Получает рейтинговые категории
     */
    getRatingCategory(rating: number): {
        name: string;
        color: string;
        minRating: number;
    };
    /**
     * Очищает старые данные
     */
    cleanup(maxAge?: number): number;
}
//# sourceMappingURL=RatingManager.d.ts.map