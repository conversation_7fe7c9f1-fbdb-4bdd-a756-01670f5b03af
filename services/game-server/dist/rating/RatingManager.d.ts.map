{"version": 3, "file": "RatingManager.d.ts", "sourceRoot": "", "sources": ["../../src/rating/RatingManager.ts"], "names": [], "mappings": "AAEA,MAAM,WAAW,YAAY;IAC3B,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE,MAAM,CAAC;IACnB,MAAM,EAAE,MAAM,CAAC;IACf,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,OAAO,EAAE,MAAM,CAAC;IAChB,aAAa,EAAE,MAAM,CAAC;IACtB,aAAa,EAAE,MAAM,CAAC;IACtB,gBAAgB,EAAE,MAAM,CAAC;IACzB,mBAAmB,EAAE,MAAM,CAAC;IAC5B,UAAU,EAAE,IAAI,CAAC;IACjB,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;CACjB;AAED,MAAM,WAAW,UAAU;IACzB,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,YAAY,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,IAAI,CAAC;CACjB;AAED,qBAAa,aAAa;IACxB,OAAO,CAAC,OAAO,CAAwC;IACvD,OAAO,CAAC,WAAW,CAAoB;IAGvC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAM;IAC/B,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAQ;IAEvC;;OAEG;IACH,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,GAAG,YAAY;IAWnE;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAmB3B;;OAEG;IACH,sBAAsB,CACpB,QAAQ,EAAE,MAAM,EAChB,UAAU,EAAE,MAAM,EAClB,OAAO,EAAE,MAAM,EACf,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,MAAM,EACd,YAAY,EAAE,MAAM,EACpB,SAAS,EAAE,MAAM,GAChB;QAAE,YAAY,EAAE,YAAY,CAAC;QAAC,WAAW,EAAE,YAAY,CAAA;KAAE;IAsC5D;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAIxB;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAyCzB;;OAEG;IACH,cAAc,CAAC,KAAK,GAAE,MAAW,GAAG,YAAY,EAAE;IAOlD;;OAEG;IACH,aAAa,IAAI;QACf,QAAQ,EAAE,YAAY,EAAE,CAAC;QACzB,MAAM,EAAE,YAAY,EAAE,CAAC;QACvB,SAAS,EAAE,YAAY,EAAE,CAAC;QAC1B,QAAQ,EAAE,YAAY,EAAE,CAAC;KAC1B;IAYD;;OAEG;IACH,cAAc,CAAC,QAAQ,EAAE,MAAM,GAAG,YAAY,GAAG,SAAS;IAI1D;;OAEG;IACH,oBAAoB,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,GAAE,MAAW,GAAG,UAAU,EAAE;IAOxE;;OAEG;IACH,cAAc;;;;;;;;;;;;;;;;;;IAsCd;;OAEG;IACH,iBAAiB,CAAC,MAAM,EAAE,MAAM,GAAG;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAC;QAAC,SAAS,EAAE,MAAM,CAAA;KAAE;IAUrF;;OAEG;IACH,OAAO,CAAC,MAAM,GAAE,MAAiC,GAAG,MAAM;CAmB3D"}