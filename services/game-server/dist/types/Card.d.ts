export type Suit = 'hearts' | 'diamonds' | 'clubs' | 'spades';
export type Rank = '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | 'J' | 'Q' | 'K' | 'A';
export interface Card {
    suit: Suit;
    rank: Rank;
}
export declare class CardUtils {
    /**
     * Создает стандартную колоду из 52 карт
     */
    static createDeck(): Card[];
    /**
     * Перемешивает колоду
     */
    static shuffleDeck(deck: Card[]): Card[];
    /**
     * Получает числовое значение карты
     */
    static getCardValue(rank: Rank): number;
    /**
     * Получает цвет масти
     */
    static getSuitColor(suit: Suit): 'red' | 'black';
    /**
     * Получает символ масти
     */
    static getSuitSymbol(suit: Suit): string;
    /**
     * Получает эмодзи масти
     */
    static getSuitEmoji(suit: Suit): string;
    /**
     * Форматирует карту для отображения
     */
    static formatCard(card: Card): string;
    /**
     * Сравнивает карты по рангу
     */
    static compareCards(a: Card, b: Card): number;
    /**
     * Проверяет, является ли карта тузом
     */
    static isAce(card: Card): boolean;
    /**
     * Проверяет, является ли карта фигурой
     */
    static isFaceCard(card: Card): boolean;
    /**
     * Проверяет, одинаковые ли масти у карт
     */
    static sameSuit(cards: Card[]): boolean;
    /**
     * Проверяет, одинаковые ли ранги у карт
     */
    static sameRank(cards: Card[]): boolean;
    /**
     * Группирует карты по рангу
     */
    static groupByRank(cards: Card[]): Map<Rank, Card[]>;
    /**
     * Группирует карты по масти
     */
    static groupBySuit(cards: Card[]): Map<Suit, Card[]>;
    /**
     * Сортирует карты по рангу (по убыванию)
     */
    static sortByRank(cards: Card[], ascending?: boolean): Card[];
    /**
     * Сортирует карты по масти
     */
    static sortBySuit(cards: Card[]): Card[];
    /**
     * Проверяет, является ли последовательность стритом
     */
    static isStraight(cards: Card[]): boolean;
    /**
     * Проверяет, является ли набор карт флешем
     */
    static isFlush(cards: Card[]): boolean;
    /**
     * Создает копию карты
     */
    static cloneCard(card: Card): Card;
    /**
     * Создает копию массива карт
     */
    static cloneCards(cards: Card[]): Card[];
}
//# sourceMappingURL=Card.d.ts.map