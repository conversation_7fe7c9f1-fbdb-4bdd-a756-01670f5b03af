"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardUtils = void 0;
class CardUtils {
    /**
     * Создает стандартную колоду из 52 карт
     */
    static createDeck() {
        const deck = [];
        const suits = ['hearts', 'diamonds', 'clubs', 'spades'];
        const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
        for (const suit of suits) {
            for (const rank of ranks) {
                deck.push({ suit, rank });
            }
        }
        return deck;
    }
    /**
     * Перемешивает колоду
     */
    static shuffleDeck(deck) {
        const shuffled = [...deck];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
    /**
     * Получает числовое значение карты
     */
    static getCardValue(rank) {
        switch (rank) {
            case '2': return 2;
            case '3': return 3;
            case '4': return 4;
            case '5': return 5;
            case '6': return 6;
            case '7': return 7;
            case '8': return 8;
            case '9': return 9;
            case '10': return 10;
            case 'J': return 11;
            case 'Q': return 12;
            case 'K': return 13;
            case 'A': return 14;
            default: return 0;
        }
    }
    /**
     * Получает цвет масти
     */
    static getSuitColor(suit) {
        return suit === 'hearts' || suit === 'diamonds' ? 'red' : 'black';
    }
    /**
     * Получает символ масти
     */
    static getSuitSymbol(suit) {
        switch (suit) {
            case 'hearts': return '♥';
            case 'diamonds': return '♦';
            case 'clubs': return '♣';
            case 'spades': return '♠';
            default: return '';
        }
    }
    /**
     * Получает эмодзи масти
     */
    static getSuitEmoji(suit) {
        switch (suit) {
            case 'hearts': return '♥️';
            case 'diamonds': return '♦️';
            case 'clubs': return '♣️';
            case 'spades': return '♠️';
            default: return '';
        }
    }
    /**
     * Форматирует карту для отображения
     */
    static formatCard(card) {
        return `${card.rank}${CardUtils.getSuitSymbol(card.suit)}`;
    }
    /**
     * Сравнивает карты по рангу
     */
    static compareCards(a, b) {
        return CardUtils.getCardValue(a.rank) - CardUtils.getCardValue(b.rank);
    }
    /**
     * Проверяет, является ли карта тузом
     */
    static isAce(card) {
        return card.rank === 'A';
    }
    /**
     * Проверяет, является ли карта фигурой
     */
    static isFaceCard(card) {
        return card.rank === 'J' || card.rank === 'Q' || card.rank === 'K';
    }
    /**
     * Проверяет, одинаковые ли масти у карт
     */
    static sameSuit(cards) {
        if (cards.length === 0)
            return true;
        const firstSuit = cards[0].suit;
        return cards.every(card => card.suit === firstSuit);
    }
    /**
     * Проверяет, одинаковые ли ранги у карт
     */
    static sameRank(cards) {
        if (cards.length === 0)
            return true;
        const firstRank = cards[0].rank;
        return cards.every(card => card.rank === firstRank);
    }
    /**
     * Группирует карты по рангу
     */
    static groupByRank(cards) {
        const groups = new Map();
        for (const card of cards) {
            if (!groups.has(card.rank)) {
                groups.set(card.rank, []);
            }
            groups.get(card.rank).push(card);
        }
        return groups;
    }
    /**
     * Группирует карты по масти
     */
    static groupBySuit(cards) {
        const groups = new Map();
        for (const card of cards) {
            if (!groups.has(card.suit)) {
                groups.set(card.suit, []);
            }
            groups.get(card.suit).push(card);
        }
        return groups;
    }
    /**
     * Сортирует карты по рангу (по убыванию)
     */
    static sortByRank(cards, ascending = false) {
        return [...cards].sort((a, b) => {
            const diff = CardUtils.getCardValue(a.rank) - CardUtils.getCardValue(b.rank);
            return ascending ? diff : -diff;
        });
    }
    /**
     * Сортирует карты по масти
     */
    static sortBySuit(cards) {
        const suitOrder = ['spades', 'hearts', 'diamonds', 'clubs'];
        return [...cards].sort((a, b) => {
            const aSuitIndex = suitOrder.indexOf(a.suit);
            const bSuitIndex = suitOrder.indexOf(b.suit);
            if (aSuitIndex !== bSuitIndex) {
                return aSuitIndex - bSuitIndex;
            }
            return CardUtils.getCardValue(a.rank) - CardUtils.getCardValue(b.rank);
        });
    }
    /**
     * Проверяет, является ли последовательность стритом
     */
    static isStraight(cards) {
        if (cards.length < 5)
            return false;
        const sorted = CardUtils.sortByRank(cards, true);
        const values = sorted.map(card => CardUtils.getCardValue(card.rank));
        const uniqueValues = [...new Set(values)];
        if (uniqueValues.length !== 5)
            return false;
        // Проверяем обычный стрит
        for (let i = 0; i < 4; i++) {
            if (uniqueValues[i + 1] - uniqueValues[i] !== 1) {
                // Проверяем A-2-3-4-5 стрит
                if (uniqueValues[0] === 2 && uniqueValues[1] === 3 &&
                    uniqueValues[2] === 4 && uniqueValues[3] === 5 &&
                    uniqueValues[4] === 14) {
                    return true;
                }
                return false;
            }
        }
        return true;
    }
    /**
     * Проверяет, является ли набор карт флешем
     */
    static isFlush(cards) {
        return cards.length >= 5 && CardUtils.sameSuit(cards);
    }
    /**
     * Создает копию карты
     */
    static cloneCard(card) {
        return { suit: card.suit, rank: card.rank };
    }
    /**
     * Создает копию массива карт
     */
    static cloneCards(cards) {
        return cards.map(card => CardUtils.cloneCard(card));
    }
}
exports.CardUtils = CardUtils;
//# sourceMappingURL=Card.js.map