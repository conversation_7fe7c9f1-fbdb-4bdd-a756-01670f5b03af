{"version": 3, "file": "Card.d.ts", "sourceRoot": "", "sources": ["../../src/types/Card.ts"], "names": [], "mappings": "AAAA,MAAM,MAAM,IAAI,GAAG,QAAQ,GAAG,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC;AAC9D,MAAM,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAEhG,MAAM,WAAW,IAAI;IACnB,IAAI,EAAE,IAAI,CAAC;IACX,IAAI,EAAE,IAAI,CAAC;CACZ;AAED,qBAAa,SAAS;IACpB;;OAEG;IACH,MAAM,CAAC,UAAU,IAAI,IAAI,EAAE;IAc3B;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE;IASxC;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,GAAG,MAAM;IAmBvC;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,GAAG,KAAK,GAAG,OAAO;IAIhD;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,GAAG,MAAM;IAUxC;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,GAAG,MAAM;IAUvC;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,MAAM;IAIrC;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM;IAI7C;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,GAAG,OAAO;IAIjC;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,OAAO;IAItC;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO;IAMvC;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO;IAMvC;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;IAapD;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;IAapD;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,SAAS,GAAE,OAAe,GAAG,IAAI,EAAE;IAOpE;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE;IAYxC;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO;IAyBzC;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO;IAItC;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI;IAIlC;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE;CAGzC"}