"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AchievementManager = void 0;
class AchievementManager {
    constructor() {
        this.achievements = new Map();
        this.playerAchievements = new Map(); // playerId -> achievements
        this.initializeAchievements();
    }
    /**
     * Инициализирует список достижений
     */
    initializeAchievements() {
        const achievements = [
            // Игровые достижения
            {
                id: 'first_game',
                name: 'Первая игра',
                description: 'Сыграйте свою первую игру',
                icon: '🎮',
                category: 'games',
                rarity: 'common',
                condition: (stats) => stats.gamesPlayed >= 1
            },
            {
                id: 'veteran',
                name: 'Ветера<PERSON>',
                description: 'Сыграйте 100 игр',
                icon: '🎖️',
                category: 'games',
                rarity: 'rare',
                condition: (stats) => stats.gamesPlayed >= 100
            },
            {
                id: 'master_player',
                name: 'Мастер игры',
                description: 'Сыграйте 500 игр',
                icon: '👑',
                category: 'games',
                rarity: 'epic',
                condition: (stats) => stats.gamesPlayed >= 500
            },
            // Достижения побед
            {
                id: 'first_win',
                name: 'Первая победа',
                description: 'Выиграйте свою первую игру',
                icon: '🏆',
                category: 'wins',
                rarity: 'common',
                condition: (stats) => stats.wins >= 1
            },
            {
                id: 'winner',
                name: 'Победитель',
                description: 'Выиграйте 50 игр',
                icon: '🥇',
                category: 'wins',
                rarity: 'rare',
                condition: (stats) => stats.wins >= 50
            },
            {
                id: 'champion',
                name: 'Чемпион',
                description: 'Выиграйте 200 игр',
                icon: '👑',
                category: 'wins',
                rarity: 'epic',
                condition: (stats) => stats.wins >= 200
            },
            // Рейтинговые достижения
            {
                id: 'rising_star',
                name: 'Восходящая звезда',
                description: 'Достигните рейтинга 1400',
                icon: '⭐',
                category: 'rating',
                rarity: 'rare',
                condition: (stats) => stats.rating >= 1400
            },
            {
                id: 'expert',
                name: 'Эксперт',
                description: 'Достигните рейтинга 1600',
                icon: '💎',
                category: 'rating',
                rarity: 'epic',
                condition: (stats) => stats.rating >= 1600
            },
            {
                id: 'master',
                name: 'Мастер',
                description: 'Достигните рейтинга 1800',
                icon: '🔥',
                category: 'rating',
                rarity: 'epic',
                condition: (stats) => stats.rating >= 1800
            },
            {
                id: 'grandmaster',
                name: 'Гроссмейстер',
                description: 'Достигните рейтинга 2000',
                icon: '👑',
                category: 'rating',
                rarity: 'legendary',
                condition: (stats) => stats.rating >= 2000
            },
            // Достижения серий
            {
                id: 'hot_streak',
                name: 'Горячая серия',
                description: 'Выиграйте 5 игр подряд',
                icon: '🔥',
                category: 'streaks',
                rarity: 'rare',
                condition: (stats) => stats.longestWinStreak >= 5
            },
            {
                id: 'unstoppable',
                name: 'Неудержимый',
                description: 'Выиграйте 10 игр подряд',
                icon: '⚡',
                category: 'streaks',
                rarity: 'epic',
                condition: (stats) => stats.longestWinStreak >= 10
            },
            {
                id: 'legendary_streak',
                name: 'Легендарная серия',
                description: 'Выиграйте 20 игр подряд',
                icon: '🌟',
                category: 'streaks',
                rarity: 'legendary',
                condition: (stats) => stats.longestWinStreak >= 20
            },
            // Специальные достижения
            {
                id: 'perfectionist',
                name: 'Перфекционист',
                description: 'Достигните 90% побед (минимум 20 игр)',
                icon: '💯',
                category: 'special',
                rarity: 'legendary',
                condition: (stats) => stats.gamesPlayed >= 20 && stats.winRate >= 90
            },
            {
                id: 'speed_demon',
                name: 'Скоростной демон',
                description: 'Средняя продолжительность игры менее 3 минут',
                icon: '💨',
                category: 'special',
                rarity: 'epic',
                condition: (stats) => stats.gamesPlayed >= 10 && stats.averageGameDuration < 180
            },
            {
                id: 'marathon_runner',
                name: 'Марафонец',
                description: 'Средняя продолжительность игры более 15 минут',
                icon: '🏃',
                category: 'special',
                rarity: 'rare',
                condition: (stats) => stats.gamesPlayed >= 10 && stats.averageGameDuration > 900
            },
            {
                id: 'comeback_king',
                name: 'Король камбэков',
                description: 'Выиграйте игру после серии из 5 поражений',
                icon: '🔄',
                category: 'special',
                rarity: 'epic',
                condition: (stats, history) => {
                    // Проверяем последние игры на наличие камбэка
                    const recentGames = history.slice(0, 6);
                    if (recentGames.length < 6)
                        return false;
                    const lastGame = recentGames[0];
                    const isLastGameWin = lastGame.winnerId === stats.playerId;
                    if (!isLastGameWin)
                        return false;
                    // Проверяем, что предыдущие 5 игр были поражениями
                    const previousGames = recentGames.slice(1, 6);
                    return previousGames.every(game => game.loserId === stats.playerId);
                }
            }
        ];
        // Добавляем достижения в карту
        achievements.forEach(achievement => {
            this.achievements.set(achievement.id, achievement);
        });
    }
    /**
     * Проверяет и разблокирует достижения для игрока
     */
    checkAchievements(playerId, playerStats, gameHistory) {
        const currentAchievements = this.playerAchievements.get(playerId) || [];
        const unlockedIds = new Set(currentAchievements.map(a => a.achievementId));
        const newAchievements = [];
        // Проверяем каждое достижение
        for (const [achievementId, achievement] of this.achievements.entries()) {
            // Пропускаем уже разблокированные
            if (unlockedIds.has(achievementId))
                continue;
            // Проверяем условие
            if (achievement.condition(playerStats, gameHistory)) {
                const playerAchievement = {
                    playerId,
                    achievementId,
                    unlockedAt: new Date()
                };
                newAchievements.push(playerAchievement);
                currentAchievements.push(playerAchievement);
            }
        }
        // Обновляем достижения игрока
        if (newAchievements.length > 0) {
            this.playerAchievements.set(playerId, currentAchievements);
        }
        return newAchievements;
    }
    /**
     * Получает все достижения игрока
     */
    getPlayerAchievements(playerId) {
        const playerAchievements = this.playerAchievements.get(playerId) || [];
        const unlockedIds = new Set(playerAchievements.map(a => a.achievementId));
        const unlocked = playerAchievements.map(pa => {
            const achievement = this.achievements.get(pa.achievementId);
            return { ...achievement, unlockedAt: pa.unlockedAt };
        }).sort((a, b) => b.unlockedAt.getTime() - a.unlockedAt.getTime());
        const locked = Array.from(this.achievements.values())
            .filter(achievement => !unlockedIds.has(achievement.id))
            .sort((a, b) => a.name.localeCompare(b.name));
        const total = this.achievements.size;
        const unlockedCount = unlocked.length;
        const percentage = Math.round((unlockedCount / total) * 100);
        return {
            unlocked,
            locked,
            progress: {
                total,
                unlocked: unlockedCount,
                percentage
            }
        };
    }
    /**
     * Получает достижения по категориям
     */
    getAchievementsByCategory() {
        const categories = {};
        for (const achievement of this.achievements.values()) {
            if (!categories[achievement.category]) {
                categories[achievement.category] = [];
            }
            categories[achievement.category].push(achievement);
        }
        // Сортируем по редкости
        const rarityOrder = { common: 0, rare: 1, epic: 2, legendary: 3 };
        for (const category in categories) {
            categories[category].sort((a, b) => rarityOrder[a.rarity] - rarityOrder[b.rarity]);
        }
        return categories;
    }
    /**
     * Получает редкие достижения
     */
    getRareAchievements() {
        return Array.from(this.achievements.values())
            .filter(achievement => achievement.rarity === 'epic' || achievement.rarity === 'legendary')
            .sort((a, b) => a.name.localeCompare(b.name));
    }
    /**
     * Получает статистику достижений
     */
    getAchievementStats() {
        const total = this.achievements.size;
        const byRarity = {};
        const byCategory = {};
        const unlockCounts = new Map();
        // Подсчитываем по редкости и категориям
        for (const achievement of this.achievements.values()) {
            byRarity[achievement.rarity] = (byRarity[achievement.rarity] || 0) + 1;
            byCategory[achievement.category] = (byCategory[achievement.category] || 0) + 1;
            unlockCounts.set(achievement.id, 0);
        }
        // Подсчитываем разблокировки
        for (const achievements of this.playerAchievements.values()) {
            for (const achievement of achievements) {
                const current = unlockCounts.get(achievement.achievementId) || 0;
                unlockCounts.set(achievement.achievementId, current + 1);
            }
        }
        // Находим самое популярное и самое редкое
        let mostUnlocked = null;
        let rarest = null;
        for (const [achievementId, count] of unlockCounts.entries()) {
            const achievement = this.achievements.get(achievementId);
            if (!mostUnlocked || count > mostUnlocked.count) {
                mostUnlocked = { achievement, count };
            }
            if (!rarest || count < rarest.count) {
                rarest = { achievement, count };
            }
        }
        return {
            totalAchievements: total,
            byRarity,
            byCategory,
            mostUnlocked,
            rarest
        };
    }
}
exports.AchievementManager = AchievementManager;
//# sourceMappingURL=AchievementManager.js.map