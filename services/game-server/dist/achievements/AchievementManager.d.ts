import { PlayerRating } from '../rating/RatingManager';
import { GameResult } from '../rating/RatingManager';
export interface Achievement {
    id: string;
    name: string;
    description: string;
    icon: string;
    category: 'games' | 'wins' | 'rating' | 'streaks' | 'special';
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
    condition: (stats: PlayerRating, history: GameResult[]) => boolean;
    reward?: {
        type: 'rating' | 'title' | 'badge';
        value: number | string;
    };
}
export interface PlayerAchievement {
    playerId: string;
    achievementId: string;
    unlockedAt: Date;
    progress?: number;
}
export declare class AchievementManager {
    private achievements;
    private playerAchievements;
    constructor();
    /**
     * Инициализирует список достижений
     */
    private initializeAchievements;
    /**
     * Проверяет и разблокирует достижения для игрока
     */
    checkAchievements(playerId: string, playerStats: PlayerRating, gameHistory: GameResult[]): PlayerAchievement[];
    /**
     * Получает все достижения игрока
     */
    getPlayerAchievements(playerId: string): {
        unlocked: Array<Achievement & {
            unlockedAt: Date;
        }>;
        locked: Achievement[];
        progress: {
            total: number;
            unlocked: number;
            percentage: number;
        };
    };
    /**
     * Получает достижения по категориям
     */
    getAchievementsByCategory(): Record<string, Achievement[]>;
    /**
     * Получает редкие достижения
     */
    getRareAchievements(): Achievement[];
    /**
     * Получает статистику достижений
     */
    getAchievementStats(): {
        totalAchievements: number;
        byRarity: Record<string, number>;
        byCategory: Record<string, number>;
        mostUnlocked: {
            achievement: Achievement;
            count: number;
        } | null;
        rarest: {
            achievement: Achievement;
            count: number;
        } | null;
    };
}
//# sourceMappingURL=AchievementManager.d.ts.map