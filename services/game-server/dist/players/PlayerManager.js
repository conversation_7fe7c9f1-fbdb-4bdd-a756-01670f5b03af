"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlayerManager = void 0;
exports.getPlayerPublicInfo = getPlayerPublicInfo;
const uuid_1 = require("uuid");
class PlayerManager {
    constructor() {
        this.players = new Map(); // socketId -> Player
        this.playersByName = new Map(); // name -> Player
    }
    /**
     * Регистрирует нового игрока
     */
    registerPlayer(socketId, name, socket) {
        // Проверяем, не занято ли имя
        if (this.playersByName.has(name)) {
            throw new Error(`Player name "${name}" is already taken`);
        }
        // Создаем игрока
        const player = {
            id: (0, uuid_1.v4)(),
            socketId,
            name,
            socket,
            joinedAt: new Date(),
            isOnline: true
        };
        this.players.set(socketId, player);
        this.playersByName.set(name, player);
        return player;
    }
    /**
     * Отменяет регистрацию игрока
     */
    unregisterPlayer(socketId) {
        const player = this.players.get(socketId);
        if (!player)
            return false;
        this.players.delete(socketId);
        this.playersByName.delete(player.name);
        return true;
    }
    /**
     * Получает игрока по socket ID
     */
    getPlayer(socketId) {
        return this.players.get(socketId);
    }
    /**
     * Получает игрока по имени
     */
    getPlayerByName(name) {
        return this.playersByName.get(name);
    }
    /**
     * Получает игрока по ID
     */
    getPlayerById(playerId) {
        for (const player of this.players.values()) {
            if (player.id === playerId) {
                return player;
            }
        }
        return undefined;
    }
    /**
     * Получает всех игроков
     */
    getAllPlayers() {
        return Array.from(this.players.values());
    }
    /**
     * Получает количество игроков
     */
    getPlayersCount() {
        return this.players.size;
    }
    /**
     * Получает онлайн игроков
     */
    getOnlinePlayers() {
        return this.getAllPlayers().filter(player => player.isOnline);
    }
    /**
     * Устанавливает статус игрока
     */
    setPlayerStatus(socketId, isOnline) {
        const player = this.players.get(socketId);
        if (!player)
            return false;
        player.isOnline = isOnline;
        return true;
    }
    /**
     * Проверяет, доступно ли имя
     */
    isNameAvailable(name) {
        return !this.playersByName.has(name);
    }
    /**
     * Получает статистику
     */
    getStats() {
        const players = this.getAllPlayers();
        const onlinePlayers = players.filter(p => p.isOnline);
        return {
            total: players.length,
            online: onlinePlayers.length,
            offline: players.length - onlinePlayers.length,
            averageSessionTime: this.calculateAverageSessionTime(players)
        };
    }
    /**
     * Вычисляет среднее время сессии
     */
    calculateAverageSessionTime(players) {
        if (players.length === 0)
            return 0;
        const now = new Date();
        const totalTime = players.reduce((sum, player) => {
            const sessionTime = now.getTime() - player.joinedAt.getTime();
            return sum + sessionTime;
        }, 0);
        return Math.round(totalTime / players.length / 1000); // в секундах
    }
    /**
     * Очищает неактивных игроков
     */
    cleanupInactivePlayers(maxInactiveTime = 30 * 60 * 1000) {
        const now = new Date();
        let cleaned = 0;
        for (const [socketId, player] of this.players.entries()) {
            if (!player.isOnline) {
                const inactiveTime = now.getTime() - player.joinedAt.getTime();
                if (inactiveTime > maxInactiveTime) {
                    this.unregisterPlayer(socketId);
                    cleaned++;
                }
            }
        }
        return cleaned;
    }
}
exports.PlayerManager = PlayerManager;
// Добавляем метод для получения публичной информации
function getPlayerPublicInfo(player) {
    return {
        id: player.id,
        name: player.name,
        isOnline: player.isOnline,
        joinedAt: player.joinedAt
    };
}
//# sourceMappingURL=PlayerManager.js.map