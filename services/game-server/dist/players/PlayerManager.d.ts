import { Socket } from 'socket.io';
export interface Player {
    id: string;
    socketId: string;
    name: string;
    socket: Socket;
    joinedAt: Date;
    isOnline: boolean;
}
export declare class PlayerManager {
    private players;
    private playersByName;
    /**
     * Регистрирует нового игрока
     */
    registerPlayer(socketId: string, name: string, socket: Socket): Player;
    /**
     * Отменяет регистрацию игрока
     */
    unregisterPlayer(socketId: string): boolean;
    /**
     * Получает игрока по socket ID
     */
    getPlayer(socketId: string): Player | undefined;
    /**
     * Получает игрока по имени
     */
    getPlayerByName(name: string): Player | undefined;
    /**
     * Получает игрока по ID
     */
    getPlayerById(playerId: string): Player | undefined;
    /**
     * Получает socket игрока по ID
     */
    getPlayerSocket(playerId: string): Socket | undefined;
    /**
     * Получает всех игроков
     */
    getAllPlayers(): Player[];
    /**
     * Получает количество игроков
     */
    getPlayersCount(): number;
    /**
     * Получает онлайн игроков
     */
    getOnlinePlayers(): Player[];
    /**
     * Устанавливает статус игрока
     */
    setPlayerStatus(socketId: string, isOnline: boolean): boolean;
    /**
     * Проверяет, доступно ли имя
     */
    isNameAvailable(name: string): boolean;
    /**
     * Получает статистику
     */
    getStats(): {
        total: number;
        online: number;
        offline: number;
        averageSessionTime: number;
    };
    /**
     * Вычисляет среднее время сессии
     */
    private calculateAverageSessionTime;
    /**
     * Очищает неактивных игроков
     */
    cleanupInactivePlayers(maxInactiveTime?: number): number;
}
export interface PublicPlayerInfo {
    id: string;
    name: string;
    isOnline: boolean;
    joinedAt: Date;
}
export declare function getPlayerPublicInfo(player: Player): PublicPlayerInfo;
//# sourceMappingURL=PlayerManager.d.ts.map