{"name": "@kozyr-master/game-server", "version": "0.1.0", "description": "Game server for multiplayer functionality", "main": "dist/index.js", "scripts": {"dev": "ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest"}, "dependencies": {"socket.io": "^4.7.2", "express": "^4.18.2", "cors": "^2.8.5", "uuid": "^9.0.0", "@kozyr-master/core": "file:../../packages/core"}, "devDependencies": {"@types/node": "^20.10.5", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/uuid": "^9.0.7", "typescript": "^5.3.3", "ts-node": "^10.9.2", "jest": "^29.7.0", "@types/jest": "^29.5.8"}}