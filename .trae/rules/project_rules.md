# Правила проекта "Козырь Мастер"

## Содержание
1. [Общая информация](#общая-информация)
2. [Структура проекта](#структура-проекта)
3. [Стандарты кодирования](#стандарты-кодирования)
4. [Процесс разработки](#процесс-разработки)
5. [Система контроля версий](#система-контроля-версий)
6. [Тестирование](#тестирование)
7. [Система достижений](#система-достижений)
8. [Документация](#документация)
9. [Обучение новых разработчиков](#обучение-новых-разработчиков)

## Общая информация

"Козырь Мастер" - это проект, построенный на основе микросервисной архитектуры с монорепозиторием для упрощения разработки и деплоя. Проект следует принципам Feature-Sliced Design для организации кодовой базы.

### Технологический стек

#### Frontend
- **React** - основная библиотека для построения пользовательского интерфейса
- **TypeScript** - типизация кода
- **Redux Toolkit** - управление состоянием
- **React Router** - маршрутизация
- **Styled Components** - стилизация компонентов
- **React Native** - для мобильных приложений
- **Electron** - для десктопного приложения
- **i18next** - интернационализация

#### Backend
- **Node.js** - серверная платформа
- **Express** - веб-фреймворк
- **MongoDB** - основная база данных
- **Redis** - кэширование и сессии
- **Socket.io** - реализация WebSocket для игровых функций
- **JWT** - аутентификация и авторизация
- **Mongoose** - ODM для MongoDB

#### DevOps
- **GitHub Actions** - CI/CD
- **Jest** и **Cypress** - тестирование
- **ESLint** и **Prettier** - линтинг и форматирование
- **Sentry** - мониторинг ошибок
- **Docker** - контейнеризация
- **Kubernetes** - оркестрация контейнеров
- **Prometheus** и **Grafana** - мониторинг и визуализация метрик

### Ключевые особенности проекта

- **Мультиплатформенность**: веб, мобильные и десктопные приложения
- **Мультиязычность**: поддержка различных языков интерфейса
- **Обучение**: интерактивные уроки и руководства по карточным играм
- **Соревнования**: турниры и рейтинговая система
- **Монетизация**: премиум-подписка, внутриигровые покупки, реклама

## Структура проекта

Проект организован следующим образом:

```
/
├── packages/               # Пакеты с общим кодом
│   ├── core/               # Ядро игровой логики
│   ├── ui/                 # UI компоненты
│   └── utils/              # Утилиты
├── services/               # Микросервисы
│   ├── auth/               # Аутентификация
│   ├── game/               # Игровой сервис
│   ├── tournament/         # Турнирный сервис
│   ├── payment/            # Платежный сервис
│   └── analytics/          # Аналитика
├── apps/                   # Клиентские приложения
│   ├── web/                # Веб-приложение
│   ├── mobile/             # Мобильное приложение
│   │   ├── android/        # Android
│   │   └── ios/            # iOS
│   └── desktop/            # Десктопное приложение
├── docs/                   # Документация
└── tools/                  # Инструменты разработки
```

### Структура веб-приложения

Веб-приложение построено на основе архитектуры Feature-Sliced Design (FSD):

```
/apps/web/
├── public/              # Статические файлы
├── src/
│   ├── app/            # Конфигурация приложения
│   │   ├── providers/  # Провайдеры (Redux, i18n, и т.д.)
│   │   ├── store/      # Конфигурация Redux
│   │   └── styles/     # Глобальные стили
│   ├── entities/       # Бизнес-сущности (Card, Player, Game)
│   ├── features/       # Функциональность (Auth, GamePlay, Chat)
│   ├── pages/          # Страницы приложения
│   ├── shared/         # Общие компоненты и утилиты
│   └── widgets/        # Композиционные компоненты
└── next.config.js      # Конфигурация Next.js
```

## Стандарты кодирования

### Общие правила

1. **Язык программирования**: Весь код должен быть написан на TypeScript с строгой типизацией.
2. **Форматирование**: Используйте Prettier для автоматического форматирования кода.
3. **Линтинг**: Следуйте правилам ESLint, настроенным для проекта.
4. **Комментарии**: Комментируйте сложные алгоритмы и бизнес-логику. Используйте JSDoc для документирования функций и классов.
5. **Производительность**: Оптимизируйте код для лучшей производительности, особенно в критических частях приложения.
6. **Безопасность**: Следуйте лучшим практикам безопасности, избегайте уязвимостей.

### Соглашения по именованию

1. **Файлы и директории**:
   - Используйте kebab-case для имен файлов и директорий (например, `user-profile.tsx`)
   - Для компонентов React используйте PascalCase (например, `UserProfile.tsx`)
   - Для хуков используйте префикс `use` (например, `useAuth.ts`)
   - Для контекстов используйте суффикс `Context` (например, `GameContext.ts`)
   - Для типов используйте суффикс `Type` или `Types` (например, `UserTypes.ts`)

2. **Переменные и функции**:
   - Используйте camelCase для переменных и функций (например, `getUserData`)
   - Используйте PascalCase для классов и типов (например, `UserProfile`)
   - Используйте UPPER_SNAKE_CASE для констант (например, `MAX_USERS`)
   - Для булевых переменных используйте префиксы `is`, `has`, `should` (например, `isActive`, `hasPermission`)

3. **Компоненты**:
   - Именуйте компоненты осмысленно, отражая их функциональность
   - Избегайте сокращений, кроме общепринятых (например, `UI`, `API`)
   - Для контейнеров используйте суффикс `Container` (например, `GameContainer`)
   - Для страниц используйте суффикс `Page` (например, `ProfilePage`)

### Структура компонентов

Следуйте принципам Feature-Sliced Design:

```
feature/
├── api/           # API запросы
├── model/         # Бизнес-логика и состояние
├── ui/            # UI компоненты
└── lib/           # Вспомогательные функции
```

### Правила для React компонентов

1. **Функциональные компоненты**: Используйте функциональные компоненты с хуками вместо классовых.
2. **Разделение ответственности**: Компоненты должны быть небольшими и иметь одну ответственность.
3. **Переиспользование**: Создавайте переиспользуемые компоненты для общих элементов интерфейса.
4. **Пропсы**: Используйте деструктуризацию пропсов и указывайте типы с помощью TypeScript.
5. **Мемоизация**: Используйте `React.memo`, `useMemo` и `useCallback` для оптимизации производительности.

## Процесс разработки

### Жизненный цикл задачи

1. **Планирование**: Задача создается в трекере задач с подробным описанием и критериями приемки.
2. **Разработка**: Разработчик создает ветку из `develop`, реализует функциональность и пишет тесты.
3. **Код-ревью**: Создается Pull Request, который должен быть проверен как минимум одним другим разработчиком.
4. **Тестирование**: После успешного код-ревью задача передается на тестирование.
5. **Релиз**: После успешного тестирования изменения сливаются в `develop` и затем в `main` при релизе.

### Правила для Pull Request

1. Каждый PR должен решать одну конкретную задачу.
2. Название PR должно содержать номер задачи и краткое описание (например, `[TASK-123] Добавление системы достижений`).
3. Описание PR должно содержать подробную информацию о внесенных изменениях, включая:
   - Ссылку на задачу в трекере
   - Описание реализованной функциональности
   - Скриншоты или видео для визуальных изменений
   - Инструкции по тестированию
4. PR должен проходить все автоматические проверки (линтинг, тесты).
5. PR должен быть проверен как минимум одним другим разработчиком.
6. Размер PR не должен превышать 500 строк кода (исключая автоматически генерируемый код).

### Правила для Code Review

1. **Своевременность**: Код-ревью должно быть выполнено в течение 24 часов после создания PR.
2. **Конструктивность**: Комментарии должны быть конструктивными и содержать предложения по улучшению.
3. **Фокус**: Сосредоточьтесь на архитектуре, производительности, безопасности и читаемости кода.
4. **Тон**: Используйте нейтральный тон в комментариях, избегайте личной критики.
5. **Проверка**: Убедитесь, что код соответствует стандартам проекта и решает поставленную задачу.

### CI/CD процесс

1. **Непрерывная интеграция (CI)**:
   - Автоматическая сборка проекта при каждом коммите
   - Запуск линтеров и форматтеров
   - Запуск модульных и интеграционных тестов
   - Статический анализ кода

2. **Непрерывная доставка (CD)**:
   - Автоматический деплой на тестовое окружение после успешного слияния в `develop`
   - Автоматический деплой на продакшн окружение после успешного слияния в `main`
   - Автоматическое создание релизных заметок

## Система контроля версий

### Git Flow

Проект использует модифицированный Git Flow:

- `main` - стабильная версия, готовая к релизу
- `develop` - основная ветка разработки
- `feature/*` - ветки для разработки новых функций
- `bugfix/*` - ветки для исправления ошибок
- `release/*` - ветки для подготовки релиза
- `hotfix/*` - ветки для срочных исправлений в продакшн

### Правила именования веток

- `feature/TASK-123-краткое-описание` - для новых функций
- `bugfix/TASK-123-краткое-описание` - для исправления ошибок
- `release/v1.2.3` - для релизов
- `hotfix/TASK-123-краткое-описание` - для срочных исправлений

### Правила коммитов

Используйте Conventional Commits для структурирования сообщений коммитов:

```
<тип>[опциональная область]: <описание>

[опциональное тело]

[опциональный футер]
```

Типы коммитов:
- `feat`: новая функциональность
- `fix`: исправление ошибки
- `docs`: изменения в документации
- `style`: изменения форматирования кода
- `refactor`: рефакторинг кода
- `test`: добавление или изменение тестов
- `chore`: изменения в процессе сборки или вспомогательных инструментах
- `perf`: улучшения производительности
- `ci`: изменения в CI/CD конфигурации
- `build`: изменения в системе сборки

### Правила для слияния веток

1. Перед слиянием ветки в `develop` или `main` необходимо обновить ветку из целевой ветки.
2. Используйте `squash and merge` для слияния веток, чтобы сохранить чистую историю коммитов.
3. Удаляйте ветки после успешного слияния.

## Тестирование

### Типы тестов

1. **Модульные тесты**: Тестирование отдельных функций и компонентов с использованием Jest.
2. **Интеграционные тесты**: Тестирование взаимодействия между компонентами.
3. **E2E тесты**: Тестирование полного пользовательского сценария с использованием Cypress.
4. **Нагрузочные тесты**: Тестирование производительности системы под нагрузкой.
5. **Тесты безопасности**: Проверка на наличие уязвимостей.

### Правила тестирования

1. Каждая новая функциональность должна быть покрыта тестами.
2. Минимальное покрытие кода тестами - 80%.
3. Тесты должны быть независимыми друг от друга.
4. Используйте моки для внешних зависимостей.
5. Тесты должны быть быстрыми и надежными.

### Структура тестов

```
__tests__/
├── unit/              # Модульные тесты
├── integration/       # Интеграционные тесты
└── e2e/               # E2E тесты
```

### Примеры тестов

#### Модульный тест для компонента

```typescript
import { render, screen } from '@testing-library/react';
import { Button } from './Button';

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    screen.getByText('Click me').click();
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

#### E2E тест для авторизации

```typescript
describe('Authentication', () => {
  it('allows user to log in', () => {
    cy.visit('/login');
    cy.get('[data-testid=email]').type('<EMAIL>');
    cy.get('[data-testid=password]').type('password123');
    cy.get('[data-testid=submit]').click();
    cy.url().should('include', '/dashboard');
    cy.get('[data-testid=user-menu]').should('contain', 'User');
  });
});
```

## Система достижений

Система достижений - важная часть проекта "Козырь Мастер", которая повышает вовлеченность пользователей.

### Структура системы достижений

Достижения настраиваются в файле `model/achievementData.ts`. Каждое достижение имеет следующие параметры:

- **id**: Уникальный идентификатор достижения
- **title**: Название достижения
- **description**: Описание достижения
- **icon**: Иконка достижения
- **category**: Категория достижения
- **difficulty**: Сложность достижения (easy, medium, hard, expert)
- **points**: Количество очков за достижение
- **condition**: Условие получения достижения
- **reward**: Награда за получение достижения
- **isSecret**: Флаг, указывающий, является ли достижение секретным
- **progress**: Функция для расчета прогресса достижения

### Категории достижений

1. **Игровые достижения**: Связаны с игровым процессом (выигрыши, проигрыши, серии)
2. **Социальные достижения**: Связаны с социальным взаимодействием (друзья, чат, команды)
3. **Коллекционные достижения**: Связаны с коллекционированием (карты, аватары, эмоции)
4. **Турнирные достижения**: Связаны с участием в турнирах (победы, места, серии)
5. **Прогрессивные достижения**: Связаны с общим прогрессом (уровни, опыт, время)

### Модель данных

Система достижений использует следующие модели данных:

```typescript
// Модель достижения
interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: AchievementCategory;
  difficulty: AchievementDifficulty;
  points: number;
  condition: (user: User) => boolean;
  reward: Reward;
  isSecret: boolean;
  progress?: (user: User) => number; // 0-100%
}

// Категории достижений
enum AchievementCategory {
  GAME = 'game',
  SOCIAL = 'social',
  COLLECTION = 'collection',
  TOURNAMENT = 'tournament',
  PROGRESS = 'progress',
}

// Сложность достижений
enum AchievementDifficulty {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard',
  EXPERT = 'expert',
}

// Модель пользовательского достижения
interface UserAchievement {
  userId: string;
  achievementId: string;
  unlockedAt: Date;
  isViewed: boolean;
  progress: number; // 0-100%
}

// Модель пользователя (из сервиса аутентификации)
interface User {
  id: string;
  username: string;
  email: string;
  achievements: UserAchievement[];
  // Другие поля пользователя
}

// Модель награды
interface Reward {
  type: RewardType;
  value: number | string;
  description: string;
}

// Типы наград
enum RewardType {
  COINS = 'coins',
  GEMS = 'gems',
  EXPERIENCE = 'experience',
  AVATAR = 'avatar',
  CARD_BACK = 'card_back',
  EMOTE = 'emote',
}
```

### Процесс проверки достижений

1. При определенных действиях пользователя (завершение игры, выигрыш турнира и т.д.) вызывается функция проверки достижений.
2. Система проверяет все доступные достижения, которые еще не получены пользователем.
3. Если условие достижения выполнено, пользователю выдается достижение и соответствующая награда.
4. Пользователь получает уведомление о новом достижении.
5. Для достижений с прогрессом, система обновляет текущий прогресс пользователя.

### Визуальное представление достижений

1. **Страница достижений**: Отображает все доступные достижения, сгруппированные по категориям.
2. **Карточка достижения**: Содержит иконку, название, описание, прогресс и награду.
3. **Уведомление**: При получении нового достижения пользователь получает всплывающее уведомление.
4. **Профиль пользователя**: Отображает избранные достижения пользователя и общую статистику.

### Рекомендации по разработке достижений

1. Создавайте разнообразные достижения для разных аспектов игры.
2. Балансируйте сложность достижений - от простых до очень сложных.
3. Регулярно добавляйте новые достижения для поддержания интереса пользователей.
4. Используйте достижения для направления пользователей к изучению различных функций игры.
5. Создавайте цепочки связанных достижений с нарастающей сложностью.
6. Добавляйте секретные достижения для создания элемента неожиданности.
7. Обеспечьте визуально привлекательное представление достижений.

## Документация

### Типы документации

1. **Техническая документация**: Описание архитектуры, API, компонентов и функций.
2. **Пользовательская документация**: Руководства пользователя, FAQ, обучающие материалы.
3. **Процессная документация**: Описание процессов разработки, тестирования, деплоя.

### Структура документации

```
docs/
├── architecture/       # Архитектурная документация
├── api/                # API документация
├── components/         # Документация компонентов
├── processes/          # Процессная документация
└── user/               # Пользовательская документация
```

### Правила документирования

1. **Актуальность**: Документация должна быть актуальной и обновляться при изменении кода.
2. **Полнота**: Документация должна быть полной и охватывать все аспекты проекта.
3. **Понятность**: Документация должна быть понятной для целевой аудитории.
4. **Структурированность**: Документация должна быть хорошо структурирована и легко навигируема.
5. **Примеры**: Документация должна содержать примеры использования.

### Инструменты для документирования

1. **Markdown**: Для общей документации.
2. **JSDoc**: Для документирования JavaScript/TypeScript кода.
3. **Swagger/OpenAPI**: Для документирования API.
4. **Storybook**: Для документирования компонентов.

## Обучение новых разработчиков

### Процесс онбординга

1. **Подготовка**: Настройка рабочего окружения, доступов и инструментов.
2. **Знакомство с проектом**: Изучение документации, архитектуры и кодовой базы.
3. **Первые задачи**: Выполнение простых задач под руководством наставника.
4. **Обратная связь**: Регулярные встречи с наставником для обсуждения прогресса.
5. **Самостоятельная работа**: Постепенный переход к самостоятельному выполнению задач.

### Материалы для обучения

1. **Документация проекта**: Техническая документация, архитектура, процессы.
2. **Обучающие материалы**: Статьи, видео, курсы по используемым технологиям.
3. **Код-ревью**: Примеры хорошего и плохого кода с комментариями.
4. **Пара-программирование**: Совместная работа с опытными разработчиками.

### Наставничество

1. **Назначение наставника**: Каждому новому разработчику назначается наставник.
2. **Регулярные встречи**: Еженедельные встречи для обсуждения прогресса и проблем.
3. **Код-ревью**: Наставник проводит код-ревью для задач нового разработчика.
4. **Поддержка**: Наставник оказывает поддержку и отвечает на вопросы.

### Чек-лист для новых разработчиков

1. **Настройка окружения**:
   - Установка необходимого ПО (Node.js, MongoDB, Redis, Git)
   - Клонирование репозитория
   - Установка зависимостей
   - Настройка IDE и расширений

2. **Изучение проекта**:
   - Прочтение документации
   - Изучение архитектуры
   - Ознакомление с кодовой базой
   - Запуск проекта локально

3. **Первые шаги**:
   - Создание простого компонента
   - Написание модульных тестов
   - Создание Pull Request
   - Прохождение код-ревью

4. **Дальнейшее развитие**:
   - Изучение более сложных частей проекта
   - Участие в планировании и обсуждении задач
   - Проведение код-ревью для других разработчиков
   - Внесение предложений по улучшению проекта