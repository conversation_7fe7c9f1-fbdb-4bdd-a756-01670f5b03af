#!/bin/bash

# Скрипт для запуска всех сервисов в development режиме

echo "🚀 Запуск Козырь Мастер в development режиме..."

# Проверяем наличие Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker не установлен. Пожалуйста, установите Docker."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose не установлен. Пожалуйста, установите Docker Compose."
    exit 1
fi

# Создаем .env файл если его нет
if [ ! -f .env ]; then
    echo "📝 Создаем .env файл из примера..."
    cp .env.example .env
    echo "⚠️  Пожалуйста, отредактируйте .env файл с вашими настройками"
fi

# Создаем директории для логов
mkdir -p logs
mkdir -p logs/nginx

# Останавливаем существующие контейнеры
echo "🛑 Останавливаем существующие контейнеры..."
docker-compose down

# Собираем и запускаем только основные сервисы для разработки
echo "🔨 Собираем и запускаем сервисы..."
docker-compose up -d postgres redis

# Ждем пока база данных запустится
echo "⏳ Ждем запуска базы данных..."
sleep 10

# Проверяем подключение к базе данных
echo "🔍 Проверяем подключение к базе данных..."
until docker-compose exec postgres pg_isready -U postgres; do
  echo "База данных еще не готова, ждем..."
  sleep 2
done

echo "✅ База данных готова!"

# Запускаем auth-service
echo "🔐 Запускаем сервис аутентификации..."
cd services/auth-service
npm install
npm run dev &
AUTH_PID=$!
cd ../..

# Ждем запуска auth-service
sleep 5

# Запускаем game-server
echo "🎮 Запускаем игровой сервер..."
cd services/game-server
npm install
npm run dev &
GAME_PID=$!
cd ../..

# Ждем запуска game-server
sleep 5

# Запускаем веб-приложение
echo "🌐 Запускаем веб-приложение..."
cd apps/web
npm install
npm run dev &
WEB_PID=$!
cd ../..

echo ""
echo "🎉 Все сервисы запущены!"
echo ""
echo "📱 Веб-приложение: http://localhost:3000"
echo "🔐 Auth Service: http://localhost:3001"
echo "🎮 Game Server: http://localhost:3002"
echo "🗄️  PostgreSQL: localhost:5432"
echo "🔴 Redis: localhost:6379"
echo ""
echo "📊 Мониторинг:"
echo "   Grafana: http://localhost:3001 (admin/admin)"
echo "   Prometheus: http://localhost:9090"
echo "   Kibana: http://localhost:5601"
echo ""
echo "🛑 Для остановки нажмите Ctrl+C"

# Функция для корректного завершения
cleanup() {
    echo ""
    echo "🛑 Останавливаем сервисы..."
    
    # Останавливаем Node.js процессы
    if [ ! -z "$AUTH_PID" ]; then
        kill $AUTH_PID 2>/dev/null
    fi
    if [ ! -z "$GAME_PID" ]; then
        kill $GAME_PID 2>/dev/null
    fi
    if [ ! -z "$WEB_PID" ]; then
        kill $WEB_PID 2>/dev/null
    fi
    
    # Останавливаем Docker контейнеры
    docker-compose down
    
    echo "✅ Все сервисы остановлены"
    exit 0
}

# Обработчик сигнала для корректного завершения
trap cleanup SIGINT SIGTERM

# Ждем сигнала завершения
wait
