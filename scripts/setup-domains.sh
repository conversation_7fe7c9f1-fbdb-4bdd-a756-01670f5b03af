#!/bin/bash

# Скрипт для настройки доменов и SSL сертификатов для Козырь Мастер

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
MAIN_DOMAIN="kozyr-master.com"
SUBDOMAINS=("www" "api" "ws" "admin" "cdn" "monitoring" "staging" "staging-api")
EMAIL="<EMAIL>"
CLOUDFLARE_API_TOKEN=${CLOUDFLARE_API_TOKEN}
LETS_ENCRYPT_STAGING=${LETS_ENCRYPT_STAGING:-false}

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
    exit 1
}

# Проверка зависимостей
check_dependencies() {
    log "Проверка зависимостей..."
    
    # Проверяем certbot
    if ! command -v certbot &> /dev/null; then
        log "Устанавливаем certbot..."
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo apt-get update
            sudo apt-get install -y certbot python3-certbot-nginx python3-certbot-dns-cloudflare
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            brew install certbot
        fi
    fi
    
    # Проверяем dig
    if ! command -v dig &> /dev/null; then
        log "Устанавливаем dig..."
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo apt-get install -y dnsutils
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            brew install bind
        fi
    fi
    
    # Проверяем curl
    if ! command -v curl &> /dev/null; then
        error "curl не установлен"
    fi
    
    success "Зависимости проверены"
}

# Проверка доступности домена
check_domain_availability() {
    local domain=$1
    log "Проверка доступности домена $domain..."
    
    # Проверяем через whois API
    local whois_response=$(curl -s "https://api.whoisjson.com/v1/$domain")
    
    if echo "$whois_response" | jq -e '.available' > /dev/null; then
        if [ "$(echo "$whois_response" | jq -r '.available')" = "true" ]; then
            success "Домен $domain доступен для регистрации"
            return 0
        else
            warning "Домен $domain уже зарегистрирован"
            return 1
        fi
    else
        warning "Не удалось проверить доступность домена $domain"
        return 1
    fi
}

# Регистрация домена (через API регистратора)
register_domain() {
    local domain=$1
    log "Регистрация домена $domain..."
    
    # Здесь должна быть интеграция с API регистратора
    # Например, Namecheap, GoDaddy, или другого
    
    cat << EOF
📋 ИНСТРУКЦИИ ПО РЕГИСТРАЦИИ ДОМЕНА:

1. Перейдите на сайт регистратора доменов:
   - Namecheap: https://www.namecheap.com
   - GoDaddy: https://www.godaddy.com
   - Reg.ru: https://www.reg.ru

2. Найдите и зарегистрируйте домен: $domain

3. После регистрации настройте DNS серверы:
   - Если используете Cloudflare: укажите серверы Cloudflare
   - Если используете AWS Route53: укажите серверы из Terraform output

4. Дождитесь распространения DNS (до 48 часов)

5. Запустите этот скрипт снова с флагом --skip-registration
EOF
    
    read -p "Нажмите Enter после регистрации домена..."
}

# Настройка Cloudflare DNS
setup_cloudflare_dns() {
    log "Настройка DNS записей в Cloudflare..."
    
    if [ -z "$CLOUDFLARE_API_TOKEN" ]; then
        error "CLOUDFLARE_API_TOKEN не установлен"
    fi
    
    # Получаем Zone ID
    local zone_response=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=$MAIN_DOMAIN" \
        -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
        -H "Content-Type: application/json")
    
    local zone_id=$(echo "$zone_response" | jq -r '.result[0].id')
    
    if [ "$zone_id" = "null" ]; then
        error "Не удалось найти зону для домена $MAIN_DOMAIN в Cloudflare"
    fi
    
    success "Zone ID: $zone_id"
    
    # Создаём A запись для основного домена
    log "Создание A записи для $MAIN_DOMAIN..."
    curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$zone_id/dns_records" \
        -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
        -H "Content-Type: application/json" \
        --data '{
            "type": "A",
            "name": "'$MAIN_DOMAIN'",
            "content": "*******",
            "ttl": 300,
            "comment": "Main domain - will be updated with actual IP"
        }' > /dev/null
    
    # Создаём записи для поддоменов
    for subdomain in "${SUBDOMAINS[@]}"; do
        log "Создание записи для $subdomain.$MAIN_DOMAIN..."
        
        curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$zone_id/dns_records" \
            -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
            -H "Content-Type: application/json" \
            --data '{
                "type": "CNAME",
                "name": "'$subdomain'",
                "content": "'$MAIN_DOMAIN'",
                "ttl": 300,
                "comment": "Subdomain for '$subdomain'"
            }' > /dev/null
    done
    
    # Настройка дополнительных записей
    log "Создание дополнительных DNS записей..."
    
    # MX записи для email
    curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$zone_id/dns_records" \
        -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
        -H "Content-Type: application/json" \
        --data '{
            "type": "MX",
            "name": "'$MAIN_DOMAIN'",
            "content": "mail.'$MAIN_DOMAIN'",
            "priority": 10,
            "ttl": 300
        }' > /dev/null
    
    # TXT записи для верификации
    curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$zone_id/dns_records" \
        -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
        -H "Content-Type: application/json" \
        --data '{
            "type": "TXT",
            "name": "'$MAIN_DOMAIN'",
            "content": "v=spf1 include:_spf.google.com ~all",
            "ttl": 300,
            "comment": "SPF record"
        }' > /dev/null
    
    success "DNS записи созданы в Cloudflare"
}

# Создание Cloudflare credentials для certbot
setup_cloudflare_credentials() {
    log "Настройка Cloudflare credentials для certbot..."
    
    local creds_file="/etc/letsencrypt/cloudflare.ini"
    
    sudo mkdir -p /etc/letsencrypt
    
    cat << EOF | sudo tee "$creds_file" > /dev/null
# Cloudflare API token
dns_cloudflare_api_token = $CLOUDFLARE_API_TOKEN
EOF
    
    sudo chmod 600 "$creds_file"
    
    success "Cloudflare credentials настроены"
}

# Получение SSL сертификатов
obtain_ssl_certificates() {
    log "Получение SSL сертификатов от Let's Encrypt..."
    
    # Определяем сервер Let's Encrypt
    local le_server="https://acme-v02.api.letsencrypt.org/directory"
    if [ "$LETS_ENCRYPT_STAGING" = "true" ]; then
        le_server="https://acme-staging-v02.api.letsencrypt.org/directory"
        warning "Используется staging сервер Let's Encrypt"
    fi
    
    # Создаём список доменов
    local domains="$MAIN_DOMAIN"
    for subdomain in "${SUBDOMAINS[@]}"; do
        domains="$domains,$subdomain.$MAIN_DOMAIN"
    done
    
    log "Получение сертификата для доменов: $domains"
    
    # Получаем сертификат через DNS challenge
    sudo certbot certonly \
        --dns-cloudflare \
        --dns-cloudflare-credentials /etc/letsencrypt/cloudflare.ini \
        --server "$le_server" \
        --email "$EMAIL" \
        --agree-tos \
        --non-interactive \
        --domains "$domains"
    
    if [ $? -eq 0 ]; then
        success "SSL сертификаты получены успешно"
    else
        error "Ошибка получения SSL сертификатов"
    fi
}

# Настройка автоматического обновления сертификатов
setup_cert_renewal() {
    log "Настройка автоматического обновления сертификатов..."
    
    # Создаём скрипт для обновления
    cat << 'EOF' | sudo tee /usr/local/bin/renew-certs.sh > /dev/null
#!/bin/bash

# Обновление SSL сертификатов
certbot renew --quiet

# Перезагрузка nginx если сертификаты обновились
if [ $? -eq 0 ]; then
    systemctl reload nginx 2>/dev/null || docker exec nginx nginx -s reload 2>/dev/null || true
fi

# Логирование
echo "$(date): Certificate renewal check completed" >> /var/log/cert-renewal.log
EOF
    
    sudo chmod +x /usr/local/bin/renew-certs.sh
    
    # Добавляем в crontab
    (sudo crontab -l 2>/dev/null; echo "0 3 * * * /usr/local/bin/renew-certs.sh") | sudo crontab -
    
    success "Автоматическое обновление сертификатов настроено"
}

# Проверка SSL сертификатов
verify_ssl_certificates() {
    log "Проверка SSL сертификатов..."
    
    local cert_path="/etc/letsencrypt/live/$MAIN_DOMAIN"
    
    if [ -f "$cert_path/fullchain.pem" ] && [ -f "$cert_path/privkey.pem" ]; then
        # Проверяем срок действия
        local expiry_date=$(openssl x509 -enddate -noout -in "$cert_path/fullchain.pem" | cut -d= -f2)
        local expiry_timestamp=$(date -d "$expiry_date" +%s)
        local current_timestamp=$(date +%s)
        local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
        
        success "Сертификат действителен до: $expiry_date ($days_until_expiry дней)"
        
        # Проверяем домены в сертификате
        log "Домены в сертификате:"
        openssl x509 -text -noout -in "$cert_path/fullchain.pem" | grep -A1 "Subject Alternative Name" | tail -1
        
        return 0
    else
        error "SSL сертификаты не найдены"
        return 1
    fi
}

# Создание конфигурации nginx с SSL
create_nginx_ssl_config() {
    log "Создание конфигурации nginx с SSL..."
    
    cat << EOF > nginx-ssl.conf
# Nginx конфигурация с SSL для Козырь Мастер

# Rate limiting
limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone \$binary_remote_addr zone=auth:10m rate=5r/s;

# Upstream серверы
upstream web_app {
    least_conn;
    server web-app:3000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream auth_service {
    least_conn;
    server auth-service:3001 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream game_server {
    least_conn;
    server game-server:3002 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# Редирект с HTTP на HTTPS
server {
    listen 80;
    server_name $MAIN_DOMAIN www.$MAIN_DOMAIN api.$MAIN_DOMAIN ws.$MAIN_DOMAIN;
    return 301 https://\$server_name\$request_uri;
}

# Основной сайт
server {
    listen 443 ssl http2;
    server_name $MAIN_DOMAIN www.$MAIN_DOMAIN;

    # SSL конфигурация
    ssl_certificate /etc/letsencrypt/live/$MAIN_DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$MAIN_DOMAIN/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    location / {
        proxy_pass http://web_app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}

# API сервер
server {
    listen 443 ssl http2;
    server_name api.$MAIN_DOMAIN;

    # SSL конфигурация
    ssl_certificate /etc/letsencrypt/live/$MAIN_DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$MAIN_DOMAIN/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # CORS headers
    add_header Access-Control-Allow-Origin "https://$MAIN_DOMAIN" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "Authorization, Content-Type" always;

    location /auth/ {
        limit_req zone=auth burst=10 nodelay;
        proxy_pass http://auth_service/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    location /game/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://game_server/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}

# WebSocket сервер
server {
    listen 443 ssl http2;
    server_name ws.$MAIN_DOMAIN;

    # SSL конфигурация
    ssl_certificate /etc/letsencrypt/live/$MAIN_DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$MAIN_DOMAIN/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    location / {
        proxy_pass http://game_server;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }
}
EOF
    
    success "Конфигурация nginx с SSL создана: nginx-ssl.conf"
}

# Тестирование SSL
test_ssl_setup() {
    log "Тестирование SSL настройки..."
    
    # Проверяем каждый домен
    local domains_to_test=("$MAIN_DOMAIN" "www.$MAIN_DOMAIN" "api.$MAIN_DOMAIN")
    
    for domain in "${domains_to_test[@]}"; do
        log "Тестирование SSL для $domain..."
        
        # Проверяем SSL сертификат
        local ssl_check=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null)
        
        if [ $? -eq 0 ]; then
            success "SSL для $domain работает корректно"
        else
            warning "Проблемы с SSL для $domain"
        fi
        
        # Проверяем HTTP -> HTTPS редирект
        local redirect_check=$(curl -s -I "http://$domain" | grep -i "location: https://")
        
        if [ -n "$redirect_check" ]; then
            success "HTTP -> HTTPS редирект для $domain работает"
        else
            warning "Проблемы с редиректом для $domain"
        fi
    done
}

# Создание отчёта
generate_domain_report() {
    log "Генерация отчёта о настройке доменов..."
    
    local report_file="domain-setup-report-$(date +%Y%m%d-%H%M%S).html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Козырь Мастер - Отчёт настройки доменов</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: linear-gradient(135deg, #4a90e2, #357abd); color: white; padding: 20px; border-radius: 10px; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #4a90e2; background: #f8f9fa; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .domain { background: white; padding: 10px; margin: 5px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌐 Козырь Мастер</h1>
        <h2>Отчёт настройки доменов и SSL</h2>
        <p>Дата: $(date)</p>
    </div>
    
    <div class="section">
        <h3>📋 Настроенные домены</h3>
        <div class="domain">
            <strong>Основной:</strong> https://$MAIN_DOMAIN
        </div>
EOF

    for subdomain in "${SUBDOMAINS[@]}"; do
        echo "        <div class=\"domain\"><strong>$subdomain:</strong> https://$subdomain.$MAIN_DOMAIN</div>" >> "$report_file"
    done

    cat >> "$report_file" << EOF
    </div>
    
    <div class="section">
        <h3>🔒 SSL сертификаты</h3>
        <p><span class="success">✅ Получены от Let's Encrypt</span></p>
        <p><span class="success">✅ Автоматическое обновление настроено</span></p>
        <p><span class="success">✅ Wildcard сертификат для всех поддоменов</span></p>
    </div>
    
    <div class="section">
        <h3>🚀 Следующие шаги</h3>
        <ul>
            <li>Обновить DNS записи с реальными IP адресами серверов</li>
            <li>Настроить мониторинг SSL сертификатов</li>
            <li>Протестировать все домены после деплоя</li>
        </ul>
    </div>
</body>
</html>
EOF
    
    success "Отчёт сохранён: $report_file"
}

# Основная функция
main() {
    log "🌐 Настройка доменов и SSL для Козырь Мастер..."
    
    check_dependencies
    
    # Проверяем доступность домена
    if ! check_domain_availability "$MAIN_DOMAIN"; then
        log "Домен уже зарегистрирован, продолжаем настройку..."
    fi
    
    # Настройка DNS
    if [ -n "$CLOUDFLARE_API_TOKEN" ]; then
        setup_cloudflare_dns
        setup_cloudflare_credentials
    else
        warning "CLOUDFLARE_API_TOKEN не установлен, пропускаем настройку DNS"
    fi
    
    # Получение SSL сертификатов
    obtain_ssl_certificates
    setup_cert_renewal
    verify_ssl_certificates
    
    # Создание конфигураций
    create_nginx_ssl_config
    
    # Тестирование
    test_ssl_setup
    
    # Отчёт
    generate_domain_report
    
    success "🎉 Настройка доменов и SSL завершена!"
    success "Домены готовы к использованию: https://$MAIN_DOMAIN"
}

# Запуск
main "$@"
