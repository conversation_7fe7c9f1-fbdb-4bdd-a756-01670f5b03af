#!/bin/bash

# Kozyr Master Production Deployment Script
# This script deploys the entire Kozyr Master platform to production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-production}
NAMESPACE="kozyr-master"
BACKUP_BUCKET="kozyr-master-backups"
MONITORING_URL="https://monitoring.kozyr-master.com"
SLACK_WEBHOOK_URL=${SLACK_WEBHOOK_URL}

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
    exit 1
}

notify_slack() {
    local message="$1"
    local color="$2"
    
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"attachments\":[{\"color\":\"$color\",\"text\":\"$message\"}]}" \
            "$SLACK_WEBHOOK_URL"
    fi
}

check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if kubectl is installed and configured
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
    fi
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        error "docker is not installed"
    fi
    
    # Check if helm is installed
    if ! command -v helm &> /dev/null; then
        error "helm is not installed"
    fi
    
    # Check cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
    fi
    
    success "Prerequisites check passed"
}

create_backup() {
    log "Creating database backup..."
    
    local timestamp=$(date +%Y%m%d-%H%M%S)
    local backup_file="kozyr-master-backup-$timestamp.sql"
    
    # Create database backup
    kubectl exec -n $NAMESPACE deployment/postgres -- \
        pg_dump -U kozyr_user kozyr_master > "/tmp/$backup_file"
    
    # Upload to S3 if AWS CLI is available
    if command -v aws &> /dev/null; then
        aws s3 cp "/tmp/$backup_file" "s3://$BACKUP_BUCKET/database/"
        success "Backup uploaded to S3: s3://$BACKUP_BUCKET/database/$backup_file"
    else
        warning "AWS CLI not found, backup saved locally: /tmp/$backup_file"
    fi
    
    # Keep local backup for rollback
    export BACKUP_FILE="/tmp/$backup_file"
}

build_and_push_images() {
    log "Building and pushing Docker images..."
    
    local services=("auth-service" "game-server" "web-app" "payment-service" "notification-service")
    local registry="ghcr.io/kozyr-master"
    local tag=$(git rev-parse --short HEAD)
    
    for service in "${services[@]}"; do
        log "Building $service..."
        
        if [ "$service" = "web-app" ]; then
            docker build -t "$registry/$service:$tag" -f "apps/web/Dockerfile.prod" .
        else
            docker build -t "$registry/$service:$tag" -f "services/$service/Dockerfile.prod" .
        fi
        
        docker push "$registry/$service:$tag"
        success "$service image built and pushed"
    done
    
    export IMAGE_TAG="$tag"
}

update_manifests() {
    log "Updating Kubernetes manifests..."
    
    # Update image tags in manifests
    sed -i.bak "s|image: kozyr-master/auth-service:.*|image: ghcr.io/kozyr-master/auth-service:$IMAGE_TAG|g" k8s/namespace.yaml
    sed -i.bak "s|image: kozyr-master/game-service:.*|image: ghcr.io/kozyr-master/game-server:$IMAGE_TAG|g" k8s/namespace.yaml
    sed -i.bak "s|image: kozyr-master/web-app:.*|image: ghcr.io/kozyr-master/web-app:$IMAGE_TAG|g" k8s/namespace.yaml
    sed -i.bak "s|image: kozyr-master/payment-service:.*|image: ghcr.io/kozyr-master/payment-service:$IMAGE_TAG|g" k8s/namespace.yaml
    sed -i.bak "s|image: kozyr-master/notification-service:.*|image: ghcr.io/kozyr-master/notification-service:$IMAGE_TAG|g" k8s/namespace.yaml
    
    success "Manifests updated with image tag: $IMAGE_TAG"
}

deploy_infrastructure() {
    log "Deploying infrastructure components..."
    
    # Create namespace if it doesn't exist
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    # Deploy monitoring stack
    log "Deploying monitoring stack..."
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo add grafana https://grafana.github.io/helm-charts
    helm repo update
    
    # Install Prometheus
    helm upgrade --install prometheus prometheus-community/kube-prometheus-stack \
        --namespace $NAMESPACE \
        --set grafana.adminPassword="$GRAFANA_PASSWORD" \
        --wait
    
    # Deploy ELK stack
    log "Deploying ELK stack..."
    helm repo add elastic https://helm.elastic.co
    helm upgrade --install elasticsearch elastic/elasticsearch \
        --namespace $NAMESPACE \
        --set replicas=3 \
        --wait
    
    helm upgrade --install kibana elastic/kibana \
        --namespace $NAMESPACE \
        --wait
    
    success "Infrastructure deployed"
}

deploy_application() {
    log "Deploying application services..."
    
    # Apply database and Redis first
    kubectl apply -f k8s/namespace.yaml
    
    # Wait for database to be ready
    log "Waiting for database to be ready..."
    kubectl wait --for=condition=ready pod -l app=postgres -n $NAMESPACE --timeout=300s
    
    # Wait for Redis to be ready
    log "Waiting for Redis to be ready..."
    kubectl wait --for=condition=ready pod -l app=redis -n $NAMESPACE --timeout=300s
    
    # Deploy application services
    kubectl rollout restart deployment/auth-service -n $NAMESPACE
    kubectl rollout restart deployment/game-service -n $NAMESPACE
    kubectl rollout restart deployment/web-app -n $NAMESPACE
    
    # Wait for deployments to complete
    log "Waiting for deployments to complete..."
    kubectl rollout status deployment/auth-service -n $NAMESPACE --timeout=600s
    kubectl rollout status deployment/game-service -n $NAMESPACE --timeout=600s
    kubectl rollout status deployment/web-app -n $NAMESPACE --timeout=600s
    
    success "Application deployed successfully"
}

run_health_checks() {
    log "Running health checks..."
    
    # Wait for services to be ready
    sleep 30
    
    # Check service health endpoints
    local services=("auth" "game" "web")
    local base_url="https://api.kozyr-master.com"
    
    for service in "${services[@]}"; do
        local url="$base_url/$service/health"
        if [ "$service" = "web" ]; then
            url="https://kozyr-master.com/api/health"
        fi
        
        log "Checking $service health at $url"
        
        if curl -f -s "$url" > /dev/null; then
            success "$service is healthy"
        else
            error "$service health check failed"
        fi
    done
    
    # Test WebSocket connection
    log "Testing WebSocket connection..."
    if node scripts/test-websocket.js; then
        success "WebSocket connection test passed"
    else
        warning "WebSocket connection test failed"
    fi
    
    # Test database connectivity
    log "Testing database connectivity..."
    if kubectl exec -n $NAMESPACE deployment/postgres -- pg_isready -U kozyr_user -d kozyr_master; then
        success "Database is accessible"
    else
        error "Database connectivity test failed"
    fi
}

update_monitoring() {
    log "Updating monitoring dashboards..."
    
    # Create deployment annotation in Grafana
    local annotation_data="{
        \"tags\": [\"deployment\"],
        \"text\": \"Production deployment: $IMAGE_TAG\",
        \"time\": $(date +%s)000
    }"
    
    if [ -n "$GRAFANA_API_KEY" ]; then
        curl -X POST \
            -H "Authorization: Bearer $GRAFANA_API_KEY" \
            -H "Content-Type: application/json" \
            -d "$annotation_data" \
            "$MONITORING_URL/api/annotations"
        
        success "Monitoring dashboards updated"
    else
        warning "GRAFANA_API_KEY not set, skipping dashboard update"
    fi
}

cleanup() {
    log "Cleaning up..."
    
    # Remove backup manifest files
    rm -f k8s/*.bak
    
    # Clean up old Docker images
    docker image prune -f
    
    success "Cleanup completed"
}

rollback() {
    error "Deployment failed, initiating rollback..."
    
    # Rollback Kubernetes deployments
    kubectl rollout undo deployment/auth-service -n $NAMESPACE
    kubectl rollout undo deployment/game-service -n $NAMESPACE
    kubectl rollout undo deployment/web-app -n $NAMESPACE
    
    # Restore database if backup exists
    if [ -n "$BACKUP_FILE" ] && [ -f "$BACKUP_FILE" ]; then
        warning "Restoring database from backup..."
        kubectl exec -i -n $NAMESPACE deployment/postgres -- \
            psql -U kozyr_user -d kozyr_master < "$BACKUP_FILE"
    fi
    
    # Notify team
    notify_slack "🚨 Production deployment failed and was rolled back" "danger"
    
    exit 1
}

main() {
    log "Starting Kozyr Master production deployment..."
    
    # Set up error handling
    trap rollback ERR
    
    # Notify start
    notify_slack "🚀 Starting production deployment of Kozyr Master" "good"
    
    # Run deployment steps
    check_prerequisites
    create_backup
    build_and_push_images
    update_manifests
    deploy_infrastructure
    deploy_application
    run_health_checks
    update_monitoring
    cleanup
    
    # Success notification
    local success_message="✅ Kozyr Master successfully deployed to production!
    
🔗 Links:
• Website: https://kozyr-master.com
• API: https://api.kozyr-master.com
• Monitoring: https://monitoring.kozyr-master.com
• Version: $IMAGE_TAG"
    
    notify_slack "$success_message" "good"
    
    success "Deployment completed successfully!"
    success "Kozyr Master is now live at https://kozyr-master.com"
}

# Run main function
main "$@"
