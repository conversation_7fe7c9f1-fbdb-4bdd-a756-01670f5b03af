name: Deploy <PERSON><PERSON><PERSON> Master

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: kozyr-master

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: kozyr_master_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        cd services/auth-service && npm ci
        cd ../game-server && npm ci
        cd ../../apps/web && npm ci
    
    - name: Run linting
      run: |
        npm run lint
        cd services/auth-service && npm run lint
        cd ../game-server && npm run lint
        cd ../../apps/web && npm run lint
    
    - name: Run type checking
      run: |
        cd services/auth-service && npm run type-check
        cd ../game-server && npm run type-check
        cd ../../apps/web && npm run type-check
    
    - name: Run tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/kozyr_master_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test_jwt_secret
        JWT_REFRESH_SECRET: test_jwt_refresh_secret
      run: |
        cd services/auth-service && npm test
        cd ../game-server && npm test
        cd ../../apps/web && npm test
    
    - name: Run E2E tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/kozyr_master_test
        REDIS_URL: redis://localhost:6379
      run: |
        npm run test:e2e

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'
    
    - name: Run npm audit
      run: |
        npm audit --audit-level high
        cd services/auth-service && npm audit --audit-level high
        cd ../game-server && npm audit --audit-level high
        cd ../../apps/web && npm audit --audit-level high

  build-and-push:
    needs: [test, security-scan]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    strategy:
      matrix:
        service: [auth-service, game-server, web-app]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ github.repository }}/${{ matrix.service }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./services/${{ matrix.service }}/Dockerfile.prod
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  deploy-staging:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}
    
    - name: Deploy to staging
      run: |
        # Update image tags in k8s manifests
        sed -i "s|image: kozyr-master/auth-service:.*|image: ${{ env.REGISTRY }}/${{ github.repository }}/auth-service:develop-${{ github.sha }}|g" k8s/namespace.yaml
        sed -i "s|image: kozyr-master/game-service:.*|image: ${{ env.REGISTRY }}/${{ github.repository }}/game-server:develop-${{ github.sha }}|g" k8s/namespace.yaml
        sed -i "s|image: kozyr-master/web-app:.*|image: ${{ env.REGISTRY }}/${{ github.repository }}/web-app:develop-${{ github.sha }}|g" k8s/namespace.yaml
        
        # Apply manifests
        kubectl apply -f k8s/namespace.yaml
        kubectl apply -f k8s/ingress-staging.yaml
        
        # Wait for rollout
        kubectl rollout status deployment/auth-service -n kozyr-master-staging --timeout=300s
        kubectl rollout status deployment/game-service -n kozyr-master-staging --timeout=300s
        kubectl rollout status deployment/web-app -n kozyr-master-staging --timeout=300s
    
    - name: Run smoke tests
      run: |
        # Wait for services to be ready
        sleep 30
        
        # Test health endpoints
        curl -f https://staging-api.kozyr-master.com/auth/health
        curl -f https://staging-api.kozyr-master.com/game/health
        curl -f https://staging.kozyr-master.com/api/health
    
    - name: Notify Slack
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow

  deploy-production:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_PRODUCTION }}
    
    - name: Create backup
      run: |
        # Create database backup before deployment
        kubectl exec -n kozyr-master deployment/postgres -- pg_dump -U kozyr_user kozyr_master > backup-$(date +%Y%m%d-%H%M%S).sql
        
        # Upload backup to S3
        aws s3 cp backup-*.sql s3://${{ secrets.BACKUP_BUCKET }}/database/
    
    - name: Deploy to production
      run: |
        # Update image tags in k8s manifests
        sed -i "s|image: kozyr-master/auth-service:.*|image: ${{ env.REGISTRY }}/${{ github.repository }}/auth-service:main-${{ github.sha }}|g" k8s/namespace.yaml
        sed -i "s|image: kozyr-master/game-service:.*|image: ${{ env.REGISTRY }}/${{ github.repository }}/game-server:main-${{ github.sha }}|g" k8s/namespace.yaml
        sed -i "s|image: kozyr-master/web-app:.*|image: ${{ env.REGISTRY }}/${{ github.repository }}/web-app:main-${{ github.sha }}|g" k8s/namespace.yaml
        
        # Apply manifests with rolling update
        kubectl apply -f k8s/namespace.yaml
        kubectl apply -f k8s/ingress-production.yaml
        
        # Wait for rollout
        kubectl rollout status deployment/auth-service -n kozyr-master --timeout=600s
        kubectl rollout status deployment/game-service -n kozyr-master --timeout=600s
        kubectl rollout status deployment/web-app -n kozyr-master --timeout=600s
    
    - name: Run production smoke tests
      run: |
        # Wait for services to be ready
        sleep 60
        
        # Test critical endpoints
        curl -f https://api.kozyr-master.com/auth/health
        curl -f https://api.kozyr-master.com/game/health
        curl -f https://kozyr-master.com/api/health
        
        # Test WebSocket connection
        node scripts/test-websocket.js
        
        # Test database connectivity
        kubectl exec -n kozyr-master deployment/postgres -- pg_isready -U kozyr_user -d kozyr_master
    
    - name: Update monitoring dashboards
      run: |
        # Update Grafana dashboards with new deployment info
        curl -X POST \
          -H "Authorization: Bearer ${{ secrets.GRAFANA_API_KEY }}" \
          -H "Content-Type: application/json" \
          -d '{"tags":["deployment"],"text":"Production deployment: ${{ github.sha }}"}' \
          https://monitoring.kozyr-master.com/api/annotations
    
    - name: Notify team
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#production'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
        custom_payload: |
          {
            "attachments": [{
              "color": "${{ job.status }}" === "success" ? "good" : "danger",
              "title": "Production Deployment ${{ job.status }}",
              "text": "Kozyr Master has been deployed to production",
              "fields": [
                {
                  "title": "Commit",
                  "value": "${{ github.sha }}",
                  "short": true
                },
                {
                  "title": "Author",
                  "value": "${{ github.actor }}",
                  "short": true
                }
              ]
            }]
          }

  mobile-build:
    needs: test
    runs-on: macos-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: mobile/package-lock.json
    
    - name: Install dependencies
      run: |
        cd mobile
        npm ci
    
    - name: Setup Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: '3.0'
        bundler-cache: true
        working-directory: mobile
    
    - name: Install Fastlane
      run: |
        cd mobile
        bundle install
    
    - name: Build iOS app
      run: |
        cd mobile
        npx react-native bundle --platform ios --dev false --entry-file index.js --bundle-output ios/main.jsbundle
        bundle exec fastlane ios build
      env:
        MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
        FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: ${{ secrets.FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD }}
    
    - name: Build Android app
      run: |
        cd mobile
        npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle
        bundle exec fastlane android build
      env:
        ANDROID_KEYSTORE_PASSWORD: ${{ secrets.ANDROID_KEYSTORE_PASSWORD }}
    
    - name: Upload to App Store Connect
      if: github.ref == 'refs/heads/main'
      run: |
        cd mobile
        bundle exec fastlane ios upload_to_testflight
      env:
        FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: ${{ secrets.FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD }}
    
    - name: Upload to Google Play Console
      if: github.ref == 'refs/heads/main'
      run: |
        cd mobile
        bundle exec fastlane android upload_to_play_store
      env:
        GOOGLE_PLAY_JSON_KEY: ${{ secrets.GOOGLE_PLAY_JSON_KEY }}
