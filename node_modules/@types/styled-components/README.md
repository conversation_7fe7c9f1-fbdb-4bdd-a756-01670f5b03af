# Installation
> `npm install --save @types/styled-components`

# Summary
This package contains type definitions for styled-components (https://github.com/styled-components/styled-components).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/styled-components.

### Additional Details
 * Last updated: Wed, 06 Dec 2023 06:36:35 GMT
 * Dependencies: [@types/hoist-non-react-statics](https://npmjs.com/package/@types/hoist-non-react-statics), [@types/react](https://npmjs.com/package/@types/react), [csstype](https://npmjs.com/package/csstype)

# Credits
These definitions were written by [<PERSON>](https://github.com/<PERSON>), [<PERSON><PERSON>](https://github.com/Igmat), [<PERSON>](https://github.com/Jess<PERSON><PERSON>), [<PERSON>](https://github.com/j<PERSON>ian), [<PERSON>](https://github.com/eps1lon), [<PERSON>](https://github.com/wagerfield), [<PERSON><PERSON>](https://github.com/<PERSON><PERSON><PERSON>), [<PERSON>](https://github.com/lifeiscontent), and [<PERSON> <PERSON>](https://github.com/acdr).
