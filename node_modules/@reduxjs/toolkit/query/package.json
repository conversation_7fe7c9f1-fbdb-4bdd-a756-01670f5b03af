{"name": "@reduxjs/toolkit-query", "version": "1.0.0", "description": "", "type": "module", "module": "../dist/query/rtk-query.legacy-esm.js", "main": "../dist/query/cjs/index.js", "types": "./../dist/query/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./../dist/query/index.d.ts", "import": "./../dist/query/rtk-query.modern.mjs", "default": "./../dist/query/cjs/index.js"}}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "sideEffects": false}