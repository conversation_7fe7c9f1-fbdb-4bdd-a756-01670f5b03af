"use strict";var Ce=Object.defineProperty;var Bt=Object.getOwnPropertyDescriptor;var Mt=Object.getOwnPropertyNames;var wt=Object.prototype.hasOwnProperty;var Ct=(e,t)=>{for(var d in t)Ce(e,d,{get:t[d],enumerable:!0})},Ft=(e,t,d,g)=>{if(t&&typeof t=="object"||typeof t=="function")for(let p of Mt(t))!wt.call(e,p)&&p!==d&&Ce(e,p,{get:()=>t[p],enumerable:!(g=Bt(t,p))||g.enumerable});return e};var vt=e=>Ft(Ce({},"__esModule",{value:!0}),e);var Wt={};Ct(Wt,{NamedSchemaError:()=>oe,QueryStatus:()=>De,_NEVER:()=>ft,buildCreateApi:()=>Me,copyWithStructuralSharing:()=>me,coreModule:()=>we,coreModuleName:()=>Se,createApi:()=>It,defaultSerializeQueryArgs:()=>Ae,fakeBaseQuery:()=>mt,fetchBaseQuery:()=>Je,retry:()=>Ye,setupListeners:()=>Xe,skipToken:()=>Re});module.exports=vt(Wt);var De=(p=>(p.uninitialized="uninitialized",p.pending="pending",p.fulfilled="fulfilled",p.rejected="rejected",p))(De||{});function Fe(e){return{status:e,isUninitialized:e==="uninitialized",isLoading:e==="pending",isSuccess:e==="fulfilled",isError:e==="rejected"}}var i=require("@reduxjs/toolkit");var Le=i.isPlainObject;function me(e,t){if(e===t||!(Le(e)&&Le(t)||Array.isArray(e)&&Array.isArray(t)))return t;let d=Object.keys(t),g=Object.keys(e),p=d.length===g.length,D=Array.isArray(t)?[]:{};for(let h of d)D[h]=me(e[h],t[h]),p&&(p=e[h]===D[h]);return p?e:D}function J(e){let t=0;for(let d in e)t++;return t}var ve=e=>[].concat(...e);function je(e){return new RegExp("(^|:)//").test(e)}function He(){return typeof document>"u"?!0:document.visibilityState!=="hidden"}function re(e){return e!=null}function _e(){return typeof navigator>"u"||navigator.onLine===void 0?!0:navigator.onLine}var Ot=e=>e.replace(/\/$/,""),Nt=e=>e.replace(/^\//,"");function Ve(e,t){if(!e)return t;if(!t)return e;if(je(t))return t;let d=e.endsWith("/")||!t.startsWith("?")?"/":"";return e=Ot(e),t=Nt(t),`${e}${d}${t}`}function ze(e,t,d){return e.has(t)?e.get(t):e.set(t,d).get(t)}var We=(...e)=>fetch(...e),qt=e=>e.status>=200&&e.status<=299,Kt=e=>/ion\/(vnd\.api\+)?json/.test(e.get("content-type")||"");function $e(e){if(!(0,i.isPlainObject)(e))return e;let t={...e};for(let[d,g]of Object.entries(t))g===void 0&&delete t[d];return t}function Je({baseUrl:e,prepareHeaders:t=R=>R,fetchFn:d=We,paramsSerializer:g,isJsonContentType:p=Kt,jsonContentType:D="application/json",jsonReplacer:h,timeout:B,responseHandler:C,validateStatus:A,...I}={}){return typeof fetch>"u"&&d===We&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),async(S,o,m)=>{let{getState:M,extra:c,endpoint:s,forced:x,type:l}=o,y,{url:Q,headers:b=new Headers(I.headers),params:E=void 0,responseHandler:k=C??"json",validateStatus:T=A??qt,timeout:u=B,...n}=typeof S=="string"?{url:S}:S,r,a=o.signal;u&&(r=new AbortController,o.signal.addEventListener("abort",r.abort),a=r.signal);let f={...I,signal:a,...n};b=new Headers($e(b)),f.headers=await t(b,{getState:M,arg:S,extra:c,endpoint:s,forced:x,type:l,extraOptions:m})||b;let P=q=>typeof q=="object"&&((0,i.isPlainObject)(q)||Array.isArray(q)||typeof q.toJSON=="function");if(!f.headers.has("content-type")&&P(f.body)&&f.headers.set("content-type",D),P(f.body)&&p(f.headers)&&(f.body=JSON.stringify(f.body,h)),E){let q=~Q.indexOf("?")?"&":"?",v=g?g(E):new URLSearchParams($e(E));Q+=q+v}Q=Ve(e,Q);let O=new Request(Q,f);y={request:new Request(Q,f)};let F,w=!1,U=r&&setTimeout(()=>{w=!0,r.abort()},u);try{F=await d(O)}catch(q){return{error:{status:w?"TIMEOUT_ERROR":"FETCH_ERROR",error:String(q)},meta:y}}finally{U&&clearTimeout(U),r?.signal.removeEventListener("abort",r.abort)}let H=F.clone();y.response=H;let W,j="";try{let q;if(await Promise.all([R(F,k).then(v=>W=v,v=>q=v),H.text().then(v=>j=v,()=>{})]),q)throw q}catch(q){return{error:{status:"PARSING_ERROR",originalStatus:F.status,data:j,error:String(q)},meta:y}}return T(F,W)?{data:W,meta:y}:{error:{status:F.status,data:W},meta:y}};async function R(S,o){if(typeof o=="function")return o(S);if(o==="content-type"&&(o=p(S.headers)?"json":"text"),o==="json"){let m=await S.text();return m.length?JSON.parse(m):null}return S.text()}}var G=class{constructor(t,d=void 0){this.value=t;this.meta=d}};async function Ut(e=0,t=5){let d=Math.min(e,t),g=~~((Math.random()+.4)*(300<<d));await new Promise(p=>setTimeout(D=>p(D),g))}function Lt(e,t){throw Object.assign(new G({error:e,meta:t}),{throwImmediately:!0})}var Ge={},jt=(e,t)=>async(d,g,p)=>{let D=[5,(t||Ge).maxRetries,(p||Ge).maxRetries].filter(I=>I!==void 0),[h]=D.slice(-1),C={maxRetries:h,backoff:Ut,retryCondition:(I,R,{attempt:S})=>S<=h,...t,...p},A=0;for(;;)try{let I=await e(d,g,p);if(I.error)throw new G(I);return I}catch(I){if(A++,I.throwImmediately){if(I instanceof G)return I.value;throw I}if(I instanceof G&&!C.retryCondition(I.value.error,d,{attempt:A,baseQueryApi:g,extraOptions:p}))return I.value;await C.backoff(A,C.maxRetries)}},Ye=Object.assign(jt,{fail:Lt});var ee=(0,i.createAction)("__rtkq/focused"),pe=(0,i.createAction)("__rtkq/unfocused"),te=(0,i.createAction)("__rtkq/online"),ce=(0,i.createAction)("__rtkq/offline"),Oe=!1;function Xe(e,t){function d(){let g=()=>e(ee()),p=()=>e(pe()),D=()=>e(te()),h=()=>e(ce()),B=()=>{window.document.visibilityState==="visible"?g():p()};return Oe||typeof window<"u"&&window.addEventListener&&(window.addEventListener("visibilitychange",B,!1),window.addEventListener("focus",g,!1),window.addEventListener("online",D,!1),window.addEventListener("offline",h,!1),Oe=!0),()=>{window.removeEventListener("focus",g),window.removeEventListener("visibilitychange",B),window.removeEventListener("online",D),window.removeEventListener("offline",h),Oe=!1}}return t?t(e,{onFocus:ee,onFocusLost:pe,onOffline:ce,onOnline:te}):d()}function ie(e){return e.type==="query"}function Ze(e){return e.type==="mutation"}function ae(e){return e.type==="infinitequery"}function le(e){return ie(e)||ae(e)}function ge(e,t,d,g,p,D){return Ht(e)?e(t,d,g,p).filter(re).map(be).map(D):Array.isArray(e)?e.map(be).map(D):[]}function Ht(e){return typeof e=="function"}function be(e){return typeof e=="string"?{type:e}:e}var Pe=require("immer");var Rn=require("@reduxjs/toolkit");function et(e,t){return e.catch(t)}var fe=Symbol("forceQueryFn"),Qe=e=>typeof e[fe]=="function";function tt({serializeQueryArgs:e,queryThunk:t,infiniteQueryThunk:d,mutationThunk:g,api:p,context:D}){let h=new Map,B=new Map,{unsubscribeQueryResult:C,removeMutationResult:A,updateSubscriptionOptions:I}=p.internalActions;return{buildInitiateQuery:s,buildInitiateInfiniteQuery:x,buildInitiateMutation:l,getRunningQueryThunk:R,getRunningMutationThunk:S,getRunningQueriesThunk:o,getRunningMutationsThunk:m};function R(y,Q){return b=>{let E=D.endpointDefinitions[y],k=e({queryArgs:Q,endpointDefinition:E,endpointName:y});return h.get(b)?.[k]}}function S(y,Q){return b=>B.get(b)?.[Q]}function o(){return y=>Object.values(h.get(y)||{}).filter(re)}function m(){return y=>Object.values(B.get(y)||{}).filter(re)}function M(y){}function c(y,Q){let b=(E,{subscribe:k=!0,forceRefetch:T,subscriptionOptions:u,[fe]:n,...r}={})=>(a,f)=>{let P=e({queryArgs:E,endpointDefinition:Q,endpointName:y}),O,N={...r,type:"query",subscribe:k,forceRefetch:T,subscriptionOptions:u,endpointName:y,originalArgs:E,queryCacheKey:P,[fe]:n};if(ie(Q))O=t(N);else{let{direction:_,initialPageParam:K}=r;O=d({...N,direction:_,initialPageParam:K})}let F=p.endpoints[y].select(E),w=a(O),U=F(f());let{requestId:H,abort:W}=w,j=U.requestId!==H,q=h.get(a)?.[P],v=()=>F(f()),V=Object.assign(n?w.then(v):j&&!q?Promise.resolve(U):Promise.all([q,w]).then(v),{arg:E,requestId:H,subscriptionOptions:u,queryCacheKey:P,abort:W,async unwrap(){let _=await V;if(_.isError)throw _.error;return _.data},refetch:()=>a(b(E,{subscribe:!1,forceRefetch:!0})),unsubscribe(){k&&a(C({queryCacheKey:P,requestId:H}))},updateSubscriptionOptions(_){V.subscriptionOptions=_,a(I({endpointName:y,requestId:H,queryCacheKey:P,options:_}))}});if(!q&&!j&&!n){let _=ze(h,a,{});_[P]=V,V.then(()=>{delete _[P],J(_)||h.delete(a)})}return V};return b}function s(y,Q){return c(y,Q)}function x(y,Q){return c(y,Q)}function l(y){return(Q,{track:b=!0,fixedCacheKey:E}={})=>(k,T)=>{let u=g({type:"mutation",endpointName:y,originalArgs:Q,track:b,fixedCacheKey:E}),n=k(u);let{requestId:r,abort:a,unwrap:f}=n,P=et(n.unwrap().then(w=>({data:w})),w=>({error:w})),O=()=>{k(A({requestId:r,fixedCacheKey:E}))},N=Object.assign(P,{arg:n.arg,requestId:r,abort:a,unwrap:f,reset:O}),F=B.get(k)||{};return B.set(k,F),F[r]=N,N.then(()=>{delete F[r],J(F)||B.delete(k)}),E&&(F[E]=N,N.then(()=>{F[E]===N&&(delete F[E],J(F)||B.delete(k))})),N}}}var nt=require("@standard-schema/utils"),oe=class extends nt.SchemaError{constructor(d,g,p,D){super(d);this.value=g;this.schemaName=p;this._bqMeta=D}};async function ne(e,t,d,g){let p=await e["~standard"].validate(t);if(p.issues)throw new oe(p.issues,t,d,g);return p.value}function _t(e){return e}var Te=(e={})=>({...e,[i.SHOULD_AUTOBATCH]:!0});function rt({reducerPath:e,baseQuery:t,context:{endpointDefinitions:d},serializeQueryArgs:g,api:p,assertTagType:D,selectors:h,onSchemaFailure:B,catchSchemaFailure:C,skipSchemaValidation:A}){let I=(n,r,a,f)=>(P,O)=>{let N=d[n],F=g({queryArgs:r,endpointDefinition:N,endpointName:n});if(P(p.internalActions.queryResultPatched({queryCacheKey:F,patches:a})),!f)return;let w=p.endpoints[n].select(r)(O()),U=ge(N.providesTags,w.data,void 0,r,{},D);P(p.internalActions.updateProvidedBy([{queryCacheKey:F,providedTags:U}]))};function R(n,r,a=0){let f=[r,...n];return a&&f.length>a?f.slice(0,-1):f}function S(n,r,a=0){let f=[...n,r];return a&&f.length>a?f.slice(1):f}let o=(n,r,a,f=!0)=>(P,O)=>{let F=p.endpoints[n].select(r)(O()),w={patches:[],inversePatches:[],undo:()=>P(p.util.patchQueryData(n,r,w.inversePatches,f))};if(F.status==="uninitialized")return w;let U;if("data"in F)if((0,Pe.isDraftable)(F.data)){let[H,W,j]=(0,Pe.produceWithPatches)(F.data,a);w.patches.push(...W),w.inversePatches.push(...j),U=H}else U=a(F.data),w.patches.push({op:"replace",path:[],value:U}),w.inversePatches.push({op:"replace",path:[],value:F.data});return w.patches.length===0||P(p.util.patchQueryData(n,r,w.patches,f)),w},m=(n,r,a)=>f=>f(p.endpoints[n].initiate(r,{subscribe:!1,forceRefetch:!0,[fe]:()=>({data:a})})),M=(n,r)=>n.query&&n[r]?n[r]:_t,c=async(n,{signal:r,abort:a,rejectWithValue:f,fulfillWithValue:P,dispatch:O,getState:N,extra:F})=>{let w=d[n.endpointName],{metaSchema:U,skipSchemaValidation:H=A}=w;try{let W=M(w,"transformResponse"),j={signal:r,abort:a,dispatch:O,getState:N,extra:F,endpoint:n.endpointName,type:n.type,forced:n.type==="query"?s(n,N()):void 0,queryCacheKey:n.type==="query"?n.queryCacheKey:void 0},q=n.type==="query"?n[fe]:void 0,v,V=async(K,L,z,ue)=>{if(L==null&&K.pages.length)return Promise.resolve({data:K});let X={queryArg:n.originalArgs,pageParam:L},ye=await _(X),$=ue?R:S;return{data:{pages:$(K.pages,ye.data,z),pageParams:$(K.pageParams,L,z)},meta:ye.meta}};async function _(K){let L,{extraOptions:z,argSchema:ue,rawResponseSchema:X,responseSchema:ye}=w;if(ue&&!H&&(K=await ne(ue,K,"argSchema",{})),q?L=q():w.query?L=await t(w.query(K),j,z):L=await w.queryFn(K,j,z,de=>t(de,j,z)),typeof process<"u",L.error)throw new G(L.error,L.meta);let{data:$}=L;X&&!H&&($=await ne(X,L.data,"rawResponseSchema",L.meta));let Z=await W($,L.meta,K);return ye&&!H&&(Z=await ne(ye,Z,"responseSchema",L.meta)),{...L,data:Z}}if(n.type==="query"&&"infiniteQueryOptions"in w){let{infiniteQueryOptions:K}=w,{maxPages:L=1/0}=K,z,ue={pages:[],pageParams:[]},X=h.selectQueryEntry(N(),n.queryCacheKey)?.data,$=s(n,N())&&!n.direction||!X?ue:X;if("direction"in n&&n.direction&&$.pages.length){let Z=n.direction==="backward",xe=(Z?Ne:Ee)(K,$);z=await V($,xe,L,Z)}else{let{initialPageParam:Z=K.initialPageParam}=n,de=X?.pageParams??[],xe=de[0]??Z,Ke=de.length;z=await V($,xe,L),q&&(z={data:z.data.pages[0]});for(let Ue=1;Ue<Ke;Ue++){let kt=Ee(K,z.data);z=await V(z.data,kt,L)}}v=z}else v=await _(n.originalArgs);return U&&!H&&v.meta&&(v.meta=await ne(U,v.meta,"metaSchema",v.meta)),P(v.data,Te({fulfilledTimeStamp:Date.now(),baseQueryMeta:v.meta}))}catch(W){let j=W;if(j instanceof G){let q=M(w,"transformErrorResponse"),{rawErrorResponseSchema:v,errorResponseSchema:V}=w,{value:_,meta:K}=j;try{v&&!H&&(_=await ne(v,_,"rawErrorResponseSchema",K)),U&&!H&&(K=await ne(U,K,"metaSchema",K));let L=await q(_,K,n.originalArgs);return V&&!H&&(L=await ne(V,L,"errorResponseSchema",K)),f(L,Te({baseQueryMeta:K}))}catch(L){j=L}}try{if(j instanceof oe){let q={endpoint:n.endpointName,arg:n.originalArgs,type:n.type,queryCacheKey:n.type==="query"?n.queryCacheKey:void 0};w.onSchemaFailure?.(j,q),B?.(j,q);let{catchSchemaFailure:v=C}=w;if(v)return f(v(j,q),Te({baseQueryMeta:j._bqMeta}))}}catch(q){j=q}throw typeof process<"u",console.error(j),j}};function s(n,r){let a=h.selectQueryEntry(r,n.queryCacheKey),f=h.selectConfig(r).refetchOnMountOrArgChange,P=a?.fulfilledTimeStamp,O=n.forceRefetch??(n.subscribe&&f);return O?O===!0||(Number(new Date)-Number(P))/1e3>=O:!1}let x=()=>(0,i.createAsyncThunk)(`${e}/executeQuery`,c,{getPendingMeta({arg:r}){let a=d[r.endpointName];return Te({startedTimeStamp:Date.now(),...ae(a)?{direction:r.direction}:{}})},condition(r,{getState:a}){let f=a(),P=h.selectQueryEntry(f,r.queryCacheKey),O=P?.fulfilledTimeStamp,N=r.originalArgs,F=P?.originalArgs,w=d[r.endpointName],U=r.direction;return Qe(r)?!0:P?.status==="pending"?!1:s(r,f)||ie(w)&&w?.forceRefetch?.({currentArg:N,previousArg:F,endpointState:P,state:f})?!0:!(O&&!U)},dispatchConditionRejection:!0}),l=x(),y=x(),Q=(0,i.createAsyncThunk)(`${e}/executeMutation`,c,{getPendingMeta(){return Te({startedTimeStamp:Date.now()})}}),b=n=>"force"in n,E=n=>"ifOlderThan"in n,k=(n,r,a)=>(f,P)=>{let O=b(a)&&a.force,N=E(a)&&a.ifOlderThan,F=(U=!0)=>{let H={forceRefetch:U,isPrefetch:!0};return p.endpoints[n].initiate(r,H)},w=p.endpoints[n].select(r)(P());if(O)f(F());else if(N){let U=w?.fulfilledTimeStamp;if(!U){f(F());return}(Number(new Date)-Number(new Date(U)))/1e3>=N&&f(F())}else f(F(!1))};function T(n){return r=>r?.meta?.arg?.endpointName===n}function u(n,r){return{matchPending:(0,i.isAllOf)((0,i.isPending)(n),T(r)),matchFulfilled:(0,i.isAllOf)((0,i.isFulfilled)(n),T(r)),matchRejected:(0,i.isAllOf)((0,i.isRejected)(n),T(r))}}return{queryThunk:l,mutationThunk:Q,infiniteQueryThunk:y,prefetch:k,updateQueryData:o,upsertQueryData:m,patchQueryData:I,buildMatchThunkActions:u}}function Ee(e,{pages:t,pageParams:d}){let g=t.length-1;return e.getNextPageParam(t[g],t,d[g],d)}function Ne(e,{pages:t,pageParams:d}){return e.getPreviousPageParam?.(t[0],t,d[0],d)}function Ie(e,t,d,g){return ge(d[e.meta.arg.endpointName][t],(0,i.isFulfilled)(e)?e.payload:void 0,(0,i.isRejectedWithValue)(e)?e.payload:void 0,e.meta.arg.originalArgs,"baseQueryMeta"in e.meta?e.meta.baseQueryMeta:void 0,g)}var at=require("immer"),he=require("immer");function ke(e,t,d){let g=e[t];g&&d(g)}function se(e){return("arg"in e?e.arg.fixedCacheKey:e.fixedCacheKey)??e.requestId}function it(e,t,d){let g=e[se(t)];g&&d(g)}var Be={};function ot({reducerPath:e,queryThunk:t,mutationThunk:d,serializeQueryArgs:g,context:{endpointDefinitions:p,apiUid:D,extractRehydrationInfo:h,hasRehydrationInfo:B},assertTagType:C,config:A}){let I=(0,i.createAction)(`${e}/resetApiState`);function R(T,u,n,r){T[u.queryCacheKey]??={status:"uninitialized",endpointName:u.endpointName},ke(T,u.queryCacheKey,a=>{a.status="pending",a.requestId=n&&a.requestId?a.requestId:r.requestId,u.originalArgs!==void 0&&(a.originalArgs=u.originalArgs),a.startedTimeStamp=r.startedTimeStamp;let f=p[r.arg.endpointName];ae(f)&&"direction"in u&&(a.direction=u.direction)})}function S(T,u,n,r){ke(T,u.arg.queryCacheKey,a=>{if(a.requestId!==u.requestId&&!r)return;let{merge:f}=p[u.arg.endpointName];if(a.status="fulfilled",f)if(a.data!==void 0){let{fulfilledTimeStamp:P,arg:O,baseQueryMeta:N,requestId:F}=u,w=(0,i.createNextState)(a.data,U=>f(U,n,{arg:O.originalArgs,baseQueryMeta:N,fulfilledTimeStamp:P,requestId:F}));a.data=w}else a.data=n;else a.data=p[u.arg.endpointName].structuralSharing??!0?me((0,at.isDraft)(a.data)?(0,he.original)(a.data):a.data,n):n;delete a.error,a.fulfilledTimeStamp=u.fulfilledTimeStamp})}let o=(0,i.createSlice)({name:`${e}/queries`,initialState:Be,reducers:{removeQueryResult:{reducer(T,{payload:{queryCacheKey:u}}){delete T[u]},prepare:(0,i.prepareAutoBatched)()},cacheEntriesUpserted:{reducer(T,u){for(let n of u.payload){let{queryDescription:r,value:a}=n;R(T,r,!0,{arg:r,requestId:u.meta.requestId,startedTimeStamp:u.meta.timestamp}),S(T,{arg:r,requestId:u.meta.requestId,fulfilledTimeStamp:u.meta.timestamp,baseQueryMeta:{}},a,!0)}},prepare:T=>({payload:T.map(r=>{let{endpointName:a,arg:f,value:P}=r,O=p[a];return{queryDescription:{type:"query",endpointName:a,originalArgs:r.arg,queryCacheKey:g({queryArgs:f,endpointDefinition:O,endpointName:a})},value:P}}),meta:{[i.SHOULD_AUTOBATCH]:!0,requestId:(0,i.nanoid)(),timestamp:Date.now()}})},queryResultPatched:{reducer(T,{payload:{queryCacheKey:u,patches:n}}){ke(T,u,r=>{r.data=(0,he.applyPatches)(r.data,n.concat())})},prepare:(0,i.prepareAutoBatched)()}},extraReducers(T){T.addCase(t.pending,(u,{meta:n,meta:{arg:r}})=>{let a=Qe(r);R(u,r,a,n)}).addCase(t.fulfilled,(u,{meta:n,payload:r})=>{let a=Qe(n.arg);S(u,n,r,a)}).addCase(t.rejected,(u,{meta:{condition:n,arg:r,requestId:a},error:f,payload:P})=>{ke(u,r.queryCacheKey,O=>{if(!n){if(O.requestId!==a)return;O.status="rejected",O.error=P??f}})}).addMatcher(B,(u,n)=>{let{queries:r}=h(n);for(let[a,f]of Object.entries(r))(f?.status==="fulfilled"||f?.status==="rejected")&&(u[a]=f)})}}),m=(0,i.createSlice)({name:`${e}/mutations`,initialState:Be,reducers:{removeMutationResult:{reducer(T,{payload:u}){let n=se(u);n in T&&delete T[n]},prepare:(0,i.prepareAutoBatched)()}},extraReducers(T){T.addCase(d.pending,(u,{meta:n,meta:{requestId:r,arg:a,startedTimeStamp:f}})=>{a.track&&(u[se(n)]={requestId:r,status:"pending",endpointName:a.endpointName,startedTimeStamp:f})}).addCase(d.fulfilled,(u,{payload:n,meta:r})=>{r.arg.track&&it(u,r,a=>{a.requestId===r.requestId&&(a.status="fulfilled",a.data=n,a.fulfilledTimeStamp=r.fulfilledTimeStamp)})}).addCase(d.rejected,(u,{payload:n,error:r,meta:a})=>{a.arg.track&&it(u,a,f=>{f.requestId===a.requestId&&(f.status="rejected",f.error=n??r)})}).addMatcher(B,(u,n)=>{let{mutations:r}=h(n);for(let[a,f]of Object.entries(r))(f?.status==="fulfilled"||f?.status==="rejected")&&a!==f?.requestId&&(u[a]=f)})}}),M={tags:{},keys:{}},c=(0,i.createSlice)({name:`${e}/invalidation`,initialState:M,reducers:{updateProvidedBy:{reducer(T,u){for(let{queryCacheKey:n,providedTags:r}of u.payload){s(T,n);for(let{type:a,id:f}of r){let P=(T.tags[a]??={})[f||"__internal_without_id"]??=[];P.includes(n)||P.push(n)}T.keys[n]=r}},prepare:(0,i.prepareAutoBatched)()}},extraReducers(T){T.addCase(o.actions.removeQueryResult,(u,{payload:{queryCacheKey:n}})=>{s(u,n)}).addMatcher(B,(u,n)=>{let{provided:r}=h(n);for(let[a,f]of Object.entries(r))for(let[P,O]of Object.entries(f)){let N=(u.tags[a]??={})[P||"__internal_without_id"]??=[];for(let F of O)N.includes(F)||N.push(F)}}).addMatcher((0,i.isAnyOf)((0,i.isFulfilled)(t),(0,i.isRejectedWithValue)(t)),(u,n)=>{x(u,[n])}).addMatcher(o.actions.cacheEntriesUpserted.match,(u,n)=>{let r=n.payload.map(({queryDescription:a,value:f})=>({type:"UNKNOWN",payload:f,meta:{requestStatus:"fulfilled",requestId:"UNKNOWN",arg:a}}));x(u,r)})}});function s(T,u){let n=T.keys[u]??[];for(let r of n){let a=r.type,f=r.id??"__internal_without_id",P=T.tags[a]?.[f];P&&(T.tags[a][f]=P.filter(O=>O!==u))}delete T.keys[u]}function x(T,u){let n=u.map(r=>{let a=Ie(r,"providesTags",p,C),{queryCacheKey:f}=r.meta.arg;return{queryCacheKey:f,providedTags:a}});c.caseReducers.updateProvidedBy(T,c.actions.updateProvidedBy(n))}let l=(0,i.createSlice)({name:`${e}/subscriptions`,initialState:Be,reducers:{updateSubscriptionOptions(T,u){},unsubscribeQueryResult(T,u){},internal_getRTKQSubscriptions(){}}}),y=(0,i.createSlice)({name:`${e}/internalSubscriptions`,initialState:Be,reducers:{subscriptionsUpdated:{reducer(T,u){return(0,he.applyPatches)(T,u.payload)},prepare:(0,i.prepareAutoBatched)()}}}),Q=(0,i.createSlice)({name:`${e}/config`,initialState:{online:_e(),focused:He(),middlewareRegistered:!1,...A},reducers:{middlewareRegistered(T,{payload:u}){T.middlewareRegistered=T.middlewareRegistered==="conflict"||D!==u?"conflict":!0}},extraReducers:T=>{T.addCase(te,u=>{u.online=!0}).addCase(ce,u=>{u.online=!1}).addCase(ee,u=>{u.focused=!0}).addCase(pe,u=>{u.focused=!1}).addMatcher(B,u=>({...u}))}}),b=(0,i.combineReducers)({queries:o.reducer,mutations:m.reducer,provided:c.reducer,subscriptions:y.reducer,config:Q.reducer}),E=(T,u)=>b(I.match(u)?void 0:T,u),k={...Q.actions,...o.actions,...l.actions,...y.actions,...m.actions,...c.actions,resetApiState:I};return{reducer:E,actions:k}}var Re=Symbol.for("RTKQ/skipToken"),yt={status:"uninitialized"},st=(0,i.createNextState)(yt,()=>{}),ut=(0,i.createNextState)(yt,()=>{});function dt({serializeQueryArgs:e,reducerPath:t,createSelector:d}){let g=l=>st,p=l=>ut;return{buildQuerySelector:S,buildInfiniteQuerySelector:o,buildMutationSelector:m,selectInvalidatedBy:M,selectCachedArgsForQuery:c,selectApiState:h,selectQueries:B,selectMutations:A,selectQueryEntry:C,selectConfig:I};function D(l){return{...l,...Fe(l.status)}}function h(l){return l[t]}function B(l){return h(l)?.queries}function C(l,y){return B(l)?.[y]}function A(l){return h(l)?.mutations}function I(l){return h(l)?.config}function R(l,y,Q){return b=>{if(b===Re)return d(g,Q);let E=e({queryArgs:b,endpointDefinition:y,endpointName:l});return d(T=>C(T,E)??st,Q)}}function S(l,y){return R(l,y,D)}function o(l,y){let{infiniteQueryOptions:Q}=y;function b(E){let k={...E,...Fe(E.status)},{isLoading:T,isError:u,direction:n}=k,r=n==="forward",a=n==="backward";return{...k,hasNextPage:s(Q,k.data),hasPreviousPage:x(Q,k.data),isFetchingNextPage:T&&r,isFetchingPreviousPage:T&&a,isFetchNextPageError:u&&r,isFetchPreviousPageError:u&&a}}return R(l,y,b)}function m(){return l=>{let y;return typeof l=="object"?y=se(l)??Re:y=l,d(y===Re?p:E=>h(E)?.mutations?.[y]??ut,D)}}function M(l,y){let Q=l[t],b=new Set;for(let E of y.filter(re).map(be)){let k=Q.provided.tags[E.type];if(!k)continue;let T=(E.id!==void 0?k[E.id]:ve(Object.values(k)))??[];for(let u of T)b.add(u)}return ve(Array.from(b.values()).map(E=>{let k=Q.queries[E];return k?[{queryCacheKey:E,endpointName:k.endpointName,originalArgs:k.originalArgs}]:[]}))}function c(l,y){return Object.values(B(l)).filter(Q=>Q?.endpointName===y&&Q.status!=="uninitialized").map(Q=>Q.originalArgs)}function s(l,y){return y?Ee(l,y)!=null:!1}function x(l,y){return!y||!l.getPreviousPageParam?!1:Ne(l,y)!=null}}var ct=require("@reduxjs/toolkit");var pt=WeakMap?new WeakMap:void 0,Ae=({endpointName:e,queryArgs:t})=>{let d="",g=pt?.get(t);if(typeof g=="string")d=g;else{let p=JSON.stringify(t,(D,h)=>(h=typeof h=="bigint"?{$bigint:h.toString()}:h,h=(0,i.isPlainObject)(h)?Object.keys(h).sort().reduce((B,C)=>(B[C]=h[C],B),{}):h,h));(0,i.isPlainObject)(t)&&pt?.set(t,p),d=p}return`${e}(${d})`};var qe=require("reselect");function Me(...e){return function(d){let g=(0,qe.weakMapMemoize)(A=>d.extractRehydrationInfo?.(A,{reducerPath:d.reducerPath??"api"})),p={reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1,invalidationBehavior:"delayed",...d,extractRehydrationInfo:g,serializeQueryArgs(A){let I=Ae;if("serializeQueryArgs"in A.endpointDefinition){let R=A.endpointDefinition.serializeQueryArgs;I=S=>{let o=R(S);return typeof o=="string"?o:Ae({...S,queryArgs:o})}}else d.serializeQueryArgs&&(I=d.serializeQueryArgs);return I(A)},tagTypes:[...d.tagTypes||[]]},D={endpointDefinitions:{},batch(A){A()},apiUid:(0,i.nanoid)(),extractRehydrationInfo:g,hasRehydrationInfo:(0,qe.weakMapMemoize)(A=>g(A)!=null)},h={injectEndpoints:C,enhanceEndpoints({addTagTypes:A,endpoints:I}){if(A)for(let R of A)p.tagTypes.includes(R)||p.tagTypes.push(R);if(I)for(let[R,S]of Object.entries(I))typeof S=="function"?S(D.endpointDefinitions[R]):Object.assign(D.endpointDefinitions[R]||{},S);return h}},B=e.map(A=>A.init(h,p,D));function C(A){let I=A.endpoints({query:R=>({...R,type:"query"}),mutation:R=>({...R,type:"mutation"}),infiniteQuery:R=>({...R,type:"infinitequery"})});for(let[R,S]of Object.entries(I)){if(A.overrideExisting!==!0&&R in D.endpointDefinitions){if(A.overrideExisting==="throw")throw new Error((0,ct.formatProdErrorMessage)(39));typeof process<"u";continue}typeof process<"u",D.endpointDefinitions[R]=S;for(let o of B)o.injectEndpoint(R,S)}return h}return h.injectEndpoints({endpoints:d.endpoints})}}var lt=require("@reduxjs/toolkit"),ft=Symbol();function mt(){return function(){throw new Error((0,lt.formatProdErrorMessage)(33))}}var Pt=require("immer");function Y(e,...t){return Object.assign(e,...t)}var gt=require("immer");var Qt=({api:e,queryThunk:t,internalState:d})=>{let g=`${e.reducerPath}/subscriptions`,p=null,D=null,{updateSubscriptionOptions:h,unsubscribeQueryResult:B}=e.internalActions,C=(o,m)=>{if(h.match(m)){let{queryCacheKey:c,requestId:s,options:x}=m.payload;return o?.[c]?.[s]&&(o[c][s]=x),!0}if(B.match(m)){let{queryCacheKey:c,requestId:s}=m.payload;return o[c]&&delete o[c][s],!0}if(e.internalActions.removeQueryResult.match(m))return delete o[m.payload.queryCacheKey],!0;if(t.pending.match(m)){let{meta:{arg:c,requestId:s}}=m,x=o[c.queryCacheKey]??={};return x[`${s}_running`]={},c.subscribe&&(x[s]=c.subscriptionOptions??x[s]??{}),!0}let M=!1;if(t.fulfilled.match(m)||t.rejected.match(m)){let c=o[m.meta.arg.queryCacheKey]||{},s=`${m.meta.requestId}_running`;M||=!!c[s],delete c[s]}if(t.rejected.match(m)){let{meta:{condition:c,arg:s,requestId:x}}=m;if(c&&s.subscribe){let l=o[s.queryCacheKey]??={};l[x]=s.subscriptionOptions??l[x]??{},M=!0}}return M},A=()=>d.currentSubscriptions,S={getSubscriptions:A,getSubscriptionCount:o=>{let M=A()[o]??{};return J(M)},isRequestSubscribed:(o,m)=>!!A()?.[o]?.[m]};return(o,m)=>{if(p||(p=JSON.parse(JSON.stringify(d.currentSubscriptions))),e.util.resetApiState.match(o))return p=d.currentSubscriptions={},D=null,[!0,!1];if(e.internalActions.internal_getRTKQSubscriptions.match(o))return[!1,S];let M=C(d.currentSubscriptions,o),c=!0;if(M){D||(D=setTimeout(()=>{let l=JSON.parse(JSON.stringify(d.currentSubscriptions)),[,y]=(0,gt.produceWithPatches)(p,()=>l);m.next(e.internalActions.subscriptionsUpdated(y)),p=l,D=null},500));let s=typeof o.type=="string"&&!!o.type.startsWith(g),x=t.rejected.match(o)&&o.meta.condition&&!!o.meta.arg.subscribe;c=!s&&!x}return[c,!1]}};function Vt(e){for(let t in e)return!1;return!0}var zt=2147483647/1e3-1,Tt=({reducerPath:e,api:t,queryThunk:d,context:g,internalState:p,selectors:{selectQueryEntry:D,selectConfig:h}})=>{let{removeQueryResult:B,unsubscribeQueryResult:C,cacheEntriesUpserted:A}=t.internalActions,I=(0,i.isAnyOf)(C.match,d.fulfilled,d.rejected,A.match);function R(c){let s=p.currentSubscriptions[c];return!!s&&!Vt(s)}let S={},o=(c,s,x)=>{let l=s.getState(),y=h(l);if(I(c)){let Q;if(A.match(c))Q=c.payload.map(b=>b.queryDescription.queryCacheKey);else{let{queryCacheKey:b}=C.match(c)?c.payload:c.meta.arg;Q=[b]}m(Q,s,y)}if(t.util.resetApiState.match(c))for(let[Q,b]of Object.entries(S))b&&clearTimeout(b),delete S[Q];if(g.hasRehydrationInfo(c)){let{queries:Q}=g.extractRehydrationInfo(c);m(Object.keys(Q),s,y)}};function m(c,s,x){let l=s.getState();for(let y of c){let Q=D(l,y);M(y,Q?.endpointName,s,x)}}function M(c,s,x,l){let Q=g.endpointDefinitions[s]?.keepUnusedDataFor??l.keepUnusedDataFor;if(Q===1/0)return;let b=Math.max(0,Math.min(Q,zt));if(!R(c)){let E=S[c];E&&clearTimeout(E),S[c]=setTimeout(()=>{R(c)||x.dispatch(B({queryCacheKey:c})),delete S[c]},b*1e3)}}return o};var ht=new Error("Promise never resolved before cacheEntryRemoved."),Rt=({api:e,reducerPath:t,context:d,queryThunk:g,mutationThunk:p,internalState:D,selectors:{selectQueryEntry:h,selectApiState:B}})=>{let C=(0,i.isAsyncThunkAction)(g),A=(0,i.isAsyncThunkAction)(p),I=(0,i.isFulfilled)(g,p),R={};function S(s,x,l){let y=R[s];y?.valueResolved&&(y.valueResolved({data:x,meta:l}),delete y.valueResolved)}function o(s){let x=R[s];x&&(delete R[s],x.cacheEntryRemoved())}let m=(s,x,l)=>{let y=M(s);function Q(b,E,k,T){let u=h(l,E),n=h(x.getState(),E);!u&&n&&c(b,T,E,x,k)}if(g.pending.match(s))Q(s.meta.arg.endpointName,y,s.meta.requestId,s.meta.arg.originalArgs);else if(e.internalActions.cacheEntriesUpserted.match(s))for(let{queryDescription:b,value:E}of s.payload){let{endpointName:k,originalArgs:T,queryCacheKey:u}=b;Q(k,u,s.meta.requestId,T),S(u,E,{})}else if(p.pending.match(s))x.getState()[t].mutations[y]&&c(s.meta.arg.endpointName,s.meta.arg.originalArgs,y,x,s.meta.requestId);else if(I(s))S(y,s.payload,s.meta.baseQueryMeta);else if(e.internalActions.removeQueryResult.match(s)||e.internalActions.removeMutationResult.match(s))o(y);else if(e.util.resetApiState.match(s))for(let b of Object.keys(R))o(b)};function M(s){return C(s)?s.meta.arg.queryCacheKey:A(s)?s.meta.arg.fixedCacheKey??s.meta.requestId:e.internalActions.removeQueryResult.match(s)?s.payload.queryCacheKey:e.internalActions.removeMutationResult.match(s)?se(s.payload):""}function c(s,x,l,y,Q){let b=d.endpointDefinitions[s],E=b?.onCacheEntryAdded;if(!E)return;let k={},T=new Promise(P=>{k.cacheEntryRemoved=P}),u=Promise.race([new Promise(P=>{k.valueResolved=P}),T.then(()=>{throw ht})]);u.catch(()=>{}),R[l]=k;let n=e.endpoints[s].select(le(b)?x:l),r=y.dispatch((P,O,N)=>N),a={...y,getCacheEntry:()=>n(y.getState()),requestId:Q,extra:r,updateCachedData:le(b)?P=>y.dispatch(e.util.updateQueryData(s,x,P)):void 0,cacheDataLoaded:u,cacheEntryRemoved:T},f=E(x,a);Promise.resolve(f).catch(P=>{if(P!==ht)throw P})}return m};var At=({api:e,context:{apiUid:t},reducerPath:d})=>(g,p)=>{e.util.resetApiState.match(g)&&p.dispatch(e.internalActions.middlewareRegistered(t)),typeof process<"u"};var St=({reducerPath:e,context:t,context:{endpointDefinitions:d},mutationThunk:g,queryThunk:p,api:D,assertTagType:h,refetchQuery:B,internalState:C})=>{let{removeQueryResult:A}=D.internalActions,I=(0,i.isAnyOf)((0,i.isFulfilled)(g),(0,i.isRejectedWithValue)(g)),R=(0,i.isAnyOf)((0,i.isFulfilled)(g,p),(0,i.isRejected)(g,p)),S=[],o=(c,s)=>{I(c)?M(Ie(c,"invalidatesTags",d,h),s):R(c)?M([],s):D.util.invalidateTags.match(c)&&M(ge(c.payload,void 0,void 0,void 0,void 0,h),s)};function m(c){let{queries:s,mutations:x}=c;for(let l of[s,x])for(let y in l)if(l[y]?.status==="pending")return!0;return!1}function M(c,s){let x=s.getState(),l=x[e];if(S.push(...c),l.config.invalidationBehavior==="delayed"&&m(l))return;let y=S;if(S=[],y.length===0)return;let Q=D.util.selectInvalidatedBy(x,y);t.batch(()=>{let b=Array.from(Q.values());for(let{queryCacheKey:E}of b){let k=l.queries[E],T=C.currentSubscriptions[E]??{};k&&(J(T)===0?s.dispatch(A({queryCacheKey:E})):k.status!=="uninitialized"&&s.dispatch(B(k)))}})}return o};var xt=({reducerPath:e,queryThunk:t,api:d,refetchQuery:g,internalState:p})=>{let D={},h=(o,m)=>{(d.internalActions.updateSubscriptionOptions.match(o)||d.internalActions.unsubscribeQueryResult.match(o))&&A(o.payload,m),(t.pending.match(o)||t.rejected.match(o)&&o.meta.condition)&&A(o.meta.arg,m),(t.fulfilled.match(o)||t.rejected.match(o)&&!o.meta.condition)&&C(o.meta.arg,m),d.util.resetApiState.match(o)&&R()};function B(o,m){let c=m.getState()[e].queries[o],s=p.currentSubscriptions[o];if(!(!c||c.status==="uninitialized"))return s}function C({queryCacheKey:o},m){let M=m.getState()[e],c=M.queries[o],s=p.currentSubscriptions[o];if(!c||c.status==="uninitialized")return;let{lowestPollingInterval:x,skipPollingIfUnfocused:l}=S(s);if(!Number.isFinite(x))return;let y=D[o];y?.timeout&&(clearTimeout(y.timeout),y.timeout=void 0);let Q=Date.now()+x;D[o]={nextPollTimestamp:Q,pollingInterval:x,timeout:setTimeout(()=>{(M.config.focused||!l)&&m.dispatch(g(c)),C({queryCacheKey:o},m)},x)}}function A({queryCacheKey:o},m){let c=m.getState()[e].queries[o],s=p.currentSubscriptions[o];if(!c||c.status==="uninitialized")return;let{lowestPollingInterval:x}=S(s);if(!Number.isFinite(x)){I(o);return}let l=D[o],y=Date.now()+x;(!l||y<l.nextPollTimestamp)&&C({queryCacheKey:o},m)}function I(o){let m=D[o];m?.timeout&&clearTimeout(m.timeout),delete D[o]}function R(){for(let o of Object.keys(D))I(o)}function S(o={}){let m=!1,M=Number.POSITIVE_INFINITY;for(let c in o)o[c].pollingInterval&&(M=Math.min(o[c].pollingInterval,M),m=o[c].skipPollingIfUnfocused||m);return{lowestPollingInterval:M,skipPollingIfUnfocused:m}}return h};var Dt=({api:e,context:t,queryThunk:d,mutationThunk:g})=>{let p=(0,i.isPending)(d,g),D=(0,i.isRejected)(d,g),h=(0,i.isFulfilled)(d,g),B={};return(A,I)=>{if(p(A)){let{requestId:R,arg:{endpointName:S,originalArgs:o}}=A.meta,m=t.endpointDefinitions[S],M=m?.onQueryStarted;if(M){let c={},s=new Promise((Q,b)=>{c.resolve=Q,c.reject=b});s.catch(()=>{}),B[R]=c;let x=e.endpoints[S].select(le(m)?o:R),l=I.dispatch((Q,b,E)=>E),y={...I,getCacheEntry:()=>x(I.getState()),requestId:R,extra:l,updateCachedData:le(m)?Q=>I.dispatch(e.util.updateQueryData(S,o,Q)):void 0,queryFulfilled:s};M(o,y)}}else if(h(A)){let{requestId:R,baseQueryMeta:S}=A.meta;B[R]?.resolve({data:A.payload,meta:S}),delete B[R]}else if(D(A)){let{requestId:R,rejectedWithValue:S,baseQueryMeta:o}=A.meta;B[R]?.reject({error:A.payload??A.error,isUnhandledError:!S,meta:o}),delete B[R]}}};var bt=({reducerPath:e,context:t,api:d,refetchQuery:g,internalState:p})=>{let{removeQueryResult:D}=d.internalActions,h=(C,A)=>{ee.match(C)&&B(A,"refetchOnFocus"),te.match(C)&&B(A,"refetchOnReconnect")};function B(C,A){let I=C.getState()[e],R=I.queries,S=p.currentSubscriptions;t.batch(()=>{for(let o of Object.keys(S)){let m=R[o],M=S[o];if(!M||!m)continue;(Object.values(M).some(s=>s[A]===!0)||Object.values(M).every(s=>s[A]===void 0)&&I.config[A])&&(J(M)===0?C.dispatch(D({queryCacheKey:o})):m.status!=="uninitialized"&&C.dispatch(g(m)))}})}return h};function Et(e){let{reducerPath:t,queryThunk:d,api:g,context:p}=e,{apiUid:D}=p,h={invalidateTags:(0,i.createAction)(`${t}/invalidateTags`)},B=R=>R.type.startsWith(`${t}/`),C=[At,Tt,St,xt,Rt,Dt];return{middleware:R=>{let S=!1,m={...e,internalState:{currentSubscriptions:{}},refetchQuery:I,isThisApiSliceAction:B},M=C.map(x=>x(m)),c=Qt(m),s=bt(m);return x=>l=>{if(!(0,i.isAction)(l))return x(l);S||(S=!0,R.dispatch(g.internalActions.middlewareRegistered(D)));let y={...R,next:x},Q=R.getState(),[b,E]=c(l,y,Q),k;if(b?k=x(l):k=E,R.getState()[t]&&(s(l,y,Q),B(l)||p.hasRehydrationInfo(l)))for(let T of M)T(l,y,Q);return k}},actions:h};function I(R){return e.api.endpoints[R.endpointName].initiate(R.originalArgs,{subscribe:!1,forceRefetch:!0})}}var Se=Symbol(),we=({createSelector:e=i.createSelector}={})=>({name:Se,init(t,{baseQuery:d,tagTypes:g,reducerPath:p,serializeQueryArgs:D,keepUnusedDataFor:h,refetchOnMountOrArgChange:B,refetchOnFocus:C,refetchOnReconnect:A,invalidationBehavior:I,onSchemaFailure:R,catchSchemaFailure:S,skipSchemaValidation:o},m){(0,Pt.enablePatches)();let M=v=>(typeof process<"u",v);Object.assign(t,{reducerPath:p,endpoints:{},internalActions:{onOnline:te,onOffline:ce,onFocus:ee,onFocusLost:pe},util:{}});let c=dt({serializeQueryArgs:D,reducerPath:p,createSelector:e}),{selectInvalidatedBy:s,selectCachedArgsForQuery:x,buildQuerySelector:l,buildInfiniteQuerySelector:y,buildMutationSelector:Q}=c;Y(t.util,{selectInvalidatedBy:s,selectCachedArgsForQuery:x});let{queryThunk:b,infiniteQueryThunk:E,mutationThunk:k,patchQueryData:T,updateQueryData:u,upsertQueryData:n,prefetch:r,buildMatchThunkActions:a}=rt({baseQuery:d,reducerPath:p,context:m,api:t,serializeQueryArgs:D,assertTagType:M,selectors:c,onSchemaFailure:R,catchSchemaFailure:S,skipSchemaValidation:o}),{reducer:f,actions:P}=ot({context:m,queryThunk:b,infiniteQueryThunk:E,mutationThunk:k,serializeQueryArgs:D,reducerPath:p,assertTagType:M,config:{refetchOnFocus:C,refetchOnReconnect:A,refetchOnMountOrArgChange:B,keepUnusedDataFor:h,reducerPath:p,invalidationBehavior:I}});Y(t.util,{patchQueryData:T,updateQueryData:u,upsertQueryData:n,prefetch:r,resetApiState:P.resetApiState,upsertQueryEntries:P.cacheEntriesUpserted}),Y(t.internalActions,P);let{middleware:O,actions:N}=Et({reducerPath:p,context:m,queryThunk:b,mutationThunk:k,infiniteQueryThunk:E,api:t,assertTagType:M,selectors:c});Y(t.util,N),Y(t,{reducer:f,middleware:O});let{buildInitiateQuery:F,buildInitiateInfiniteQuery:w,buildInitiateMutation:U,getRunningMutationThunk:H,getRunningMutationsThunk:W,getRunningQueriesThunk:j,getRunningQueryThunk:q}=tt({queryThunk:b,mutationThunk:k,infiniteQueryThunk:E,api:t,serializeQueryArgs:D,context:m});return Y(t.util,{getRunningMutationThunk:H,getRunningMutationsThunk:W,getRunningQueryThunk:q,getRunningQueriesThunk:j}),{name:Se,injectEndpoint(v,V){let _=t,K=_.endpoints[v]??={};ie(V)&&Y(K,{name:v,select:l(v,V),initiate:F(v,V)},a(b,v)),Ze(V)&&Y(K,{name:v,select:Q(),initiate:U(v)},a(k,v)),ae(V)&&Y(K,{name:v,select:y(v,V),initiate:w(v,V)},a(b,v))}}}});var It=Me(we());0&&(module.exports={NamedSchemaError,QueryStatus,_NEVER,buildCreateApi,copyWithStructuralSharing,coreModule,coreModuleName,createApi,defaultSerializeQueryArgs,fakeBaseQuery,fetchBaseQuery,retry,setupListeners,skipToken});
//# sourceMappingURL=rtk-query.production.min.cjs.map