var Ue=(p=>(p.uninitialized="uninitialized",p.pending="pending",p.fulfilled="fulfilled",p.rejected="rejected",p))(Ue||{});function Le(e){return{status:e,isUninitialized:e==="uninitialized",isLoading:e==="pending",isSuccess:e==="fulfilled",isError:e==="rejected"}}import{createAction as X,createSlice as ae,createSelector as Ye,createAsyncThunk as je,combineReducers as Xe,createNextState as Se,isAnyOf as oe,isAllOf as Pe,isAction as Ze,isPending as Ie,isRejected as fe,isFulfilled as W,isRejectedWithValue as me,isAsyncThunkAction as He,prepareAutoBatched as ge,SHOULD_AUTOBATCH as ke,isPlainObject as te,nanoid as Be}from"@reduxjs/toolkit";var et=te;function Me(e,n){if(e===n||!(et(e)&&et(n)||Array.isArray(e)&&Array.isArray(n)))return n;let y=Object.keys(n),m=Object.keys(e),p=y.length===m.length,x=Array.isArray(n)?[]:{};for(let T of y)x[T]=Me(e[T],n[T]),p&&(p=e[T]===x[T]);return p?e:x}function J(e){let n=0;for(let y in e)n++;return n}var _e=e=>[].concat(...e);function tt(e){return new RegExp("(^|:)//").test(e)}function nt(){return typeof document>"u"?!0:document.visibilityState!=="hidden"}function se(e){return e!=null}function rt(){return typeof navigator>"u"||navigator.onLine===void 0?!0:navigator.onLine}var Ct=e=>e.replace(/\/$/,""),Ft=e=>e.replace(/^\//,"");function it(e,n){if(!e)return n;if(!n)return e;if(tt(n))return n;let y=e.endsWith("/")||!n.startsWith("?")?"/":"";return e=Ct(e),n=Ft(n),`${e}${y}${n}`}function at(e,n,y){return e.has(n)?e.get(n):e.set(n,y).get(n)}var ot=(...e)=>fetch(...e),vt=e=>e.status>=200&&e.status<=299,Ot=e=>/ion\/(vnd\.api\+)?json/.test(e.get("content-type")||"");function st(e){if(!te(e))return e;let n={...e};for(let[y,m]of Object.entries(n))m===void 0&&delete n[y];return n}function Nt({baseUrl:e,prepareHeaders:n=h=>h,fetchFn:y=ot,paramsSerializer:m,isJsonContentType:p=Ot,jsonContentType:x="application/json",jsonReplacer:T,timeout:k,responseHandler:w,validateStatus:R,...P}={}){return typeof fetch>"u"&&y===ot&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),async(A,a,f)=>{let{getState:B,extra:d,endpoint:o,forced:S,type:c}=a,u,{url:g,headers:D=new Headers(P.headers),params:b=void 0,responseHandler:I=w??"json",validateStatus:Q=R??vt,timeout:s=k,...t}=typeof A=="string"?{url:A}:A,r,i=a.signal;s&&(r=new AbortController,a.signal.addEventListener("abort",r.abort),i=r.signal);let l={...P,signal:i,...t};D=new Headers(st(D)),l.headers=await n(D,{getState:B,arg:A,extra:d,endpoint:o,forced:S,type:c,extraOptions:f})||D;let E=N=>typeof N=="object"&&(te(N)||Array.isArray(N)||typeof N.toJSON=="function");if(!l.headers.has("content-type")&&E(l.body)&&l.headers.set("content-type",x),E(l.body)&&p(l.headers)&&(l.body=JSON.stringify(l.body,T)),b){let N=~g.indexOf("?")?"&":"?",F=m?m(b):new URLSearchParams(st(b));g+=N+F}g=it(e,g);let v=new Request(g,l);u={request:new Request(g,l)};let C,M=!1,K=r&&setTimeout(()=>{M=!0,r.abort()},s);try{C=await y(v)}catch(N){return{error:{status:M?"TIMEOUT_ERROR":"FETCH_ERROR",error:String(N)},meta:u}}finally{K&&clearTimeout(K),r?.signal.removeEventListener("abort",r.abort)}let j=C.clone();u.response=j;let z,L="";try{let N;if(await Promise.all([h(C,I).then(F=>z=F,F=>N=F),j.text().then(F=>L=F,()=>{})]),N)throw N}catch(N){return{error:{status:"PARSING_ERROR",originalStatus:C.status,data:L,error:String(N)},meta:u}}return Q(C,z)?{data:z,meta:u}:{error:{status:C.status,data:z},meta:u}};async function h(A,a){if(typeof a=="function")return a(A);if(a==="content-type"&&(a=p(A.headers)?"json":"text"),a==="json"){let f=await A.text();return f.length?JSON.parse(f):null}return A.text()}}var G=class{constructor(n,y=void 0){this.value=n;this.meta=y}};async function qt(e=0,n=5){let y=Math.min(e,n),m=~~((Math.random()+.4)*(300<<y));await new Promise(p=>setTimeout(x=>p(x),m))}function Kt(e,n){throw Object.assign(new G({error:e,meta:n}),{throwImmediately:!0})}var ut={},Ut=(e,n)=>async(y,m,p)=>{let x=[5,(n||ut).maxRetries,(p||ut).maxRetries].filter(P=>P!==void 0),[T]=x.slice(-1),w={maxRetries:T,backoff:qt,retryCondition:(P,h,{attempt:A})=>A<=T,...n,...p},R=0;for(;;)try{let P=await e(y,m,p);if(P.error)throw new G(P);return P}catch(P){if(R++,P.throwImmediately){if(P instanceof G)return P.value;throw P}if(P instanceof G&&!w.retryCondition(P.value.error,y,{attempt:R,baseQueryApi:m,extraOptions:p}))return P.value;await w.backoff(R,w.maxRetries)}},Lt=Object.assign(Ut,{fail:Kt});var ne=X("__rtkq/focused"),Qe=X("__rtkq/unfocused"),re=X("__rtkq/online"),Te=X("__rtkq/offline"),Ve=!1;function jt(e,n){function y(){let m=()=>e(ne()),p=()=>e(Qe()),x=()=>e(re()),T=()=>e(Te()),k=()=>{window.document.visibilityState==="visible"?m():p()};return Ve||typeof window<"u"&&window.addEventListener&&(window.addEventListener("visibilitychange",k,!1),window.addEventListener("focus",m,!1),window.addEventListener("online",x,!1),window.addEventListener("offline",T,!1),Ve=!0),()=>{window.removeEventListener("focus",m),window.removeEventListener("visibilitychange",k),window.removeEventListener("online",x),window.removeEventListener("offline",T),Ve=!1}}return n?n(e,{onFocus:ne,onFocusLost:Qe,onOffline:Te,onOnline:re}):y()}function ue(e){return e.type==="query"}function yt(e){return e.type==="mutation"}function ye(e){return e.type==="infinitequery"}function he(e){return ue(e)||ye(e)}function xe(e,n,y,m,p,x){return Ht(e)?e(n,y,m,p).filter(se).map(we).map(x):Array.isArray(e)?e.map(we).map(x):[]}function Ht(e){return typeof e=="function"}function we(e){return typeof e=="string"?{type:e}:e}import{isDraftable as Vt,produceWithPatches as zt}from"immer";import"@reduxjs/toolkit";function dt(e,n){return e.catch(n)}var Re=Symbol("forceQueryFn"),De=e=>typeof e[Re]=="function";function pt({serializeQueryArgs:e,queryThunk:n,infiniteQueryThunk:y,mutationThunk:m,api:p,context:x}){let T=new Map,k=new Map,{unsubscribeQueryResult:w,removeMutationResult:R,updateSubscriptionOptions:P}=p.internalActions;return{buildInitiateQuery:o,buildInitiateInfiniteQuery:S,buildInitiateMutation:c,getRunningQueryThunk:h,getRunningMutationThunk:A,getRunningQueriesThunk:a,getRunningMutationsThunk:f};function h(u,g){return D=>{let b=x.endpointDefinitions[u],I=e({queryArgs:g,endpointDefinition:b,endpointName:u});return T.get(D)?.[I]}}function A(u,g){return D=>k.get(D)?.[g]}function a(){return u=>Object.values(T.get(u)||{}).filter(se)}function f(){return u=>Object.values(k.get(u)||{}).filter(se)}function B(u){}function d(u,g){let D=(b,{subscribe:I=!0,forceRefetch:Q,subscriptionOptions:s,[Re]:t,...r}={})=>(i,l)=>{let E=e({queryArgs:b,endpointDefinition:g,endpointName:u}),v,O={...r,type:"query",subscribe:I,forceRefetch:Q,subscriptionOptions:s,endpointName:u,originalArgs:b,queryCacheKey:E,[Re]:t};if(ue(g))v=n(O);else{let{direction:H,initialPageParam:q}=r;v=y({...O,direction:H,initialPageParam:q})}let C=p.endpoints[u].select(b),M=i(v),K=C(l());let{requestId:j,abort:z}=M,L=K.requestId!==j,N=T.get(i)?.[E],F=()=>C(l()),_=Object.assign(t?M.then(F):L&&!N?Promise.resolve(K):Promise.all([N,M]).then(F),{arg:b,requestId:j,subscriptionOptions:s,queryCacheKey:E,abort:z,async unwrap(){let H=await _;if(H.isError)throw H.error;return H.data},refetch:()=>i(D(b,{subscribe:!1,forceRefetch:!0})),unsubscribe(){I&&i(w({queryCacheKey:E,requestId:j}))},updateSubscriptionOptions(H){_.subscriptionOptions=H,i(P({endpointName:u,requestId:j,queryCacheKey:E,options:H}))}});if(!N&&!L&&!t){let H=at(T,i,{});H[E]=_,_.then(()=>{delete H[E],J(H)||T.delete(i)})}return _};return D}function o(u,g){return d(u,g)}function S(u,g){return d(u,g)}function c(u){return(g,{track:D=!0,fixedCacheKey:b}={})=>(I,Q)=>{let s=m({type:"mutation",endpointName:u,originalArgs:g,track:D,fixedCacheKey:b}),t=I(s);let{requestId:r,abort:i,unwrap:l}=t,E=dt(t.unwrap().then(M=>({data:M})),M=>({error:M})),v=()=>{I(R({requestId:r,fixedCacheKey:b}))},O=Object.assign(E,{arg:t.arg,requestId:r,abort:i,unwrap:l,reset:v}),C=k.get(I)||{};return k.set(I,C),C[r]=O,O.then(()=>{delete C[r],J(C)||k.delete(I)}),b&&(C[b]=O,O.then(()=>{C[b]===O&&(delete C[b],J(C)||k.delete(I))})),O}}}import{SchemaError as _t}from"@standard-schema/utils";var Ae=class extends _t{constructor(y,m,p,x){super(y);this.value=m;this.schemaName=p;this._bqMeta=x}};async function ie(e,n,y,m){let p=await e["~standard"].validate(n);if(p.issues)throw new Ae(p.issues,n,y,m);return p.value}function Wt(e){return e}var be=(e={})=>({...e,[ke]:!0});function ct({reducerPath:e,baseQuery:n,context:{endpointDefinitions:y},serializeQueryArgs:m,api:p,assertTagType:x,selectors:T,onSchemaFailure:k,catchSchemaFailure:w,skipSchemaValidation:R}){let P=(t,r,i,l)=>(E,v)=>{let O=y[t],C=m({queryArgs:r,endpointDefinition:O,endpointName:t});if(E(p.internalActions.queryResultPatched({queryCacheKey:C,patches:i})),!l)return;let M=p.endpoints[t].select(r)(v()),K=xe(O.providesTags,M.data,void 0,r,{},x);E(p.internalActions.updateProvidedBy([{queryCacheKey:C,providedTags:K}]))};function h(t,r,i=0){let l=[r,...t];return i&&l.length>i?l.slice(0,-1):l}function A(t,r,i=0){let l=[...t,r];return i&&l.length>i?l.slice(1):l}let a=(t,r,i,l=!0)=>(E,v)=>{let C=p.endpoints[t].select(r)(v()),M={patches:[],inversePatches:[],undo:()=>E(p.util.patchQueryData(t,r,M.inversePatches,l))};if(C.status==="uninitialized")return M;let K;if("data"in C)if(Vt(C.data)){let[j,z,L]=zt(C.data,i);M.patches.push(...z),M.inversePatches.push(...L),K=j}else K=i(C.data),M.patches.push({op:"replace",path:[],value:K}),M.inversePatches.push({op:"replace",path:[],value:C.data});return M.patches.length===0||E(p.util.patchQueryData(t,r,M.patches,l)),M},f=(t,r,i)=>l=>l(p.endpoints[t].initiate(r,{subscribe:!1,forceRefetch:!0,[Re]:()=>({data:i})})),B=(t,r)=>t.query&&t[r]?t[r]:Wt,d=async(t,{signal:r,abort:i,rejectWithValue:l,fulfillWithValue:E,dispatch:v,getState:O,extra:C})=>{let M=y[t.endpointName],{metaSchema:K,skipSchemaValidation:j=R}=M;try{let z=B(M,"transformResponse"),L={signal:r,abort:i,dispatch:v,getState:O,extra:C,endpoint:t.endpointName,type:t.type,forced:t.type==="query"?o(t,O()):void 0,queryCacheKey:t.type==="query"?t.queryCacheKey:void 0},N=t.type==="query"?t[Re]:void 0,F,_=async(q,U,V,pe)=>{if(U==null&&q.pages.length)return Promise.resolve({data:q});let Z={queryArg:t.originalArgs,pageParam:U},ce=await H(Z),$=pe?h:A;return{data:{pages:$(q.pages,ce.data,V),pageParams:$(q.pageParams,U,V)},meta:ce.meta}};async function H(q){let U,{extraOptions:V,argSchema:pe,rawResponseSchema:Z,responseSchema:ce}=M;if(pe&&!j&&(q=await ie(pe,q,"argSchema",{})),N?U=N():M.query?U=await n(M.query(q),L,V):U=await M.queryFn(q,L,V,le=>n(le,L,V)),typeof process<"u",U.error)throw new G(U.error,U.meta);let{data:$}=U;Z&&!j&&($=await ie(Z,U.data,"rawResponseSchema",U.meta));let ee=await z($,U.meta,q);return ce&&!j&&(ee=await ie(ce,ee,"responseSchema",U.meta)),{...U,data:ee}}if(t.type==="query"&&"infiniteQueryOptions"in M){let{infiniteQueryOptions:q}=M,{maxPages:U=1/0}=q,V,pe={pages:[],pageParams:[]},Z=T.selectQueryEntry(O(),t.queryCacheKey)?.data,$=o(t,O())&&!t.direction||!Z?pe:Z;if("direction"in t&&t.direction&&$.pages.length){let ee=t.direction==="backward",Ee=(ee?ze:Ce)(q,$);V=await _($,Ee,U,ee)}else{let{initialPageParam:ee=q.initialPageParam}=t,le=Z?.pageParams??[],Ee=le[0]??ee,Je=le.length;V=await _($,Ee,U),N&&(V={data:V.data.pages[0]});for(let Ge=1;Ge<Je;Ge++){let wt=Ce(q,V.data);V=await _(V.data,wt,U)}}F=V}else F=await H(t.originalArgs);return K&&!j&&F.meta&&(F.meta=await ie(K,F.meta,"metaSchema",F.meta)),E(F.data,be({fulfilledTimeStamp:Date.now(),baseQueryMeta:F.meta}))}catch(z){let L=z;if(L instanceof G){let N=B(M,"transformErrorResponse"),{rawErrorResponseSchema:F,errorResponseSchema:_}=M,{value:H,meta:q}=L;try{F&&!j&&(H=await ie(F,H,"rawErrorResponseSchema",q)),K&&!j&&(q=await ie(K,q,"metaSchema",q));let U=await N(H,q,t.originalArgs);return _&&!j&&(U=await ie(_,U,"errorResponseSchema",q)),l(U,be({baseQueryMeta:q}))}catch(U){L=U}}try{if(L instanceof Ae){let N={endpoint:t.endpointName,arg:t.originalArgs,type:t.type,queryCacheKey:t.type==="query"?t.queryCacheKey:void 0};M.onSchemaFailure?.(L,N),k?.(L,N);let{catchSchemaFailure:F=w}=M;if(F)return l(F(L,N),be({baseQueryMeta:L._bqMeta}))}}catch(N){L=N}throw typeof process<"u",console.error(L),L}};function o(t,r){let i=T.selectQueryEntry(r,t.queryCacheKey),l=T.selectConfig(r).refetchOnMountOrArgChange,E=i?.fulfilledTimeStamp,v=t.forceRefetch??(t.subscribe&&l);return v?v===!0||(Number(new Date)-Number(E))/1e3>=v:!1}let S=()=>je(`${e}/executeQuery`,d,{getPendingMeta({arg:r}){let i=y[r.endpointName];return be({startedTimeStamp:Date.now(),...ye(i)?{direction:r.direction}:{}})},condition(r,{getState:i}){let l=i(),E=T.selectQueryEntry(l,r.queryCacheKey),v=E?.fulfilledTimeStamp,O=r.originalArgs,C=E?.originalArgs,M=y[r.endpointName],K=r.direction;return De(r)?!0:E?.status==="pending"?!1:o(r,l)||ue(M)&&M?.forceRefetch?.({currentArg:O,previousArg:C,endpointState:E,state:l})?!0:!(v&&!K)},dispatchConditionRejection:!0}),c=S(),u=S(),g=je(`${e}/executeMutation`,d,{getPendingMeta(){return be({startedTimeStamp:Date.now()})}}),D=t=>"force"in t,b=t=>"ifOlderThan"in t,I=(t,r,i)=>(l,E)=>{let v=D(i)&&i.force,O=b(i)&&i.ifOlderThan,C=(K=!0)=>{let j={forceRefetch:K,isPrefetch:!0};return p.endpoints[t].initiate(r,j)},M=p.endpoints[t].select(r)(E());if(v)l(C());else if(O){let K=M?.fulfilledTimeStamp;if(!K){l(C());return}(Number(new Date)-Number(new Date(K)))/1e3>=O&&l(C())}else l(C(!1))};function Q(t){return r=>r?.meta?.arg?.endpointName===t}function s(t,r){return{matchPending:Pe(Ie(t),Q(r)),matchFulfilled:Pe(W(t),Q(r)),matchRejected:Pe(fe(t),Q(r))}}return{queryThunk:c,mutationThunk:g,infiniteQueryThunk:u,prefetch:I,updateQueryData:a,upsertQueryData:f,patchQueryData:P,buildMatchThunkActions:s}}function Ce(e,{pages:n,pageParams:y}){let m=n.length-1;return e.getNextPageParam(n[m],n,y[m],y)}function ze(e,{pages:n,pageParams:y}){return e.getPreviousPageParam?.(n[0],n,y[0],y)}function Fe(e,n,y,m){return xe(y[e.meta.arg.endpointName][n],W(e)?e.payload:void 0,me(e)?e.payload:void 0,e.meta.arg.originalArgs,"baseQueryMeta"in e.meta?e.meta.baseQueryMeta:void 0,m)}import{isDraft as $t}from"immer";import{applyPatches as lt,original as Jt}from"immer";function ve(e,n,y){let m=e[n];m&&y(m)}function de(e){return("arg"in e?e.arg.fixedCacheKey:e.fixedCacheKey)??e.requestId}function ft(e,n,y){let m=e[de(n)];m&&y(m)}var Oe={};function mt({reducerPath:e,queryThunk:n,mutationThunk:y,serializeQueryArgs:m,context:{endpointDefinitions:p,apiUid:x,extractRehydrationInfo:T,hasRehydrationInfo:k},assertTagType:w,config:R}){let P=X(`${e}/resetApiState`);function h(Q,s,t,r){Q[s.queryCacheKey]??={status:"uninitialized",endpointName:s.endpointName},ve(Q,s.queryCacheKey,i=>{i.status="pending",i.requestId=t&&i.requestId?i.requestId:r.requestId,s.originalArgs!==void 0&&(i.originalArgs=s.originalArgs),i.startedTimeStamp=r.startedTimeStamp;let l=p[r.arg.endpointName];ye(l)&&"direction"in s&&(i.direction=s.direction)})}function A(Q,s,t,r){ve(Q,s.arg.queryCacheKey,i=>{if(i.requestId!==s.requestId&&!r)return;let{merge:l}=p[s.arg.endpointName];if(i.status="fulfilled",l)if(i.data!==void 0){let{fulfilledTimeStamp:E,arg:v,baseQueryMeta:O,requestId:C}=s,M=Se(i.data,K=>l(K,t,{arg:v.originalArgs,baseQueryMeta:O,fulfilledTimeStamp:E,requestId:C}));i.data=M}else i.data=t;else i.data=p[s.arg.endpointName].structuralSharing??!0?Me($t(i.data)?Jt(i.data):i.data,t):t;delete i.error,i.fulfilledTimeStamp=s.fulfilledTimeStamp})}let a=ae({name:`${e}/queries`,initialState:Oe,reducers:{removeQueryResult:{reducer(Q,{payload:{queryCacheKey:s}}){delete Q[s]},prepare:ge()},cacheEntriesUpserted:{reducer(Q,s){for(let t of s.payload){let{queryDescription:r,value:i}=t;h(Q,r,!0,{arg:r,requestId:s.meta.requestId,startedTimeStamp:s.meta.timestamp}),A(Q,{arg:r,requestId:s.meta.requestId,fulfilledTimeStamp:s.meta.timestamp,baseQueryMeta:{}},i,!0)}},prepare:Q=>({payload:Q.map(r=>{let{endpointName:i,arg:l,value:E}=r,v=p[i];return{queryDescription:{type:"query",endpointName:i,originalArgs:r.arg,queryCacheKey:m({queryArgs:l,endpointDefinition:v,endpointName:i})},value:E}}),meta:{[ke]:!0,requestId:Be(),timestamp:Date.now()}})},queryResultPatched:{reducer(Q,{payload:{queryCacheKey:s,patches:t}}){ve(Q,s,r=>{r.data=lt(r.data,t.concat())})},prepare:ge()}},extraReducers(Q){Q.addCase(n.pending,(s,{meta:t,meta:{arg:r}})=>{let i=De(r);h(s,r,i,t)}).addCase(n.fulfilled,(s,{meta:t,payload:r})=>{let i=De(t.arg);A(s,t,r,i)}).addCase(n.rejected,(s,{meta:{condition:t,arg:r,requestId:i},error:l,payload:E})=>{ve(s,r.queryCacheKey,v=>{if(!t){if(v.requestId!==i)return;v.status="rejected",v.error=E??l}})}).addMatcher(k,(s,t)=>{let{queries:r}=T(t);for(let[i,l]of Object.entries(r))(l?.status==="fulfilled"||l?.status==="rejected")&&(s[i]=l)})}}),f=ae({name:`${e}/mutations`,initialState:Oe,reducers:{removeMutationResult:{reducer(Q,{payload:s}){let t=de(s);t in Q&&delete Q[t]},prepare:ge()}},extraReducers(Q){Q.addCase(y.pending,(s,{meta:t,meta:{requestId:r,arg:i,startedTimeStamp:l}})=>{i.track&&(s[de(t)]={requestId:r,status:"pending",endpointName:i.endpointName,startedTimeStamp:l})}).addCase(y.fulfilled,(s,{payload:t,meta:r})=>{r.arg.track&&ft(s,r,i=>{i.requestId===r.requestId&&(i.status="fulfilled",i.data=t,i.fulfilledTimeStamp=r.fulfilledTimeStamp)})}).addCase(y.rejected,(s,{payload:t,error:r,meta:i})=>{i.arg.track&&ft(s,i,l=>{l.requestId===i.requestId&&(l.status="rejected",l.error=t??r)})}).addMatcher(k,(s,t)=>{let{mutations:r}=T(t);for(let[i,l]of Object.entries(r))(l?.status==="fulfilled"||l?.status==="rejected")&&i!==l?.requestId&&(s[i]=l)})}}),B={tags:{},keys:{}},d=ae({name:`${e}/invalidation`,initialState:B,reducers:{updateProvidedBy:{reducer(Q,s){for(let{queryCacheKey:t,providedTags:r}of s.payload){o(Q,t);for(let{type:i,id:l}of r){let E=(Q.tags[i]??={})[l||"__internal_without_id"]??=[];E.includes(t)||E.push(t)}Q.keys[t]=r}},prepare:ge()}},extraReducers(Q){Q.addCase(a.actions.removeQueryResult,(s,{payload:{queryCacheKey:t}})=>{o(s,t)}).addMatcher(k,(s,t)=>{let{provided:r}=T(t);for(let[i,l]of Object.entries(r))for(let[E,v]of Object.entries(l)){let O=(s.tags[i]??={})[E||"__internal_without_id"]??=[];for(let C of v)O.includes(C)||O.push(C)}}).addMatcher(oe(W(n),me(n)),(s,t)=>{S(s,[t])}).addMatcher(a.actions.cacheEntriesUpserted.match,(s,t)=>{let r=t.payload.map(({queryDescription:i,value:l})=>({type:"UNKNOWN",payload:l,meta:{requestStatus:"fulfilled",requestId:"UNKNOWN",arg:i}}));S(s,r)})}});function o(Q,s){let t=Q.keys[s]??[];for(let r of t){let i=r.type,l=r.id??"__internal_without_id",E=Q.tags[i]?.[l];E&&(Q.tags[i][l]=E.filter(v=>v!==s))}delete Q.keys[s]}function S(Q,s){let t=s.map(r=>{let i=Fe(r,"providesTags",p,w),{queryCacheKey:l}=r.meta.arg;return{queryCacheKey:l,providedTags:i}});d.caseReducers.updateProvidedBy(Q,d.actions.updateProvidedBy(t))}let c=ae({name:`${e}/subscriptions`,initialState:Oe,reducers:{updateSubscriptionOptions(Q,s){},unsubscribeQueryResult(Q,s){},internal_getRTKQSubscriptions(){}}}),u=ae({name:`${e}/internalSubscriptions`,initialState:Oe,reducers:{subscriptionsUpdated:{reducer(Q,s){return lt(Q,s.payload)},prepare:ge()}}}),g=ae({name:`${e}/config`,initialState:{online:rt(),focused:nt(),middlewareRegistered:!1,...R},reducers:{middlewareRegistered(Q,{payload:s}){Q.middlewareRegistered=Q.middlewareRegistered==="conflict"||x!==s?"conflict":!0}},extraReducers:Q=>{Q.addCase(re,s=>{s.online=!0}).addCase(Te,s=>{s.online=!1}).addCase(ne,s=>{s.focused=!0}).addCase(Qe,s=>{s.focused=!1}).addMatcher(k,s=>({...s}))}}),D=Xe({queries:a.reducer,mutations:f.reducer,provided:d.reducer,subscriptions:u.reducer,config:g.reducer}),b=(Q,s)=>D(P.match(s)?void 0:Q,s),I={...g.actions,...a.actions,...c.actions,...u.actions,...f.actions,...d.actions,resetApiState:P};return{reducer:b,actions:I}}var Ne=Symbol.for("RTKQ/skipToken"),Tt={status:"uninitialized"},gt=Se(Tt,()=>{}),Qt=Se(Tt,()=>{});function ht({serializeQueryArgs:e,reducerPath:n,createSelector:y}){let m=c=>gt,p=c=>Qt;return{buildQuerySelector:A,buildInfiniteQuerySelector:a,buildMutationSelector:f,selectInvalidatedBy:B,selectCachedArgsForQuery:d,selectApiState:T,selectQueries:k,selectMutations:R,selectQueryEntry:w,selectConfig:P};function x(c){return{...c,...Le(c.status)}}function T(c){return c[n]}function k(c){return T(c)?.queries}function w(c,u){return k(c)?.[u]}function R(c){return T(c)?.mutations}function P(c){return T(c)?.config}function h(c,u,g){return D=>{if(D===Ne)return y(m,g);let b=e({queryArgs:D,endpointDefinition:u,endpointName:c});return y(Q=>w(Q,b)??gt,g)}}function A(c,u){return h(c,u,x)}function a(c,u){let{infiniteQueryOptions:g}=u;function D(b){let I={...b,...Le(b.status)},{isLoading:Q,isError:s,direction:t}=I,r=t==="forward",i=t==="backward";return{...I,hasNextPage:o(g,I.data),hasPreviousPage:S(g,I.data),isFetchingNextPage:Q&&r,isFetchingPreviousPage:Q&&i,isFetchNextPageError:s&&r,isFetchPreviousPageError:s&&i}}return h(c,u,D)}function f(){return c=>{let u;return typeof c=="object"?u=de(c)??Ne:u=c,y(u===Ne?p:b=>T(b)?.mutations?.[u]??Qt,x)}}function B(c,u){let g=c[n],D=new Set;for(let b of u.filter(se).map(we)){let I=g.provided.tags[b.type];if(!I)continue;let Q=(b.id!==void 0?I[b.id]:_e(Object.values(I)))??[];for(let s of Q)D.add(s)}return _e(Array.from(D.values()).map(b=>{let I=g.queries[b];return I?[{queryCacheKey:b,endpointName:I.endpointName,originalArgs:I.originalArgs}]:[]}))}function d(c,u){return Object.values(k(c)).filter(g=>g?.endpointName===u&&g.status!=="uninitialized").map(g=>g.originalArgs)}function o(c,u){return u?Ce(c,u)!=null:!1}function S(c,u){return!u||!c.getPreviousPageParam?!1:ze(c,u)!=null}}import{formatProdErrorMessage as Gt}from"@reduxjs/toolkit";var Rt=WeakMap?new WeakMap:void 0,qe=({endpointName:e,queryArgs:n})=>{let y="",m=Rt?.get(n);if(typeof m=="string")y=m;else{let p=JSON.stringify(n,(x,T)=>(T=typeof T=="bigint"?{$bigint:T.toString()}:T,T=te(T)?Object.keys(T).sort().reduce((k,w)=>(k[w]=T[w],k),{}):T,T));te(n)&&Rt?.set(n,p),y=p}return`${e}(${y})`};import{weakMapMemoize as At}from"reselect";function We(...e){return function(y){let m=At(R=>y.extractRehydrationInfo?.(R,{reducerPath:y.reducerPath??"api"})),p={reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1,invalidationBehavior:"delayed",...y,extractRehydrationInfo:m,serializeQueryArgs(R){let P=qe;if("serializeQueryArgs"in R.endpointDefinition){let h=R.endpointDefinition.serializeQueryArgs;P=A=>{let a=h(A);return typeof a=="string"?a:qe({...A,queryArgs:a})}}else y.serializeQueryArgs&&(P=y.serializeQueryArgs);return P(R)},tagTypes:[...y.tagTypes||[]]},x={endpointDefinitions:{},batch(R){R()},apiUid:Be(),extractRehydrationInfo:m,hasRehydrationInfo:At(R=>m(R)!=null)},T={injectEndpoints:w,enhanceEndpoints({addTagTypes:R,endpoints:P}){if(R)for(let h of R)p.tagTypes.includes(h)||p.tagTypes.push(h);if(P)for(let[h,A]of Object.entries(P))typeof A=="function"?A(x.endpointDefinitions[h]):Object.assign(x.endpointDefinitions[h]||{},A);return T}},k=e.map(R=>R.init(T,p,x));function w(R){let P=R.endpoints({query:h=>({...h,type:"query"}),mutation:h=>({...h,type:"mutation"}),infiniteQuery:h=>({...h,type:"infinitequery"})});for(let[h,A]of Object.entries(P)){if(R.overrideExisting!==!0&&h in x.endpointDefinitions){if(R.overrideExisting==="throw")throw new Error(Gt(39));typeof process<"u";continue}typeof process<"u",x.endpointDefinitions[h]=A;for(let a of k)a.injectEndpoint(h,A)}return T}return T.injectEndpoints({endpoints:y.endpoints})}}import{formatProdErrorMessage as Yt}from"@reduxjs/toolkit";var Xt=Symbol();function Zt(){return function(){throw new Error(Yt(33))}}import{enablePatches as rn}from"immer";function Y(e,...n){return Object.assign(e,...n)}import{produceWithPatches as en}from"immer";var St=({api:e,queryThunk:n,internalState:y})=>{let m=`${e.reducerPath}/subscriptions`,p=null,x=null,{updateSubscriptionOptions:T,unsubscribeQueryResult:k}=e.internalActions,w=(a,f)=>{if(T.match(f)){let{queryCacheKey:d,requestId:o,options:S}=f.payload;return a?.[d]?.[o]&&(a[d][o]=S),!0}if(k.match(f)){let{queryCacheKey:d,requestId:o}=f.payload;return a[d]&&delete a[d][o],!0}if(e.internalActions.removeQueryResult.match(f))return delete a[f.payload.queryCacheKey],!0;if(n.pending.match(f)){let{meta:{arg:d,requestId:o}}=f,S=a[d.queryCacheKey]??={};return S[`${o}_running`]={},d.subscribe&&(S[o]=d.subscriptionOptions??S[o]??{}),!0}let B=!1;if(n.fulfilled.match(f)||n.rejected.match(f)){let d=a[f.meta.arg.queryCacheKey]||{},o=`${f.meta.requestId}_running`;B||=!!d[o],delete d[o]}if(n.rejected.match(f)){let{meta:{condition:d,arg:o,requestId:S}}=f;if(d&&o.subscribe){let c=a[o.queryCacheKey]??={};c[S]=o.subscriptionOptions??c[S]??{},B=!0}}return B},R=()=>y.currentSubscriptions,A={getSubscriptions:R,getSubscriptionCount:a=>{let B=R()[a]??{};return J(B)},isRequestSubscribed:(a,f)=>!!R()?.[a]?.[f]};return(a,f)=>{if(p||(p=JSON.parse(JSON.stringify(y.currentSubscriptions))),e.util.resetApiState.match(a))return p=y.currentSubscriptions={},x=null,[!0,!1];if(e.internalActions.internal_getRTKQSubscriptions.match(a))return[!1,A];let B=w(y.currentSubscriptions,a),d=!0;if(B){x||(x=setTimeout(()=>{let c=JSON.parse(JSON.stringify(y.currentSubscriptions)),[,u]=en(p,()=>c);f.next(e.internalActions.subscriptionsUpdated(u)),p=c,x=null},500));let o=typeof a.type=="string"&&!!a.type.startsWith(m),S=n.rejected.match(a)&&a.meta.condition&&!!a.meta.arg.subscribe;d=!o&&!S}return[d,!1]}};function tn(e){for(let n in e)return!1;return!0}var nn=2147483647/1e3-1,xt=({reducerPath:e,api:n,queryThunk:y,context:m,internalState:p,selectors:{selectQueryEntry:x,selectConfig:T}})=>{let{removeQueryResult:k,unsubscribeQueryResult:w,cacheEntriesUpserted:R}=n.internalActions,P=oe(w.match,y.fulfilled,y.rejected,R.match);function h(d){let o=p.currentSubscriptions[d];return!!o&&!tn(o)}let A={},a=(d,o,S)=>{let c=o.getState(),u=T(c);if(P(d)){let g;if(R.match(d))g=d.payload.map(D=>D.queryDescription.queryCacheKey);else{let{queryCacheKey:D}=w.match(d)?d.payload:d.meta.arg;g=[D]}f(g,o,u)}if(n.util.resetApiState.match(d))for(let[g,D]of Object.entries(A))D&&clearTimeout(D),delete A[g];if(m.hasRehydrationInfo(d)){let{queries:g}=m.extractRehydrationInfo(d);f(Object.keys(g),o,u)}};function f(d,o,S){let c=o.getState();for(let u of d){let g=x(c,u);B(u,g?.endpointName,o,S)}}function B(d,o,S,c){let g=m.endpointDefinitions[o]?.keepUnusedDataFor??c.keepUnusedDataFor;if(g===1/0)return;let D=Math.max(0,Math.min(g,nn));if(!h(d)){let b=A[d];b&&clearTimeout(b),A[d]=setTimeout(()=>{h(d)||S.dispatch(k({queryCacheKey:d})),delete A[d]},D*1e3)}}return a};var Dt=new Error("Promise never resolved before cacheEntryRemoved."),bt=({api:e,reducerPath:n,context:y,queryThunk:m,mutationThunk:p,internalState:x,selectors:{selectQueryEntry:T,selectApiState:k}})=>{let w=He(m),R=He(p),P=W(m,p),h={};function A(o,S,c){let u=h[o];u?.valueResolved&&(u.valueResolved({data:S,meta:c}),delete u.valueResolved)}function a(o){let S=h[o];S&&(delete h[o],S.cacheEntryRemoved())}let f=(o,S,c)=>{let u=B(o);function g(D,b,I,Q){let s=T(c,b),t=T(S.getState(),b);!s&&t&&d(D,Q,b,S,I)}if(m.pending.match(o))g(o.meta.arg.endpointName,u,o.meta.requestId,o.meta.arg.originalArgs);else if(e.internalActions.cacheEntriesUpserted.match(o))for(let{queryDescription:D,value:b}of o.payload){let{endpointName:I,originalArgs:Q,queryCacheKey:s}=D;g(I,s,o.meta.requestId,Q),A(s,b,{})}else if(p.pending.match(o))S.getState()[n].mutations[u]&&d(o.meta.arg.endpointName,o.meta.arg.originalArgs,u,S,o.meta.requestId);else if(P(o))A(u,o.payload,o.meta.baseQueryMeta);else if(e.internalActions.removeQueryResult.match(o)||e.internalActions.removeMutationResult.match(o))a(u);else if(e.util.resetApiState.match(o))for(let D of Object.keys(h))a(D)};function B(o){return w(o)?o.meta.arg.queryCacheKey:R(o)?o.meta.arg.fixedCacheKey??o.meta.requestId:e.internalActions.removeQueryResult.match(o)?o.payload.queryCacheKey:e.internalActions.removeMutationResult.match(o)?de(o.payload):""}function d(o,S,c,u,g){let D=y.endpointDefinitions[o],b=D?.onCacheEntryAdded;if(!b)return;let I={},Q=new Promise(E=>{I.cacheEntryRemoved=E}),s=Promise.race([new Promise(E=>{I.valueResolved=E}),Q.then(()=>{throw Dt})]);s.catch(()=>{}),h[c]=I;let t=e.endpoints[o].select(he(D)?S:c),r=u.dispatch((E,v,O)=>O),i={...u,getCacheEntry:()=>t(u.getState()),requestId:g,extra:r,updateCachedData:he(D)?E=>u.dispatch(e.util.updateQueryData(o,S,E)):void 0,cacheDataLoaded:s,cacheEntryRemoved:Q},l=b(S,i);Promise.resolve(l).catch(E=>{if(E!==Dt)throw E})}return f};var Et=({api:e,context:{apiUid:n},reducerPath:y})=>(m,p)=>{e.util.resetApiState.match(m)&&p.dispatch(e.internalActions.middlewareRegistered(n)),typeof process<"u"};var Pt=({reducerPath:e,context:n,context:{endpointDefinitions:y},mutationThunk:m,queryThunk:p,api:x,assertTagType:T,refetchQuery:k,internalState:w})=>{let{removeQueryResult:R}=x.internalActions,P=oe(W(m),me(m)),h=oe(W(m,p),fe(m,p)),A=[],a=(d,o)=>{P(d)?B(Fe(d,"invalidatesTags",y,T),o):h(d)?B([],o):x.util.invalidateTags.match(d)&&B(xe(d.payload,void 0,void 0,void 0,void 0,T),o)};function f(d){let{queries:o,mutations:S}=d;for(let c of[o,S])for(let u in c)if(c[u]?.status==="pending")return!0;return!1}function B(d,o){let S=o.getState(),c=S[e];if(A.push(...d),c.config.invalidationBehavior==="delayed"&&f(c))return;let u=A;if(A=[],u.length===0)return;let g=x.util.selectInvalidatedBy(S,u);n.batch(()=>{let D=Array.from(g.values());for(let{queryCacheKey:b}of D){let I=c.queries[b],Q=w.currentSubscriptions[b]??{};I&&(J(Q)===0?o.dispatch(R({queryCacheKey:b})):I.status!=="uninitialized"&&o.dispatch(k(I)))}})}return a};var It=({reducerPath:e,queryThunk:n,api:y,refetchQuery:m,internalState:p})=>{let x={},T=(a,f)=>{(y.internalActions.updateSubscriptionOptions.match(a)||y.internalActions.unsubscribeQueryResult.match(a))&&R(a.payload,f),(n.pending.match(a)||n.rejected.match(a)&&a.meta.condition)&&R(a.meta.arg,f),(n.fulfilled.match(a)||n.rejected.match(a)&&!a.meta.condition)&&w(a.meta.arg,f),y.util.resetApiState.match(a)&&h()};function k(a,f){let d=f.getState()[e].queries[a],o=p.currentSubscriptions[a];if(!(!d||d.status==="uninitialized"))return o}function w({queryCacheKey:a},f){let B=f.getState()[e],d=B.queries[a],o=p.currentSubscriptions[a];if(!d||d.status==="uninitialized")return;let{lowestPollingInterval:S,skipPollingIfUnfocused:c}=A(o);if(!Number.isFinite(S))return;let u=x[a];u?.timeout&&(clearTimeout(u.timeout),u.timeout=void 0);let g=Date.now()+S;x[a]={nextPollTimestamp:g,pollingInterval:S,timeout:setTimeout(()=>{(B.config.focused||!c)&&f.dispatch(m(d)),w({queryCacheKey:a},f)},S)}}function R({queryCacheKey:a},f){let d=f.getState()[e].queries[a],o=p.currentSubscriptions[a];if(!d||d.status==="uninitialized")return;let{lowestPollingInterval:S}=A(o);if(!Number.isFinite(S)){P(a);return}let c=x[a],u=Date.now()+S;(!c||u<c.nextPollTimestamp)&&w({queryCacheKey:a},f)}function P(a){let f=x[a];f?.timeout&&clearTimeout(f.timeout),delete x[a]}function h(){for(let a of Object.keys(x))P(a)}function A(a={}){let f=!1,B=Number.POSITIVE_INFINITY;for(let d in a)a[d].pollingInterval&&(B=Math.min(a[d].pollingInterval,B),f=a[d].skipPollingIfUnfocused||f);return{lowestPollingInterval:B,skipPollingIfUnfocused:f}}return T};var kt=({api:e,context:n,queryThunk:y,mutationThunk:m})=>{let p=Ie(y,m),x=fe(y,m),T=W(y,m),k={};return(R,P)=>{if(p(R)){let{requestId:h,arg:{endpointName:A,originalArgs:a}}=R.meta,f=n.endpointDefinitions[A],B=f?.onQueryStarted;if(B){let d={},o=new Promise((g,D)=>{d.resolve=g,d.reject=D});o.catch(()=>{}),k[h]=d;let S=e.endpoints[A].select(he(f)?a:h),c=P.dispatch((g,D,b)=>b),u={...P,getCacheEntry:()=>S(P.getState()),requestId:h,extra:c,updateCachedData:he(f)?g=>P.dispatch(e.util.updateQueryData(A,a,g)):void 0,queryFulfilled:o};B(a,u)}}else if(T(R)){let{requestId:h,baseQueryMeta:A}=R.meta;k[h]?.resolve({data:R.payload,meta:A}),delete k[h]}else if(x(R)){let{requestId:h,rejectedWithValue:A,baseQueryMeta:a}=R.meta;k[h]?.reject({error:R.payload??R.error,isUnhandledError:!A,meta:a}),delete k[h]}}};var Bt=({reducerPath:e,context:n,api:y,refetchQuery:m,internalState:p})=>{let{removeQueryResult:x}=y.internalActions,T=(w,R)=>{ne.match(w)&&k(R,"refetchOnFocus"),re.match(w)&&k(R,"refetchOnReconnect")};function k(w,R){let P=w.getState()[e],h=P.queries,A=p.currentSubscriptions;n.batch(()=>{for(let a of Object.keys(A)){let f=h[a],B=A[a];if(!B||!f)continue;(Object.values(B).some(o=>o[R]===!0)||Object.values(B).every(o=>o[R]===void 0)&&P.config[R])&&(J(B)===0?w.dispatch(x({queryCacheKey:a})):f.status!=="uninitialized"&&w.dispatch(m(f)))}})}return T};function Mt(e){let{reducerPath:n,queryThunk:y,api:m,context:p}=e,{apiUid:x}=p,T={invalidateTags:X(`${n}/invalidateTags`)},k=h=>h.type.startsWith(`${n}/`),w=[Et,xt,Pt,It,bt,kt];return{middleware:h=>{let A=!1,f={...e,internalState:{currentSubscriptions:{}},refetchQuery:P,isThisApiSliceAction:k},B=w.map(S=>S(f)),d=St(f),o=Bt(f);return S=>c=>{if(!Ze(c))return S(c);A||(A=!0,h.dispatch(m.internalActions.middlewareRegistered(x)));let u={...h,next:S},g=h.getState(),[D,b]=d(c,u,g),I;if(D?I=S(c):I=b,h.getState()[n]&&(o(c,u,g),k(c)||p.hasRehydrationInfo(c)))for(let Q of B)Q(c,u,g);return I}},actions:T};function P(h){return e.api.endpoints[h.endpointName].initiate(h.originalArgs,{subscribe:!1,forceRefetch:!0})}}var Ke=Symbol(),$e=({createSelector:e=Ye}={})=>({name:Ke,init(n,{baseQuery:y,tagTypes:m,reducerPath:p,serializeQueryArgs:x,keepUnusedDataFor:T,refetchOnMountOrArgChange:k,refetchOnFocus:w,refetchOnReconnect:R,invalidationBehavior:P,onSchemaFailure:h,catchSchemaFailure:A,skipSchemaValidation:a},f){rn();let B=F=>(typeof process<"u",F);Object.assign(n,{reducerPath:p,endpoints:{},internalActions:{onOnline:re,onOffline:Te,onFocus:ne,onFocusLost:Qe},util:{}});let d=ht({serializeQueryArgs:x,reducerPath:p,createSelector:e}),{selectInvalidatedBy:o,selectCachedArgsForQuery:S,buildQuerySelector:c,buildInfiniteQuerySelector:u,buildMutationSelector:g}=d;Y(n.util,{selectInvalidatedBy:o,selectCachedArgsForQuery:S});let{queryThunk:D,infiniteQueryThunk:b,mutationThunk:I,patchQueryData:Q,updateQueryData:s,upsertQueryData:t,prefetch:r,buildMatchThunkActions:i}=ct({baseQuery:y,reducerPath:p,context:f,api:n,serializeQueryArgs:x,assertTagType:B,selectors:d,onSchemaFailure:h,catchSchemaFailure:A,skipSchemaValidation:a}),{reducer:l,actions:E}=mt({context:f,queryThunk:D,infiniteQueryThunk:b,mutationThunk:I,serializeQueryArgs:x,reducerPath:p,assertTagType:B,config:{refetchOnFocus:w,refetchOnReconnect:R,refetchOnMountOrArgChange:k,keepUnusedDataFor:T,reducerPath:p,invalidationBehavior:P}});Y(n.util,{patchQueryData:Q,updateQueryData:s,upsertQueryData:t,prefetch:r,resetApiState:E.resetApiState,upsertQueryEntries:E.cacheEntriesUpserted}),Y(n.internalActions,E);let{middleware:v,actions:O}=Mt({reducerPath:p,context:f,queryThunk:D,mutationThunk:I,infiniteQueryThunk:b,api:n,assertTagType:B,selectors:d});Y(n.util,O),Y(n,{reducer:l,middleware:v});let{buildInitiateQuery:C,buildInitiateInfiniteQuery:M,buildInitiateMutation:K,getRunningMutationThunk:j,getRunningMutationsThunk:z,getRunningQueriesThunk:L,getRunningQueryThunk:N}=pt({queryThunk:D,mutationThunk:I,infiniteQueryThunk:b,api:n,serializeQueryArgs:x,context:f});return Y(n.util,{getRunningMutationThunk:j,getRunningMutationsThunk:z,getRunningQueryThunk:N,getRunningQueriesThunk:L}),{name:Ke,injectEndpoint(F,_){let H=n,q=H.endpoints[F]??={};ue(_)&&Y(q,{name:F,select:c(F,_),initiate:C(F,_)},i(D,F)),yt(_)&&Y(q,{name:F,select:g(),initiate:K(F)},i(I,F)),ye(_)&&Y(q,{name:F,select:u(F,_),initiate:M(F,_)},i(D,F))}}}});var an=We($e());export{Ae as NamedSchemaError,Ue as QueryStatus,Xt as _NEVER,We as buildCreateApi,Me as copyWithStructuralSharing,$e as coreModule,Ke as coreModuleName,an as createApi,qe as defaultSerializeQueryArgs,Zt as fakeBaseQuery,Nt as fetchBaseQuery,Lt as retry,jt as setupListeners,Ne as skipToken};
//# sourceMappingURL=rtk-query.browser.mjs.map