{"version": 3, "sources": ["../../../src/query/react/index.ts", "../../../src/query/react/module.ts", "../../../src/query/utils/capitalize.ts", "../../../src/query/utils/countObjectKeys.ts", "../../../src/query/endpointDefinitions.ts", "../../../src/query/tsHelpers.ts", "../../../src/query/react/buildHooks.ts", "../../../src/query/react/constants.ts", "../../../src/query/react/useSerializedStableValue.ts", "../../../src/query/react/useShallowStableValue.ts", "../../../src/query/react/ApiProvider.tsx"], "sourcesContent": ["// This must remain here so that the `mangleErrors.cjs` build script\n// does not have to import this into each source file it rewrites.\nimport { formatProdErrorMessage } from '@reduxjs/toolkit';\nimport { buildCreateApi, coreModule } from '@reduxjs/toolkit/query';\nimport { reactHooksModule, reactHooksModuleName } from './module';\nexport * from '@reduxjs/toolkit/query';\nexport { ApiProvider } from './ApiProvider';\nconst createApi = /* @__PURE__ */buildCreateApi(coreModule(), reactHooksModule());\nexport type { TypedUseMutationResult, TypedUseQueryHookResult, TypedUseQueryStateResult, TypedUseQuerySubscriptionResult, TypedLazyQueryTrigger, TypedUseLazyQuery, TypedUseMutation, TypedMutationTrigger, TypedQueryStateSelector, TypedUseQueryState, TypedUseQuery, TypedUseQuerySubscription, TypedUseLazyQuerySubscription, TypedUseQueryStateOptions, TypedUseLazyQueryStateResult, TypedUseInfiniteQuery, TypedUseInfiniteQueryHookResult, TypedUseInfiniteQueryStateResult, TypedUseInfiniteQuerySubscriptionResult, TypedUseInfiniteQueryStateOptions, TypedInfiniteQueryStateSelector, TypedUseInfiniteQuerySubscription, TypedUseInfiniteQueryState, TypedLazyInfiniteQueryTrigger } from './buildHooks';\nexport { UNINITIALIZED_VALUE } from './constants';\nexport { createApi, reactHooksModule, reactHooksModuleName };", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport type { Api, BaseQueryFn, EndpointDefinitions, InfiniteQueryDefinition, Module, MutationDefinition, PrefetchOptions, QueryArgFrom, QueryDefinition, QueryKeys } from '@reduxjs/toolkit/query';\nimport { batch as rrBatch, useDispatch as rrUseDispatch, useSelector as rrUseSelector, useStore as rrUseStore } from 'react-redux';\nimport { createSelector as _createSelector } from 'reselect';\nimport { isInfiniteQueryDefinition, isMutationDefinition, isQueryDefinition } from '../endpointDefinitions';\nimport { safeAssign } from '../tsHelpers';\nimport { capitalize, countObjectKeys } from '../utils';\nimport type { InfiniteQueryHooks, MutationHooks, QueryHooks } from './buildHooks';\nimport { buildHooks } from './buildHooks';\nimport type { HooksWithUniqueNames } from './namedHooks';\nexport const reactHooksModuleName = /* @__PURE__ */Symbol();\nexport type ReactHooksModule = typeof reactHooksModuleName;\ndeclare module '@reduxjs/toolkit/query' {\n  export interface ApiModules<\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  BaseQuery extends BaseQueryFn, Definitions extends EndpointDefinitions,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  ReducerPath extends string,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  TagTypes extends string> {\n    [reactHooksModuleName]: {\n      /**\n       *  Endpoints based on the input endpoints provided to `createApi`, containing `select`, `hooks` and `action matchers`.\n       */\n      endpoints: { [K in keyof Definitions]: Definitions[K] extends QueryDefinition<any, any, any, any, any> ? QueryHooks<Definitions[K]> : Definitions[K] extends MutationDefinition<any, any, any, any, any> ? MutationHooks<Definitions[K]> : Definitions[K] extends InfiniteQueryDefinition<any, any, any, any, any> ? InfiniteQueryHooks<Definitions[K]> : never };\n      /**\n       * A hook that accepts a string endpoint name, and provides a callback that when called, pre-fetches the data for that endpoint.\n       */\n      usePrefetch<EndpointName extends QueryKeys<Definitions>>(endpointName: EndpointName, options?: PrefetchOptions): (arg: QueryArgFrom<Definitions[EndpointName]>, options?: PrefetchOptions) => void;\n    } & HooksWithUniqueNames<Definitions>;\n  }\n}\ntype RR = typeof import('react-redux');\nexport interface ReactHooksModuleOptions {\n  /**\n   * The hooks from React Redux to be used\n   */\n  hooks?: {\n    /**\n     * The version of the `useDispatch` hook to be used\n     */\n    useDispatch: RR['useDispatch'];\n    /**\n     * The version of the `useSelector` hook to be used\n     */\n    useSelector: RR['useSelector'];\n    /**\n     * The version of the `useStore` hook to be used\n     */\n    useStore: RR['useStore'];\n  };\n  /**\n   * The version of the `batchedUpdates` function to be used\n   */\n  batch?: RR['batch'];\n  /**\n   * Enables performing asynchronous tasks immediately within a render.\n   *\n   * @example\n   *\n   * ```ts\n   * import {\n   *   buildCreateApi,\n   *   coreModule,\n   *   reactHooksModule\n   * } from '@reduxjs/toolkit/query/react'\n   *\n   * const createApi = buildCreateApi(\n   *   coreModule(),\n   *   reactHooksModule({ unstable__sideEffectsInRender: true })\n   * )\n   * ```\n   */\n  unstable__sideEffectsInRender?: boolean;\n  /**\n   * A selector creator (usually from `reselect`, or matching the same signature)\n   */\n  createSelector?: typeof _createSelector;\n}\n\n/**\n * Creates a module that generates react hooks from endpoints, for use with `buildCreateApi`.\n *\n *  @example\n * ```ts\n * const MyContext = React.createContext<ReactReduxContextValue | null>(null);\n * const customCreateApi = buildCreateApi(\n *   coreModule(),\n *   reactHooksModule({\n *     hooks: {\n *       useDispatch: createDispatchHook(MyContext),\n *       useSelector: createSelectorHook(MyContext),\n *       useStore: createStoreHook(MyContext)\n *     }\n *   })\n * );\n * ```\n *\n * @returns A module for use with `buildCreateApi`\n */\nexport const reactHooksModule = ({\n  batch = rrBatch,\n  hooks = {\n    useDispatch: rrUseDispatch,\n    useSelector: rrUseSelector,\n    useStore: rrUseStore\n  },\n  createSelector = _createSelector,\n  unstable__sideEffectsInRender = false,\n  ...rest\n}: ReactHooksModuleOptions = {}): Module<ReactHooksModule> => {\n  if (process.env.NODE_ENV !== 'production') {\n    const hookNames = ['useDispatch', 'useSelector', 'useStore'] as const;\n    let warned = false;\n    for (const hookName of hookNames) {\n      // warn for old hook options\n      if (countObjectKeys(rest) > 0) {\n        if ((rest as Partial<typeof hooks>)[hookName]) {\n          if (!warned) {\n            console.warn('As of RTK 2.0, the hooks now need to be specified as one object, provided under a `hooks` key:' + '\\n`reactHooksModule({ hooks: { useDispatch, useSelector, useStore } })`');\n            warned = true;\n          }\n        }\n        // migrate\n        // @ts-ignore\n        hooks[hookName] = rest[hookName];\n      }\n      // then make sure we have them all\n      if (typeof hooks[hookName] !== 'function') {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(36) : `When using custom hooks for context, all ${hookNames.length} hooks need to be provided: ${hookNames.join(', ')}.\\nHook ${hookName} was either not provided or not a function.`);\n      }\n    }\n  }\n  return {\n    name: reactHooksModuleName,\n    init(api, {\n      serializeQueryArgs\n    }, context) {\n      const anyApi = api as any as Api<any, Record<string, any>, any, any, ReactHooksModule>;\n      const {\n        buildQueryHooks,\n        buildInfiniteQueryHooks,\n        buildMutationHook,\n        usePrefetch\n      } = buildHooks({\n        api,\n        moduleOptions: {\n          batch,\n          hooks,\n          unstable__sideEffectsInRender,\n          createSelector\n        },\n        serializeQueryArgs,\n        context\n      });\n      safeAssign(anyApi, {\n        usePrefetch\n      });\n      safeAssign(context, {\n        batch\n      });\n      return {\n        injectEndpoint(endpointName, definition) {\n          if (isQueryDefinition(definition)) {\n            const {\n              useQuery,\n              useLazyQuery,\n              useLazyQuerySubscription,\n              useQueryState,\n              useQuerySubscription\n            } = buildQueryHooks(endpointName);\n            safeAssign(anyApi.endpoints[endpointName], {\n              useQuery,\n              useLazyQuery,\n              useLazyQuerySubscription,\n              useQueryState,\n              useQuerySubscription\n            });\n            (api as any)[`use${capitalize(endpointName)}Query`] = useQuery;\n            (api as any)[`useLazy${capitalize(endpointName)}Query`] = useLazyQuery;\n          }\n          if (isMutationDefinition(definition)) {\n            const useMutation = buildMutationHook(endpointName);\n            safeAssign(anyApi.endpoints[endpointName], {\n              useMutation\n            });\n            (api as any)[`use${capitalize(endpointName)}Mutation`] = useMutation;\n          } else if (isInfiniteQueryDefinition(definition)) {\n            const {\n              useInfiniteQuery,\n              useInfiniteQuerySubscription,\n              useInfiniteQueryState\n            } = buildInfiniteQueryHooks(endpointName);\n            safeAssign(anyApi.endpoints[endpointName], {\n              useInfiniteQuery,\n              useInfiniteQuerySubscription,\n              useInfiniteQueryState\n            });\n            (api as any)[`use${capitalize(endpointName)}InfiniteQuery`] = useInfiniteQuery;\n          }\n        }\n      };\n    }\n  };\n};", "export function capitalize(str: string) {\n  return str.replace(str[0], str[0].toUpperCase());\n}", "// Fast method for counting an object's keys\n// without resorting to `Object.keys(obj).length\n// Will this make a big difference in perf? Probably not\n// But we can save a few allocations.\n\nexport function countObjectKeys(obj: Record<any, any>) {\n  let count = 0;\n  for (const _key in obj) {\n    count++;\n  }\n  return count;\n}", "import type { Api } from '@reduxjs/toolkit/query';\nimport type { StandardSchemaV1 } from '@standard-schema/spec';\nimport type { BaseQueryApi, BaseQueryArg, BaseQueryError, BaseQueryExtraOptions, BaseQueryFn, BaseQueryMeta, BaseQueryResult, QueryReturnValue } from './baseQueryTypes';\nimport type { CacheCollectionQueryExtraOptions } from './core/buildMiddleware/cacheCollection';\nimport type { CacheLifecycleInfiniteQueryExtraOptions, CacheLifecycleMutationExtraOptions, CacheLifecycleQueryExtraOptions } from './core/buildMiddleware/cacheLifecycle';\nimport type { QueryLifecycleInfiniteQueryExtraOptions, QueryLifecycleMutationExtraOptions, QueryLifecycleQueryExtraOptions } from './core/buildMiddleware/queryLifecycle';\nimport type { InfiniteData, InfiniteQueryConfigOptions, QuerySubState, RootState } from './core/index';\nimport type { SerializeQueryArgs } from './defaultSerializeQueryArgs';\nimport type { NEVER } from './fakeBaseQuery';\nimport type { CastAny, HasRequiredProps, MaybePromise, NonUndefined, OmitFromUnion, UnwrapPromise } from './tsHelpers';\nimport { isNotNullish } from './utils';\nimport type { NamedSchemaError } from './standardSchema';\nconst rawResultType = /* @__PURE__ */Symbol();\nconst resultType = /* @__PURE__ */Symbol();\nconst baseQuery = /* @__PURE__ */Symbol();\nexport interface SchemaFailureInfo {\n  endpoint: string;\n  arg: any;\n  type: 'query' | 'mutation';\n  queryCacheKey?: string;\n}\nexport type SchemaFailureHandler = (error: NamedSchemaError, info: SchemaFailureInfo) => void;\nexport type SchemaFailureConverter<BaseQuery extends BaseQueryFn> = (error: NamedSchemaError, info: SchemaFailureInfo) => BaseQueryError<BaseQuery>;\nexport type EndpointDefinitionWithQuery<QueryArg, BaseQuery extends BaseQueryFn, ResultType, RawResultType extends BaseQueryResult<BaseQuery>> = {\n  /**\n   * `query` can be a function that returns either a `string` or an `object` which is passed to your `baseQuery`. If you are using [fetchBaseQuery](./fetchBaseQuery), this can return either a `string` or an `object` of properties in `FetchArgs`. If you use your own custom [`baseQuery`](../../rtk-query/usage/customizing-queries), you can customize this behavior to your liking.\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"query example\"\n   *\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   * type PostsResponse = Post[]\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   tagTypes: ['Post'],\n   *   endpoints: (build) => ({\n   *     getPosts: build.query<PostsResponse, void>({\n   *       // highlight-start\n   *       query: () => 'posts',\n   *       // highlight-end\n   *     }),\n   *     addPost: build.mutation<Post, Partial<Post>>({\n   *      // highlight-start\n   *      query: (body) => ({\n   *        url: `posts`,\n   *        method: 'POST',\n   *        body,\n   *      }),\n   *      // highlight-end\n   *      invalidatesTags: [{ type: 'Post', id: 'LIST' }],\n   *    }),\n   *   })\n   * })\n   * ```\n   */\n  query(arg: QueryArg): BaseQueryArg<BaseQuery>;\n  queryFn?: never;\n  /**\n   * A function to manipulate the data returned by a query or mutation.\n   */\n  transformResponse?(baseQueryReturnValue: RawResultType, meta: BaseQueryMeta<BaseQuery>, arg: QueryArg): ResultType | Promise<ResultType>;\n  /**\n   * A function to manipulate the data returned by a failed query or mutation.\n   */\n  transformErrorResponse?(baseQueryReturnValue: BaseQueryError<BaseQuery>, meta: BaseQueryMeta<BaseQuery>, arg: QueryArg): unknown;\n\n  /**\n   * A schema for the result *before* it's passed to `transformResponse`.\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta no-transpile\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * import * as v from \"valibot\"\n   *\n   * const postSchema = v.object({ id: v.number(), name: v.string() })\n   * type Post = v.InferOutput<typeof postSchema>\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   endpoints: (build) => ({\n   *     getPostName: build.query<Post, { id: number }>({\n   *       query: ({ id }) => `/post/${id}`,\n   *       rawResponseSchema: postSchema,\n   *       transformResponse: (post) => post.name,\n   *     }),\n   *   })\n   * })\n   * ```\n   */\n  rawResponseSchema?: StandardSchemaV1<RawResultType>;\n\n  /**\n   * A schema for the error object returned by the `query` or `queryFn`, *before* it's passed to `transformErrorResponse`.\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta no-transpile\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * import * as v from \"valibot\"\n   * import {customBaseQuery, baseQueryErrorSchema} from \"./customBaseQuery\"\n   *\n   * const api = createApi({\n   *   baseQuery: customBaseQuery,\n   *   endpoints: (build) => ({\n   *     getPost: build.query<Post, { id: number }>({\n   *       query: ({ id }) => `/post/${id}`,\n   *       rawErrorResponseSchema: baseQueryErrorSchema,\n   *       transformErrorResponse: (error) => error.data,\n   *     }),\n   *   })\n   * })\n   * ```\n   */\n  rawErrorResponseSchema?: StandardSchemaV1<BaseQueryError<BaseQuery>>;\n};\nexport type EndpointDefinitionWithQueryFn<QueryArg, BaseQuery extends BaseQueryFn, ResultType> = {\n  /**\n   * Can be used in place of `query` as an inline function that bypasses `baseQuery` completely for the endpoint.\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta title=\"Basic queryFn example\"\n   *\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   * type PostsResponse = Post[]\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   endpoints: (build) => ({\n   *     getPosts: build.query<PostsResponse, void>({\n   *       query: () => 'posts',\n   *     }),\n   *     flipCoin: build.query<'heads' | 'tails', void>({\n   *       // highlight-start\n   *       queryFn(arg, queryApi, extraOptions, baseQuery) {\n   *         const randomVal = Math.random()\n   *         if (randomVal < 0.45) {\n   *           return { data: 'heads' }\n   *         }\n   *         if (randomVal < 0.9) {\n   *           return { data: 'tails' }\n   *         }\n   *         return { error: { status: 500, statusText: 'Internal Server Error', data: \"Coin landed on its edge!\" } }\n   *       }\n   *       // highlight-end\n   *     })\n   *   })\n   * })\n   * ```\n   */\n  queryFn(arg: QueryArg, api: BaseQueryApi, extraOptions: BaseQueryExtraOptions<BaseQuery>, baseQuery: (arg: Parameters<BaseQuery>[0]) => ReturnType<BaseQuery>): MaybePromise<QueryReturnValue<ResultType, BaseQueryError<BaseQuery>, BaseQueryMeta<BaseQuery>>>;\n  query?: never;\n  transformResponse?: never;\n  transformErrorResponse?: never;\n  rawResponseSchema?: never;\n  rawErrorResponseSchema?: never;\n};\ntype BaseEndpointTypes<QueryArg, BaseQuery extends BaseQueryFn, ResultType> = {\n  QueryArg: QueryArg;\n  BaseQuery: BaseQuery;\n  ResultType: ResultType;\n};\ninterface CommonEndpointDefinition<QueryArg, BaseQuery extends BaseQueryFn, ResultType> {\n  /**\n   * A schema for the arguments to be passed to the `query` or `queryFn`.\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta no-transpile\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * import * as v from \"valibot\"\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   endpoints: (build) => ({\n   *     getPost: build.query<Post, { id: number }>({\n   *       query: ({ id }) => `/post/${id}`,\n   *       argSchema: v.object({ id: v.number() }),\n   *     }),\n   *   })\n   * })\n   * ```\n   */\n  argSchema?: StandardSchemaV1<QueryArg>;\n\n  /**\n   * A schema for the result (including `transformResponse` if provided).\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta no-transpile\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * import * as v from \"valibot\"\n   *\n   * const postSchema = v.object({ id: v.number(), name: v.string() })\n   * type Post = v.InferOutput<typeof postSchema>\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   endpoints: (build) => ({\n   *     getPost: build.query<Post, { id: number }>({\n   *       query: ({ id }) => `/post/${id}`,\n   *       responseSchema: postSchema,\n   *     }),\n   *   })\n   * })\n   * ```\n   */\n  responseSchema?: StandardSchemaV1<ResultType>;\n\n  /**\n   * A schema for the error object returned by the `query` or `queryFn` (including `transformErrorResponse` if provided).\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta no-transpile\n   * import { createApi } from '@reduxjs/toolkit/query/react'\n   * import * as v from \"valibot\"\n   * import { customBaseQuery, baseQueryErrorSchema } from \"./customBaseQuery\"\n   *\n   * const api = createApi({\n   *   baseQuery: customBaseQuery,\n   *   endpoints: (build) => ({\n   *     getPost: build.query<Post, { id: number }>({\n   *       query: ({ id }) => `/post/${id}`,\n   *       errorResponseSchema: baseQueryErrorSchema,\n   *     }),\n   *   })\n   * })\n   * ```\n   */\n  errorResponseSchema?: StandardSchemaV1<BaseQueryError<BaseQuery>>;\n\n  /**\n   * A schema for the `meta` property returned by the `query` or `queryFn`.\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta no-transpile\n   * import { createApi } from '@reduxjs/toolkit/query/react'\n   * import * as v from \"valibot\"\n   * import { customBaseQuery, baseQueryMetaSchema } from \"./customBaseQuery\"\n   *\n   * const api = createApi({\n   *   baseQuery: customBaseQuery,\n   *   endpoints: (build) => ({\n   *     getPost: build.query<Post, { id: number }>({\n   *       query: ({ id }) => `/post/${id}`,\n   *       metaSchema: baseQueryMetaSchema,\n   *     }),\n   *   })\n   * })\n   * ```\n   */\n  metaSchema?: StandardSchemaV1<BaseQueryMeta<BaseQuery>>;\n\n  /**\n   * Defaults to `true`.\n   *\n   * Most apps should leave this setting on. The only time it can be a performance issue\n   * is if an API returns extremely large amounts of data (e.g. 10,000 rows per request) and\n   * you're unable to paginate it.\n   *\n   * For details of how this works, please see the below. When it is set to `false`,\n   * every request will cause subscribed components to rerender, even when the data has not changed.\n   *\n   * @see https://redux-toolkit.js.org/api/other-exports#copywithstructuralsharing\n   */\n  structuralSharing?: boolean;\n\n  /**\n   * A function that is called when a schema validation fails.\n   *\n   * Gets called with a `NamedSchemaError` and an object containing the endpoint name, the type of the endpoint, the argument passed to the endpoint, and the query cache key (if applicable).\n   *\n   * `NamedSchemaError` has the following properties:\n   * - `issues`: an array of issues that caused the validation to fail\n   * - `value`: the value that was passed to the schema\n   * - `schemaName`: the name of the schema that was used to validate the value (e.g. `argSchema`)\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta no-transpile\n   * import { createApi } from '@reduxjs/toolkit/query/react'\n   * import * as v from \"valibot\"\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   endpoints: (build) => ({\n   *     getPost: build.query<Post, { id: number }>({\n   *       query: ({ id }) => `/post/${id}`,\n   *       onSchemaFailure: (error, info) => {\n   *         console.error(error, info)\n   *       },\n   *     }),\n   *   })\n   * })\n   * ```\n   */\n  onSchemaFailure?: SchemaFailureHandler;\n\n  /**\n   * Convert a schema validation failure into an error shape matching base query errors.\n   *\n   * When not provided, schema failures are treated as fatal, and normal error handling such as tag invalidation will not be executed.\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta no-transpile\n   * import { createApi } from '@reduxjs/toolkit/query/react'\n   * import * as v from \"valibot\"\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   endpoints: (build) => ({\n   *     getPost: build.query<Post, { id: number }>({\n   *       query: ({ id }) => `/post/${id}`,\n   *       responseSchema: v.object({ id: v.number(), name: v.string() }),\n   *       catchSchemaFailure: (error, info) => ({\n   *         status: \"CUSTOM_ERROR\",\n   *         error: error.schemaName + \" failed validation\",\n   *         data: error.issues,\n   *       }),\n   *     }),\n   *   }),\n   * })\n   * ```\n   */\n  catchSchemaFailure?: SchemaFailureConverter<BaseQuery>;\n\n  /**\n   * Defaults to `false`.\n   *\n   * If set to `true`, will skip schema validation for this endpoint.\n   * Overrides the global setting.\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta no-transpile\n   * import { createApi } from '@reduxjs/toolkit/query/react'\n   * import * as v from \"valibot\"\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   endpoints: (build) => ({\n   *     getPost: build.query<Post, { id: number }>({\n   *       query: ({ id }) => `/post/${id}`,\n   *       responseSchema: v.object({ id: v.number(), name: v.string() }),\n   *       skipSchemaValidation: process.env.NODE_ENV === \"test\", // skip schema validation in tests, since we'll be mocking the response\n   *     }),\n   *   })\n   * })\n   * ```\n   */\n  skipSchemaValidation?: boolean;\n}\nexport type BaseEndpointDefinition<QueryArg, BaseQuery extends BaseQueryFn, ResultType, RawResultType extends BaseQueryResult<BaseQuery> = BaseQueryResult<BaseQuery>> = (([CastAny<BaseQueryResult<BaseQuery>, {}>] extends [NEVER] ? never : EndpointDefinitionWithQuery<QueryArg, BaseQuery, ResultType, RawResultType>) | EndpointDefinitionWithQueryFn<QueryArg, BaseQuery, ResultType>) & CommonEndpointDefinition<QueryArg, BaseQuery, ResultType> & {\n  /* phantom type */\n  [rawResultType]?: RawResultType;\n  /* phantom type */\n  [resultType]?: ResultType;\n  /* phantom type */\n  [baseQuery]?: BaseQuery;\n} & HasRequiredProps<BaseQueryExtraOptions<BaseQuery>, {\n  extraOptions: BaseQueryExtraOptions<BaseQuery>;\n}, {\n  extraOptions?: BaseQueryExtraOptions<BaseQuery>;\n}>;\nexport enum DefinitionType {\n  query = 'query',\n  mutation = 'mutation',\n  infinitequery = 'infinitequery',\n}\nexport type GetResultDescriptionFn<TagTypes extends string, ResultType, QueryArg, ErrorType, MetaType> = (result: ResultType | undefined, error: ErrorType | undefined, arg: QueryArg, meta: MetaType) => ReadonlyArray<TagDescription<TagTypes> | undefined | null>;\nexport type FullTagDescription<TagType> = {\n  type: TagType;\n  id?: number | string;\n};\nexport type TagDescription<TagType> = TagType | FullTagDescription<TagType>;\n\n/**\n * @public\n */\nexport type ResultDescription<TagTypes extends string, ResultType, QueryArg, ErrorType, MetaType> = ReadonlyArray<TagDescription<TagTypes> | undefined | null> | GetResultDescriptionFn<TagTypes, ResultType, QueryArg, ErrorType, MetaType>;\ntype QueryTypes<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> = BaseEndpointTypes<QueryArg, BaseQuery, ResultType> & {\n  /**\n   * The endpoint definition type. To be used with some internal generic types.\n   * @example\n   * ```ts\n   * const useMyWrappedHook: UseQuery<typeof api.endpoints.query.Types.QueryDefinition> = ...\n   * ```\n   */\n  QueryDefinition: QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n  TagTypes: TagTypes;\n  ReducerPath: ReducerPath;\n};\n\n/**\n * @public\n */\nexport interface QueryExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> extends CacheLifecycleQueryExtraOptions<ResultType, QueryArg, BaseQuery, ReducerPath>, QueryLifecycleQueryExtraOptions<ResultType, QueryArg, BaseQuery, ReducerPath>, CacheCollectionQueryExtraOptions {\n  type: DefinitionType.query;\n\n  /**\n   * Used by `query` endpoints. Determines which 'tag' is attached to the cached data returned by the query.\n   * Expects an array of tag type strings, an array of objects of tag types with ids, or a function that returns such an array.\n   * 1.  `['Post']` - equivalent to `2`\n   * 2.  `[{ type: 'Post' }]` - equivalent to `1`\n   * 3.  `[{ type: 'Post', id: 1 }]`\n   * 4.  `(result, error, arg) => ['Post']` - equivalent to `5`\n   * 5.  `(result, error, arg) => [{ type: 'Post' }]` - equivalent to `4`\n   * 6.  `(result, error, arg) => [{ type: 'Post', id: 1 }]`\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"providesTags example\"\n   *\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   * type PostsResponse = Post[]\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   tagTypes: ['Posts'],\n   *   endpoints: (build) => ({\n   *     getPosts: build.query<PostsResponse, void>({\n   *       query: () => 'posts',\n   *       // highlight-start\n   *       providesTags: (result) =>\n   *         result\n   *           ? [\n   *               ...result.map(({ id }) => ({ type: 'Posts' as const, id })),\n   *               { type: 'Posts', id: 'LIST' },\n   *             ]\n   *           : [{ type: 'Posts', id: 'LIST' }],\n   *       // highlight-end\n   *     })\n   *   })\n   * })\n   * ```\n   */\n  providesTags?: ResultDescription<TagTypes, ResultType, QueryArg, BaseQueryError<BaseQuery>, BaseQueryMeta<BaseQuery>>;\n  /**\n   * Not to be used. A query should not invalidate tags in the cache.\n   */\n  invalidatesTags?: never;\n\n  /**\n   * Can be provided to return a custom cache key value based on the query arguments.\n   *\n   * This is primarily intended for cases where a non-serializable value is passed as part of the query arg object and should be excluded from the cache key.  It may also be used for cases where an endpoint should only have a single cache entry, such as an infinite loading / pagination implementation.\n   *\n   * Unlike the `createApi` version which can _only_ return a string, this per-endpoint option can also return an an object, number, or boolean.  If it returns a string, that value will be used as the cache key directly.  If it returns an object / number / boolean, that value will be passed to the built-in `defaultSerializeQueryArgs`.  This simplifies the use case of stripping out args you don't want included in the cache key.\n   *\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"serializeQueryArgs : exclude value\"\n   *\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   *\n   * interface MyApiClient {\n   *   fetchPost: (id: string) => Promise<Post>\n   * }\n   *\n   * createApi({\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *  endpoints: (build) => ({\n   *    // Example: an endpoint with an API client passed in as an argument,\n   *    // but only the item ID should be used as the cache key\n   *    getPost: build.query<Post, { id: string; client: MyApiClient }>({\n   *      queryFn: async ({ id, client }) => {\n   *        const post = await client.fetchPost(id)\n   *        return { data: post }\n   *      },\n   *      // highlight-start\n   *      serializeQueryArgs: ({ queryArgs, endpointDefinition, endpointName }) => {\n   *        const { id } = queryArgs\n   *        // This can return a string, an object, a number, or a boolean.\n   *        // If it returns an object, number or boolean, that value\n   *        // will be serialized automatically via `defaultSerializeQueryArgs`\n   *        return { id } // omit `client` from the cache key\n   *\n   *        // Alternately, you can use `defaultSerializeQueryArgs` yourself:\n   *        // return defaultSerializeQueryArgs({\n   *        //   endpointName,\n   *        //   queryArgs: { id },\n   *        //   endpointDefinition\n   *        // })\n   *        // Or  create and return a string yourself:\n   *        // return `getPost(${id})`\n   *      },\n   *      // highlight-end\n   *    }),\n   *  }),\n   *})\n   * ```\n   */\n  serializeQueryArgs?: SerializeQueryArgs<QueryArg, string | number | boolean | Record<any, any>>;\n\n  /**\n   * Can be provided to merge an incoming response value into the current cache data.\n   * If supplied, no automatic structural sharing will be applied - it's up to\n   * you to update the cache appropriately.\n   *\n   * Since RTKQ normally replaces cache entries with the new response, you will usually\n   * need to use this with the `serializeQueryArgs` or `forceRefetch` options to keep\n   * an existing cache entry so that it can be updated.\n   *\n   * Since this is wrapped with Immer, you may either mutate the `currentCacheValue` directly,\n   * or return a new value, but _not_ both at once.\n   *\n   * Will only be called if the existing `currentCacheData` is _not_ `undefined` - on first response,\n   * the cache entry will just save the response data directly.\n   *\n   * Useful if you don't want a new request to completely override the current cache value,\n   * maybe because you have manually updated it from another source and don't want those\n   * updates to get lost.\n   *\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"merge: pagination\"\n   *\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   *\n   * createApi({\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *  endpoints: (build) => ({\n   *    listItems: build.query<string[], number>({\n   *      query: (pageNumber) => `/listItems?page=${pageNumber}`,\n   *     // Only have one cache entry because the arg always maps to one string\n   *     serializeQueryArgs: ({ endpointName }) => {\n   *       return endpointName\n   *      },\n   *      // Always merge incoming data to the cache entry\n   *      merge: (currentCache, newItems) => {\n   *        currentCache.push(...newItems)\n   *      },\n   *      // Refetch when the page arg changes\n   *      forceRefetch({ currentArg, previousArg }) {\n   *        return currentArg !== previousArg\n   *      },\n   *    }),\n   *  }),\n   *})\n   * ```\n   */\n  merge?(currentCacheData: ResultType, responseData: ResultType, otherArgs: {\n    arg: QueryArg;\n    baseQueryMeta: BaseQueryMeta<BaseQuery>;\n    requestId: string;\n    fulfilledTimeStamp: number;\n  }): ResultType | void;\n\n  /**\n   * Check to see if the endpoint should force a refetch in cases where it normally wouldn't.\n   * This is primarily useful for \"infinite scroll\" / pagination use cases where\n   * RTKQ is keeping a single cache entry that is added to over time, in combination\n   * with `serializeQueryArgs` returning a fixed cache key and a `merge` callback\n   * set to add incoming data to the cache entry each time.\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"forceRefresh: pagination\"\n   *\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   *\n   * createApi({\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *  endpoints: (build) => ({\n   *    listItems: build.query<string[], number>({\n   *      query: (pageNumber) => `/listItems?page=${pageNumber}`,\n   *     // Only have one cache entry because the arg always maps to one string\n   *     serializeQueryArgs: ({ endpointName }) => {\n   *       return endpointName\n   *      },\n   *      // Always merge incoming data to the cache entry\n   *      merge: (currentCache, newItems) => {\n   *        currentCache.push(...newItems)\n   *      },\n   *      // Refetch when the page arg changes\n   *      forceRefetch({ currentArg, previousArg }) {\n   *        return currentArg !== previousArg\n   *      },\n   *    }),\n   *  }),\n   *})\n   * ```\n   */\n  forceRefetch?(params: {\n    currentArg: QueryArg | undefined;\n    previousArg: QueryArg | undefined;\n    state: RootState<any, any, string>;\n    endpointState?: QuerySubState<any>;\n  }): boolean;\n\n  /**\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\n   */\n  Types?: QueryTypes<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n}\nexport type QueryDefinition<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string, RawResultType extends BaseQueryResult<BaseQuery> = BaseQueryResult<BaseQuery>> = BaseEndpointDefinition<QueryArg, BaseQuery, ResultType, RawResultType> & QueryExtraOptions<TagTypes, ResultType, QueryArg, BaseQuery, ReducerPath>;\nexport type InfiniteQueryTypes<QueryArg, PageParam, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> = BaseEndpointTypes<QueryArg, BaseQuery, ResultType> & {\n  /**\n   * The endpoint definition type. To be used with some internal generic types.\n   * @example\n   * ```ts\n   * const useMyWrappedHook: UseQuery<typeof api.endpoints.query.Types.QueryDefinition> = ...\n   * ```\n   */\n  InfiniteQueryDefinition: InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, TagTypes, ResultType, ReducerPath>;\n  TagTypes: TagTypes;\n  ReducerPath: ReducerPath;\n};\nexport interface InfiniteQueryExtraOptions<TagTypes extends string, ResultType, QueryArg, PageParam, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> extends CacheLifecycleInfiniteQueryExtraOptions<InfiniteData<ResultType, PageParam>, QueryArg, BaseQuery, ReducerPath>, QueryLifecycleInfiniteQueryExtraOptions<InfiniteData<ResultType, PageParam>, QueryArg, BaseQuery, ReducerPath>, CacheCollectionQueryExtraOptions {\n  type: DefinitionType.infinitequery;\n  providesTags?: ResultDescription<TagTypes, InfiniteData<ResultType, PageParam>, QueryArg, BaseQueryError<BaseQuery>, BaseQueryMeta<BaseQuery>>;\n  /**\n   * Not to be used. A query should not invalidate tags in the cache.\n   */\n  invalidatesTags?: never;\n\n  /**\n   * Required options to configure the infinite query behavior.\n   * `initialPageParam` and `getNextPageParam` are required, to\n   * ensure the infinite query can properly fetch the next page of data.\n   * `initialPageParam` may be specified when using the\n   * endpoint, to override the default value.\n   * `maxPages` and `getPreviousPageParam` are both optional.\n   * \n   * @example\n   * \n   * ```ts\n   * // codeblock-meta title=\"infiniteQueryOptions example\"\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\n   * \n   * type Pokemon = {\n   *   id: string\n   *   name: string\n   * }\n   * \n   * const pokemonApi = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: 'https://pokeapi.co/api/v2/' }),\n   *   endpoints: (build) => ({\n   *     getInfinitePokemonWithMax: build.infiniteQuery<Pokemon[], string, number>({\n   *       infiniteQueryOptions: {\n   *         initialPageParam: 0,\n   *         maxPages: 3,\n   *         getNextPageParam: (lastPage, allPages, lastPageParam, allPageParams) =>\n   *           lastPageParam + 1,\n   *         getPreviousPageParam: (\n   *           firstPage,\n   *           allPages,\n   *           firstPageParam,\n   *           allPageParams,\n   *         ) => {\n   *           return firstPageParam > 0 ? firstPageParam - 1 : undefined\n   *         },\n   *       },\n   *       query({pageParam}) {\n   *         return `https://example.com/listItems?page=${pageParam}`\n   *       },\n   *     }),\n   *   }),\n   * })\n   \n   * ```\n   */\n  infiniteQueryOptions: InfiniteQueryConfigOptions<ResultType, PageParam>;\n\n  /**\n   * Can be provided to return a custom cache key value based on the query arguments.\n   *\n   * This is primarily intended for cases where a non-serializable value is passed as part of the query arg object and should be excluded from the cache key.  It may also be used for cases where an endpoint should only have a single cache entry, such as an infinite loading / pagination implementation.\n   *\n   * Unlike the `createApi` version which can _only_ return a string, this per-endpoint option can also return an an object, number, or boolean.  If it returns a string, that value will be used as the cache key directly.  If it returns an object / number / boolean, that value will be passed to the built-in `defaultSerializeQueryArgs`.  This simplifies the use case of stripping out args you don't want included in the cache key.\n   *\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"serializeQueryArgs : exclude value\"\n   *\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   *\n   * interface MyApiClient {\n   *   fetchPost: (id: string) => Promise<Post>\n   * }\n   *\n   * createApi({\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *  endpoints: (build) => ({\n   *    // Example: an endpoint with an API client passed in as an argument,\n   *    // but only the item ID should be used as the cache key\n   *    getPost: build.query<Post, { id: string; client: MyApiClient }>({\n   *      queryFn: async ({ id, client }) => {\n   *        const post = await client.fetchPost(id)\n   *        return { data: post }\n   *      },\n   *      // highlight-start\n   *      serializeQueryArgs: ({ queryArgs, endpointDefinition, endpointName }) => {\n   *        const { id } = queryArgs\n   *        // This can return a string, an object, a number, or a boolean.\n   *        // If it returns an object, number or boolean, that value\n   *        // will be serialized automatically via `defaultSerializeQueryArgs`\n   *        return { id } // omit `client` from the cache key\n   *\n   *        // Alternately, you can use `defaultSerializeQueryArgs` yourself:\n   *        // return defaultSerializeQueryArgs({\n   *        //   endpointName,\n   *        //   queryArgs: { id },\n   *        //   endpointDefinition\n   *        // })\n   *        // Or  create and return a string yourself:\n   *        // return `getPost(${id})`\n   *      },\n   *      // highlight-end\n   *    }),\n   *  }),\n   *})\n   * ```\n   */\n  serializeQueryArgs?: SerializeQueryArgs<QueryArg, string | number | boolean | Record<any, any>>;\n\n  /**\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\n   */\n  Types?: InfiniteQueryTypes<QueryArg, PageParam, BaseQuery, TagTypes, ResultType, ReducerPath>;\n}\nexport type InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string, RawResultType extends BaseQueryResult<BaseQuery> = BaseQueryResult<BaseQuery>> =\n// Infinite query endpoints receive `{queryArg, pageParam}`\nBaseEndpointDefinition<InfiniteQueryCombinedArg<QueryArg, PageParam>, BaseQuery, ResultType, RawResultType> & InfiniteQueryExtraOptions<TagTypes, ResultType, QueryArg, PageParam, BaseQuery, ReducerPath>;\ntype MutationTypes<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> = BaseEndpointTypes<QueryArg, BaseQuery, ResultType> & {\n  /**\n   * The endpoint definition type. To be used with some internal generic types.\n   * @example\n   * ```ts\n   * const useMyWrappedHook: UseMutation<typeof api.endpoints.query.Types.MutationDefinition> = ...\n   * ```\n   */\n  MutationDefinition: MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n  TagTypes: TagTypes;\n  ReducerPath: ReducerPath;\n};\n\n/**\n * @public\n */\nexport interface MutationExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> extends CacheLifecycleMutationExtraOptions<ResultType, QueryArg, BaseQuery, ReducerPath>, QueryLifecycleMutationExtraOptions<ResultType, QueryArg, BaseQuery, ReducerPath> {\n  type: DefinitionType.mutation;\n\n  /**\n   * Used by `mutation` endpoints. Determines which cached data should be either re-fetched or removed from the cache.\n   * Expects the same shapes as `providesTags`.\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"invalidatesTags example\"\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   * type PostsResponse = Post[]\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   tagTypes: ['Posts'],\n   *   endpoints: (build) => ({\n   *     getPosts: build.query<PostsResponse, void>({\n   *       query: () => 'posts',\n   *       providesTags: (result) =>\n   *         result\n   *           ? [\n   *               ...result.map(({ id }) => ({ type: 'Posts' as const, id })),\n   *               { type: 'Posts', id: 'LIST' },\n   *             ]\n   *           : [{ type: 'Posts', id: 'LIST' }],\n   *     }),\n   *     addPost: build.mutation<Post, Partial<Post>>({\n   *       query(body) {\n   *         return {\n   *           url: `posts`,\n   *           method: 'POST',\n   *           body,\n   *         }\n   *       },\n   *       // highlight-start\n   *       invalidatesTags: [{ type: 'Posts', id: 'LIST' }],\n   *       // highlight-end\n   *     }),\n   *   })\n   * })\n   * ```\n   */\n  invalidatesTags?: ResultDescription<TagTypes, ResultType, QueryArg, BaseQueryError<BaseQuery>, BaseQueryMeta<BaseQuery>>;\n  /**\n   * Not to be used. A mutation should not provide tags to the cache.\n   */\n  providesTags?: never;\n\n  /**\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\n   */\n  Types?: MutationTypes<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n}\nexport type MutationDefinition<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string, RawResultType extends BaseQueryResult<BaseQuery> = BaseQueryResult<BaseQuery>> = BaseEndpointDefinition<QueryArg, BaseQuery, ResultType, RawResultType> & MutationExtraOptions<TagTypes, ResultType, QueryArg, BaseQuery, ReducerPath>;\nexport type EndpointDefinition<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string, PageParam = any, RawResultType extends BaseQueryResult<BaseQuery> = BaseQueryResult<BaseQuery>> = QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath, RawResultType> | MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath, RawResultType> | InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, TagTypes, ResultType, ReducerPath, RawResultType>;\nexport type EndpointDefinitions = Record<string, EndpointDefinition<any, any, any, any, any, any, any>>;\nexport function isQueryDefinition(e: EndpointDefinition<any, any, any, any, any, any, any>): e is QueryDefinition<any, any, any, any, any, any> {\n  return e.type === DefinitionType.query;\n}\nexport function isMutationDefinition(e: EndpointDefinition<any, any, any, any, any, any, any>): e is MutationDefinition<any, any, any, any, any, any> {\n  return e.type === DefinitionType.mutation;\n}\nexport function isInfiniteQueryDefinition(e: EndpointDefinition<any, any, any, any, any, any, any>): e is InfiniteQueryDefinition<any, any, any, any, any, any, any> {\n  return e.type === DefinitionType.infinitequery;\n}\nexport function isAnyQueryDefinition(e: EndpointDefinition<any, any, any, any>): e is QueryDefinition<any, any, any, any> | InfiniteQueryDefinition<any, any, any, any, any> {\n  return isQueryDefinition(e) || isInfiniteQueryDefinition(e);\n}\nexport type EndpointBuilder<BaseQuery extends BaseQueryFn, TagTypes extends string, ReducerPath extends string> = {\n  /**\n   * An endpoint definition that retrieves data, and may provide tags to the cache.\n   *\n   * @example\n   * ```js\n   * // codeblock-meta title=\"Example of all query endpoint options\"\n   * const api = createApi({\n   *  baseQuery,\n   *  endpoints: (build) => ({\n   *    getPost: build.query({\n   *      query: (id) => ({ url: `post/${id}` }),\n   *      // Pick out data and prevent nested properties in a hook or selector\n   *      transformResponse: (response) => response.data,\n   *      // Pick out error and prevent nested properties in a hook or selector\n   *      transformErrorResponse: (response) => response.error,\n   *      // `result` is the server response\n   *      providesTags: (result, error, id) => [{ type: 'Post', id }],\n   *      // trigger side effects or optimistic updates\n   *      onQueryStarted(id, { dispatch, getState, extra, requestId, queryFulfilled, getCacheEntry, updateCachedData }) {},\n   *      // handle subscriptions etc\n   *      onCacheEntryAdded(id, { dispatch, getState, extra, requestId, cacheEntryRemoved, cacheDataLoaded, getCacheEntry, updateCachedData }) {},\n   *    }),\n   *  }),\n   *});\n   *```\n   */\n  query<ResultType, QueryArg, RawResultType extends BaseQueryResult<BaseQuery> = BaseQueryResult<BaseQuery>>(definition: OmitFromUnion<QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath, RawResultType>, 'type'>): QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath, RawResultType>;\n\n  /**\n   * An endpoint definition that alters data on the server or will possibly invalidate the cache.\n   *\n   * @example\n   * ```js\n   * // codeblock-meta title=\"Example of all mutation endpoint options\"\n   * const api = createApi({\n   *   baseQuery,\n   *   endpoints: (build) => ({\n   *     updatePost: build.mutation({\n   *       query: ({ id, ...patch }) => ({ url: `post/${id}`, method: 'PATCH', body: patch }),\n   *       // Pick out data and prevent nested properties in a hook or selector\n   *       transformResponse: (response) => response.data,\n   *       // Pick out error and prevent nested properties in a hook or selector\n   *       transformErrorResponse: (response) => response.error,\n   *       // `result` is the server response\n   *       invalidatesTags: (result, error, id) => [{ type: 'Post', id }],\n   *      // trigger side effects or optimistic updates\n   *      onQueryStarted(id, { dispatch, getState, extra, requestId, queryFulfilled, getCacheEntry }) {},\n   *      // handle subscriptions etc\n   *      onCacheEntryAdded(id, { dispatch, getState, extra, requestId, cacheEntryRemoved, cacheDataLoaded, getCacheEntry }) {},\n   *     }),\n   *   }),\n   * });\n   * ```\n   */\n  mutation<ResultType, QueryArg, RawResultType extends BaseQueryResult<BaseQuery> = BaseQueryResult<BaseQuery>>(definition: OmitFromUnion<MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath, RawResultType>, 'type'>): MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath, RawResultType>;\n  infiniteQuery<ResultType, QueryArg, PageParam, RawResultType extends BaseQueryResult<BaseQuery> = BaseQueryResult<BaseQuery>>(definition: OmitFromUnion<InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, TagTypes, ResultType, ReducerPath, RawResultType>, 'type'>): InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, TagTypes, ResultType, ReducerPath, RawResultType>;\n};\nexport type AssertTagTypes = <T extends FullTagDescription<string>>(t: T) => T;\nexport function calculateProvidedBy<ResultType, QueryArg, ErrorType, MetaType>(description: ResultDescription<string, ResultType, QueryArg, ErrorType, MetaType> | undefined, result: ResultType | undefined, error: ErrorType | undefined, queryArg: QueryArg, meta: MetaType | undefined, assertTagTypes: AssertTagTypes): readonly FullTagDescription<string>[] {\n  if (isFunction(description)) {\n    return description(result as ResultType, error as undefined, queryArg, meta as MetaType).filter(isNotNullish).map(expandTagDescription).map(assertTagTypes);\n  }\n  if (Array.isArray(description)) {\n    return description.map(expandTagDescription).map(assertTagTypes);\n  }\n  return [];\n}\nfunction isFunction<T>(t: T): t is Extract<T, Function> {\n  return typeof t === 'function';\n}\nexport function expandTagDescription(description: TagDescription<string>): FullTagDescription<string> {\n  return typeof description === 'string' ? {\n    type: description\n  } : description;\n}\nexport type QueryArgFrom<D extends BaseEndpointDefinition<any, any, any, any>> = D extends BaseEndpointDefinition<infer QA, any, any, any> ? QA : never;\n\n// Just extracting `QueryArg` from `BaseEndpointDefinition`\n// doesn't sufficiently match here.\n// We need to explicitly match against `InfiniteQueryDefinition`\nexport type InfiniteQueryArgFrom<D extends BaseEndpointDefinition<any, any, any, any>> = D extends InfiniteQueryDefinition<infer QA, any, any, any, any, any, any> ? QA : never;\nexport type QueryArgFromAnyQuery<D extends BaseEndpointDefinition<any, any, any, any>> = D extends InfiniteQueryDefinition<any, any, any, any, any, any, any> ? InfiniteQueryArgFrom<D> : D extends QueryDefinition<any, any, any, any, any, any> ? QueryArgFrom<D> : never;\nexport type ResultTypeFrom<D extends BaseEndpointDefinition<any, any, any, any>> = D extends BaseEndpointDefinition<any, any, infer RT, any> ? RT : unknown;\nexport type ReducerPathFrom<D extends EndpointDefinition<any, any, any, any, any, any, any>> = D extends EndpointDefinition<any, any, any, any, infer RP, any, any> ? RP : unknown;\nexport type TagTypesFrom<D extends EndpointDefinition<any, any, any, any, any, any, any>> = D extends EndpointDefinition<any, any, infer TT, any, any, any, any> ? TT : unknown;\nexport type PageParamFrom<D extends InfiniteQueryDefinition<any, any, any, any, any, any, any>> = D extends InfiniteQueryDefinition<any, infer PP, any, any, any, any, any> ? PP : unknown;\nexport type InfiniteQueryCombinedArg<QueryArg, PageParam> = {\n  queryArg: QueryArg;\n  pageParam: PageParam;\n};\nexport type TagTypesFromApi<T> = T extends Api<any, any, any, infer TagTypes> ? TagTypes : never;\nexport type DefinitionsFromApi<T> = T extends Api<any, infer Definitions, any, any> ? Definitions : never;\nexport type TransformedResponse<NewDefinitions extends EndpointDefinitions, K, ResultType> = K extends keyof NewDefinitions ? NewDefinitions[K]['transformResponse'] extends undefined ? ResultType : UnwrapPromise<ReturnType<NonUndefined<NewDefinitions[K]['transformResponse']>>> : ResultType;\nexport type OverrideResultType<Definition, NewResultType> = Definition extends QueryDefinition<infer QueryArg, infer BaseQuery, infer TagTypes, any, infer ReducerPath> ? QueryDefinition<QueryArg, BaseQuery, TagTypes, NewResultType, ReducerPath> : Definition extends MutationDefinition<infer QueryArg, infer BaseQuery, infer TagTypes, any, infer ReducerPath> ? MutationDefinition<QueryArg, BaseQuery, TagTypes, NewResultType, ReducerPath> : Definition extends InfiniteQueryDefinition<infer QueryArg, infer PageParam, infer BaseQuery, infer TagTypes, any, infer ReducerPath> ? InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, TagTypes, NewResultType, ReducerPath> : never;\nexport type UpdateDefinitions<Definitions extends EndpointDefinitions, NewTagTypes extends string, NewDefinitions extends EndpointDefinitions> = { [K in keyof Definitions]: Definitions[K] extends QueryDefinition<infer QueryArg, infer BaseQuery, any, infer ResultType, infer ReducerPath> ? QueryDefinition<QueryArg, BaseQuery, NewTagTypes, TransformedResponse<NewDefinitions, K, ResultType>, ReducerPath> : Definitions[K] extends MutationDefinition<infer QueryArg, infer BaseQuery, any, infer ResultType, infer ReducerPath> ? MutationDefinition<QueryArg, BaseQuery, NewTagTypes, TransformedResponse<NewDefinitions, K, ResultType>, ReducerPath> : Definitions[K] extends InfiniteQueryDefinition<infer QueryArg, infer PageParam, infer BaseQuery, any, infer ResultType, infer ReducerPath> ? InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, NewTagTypes, TransformedResponse<NewDefinitions, K, ResultType>, ReducerPath> : never };", "export type Id<T> = { [K in keyof T]: T[K] } & {};\nexport type WithRequiredProp<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;\nexport type Override<T1, T2> = T2 extends any ? Omit<T1, keyof T2> & T2 : never;\nexport function assertCast<T>(v: any): asserts v is T {}\nexport function safeAssign<T extends object>(target: T, ...args: Array<Partial<NoInfer<T>>>): T {\n  return Object.assign(target, ...args);\n}\n\n/**\n * Convert a Union type `(A|B)` to an intersection type `(A&B)`\n */\nexport type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never;\nexport type NonOptionalKeys<T> = { [K in keyof T]-?: undefined extends T[K] ? never : K }[keyof T];\nexport type HasRequiredProps<T, True, False> = NonOptionalKeys<T> extends never ? False : True;\nexport type OptionalIfAllPropsOptional<T> = HasRequiredProps<T, T, T | never>;\nexport type NoInfer<T> = [T][T extends any ? 0 : never];\nexport type NonUndefined<T> = T extends undefined ? never : T;\nexport type UnwrapPromise<T> = T extends PromiseLike<infer V> ? V : T;\nexport type MaybePromise<T> = T | PromiseLike<T>;\nexport type OmitFromUnion<T, K extends keyof T> = T extends any ? Omit<T, K> : never;\nexport type IsAny<T, True, False = never> = true | false extends (T extends never ? true : false) ? True : False;\nexport type CastAny<T, CastTo> = IsAny<T, CastTo, T>;", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2 } from \"@reduxjs/toolkit\";\nimport type { Selector, ThunkAction, ThunkDispatch, UnknownAction } from '@reduxjs/toolkit';\nimport type { Api, ApiContext, ApiEndpointInfiniteQuery, ApiEndpointMutation, ApiEndpointQuery, BaseQueryFn, CoreModule, EndpointDefinitions, InfiniteQueryActionCreatorResult, InfiniteQueryArgFrom, InfiniteQueryDefinition, InfiniteQueryResultSelectorResult, InfiniteQuerySubState, MutationActionCreatorResult, MutationDefinition, MutationResultSelectorResult, PageParamFrom, PrefetchOptions, QueryActionCreatorResult, QueryArgFrom, QueryCacheKey, QueryDefinition, QueryKeys, QueryResultSelectorResult, QuerySubState, ResultTypeFrom, RootState, SerializeQueryArgs, SkipToken, SubscriptionOptions, TSHelpersId, TSHelpersNoInfer, TSHelpersOverride } from '@reduxjs/toolkit/query';\nimport { defaultSerializeQueryArgs, QueryStatus, skipToken } from '@reduxjs/toolkit/query';\nimport type { DependencyList } from 'react';\nimport { useCallback, useDebugValue, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';\nimport { shallowEqual } from 'react-redux';\nimport type { SubscriptionSelectors } from '../core/buildMiddleware/index';\nimport type { InfiniteData, InfiniteQueryConfigOptions } from '../core/index';\nimport type { UninitializedValue } from './constants';\nimport { UNINITIALIZED_VALUE } from './constants';\nimport type { ReactHooksModuleOptions } from './module';\nimport { useStableQueryArgs } from './useSerializedStableValue';\nimport { useShallowStableValue } from './useShallowStableValue';\nimport type { InfiniteQueryDirection } from '../core/apiState';\nimport { isInfiniteQueryDefinition } from '../endpointDefinitions';\nimport type { StartInfiniteQueryActionCreator } from '../core/buildInitiate';\n\n// Copy-pasted from React-Redux\nconst canUseDOM = () => !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\nconst isDOM = /* @__PURE__ */canUseDOM();\n\n// Under React Native, we know that we always want to use useLayoutEffect\n\nconst isRunningInReactNative = () => typeof navigator !== 'undefined' && navigator.product === 'ReactNative';\nconst isReactNative = /* @__PURE__ */isRunningInReactNative();\nconst getUseIsomorphicLayoutEffect = () => isDOM || isReactNative ? useLayoutEffect : useEffect;\nexport const useIsomorphicLayoutEffect = /* @__PURE__ */getUseIsomorphicLayoutEffect();\nexport type QueryHooks<Definition extends QueryDefinition<any, any, any, any, any>> = {\n  useQuery: UseQuery<Definition>;\n  useLazyQuery: UseLazyQuery<Definition>;\n  useQuerySubscription: UseQuerySubscription<Definition>;\n  useLazyQuerySubscription: UseLazyQuerySubscription<Definition>;\n  useQueryState: UseQueryState<Definition>;\n};\nexport type InfiniteQueryHooks<Definition extends InfiniteQueryDefinition<any, any, any, any, any>> = {\n  useInfiniteQuery: UseInfiniteQuery<Definition>;\n  useInfiniteQuerySubscription: UseInfiniteQuerySubscription<Definition>;\n  useInfiniteQueryState: UseInfiniteQueryState<Definition>;\n};\nexport type MutationHooks<Definition extends MutationDefinition<any, any, any, any, any>> = {\n  useMutation: UseMutation<Definition>;\n};\n\n/**\n * A React hook that automatically triggers fetches of data from an endpoint, 'subscribes' the component to the cached data, and reads the request status and cached data from the Redux store. The component will re-render as the loading status changes and the data becomes available.\n *\n * The query arg is used as a cache key. Changing the query arg will tell the hook to re-fetch the data if it does not exist in the cache already, and the hook will return the data for that query arg once it's available.\n *\n * This hook combines the functionality of both [`useQueryState`](#usequerystate) and [`useQuerySubscription`](#usequerysubscription) together, and is intended to be used in the majority of situations.\n *\n * #### Features\n *\n * - Automatically triggers requests to retrieve data based on the hook argument and whether cached data exists by default\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met\n * - Returns the latest request status and cached data from the Redux store\n * - Re-renders as the request status changes and data becomes available\n */\nexport type UseQuery<D extends QueryDefinition<any, any, any, any>> = <R extends Record<string, any> = UseQueryStateDefaultResult<D>>(arg: QueryArgFrom<D> | SkipToken, options?: UseQuerySubscriptionOptions & UseQueryStateOptions<D, R>) => UseQueryHookResult<D, R>;\nexport type TypedUseQuery<ResultType, QueryArg, BaseQuery extends BaseQueryFn> = UseQuery<QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>>;\nexport type UseQueryHookResult<D extends QueryDefinition<any, any, any, any>, R = UseQueryStateDefaultResult<D>> = UseQueryStateResult<D, R> & UseQuerySubscriptionResult<D>;\n\n/**\n * Helper type to manually type the result\n * of the `useQuery` hook in userland code.\n */\nexport type TypedUseQueryHookResult<ResultType, QueryArg, BaseQuery extends BaseQueryFn, R = UseQueryStateDefaultResult<QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>>> = TypedUseQueryStateResult<ResultType, QueryArg, BaseQuery, R> & TypedUseQuerySubscriptionResult<ResultType, QueryArg, BaseQuery>;\nexport type UseQuerySubscriptionOptions = SubscriptionOptions & {\n  /**\n   * Prevents a query from automatically running.\n   *\n   * @remarks\n   * When `skip` is true (or `skipToken` is passed in as `arg`):\n   *\n   * - **If the query has cached data:**\n   *   * The cached data **will not be used** on the initial load, and will ignore updates from any identical query until the `skip` condition is removed\n   *   * The query will have a status of `uninitialized`\n   *   * If `skip: false` is set after the initial load, the cached result will be used\n   * - **If the query does not have cached data:**\n   *   * The query will have a status of `uninitialized`\n   *   * The query will not exist in the state when viewed with the dev tools\n   *   * The query will not automatically fetch on mount\n   *   * The query will not automatically run when additional components with the same query are added that do run\n   *\n   * @example\n   * ```tsx\n   * // codeblock-meta no-transpile title=\"Skip example\"\n   * const Pokemon = ({ name, skip }: { name: string; skip: boolean }) => {\n   *   const { data, error, status } = useGetPokemonByNameQuery(name, {\n   *     skip,\n   *   });\n   *\n   *   return (\n   *     <div>\n   *       {name} - {status}\n   *     </div>\n   *   );\n   * };\n   * ```\n   */\n  skip?: boolean;\n  /**\n   * Defaults to `false`. This setting allows you to control whether if a cached result is already available, RTK Query will only serve a cached result, or if it should `refetch` when set to `true` or if an adequate amount of time has passed since the last successful query result.\n   * - `false` - Will not cause a query to be performed _unless_ it does not exist yet.\n   * - `true` - Will always refetch when a new subscriber to a query is added. Behaves the same as calling the `refetch` callback or passing `forceRefetch: true` in the action creator.\n   * - `number` - **Value is in seconds**. If a number is provided and there is an existing query in the cache, it will compare the current time vs the last fulfilled timestamp, and only refetch if enough time has elapsed.\n   *\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\n   */\n  refetchOnMountOrArgChange?: boolean | number;\n};\n\n/**\n * A React hook that automatically triggers fetches of data from an endpoint, and 'subscribes' the component to the cached data.\n *\n * The query arg is used as a cache key. Changing the query arg will tell the hook to re-fetch the data if it does not exist in the cache already.\n *\n * Note that this hook does not return a request status or cached data. For that use-case, see [`useQuery`](#usequery) or [`useQueryState`](#usequerystate).\n *\n * #### Features\n *\n * - Automatically triggers requests to retrieve data based on the hook argument and whether cached data exists by default\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met\n */\nexport type UseQuerySubscription<D extends QueryDefinition<any, any, any, any>> = (arg: QueryArgFrom<D> | SkipToken, options?: UseQuerySubscriptionOptions) => UseQuerySubscriptionResult<D>;\nexport type TypedUseQuerySubscription<ResultType, QueryArg, BaseQuery extends BaseQueryFn> = UseQuerySubscription<QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>>;\nexport type UseQuerySubscriptionResult<D extends QueryDefinition<any, any, any, any>> = Pick<QueryActionCreatorResult<D>, 'refetch'>;\n\n/**\n * Helper type to manually type the result\n * of the `useQuerySubscription` hook in userland code.\n */\nexport type TypedUseQuerySubscriptionResult<ResultType, QueryArg, BaseQuery extends BaseQueryFn> = UseQuerySubscriptionResult<QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>>;\nexport type UseLazyQueryLastPromiseInfo<D extends QueryDefinition<any, any, any, any>> = {\n  lastArg: QueryArgFrom<D>;\n};\n\n/**\n * A React hook similar to [`useQuery`](#usequery), but with manual control over when the data fetching occurs.\n *\n * This hook includes the functionality of [`useLazyQuerySubscription`](#uselazyquerysubscription).\n *\n * #### Features\n *\n * - Manual control over firing a request to retrieve data\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\n * - Returns the latest request status and cached data from the Redux store\n * - Re-renders as the request status changes and data becomes available\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met and the fetch has been manually called at least once\n *\n * #### Note\n *\n * When the trigger function returned from a LazyQuery is called, it always initiates a new request to the server even if there is cached data. Set `preferCacheValue`(the second argument to the function) as `true` if you want it to immediately return a cached value if one exists.\n */\nexport type UseLazyQuery<D extends QueryDefinition<any, any, any, any>> = <R extends Record<string, any> = UseQueryStateDefaultResult<D>>(options?: SubscriptionOptions & Omit<UseQueryStateOptions<D, R>, 'skip'>) => [LazyQueryTrigger<D>, UseLazyQueryStateResult<D, R>, UseLazyQueryLastPromiseInfo<D>];\nexport type TypedUseLazyQuery<ResultType, QueryArg, BaseQuery extends BaseQueryFn> = UseLazyQuery<QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>>;\nexport type UseLazyQueryStateResult<D extends QueryDefinition<any, any, any, any>, R = UseQueryStateDefaultResult<D>> = UseQueryStateResult<D, R> & {\n  /**\n   * Resets the hook state to its initial `uninitialized` state.\n   * This will also remove the last result from the cache.\n   */\n  reset: () => void;\n};\n\n/**\n * Helper type to manually type the result\n * of the `useLazyQuery` hook in userland code.\n */\nexport type TypedUseLazyQueryStateResult<ResultType, QueryArg, BaseQuery extends BaseQueryFn, R = UseQueryStateDefaultResult<QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>>> = UseLazyQueryStateResult<QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>, R>;\nexport type LazyQueryTrigger<D extends QueryDefinition<any, any, any, any>> = {\n  /**\n   * Triggers a lazy query.\n   *\n   * By default, this will start a new request even if there is already a value in the cache.\n   * If you want to use the cache value and only start a request if there is no cache value, set the second argument to `true`.\n   *\n   * @remarks\n   * If you need to access the error or success payload immediately after a lazy query, you can chain .unwrap().\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta title=\"Using .unwrap with async await\"\n   * try {\n   *   const payload = await getUserById(1).unwrap();\n   *   console.log('fulfilled', payload)\n   * } catch (error) {\n   *   console.error('rejected', error);\n   * }\n   * ```\n   */\n  (arg: QueryArgFrom<D>, preferCacheValue?: boolean): QueryActionCreatorResult<D>;\n};\nexport type TypedLazyQueryTrigger<ResultType, QueryArg, BaseQuery extends BaseQueryFn> = LazyQueryTrigger<QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>>;\n\n/**\n * A React hook similar to [`useQuerySubscription`](#usequerysubscription), but with manual control over when the data fetching occurs.\n *\n * Note that this hook does not return a request status or cached data. For that use-case, see [`useLazyQuery`](#uselazyquery).\n *\n * #### Features\n *\n * - Manual control over firing a request to retrieve data\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met and the fetch has been manually called at least once\n */\nexport type UseLazyQuerySubscription<D extends QueryDefinition<any, any, any, any>> = (options?: SubscriptionOptions) => readonly [LazyQueryTrigger<D>, QueryArgFrom<D> | UninitializedValue, {\n  reset: () => void;\n}];\nexport type TypedUseLazyQuerySubscription<ResultType, QueryArg, BaseQuery extends BaseQueryFn> = UseLazyQuerySubscription<QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>>;\n\n/**\n * @internal\n */\nexport type QueryStateSelector<R extends Record<string, any>, D extends QueryDefinition<any, any, any, any>> = (state: UseQueryStateDefaultResult<D>) => R;\n\n/**\n * Provides a way to define a strongly-typed version of\n * {@linkcode QueryStateSelector} for use with a specific query.\n * This is useful for scenarios where you want to create a \"pre-typed\"\n * {@linkcode UseQueryStateOptions.selectFromResult | selectFromResult}\n * function.\n *\n * @example\n * <caption>#### __Create a strongly-typed `selectFromResult` selector function__</caption>\n *\n * ```tsx\n * import type { TypedQueryStateSelector } from '@reduxjs/toolkit/query/react'\n * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n *\n * type Post = {\n *   id: number\n *   title: string\n * }\n *\n * type PostsApiResponse = {\n *   posts: Post[]\n *   total: number\n *   skip: number\n *   limit: number\n * }\n *\n * type QueryArgument = number | undefined\n *\n * type BaseQueryFunction = ReturnType<typeof fetchBaseQuery>\n *\n * type SelectedResult = Pick<PostsApiResponse, 'posts'>\n *\n * const postsApiSlice = createApi({\n *   baseQuery: fetchBaseQuery({ baseUrl: 'https://dummyjson.com/posts' }),\n *   reducerPath: 'postsApi',\n *   tagTypes: ['Posts'],\n *   endpoints: (build) => ({\n *     getPosts: build.query<PostsApiResponse, QueryArgument>({\n *       query: (limit = 5) => `?limit=${limit}&select=title`,\n *     }),\n *   }),\n * })\n *\n * const { useGetPostsQuery } = postsApiSlice\n *\n * function PostById({ id }: { id: number }) {\n *   const { post } = useGetPostsQuery(undefined, {\n *     selectFromResult: (state) => ({\n *       post: state.data?.posts.find((post) => post.id === id),\n *     }),\n *   })\n *\n *   return <li>{post?.title}</li>\n * }\n *\n * const EMPTY_ARRAY: Post[] = []\n *\n * const typedSelectFromResult: TypedQueryStateSelector<\n *   PostsApiResponse,\n *   QueryArgument,\n *   BaseQueryFunction,\n *   SelectedResult\n * > = (state) => ({ posts: state.data?.posts ?? EMPTY_ARRAY })\n *\n * function PostsList() {\n *   const { posts } = useGetPostsQuery(undefined, {\n *     selectFromResult: typedSelectFromResult,\n *   })\n *\n *   return (\n *     <div>\n *       <ul>\n *         {posts.map((post) => (\n *           <PostById key={post.id} id={post.id} />\n *         ))}\n *       </ul>\n *     </div>\n *   )\n * }\n * ```\n *\n * @template ResultType - The type of the result `data` returned by the query.\n * @template QueryArgumentType - The type of the argument passed into the query.\n * @template BaseQueryFunctionType - The type of the base query function being used.\n * @template SelectedResultType - The type of the selected result returned by the __`selectFromResult`__ function.\n *\n * @since 2.3.0\n * @public\n */\nexport type TypedQueryStateSelector<ResultType, QueryArgumentType, BaseQueryFunctionType extends BaseQueryFn, SelectedResultType extends Record<string, any> = UseQueryStateDefaultResult<QueryDefinition<QueryArgumentType, BaseQueryFunctionType, string, ResultType, string>>> = QueryStateSelector<SelectedResultType, QueryDefinition<QueryArgumentType, BaseQueryFunctionType, string, ResultType, string>>;\n\n/**\n * A React hook that reads the request status and cached data from the Redux store. The component will re-render as the loading status changes and the data becomes available.\n *\n * Note that this hook does not trigger fetching new data. For that use-case, see [`useQuery`](#usequery) or [`useQuerySubscription`](#usequerysubscription).\n *\n * #### Features\n *\n * - Returns the latest request status and cached data from the Redux store\n * - Re-renders as the request status changes and data becomes available\n */\nexport type UseQueryState<D extends QueryDefinition<any, any, any, any>> = <R extends Record<string, any> = UseQueryStateDefaultResult<D>>(arg: QueryArgFrom<D> | SkipToken, options?: UseQueryStateOptions<D, R>) => UseQueryStateResult<D, R>;\nexport type TypedUseQueryState<ResultType, QueryArg, BaseQuery extends BaseQueryFn> = UseQueryState<QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>>;\n\n/**\n * @internal\n */\nexport type UseQueryStateOptions<D extends QueryDefinition<any, any, any, any>, R extends Record<string, any>> = {\n  /**\n   * Prevents a query from automatically running.\n   *\n   * @remarks\n   * When skip is true:\n   *\n   * - **If the query has cached data:**\n   *   * The cached data **will not be used** on the initial load, and will ignore updates from any identical query until the `skip` condition is removed\n   *   * The query will have a status of `uninitialized`\n   *   * If `skip: false` is set after skipping the initial load, the cached result will be used\n   * - **If the query does not have cached data:**\n   *   * The query will have a status of `uninitialized`\n   *   * The query will not exist in the state when viewed with the dev tools\n   *   * The query will not automatically fetch on mount\n   *   * The query will not automatically run when additional components with the same query are added that do run\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta title=\"Skip example\"\n   * const Pokemon = ({ name, skip }: { name: string; skip: boolean }) => {\n   *   const { data, error, status } = useGetPokemonByNameQuery(name, {\n   *     skip,\n   *   });\n   *\n   *   return (\n   *     <div>\n   *       {name} - {status}\n   *     </div>\n   *   );\n   * };\n   * ```\n   */\n  skip?: boolean;\n  /**\n   * `selectFromResult` allows you to get a specific segment from a query result in a performant manner.\n   * When using this feature, the component will not rerender unless the underlying data of the selected item has changed.\n   * If the selected item is one element in a larger collection, it will disregard changes to elements in the same collection.\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta title=\"Using selectFromResult to extract a single result\"\n   * function PostsList() {\n   *   const { data: posts } = api.useGetPostsQuery();\n   *\n   *   return (\n   *     <ul>\n   *       {posts?.data?.map((post) => (\n   *         <PostById key={post.id} id={post.id} />\n   *       ))}\n   *     </ul>\n   *   );\n   * }\n   *\n   * function PostById({ id }: { id: number }) {\n   *   // Will select the post with the given id, and will only rerender if the given posts data changes\n   *   const { post } = api.useGetPostsQuery(undefined, {\n   *     selectFromResult: ({ data }) => ({ post: data?.find((post) => post.id === id) }),\n   *   });\n   *\n   *   return <li>{post?.name}</li>;\n   * }\n   * ```\n   */\n  selectFromResult?: QueryStateSelector<R, D>;\n};\n\n/**\n * Provides a way to define a \"pre-typed\" version of\n * {@linkcode UseQueryStateOptions} with specific options for a given query.\n * This is particularly useful for setting default query behaviors such as\n * refetching strategies, which can be overridden as needed.\n *\n * @example\n * <caption>#### __Create a `useQuery` hook with default options__</caption>\n *\n * ```ts\n * import type {\n *   SubscriptionOptions,\n *   TypedUseQueryStateOptions,\n * } from '@reduxjs/toolkit/query/react'\n * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n *\n * type Post = {\n *   id: number\n *   name: string\n * }\n *\n * const api = createApi({\n *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n *   tagTypes: ['Post'],\n *   endpoints: (build) => ({\n *     getPosts: build.query<Post[], void>({\n *       query: () => 'posts',\n *     }),\n *   }),\n * })\n *\n * const { useGetPostsQuery } = api\n *\n * export const useGetPostsQueryWithDefaults = <\n *   SelectedResult extends Record<string, any>,\n * >(\n *   overrideOptions: TypedUseQueryStateOptions<\n *     Post[],\n *     void,\n *     ReturnType<typeof fetchBaseQuery>,\n *     SelectedResult\n *   > &\n *     SubscriptionOptions,\n * ) =>\n *   useGetPostsQuery(undefined, {\n *     // Insert default options here\n *\n *     refetchOnMountOrArgChange: true,\n *     refetchOnFocus: true,\n *     ...overrideOptions,\n *   })\n * ```\n *\n * @template ResultType - The type of the result `data` returned by the query.\n * @template QueryArg - The type of the argument passed into the query.\n * @template BaseQuery - The type of the base query function being used.\n * @template SelectedResult - The type of the selected result returned by the __`selectFromResult`__ function.\n *\n * @since 2.2.8\n * @public\n */\nexport type TypedUseQueryStateOptions<ResultType, QueryArg, BaseQuery extends BaseQueryFn, SelectedResult extends Record<string, any> = UseQueryStateDefaultResult<QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>>> = UseQueryStateOptions<QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>, SelectedResult>;\nexport type UseQueryStateResult<_ extends QueryDefinition<any, any, any, any>, R> = TSHelpersNoInfer<R>;\n\n/**\n * Helper type to manually type the result\n * of the `useQueryState` hook in userland code.\n */\nexport type TypedUseQueryStateResult<ResultType, QueryArg, BaseQuery extends BaseQueryFn, R = UseQueryStateDefaultResult<QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>>> = TSHelpersNoInfer<R>;\ntype UseQueryStateBaseResult<D extends QueryDefinition<any, any, any, any>> = QuerySubState<D> & {\n  /**\n   * Where `data` tries to hold data as much as possible, also re-using\n   * data from the last arguments passed into the hook, this property\n   * will always contain the received data from the query, for the current query arguments.\n   */\n  currentData?: ResultTypeFrom<D>;\n  /**\n   * Query has not started yet.\n   */\n  isUninitialized: false;\n  /**\n   * Query is currently loading for the first time. No data yet.\n   */\n  isLoading: false;\n  /**\n   * Query is currently fetching, but might have data from an earlier request.\n   */\n  isFetching: false;\n  /**\n   * Query has data from a successful load.\n   */\n  isSuccess: false;\n  /**\n   * Query is currently in \"error\" state.\n   */\n  isError: false;\n};\ntype UseQueryStateDefaultResult<D extends QueryDefinition<any, any, any, any>> = TSHelpersId<TSHelpersOverride<Extract<UseQueryStateBaseResult<D>, {\n  status: QueryStatus.uninitialized;\n}>, {\n  isUninitialized: true;\n}> | TSHelpersOverride<UseQueryStateBaseResult<D>, {\n  isLoading: true;\n  isFetching: boolean;\n  data: undefined;\n} | ({\n  isSuccess: true;\n  isFetching: true;\n  error: undefined;\n} & Required<Pick<UseQueryStateBaseResult<D>, 'data' | 'fulfilledTimeStamp'>>) | ({\n  isSuccess: true;\n  isFetching: false;\n  error: undefined;\n} & Required<Pick<UseQueryStateBaseResult<D>, 'data' | 'fulfilledTimeStamp' | 'currentData'>>) | ({\n  isError: true;\n} & Required<Pick<UseQueryStateBaseResult<D>, 'error'>>)>> & {\n  /**\n   * @deprecated Included for completeness, but discouraged.\n   * Please use the `isLoading`, `isFetching`, `isSuccess`, `isError`\n   * and `isUninitialized` flags instead\n   */\n  status: QueryStatus;\n};\nexport type LazyInfiniteQueryTrigger<D extends InfiniteQueryDefinition<any, any, any, any, any>> = {\n  /**\n   * Triggers a lazy query.\n   *\n   * By default, this will start a new request even if there is already a value in the cache.\n   * If you want to use the cache value and only start a request if there is no cache value, set the second argument to `true`.\n   *\n   * @remarks\n   * If you need to access the error or success payload immediately after a lazy query, you can chain .unwrap().\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta title=\"Using .unwrap with async await\"\n   * try {\n   *   const payload = await getUserById(1).unwrap();\n   *   console.log('fulfilled', payload)\n   * } catch (error) {\n   *   console.error('rejected', error);\n   * }\n   * ```\n   */\n  (arg: QueryArgFrom<D>, direction: InfiniteQueryDirection): InfiniteQueryActionCreatorResult<D>;\n};\nexport type TypedLazyInfiniteQueryTrigger<ResultType, QueryArg, PageParam, BaseQuery extends BaseQueryFn> = LazyInfiniteQueryTrigger<InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, string, ResultType, string>>;\nexport type UseInfiniteQuerySubscriptionOptions<D extends InfiniteQueryDefinition<any, any, any, any, any>> = SubscriptionOptions & {\n  /**\n   * Prevents a query from automatically running.\n   *\n   * @remarks\n   * When `skip` is true (or `skipToken` is passed in as `arg`):\n   *\n   * - **If the query has cached data:**\n   *   * The cached data **will not be used** on the initial load, and will ignore updates from any identical query until the `skip` condition is removed\n   *   * The query will have a status of `uninitialized`\n   *   * If `skip: false` is set after the initial load, the cached result will be used\n   * - **If the query does not have cached data:**\n   *   * The query will have a status of `uninitialized`\n   *   * The query will not exist in the state when viewed with the dev tools\n   *   * The query will not automatically fetch on mount\n   *   * The query will not automatically run when additional components with the same query are added that do run\n   *\n   * @example\n   * ```tsx\n   * // codeblock-meta no-transpile title=\"Skip example\"\n   * const Pokemon = ({ name, skip }: { name: string; skip: boolean }) => {\n   *   const { data, error, status } = useGetPokemonByNameQuery(name, {\n   *     skip,\n   *   });\n   *\n   *   return (\n   *     <div>\n   *       {name} - {status}\n   *     </div>\n   *   );\n   * };\n   * ```\n   */\n  skip?: boolean;\n  /**\n   * Defaults to `false`. This setting allows you to control whether if a cached result is already available, RTK Query will only serve a cached result, or if it should `refetch` when set to `true` or if an adequate amount of time has passed since the last successful query result.\n   * - `false` - Will not cause a query to be performed _unless_ it does not exist yet.\n   * - `true` - Will always refetch when a new subscriber to a query is added. Behaves the same as calling the `refetch` callback or passing `forceRefetch: true` in the action creator.\n   * - `number` - **Value is in seconds**. If a number is provided and there is an existing query in the cache, it will compare the current time vs the last fulfilled timestamp, and only refetch if enough time has elapsed.\n   *\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\n   */\n  refetchOnMountOrArgChange?: boolean | number;\n  initialPageParam?: PageParamFrom<D>;\n};\nexport type TypedUseInfiniteQuerySubscription<ResultType, QueryArg, PageParam, BaseQuery extends BaseQueryFn> = UseInfiniteQuerySubscription<InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, string, ResultType, string>>;\nexport type UseInfiniteQuerySubscriptionResult<D extends InfiniteQueryDefinition<any, any, any, any, any>> = Pick<InfiniteQueryActionCreatorResult<D>, 'refetch'> & {\n  trigger: LazyInfiniteQueryTrigger<D>;\n  fetchNextPage: () => InfiniteQueryActionCreatorResult<D>;\n  fetchPreviousPage: () => InfiniteQueryActionCreatorResult<D>;\n};\n\n/**\n * Helper type to manually type the result\n * of the `useQuerySubscription` hook in userland code.\n */\nexport type TypedUseInfiniteQuerySubscriptionResult<ResultType, QueryArg, PageParam, BaseQuery extends BaseQueryFn> = UseInfiniteQuerySubscriptionResult<InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, string, ResultType, string>>;\nexport type InfiniteQueryStateSelector<R extends Record<string, any>, D extends InfiniteQueryDefinition<any, any, any, any, any>> = (state: UseInfiniteQueryStateDefaultResult<D>) => R;\nexport type TypedInfiniteQueryStateSelector<ResultType, QueryArg, PageParam, BaseQuery extends BaseQueryFn, SelectedResult extends Record<string, any> = UseInfiniteQueryStateDefaultResult<InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, string, ResultType, string>>> = InfiniteQueryStateSelector<SelectedResult, InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, string, ResultType, string>>;\n\n/**\n * A React hook that automatically triggers fetches of data from an endpoint, 'subscribes' the component to the cached data, and reads the request status and cached data from the Redux store. The component will re-render as the loading status changes and the data becomes available.  Additionally, it will cache multiple \"pages\" worth of responses within a single cache entry, and allows fetching more pages forwards and backwards from the current cached pages.\n *\n * The query arg is used as a cache key. Changing the query arg will tell the hook to re-fetch the data if it does not exist in the cache already, and the hook will return the data for that query arg once it's available.\n *\n *  The `data` field will be a `{pages: Data[], pageParams: PageParam[]}` structure containing all fetched page responses and the corresponding page param values for each page. You may use this to render individual pages, combine all pages into a single infinite list, or other display logic as needed.\n *\n * This hook combines the functionality of both [`useInfiniteQueryState`](#useinfinitequerystate) and [`useInfiniteQuerySubscription`](#useinfinitequerysubscription) together, and is intended to be used in the majority of situations.\n *\n * As with normal query hooks, `skipToken` is a valid argument that will skip the query from executing.\n *\n * By default, the initial request will use the `initialPageParam` value that was defined on the infinite query endpoint. If you want to start from a different value, you can pass `initialPageParam` as part of the hook options to override that initial request value.\n *\n * Use the returned `fetchNextPage` and `fetchPreviousPage` methods on the hook result object to trigger fetches forwards and backwards. These will always calculate the next or previous page param based on the current cached pages and the provided `getNext/PreviousPageParam` callbacks defined in the endpoint.\n *\n *\n * #### Features\n *\n * - Automatically triggers requests to retrieve data based on the hook argument and whether cached data exists by default\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\n * - Caches multiple pages worth of responses, and provides methods to trigger more page fetches forwards and backwards\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met\n * - Returns the latest request status and cached data from the Redux store\n * - Re-renders as the request status changes and data becomes available\n */\nexport type UseInfiniteQuery<D extends InfiniteQueryDefinition<any, any, any, any, any>> = <R extends Record<string, any> = UseInfiniteQueryStateDefaultResult<D>>(arg: InfiniteQueryArgFrom<D> | SkipToken, options?: UseInfiniteQuerySubscriptionOptions<D> & UseInfiniteQueryStateOptions<D, R>) => UseInfiniteQueryHookResult<D, R> & Pick<UseInfiniteQuerySubscriptionResult<D>, 'fetchNextPage' | 'fetchPreviousPage'>;\nexport type TypedUseInfiniteQuery<ResultType, QueryArg, PageParam, BaseQuery extends BaseQueryFn> = UseInfiniteQuery<InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, string, ResultType, string>>;\n\n/**\n * A React hook that reads the request status and cached data from the Redux store. The component will re-render as the loading status changes and the data becomes available.\n *\n * Note that this hook does not trigger fetching new data. For that use-case, see [`useInfiniteQuery`](#useinfinitequery) or [`useInfiniteQuerySubscription`](#useinfinitequerysubscription).\n *\n * #### Features\n *\n * - Returns the latest request status and cached data from the Redux store\n * - Re-renders as the request status changes and data becomes available\n */\nexport type UseInfiniteQueryState<D extends InfiniteQueryDefinition<any, any, any, any, any>> = <R extends Record<string, any> = UseInfiniteQueryStateDefaultResult<D>>(arg: InfiniteQueryArgFrom<D> | SkipToken, options?: UseInfiniteQueryStateOptions<D, R>) => UseInfiniteQueryStateResult<D, R>;\nexport type TypedUseInfiniteQueryState<ResultType, QueryArg, PageParam, BaseQuery extends BaseQueryFn> = UseInfiniteQueryState<InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, string, ResultType, string>>;\n\n/**\n * A React hook that automatically triggers fetches of data from an endpoint, and 'subscribes' the component to the cached data. Additionally, it will cache multiple \"pages\" worth of responses within a single cache entry, and allows fetching more pages forwards and backwards from the current cached pages.\n *\n * The query arg is used as a cache key. Changing the query arg will tell the hook to re-fetch the data if it does not exist in the cache already.\n *\n * Note that this hook does not return a request status or cached data. For that use-case, see [`useInfiniteQuery`](#useinfinitequery) or [`useInfiniteQueryState`](#useinfinitequerystate).\n *\n * #### Features\n *\n * - Automatically triggers requests to retrieve data based on the hook argument and whether cached data exists by default\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\n * - Caches multiple pages worth of responses, and provides methods to trigger more page fetches forwards and backwards\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met\n */\nexport type UseInfiniteQuerySubscription<D extends InfiniteQueryDefinition<any, any, any, any, any>> = (arg: InfiniteQueryArgFrom<D> | SkipToken, options?: UseInfiniteQuerySubscriptionOptions<D>) => UseInfiniteQuerySubscriptionResult<D>;\nexport type UseInfiniteQueryHookResult<D extends InfiniteQueryDefinition<any, any, any, any, any>, R = UseInfiniteQueryStateDefaultResult<D>> = UseInfiniteQueryStateResult<D, R> & Pick<UseInfiniteQuerySubscriptionResult<D>, 'refetch'>;\nexport type TypedUseInfiniteQueryHookResult<ResultType, QueryArg, PageParam, BaseQuery extends BaseQueryFn, R extends Record<string, any> = UseInfiniteQueryStateDefaultResult<InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, string, ResultType, string>>> = UseInfiniteQueryHookResult<InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, string, ResultType, string>, R>;\nexport type UseInfiniteQueryStateOptions<D extends InfiniteQueryDefinition<any, any, any, any, any>, R extends Record<string, any>> = {\n  /**\n   * Prevents a query from automatically running.\n   *\n   * @remarks\n   * When skip is true:\n   *\n   * - **If the query has cached data:**\n   *   * The cached data **will not be used** on the initial load, and will ignore updates from any identical query until the `skip` condition is removed\n   *   * The query will have a status of `uninitialized`\n   *   * If `skip: false` is set after skipping the initial load, the cached result will be used\n   * - **If the query does not have cached data:**\n   *   * The query will have a status of `uninitialized`\n   *   * The query will not exist in the state when viewed with the dev tools\n   *   * The query will not automatically fetch on mount\n   *   * The query will not automatically run when additional components with the same query are added that do run\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta title=\"Skip example\"\n   * const Pokemon = ({ name, skip }: { name: string; skip: boolean }) => {\n   *   const { data, error, status } = useGetPokemonByNameQuery(name, {\n   *     skip,\n   *   });\n   *\n   *   return (\n   *     <div>\n   *       {name} - {status}\n   *     </div>\n   *   );\n   * };\n   * ```\n   */\n  skip?: boolean;\n  /**\n   * `selectFromResult` allows you to get a specific segment from a query result in a performant manner.\n   * When using this feature, the component will not rerender unless the underlying data of the selected item has changed.\n   * If the selected item is one element in a larger collection, it will disregard changes to elements in the same collection.\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta title=\"Using selectFromResult to extract a single result\"\n   * function PostsList() {\n   *   const { data: posts } = api.useGetPostsQuery();\n   *\n   *   return (\n   *     <ul>\n   *       {posts?.data?.map((post) => (\n   *         <PostById key={post.id} id={post.id} />\n   *       ))}\n   *     </ul>\n   *   );\n   * }\n   *\n   * function PostById({ id }: { id: number }) {\n   *   // Will select the post with the given id, and will only rerender if the given posts data changes\n   *   const { post } = api.useGetPostsQuery(undefined, {\n   *     selectFromResult: ({ data }) => ({ post: data?.find((post) => post.id === id) }),\n   *   });\n   *\n   *   return <li>{post?.name}</li>;\n   * }\n   * ```\n   */\n  selectFromResult?: InfiniteQueryStateSelector<R, D>;\n};\nexport type TypedUseInfiniteQueryStateOptions<ResultType, QueryArg, PageParam, BaseQuery extends BaseQueryFn, SelectedResult extends Record<string, any> = UseInfiniteQueryStateDefaultResult<InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, string, ResultType, string>>> = UseInfiniteQueryStateOptions<InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, string, ResultType, string>, SelectedResult>;\nexport type UseInfiniteQueryStateResult<D extends InfiniteQueryDefinition<any, any, any, any, any>, R = UseInfiniteQueryStateDefaultResult<D>> = TSHelpersNoInfer<R>;\nexport type TypedUseInfiniteQueryStateResult<ResultType, QueryArg, PageParam, BaseQuery extends BaseQueryFn, R = UseInfiniteQueryStateDefaultResult<InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, string, ResultType, string>>> = UseInfiniteQueryStateResult<InfiniteQueryDefinition<QueryArg, PageParam, BaseQuery, string, ResultType, string>, R>;\ntype UseInfiniteQueryStateBaseResult<D extends InfiniteQueryDefinition<any, any, any, any, any>> = InfiniteQuerySubState<D> & {\n  /**\n   * Where `data` tries to hold data as much as possible, also re-using\n   * data from the last arguments passed into the hook, this property\n   * will always contain the received data from the query, for the current query arguments.\n   */\n  currentData?: InfiniteData<ResultTypeFrom<D>, PageParamFrom<D>>;\n  /**\n   * Query has not started yet.\n   */\n  isUninitialized: false;\n  /**\n   * Query is currently loading for the first time. No data yet.\n   */\n  isLoading: false;\n  /**\n   * Query is currently fetching, but might have data from an earlier request.\n   */\n  isFetching: false;\n  /**\n   * Query has data from a successful load.\n   */\n  isSuccess: false;\n  /**\n   * Query is currently in \"error\" state.\n   */\n  isError: false;\n  hasNextPage: false;\n  hasPreviousPage: false;\n  isFetchingNextPage: false;\n  isFetchingPreviousPage: false;\n};\ntype UseInfiniteQueryStateDefaultResult<D extends InfiniteQueryDefinition<any, any, any, any, any>> = TSHelpersId<TSHelpersOverride<Extract<UseInfiniteQueryStateBaseResult<D>, {\n  status: QueryStatus.uninitialized;\n}>, {\n  isUninitialized: true;\n}> | TSHelpersOverride<UseInfiniteQueryStateBaseResult<D>, {\n  isLoading: true;\n  isFetching: boolean;\n  data: undefined;\n} | ({\n  isSuccess: true;\n  isFetching: true;\n  error: undefined;\n} & Required<Pick<UseInfiniteQueryStateBaseResult<D>, 'data' | 'fulfilledTimeStamp'>>) | ({\n  isSuccess: true;\n  isFetching: false;\n  error: undefined;\n} & Required<Pick<UseInfiniteQueryStateBaseResult<D>, 'data' | 'fulfilledTimeStamp' | 'currentData'>>) | ({\n  isError: true;\n} & Required<Pick<UseInfiniteQueryStateBaseResult<D>, 'error'>>)>> & {\n  /**\n   * @deprecated Included for completeness, but discouraged.\n   * Please use the `isLoading`, `isFetching`, `isSuccess`, `isError`\n   * and `isUninitialized` flags instead\n   */\n  status: QueryStatus;\n};\nexport type MutationStateSelector<R extends Record<string, any>, D extends MutationDefinition<any, any, any, any>> = (state: MutationResultSelectorResult<D>) => R;\nexport type UseMutationStateOptions<D extends MutationDefinition<any, any, any, any>, R extends Record<string, any>> = {\n  selectFromResult?: MutationStateSelector<R, D>;\n  fixedCacheKey?: string;\n};\nexport type UseMutationStateResult<D extends MutationDefinition<any, any, any, any>, R> = TSHelpersNoInfer<R> & {\n  originalArgs?: QueryArgFrom<D>;\n  /**\n   * Resets the hook state to its initial `uninitialized` state.\n   * This will also remove the last result from the cache.\n   */\n  reset: () => void;\n};\n\n/**\n * Helper type to manually type the result\n * of the `useMutation` hook in userland code.\n */\nexport type TypedUseMutationResult<ResultType, QueryArg, BaseQuery extends BaseQueryFn, R = MutationResultSelectorResult<MutationDefinition<QueryArg, BaseQuery, string, ResultType, string>>> = UseMutationStateResult<MutationDefinition<QueryArg, BaseQuery, string, ResultType, string>, R>;\n\n/**\n * A React hook that lets you trigger an update request for a given endpoint, and subscribes the component to read the request status from the Redux store. The component will re-render as the loading status changes.\n *\n * #### Features\n *\n * - Manual control over firing a request to alter data on the server or possibly invalidate the cache\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\n * - Returns the latest request status and cached data from the Redux store\n * - Re-renders as the request status changes and data becomes available\n */\nexport type UseMutation<D extends MutationDefinition<any, any, any, any>> = <R extends Record<string, any> = MutationResultSelectorResult<D>>(options?: UseMutationStateOptions<D, R>) => readonly [MutationTrigger<D>, UseMutationStateResult<D, R>];\nexport type TypedUseMutation<ResultType, QueryArg, BaseQuery extends BaseQueryFn> = UseMutation<MutationDefinition<QueryArg, BaseQuery, string, ResultType, string>>;\nexport type MutationTrigger<D extends MutationDefinition<any, any, any, any>> = {\n  /**\n   * Triggers the mutation and returns a Promise.\n   * @remarks\n   * If you need to access the error or success payload immediately after a mutation, you can chain .unwrap().\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta title=\"Using .unwrap with async await\"\n   * try {\n   *   const payload = await addPost({ id: 1, name: 'Example' }).unwrap();\n   *   console.log('fulfilled', payload)\n   * } catch (error) {\n   *   console.error('rejected', error);\n   * }\n   * ```\n   */\n  (arg: QueryArgFrom<D>): MutationActionCreatorResult<D>;\n};\nexport type TypedMutationTrigger<ResultType, QueryArg, BaseQuery extends BaseQueryFn> = MutationTrigger<MutationDefinition<QueryArg, BaseQuery, string, ResultType, string>>;\n\n/**\n * Wrapper around `defaultQueryStateSelector` to be used in `useQuery`.\n * We want the initial render to already come back with\n * `{ isUninitialized: false, isFetching: true, isLoading: true }`\n * to prevent that the library user has to do an additional check for `isUninitialized`/\n */\nconst noPendingQueryStateSelector: QueryStateSelector<any, any> = selected => {\n  if (selected.isUninitialized) {\n    return {\n      ...selected,\n      isUninitialized: false,\n      isFetching: true,\n      isLoading: selected.data !== undefined ? false : true,\n      status: QueryStatus.pending\n    } as any;\n  }\n  return selected;\n};\nfunction pick<T, K extends keyof T>(obj: T, ...keys: K[]): Pick<T, K> {\n  const ret: any = {};\n  keys.forEach(key => {\n    ret[key] = obj[key];\n  });\n  return ret;\n}\nconst COMMON_HOOK_DEBUG_FIELDS = ['data', 'status', 'isLoading', 'isSuccess', 'isError', 'error'] as const;\ntype GenericPrefetchThunk = (endpointName: any, arg: any, options: PrefetchOptions) => ThunkAction<void, any, any, UnknownAction>;\n\n/**\n *\n * @param opts.api - An API with defined endpoints to create hooks for\n * @param opts.moduleOptions.batch - The version of the `batchedUpdates` function to be used\n * @param opts.moduleOptions.useDispatch - The version of the `useDispatch` hook to be used\n * @param opts.moduleOptions.useSelector - The version of the `useSelector` hook to be used\n * @returns An object containing functions to generate hooks based on an endpoint\n */\nexport function buildHooks<Definitions extends EndpointDefinitions>({\n  api,\n  moduleOptions: {\n    batch,\n    hooks: {\n      useDispatch,\n      useSelector,\n      useStore\n    },\n    unstable__sideEffectsInRender,\n    createSelector\n  },\n  serializeQueryArgs,\n  context\n}: {\n  api: Api<any, Definitions, any, any, CoreModule>;\n  moduleOptions: Required<ReactHooksModuleOptions>;\n  serializeQueryArgs: SerializeQueryArgs<any>;\n  context: ApiContext<Definitions>;\n}) {\n  const usePossiblyImmediateEffect: (effect: () => void | undefined, deps?: DependencyList) => void = unstable__sideEffectsInRender ? cb => cb() : useEffect;\n  return {\n    buildQueryHooks,\n    buildInfiniteQueryHooks,\n    buildMutationHook,\n    usePrefetch\n  };\n  function queryStatePreSelector(currentState: QueryResultSelectorResult<any>, lastResult: UseQueryStateDefaultResult<any> | undefined, queryArgs: any): UseQueryStateDefaultResult<any> {\n    // if we had a last result and the current result is uninitialized,\n    // we might have called `api.util.resetApiState`\n    // in this case, reset the hook\n    if (lastResult?.endpointName && currentState.isUninitialized) {\n      const {\n        endpointName\n      } = lastResult;\n      const endpointDefinition = context.endpointDefinitions[endpointName];\n      if (queryArgs !== skipToken && serializeQueryArgs({\n        queryArgs: lastResult.originalArgs,\n        endpointDefinition,\n        endpointName\n      }) === serializeQueryArgs({\n        queryArgs,\n        endpointDefinition,\n        endpointName\n      })) lastResult = undefined;\n    }\n\n    // data is the last known good request result we have tracked - or if none has been tracked yet the last good result for the current args\n    let data = currentState.isSuccess ? currentState.data : lastResult?.data;\n    if (data === undefined) data = currentState.data;\n    const hasData = data !== undefined;\n\n    // isFetching = true any time a request is in flight\n    const isFetching = currentState.isLoading;\n\n    // isLoading = true only when loading while no data is present yet (initial load with no data in the cache)\n    const isLoading = (!lastResult || lastResult.isLoading || lastResult.isUninitialized) && !hasData && isFetching;\n\n    // isSuccess = true when data is present and we're not refetching after an error.\n    // That includes cases where the _current_ item is either actively\n    // fetching or about to fetch due to an uninitialized entry.\n    const isSuccess = currentState.isSuccess || hasData && (isFetching && !lastResult?.isError || currentState.isUninitialized);\n    return {\n      ...currentState,\n      data,\n      currentData: currentState.data,\n      isFetching,\n      isLoading,\n      isSuccess\n    } as UseQueryStateDefaultResult<any>;\n  }\n  function infiniteQueryStatePreSelector(currentState: InfiniteQueryResultSelectorResult<any>, lastResult: UseInfiniteQueryStateDefaultResult<any> | undefined, queryArgs: any): UseInfiniteQueryStateDefaultResult<any> {\n    // if we had a last result and the current result is uninitialized,\n    // we might have called `api.util.resetApiState`\n    // in this case, reset the hook\n    if (lastResult?.endpointName && currentState.isUninitialized) {\n      const {\n        endpointName\n      } = lastResult;\n      const endpointDefinition = context.endpointDefinitions[endpointName];\n      if (queryArgs !== skipToken && serializeQueryArgs({\n        queryArgs: lastResult.originalArgs,\n        endpointDefinition,\n        endpointName\n      }) === serializeQueryArgs({\n        queryArgs,\n        endpointDefinition,\n        endpointName\n      })) lastResult = undefined;\n    }\n\n    // data is the last known good request result we have tracked - or if none has been tracked yet the last good result for the current args\n    let data = currentState.isSuccess ? currentState.data : lastResult?.data;\n    if (data === undefined) data = currentState.data;\n    const hasData = data !== undefined;\n\n    // isFetching = true any time a request is in flight\n    const isFetching = currentState.isLoading;\n    // isLoading = true only when loading while no data is present yet (initial load with no data in the cache)\n    const isLoading = (!lastResult || lastResult.isLoading || lastResult.isUninitialized) && !hasData && isFetching;\n    // isSuccess = true when data is present\n    const isSuccess = currentState.isSuccess || isFetching && hasData;\n    return {\n      ...currentState,\n      data,\n      currentData: currentState.data,\n      isFetching,\n      isLoading,\n      isSuccess\n    } as UseInfiniteQueryStateDefaultResult<any>;\n  }\n  function usePrefetch<EndpointName extends QueryKeys<Definitions>>(endpointName: EndpointName, defaultOptions?: PrefetchOptions) {\n    const dispatch = useDispatch<ThunkDispatch<any, any, UnknownAction>>();\n    const stableDefaultOptions = useShallowStableValue(defaultOptions);\n    return useCallback((arg: any, options?: PrefetchOptions) => dispatch((api.util.prefetch as GenericPrefetchThunk)(endpointName, arg, {\n      ...stableDefaultOptions,\n      ...options\n    })), [endpointName, dispatch, stableDefaultOptions]);\n  }\n  function useQuerySubscriptionCommonImpl<T extends QueryActionCreatorResult<any> | InfiniteQueryActionCreatorResult<any>>(endpointName: string, arg: unknown | SkipToken, {\n    refetchOnReconnect,\n    refetchOnFocus,\n    refetchOnMountOrArgChange,\n    skip = false,\n    pollingInterval = 0,\n    skipPollingIfUnfocused = false,\n    ...rest\n  }: UseQuerySubscriptionOptions = {}) {\n    const {\n      initiate\n    } = api.endpoints[endpointName] as ApiEndpointQuery<QueryDefinition<any, any, any, any, any>, Definitions>;\n    const dispatch = useDispatch<ThunkDispatch<any, any, UnknownAction>>();\n\n    // TODO: Change this to `useRef<SubscriptionSelectors>(undefined)` after upgrading to React 19.\n    const subscriptionSelectorsRef = useRef<SubscriptionSelectors | undefined>(undefined);\n    if (!subscriptionSelectorsRef.current) {\n      const returnedValue = dispatch(api.internalActions.internal_getRTKQSubscriptions());\n      if (process.env.NODE_ENV !== 'production') {\n        if (typeof returnedValue !== 'object' || typeof returnedValue?.type === 'string') {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(37) : `Warning: Middleware for RTK-Query API at reducerPath \"${api.reducerPath}\" has not been added to the store.\n    You must add the middleware for RTK-Query to function correctly!`);\n        }\n      }\n      subscriptionSelectorsRef.current = returnedValue as unknown as SubscriptionSelectors;\n    }\n    const stableArg = useStableQueryArgs(skip ? skipToken : arg,\n    // Even if the user provided a per-endpoint `serializeQueryArgs` with\n    // a consistent return value, _here_ we want to use the default behavior\n    // so we can tell if _anything_ actually changed. Otherwise, we can end up\n    // with a case where the query args did change but the serialization doesn't,\n    // and then we never try to initiate a refetch.\n    defaultSerializeQueryArgs, context.endpointDefinitions[endpointName], endpointName);\n    const stableSubscriptionOptions = useShallowStableValue({\n      refetchOnReconnect,\n      refetchOnFocus,\n      pollingInterval,\n      skipPollingIfUnfocused\n    });\n    const initialPageParam = (rest as UseInfiniteQuerySubscriptionOptions<any>).initialPageParam;\n    const stableInitialPageParam = useShallowStableValue(initialPageParam);\n\n    /**\n     * @todo Change this to `useRef<QueryActionCreatorResult<any>>(undefined)` after upgrading to React 19.\n     */\n    const promiseRef = useRef<T | undefined>(undefined);\n    let {\n      queryCacheKey,\n      requestId\n    } = promiseRef.current || {};\n\n    // HACK We've saved the middleware subscription lookup callbacks into a ref,\n    // so we can directly check here if the subscription exists for this query.\n    let currentRenderHasSubscription = false;\n    if (queryCacheKey && requestId) {\n      currentRenderHasSubscription = subscriptionSelectorsRef.current.isRequestSubscribed(queryCacheKey, requestId);\n    }\n    const subscriptionRemoved = !currentRenderHasSubscription && promiseRef.current !== undefined;\n    usePossiblyImmediateEffect((): void | undefined => {\n      if (subscriptionRemoved) {\n        promiseRef.current = undefined;\n      }\n    }, [subscriptionRemoved]);\n    usePossiblyImmediateEffect((): void | undefined => {\n      const lastPromise = promiseRef.current;\n      if (typeof process !== 'undefined' && process.env.NODE_ENV === 'removeMeOnCompilation') {\n        // this is only present to enforce the rule of hooks to keep `isSubscribed` in the dependency array\n        console.log(subscriptionRemoved);\n      }\n      if (stableArg === skipToken) {\n        lastPromise?.unsubscribe();\n        promiseRef.current = undefined;\n        return;\n      }\n      const lastSubscriptionOptions = promiseRef.current?.subscriptionOptions;\n      if (!lastPromise || lastPromise.arg !== stableArg) {\n        lastPromise?.unsubscribe();\n        const promise = dispatch(initiate(stableArg, {\n          subscriptionOptions: stableSubscriptionOptions,\n          forceRefetch: refetchOnMountOrArgChange,\n          ...(isInfiniteQueryDefinition(context.endpointDefinitions[endpointName]) ? {\n            initialPageParam: stableInitialPageParam\n          } : {})\n        }));\n        promiseRef.current = promise as T;\n      } else if (stableSubscriptionOptions !== lastSubscriptionOptions) {\n        lastPromise.updateSubscriptionOptions(stableSubscriptionOptions);\n      }\n    }, [dispatch, initiate, refetchOnMountOrArgChange, stableArg, stableSubscriptionOptions, subscriptionRemoved, stableInitialPageParam, endpointName]);\n    return [promiseRef, dispatch, initiate, stableSubscriptionOptions] as const;\n  }\n  function buildUseQueryState(endpointName: string, preSelector: typeof queryStatePreSelector | typeof infiniteQueryStatePreSelector) {\n    const useQueryState = (arg: any, {\n      skip = false,\n      selectFromResult\n    }: UseQueryStateOptions<any, any> | UseInfiniteQueryStateOptions<any, any> = {}) => {\n      const {\n        select\n      } = api.endpoints[endpointName] as ApiEndpointQuery<QueryDefinition<any, any, any, any, any>, Definitions>;\n      const stableArg = useStableQueryArgs(skip ? skipToken : arg, serializeQueryArgs, context.endpointDefinitions[endpointName], endpointName);\n      type ApiRootState = Parameters<ReturnType<typeof select>>[0];\n      const lastValue = useRef<any>(undefined);\n      const selectDefaultResult: Selector<ApiRootState, any, [any]> = useMemo(() =>\n      // Normally ts-ignores are bad and should be avoided, but we're\n      // already casting this selector to be `Selector<any>` anyway,\n      // so the inconsistencies don't matter here\n      // @ts-ignore\n      createSelector([\n      // @ts-ignore\n      select(stableArg), (_: ApiRootState, lastResult: any) => lastResult, (_: ApiRootState) => stableArg], preSelector, {\n        memoizeOptions: {\n          resultEqualityCheck: shallowEqual\n        }\n      }), [select, stableArg]);\n      const querySelector: Selector<ApiRootState, any, [any]> = useMemo(() => selectFromResult ? createSelector([selectDefaultResult], selectFromResult, {\n        devModeChecks: {\n          identityFunctionCheck: 'never'\n        }\n      }) : selectDefaultResult, [selectDefaultResult, selectFromResult]);\n      const currentState = useSelector((state: RootState<Definitions, any, any>) => querySelector(state, lastValue.current), shallowEqual);\n      const store = useStore<RootState<Definitions, any, any>>();\n      const newLastValue = selectDefaultResult(store.getState(), lastValue.current);\n      useIsomorphicLayoutEffect(() => {\n        lastValue.current = newLastValue;\n      }, [newLastValue]);\n      return currentState;\n    };\n    return useQueryState;\n  }\n  function usePromiseRefUnsubscribeOnUnmount(promiseRef: React.RefObject<{\n    unsubscribe?: () => void;\n  } | undefined>) {\n    useEffect(() => {\n      return () => {\n        promiseRef.current?.unsubscribe?.()\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        ;\n        (promiseRef.current as any) = undefined;\n      };\n    }, [promiseRef]);\n  }\n  function refetchOrErrorIfUnmounted<T extends QueryActionCreatorResult<any> | InfiniteQueryActionCreatorResult<any>>(promiseRef: React.RefObject<T | undefined>): T {\n    if (!promiseRef.current) throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(38) : 'Cannot refetch a query that has not been started yet.');\n    return promiseRef.current.refetch() as T;\n  }\n  function buildQueryHooks(endpointName: string): QueryHooks<any> {\n    const useQuerySubscription: UseQuerySubscription<any> = (arg: any, options = {}) => {\n      const [promiseRef] = useQuerySubscriptionCommonImpl<QueryActionCreatorResult<any>>(endpointName, arg, options);\n      usePromiseRefUnsubscribeOnUnmount(promiseRef);\n      return useMemo(() => ({\n        /**\n         * A method to manually refetch data for the query\n         */\n        refetch: () => refetchOrErrorIfUnmounted(promiseRef)\n      }), [promiseRef]);\n    };\n    const useLazyQuerySubscription: UseLazyQuerySubscription<any> = ({\n      refetchOnReconnect,\n      refetchOnFocus,\n      pollingInterval = 0,\n      skipPollingIfUnfocused = false\n    } = {}) => {\n      const {\n        initiate\n      } = api.endpoints[endpointName] as ApiEndpointQuery<QueryDefinition<any, any, any, any, any>, Definitions>;\n      const dispatch = useDispatch<ThunkDispatch<any, any, UnknownAction>>();\n      const [arg, setArg] = useState<any>(UNINITIALIZED_VALUE);\n\n      // TODO: Change this to `useRef<QueryActionCreatorResult<any>>(undefined)` after upgrading to React 19.\n      /**\n       * @todo Change this to `useRef<QueryActionCreatorResult<any>>(undefined)` after upgrading to React 19.\n       */\n      const promiseRef = useRef<QueryActionCreatorResult<any> | undefined>(undefined);\n      const stableSubscriptionOptions = useShallowStableValue({\n        refetchOnReconnect,\n        refetchOnFocus,\n        pollingInterval,\n        skipPollingIfUnfocused\n      });\n      usePossiblyImmediateEffect(() => {\n        const lastSubscriptionOptions = promiseRef.current?.subscriptionOptions;\n        if (stableSubscriptionOptions !== lastSubscriptionOptions) {\n          promiseRef.current?.updateSubscriptionOptions(stableSubscriptionOptions);\n        }\n      }, [stableSubscriptionOptions]);\n      const subscriptionOptionsRef = useRef(stableSubscriptionOptions);\n      usePossiblyImmediateEffect(() => {\n        subscriptionOptionsRef.current = stableSubscriptionOptions;\n      }, [stableSubscriptionOptions]);\n      const trigger = useCallback(function (arg: any, preferCacheValue = false) {\n        let promise: QueryActionCreatorResult<any>;\n        batch(() => {\n          promiseRef.current?.unsubscribe();\n          promiseRef.current = promise = dispatch(initiate(arg, {\n            subscriptionOptions: subscriptionOptionsRef.current,\n            forceRefetch: !preferCacheValue\n          }));\n          setArg(arg);\n        });\n        return promise!;\n      }, [dispatch, initiate]);\n      const reset = useCallback(() => {\n        if (promiseRef.current?.queryCacheKey) {\n          dispatch(api.internalActions.removeQueryResult({\n            queryCacheKey: promiseRef.current?.queryCacheKey as QueryCacheKey\n          }));\n        }\n      }, [dispatch]);\n\n      /* cleanup on unmount */\n      useEffect(() => {\n        return () => {\n          promiseRef?.current?.unsubscribe();\n        };\n      }, []);\n\n      /* if \"cleanup on unmount\" was triggered from a fast refresh, we want to reinstate the query */\n      useEffect(() => {\n        if (arg !== UNINITIALIZED_VALUE && !promiseRef.current) {\n          trigger(arg, true);\n        }\n      }, [arg, trigger]);\n      return useMemo(() => [trigger, arg, {\n        reset\n      }] as const, [trigger, arg, reset]);\n    };\n    const useQueryState: UseQueryState<any> = buildUseQueryState(endpointName, queryStatePreSelector);\n    return {\n      useQueryState,\n      useQuerySubscription,\n      useLazyQuerySubscription,\n      useLazyQuery(options) {\n        const [trigger, arg, {\n          reset\n        }] = useLazyQuerySubscription(options);\n        const queryStateResults = useQueryState(arg, {\n          ...options,\n          skip: arg === UNINITIALIZED_VALUE\n        });\n        const info = useMemo(() => ({\n          lastArg: arg\n        }), [arg]);\n        return useMemo(() => [trigger, {\n          ...queryStateResults,\n          reset\n        }, info], [trigger, queryStateResults, reset, info]);\n      },\n      useQuery(arg, options) {\n        const querySubscriptionResults = useQuerySubscription(arg, options);\n        const queryStateResults = useQueryState(arg, {\n          selectFromResult: arg === skipToken || options?.skip ? undefined : noPendingQueryStateSelector,\n          ...options\n        });\n        const debugValue = pick(queryStateResults, ...COMMON_HOOK_DEBUG_FIELDS);\n        useDebugValue(debugValue);\n        return useMemo(() => ({\n          ...queryStateResults,\n          ...querySubscriptionResults\n        }), [queryStateResults, querySubscriptionResults]);\n      }\n    };\n  }\n  function buildInfiniteQueryHooks(endpointName: string): InfiniteQueryHooks<any> {\n    const useInfiniteQuerySubscription: UseInfiniteQuerySubscription<any> = (arg: any, options = {}) => {\n      const [promiseRef, dispatch, initiate, stableSubscriptionOptions] = useQuerySubscriptionCommonImpl<InfiniteQueryActionCreatorResult<any>>(endpointName, arg, options);\n      const subscriptionOptionsRef = useRef(stableSubscriptionOptions);\n      usePossiblyImmediateEffect(() => {\n        subscriptionOptionsRef.current = stableSubscriptionOptions;\n      }, [stableSubscriptionOptions]);\n      const trigger: LazyInfiniteQueryTrigger<any> = useCallback(function (arg: unknown, direction: 'forward' | 'backward') {\n        let promise: InfiniteQueryActionCreatorResult<any>;\n        batch(() => {\n          promiseRef.current?.unsubscribe();\n          promiseRef.current = promise = dispatch((initiate as StartInfiniteQueryActionCreator<any>)(arg, {\n            subscriptionOptions: subscriptionOptionsRef.current,\n            direction\n          }));\n        });\n        return promise!;\n      }, [promiseRef, dispatch, initiate]);\n      usePromiseRefUnsubscribeOnUnmount(promiseRef);\n      const stableArg = useStableQueryArgs(options.skip ? skipToken : arg,\n      // Even if the user provided a per-endpoint `serializeQueryArgs` with\n      // a consistent return value, _here_ we want to use the default behavior\n      // so we can tell if _anything_ actually changed. Otherwise, we can end up\n      // with a case where the query args did change but the serialization doesn't,\n      // and then we never try to initiate a refetch.\n      defaultSerializeQueryArgs, context.endpointDefinitions[endpointName], endpointName);\n      const refetch = useCallback(() => refetchOrErrorIfUnmounted(promiseRef), [promiseRef]);\n      return useMemo(() => {\n        const fetchNextPage = () => {\n          return trigger(stableArg, 'forward');\n        };\n        const fetchPreviousPage = () => {\n          return trigger(stableArg, 'backward');\n        };\n        return {\n          trigger,\n          /**\n           * A method to manually refetch data for the query\n           */\n          refetch,\n          fetchNextPage,\n          fetchPreviousPage\n        };\n      }, [refetch, trigger, stableArg]);\n    };\n    const useInfiniteQueryState: UseInfiniteQueryState<any> = buildUseQueryState(endpointName, infiniteQueryStatePreSelector);\n    return {\n      useInfiniteQueryState,\n      useInfiniteQuerySubscription,\n      useInfiniteQuery(arg, options) {\n        const {\n          refetch,\n          fetchNextPage,\n          fetchPreviousPage\n        } = useInfiniteQuerySubscription(arg, options);\n        const queryStateResults = useInfiniteQueryState(arg, {\n          selectFromResult: arg === skipToken || options?.skip ? undefined : noPendingQueryStateSelector,\n          ...options\n        });\n        const debugValue = pick(queryStateResults, ...COMMON_HOOK_DEBUG_FIELDS, 'hasNextPage', 'hasPreviousPage');\n        useDebugValue(debugValue);\n        return useMemo(() => ({\n          ...queryStateResults,\n          fetchNextPage,\n          fetchPreviousPage,\n          refetch\n        }), [queryStateResults, fetchNextPage, fetchPreviousPage, refetch]);\n      }\n    };\n  }\n  function buildMutationHook(name: string): UseMutation<any> {\n    return ({\n      selectFromResult,\n      fixedCacheKey\n    } = {}) => {\n      const {\n        select,\n        initiate\n      } = api.endpoints[name] as ApiEndpointMutation<MutationDefinition<any, any, any, any, any>, Definitions>;\n      const dispatch = useDispatch<ThunkDispatch<any, any, UnknownAction>>();\n      const [promise, setPromise] = useState<MutationActionCreatorResult<any>>();\n      useEffect(() => () => {\n        if (!promise?.arg.fixedCacheKey) {\n          promise?.reset();\n        }\n      }, [promise]);\n      const triggerMutation = useCallback(function (arg: Parameters<typeof initiate>['0']) {\n        const promise = dispatch(initiate(arg, {\n          fixedCacheKey\n        }));\n        setPromise(promise);\n        return promise;\n      }, [dispatch, initiate, fixedCacheKey]);\n      const {\n        requestId\n      } = promise || {};\n      const selectDefaultResult = useMemo(() => select({\n        fixedCacheKey,\n        requestId: promise?.requestId\n      }), [fixedCacheKey, promise, select]);\n      const mutationSelector = useMemo((): Selector<RootState<Definitions, any, any>, any> => selectFromResult ? createSelector([selectDefaultResult], selectFromResult) : selectDefaultResult, [selectFromResult, selectDefaultResult]);\n      const currentState = useSelector(mutationSelector, shallowEqual);\n      const originalArgs = fixedCacheKey == null ? promise?.arg.originalArgs : undefined;\n      const reset = useCallback(() => {\n        batch(() => {\n          if (promise) {\n            setPromise(undefined);\n          }\n          if (fixedCacheKey) {\n            dispatch(api.internalActions.removeMutationResult({\n              requestId,\n              fixedCacheKey\n            }));\n          }\n        });\n      }, [dispatch, fixedCacheKey, promise, requestId]);\n      const debugValue = pick(currentState, ...COMMON_HOOK_DEBUG_FIELDS, 'endpointName');\n      useDebugValue(debugValue);\n      const finalState = useMemo(() => ({\n        ...currentState,\n        originalArgs,\n        reset\n      }), [currentState, originalArgs, reset]);\n      return useMemo(() => [triggerMutation, finalState] as const, [triggerMutation, finalState]);\n    };\n  }\n}", "export const UNINITIALIZED_VALUE = Symbol();\nexport type UninitializedValue = typeof UNINITIALIZED_VALUE;", "import { useEffect, useRef, useMemo } from 'react';\nimport type { SerializeQueryArgs } from '@reduxjs/toolkit/query';\nimport type { EndpointDefinition } from '@reduxjs/toolkit/query';\nexport function useStableQueryArgs<T>(queryArgs: T, serialize: SerializeQueryArgs<any>, endpointDefinition: EndpointDefinition<any, any, any, any>, endpointName: string) {\n  const incoming = useMemo(() => ({\n    queryArgs,\n    serialized: typeof queryArgs == 'object' ? serialize({\n      queryArgs,\n      endpointDefinition,\n      endpointName\n    }) : queryArgs\n  }), [queryArgs, serialize, endpointDefinition, endpointName]);\n  const cache = useRef(incoming);\n  useEffect(() => {\n    if (cache.current.serialized !== incoming.serialized) {\n      cache.current = incoming;\n    }\n  }, [incoming]);\n  return cache.current.serialized === incoming.serialized ? cache.current.queryArgs : queryArgs;\n}", "import { useEffect, useRef } from 'react';\nimport { shallowEqual } from 'react-redux';\nexport function useShallowStableValue<T>(value: T) {\n  const cache = useRef(value);\n  useEffect(() => {\n    if (!shallowEqual(cache.current, value)) {\n      cache.current = value;\n    }\n  }, [value]);\n  return shallowEqual(cache.current, value) ? cache.current : value;\n}", "import { configureStore, formatProdErrorMessage as _formatProdErrorMessage } from '@reduxjs/toolkit';\nimport type { Context } from 'react';\nimport { useContext } from 'react';\nimport { useEffect } from 'react';\nimport * as React from 'react';\nimport type { ReactReduxContextValue } from 'react-redux';\nimport { Provider, ReactReduxContext } from 'react-redux';\nimport { setupListeners } from '@reduxjs/toolkit/query';\nimport type { Api } from '@reduxjs/toolkit/query';\n\n/**\n * Can be used as a `Provider` if you **do not already have a Redux store**.\n *\n * @example\n * ```tsx\n * // codeblock-meta no-transpile title=\"Basic usage - wrap your App with ApiProvider\"\n * import * as React from 'react';\n * import { ApiProvider } from '@reduxjs/toolkit/query/react';\n * import { Pokemon } from './features/Pokemon';\n *\n * function App() {\n *   return (\n *     <ApiProvider api={api}>\n *       <Pokemon />\n *     </ApiProvider>\n *   );\n * }\n * ```\n *\n * @remarks\n * Using this together with an existing redux store, both will\n * conflict with each other - please use the traditional redux setup\n * in that case.\n */\nexport function ApiProvider(props: {\n  children: any;\n  api: Api<any, {}, any, any>;\n  setupListeners?: Parameters<typeof setupListeners>[1] | false;\n  context?: Context<ReactReduxContextValue | null>;\n}) {\n  const context = props.context || ReactReduxContext;\n  const existingContext = useContext(context);\n  if (existingContext) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(35) : 'Existing Redux context detected. If you already have a store set up, please use the traditional Redux setup.');\n  }\n  const [store] = React.useState(() => configureStore({\n    reducer: {\n      [props.api.reducerPath]: props.api.reducer\n    },\n    middleware: gDM => gDM().concat(props.api.middleware)\n  }));\n  // Adds the event listeners for online/offline/focus/etc\n  useEffect((): undefined | (() => void) => props.setupListeners === false ? undefined : setupListeners(store.dispatch, props.setupListeners), [props.setupListeners, store.dispatch]);\n  return <Provider store={store} context={context}>\n      {props.children}\n    </Provider>;\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,SAAS,gBAAgB,kBAAkB;;;ACH3C,SAAS,0BAA0BA,gCAA+B;AAElE,SAAS,SAAS,SAAS,eAAe,eAAe,eAAe,eAAe,YAAY,kBAAkB;AACrH,SAAS,kBAAkB,uBAAuB;;;ACH3C,SAAS,WAAW,KAAa;AACtC,SAAO,IAAI,QAAQ,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,YAAY,CAAC;AACjD;;;ACGO,SAAS,gBAAgB,KAAuB;AACrD,MAAI,QAAQ;AACZ,aAAW,QAAQ,KAAK;AACtB;AAAA,EACF;AACA,SAAO;AACT;;;ACm0BO,SAAS,kBAAkB,GAA8G;AAC9I,SAAO,EAAE,SAAS;AACpB;AACO,SAAS,qBAAqB,GAAiH;AACpJ,SAAO,EAAE,SAAS;AACpB;AACO,SAAS,0BAA0B,GAA2H;AACnK,SAAO,EAAE,SAAS;AACpB;;;ACl1BO,SAAS,WAA6B,WAAc,MAAqC;AAC9F,SAAO,OAAO,OAAO,QAAQ,GAAG,IAAI;AACtC;;;ACNA,SAAS,0BAA0B,yBAAyB,0BAA0B,gCAAgC;AAGtH,SAAS,2BAA2B,aAAa,iBAAiB;AAElE,SAAS,aAAa,eAAe,aAAAC,YAAW,iBAAiB,WAAAC,UAAS,UAAAC,SAAQ,gBAAgB;AAClG,SAAS,gBAAAC,qBAAoB;;;ACNtB,IAAM,sBAAsB,OAAO;;;ACA1C,SAAS,WAAW,QAAQ,eAAe;AAGpC,SAAS,mBAAsB,WAAc,WAAoC,oBAA4D,cAAsB;AACxK,QAAM,WAAW,QAAQ,OAAO;AAAA,IAC9B;AAAA,IACA,YAAY,OAAO,aAAa,WAAW,UAAU;AAAA,MACnD;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,IAAI;AAAA,EACP,IAAI,CAAC,WAAW,WAAW,oBAAoB,YAAY,CAAC;AAC5D,QAAM,QAAQ,OAAO,QAAQ;AAC7B,YAAU,MAAM;AACd,QAAI,MAAM,QAAQ,eAAe,SAAS,YAAY;AACpD,YAAM,UAAU;AAAA,IAClB;AAAA,EACF,GAAG,CAAC,QAAQ,CAAC;AACb,SAAO,MAAM,QAAQ,eAAe,SAAS,aAAa,MAAM,QAAQ,YAAY;AACtF;;;ACnBA,SAAS,aAAAC,YAAW,UAAAC,eAAc;AAClC,SAAS,oBAAoB;AACtB,SAAS,sBAAyB,OAAU;AACjD,QAAM,QAAQA,QAAO,KAAK;AAC1B,EAAAD,WAAU,MAAM;AACd,QAAI,CAAC,aAAa,MAAM,SAAS,KAAK,GAAG;AACvC,YAAM,UAAU;AAAA,IAClB;AAAA,EACF,GAAG,CAAC,KAAK,CAAC;AACV,SAAO,aAAa,MAAM,SAAS,KAAK,IAAI,MAAM,UAAU;AAC9D;;;AHSA,IAAM,YAAY,MAAM,CAAC,EAAE,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa,eAAe,OAAO,OAAO,SAAS,kBAAkB;AAC/I,IAAM,QAAuB,0BAAU;AAIvC,IAAM,yBAAyB,MAAM,OAAO,cAAc,eAAe,UAAU,YAAY;AAC/F,IAAM,gBAA+B,uCAAuB;AAC5D,IAAM,+BAA+B,MAAM,SAAS,gBAAgB,kBAAkBE;AAC/E,IAAM,4BAA2C,6CAA6B;AA+yBrF,IAAM,8BAA4D,cAAY;AAC5E,MAAI,SAAS,iBAAiB;AAC5B,WAAO,iCACF,WADE;AAAA,MAEL,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,WAAW,SAAS,SAAS,SAAY,QAAQ;AAAA,MACjD,QAAQ,YAAY;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,KAA2B,QAAW,MAAuB;AACpE,QAAM,MAAW,CAAC;AAClB,OAAK,QAAQ,SAAO;AAClB,QAAI,GAAG,IAAI,IAAI,GAAG;AAAA,EACpB,CAAC;AACD,SAAO;AACT;AACA,IAAM,2BAA2B,CAAC,QAAQ,UAAU,aAAa,aAAa,WAAW,OAAO;AAWzF,SAAS,WAAoD;AAAA,EAClE;AAAA,EACA,eAAe;AAAA,IACb;AAAA,IACA,OAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,EACA;AACF,GAKG;AACD,QAAM,6BAA8F,gCAAgC,QAAM,GAAG,IAAIA;AACjJ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,WAAS,sBAAsB,cAA8C,YAAyD,WAAiD;AAIrL,SAAI,yCAAY,iBAAgB,aAAa,iBAAiB;AAC5D,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM,qBAAqB,QAAQ,oBAAoB,YAAY;AACnE,UAAI,cAAc,aAAa,mBAAmB;AAAA,QAChD,WAAW,WAAW;AAAA,QACtB;AAAA,QACA;AAAA,MACF,CAAC,MAAM,mBAAmB;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC,EAAG,cAAa;AAAA,IACnB;AAGA,QAAI,OAAO,aAAa,YAAY,aAAa,OAAO,yCAAY;AACpE,QAAI,SAAS,OAAW,QAAO,aAAa;AAC5C,UAAM,UAAU,SAAS;AAGzB,UAAM,aAAa,aAAa;AAGhC,UAAM,aAAa,CAAC,cAAc,WAAW,aAAa,WAAW,oBAAoB,CAAC,WAAW;AAKrG,UAAM,YAAY,aAAa,aAAa,YAAY,cAAc,EAAC,yCAAY,YAAW,aAAa;AAC3G,WAAO,iCACF,eADE;AAAA,MAEL;AAAA,MACA,aAAa,aAAa;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,WAAS,8BAA8B,cAAsD,YAAiE,WAAyD;AAIrN,SAAI,yCAAY,iBAAgB,aAAa,iBAAiB;AAC5D,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM,qBAAqB,QAAQ,oBAAoB,YAAY;AACnE,UAAI,cAAc,aAAa,mBAAmB;AAAA,QAChD,WAAW,WAAW;AAAA,QACtB;AAAA,QACA;AAAA,MACF,CAAC,MAAM,mBAAmB;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC,EAAG,cAAa;AAAA,IACnB;AAGA,QAAI,OAAO,aAAa,YAAY,aAAa,OAAO,yCAAY;AACpE,QAAI,SAAS,OAAW,QAAO,aAAa;AAC5C,UAAM,UAAU,SAAS;AAGzB,UAAM,aAAa,aAAa;AAEhC,UAAM,aAAa,CAAC,cAAc,WAAW,aAAa,WAAW,oBAAoB,CAAC,WAAW;AAErG,UAAM,YAAY,aAAa,aAAa,cAAc;AAC1D,WAAO,iCACF,eADE;AAAA,MAEL;AAAA,MACA,aAAa,aAAa;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,WAAS,YAAyD,cAA4B,gBAAkC;AAC9H,UAAM,WAAW,YAAoD;AACrE,UAAM,uBAAuB,sBAAsB,cAAc;AACjE,WAAO,YAAY,CAAC,KAAU,YAA8B,SAAU,IAAI,KAAK,SAAkC,cAAc,KAAK,kCAC/H,uBACA,QACJ,CAAC,GAAG,CAAC,cAAc,UAAU,oBAAoB,CAAC;AAAA,EACrD;AACA,WAAS,+BAAgH,cAAsB,KAA0B,KAQxI,CAAC,GAAG;AARoI,iBACvK;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP,kBAAkB;AAAA,MAClB,yBAAyB;AAAA,IAr+B7B,IA+9B2K,IAOpK,iBAPoK,IAOpK;AAAA,MANH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAGA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,IAAI,UAAU,YAAY;AAC9B,UAAM,WAAW,YAAoD;AAGrE,UAAM,2BAA2BC,QAA0C,MAAS;AACpF,QAAI,CAAC,yBAAyB,SAAS;AACrC,YAAM,gBAAgB,SAAS,IAAI,gBAAgB,8BAA8B,CAAC;AAClF,UAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,YAAI,OAAO,kBAAkB,YAAY,QAAO,+CAAe,UAAS,UAAU;AAChF,gBAAM,IAAI,MAAM,QAAQ,IAAI,aAAa,eAAe,wBAAwB,EAAE,IAAI,yDAAyD,IAAI,WAAW;AAAA,qEACnG;AAAA,QAC7D;AAAA,MACF;AACA,+BAAyB,UAAU;AAAA,IACrC;AACA,UAAM,YAAY;AAAA,MAAmB,OAAO,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMxD;AAAA,MAA2B,QAAQ,oBAAoB,YAAY;AAAA,MAAG;AAAA,IAAY;AAClF,UAAM,4BAA4B,sBAAsB;AAAA,MACtD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,UAAM,mBAAoB,KAAkD;AAC5E,UAAM,yBAAyB,sBAAsB,gBAAgB;AAKrE,UAAM,aAAaA,QAAsB,MAAS;AAClD,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,WAAW,WAAW,CAAC;AAI3B,QAAI,+BAA+B;AACnC,QAAI,iBAAiB,WAAW;AAC9B,qCAA+B,yBAAyB,QAAQ,oBAAoB,eAAe,SAAS;AAAA,IAC9G;AACA,UAAM,sBAAsB,CAAC,gCAAgC,WAAW,YAAY;AACpF,+BAA2B,MAAwB;AACjD,UAAI,qBAAqB;AACvB,mBAAW,UAAU;AAAA,MACvB;AAAA,IACF,GAAG,CAAC,mBAAmB,CAAC;AACxB,+BAA2B,MAAwB;AA9hCvD,UAAAC;AA+hCM,YAAM,cAAc,WAAW;AAC/B,UAAI,OAAO,YAAY,eAAe,QAAQ,IAAI,aAAa,yBAAyB;AAEtF,gBAAQ,IAAI,mBAAmB;AAAA,MACjC;AACA,UAAI,cAAc,WAAW;AAC3B,mDAAa;AACb,mBAAW,UAAU;AACrB;AAAA,MACF;AACA,YAAM,2BAA0BA,MAAA,WAAW,YAAX,gBAAAA,IAAoB;AACpD,UAAI,CAAC,eAAe,YAAY,QAAQ,WAAW;AACjD,mDAAa;AACb,cAAM,UAAU,SAAS,SAAS,WAAW;AAAA,UAC3C,qBAAqB;AAAA,UACrB,cAAc;AAAA,WACV,0BAA0B,QAAQ,oBAAoB,YAAY,CAAC,IAAI;AAAA,UACzE,kBAAkB;AAAA,QACpB,IAAI,CAAC,EACN,CAAC;AACF,mBAAW,UAAU;AAAA,MACvB,WAAW,8BAA8B,yBAAyB;AAChE,oBAAY,0BAA0B,yBAAyB;AAAA,MACjE;AAAA,IACF,GAAG,CAAC,UAAU,UAAU,2BAA2B,WAAW,2BAA2B,qBAAqB,wBAAwB,YAAY,CAAC;AACnJ,WAAO,CAAC,YAAY,UAAU,UAAU,yBAAyB;AAAA,EACnE;AACA,WAAS,mBAAmB,cAAsB,aAAkF;AAClI,UAAM,gBAAgB,CAAC,KAAU;AAAA,MAC/B,OAAO;AAAA,MACP;AAAA,IACF,IAA6E,CAAC,MAAM;AAClF,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,IAAI,UAAU,YAAY;AAC9B,YAAM,YAAY,mBAAmB,OAAO,YAAY,KAAK,oBAAoB,QAAQ,oBAAoB,YAAY,GAAG,YAAY;AAExI,YAAM,YAAYD,QAAY,MAAS;AACvC,YAAM,sBAA0DE,SAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,QAKxE,eAAe;AAAA;AAAA,UAEf,OAAO,SAAS;AAAA,UAAG,CAAC,GAAiB,eAAoB;AAAA,UAAY,CAAC,MAAoB;AAAA,QAAS,GAAG,aAAa;AAAA,UACjH,gBAAgB;AAAA,YACd,qBAAqBC;AAAA,UACvB;AAAA,QACF,CAAC;AAAA,SAAG,CAAC,QAAQ,SAAS,CAAC;AACvB,YAAM,gBAAoDD,SAAQ,MAAM,mBAAmB,eAAe,CAAC,mBAAmB,GAAG,kBAAkB;AAAA,QACjJ,eAAe;AAAA,UACb,uBAAuB;AAAA,QACzB;AAAA,MACF,CAAC,IAAI,qBAAqB,CAAC,qBAAqB,gBAAgB,CAAC;AACjE,YAAM,eAAe,YAAY,CAAC,UAA4C,cAAc,OAAO,UAAU,OAAO,GAAGC,aAAY;AACnI,YAAM,QAAQ,SAA2C;AACzD,YAAM,eAAe,oBAAoB,MAAM,SAAS,GAAG,UAAU,OAAO;AAC5E,gCAA0B,MAAM;AAC9B,kBAAU,UAAU;AAAA,MACtB,GAAG,CAAC,YAAY,CAAC;AACjB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,WAAS,kCAAkC,YAE3B;AACd,IAAAJ,WAAU,MAAM;AACd,aAAO,MAAM;AApmCnB;AAqmCQ,+BAAW,YAAX,mBAAoB,gBAApB;AAGA,QAAC,WAAW,UAAkB;AAAA,MAChC;AAAA,IACF,GAAG,CAAC,UAAU,CAAC;AAAA,EACjB;AACA,WAAS,0BAA2G,YAA+C;AACjK,QAAI,CAAC,WAAW,QAAS,OAAM,IAAI,MAAM,QAAQ,IAAI,aAAa,eAAe,yBAAyB,EAAE,IAAI,uDAAuD;AACvK,WAAO,WAAW,QAAQ,QAAQ;AAAA,EACpC;AACA,WAAS,gBAAgB,cAAuC;AAC9D,UAAM,uBAAkD,CAAC,KAAU,UAAU,CAAC,MAAM;AAClF,YAAM,CAAC,UAAU,IAAI,+BAA8D,cAAc,KAAK,OAAO;AAC7G,wCAAkC,UAAU;AAC5C,aAAOG,SAAQ,OAAO;AAAA;AAAA;AAAA;AAAA,QAIpB,SAAS,MAAM,0BAA0B,UAAU;AAAA,MACrD,IAAI,CAAC,UAAU,CAAC;AAAA,IAClB;AACA,UAAM,2BAA0D,CAAC;AAAA,MAC/D;AAAA,MACA;AAAA,MACA,kBAAkB;AAAA,MAClB,yBAAyB;AAAA,IAC3B,IAAI,CAAC,MAAM;AACT,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,IAAI,UAAU,YAAY;AAC9B,YAAM,WAAW,YAAoD;AACrE,YAAM,CAAC,KAAK,MAAM,IAAI,SAAc,mBAAmB;AAMvD,YAAM,aAAaF,QAAkD,MAAS;AAC9E,YAAM,4BAA4B,sBAAsB;AAAA,QACtD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,iCAA2B,MAAM;AAlpCvC;AAmpCQ,cAAM,2BAA0B,gBAAW,YAAX,mBAAoB;AACpD,YAAI,8BAA8B,yBAAyB;AACzD,2BAAW,YAAX,mBAAoB,0BAA0B;AAAA,QAChD;AAAA,MACF,GAAG,CAAC,yBAAyB,CAAC;AAC9B,YAAM,yBAAyBA,QAAO,yBAAyB;AAC/D,iCAA2B,MAAM;AAC/B,+BAAuB,UAAU;AAAA,MACnC,GAAG,CAAC,yBAAyB,CAAC;AAC9B,YAAM,UAAU,YAAY,SAAUI,MAAU,mBAAmB,OAAO;AACxE,YAAI;AACJ,cAAM,MAAM;AA9pCpB;AA+pCU,2BAAW,YAAX,mBAAoB;AACpB,qBAAW,UAAU,UAAU,SAAS,SAASA,MAAK;AAAA,YACpD,qBAAqB,uBAAuB;AAAA,YAC5C,cAAc,CAAC;AAAA,UACjB,CAAC,CAAC;AACF,iBAAOA,IAAG;AAAA,QACZ,CAAC;AACD,eAAO;AAAA,MACT,GAAG,CAAC,UAAU,QAAQ,CAAC;AACvB,YAAM,QAAQ,YAAY,MAAM;AAxqCtC;AAyqCQ,aAAI,gBAAW,YAAX,mBAAoB,eAAe;AACrC,mBAAS,IAAI,gBAAgB,kBAAkB;AAAA,YAC7C,gBAAe,gBAAW,YAAX,mBAAoB;AAAA,UACrC,CAAC,CAAC;AAAA,QACJ;AAAA,MACF,GAAG,CAAC,QAAQ,CAAC;AAGb,MAAAL,WAAU,MAAM;AACd,eAAO,MAAM;AAlrCrB;AAmrCU,yDAAY,YAAZ,mBAAqB;AAAA,QACvB;AAAA,MACF,GAAG,CAAC,CAAC;AAGL,MAAAA,WAAU,MAAM;AACd,YAAI,QAAQ,uBAAuB,CAAC,WAAW,SAAS;AACtD,kBAAQ,KAAK,IAAI;AAAA,QACnB;AAAA,MACF,GAAG,CAAC,KAAK,OAAO,CAAC;AACjB,aAAOG,SAAQ,MAAM,CAAC,SAAS,KAAK;AAAA,QAClC;AAAA,MACF,CAAC,GAAY,CAAC,SAAS,KAAK,KAAK,CAAC;AAAA,IACpC;AACA,UAAM,gBAAoC,mBAAmB,cAAc,qBAAqB;AAChG,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa,SAAS;AACpB,cAAM,CAAC,SAAS,KAAK;AAAA,UACnB;AAAA,QACF,CAAC,IAAI,yBAAyB,OAAO;AACrC,cAAM,oBAAoB,cAAc,KAAK,iCACxC,UADwC;AAAA,UAE3C,MAAM,QAAQ;AAAA,QAChB,EAAC;AACD,cAAM,OAAOA,SAAQ,OAAO;AAAA,UAC1B,SAAS;AAAA,QACX,IAAI,CAAC,GAAG,CAAC;AACT,eAAOA,SAAQ,MAAM,CAAC,SAAS,iCAC1B,oBAD0B;AAAA,UAE7B;AAAA,QACF,IAAG,IAAI,GAAG,CAAC,SAAS,mBAAmB,OAAO,IAAI,CAAC;AAAA,MACrD;AAAA,MACA,SAAS,KAAK,SAAS;AACrB,cAAM,2BAA2B,qBAAqB,KAAK,OAAO;AAClE,cAAM,oBAAoB,cAAc,KAAK;AAAA,UAC3C,kBAAkB,QAAQ,cAAa,mCAAS,QAAO,SAAY;AAAA,WAChE,QACJ;AACD,cAAM,aAAa,KAAK,mBAAmB,GAAG,wBAAwB;AACtE,sBAAc,UAAU;AACxB,eAAOA,SAAQ,MAAO,kCACjB,oBACA,2BACD,CAAC,mBAAmB,wBAAwB,CAAC;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AACA,WAAS,wBAAwB,cAA+C;AAC9E,UAAM,+BAAkE,CAAC,KAAU,UAAU,CAAC,MAAM;AAClG,YAAM,CAAC,YAAY,UAAU,UAAU,yBAAyB,IAAI,+BAAsE,cAAc,KAAK,OAAO;AACpK,YAAM,yBAAyBF,QAAO,yBAAyB;AAC/D,iCAA2B,MAAM;AAC/B,+BAAuB,UAAU;AAAA,MACnC,GAAG,CAAC,yBAAyB,CAAC;AAC9B,YAAM,UAAyC,YAAY,SAAUI,MAAc,WAAmC;AACpH,YAAI;AACJ,cAAM,MAAM;AA9uCpB;AA+uCU,2BAAW,YAAX,mBAAoB;AACpB,qBAAW,UAAU,UAAU,SAAU,SAAkDA,MAAK;AAAA,YAC9F,qBAAqB,uBAAuB;AAAA,YAC5C;AAAA,UACF,CAAC,CAAC;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACT,GAAG,CAAC,YAAY,UAAU,QAAQ,CAAC;AACnC,wCAAkC,UAAU;AAC5C,YAAM,YAAY;AAAA,QAAmB,QAAQ,OAAO,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMhE;AAAA,QAA2B,QAAQ,oBAAoB,YAAY;AAAA,QAAG;AAAA,MAAY;AAClF,YAAM,UAAU,YAAY,MAAM,0BAA0B,UAAU,GAAG,CAAC,UAAU,CAAC;AACrF,aAAOF,SAAQ,MAAM;AACnB,cAAM,gBAAgB,MAAM;AAC1B,iBAAO,QAAQ,WAAW,SAAS;AAAA,QACrC;AACA,cAAM,oBAAoB,MAAM;AAC9B,iBAAO,QAAQ,WAAW,UAAU;AAAA,QACtC;AACA,eAAO;AAAA,UACL;AAAA;AAAA;AAAA;AAAA,UAIA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,GAAG,CAAC,SAAS,SAAS,SAAS,CAAC;AAAA,IAClC;AACA,UAAM,wBAAoD,mBAAmB,cAAc,6BAA6B;AACxH,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,iBAAiB,KAAK,SAAS;AAC7B,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,6BAA6B,KAAK,OAAO;AAC7C,cAAM,oBAAoB,sBAAsB,KAAK;AAAA,UACnD,kBAAkB,QAAQ,cAAa,mCAAS,QAAO,SAAY;AAAA,WAChE,QACJ;AACD,cAAM,aAAa,KAAK,mBAAmB,GAAG,0BAA0B,eAAe,iBAAiB;AACxG,sBAAc,UAAU;AACxB,eAAOA,SAAQ,MAAO,iCACjB,oBADiB;AAAA,UAEpB;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,CAAC,mBAAmB,eAAe,mBAAmB,OAAO,CAAC;AAAA,MACpE;AAAA,IACF;AAAA,EACF;AACA,WAAS,kBAAkB,MAAgC;AACzD,WAAO,CAAC;AAAA,MACN;AAAA,MACA;AAAA,IACF,IAAI,CAAC,MAAM;AACT,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,IAAI,UAAU,IAAI;AACtB,YAAM,WAAW,YAAoD;AACrE,YAAM,CAAC,SAAS,UAAU,IAAI,SAA2C;AACzE,MAAAH,WAAU,MAAM,MAAM;AACpB,YAAI,EAAC,mCAAS,IAAI,gBAAe;AAC/B,6CAAS;AAAA,QACX;AAAA,MACF,GAAG,CAAC,OAAO,CAAC;AACZ,YAAM,kBAAkB,YAAY,SAAU,KAAuC;AACnF,cAAMM,WAAU,SAAS,SAAS,KAAK;AAAA,UACrC;AAAA,QACF,CAAC,CAAC;AACF,mBAAWA,QAAO;AAClB,eAAOA;AAAA,MACT,GAAG,CAAC,UAAU,UAAU,aAAa,CAAC;AACtC,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,WAAW,CAAC;AAChB,YAAM,sBAAsBH,SAAQ,MAAM,OAAO;AAAA,QAC/C;AAAA,QACA,WAAW,mCAAS;AAAA,MACtB,CAAC,GAAG,CAAC,eAAe,SAAS,MAAM,CAAC;AACpC,YAAM,mBAAmBA,SAAQ,MAAuD,mBAAmB,eAAe,CAAC,mBAAmB,GAAG,gBAAgB,IAAI,qBAAqB,CAAC,kBAAkB,mBAAmB,CAAC;AACjO,YAAM,eAAe,YAAY,kBAAkBC,aAAY;AAC/D,YAAM,eAAe,iBAAiB,OAAO,mCAAS,IAAI,eAAe;AACzE,YAAM,QAAQ,YAAY,MAAM;AAC9B,cAAM,MAAM;AACV,cAAI,SAAS;AACX,uBAAW,MAAS;AAAA,UACtB;AACA,cAAI,eAAe;AACjB,qBAAS,IAAI,gBAAgB,qBAAqB;AAAA,cAChD;AAAA,cACA;AAAA,YACF,CAAC,CAAC;AAAA,UACJ;AAAA,QACF,CAAC;AAAA,MACH,GAAG,CAAC,UAAU,eAAe,SAAS,SAAS,CAAC;AAChD,YAAM,aAAa,KAAK,cAAc,GAAG,0BAA0B,cAAc;AACjF,oBAAc,UAAU;AACxB,YAAM,aAAaD,SAAQ,MAAO,iCAC7B,eAD6B;AAAA,QAEhC;AAAA,QACA;AAAA,MACF,IAAI,CAAC,cAAc,cAAc,KAAK,CAAC;AACvC,aAAOA,SAAQ,MAAM,CAAC,iBAAiB,UAAU,GAAY,CAAC,iBAAiB,UAAU,CAAC;AAAA,IAC5F;AAAA,EACF;AACF;;;ALz1CO,IAAM,uBAAsC,uBAAO;AA0FnD,IAAM,mBAAmB,CAAC,KAUJ,CAAC,MAAgC;AAV7B,eAC/B;AAAA,YAAQ;AAAA,IACR,QAAQ;AAAA,MACN,aAAa;AAAA,MACb,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AAAA,IACA,iBAAiB;AAAA,IACjB,gCAAgC;AAAA,EA5GlC,IAoGiC,IAS5B,iBAT4B,IAS5B;AAAA,IARH;AAAA,IACA;AAAA,IAKA;AAAA,IACA;AAAA;AAGA,MAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,UAAM,YAAY,CAAC,eAAe,eAAe,UAAU;AAC3D,QAAI,SAAS;AACb,eAAW,YAAY,WAAW;AAEhC,UAAI,gBAAgB,IAAI,IAAI,GAAG;AAC7B,YAAK,KAA+B,QAAQ,GAAG;AAC7C,cAAI,CAAC,QAAQ;AACX,oBAAQ,KAAK,uKAA4K;AACzL,qBAAS;AAAA,UACX;AAAA,QACF;AAGA,cAAM,QAAQ,IAAI,KAAK,QAAQ;AAAA,MACjC;AAEA,UAAI,OAAO,MAAM,QAAQ,MAAM,YAAY;AACzC,cAAM,IAAI,MAAM,QAAQ,IAAI,aAAa,eAAeI,yBAAwB,EAAE,IAAI,4CAA4C,UAAU,MAAM,+BAA+B,UAAU,KAAK,IAAI,CAAC;AAAA,OAAW,QAAQ,6CAA6C;AAAA,MACvQ;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,KAAK,KAAK;AAAA,MACR;AAAA,IACF,GAAG,SAAS;AACV,YAAM,SAAS;AACf,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,WAAW;AAAA,QACb;AAAA,QACA,eAAe;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,iBAAW,QAAQ;AAAA,QACjB;AAAA,MACF,CAAC;AACD,iBAAW,SAAS;AAAA,QAClB;AAAA,MACF,CAAC;AACD,aAAO;AAAA,QACL,eAAe,cAAc,YAAY;AACvC,cAAI,kBAAkB,UAAU,GAAG;AACjC,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF,IAAI,gBAAgB,YAAY;AAChC,uBAAW,OAAO,UAAU,YAAY,GAAG;AAAA,cACzC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AACD,YAAC,IAAY,MAAM,WAAW,YAAY,CAAC,OAAO,IAAI;AACtD,YAAC,IAAY,UAAU,WAAW,YAAY,CAAC,OAAO,IAAI;AAAA,UAC5D;AACA,cAAI,qBAAqB,UAAU,GAAG;AACpC,kBAAM,cAAc,kBAAkB,YAAY;AAClD,uBAAW,OAAO,UAAU,YAAY,GAAG;AAAA,cACzC;AAAA,YACF,CAAC;AACD,YAAC,IAAY,MAAM,WAAW,YAAY,CAAC,UAAU,IAAI;AAAA,UAC3D,WAAW,0BAA0B,UAAU,GAAG;AAChD,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,cACA;AAAA,YACF,IAAI,wBAAwB,YAAY;AACxC,uBAAW,OAAO,UAAU,YAAY,GAAG;AAAA,cACzC;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AACD,YAAC,IAAY,MAAM,WAAW,YAAY,CAAC,eAAe,IAAI;AAAA,UAChE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ADvMA,cAAc;;;AULd,SAAS,gBAAgB,0BAA0BC,gCAA+B;AAElF,SAAS,kBAAkB;AAC3B,SAAS,aAAAC,kBAAiB;AAC1B,YAAY,WAAW;AAEvB,SAAS,UAAU,yBAAyB;AAC5C,SAAS,sBAAsB;AA2BxB,SAAS,YAAY,OAKzB;AACD,QAAM,UAAU,MAAM,WAAW;AACjC,QAAM,kBAAkB,WAAW,OAAO;AAC1C,MAAI,iBAAiB;AACnB,UAAM,IAAI,MAAM,QAAQ,IAAI,aAAa,eAAeD,yBAAwB,EAAE,IAAI,8GAA8G;AAAA,EACtM;AACA,QAAM,CAAC,KAAK,IAAU,eAAS,MAAM,eAAe;AAAA,IAClD,SAAS;AAAA,MACP,CAAC,MAAM,IAAI,WAAW,GAAG,MAAM,IAAI;AAAA,IACrC;AAAA,IACA,YAAY,SAAO,IAAI,EAAE,OAAO,MAAM,IAAI,UAAU;AAAA,EACtD,CAAC,CAAC;AAEF,EAAAC,WAAU,MAAgC,MAAM,mBAAmB,QAAQ,SAAY,eAAe,MAAM,UAAU,MAAM,cAAc,GAAG,CAAC,MAAM,gBAAgB,MAAM,QAAQ,CAAC;AACnL,SAAO,oCAAC,YAAS,OAAc,WAC1B,MAAM,QACT;AACJ;;;AVjDA,IAAM,YAA2B,+BAAe,WAAW,GAAG,iBAAiB,CAAC;", "names": ["_formatProdErrorMessage", "useEffect", "useMemo", "useRef", "shallowEqual", "useEffect", "useRef", "useEffect", "useRef", "_a", "useMemo", "shallowEqual", "arg", "promise", "_formatProdErrorMessage", "_formatProdErrorMessage", "useEffect"]}