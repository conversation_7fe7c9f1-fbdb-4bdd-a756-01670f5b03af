import { io, Socket } from 'socket.io-client';
import { Alert } from 'react-native';
import { StorageService } from './StorageService';
import { NotificationService } from './NotificationService';
import { SoundService } from './SoundService';

export interface Player {
  id: string;
  name: string;
  rating?: number;
  level?: number;
  isOnline?: boolean;
}

export interface GameRoom {
  id: string;
  name: string;
  players: Player[];
  maxPlayers: number;
  gameType: string;
  status: 'waiting' | 'playing' | 'finished';
}

export interface GameState {
  id: string;
  players: any[];
  currentPlayer: string;
  phase: string;
  [key: string]: any;
}

class SocketServiceClass {
  private socket: Socket | null = null;
  private serverUrl = 'http://localhost:3002'; // Для разработки
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;
  private isConnecting = false;
  private eventListeners: Map<string, Function[]> = new Map();

  /**
   * Подключается к серверу
   */
  async connect(): Promise<boolean> {
    if (this.isConnecting || this.socket?.connected) {
      return true;
    }

    this.isConnecting = true;

    try {
      const user = await StorageService.getUser();
      if (!user) {
        throw new Error('No user data found');
      }

      this.socket = io(this.serverUrl, {
        transports: ['websocket'],
        timeout: 10000,
        forceNew: true,
      });

      this.setupEventListeners();

      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        this.socket!.on('connect', () => {
          clearTimeout(timeout);
          console.log('Connected to server');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          
          // Регистрируем игрока
          this.socket!.emit('register_player', {
            name: user.name,
            id: user.id,
          });
          
          resolve(true);
        });

        this.socket!.on('connect_error', (error) => {
          clearTimeout(timeout);
          console.error('Connection error:', error);
          this.isConnecting = false;
          reject(error);
        });
      });
    } catch (error) {
      this.isConnecting = false;
      console.error('Socket connection error:', error);
      return false;
    }
  }

  /**
   * Отключается от сервера
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  /**
   * Переподключается к серверу
   */
  async reconnect(): Promise<boolean> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      Alert.alert(
        'Ошибка подключения',
        'Не удалось подключиться к серверу. Проверьте интернет-соединение.',
      );
      return false;
    }

    this.reconnectAttempts++;
    console.log(`Reconnection attempt ${this.reconnectAttempts}`);

    this.disconnect();
    
    return new Promise((resolve) => {
      setTimeout(async () => {
        const success = await this.connect();
        resolve(success);
      }, this.reconnectInterval);
    });
  }

  /**
   * Проверяет состояние подключения
   */
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Отправляет событие на сервер
   */
  emit(event: string, data?: any): void {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    } else {
      console.warn('Socket not connected, cannot emit event:', event);
    }
  }

  /**
   * Подписывается на событие
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);

    if (this.socket) {
      this.socket.on(event, callback as any);
    }
  }

  /**
   * Отписывается от события
   */
  off(event: string, callback?: Function): void {
    if (this.socket) {
      if (callback) {
        this.socket.off(event, callback as any);
      } else {
        this.socket.off(event);
      }
    }

    if (callback) {
      const listeners = this.eventListeners.get(event);
      if (listeners) {
        const index = listeners.indexOf(callback);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    } else {
      this.eventListeners.delete(event);
    }
  }

  /**
   * Настраивает обработчики событий
   */
  private setupEventListeners(): void {
    if (!this.socket) return;

    // Основные события подключения
    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected:', reason);
      if (reason === 'io server disconnect') {
        // Сервер принудительно отключил
        this.reconnect();
      }
    });

    this.socket.on('reconnect', () => {
      console.log('Reconnected to server');
      this.reconnectAttempts = 0;
    });

    this.socket.on('reconnect_error', (error) => {
      console.error('Reconnection error:', error);
    });

    // Игровые события
    this.socket.on('game_invitation', (data) => {
      NotificationService.showGameInvitation(data);
      SoundService.playNotification();
    });

    this.socket.on('friend_request', (data) => {
      NotificationService.showFriendRequest(data);
      SoundService.playNotification();
    });

    this.socket.on('achievement_unlocked', (data) => {
      NotificationService.showAchievement(data);
      SoundService.playAchievement();
    });

    this.socket.on('level_up', (data) => {
      NotificationService.showLevelUp(data);
      SoundService.playLevelUp();
    });

    this.socket.on('tournament_started', (data) => {
      NotificationService.showTournamentUpdate(data);
      SoundService.playNotification();
    });

    // Восстанавливаем пользовательские обработчики
    for (const [event, callbacks] of this.eventListeners.entries()) {
      for (const callback of callbacks) {
        this.socket.on(event, callback as any);
      }
    }
  }

  // Игровые методы
  /**
   * Создает игровую комнату
   */
  createRoom(roomName: string, gameType: string, maxPlayers: number = 2): void {
    this.emit('create_room', {
      name: roomName,
      gameType,
      maxPlayers,
    });
  }

  /**
   * Присоединяется к комнате
   */
  joinRoom(roomId: string): void {
    this.emit('join_room', { roomId });
  }

  /**
   * Покидает комнату
   */
  leaveRoom(roomId: string): void {
    this.emit('leave_room', { roomId });
  }

  /**
   * Начинает игру
   */
  startGame(roomId: string): void {
    this.emit('start_game', { roomId });
  }

  /**
   * Делает ход в игре
   */
  makeMove(roomId: string, move: any): void {
    this.emit('make_move', { roomId, move });
  }

  /**
   * Отправляет сообщение в чат
   */
  sendChatMessage(roomId: string, message: string): void {
    this.emit('chat_message', { roomId, message });
  }

  // Социальные методы
  /**
   * Отправляет запрос в друзья
   */
  sendFriendRequest(targetPlayerId: string): void {
    this.emit('send_friend_request', { targetPlayerId });
  }

  /**
   * Принимает запрос в друзья
   */
  acceptFriendRequest(requestId: string): void {
    this.emit('accept_friend_request', { requestId });
  }

  /**
   * Отклоняет запрос в друзья
   */
  rejectFriendRequest(requestId: string): void {
    this.emit('reject_friend_request', { requestId });
  }

  /**
   * Приглашает друга в игру
   */
  inviteFriend(friendId: string, gameType: string): void {
    this.emit('invite_friend', { friendId, gameType });
  }

  // Турнирные методы
  /**
   * Регистрируется в турнире
   */
  registerForTournament(tournamentId: string): void {
    this.emit('register_tournament', { tournamentId });
  }

  /**
   * Покидает турнир
   */
  leaveTournament(tournamentId: string): void {
    this.emit('leave_tournament', { tournamentId });
  }

  // Покерные методы
  /**
   * Создает покерную комнату
   */
  createPokerRoom(roomData: any): void {
    this.emit('create_poker_room', roomData);
  }

  /**
   * Присоединяется к покерной комнате
   */
  joinPokerRoom(roomId: string, password?: string): void {
    this.emit('join_poker_room', { roomId, password });
  }

  /**
   * Делает действие в покере
   */
  makePokerAction(roomId: string, action: any): void {
    this.emit('poker_action', { roomId, action });
  }

  // Клановые методы
  /**
   * Создает клан
   */
  createClan(clanData: any): void {
    this.emit('create_clan', clanData);
  }

  /**
   * Подает заявку в клан
   */
  applyClan(clanId: string, message: string): void {
    this.emit('apply_clan', { clanId, message });
  }

  /**
   * Покидает клан
   */
  leaveClan(): void {
    this.emit('leave_clan');
  }

  // Методы получения данных
  /**
   * Получает список комнат
   */
  getRooms(): void {
    this.emit('get_rooms');
  }

  /**
   * Получает рейтинг игрока
   */
  getPlayerRating(): void {
    this.emit('get_player_rating');
  }

  /**
   * Получает достижения игрока
   */
  getPlayerAchievements(): void {
    this.emit('get_player_achievements');
  }

  /**
   * Получает список друзей
   */
  getFriends(): void {
    this.emit('get_friends');
  }

  /**
   * Получает прогресс игрока
   */
  getPlayerProgress(): void {
    this.emit('get_player_progress');
  }

  /**
   * Получает активные турниры
   */
  getTournaments(): void {
    this.emit('get_tournaments');
  }

  /**
   * Получает информацию о клане
   */
  getClanInfo(): void {
    this.emit('get_clan_info');
  }

  /**
   * Получает сезонные события
   */
  getSeasonalEvents(): void {
    this.emit('get_seasonal_events');
  }
}

export const SocketService = new SocketServiceClass();
