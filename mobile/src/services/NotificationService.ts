import PushNotification, { Importance } from 'react-native-push-notification';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import { Platform, Alert } from 'react-native';
import { StorageService } from './StorageService';

export interface NotificationData {
  id: string;
  title: string;
  message: string;
  data?: any;
  type: 'game_invitation' | 'friend_request' | 'achievement' | 'level_up' | 'tournament' | 'clan' | 'general';
}

class NotificationServiceClass {
  private isInitialized = false;
  private notificationSettings = {
    enabled: true,
    sound: true,
    vibration: true,
    gameInvitations: true,
    friendRequests: true,
    achievements: true,
    tournaments: true,
    clanEvents: true,
  };

  /**
   * Инициализирует сервис уведомлений
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Загружаем настройки
      await this.loadSettings();

      // Настраиваем push-уведомления
      this.configurePushNotifications();

      // Запрашиваем разрешения
      await this.requestPermissions();

      this.isInitialized = true;
      console.log('NotificationService initialized');
    } catch (error) {
      console.error('NotificationService initialization error:', error);
    }
  }

  /**
   * Настраивает push-уведомления
   */
  private configurePushNotifications(): void {
    PushNotification.configure({
      // Обработчик получения уведомления
      onNotification: (notification) => {
        console.log('Notification received:', notification);
        
        if (notification.userInteraction) {
          // Пользователь нажал на уведомление
          this.handleNotificationTap(notification);
        }

        // Для iOS
        if (Platform.OS === 'ios') {
          notification.finish(PushNotificationIOS.FetchResult.NoData);
        }
      },

      // Обработчик регистрации токена
      onRegister: (token) => {
        console.log('Push token:', token);
        // Отправляем токен на сервер
        this.sendTokenToServer(token.token);
      },

      // Обработчик ошибок регистрации
      onRegistrationError: (error) => {
        console.error('Push registration error:', error);
      },

      // Настройки
      permissions: {
        alert: true,
        badge: true,
        sound: true,
      },
      popInitialNotification: true,
      requestPermissions: Platform.OS === 'ios',
    });

    // Создаем каналы уведомлений для Android
    if (Platform.OS === 'android') {
      this.createNotificationChannels();
    }
  }

  /**
   * Создает каналы уведомлений для Android
   */
  private createNotificationChannels(): void {
    const channels = [
      {
        channelId: 'game_invitations',
        channelName: 'Приглашения в игры',
        channelDescription: 'Уведомления о приглашениях в игры',
        importance: Importance.HIGH,
      },
      {
        channelId: 'friend_requests',
        channelName: 'Запросы в друзья',
        channelDescription: 'Уведомления о запросах в друзья',
        importance: Importance.DEFAULT,
      },
      {
        channelId: 'achievements',
        channelName: 'Достижения',
        channelDescription: 'Уведомления о новых достижениях',
        importance: Importance.HIGH,
      },
      {
        channelId: 'tournaments',
        channelName: 'Турниры',
        channelDescription: 'Уведомления о турнирах',
        importance: Importance.DEFAULT,
      },
      {
        channelId: 'clan_events',
        channelName: 'События клана',
        channelDescription: 'Уведомления о событиях клана',
        importance: Importance.DEFAULT,
      },
      {
        channelId: 'general',
        channelName: 'Общие',
        channelDescription: 'Общие уведомления',
        importance: Importance.LOW,
      },
    ];

    channels.forEach(channel => {
      PushNotification.createChannel(channel, () => {
        console.log(`Channel ${channel.channelId} created`);
      });
    });
  }

  /**
   * Запрашивает разрешения на уведомления
   */
  private async requestPermissions(): Promise<boolean> {
    if (Platform.OS === 'ios') {
      const permissions = await PushNotificationIOS.requestPermissions({
        alert: true,
        badge: true,
        sound: true,
      });
      return permissions.alert || permissions.badge || permissions.sound;
    } else {
      // На Android разрешения запрашиваются автоматически
      return true;
    }
  }

  /**
   * Отправляет токен на сервер
   */
  private async sendTokenToServer(token: string): Promise<void> {
    try {
      // Здесь должна быть отправка токена на сервер
      await StorageService.savePushToken(token);
      console.log('Push token saved:', token);
    } catch (error) {
      console.error('Error sending token to server:', error);
    }
  }

  /**
   * Обрабатывает нажатие на уведомление
   */
  private handleNotificationTap(notification: any): void {
    const { data } = notification;
    
    if (data?.type) {
      switch (data.type) {
        case 'game_invitation':
          // Переход к игре
          break;
        case 'friend_request':
          // Переход к друзьям
          break;
        case 'achievement':
          // Переход к достижениям
          break;
        case 'tournament':
          // Переход к турнирам
          break;
        case 'clan':
          // Переход к клану
          break;
      }
    }
  }

  /**
   * Показывает локальное уведомление
   */
  showLocalNotification(data: NotificationData): void {
    if (!this.notificationSettings.enabled) return;

    const channelId = this.getChannelId(data.type);
    
    if (!this.isNotificationTypeEnabled(data.type)) return;

    PushNotification.localNotification({
      id: data.id,
      title: data.title,
      message: data.message,
      channelId,
      playSound: this.notificationSettings.sound,
      vibrate: this.notificationSettings.vibration,
      userInfo: data.data,
      actions: this.getNotificationActions(data.type),
    });
  }

  /**
   * Показывает уведомление о приглашении в игру
   */
  showGameInvitation(data: any): void {
    this.showLocalNotification({
      id: `game_invitation_${Date.now()}`,
      title: 'Приглашение в игру',
      message: `${data.fromPlayer} приглашает вас сыграть в ${data.gameType}`,
      type: 'game_invitation',
      data,
    });
  }

  /**
   * Показывает уведомление о запросе в друзья
   */
  showFriendRequest(data: any): void {
    this.showLocalNotification({
      id: `friend_request_${Date.now()}`,
      title: 'Запрос в друзья',
      message: `${data.fromPlayer} хочет добавить вас в друзья`,
      type: 'friend_request',
      data,
    });
  }

  /**
   * Показывает уведомление о достижении
   */
  showAchievement(data: any): void {
    this.showLocalNotification({
      id: `achievement_${Date.now()}`,
      title: 'Новое достижение!',
      message: `Получено: ${data.name}`,
      type: 'achievement',
      data,
    });
  }

  /**
   * Показывает уведомление о повышении уровня
   */
  showLevelUp(data: any): void {
    this.showLocalNotification({
      id: `level_up_${Date.now()}`,
      title: 'Повышение уровня!',
      message: `Поздравляем! Вы достигли ${data.newLevel} уровня!`,
      type: 'level_up',
      data,
    });
  }

  /**
   * Показывает уведомление о турнире
   */
  showTournamentUpdate(data: any): void {
    this.showLocalNotification({
      id: `tournament_${Date.now()}`,
      title: 'Турнир',
      message: data.message,
      type: 'tournament',
      data,
    });
  }

  /**
   * Показывает уведомление о клане
   */
  showClanNotification(data: any): void {
    this.showLocalNotification({
      id: `clan_${Date.now()}`,
      title: 'Клан',
      message: data.message,
      type: 'clan',
      data,
    });
  }

  /**
   * Отменяет уведомление
   */
  cancelNotification(notificationId: string): void {
    PushNotification.cancelLocalNotification(notificationId);
  }

  /**
   * Отменяет все уведомления
   */
  cancelAllNotifications(): void {
    PushNotification.cancelAllLocalNotifications();
  }

  /**
   * Получает настройки уведомлений
   */
  getSettings() {
    return { ...this.notificationSettings };
  }

  /**
   * Обновляет настройки уведомлений
   */
  async updateSettings(newSettings: Partial<typeof this.notificationSettings>): Promise<void> {
    this.notificationSettings = { ...this.notificationSettings, ...newSettings };
    await this.saveSettings();
  }

  /**
   * Загружает настройки из хранилища
   */
  private async loadSettings(): Promise<void> {
    try {
      const settings = await StorageService.getNotificationSettings();
      if (settings) {
        this.notificationSettings = { ...this.notificationSettings, ...settings };
      }
    } catch (error) {
      console.error('Error loading notification settings:', error);
    }
  }

  /**
   * Сохраняет настройки в хранилище
   */
  private async saveSettings(): Promise<void> {
    try {
      await StorageService.saveNotificationSettings(this.notificationSettings);
    } catch (error) {
      console.error('Error saving notification settings:', error);
    }
  }

  /**
   * Получает ID канала для типа уведомления
   */
  private getChannelId(type: NotificationData['type']): string {
    switch (type) {
      case 'game_invitation':
        return 'game_invitations';
      case 'friend_request':
        return 'friend_requests';
      case 'achievement':
      case 'level_up':
        return 'achievements';
      case 'tournament':
        return 'tournaments';
      case 'clan':
        return 'clan_events';
      default:
        return 'general';
    }
  }

  /**
   * Проверяет, включен ли тип уведомления
   */
  private isNotificationTypeEnabled(type: NotificationData['type']): boolean {
    switch (type) {
      case 'game_invitation':
        return this.notificationSettings.gameInvitations;
      case 'friend_request':
        return this.notificationSettings.friendRequests;
      case 'achievement':
      case 'level_up':
        return this.notificationSettings.achievements;
      case 'tournament':
        return this.notificationSettings.tournaments;
      case 'clan':
        return this.notificationSettings.clanEvents;
      default:
        return true;
    }
  }

  /**
   * Получает действия для уведомления
   */
  private getNotificationActions(type: NotificationData['type']): string[] {
    switch (type) {
      case 'game_invitation':
        return ['Принять', 'Отклонить'];
      case 'friend_request':
        return ['Принять', 'Отклонить'];
      default:
        return [];
    }
  }
}

export const NotificationService = new NotificationServiceClass();
