import React, { useEffect, useState } from 'react';
import {
  StatusBar,
  StyleSheet,
  View,
  Alert,
  AppState,
  AppStateStatus,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DeviceInfo from 'react-native-device-info';

// Screens
import SplashScreen from './screens/SplashScreen';
import LoginScreen from './screens/LoginScreen';
import HomeScreen from './screens/HomeScreen';
import GameScreen from './screens/GameScreen';
import MultiplayerScreen from './screens/MultiplayerScreen';
import ProfileScreen from './screens/ProfileScreen';
import LeaderboardScreen from './screens/LeaderboardScreen';
import TournamentsScreen from './screens/TournamentsScreen';
import FriendsScreen from './screens/FriendsScreen';
import SettingsScreen from './screens/SettingsScreen';
import PokerScreen from './screens/PokerScreen';
import BlackjackScreen from './screens/BlackjackScreen';
import ClansScreen from './screens/ClansScreen';
import EventsScreen from './screens/EventsScreen';

// Services
import { SocketService } from './services/SocketService';
import { StorageService } from './services/StorageService';
import { NotificationService } from './services/NotificationService';
import { SoundService } from './services/SoundService';

// Context
import { GameProvider } from './context/GameContext';
import { UserProvider } from './context/UserContext';

// Types
export type RootStackParamList = {
  Splash: undefined;
  Login: undefined;
  Main: undefined;
  Game: { gameType: 'durak' | 'poker' | 'blackjack'; mode: 'single' | 'multi' };
  Multiplayer: undefined;
  Poker: undefined;
  Blackjack: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Profile: undefined;
  Leaderboard: undefined;
  Tournaments: undefined;
  Friends: undefined;
  Clans: undefined;
  Events: undefined;
  Settings: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

const MainTabs = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Home':
              iconName = 'home';
              break;
            case 'Profile':
              iconName = 'person';
              break;
            case 'Leaderboard':
              iconName = 'leaderboard';
              break;
            case 'Tournaments':
              iconName = 'emoji-events';
              break;
            case 'Friends':
              iconName = 'people';
              break;
            case 'Clans':
              iconName = 'groups';
              break;
            case 'Events':
              iconName = 'event';
              break;
            case 'Settings':
              iconName = 'settings';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#4CAF50',
        tabBarInactiveTintColor: 'gray',
        tabBarStyle: {
          backgroundColor: '#1a1a1a',
          borderTopColor: '#333',
        },
        headerStyle: {
          backgroundColor: '#1a1a1a',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeScreen} 
        options={{ title: 'Главная' }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen} 
        options={{ title: 'Профиль' }}
      />
      <Tab.Screen 
        name="Leaderboard" 
        component={LeaderboardScreen} 
        options={{ title: 'Рейтинг' }}
      />
      <Tab.Screen 
        name="Tournaments" 
        component={TournamentsScreen} 
        options={{ title: 'Турниры' }}
      />
      <Tab.Screen 
        name="Friends" 
        component={FriendsScreen} 
        options={{ title: 'Друзья' }}
      />
      <Tab.Screen 
        name="Clans" 
        component={ClansScreen} 
        options={{ title: 'Кланы' }}
      />
      <Tab.Screen 
        name="Events" 
        component={EventsScreen} 
        options={{ title: 'События' }}
      />
      <Tab.Screen 
        name="Settings" 
        component={SettingsScreen} 
        options={{ title: 'Настройки' }}
      />
    </Tab.Navigator>
  );
};

const App: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [appState, setAppState] = useState(AppState.currentState);

  useEffect(() => {
    initializeApp();
    
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription?.remove();
      cleanup();
    };
  }, []);

  const initializeApp = async () => {
    try {
      // Инициализируем сервисы
      await StorageService.initialize();
      await NotificationService.initialize();
      await SoundService.initialize();
      
      // Проверяем сохраненные данные пользователя
      const savedUser = await StorageService.getUser();
      if (savedUser) {
        setIsLoggedIn(true);
        // Автоматически подключаемся к серверу
        SocketService.connect();
      }
      
      // Получаем информацию об устройстве
      const deviceInfo = {
        deviceId: await DeviceInfo.getUniqueId(),
        deviceName: await DeviceInfo.getDeviceName(),
        systemVersion: DeviceInfo.getSystemVersion(),
        appVersion: DeviceInfo.getVersion(),
      };
      
      console.log('Device Info:', deviceInfo);
      
    } catch (error) {
      console.error('App initialization error:', error);
      Alert.alert('Ошибка', 'Не удалось инициализировать приложение');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (appState.match(/inactive|background/) && nextAppState === 'active') {
      // Приложение стало активным
      if (isLoggedIn) {
        SocketService.reconnect();
      }
    } else if (nextAppState.match(/inactive|background/)) {
      // Приложение ушло в фон
      SocketService.disconnect();
    }
    
    setAppState(nextAppState);
  };

  const cleanup = () => {
    SocketService.disconnect();
    SoundService.cleanup();
  };

  const handleLogin = async (userData: any) => {
    try {
      await StorageService.saveUser(userData);
      setIsLoggedIn(true);
      SocketService.connect();
      
      Toast.show({
        type: 'success',
        text1: 'Добро пожаловать!',
        text2: `Привет, ${userData.name}!`,
      });
    } catch (error) {
      console.error('Login error:', error);
      Alert.alert('Ошибка', 'Не удалось войти в систему');
    }
  };

  const handleLogout = async () => {
    try {
      await StorageService.clearUser();
      setIsLoggedIn(false);
      SocketService.disconnect();
      
      Toast.show({
        type: 'info',
        text1: 'До свидания!',
        text2: 'Вы вышли из системы',
      });
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  if (isLoading) {
    return <SplashScreen />;
  }

  return (
    <SafeAreaProvider>
      <UserProvider>
        <GameProvider>
          <NavigationContainer>
            <StatusBar
              barStyle="light-content"
              backgroundColor="#1a1a1a"
              translucent={false}
            />
            <View style={styles.container}>
              <Stack.Navigator
                screenOptions={{
                  headerStyle: {
                    backgroundColor: '#1a1a1a',
                  },
                  headerTintColor: '#fff',
                  headerTitleStyle: {
                    fontWeight: 'bold',
                  },
                }}
              >
                {!isLoggedIn ? (
                  <Stack.Screen
                    name="Login"
                    options={{ headerShown: false }}
                  >
                    {(props) => (
                      <LoginScreen {...props} onLogin={handleLogin} />
                    )}
                  </Stack.Screen>
                ) : (
                  <>
                    <Stack.Screen
                      name="Main"
                      component={MainTabs}
                      options={{ headerShown: false }}
                    />
                    <Stack.Screen
                      name="Game"
                      component={GameScreen}
                      options={{ title: 'Игра' }}
                    />
                    <Stack.Screen
                      name="Multiplayer"
                      component={MultiplayerScreen}
                      options={{ title: 'Мультиплеер' }}
                    />
                    <Stack.Screen
                      name="Poker"
                      component={PokerScreen}
                      options={{ title: 'Покер' }}
                    />
                    <Stack.Screen
                      name="Blackjack"
                      component={BlackjackScreen}
                      options={{ title: 'Блэкджек' }}
                    />
                  </>
                )}
              </Stack.Navigator>
            </View>
            <Toast />
          </NavigationContainer>
        </GameProvider>
      </UserProvider>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
});

export default App;
