# 🎮 Козырь Мастер - Полная игровая экосистема

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D16.0.0-brightgreen)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-4.8+-blue)](https://www.typescriptlang.org/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue)](https://www.docker.com/)

**Козырь Мастер** - это современная мультиплатформенная экосистема для карточных игр, включающая в себя полный набор игр, социальные функции, экономическую систему и киберспортивную платформу.

## 🌟 Особенности

### 🎯 **5 Карточных игр**
- **Дурак классический** - русская народная игра
- **Дурак переводной** - с возможностью перевода карт
- **Покер (Texas Hold'em)** - международный покер
- **Преферанс** - интеллектуальная русская игра
- **Блэкджек** - классическая игра против дилера

### 🌐 **Мультиплатформенность**
- **Веб-приложение** (Next.js + React)
- **Мобильные приложения** (React Native для iOS/Android)
- **Адаптивный дизайн** для всех устройств
- **Офлайн режим** с синхронизацией

### 🏰 **Социальная экосистема**
- **Система друзей** с приглашениями
- **Кланы и гильдии** с перками и уровнями
- **Чат система** с модерацией
- **Турниры** различных форматов

### 💰 **Экономическая система**
- **4 типа валют** (монеты, кристаллы, жетоны, сезонные очки)
- **Магазин** с косметикой и усилениями
- **Ежедневные награды** и достижения
- **Система прогресса** с уровнями и титулами

### 🏆 **Киберспорт и стриминг**
- **Профессиональные турниры** с призовыми фондами
- **Система стриминга** с интеграцией Twitch/YouTube
- **Лиги и рейтинги** для соревновательной игры
- **Спектаторский режим** для наблюдения

### 🔒 **Безопасность и мониторинг**
- **Античит система** с детекцией аномалий
- **Система безопасности** с rate limiting
- **Полная аналитика** игроков и игр
- **A/B тестирование** для оптимизации

## 🚀 Быстрый старт

### Требования
- Node.js 16+
- Docker и Docker Compose
- Git

### Установка и запуск

```bash
# Клонирование репозитория
git clone https://github.com/kozyr-master/platform.git
cd platform

# Настройка переменных окружения
cp .env.example .env
# Отредактируйте .env файл с вашими настройками

# Запуск всей экосистемы
docker-compose up -d

# Или запуск для разработки
npm install
npm run dev
```

### Доступ к сервисам
- **Веб-приложение:** http://localhost:3000
- **API сервер:** http://localhost:3002
- **Мониторинг (Grafana):** http://localhost:3001
- **Логи (Kibana):** http://localhost:5601
- **Метрики (Prometheus):** http://localhost:9090

## 📁 Структура проекта

```
kozyr-master/
├── apps/
│   └── web/                    # Next.js веб-приложение
├── services/
│   ├── game-server/           # Основной игровой сервер
│   ├── notification-service/   # Сервис уведомлений
│   └── analytics-service/      # Сервис аналитики
├── mobile/                     # React Native приложение
├── docs/                       # Документация
├── nginx/                      # Конфигурация Nginx
├── monitoring/                 # Конфигурации мониторинга
├── scripts/                    # Скрипты развертывания
└── docker-compose.yml          # Docker конфигурация
```

## 🎮 Игровые возможности

### Дурак (Классический и переводной)
```typescript
// Создание игры в дурака
const durakGame = new DurakGame('game_123');
durakGame.addPlayer('player_1', 'Игрок 1');
durakGame.addPlayer('player_2', 'Игрок 2');
durakGame.startNewGame();

// Атака
durakGame.attack('player_1', [{ suit: 'hearts', rank: '7' }]);

// Защита
durakGame.defend('player_2', [{
  attackCard: { suit: 'hearts', rank: '7' },
  defenseCard: { suit: 'hearts', rank: 'J' }
}]);
```

### Покер (Texas Hold'em)
```typescript
// Создание покерного стола
const pokerTable = new PokerTable('table_123', {
  maxPlayers: 6,
  smallBlind: 10,
  bigBlind: 20,
  startingChips: 1000
});

// Действия игрока
pokerTable.playerAction('player_1', 'bet', 50);
pokerTable.playerAction('player_2', 'call');
pokerTable.playerAction('player_3', 'fold');
```

### Преферанс
```typescript
// Создание игры в преферанс
const preferansGame = new PreferansGame('game_456');
preferansGame.addPlayer('player_1', 'Игрок 1');
preferansGame.addPlayer('player_2', 'Игрок 2');
preferansGame.addPlayer('player_3', 'Игрок 3');

// Торговля
preferansGame.makeBid('player_1', { suit: 'spades', level: 7 });
preferansGame.makeBid('player_2', 'pass');
```

## 🏗️ Архитектура

### Backend (Node.js + TypeScript)
- **Модульная архитектура** с разделением по играм
- **WebSocket** для real-time взаимодействия
- **REST API** для стандартных операций
- **Микросервисная архитектура** для масштабирования

### Frontend (Next.js + React)
- **Server-Side Rendering** для SEO
- **Progressive Web App** возможности
- **Responsive дизайн** для всех устройств
- **Real-time обновления** через WebSocket

### Mobile (React Native)
- **Кроссплатформенная разработка** (iOS/Android)
- **Push-уведомления** для вовлечения
- **Офлайн поддержка** с синхронизацией
- **Нативная производительность**

### Базы данных
- **MongoDB** - основные игровые данные
- **Redis** - кэширование и сессии
- **PostgreSQL** - аналитика и отчеты

## 📊 Мониторинг и аналитика

### Метрики производительности
- **Время отклика API** и WebSocket
- **Использование ресурсов** (CPU, память, сеть)
- **Количество активных игроков** и игр
- **Ошибки и исключения** в реальном времени

### Игровая аналитика
- **Поведение игроков** и паттерны игры
- **Конверсия** и монетизация
- **A/B тестирование** функций
- **Когортный анализ** удержания

### Безопасность
- **Детекция читерства** с машинным обучением
- **Rate limiting** и DDoS защита
- **Мониторинг подозрительной активности**
- **Аудит безопасности** и логирование
