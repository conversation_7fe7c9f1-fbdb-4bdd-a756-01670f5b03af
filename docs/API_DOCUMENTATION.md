# Козырь Мастер - API Документация

## Обзор

Козырь Мастер предоставляет полнофункциональный REST API и WebSocket интерфейс для создания карточных игр и управления игровой экосистемой.

**Базовый URL:** `https://api.kozyr-master.com`  
**WebSocket URL:** `wss://api.kozyr-master.com/socket.io`  
**Версия API:** `v1`

## Аутентификация

### JWT Токены
Все защищенные эндпоинты требуют JWT токен в заголовке:
```
Authorization: Bearer <jwt_token>
```

### Получение токена
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Ответ:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "player": {
    "id": "player_123",
    "name": "PlayerName",
    "level": 15,
    "rating": 1250
  }
}
```

## Основные эндпоинты

### 🎮 Игровые комнаты

#### Получить список комнат
```http
GET /api/rooms
```

**Ответ:**
```json
{
  "rooms": [
    {
      "id": "room_123",
      "name": "Дурак для начинающих",
      "gameType": "durak",
      "players": 2,
      "maxPlayers": 4,
      "status": "waiting",
      "isPrivate": false
    }
  ]
}
```

#### Создать комнату
```http
POST /api/rooms
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Моя игра",
  "gameType": "durak",
  "maxPlayers": 4,
  "isPrivate": false,
  "password": "optional"
}
```

#### Присоединиться к комнате
```http
POST /api/rooms/{roomId}/join
Authorization: Bearer <token>
Content-Type: application/json

{
  "password": "optional"
}
```

### 🃏 Игровые действия

#### Сделать ход
```http
POST /api/games/{gameId}/move
Authorization: Bearer <token>
Content-Type: application/json

{
  "action": "attack",
  "cards": [
    {"suit": "hearts", "rank": "7"},
    {"suit": "spades", "rank": "7"}
  ]
}
```

#### Получить состояние игры
```http
GET /api/games/{gameId}/state
Authorization: Bearer <token>
```

**Ответ:**
```json
{
  "gameId": "game_123",
  "gameType": "durak",
  "phase": "playing",
  "currentPlayer": "player_456",
  "players": [
    {
      "id": "player_123",
      "name": "Player1",
      "handSize": 6,
      "isDefending": false
    }
  ],
  "attackCards": [],
  "defenseCards": [],
  "trumpSuit": "spades",
  "deckSize": 20
}
```

### 👥 Социальные функции

#### Получить список друзей
```http
GET /api/friends
Authorization: Bearer <token>
```

#### Отправить запрос в друзья
```http
POST /api/friends/request
Authorization: Bearer <token>
Content-Type: application/json

{
  "targetPlayerId": "player_456"
}
```

#### Пригласить друга в игру
```http
POST /api/friends/{friendId}/invite
Authorization: Bearer <token>
Content-Type: application/json

{
  "gameType": "poker",
  "message": "Давай сыграем!"
}
```

### 🏰 Кланы

#### Получить информацию о клане
```http
GET /api/clans/my
Authorization: Bearer <token>
```

#### Создать клан
```http
POST /api/clans
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Мастера карт",
  "tag": "MAST",
  "description": "Клан для профессиональных игроков",
  "isPublic": true
}
```

#### Подать заявку в клан
```http
POST /api/clans/{clanId}/apply
Authorization: Bearer <token>
Content-Type: application/json

{
  "message": "Хочу присоединиться к вашему клану!"
}
```

### 🏆 Турниры

#### Получить активные турниры
```http
GET /api/tournaments/active
```

#### Зарегистрироваться в турнире
```http
POST /api/tournaments/{tournamentId}/register
Authorization: Bearer <token>
```

#### Получить результаты турнира
```http
GET /api/tournaments/{tournamentId}/results
```

### 💰 Экономика

#### Получить кошелек игрока
```http
GET /api/economy/wallet
Authorization: Bearer <token>
```

**Ответ:**
```json
{
  "currency": {
    "coins": 1500,
    "gems": 50,
    "tokens": 25,
    "seasonPoints": 100
  },
  "totalEarned": {
    "coins": 5000,
    "gems": 100,
    "tokens": 75,
    "seasonPoints": 300
  }
}
```

#### Получить магазин
```http
GET /api/economy/shop
```

#### Купить предмет
```http
POST /api/economy/purchase
Authorization: Bearer <token>
Content-Type: application/json

{
  "itemId": "card_back_royal"
}
```

### 📊 Статистика и достижения

#### Получить статистику игрока
```http
GET /api/players/{playerId}/stats
```

**Ответ:**
```json
{
  "overall": {
    "gamesPlayed": 150,
    "gamesWon": 95,
    "winRate": 63.33,
    "level": 15,
    "experience": 2500
  },
  "byGame": {
    "durak": {
      "gamesPlayed": 100,
      "gamesWon": 65,
      "winRate": 65.0
    },
    "poker": {
      "gamesPlayed": 50,
      "gamesWon": 30,
      "winRate": 60.0,
      "totalWinnings": 5000,
      "biggestPot": 500
    }
  }
}
```

#### Получить достижения
```http
GET /api/achievements
Authorization: Bearer <token>
```

### 🎊 Сезонные события

#### Получить активные события
```http
GET /api/events/active
```

#### Зарегистрироваться в событии
```http
POST /api/events/{eventId}/register
Authorization: Bearer <token>
```

#### Получить прогресс в событии
```http
GET /api/events/{eventId}/progress
Authorization: Bearer <token>
```

## WebSocket События

### Подключение
```javascript
const socket = io('wss://api.kozyr-master.com', {
  auth: {
    token: 'your_jwt_token'
  }
});
```

### Игровые события

#### Присоединение к комнате
```javascript
socket.emit('join_room', { roomId: 'room_123' });
```

#### Игровые действия
```javascript
socket.emit('game_action', {
  gameId: 'game_123',
  action: 'attack',
  data: {
    cards: [{ suit: 'hearts', rank: '7' }]
  }
});
```

#### Получение обновлений игры
```javascript
socket.on('game_update', (data) => {
  console.log('Обновление игры:', data);
});
```

### Социальные события

#### Приглашение в игру
```javascript
socket.on('game_invitation', (data) => {
  console.log('Приглашение от:', data.fromPlayer);
});
```

#### Сообщения чата
```javascript
socket.emit('chat_message', {
  roomId: 'room_123',
  message: 'Привет всем!'
});

socket.on('chat_message', (data) => {
  console.log(`${data.playerName}: ${data.message}`);
});
```

## Коды ошибок

| Код | Описание |
|-----|----------|
| 400 | Неверный запрос |
| 401 | Не авторизован |
| 403 | Доступ запрещен |
| 404 | Ресурс не найден |
| 409 | Конфликт (например, комната заполнена) |
| 429 | Слишком много запросов |
| 500 | Внутренняя ошибка сервера |

### Формат ошибки
```json
{
  "success": false,
  "error": {
    "code": "ROOM_FULL",
    "message": "Комната заполнена",
    "details": {
      "roomId": "room_123",
      "maxPlayers": 4
    }
  }
}
```

## Rate Limiting

| Эндпоинт | Лимит |
|----------|-------|
| `/api/auth/*` | 5 запросов в минуту |
| `/api/games/*` | 30 запросов в секунду |
| `/api/chat/*` | 30 сообщений в минуту |
| `/api/economy/purchase` | 5 покупок в минуту |
| Остальные | 60 запросов в минуту |

## Примеры использования

### Создание и присоединение к игре
```javascript
// 1. Создать комнату
const createResponse = await fetch('/api/rooms', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: 'Быстрая игра',
    gameType: 'durak',
    maxPlayers: 2
  })
});

const { roomId } = await createResponse.json();

// 2. Подключиться через WebSocket
socket.emit('join_room', { roomId });

// 3. Начать игру когда все готовы
socket.emit('start_game', { roomId });
```

### Покерная игра
```javascript
// Создать покерный стол
const pokerRoom = await fetch('/api/rooms', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: 'Покер $10/$20',
    gameType: 'poker',
    maxPlayers: 6,
    settings: {
      smallBlind: 10,
      bigBlind: 20,
      startingChips: 1000
    }
  })
});

// Сделать ставку
socket.emit('poker_action', {
  roomId: 'room_123',
  action: 'bet',
  amount: 50
});
```

## SDK и библиотеки

### JavaScript/TypeScript
```bash
npm install kozyr-master-sdk
```

```javascript
import { KozyrMasterClient } from 'kozyr-master-sdk';

const client = new KozyrMasterClient({
  apiUrl: 'https://api.kozyr-master.com',
  token: 'your_jwt_token'
});

// Создать игру
const game = await client.createGame('durak', { maxPlayers: 4 });

// Сделать ход
await game.makeMove('attack', [{ suit: 'hearts', rank: '7' }]);
```

### React Native
```bash
npm install kozyr-master-react-native
```

```javascript
import { useKozyrMaster } from 'kozyr-master-react-native';

function GameScreen() {
  const { gameState, makeMove, isConnected } = useKozyrMaster();
  
  return (
    <View>
      <Text>Статус: {isConnected ? 'Подключен' : 'Отключен'}</Text>
      <GameBoard gameState={gameState} onMove={makeMove} />
    </View>
  );
}
```

## Поддержка

- **Документация:** https://docs.kozyr-master.com
- **GitHub:** https://github.com/kozyr-master/api
- **Discord:** https://discord.gg/kozyr-master
- **Email:** <EMAIL>

## Версионирование

API использует семантическое версионирование. Текущая версия: **v1.0.0**

Все изменения документируются в [CHANGELOG.md](./CHANGELOG.md).
