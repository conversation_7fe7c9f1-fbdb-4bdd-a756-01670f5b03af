# Документация по API компонентов

## Введение

Этот документ описывает API ключевых компонентов проекта "Козырь Мастер". Документация организована по слоям в соответствии с методологией Feature-Sliced Design.

## Shared Layer

### LanguageSwitcher

**Описание**: Компонент для переключения языка приложения.

**Импорт**:
```tsx
import { LanguageSwitcher } from '@shared/ui/LanguageSwitcher';
```

**Использование**:
```tsx
<LanguageSwitcher />
```

**Пропсы**: Компонент не принимает пропсы.

### Button

**Описание**: Базовый компонент кнопки с различными вариантами отображения.

**Импорт**:
```tsx
import { Button } from '@shared/ui/Button';
```

**Использование**:
```tsx
<Button variant="primary" onClick={handleClick}>Нажми меня</Button>
```

**Пропсы**:
- `variant`: `'primary' | 'secondary' | 'outline' | 'text'` - Вариант отображения кнопки
- `size`: `'small' | 'medium' | 'large'` - Размер кнопки
- `disabled`: `boolean` - Флаг отключения кнопки
- `fullWidth`: `boolean` - Флаг растягивания кнопки на всю ширину контейнера
- `onClick`: `() => void` - Обработчик клика
- `children`: `React.ReactNode` - Содержимое кнопки

## Entities Layer

### Card

**Описание**: Компонент игральной карты.

**Импорт**:
```tsx
import { Card } from '@entities/card/ui/Card';
```

**Использование**:
```tsx
<Card suit="hearts" rank="A" isFlipped={false} onClick={handleCardClick} />
```

**Пропсы**:
- `suit`: `'hearts' | 'diamonds' | 'clubs' | 'spades'` - Масть карты
- `rank`: `'2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | 'J' | 'Q' | 'K' | 'A'` - Ранг карты
- `isFlipped`: `boolean` - Флаг перевернутой карты
- `isSelected`: `boolean` - Флаг выбранной карты
- `isHighlighted`: `boolean` - Флаг подсвеченной карты
- `onClick`: `() => void` - Обработчик клика по карте

### GameBoard

**Описание**: Компонент игрового поля.

**Импорт**:
```tsx
import { GameBoard } from '@entities/game/ui/GameBoard';
```

**Использование**:
```tsx
<GameBoard 
  playerCards={playerCards}
  tableCards={tableCards}
  trumpCard={trumpCard}
  deckCount={36}
  onCardPlay={handleCardPlay}
/>
```

**Пропсы**:
- `playerCards`: `Card[]` - Карты игрока
- `tableCards`: `Card[]` - Карты на столе
- `trumpCard`: `Card` - Козырная карта
- `deckCount`: `number` - Количество карт в колоде
- `onCardPlay`: `(card: Card) => void` - Обработчик хода картой

### UserProfile

**Описание**: Компонент профиля пользователя.

**Импорт**:
```tsx
import { UserProfile } from '@entities/user/ui/UserProfile';
```

**Использование**:
```tsx
<UserProfile 
  user={user}
  isEditable={true}
  onEdit={handleEdit}
/>
```

**Пропсы**:
- `user`: `User` - Данные пользователя
- `isEditable`: `boolean` - Флаг возможности редактирования
- `onEdit`: `(userData: Partial<User>) => void` - Обработчик редактирования

## Features Layer

### AuthForm

**Описание**: Компонент формы авторизации.

**Импорт**:
```tsx
import { AuthForm } from '@features/auth/ui/AuthForm';
```

**Использование**:
```tsx
<AuthForm 
  type="login"
  onSubmit={handleSubmit}
  isLoading={isLoading}
/>
```

**Пропсы**:
- `type`: `'login' | 'register' | 'reset-password'` - Тип формы
- `onSubmit`: `(data: AuthFormData) => void` - Обработчик отправки формы
- `isLoading`: `boolean` - Флаг загрузки
- `error`: `string` - Текст ошибки

### GameCreation

**Описание**: Компонент создания игры.

**Импорт**:
```tsx
import { GameCreation } from '@features/game-creation/ui/GameCreation';
```

**Использование**:
```tsx
<GameCreation 
  onCreateGame={handleCreateGame}
  availableGames={availableGames}
/>
```

**Пропсы**:
- `onCreateGame`: `(gameSettings: GameSettings) => void` - Обработчик создания игры
- `availableGames`: `GameType[]` - Доступные типы игр
- `isLoading`: `boolean` - Флаг загрузки

## Widgets Layer

### Header

**Описание**: Компонент шапки приложения.

**Импорт**:
```tsx
import { Header } from '@widgets/header';
```

**Использование**:
```tsx
<Header user={user} onLogout={handleLogout} />
```

**Пропсы**:
- `user`: `User | null` - Данные пользователя
- `onLogout`: `() => void` - Обработчик выхода из системы

### Sidebar

**Описание**: Компонент боковой панели.

**Импорт**:
```tsx
import { Sidebar } from '@widgets/sidebar';
```

**Использование**:
```tsx
<Sidebar isOpen={isOpen} onClose={handleClose} />
```

**Пропсы**:
- `isOpen`: `boolean` - Флаг открытия боковой панели
- `onClose`: `() => void` - Обработчик закрытия боковой панели

### Chat

**Описание**: Компонент чата.

**Импорт**:
```tsx
import { Chat } from '@widgets/chat';
```

**Использование**:
```tsx
<Chat 
  messages={messages}
  onSendMessage={handleSendMessage}
  isLoading={isLoading}
/>
```

**Пропсы**:
- `messages`: `Message[]` - Сообщения чата
- `onSendMessage`: `(text: string) => void` - Обработчик отправки сообщения
- `isLoading`: `boolean` - Флаг загрузки

## Использование i18n

### Пример использования переводов

```tsx
import { useTranslation } from '@shared/lib/i18n';

const MyComponent = () => {
  const { t } = useTranslation('common');
  
  return (
    <div>
      <h1>{t('welcome')}</h1>
      <p>{t('join_game')}</p>
      <button>{t('buttons.save')}</button>
    </div>
  );
};
```

### Переключение языка

```tsx
import { useTranslation } from '@shared/lib/i18n';

const LanguageControl = () => {
  const { i18n } = useTranslation();
  
  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };
  
  return (
    <div>
      <button onClick={() => changeLanguage('ru')}>RU</button>
      <button onClick={() => changeLanguage('en')}>EN</button>
    </div>
  );
};
```

## Типизация

Все компоненты и их пропсы должны быть типизированы с использованием TypeScript для обеспечения безопасности типов и улучшения разработки.

```tsx
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  fullWidth?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = (props) => {
  // Реализация компонента
};
```

## Заключение

Данная документация описывает API ключевых компонентов проекта "Козырь Мастер". При разработке новых компонентов следует придерживаться аналогичного подхода к документированию и типизации для обеспечения единообразия и удобства использования компонентов.