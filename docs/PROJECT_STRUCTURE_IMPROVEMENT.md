# План улучшения структуры проекта "Козырь Мастер"

## Текущее состояние

Проект уже имеет базовую структуру, соответствующую методологии Feature-Sliced Design (FSD), с разделением на слои:
- entities (сущности)
- shared (общие компоненты)
- pages (страницы)

Однако для оптимальной поддержки многопользовательского режима и улучшения модульности требуется дальнейшая реорганизация.

## Предлагаемые улучшения

### 1. Полная реорганизация по Feature-Sliced Design

Расширить структуру FSD, добавив недостающие слои:

```
src/
├── app/           # Инициализация приложения, провайдеры, глобальные стили
├── processes/     # Сквозные бизнес-процессы (авторизация, многопользовательская синхронизация)
├── pages/         # Страницы приложения
├── widgets/       # Композиционные блоки для страниц (GameBoard, PlayerList, ChatWidget)
├── features/      # Интерактивные функции (CreateGame, JoinGame, MakeMove)
├── entities/      # Бизнес-сущности (Card, Player, Game)
└── shared/        # Переиспользуемые ресурсы (UI, API, libs)
```

### 2. Модуль многопользовательского режима

Создать специализированный модуль для многопользовательской функциональности:

```
src/
├── features/
│   ├── multiplayer/
│   │   ├── api/         # WebSocket клиент и методы взаимодействия
│   │   ├── model/       # Состояние и логика многопользовательского режима
│   │   ├── ui/          # Компоненты интерфейса для многопользовательского режима
│   │   └── lib/         # Вспомогательные функции
```

### 3. Централизованная система управления состоянием

Реализовать централизованную систему управления состоянием с использованием React Context и хуков:

```typescript
// src/features/multiplayer/model/context.tsx
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { createSocketConnection } from '../api/socket';

const MultiplayerContext = createContext(null);

export const MultiplayerProvider = ({ children }) => {
  // Реализация провайдера
};

export const useMultiplayer = () => {
  const context = useContext(MultiplayerContext);
  if (!context) {
    throw new Error('useMultiplayer must be used within a MultiplayerProvider');
  }
  return context;
};
```

### 4. Типизация всех компонентов и сервисов

Добавить строгую типизацию для всех компонентов и сервисов:

```typescript
// src/features/multiplayer/types.ts
export interface Player {
  id: string;
  name: string;
  isReady: boolean;
  cards: Card[];
  // Другие свойства игрока
}

export interface GameRoom {
  id: string;
  name: string;
  players: Player[];
  status: 'waiting' | 'playing' | 'finished';
  // Другие свойства комнаты
}

export interface MultiplayerState {
  connection: {
    status: 'disconnected' | 'connecting' | 'connected' | 'error';
    error: string | null;
  };
  rooms: GameRoom[];
  currentRoom: GameRoom | null;
  // Другие свойства состояния
}
```

### 5. Система профилей настроек

Реализовать систему профилей настроек для разных режимов игры:

```typescript
// src/entities/game/config/profiles.ts
export const gameProfiles = {
  classic: {
    name: 'Классический',
    rules: {
      // Настройки классического режима
    },
  },
  fast: {
    name: 'Быстрая игра',
    rules: {
      // Настройки быстрого режима
    },
  },
  multiplayer: {
    name: 'Многопользовательский',
    rules: {
      // Настройки многопользовательского режима
    },
  },
};
```

### 6. Улучшение интерфейса компонентов настроек

Создать модульные компоненты для настройки игры:

```typescript
// src/features/game-settings/ui/GameSettingsForm.tsx
import React from 'react';
import { useGameSettings } from '../model/hooks';
import { gameProfiles } from '@/entities/game/config/profiles';

export const GameSettingsForm = () => {
  const { settings, updateSettings, resetToProfile } = useGameSettings();
  
  // Реализация формы настроек
};
```

## Дальнейшие шаги

1. Реализовать предложенную структуру директорий
2. Перенести существующие компоненты в соответствующие слои
3. Разработать новые компоненты для многопользовательского режима
4. Интегрировать WebSocket клиент с серверной частью
5. Реализовать систему управления состоянием для многопользовательского режима
6. Добавить типизацию для всех компонентов и сервисов
7. Создать систему профилей настроек
8. Улучшить интерфейс компонентов настроек

## Ожидаемые результаты

- Более модульная и поддерживаемая архитектура
- Четкое разделение ответственности между компонентами
- Улучшенная типизация для предотвращения ошибок
- Готовность к интеграции многопользовательского режима
- Гибкая система настроек для разных режимов игры