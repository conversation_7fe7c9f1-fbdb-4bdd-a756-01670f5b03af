# Документация по компонентам карт

## Обзор

Компоненты карт представляют собой основу игрового процесса в проекте Kozyr Master. Они обеспечивают визуальное представление игральных карт с различными настройками, анимациями и эффектами. Данная документация описывает доступные компоненты, их API и примеры использования.

## Компоненты

### Card

Основной компонент для отображения игральной карты.

#### Свойства (Props)

| Свойство         | Тип                                                      | По умолчанию | Описание                            |
| ---------------- | -------------------------------------------------------- | ------------ | ----------------------------------- |
| `suit`           | `'hearts' \| 'diamonds' \| 'clubs' \| 'spades'`          | Обязательное | Масть карты                         |
| `rank`           | `string \| number`                                       | Обязательное | Ранг карты (A, K, Q, J, 10, 9, ...) |
| `isSelected`     | `boolean`                                                | `false`      | Выбрана ли карта                    |
| `isPlayable`     | `boolean`                                                | `false`      | Можно ли сыграть карту              |
| `onClick`        | `() => void`                                             | `undefined`  | Обработчик клика по карте           |
| `highContrast`   | `boolean`                                                | `false`      | Режим высокого контраста            |
| `animationSpeed` | `'slow' \| 'normal' \| 'fast' \| 'instant'`              | `'normal'`   | Скорость анимации                   |
| `isFaceDown`     | `boolean`                                                | `false`      | Отображать рубашкой вверх           |
| `animationType`  | `'simple' \| 'pulse' \| 'flip' \| '3d-rotate' \| 'glow'` | `'simple'`   | Тип анимации                        |
| `enableSound`    | `boolean`                                                | `true`       | Включить звуковые эффекты           |

#### Пример использования

```tsx
import { Card } from '../entities/card/ui/Card';

// Базовое использование
<Card suit="hearts" rank="A" />

// С дополнительными свойствами
<Card
  suit="diamonds"
  rank="K"
  isSelected={true}
  animationType="flip"
  animationSpeed="fast"
  highContrast={false}
  isFaceDown={false}
  enableSound={true}
  onClick={() => console.log('Карта выбрана')}
/>
```

### CardExample

Компонент для демонстрации различных возможностей карт.

#### Пример использования

```tsx
import { CardExample } from "../entities/card/ui/CardExample";

<CardExample />;
```

### CardExampleAdvanced

Расширенный компонент для демонстрации всех возможностей карт с дополнительными настройками и интерактивным конструктором.

#### Возможности

- Выбор типа колоды (классическая, современная, минималистичная, фэнтези)
- Выбор темы оформления (светлая, темная, цветная, монохромная)
- Различные варианты расположения карт (сетка, веер, стопка, круг)
- Интерактивный конструктор карт с настройкой всех параметров
- Демонстрация всех типов анимаций и эффектов
- Настройка скорости анимации
- Переключение между режимами контрастности
- Отображение карт лицевой стороной и рубашкой вверх
- Управление звуковыми эффектами

#### Пример использования

```tsx
import { CardExampleAdvanced } from "../entities/card/ui/CardExampleAdvanced";

<CardExampleAdvanced />;
```

## Модели и типы

### CardModel

Основная модель данных для карты.

```typescript
export interface CardModel {
  id: string;
  suit: CardSuit;
  rank: CardRank;
  isVisible: boolean; // Видна ли карта игроку
}
```

### CardSuit

Тип для мастей карт.

```typescript
export type CardSuit = "hearts" | "diamonds" | "clubs" | "spades";
```

### CardRank

Тип для рангов карт.

```typescript
export type CardRank = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13;
```

## Рекомендации по использованию

### Производительность

- При отображении большого количества карт рекомендуется использовать виртуализацию
- Для оптимизации производительности анимаций используйте `animationSpeed="instant"` для неважных карт
- Используйте `React.memo` для предотвращения ненужных перерендеров компонентов карт

### Доступность

- Режим высокого контраста (`highContrast={true}`) улучшает видимость для пользователей с нарушениями зрения
- Добавляйте атрибуты `aria-label` для улучшения доступности для скринридеров
- Обеспечьте возможность управления с клавиатуры для всех интерактивных элементов

### Кастомизация

- Для создания собственных стилей карт используйте styled-components
- Для добавления новых типов анимаций расширьте существующие keyframes
- Для создания новых типов колод добавьте соответствующие изображения и стили

## Планы по развитию

- Добавление поддержки различных размеров карт
- Реализация дополнительных типов анимаций и эффектов
- Создание специальных карт (джокеры, карты с особыми эффектами)
- Оптимизация производительности для мобильных устройств
- Добавление возможности создания пользовательских колод
- Интеграция с системой тем приложения

## Примеры интеграции

### Использование в игровом столе

```tsx
import { Card } from "../entities/card/ui/Card";
import { CardModel } from "../entities/card/model/types";

interface GameTableProps {
  playerCards: CardModel[];
  // другие свойства
}

const GameTable: React.FC<GameTableProps> = ({ playerCards }) => {
  const handleCardClick = (card: CardModel) => {
    // Логика обработки выбора карты
  };

  return (
    <div className="game-table">
      <div className="player-hand">
        {playerCards.map((card) => (
          <Card
            key={card.id}
            suit={card.suit}
            rank={card.rank}
            isPlayable={true}
            onClick={() => handleCardClick(card)}
            animationType="simple"
            enableSound={true}
          />
        ))}
      </div>
      {/* Другие элементы игрового стола */}
    </div>
  );
};
```

### Создание колоды карт

```typescript
import { CardSuit, CardRank, CardModel } from "../entities/card/model/types";

const createDeck = (): CardModel[] => {
  const suits: CardSuit[] = ["hearts", "diamonds", "clubs", "spades"];
  const ranks: CardRank[] = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13];
  const deck: CardModel[] = [];

  suits.forEach((suit) => {
    ranks.forEach((rank) => {
      deck.push({
        id: `${suit}-${rank}`,
        suit,
        rank,
        isVisible: false,
      });
    });
  });

  return deck;
};

// Перемешивание колоды
const shuffleDeck = (deck: CardModel[]): CardModel[] => {
  return [...deck].sort(() => Math.random() - 0.5);
};
```
