# Руководство по тестированию проекта "Козырь Мастер"

## Введение

Этот документ описывает подход к тестированию проекта "Козырь Мастер", включая типы тестов, инструменты и рекомендации по написанию тестов.

## Типы тестов

### 1. Модульные тесты (Unit Tests)

Модульные тесты проверяют отдельные компоненты, функции и классы в изоляции от остальной системы.

**Инструменты**:
- Jest - основной фреймворк для тестирования
- React Testing Library - для тестирования React-компонентов
- MSW (Mock Service Worker) - для мокирования API-запросов

**Пример модульного теста для компонента**:

```tsx
// Card.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Card } from '@entities/card/ui/Card';

describe('Card Component', () => {
  it('renders correctly with given props', () => {
    const handleClick = jest.fn();
    render(
      <Card 
        suit="hearts" 
        rank="A" 
        isFlipped={false} 
        onClick={handleClick} 
      />
    );
    
    expect(screen.getByTestId('card')).toBeInTheDocument();
    expect(screen.getByText('A')).toBeInTheDocument();
  });
  
  it('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(
      <Card 
        suit="hearts" 
        rank="A" 
        isFlipped={false} 
        onClick={handleClick} 
      />
    );
    
    fireEvent.click(screen.getByTestId('card'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  it('shows back side when flipped', () => {
    render(
      <Card 
        suit="hearts" 
        rank="A" 
        isFlipped={true} 
        onClick={() => {}} 
      />
    );
    
    expect(screen.queryByText('A')).not.toBeInTheDocument();
    expect(screen.getByTestId('card-back')).toBeInTheDocument();
  });
});
```

**Пример модульного теста для утилиты**:

```tsx
// gameUtils.test.ts
import { sortCards, calculateScore } from '@entities/game/lib/gameUtils';

describe('Game Utilities', () => {
  describe('sortCards', () => {
    it('sorts cards by suit and rank', () => {
      const cards = [
        { suit: 'spades', rank: '10' },
        { suit: 'hearts', rank: 'A' },
        { suit: 'hearts', rank: '5' },
        { suit: 'clubs', rank: 'K' },
      ];
      
      const sortedCards = sortCards(cards);
      
      expect(sortedCards[0].suit).toBe('clubs');
      expect(sortedCards[1].suit).toBe('hearts');
      expect(sortedCards[1].rank).toBe('5');
      expect(sortedCards[2].suit).toBe('hearts');
      expect(sortedCards[2].rank).toBe('A');
      expect(sortedCards[3].suit).toBe('spades');
    });
  });
  
  describe('calculateScore', () => {
    it('calculates score correctly', () => {
      const cards = [
        { suit: 'hearts', rank: 'A', value: 11 },
        { suit: 'clubs', rank: 'K', value: 4 },
        { suit: 'diamonds', rank: '10', value: 10 },
      ];
      
      const score = calculateScore(cards);
      expect(score).toBe(25);
    });
  });
});
```

### 2. Интеграционные тесты (Integration Tests)

Интеграционные тесты проверяют взаимодействие между компонентами и модулями системы.

**Пример интеграционного теста**:

```tsx
// GameBoard.integration.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { GameBoard } from '@entities/game/ui/GameBoard';
import { GameProvider } from '@entities/game/model/GameProvider';

describe('GameBoard Integration', () => {
  it('allows player to play a card', async () => {
    const playerCards = [
      { suit: 'hearts', rank: 'A', id: '1' },
      { suit: 'clubs', rank: 'K', id: '2' },
    ];
    
    const handleCardPlay = jest.fn();
    
    render(
      <GameProvider>
        <GameBoard 
          playerCards={playerCards}
          tableCards={[]}
          trumpCard={{ suit: 'diamonds', rank: '10', id: '3' }}
          deckCount={20}
          onCardPlay={handleCardPlay}
        />
      </GameProvider>
    );
    
    // Находим и кликаем по карте игрока
    const playerCard = screen.getByText('A');
    fireEvent.click(playerCard);
    
    // Проверяем, что обработчик был вызван с правильной картой
    expect(handleCardPlay).toHaveBeenCalledWith(playerCards[0]);
  });
  
  it('updates the table when a card is played', async () => {
    const playerCards = [
      { suit: 'hearts', rank: 'A', id: '1' },
      { suit: 'clubs', rank: 'K', id: '2' },
    ];
    
    const tableCards = [];
    
    const { rerender } = render(
      <GameProvider>
        <GameBoard 
          playerCards={playerCards}
          tableCards={tableCards}
          trumpCard={{ suit: 'diamonds', rank: '10', id: '3' }}
          deckCount={20}
          onCardPlay={() => {}}
        />
      </GameProvider>
    );
    
    // Проверяем, что на столе нет карт
    expect(screen.queryByTestId('table-card')).not.toBeInTheDocument();
    
    // Обновляем пропсы, добавляя карту на стол
    rerender(
      <GameProvider>
        <GameBoard 
          playerCards={playerCards}
          tableCards={[{ suit: 'hearts', rank: 'A', id: '1' }]}
          trumpCard={{ suit: 'diamonds', rank: '10', id: '3' }}
          deckCount={20}
          onCardPlay={() => {}}
        />
      </GameProvider>
    );
    
    // Проверяем, что карта появилась на столе
    expect(screen.getByTestId('table-card')).toBeInTheDocument();
  });
});
```

### 3. E2E-тесты (End-to-End Tests)

E2E-тесты проверяют работу всего приложения с точки зрения пользователя.

**Инструменты**:
- Cypress - для E2E-тестирования
- Playwright - альтернатива для E2E-тестирования

**Пример E2E-теста**:

```js
// cypress/integration/game.spec.js
describe('Game Flow', () => {
  beforeEach(() => {
    cy.intercept('GET', '/api/games', { fixture: 'games.json' }).as('getGames');
    cy.intercept('POST', '/api/games', { fixture: 'newGame.json' }).as('createGame');
    
    cy.login('testuser', 'password');
    cy.visit('/games');
  });
  
  it('allows user to create and join a game', () => {
    // Ждем загрузки списка игр
    cy.wait('@getGames');
    
    // Нажимаем кнопку создания игры
    cy.contains('Создать игру').click();
    
    // Заполняем форму создания игры
    cy.get('[data-testid=game-name-input]').type('Test Game');
    cy.get('[data-testid=game-type-select]').select('durak');
    cy.get('[data-testid=create-game-button]').click();
    
    // Ждем создания игры
    cy.wait('@createGame');
    
    // Проверяем, что мы перешли на страницу игры
    cy.url().should('include', '/games/');
    
    // Проверяем, что игровое поле отображается
    cy.get('[data-testid=game-board]').should('be.visible');
    cy.get('[data-testid=player-hand]').should('be.visible');
    cy.get('[data-testid=deck]').should('be.visible');
  });
  
  it('allows user to play a card', () => {
    // Переходим на страницу существующей игры
    cy.visit('/games/123');
    
    // Ждем загрузки игры
    cy.get('[data-testid=game-board]').should('be.visible');
    
    // Выбираем карту из руки игрока
    cy.get('[data-testid=player-card]').first().click();
    
    // Проверяем, что карта появилась на столе
    cy.get('[data-testid=table-card]').should('be.visible');
  });
});
```

## Настройка тестового окружения

### Jest

**jest.config.js**:

```js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapper: {
    '^@app/(.*)$': '<rootDir>/src/app/$1',
    '^@pages/(.*)$': '<rootDir>/src/pages/$1',
    '^@widgets/(.*)$': '<rootDir>/src/widgets/$1',
    '^@features/(.*)$': '<rootDir>/src/features/$1',
    '^@entities/(.*)$': '<rootDir>/src/entities/$1',
    '^@shared/(.*)$': '<rootDir>/src/shared/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{ts,tsx}',
    '!src/app/**/*',
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
};
```

**jest.setup.js**:

```js
import '@testing-library/jest-dom';
import { server } from './src/shared/api/mocks/server';

// Устанавливаем MSW перед всеми тестами
beforeAll(() => server.listen());

// Сбрасываем обработчики между тестами
afterEach(() => server.resetHandlers());

// Закрываем сервер после всех тестов
afterAll(() => server.close());
```

### Cypress

**cypress.json**:

```json
{
  "baseUrl": "http://localhost:3000",
  "viewportWidth": 1280,
  "viewportHeight": 720,
  "video": false,
  "screenshotOnRunFailure": true,
  "defaultCommandTimeout": 10000,
  "requestTimeout": 10000,
  "responseTimeout": 10000,
  "chromeWebSecurity": false
}
```

## Рекомендации по написанию тестов

1. **Следуйте принципу AAA (Arrange-Act-Assert)**:
   - Arrange: подготовка данных и окружения
   - Act: выполнение действия, которое тестируется
   - Assert: проверка результата

2. **Используйте data-testid для выбора элементов**:
   ```tsx
   <button data-testid="submit-button">Отправить</button>
   ```
   ```tsx
   screen.getByTestId('submit-button')
   ```

3. **Тестируйте поведение, а не реализацию**:
   - Фокусируйтесь на том, что компонент делает, а не как он это делает
   - Избегайте тестирования внутренних деталей реализации

4. **Используйте мокирование с осторожностью**:
   - Мокируйте только внешние зависимости (API, сторонние сервисы)
   - Избегайте мокирования компонентов, которые тестируете

5. **Пишите тесты на критические пути**:
   - Авторизация
   - Создание игры
   - Игровой процесс
   - Обработка ошибок

## Интеграция с CI/CD

Настройте запуск тестов в CI/CD пайплайне:

```yaml
# .github/workflows/test.yml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16.x'
        cache: 'npm'
    - run: npm ci
    - run: npm run test:ci
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v2

  e2e:
    runs-on: ubuntu-latest
    needs: test

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16.x'
        cache: 'npm'
    - run: npm ci
    - name: Cypress run
      uses: cypress-io/github-action@v2
      with:
        build: npm run build
        start: npm start
        wait-on: 'http://localhost:3000'
```

## Заключение

Тестирование является важной частью разработки проекта "Козырь Мастер". Следуя рекомендациям из этого руководства, вы сможете создать надежные тесты, которые помогут обеспечить качество и стабильность приложения.

Помните, что хорошие тесты должны быть:
- Быстрыми
- Независимыми
- Повторяемыми
- Самодостаточными
- Понятными

Регулярно запускайте тесты и поддерживайте высокий уровень покрытия кода тестами для обеспечения качества проекта.